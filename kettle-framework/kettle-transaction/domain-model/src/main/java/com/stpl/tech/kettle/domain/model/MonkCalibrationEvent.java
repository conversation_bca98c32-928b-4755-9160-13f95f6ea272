package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class MonkCalibrationEvent implements Serializable {

    private static final long serialVersionUID = -6495180755613242340L;

    private Integer monkCalibrationEventId;
    private Integer unitId;
    private Integer orderId;
    private Integer orderItemId;
    private Integer monkNo;
    private String processStatus;
    private String calibrationStatus;
    private Date orderTime;
    private Date startTime;
    private Date endTime;
    private Date serverTime;
    private Integer expectedQuantity;
    private Integer machineQuantity;
    private Integer enteredQuantity;
    private Integer milkQuantity;
    private Integer totalMonks;
    private Integer activeMonks;
    private Integer inActiveMonks;
    private Integer calibratedMonks;
    private Integer activeNonCalibratedMonks;
    private List<TroubleShoot> troubleShoots;

    public Integer getMonkCalibrationEventId() {
        return monkCalibrationEventId;
    }

    public void setMonkCalibrationEventId(Integer monkCalibrationEventId) {
        this.monkCalibrationEventId = monkCalibrationEventId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Integer getMonkNo() {
        return monkNo;
    }

    public void setMonkNo(Integer monkNo) {
        this.monkNo = monkNo;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public String getCalibrationStatus() {
        return calibrationStatus;
    }

    public void setCalibrationStatus(String calibrationStatus) {
        this.calibrationStatus = calibrationStatus;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getServerTime() {
        return serverTime;
    }

    public void setServerTime(Date serverTime) {
        this.serverTime = serverTime;
    }

    public Integer getExpectedQuantity() {
        return expectedQuantity;
    }

    public void setExpectedQuantity(Integer expectedQuantity) {
        this.expectedQuantity = expectedQuantity;
    }

    public Integer getMachineQuantity() {
        return machineQuantity;
    }

    public void setMachineQuantity(Integer machineQuantity) {
        this.machineQuantity = machineQuantity;
    }

    public Integer getEnteredQuantity() {
        return enteredQuantity;
    }

    public void setEnteredQuantity(Integer enteredQuantity) {
        this.enteredQuantity = enteredQuantity;
    }

    public Integer getMilkQuantity() {
        return milkQuantity;
    }

    public void setMilkQuantity(Integer milkQuantity) {
        this.milkQuantity = milkQuantity;
    }

    public Integer getTotalMonks() {
        return totalMonks;
    }

    public void setTotalMonks(Integer totalMonks) {
        this.totalMonks = totalMonks;
    }

    public Integer getActiveMonks() {
        return activeMonks;
    }

    public void setActiveMonks(Integer activeMonks) {
        this.activeMonks = activeMonks;
    }

    public Integer getInActiveMonks() {
        return inActiveMonks;
    }

    public void setInActiveMonks(Integer inActiveMonks) {
        this.inActiveMonks = inActiveMonks;
    }

    public Integer getCalibratedMonks() {
        return calibratedMonks;
    }

    public void setCalibratedMonks(Integer calibratedMonks) {
        this.calibratedMonks = calibratedMonks;
    }

    public Integer getActiveNonCalibratedMonks() {
        return activeNonCalibratedMonks;
    }

    public void setActiveNonCalibratedMonks(Integer activeNonCalibratedMonks) {
        this.activeNonCalibratedMonks = activeNonCalibratedMonks;
    }

    public List<TroubleShoot> getTroubleShoots() {
        return troubleShoots;
    }

    public void setTroubleShoots(List<TroubleShoot> troubleShoots) {
        this.troubleShoots = troubleShoots;
    }

    @Override
    public String toString() {
        return "MonkCalibrationEvent{" +
                "monkCalibrationEventId=" + monkCalibrationEventId +
                ", unitId=" + unitId +
                ", orderId=" + orderId +
                ", orderItemId=" + orderItemId +
                ", monkNo=" + monkNo +
                ", processStatus='" + processStatus + '\'' +
                ", calibrationStatus='" + calibrationStatus + '\'' +
                ", orderTime=" + orderTime +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", serverTime=" + serverTime +
                ", expectedQuantity=" + expectedQuantity +
                ", machineQuantity=" + machineQuantity +
                ", enteredQuantity=" + enteredQuantity +
                ", milkQuantity=" + milkQuantity +
                ", totalMonks=" + totalMonks +
                ", activeMonks=" + activeMonks +
                ", inActiveMonks=" + inActiveMonks +
                ", calibratedMonks=" + calibratedMonks +
                ", activeNonCalibratedMonks=" + activeNonCalibratedMonks +
                '}';
    }
}
