/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.09.05 at 12:05:55 PM IST 
//


package com.stpl.tech.kettle.delivery.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stpl.tech.master.domain.model.Unit;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="clientId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="merchantId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="merchantName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="merchantAddress" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="merchantLat" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="merchantLong" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="merchantContact" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="merchantType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="merchantVerified" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "clientId",
    "merchantId",
    "merchantName",
    "merchantAddress",
    "merchantLat",
    "merchantLong",
    "merchantContact",
    "merchantType",
    "merchantVerified"
})
@XmlRootElement(name = "GrabMerchant")
public class GrabMerchant implements GrabRequest{

    @XmlElement(required = true)
    protected String clientId;
    @XmlElement(required = true)
    protected String merchantId;
    @XmlElement(required = true)
    protected String merchantName;
    @XmlElement(required = true)
    protected String merchantAddress;
    @XmlElement(required = true)
    protected String merchantLat;
    @XmlElement(required = true)
    protected String merchantLong;
    @XmlElement(required = true)
    protected String merchantContact;
    @XmlElement(required = true)
    protected String merchantType;
    @XmlElement(required = true)
    protected String merchantVerified;


    public GrabMerchant(){}


    public GrabMerchant(Unit unit) throws JsonProcessingException {
        this.setMerchantAddress(unit.getAddress().toString());
        this.setMerchantLat(unit.getAddress().getLatitude());
        this.setMerchantLong(unit.getAddress().getLongitude());
        this.setMerchantName(unit.getName());
        this.setMerchantContact(unit.getAddress().getContact1());
        this.setMerchantId(String.valueOf(unit.getId()));
        this.setMerchantType("R");
        this.setMerchantVerified(String.valueOf(0));
    }


    /**
     * Gets the value of the clientId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClientId() {
        return clientId;
    }

    /**
     * Sets the value of the clientId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClientId(String value) {
        this.clientId = value;
    }

    /**
     * Gets the value of the merchantId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * Sets the value of the merchantId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMerchantId(String value) {
        this.merchantId = value;
    }

    /**
     * Gets the value of the merchantName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMerchantName() {
        return merchantName;
    }

    /**
     * Sets the value of the merchantName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMerchantName(String value) {
        this.merchantName = value;
    }

    /**
     * Gets the value of the merchantAddress property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMerchantAddress() {
        return merchantAddress;
    }

    /**
     * Sets the value of the merchantAddress property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMerchantAddress(String value) {
        this.merchantAddress = value;
    }

    /**
     * Gets the value of the merchantLat property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMerchantLat() {
        return merchantLat;
    }

    /**
     * Sets the value of the merchantLat property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMerchantLat(String value) {
        this.merchantLat = value;
    }

    /**
     * Gets the value of the merchantLong property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMerchantLong() {
        return merchantLong;
    }

    /**
     * Sets the value of the merchantLong property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMerchantLong(String value) {
        this.merchantLong = value;
    }

    /**
     * Gets the value of the merchantContact property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMerchantContact() {
        return merchantContact;
    }

    /**
     * Sets the value of the merchantContact property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMerchantContact(String value) {
        this.merchantContact = value;
    }

    /**
     * Gets the value of the merchantType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMerchantType() {
        return merchantType;
    }

    /**
     * Sets the value of the merchantType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMerchantType(String value) {
        this.merchantType = value;
    }

    /**
     * Gets the value of the merchantVerified property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMerchantVerified() {
        return merchantVerified;
    }

    /**
     * Sets the value of the merchantVerified property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMerchantVerified(String value) {
        this.merchantVerified = value;
    }

}
