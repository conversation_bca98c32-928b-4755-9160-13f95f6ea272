/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.domain.model;

public enum XTwoCommandType {
    AUTOCOOK_START("AUTOCOOK_START"),
    AUTOCOOK_STOP("AUTO<PERSON>OK_STOP"),
    CLEANING_CYCLE_START("CLEANING_CYCLE_START"),
    CLEANING_CYCLE_STOP("CLEANING_CYCLE_STOP"),
    START_INDUCTION("START_INDUCTION"),
    STOP_INDUCTION("STOP_INDUCTION"),
    PRIMING_CTRL("PRIMING_CTRL");

    private final String command;

    XTwoCommandType(String command) {
        this.command = command;
    }
}
