package com.stpl.tech.kettle.domain.model;

public class CreateNextOfferRequest {
	private Integer customerId;
	private String contactNumber;
	private Integer brandId;
	private Integer campaignId;
	private String utmSource;
	private String utmMedium;

	private String campaignToken;

	private String authToken;

	public CreateNextOfferRequest() {
	}

	public CreateNextOfferRequest(Integer customerId, String contactNumber, Integer brandId, Integer campaignId, String utmSource, String utmMedium) {
		this.customerId = customerId;
		this.contactNumber = contactNumber;
		this.brandId = brandId;
		this.campaignId = campaignId;
		this.utmSource = utmSource;
		this.utmMedium = utmMedium;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public Integer getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(Integer campaignId) {
		this.campaignId = campaignId;
	}

	public String getUtmSource() {
		return utmSource;
	}

	public void setUtmSource(String utmSource) {
		this.utmSource = utmSource;
	}

	public String getUtmMedium() {
		return utmMedium;
	}

	public void setUtmMedium(String utmMedium) {
		this.utmMedium = utmMedium;
	}

	public String getCampaignToken() {
		return campaignToken;
	}

	public void setCampaignToken(String campaignToken) {
		this.campaignToken = campaignToken;
	}

	public String getAuthToken() {
		return authToken;
	}

	public void setAuthToken(String authToken) {
		this.authToken = authToken;
	}
}
