package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlElement;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

@ExcelSheet(value = "Unit Budget Detail")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 100)
public class UnitBudget {

	@ExcelField(position = 1, validationType = ExcelField.ValidationType.HARD, regex = "[0-2][0-9][0-9][0-9]")
	private int unitId;
	@XmlElement(required = true)
	@ExcelField(position = 2)
	private String unitName;
	@ExcelField(position = 3)
	private int month;
	@ExcelField(position = 4)
	private int year;

	@ExcelField(position = 5)
	private Integer ticket;
	@ExcelField(position = 6)
	private BigDecimal sales;
	@ExcelField(position = 7)
	private BigDecimal apc;
	@ExcelField(position = 8)
	private BigDecimal gmv;
	@ExcelField(position = 9)
	private BigDecimal discount;

	@ExcelField(position = 10)
	private Integer dineInTicket;
	@ExcelField(position = 11)
	private BigDecimal dineInSales;
	@ExcelField(position = 12)
	private BigDecimal dineInApc;
	@ExcelField(position = 13)
	private BigDecimal dineInGmv;
	@ExcelField(position = 14)
	private BigDecimal dineInDiscount;

	@ExcelField(position = 15)
	private Integer deliveryTicket;
	@ExcelField(position = 16)
	private BigDecimal deliverySales;
	@ExcelField(position = 17)
	private BigDecimal deliveryApc;
	@ExcelField(position = 18)
	private BigDecimal deliveryGmv;
	@ExcelField(position = 19)
	private BigDecimal deliveryDiscount;

	@ExcelField(position = 20)
	private BigDecimal giftCardSale;
	@ExcelField(position = 21)
	private BigDecimal dineInCogs;
	@ExcelField(position = 22)
	private BigDecimal deliveryCogs;
	@ExcelField(position = 23)
	private BigDecimal employeeMealCogs;

	@ExcelField(position = 24)
	private BigDecimal unsatifiedCustomerCost;
	@ExcelField(position = 25)
	private BigDecimal expiryWastage;
	@ExcelField(position = 26)
	private BigDecimal wastageOther;

	@ExcelField(position = 27)
	private BigDecimal consumableUtility;
	@ExcelField(position = 28)
	private BigDecimal consumableStationary;
	@ExcelField(position = 29)
	private BigDecimal consumableUniform;
	@ExcelField(position = 30)
	private BigDecimal consumableEquipment;
	@ExcelField(position = 31)
	private BigDecimal consumableCutlery;
	@ExcelField(position = 32)
	private BigDecimal fixedAssets;

	@ExcelField(position = 33)
	private BigDecimal salary;
	@ExcelField(position = 34)
	private BigDecimal medicalInsurance;
	@ExcelField(position = 35)
	private BigDecimal salaryIncentive;
	@ExcelField(position = 36)
	private BigDecimal securityGuardCharges;
	@ExcelField(position = 37)
	private BigDecimal salesIncentive;

	@ExcelField(position = 38)
	private BigDecimal depreciationOfBike;
	@ExcelField(position = 39)
	private BigDecimal fuelCharges;
	@ExcelField(position = 40)
	private BigDecimal vehicleRegularMaintenance;
	@ExcelField(position = 41)
	private BigDecimal vehicleMonthlyMaintenance;
	@ExcelField(position = 42)
	private BigDecimal partnerDeliveryCharges;
	@ExcelField(position = 43)
	private BigDecimal parkingCharges;

	@ExcelField(position = 44)
	private BigDecimal marketingAndSampling;
	@ExcelField(position = 45)
	private BigDecimal advertisementBTL;
	@ExcelField(position = 46)
	private BigDecimal consumableMarketing;

	@ExcelField(position = 48)
	private BigDecimal logisticCharges;

	@ExcelField(position = 49)
	private BigDecimal energyElectricity;
	@ExcelField(position = 50)
	private BigDecimal energyDG;
	@ExcelField(position = 51)
	private BigDecimal waterCharges;
	@ExcelField(position = 52)
	private BigDecimal communicationInternet;
	@ExcelField(position = 53)
	private BigDecimal communicationTelephone;
	@ExcelField(position = 55)
	private BigDecimal communicationILL;

	@ExcelField(position = 56)
	private BigDecimal creditCardTransactionCharges;
	@ExcelField(position = 57)
	private BigDecimal voucherTransactionCharges;
	@ExcelField(position = 58)
	private BigDecimal walletsTransactionCharges;
	@ExcelField(position = 59)
	private BigDecimal commissionChannelPartners;
	@ExcelField(position = 60)
	private BigDecimal commissionChange;
	@ExcelField(position = 61)
	private BigDecimal payrollProcessingFee;
	@ExcelField(position = 62)
	private BigDecimal newsPaper;
	@ExcelField(position = 63)
	private BigDecimal localConveynece;
	@ExcelField(position = 64)
	private BigDecimal staffWelfareExpenses;
	@ExcelField(position = 65)
	private BigDecimal courierCharges;
	@ExcelField(position = 66)
	private BigDecimal printingAndStationary;
	@ExcelField(position = 67)
	private BigDecimal businessPromotion;
	@ExcelField(position = 68)
	private BigDecimal legalChargesCafe;
	@ExcelField(position = 69)
	private BigDecimal legalCharges;
	@ExcelField(position = 70)
	private BigDecimal professionalCharges;
	@ExcelField(position = 71)
	private BigDecimal cenvatReversal;
	@ExcelField(position = 72)
	private BigDecimal propertyTax;

	@ExcelField(position = 73)
	private BigDecimal openingLicencesFees;
	@ExcelField(position = 74)
	private BigDecimal registrationCharges;
	@ExcelField(position = 75)
	private BigDecimal stampDutyCharges;
	@ExcelField(position = 76)
	private BigDecimal designingFees;
	@ExcelField(position = 77)

	private BigDecimal buildingMaintenance;
	@ExcelField(position = 78)
	private BigDecimal cleaningCharges;
	@ExcelField(position = 79)
	private BigDecimal computerMaintenance;
	@ExcelField(position = 80)
	private BigDecimal pestControlCharges;
	@ExcelField(position = 81)
	private BigDecimal equipmentMaintenance;
	@ExcelField(position = 82)
	private BigDecimal prontoAMC;
	@ExcelField(position = 83)
	private BigDecimal maintenanceSalary;

	@ExcelField(position = 84)
	private BigDecimal dgRental;
	@ExcelField(position = 85)
	private BigDecimal edcRental;
	@ExcelField(position = 86)
	private BigDecimal systemRental;
	@ExcelField(position = 87)
	private BigDecimal roRental;
	@ExcelField(position = 88)
	private BigDecimal insuranceCharges;
	@ExcelField(position = 89)
	private BigDecimal propertyFixRent;
	@ExcelField(position = 90)
	private BigDecimal revenueShare;
	@ExcelField(position = 91)
	private BigDecimal fixCAM;
	@ExcelField(position = 92)
	private BigDecimal chillingCharges;
	@ExcelField(position = 93)
	private BigDecimal marketingCharges;
	@ExcelField(position = 94)
	private BigDecimal pettyCashRentals;
	@ExcelField(position = 95)
	private BigDecimal musicRentals;
	@ExcelField(position = 96)
	private BigDecimal internetPartnerRental;

	@ExcelField(position = 97)
	private BigDecimal supportOperations;
	@ExcelField(position = 98)
	private BigDecimal supportHeadOffice;

	@ExcelField(position = 99)
	private BigDecimal techologyPlatformCharges;
	@ExcelField(position = 100)
	private BigDecimal techologyTraining;
	@ExcelField(position = 101)
	private BigDecimal techologyMail;
	@ExcelField(position = 102)
	private BigDecimal techologyOthers;

	@ExcelField(position = 103)
	private BigDecimal corporateMarketingDigital;
	@ExcelField(position = 104)
	private BigDecimal corporateMarketingAdvOffline;
	@ExcelField(position = 105)
	private BigDecimal corporateMarketingAdvOnline;
	@ExcelField(position = 106)
	private BigDecimal corporateMarketingOutdoor;
	@ExcelField(position = 107)
	private BigDecimal corporateMarketingPnS;
	@ExcelField(position = 108)
	private BigDecimal corporateMarketingAgencyFees;

	@ExcelField(position = 109)
	private BigDecimal stockVariance;

	@ExcelField(position = 110)
	private BigDecimal technologyVariable;
	@ExcelField(position = 111)
	private BigDecimal marketingLocalStoreVariable;
	@ExcelField(position = 112)
	private BigDecimal supportVariable;
	@ExcelField(position = 113)
	private BigDecimal maintenanceVariable;
	@ExcelField(position = 114)
	private BigDecimal corporateMarketingVariable;
	@ExcelField(position = 115)
	private BigDecimal fixedCostVariable;
	@ExcelField(position = 116)
	private BigDecimal manpowerVariable;
	@ExcelField(position = 117)
	private BigDecimal supplyChainVariable;
	@ExcelField(position = 118)
	private BigDecimal deliveryChargesVariable;
	@ExcelField(position = 119)
	private BigDecimal anyOtherVariable1;
	@ExcelField(position = 120)
	private BigDecimal anyOtherVariable2;
	@ExcelField(position = 121)
	private BigDecimal anyOtherVariable3;

	@ExcelField(position = 122)
	private String onRevenueShare;
	@ExcelField(position = 123)
	private BigDecimal electricityMeter1FixedCharge;
	@ExcelField(position = 124)
	private BigDecimal electricityMeter1PerUnitCharge;
	@ExcelField(position = 125)
	private BigDecimal electricityMeter1TaxPercentage;
	@ExcelField(position = 126)
	private BigDecimal electricityMeter1OtherCharge;
	@ExcelField(position = 127)
	private BigDecimal electricityMeter2FixedCharge;
	@ExcelField(position = 128)
	private BigDecimal electricityMeter2PerUnitCharge;
	@ExcelField(position = 129)
	private BigDecimal electricityMeter2TaxPercentage;
	@ExcelField(position = 130)
	private BigDecimal electricityMeter2OtherCharge;
	@ExcelField(position = 131)
	private BigDecimal electricityMeter3FixedCharge;
	@ExcelField(position = 132)
	private BigDecimal electricityMeter3PerUnitCharge;
	@ExcelField(position = 133)
	private BigDecimal electricityMeter3TaxPercentage;
	@ExcelField(position = 134)
	private BigDecimal electricityMeter3OtherCharge;
	@ExcelField(position = 135)
	private BigDecimal dgMeterFixedCharge;
	@ExcelField(position = 136)
	private BigDecimal dgMeterPerUnitCharge;
	@ExcelField(position = 137)
	private BigDecimal dgMeterTaxPercentage;
	@ExcelField(position = 138)
	private BigDecimal dgMeterOtherCharge;
	@ExcelField(position = 139)
	private BigDecimal pettyTravelExpense;
	@ExcelField(position = 140)
	private BigDecimal ppeCost;

	public UnitBudget() {
		// TODO Auto-generated constructor stub
	}

	public UnitBudget(int unitId, String unitName) {
		this.unitId = unitId;
		this.unitName = unitName;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public Integer getTicket() {
		return ticket;
	}

	public void setTicket(Integer ticket) {
		this.ticket = ticket;
	}

	public BigDecimal getSales() {
		return sales;
	}

	public void setSales(BigDecimal sales) {
		this.sales = sales;
	}

	public BigDecimal getApc() {
		return apc;
	}

	public void setApc(BigDecimal apc) {
		this.apc = apc;
	}

	public BigDecimal getGmv() {
		return gmv;
	}

	public void setGmv(BigDecimal gmv) {
		this.gmv = gmv;
	}

	public BigDecimal getDiscount() {
		return discount;
	}

	public void setDiscount(BigDecimal discount) {
		this.discount = discount;
	}

	public Integer getDineInTicket() {
		return dineInTicket;
	}

	public void setDineInTicket(Integer dineInTicket) {
		this.dineInTicket = dineInTicket;
	}

	public BigDecimal getDineInSales() {
		return dineInSales;
	}

	public void setDineInSales(BigDecimal dineInSales) {
		this.dineInSales = dineInSales;
	}

	public BigDecimal getDineInApc() {
		return dineInApc;
	}

	public void setDineInApc(BigDecimal dineInApc) {
		this.dineInApc = dineInApc;
	}

	public BigDecimal getDineInGmv() {
		return dineInGmv;
	}

	public void setDineInGmv(BigDecimal dineInGmv) {
		this.dineInGmv = dineInGmv;
	}

	public BigDecimal getDineInDiscount() {
		return dineInDiscount;
	}

	public void setDineInDiscount(BigDecimal dineInDiscount) {
		this.dineInDiscount = dineInDiscount;
	}

	public Integer getDeliveryTicket() {
		return deliveryTicket;
	}

	public void setDeliveryTicket(Integer deliveryTicket) {
		this.deliveryTicket = deliveryTicket;
	}

	public BigDecimal getDeliverySales() {
		return deliverySales;
	}

	public void setDeliverySales(BigDecimal deliverySales) {
		this.deliverySales = deliverySales;
	}

	public BigDecimal getDeliveryApc() {
		return deliveryApc;
	}

	public void setDeliveryApc(BigDecimal deliveryApc) {
		this.deliveryApc = deliveryApc;
	}

	public BigDecimal getDeliveryGmv() {
		return deliveryGmv;
	}

	public void setDeliveryGmv(BigDecimal deliveryGmv) {
		this.deliveryGmv = deliveryGmv;
	}

	public BigDecimal getDeliveryDiscount() {
		return deliveryDiscount;
	}

	public void setDeliveryDiscount(BigDecimal deliveryDiscount) {
		this.deliveryDiscount = deliveryDiscount;
	}

	public BigDecimal getGiftCardSale() {
		return giftCardSale;
	}

	public void setGiftCardSale(BigDecimal giftCardSale) {
		this.giftCardSale = giftCardSale;
	}

	public BigDecimal getDineInCogs() {
		return dineInCogs;
	}

	public void setDineInCogs(BigDecimal dineInCogs) {
		this.dineInCogs = dineInCogs;
	}

	public BigDecimal getDeliveryCogs() {
		return deliveryCogs;
	}

	public void setDeliveryCogs(BigDecimal deliveryCogs) {
		this.deliveryCogs = deliveryCogs;
	}

	public BigDecimal getEmployeeMealCogs() {
		return employeeMealCogs;
	}

	public void setEmployeeMealCogs(BigDecimal employeeMealCogs) {
		this.employeeMealCogs = employeeMealCogs;
	}

	public BigDecimal getUnsatifiedCustomerCost() {
		return unsatifiedCustomerCost;
	}

	public void setUnsatifiedCustomerCost(BigDecimal unsatifiedCustomerCost) {
		this.unsatifiedCustomerCost = unsatifiedCustomerCost;
	}

	public BigDecimal getPPECost() {
		return this.ppeCost;
	}

	public void setPPECost(BigDecimal ppeCost) {
		this.ppeCost = ppeCost;
	}

	public BigDecimal getExpiryWastage() {
		return expiryWastage;
	}

	public void setExpiryWastage(BigDecimal expiryWastage) {
		this.expiryWastage = expiryWastage;
	}

	public BigDecimal getWastageOther() {
		return wastageOther;
	}

	public void setWastageOther(BigDecimal wastageOther) {
		this.wastageOther = wastageOther;
	}

	public BigDecimal getConsumableUtility() {
		return consumableUtility;
	}

	public void setConsumableUtility(BigDecimal consumableUtility) {
		this.consumableUtility = consumableUtility;
	}

	public BigDecimal getConsumableStationary() {
		return consumableStationary;
	}

	public void setConsumableStationary(BigDecimal consumableStationary) {
		this.consumableStationary = consumableStationary;
	}

	public BigDecimal getConsumableUniform() {
		return consumableUniform;
	}

	public void setConsumableUniform(BigDecimal consumableUniform) {
		this.consumableUniform = consumableUniform;
	}

	public BigDecimal getConsumableEquipment() {
		return consumableEquipment;
	}

	public void setConsumableEquipment(BigDecimal consumableEquipment) {
		this.consumableEquipment = consumableEquipment;
	}

	public BigDecimal getConsumableCutlery() {
		return consumableCutlery;
	}

	public void setConsumableCutlery(BigDecimal consumableCutlery) {
		this.consumableCutlery = consumableCutlery;
	}

	public BigDecimal getFixedAssets() {
		return fixedAssets;
	}

	public void setFixedAssets(BigDecimal fixedAssets) {
		this.fixedAssets = fixedAssets;
	}

	public BigDecimal getSalary() {
		return salary;
	}

	public void setSalary(BigDecimal salary) {
		this.salary = salary;
	}

	public BigDecimal getMedicalInsurance() {
		return medicalInsurance;
	}

	public void setMedicalInsurance(BigDecimal medicalInsurance) {
		this.medicalInsurance = medicalInsurance;
	}

	public BigDecimal getSalaryIncentive() {
		return salaryIncentive;
	}

	public void setSalaryIncentive(BigDecimal salaryIncentive) {
		this.salaryIncentive = salaryIncentive;
	}

	public BigDecimal getSecurityGuardCharges() {
		return securityGuardCharges;
	}

	public void setSecurityGuardCharges(BigDecimal securityGuardCharges) {
		this.securityGuardCharges = securityGuardCharges;
	}

	public BigDecimal getSalesIncentive() {
		return salesIncentive;
	}

	public void setSalesIncentive(BigDecimal salesIncentive) {
		this.salesIncentive = salesIncentive;
	}

	public BigDecimal getDepreciationOfBike() {
		return depreciationOfBike;
	}

	public void setDepreciationOfBike(BigDecimal depreciationOfBike) {
		this.depreciationOfBike = depreciationOfBike;
	}

	public BigDecimal getFuelCharges() {
		return fuelCharges;
	}

	public void setFuelCharges(BigDecimal fuelCharges) {
		this.fuelCharges = fuelCharges;
	}

	public BigDecimal getVehicleRegularMaintenance() {
		return vehicleRegularMaintenance;
	}

	public void setVehicleRegularMaintenance(BigDecimal vehicleRegularMaintenance) {
		this.vehicleRegularMaintenance = vehicleRegularMaintenance;
	}

	public BigDecimal getVehicleMonthlyMaintenance() {
		return vehicleMonthlyMaintenance;
	}

	public void setVehicleMonthlyMaintenance(BigDecimal vehicleMonthlyMaintenance) {
		this.vehicleMonthlyMaintenance = vehicleMonthlyMaintenance;
	}

	public BigDecimal getPartnerDeliveryCharges() {
		return partnerDeliveryCharges;
	}

	public void setPartnerDeliveryCharges(BigDecimal partnerDeliveryCharges) {
		this.partnerDeliveryCharges = partnerDeliveryCharges;
	}

	public BigDecimal getParkingCharges() {
		return parkingCharges;
	}

	public void setParkingCharges(BigDecimal parkingCharges) {
		this.parkingCharges = parkingCharges;
	}

	public BigDecimal getMarketingAndSampling() {
		return marketingAndSampling;
	}

	public void setMarketingAndSampling(BigDecimal marketingAndSampling) {
		this.marketingAndSampling = marketingAndSampling;
	}

	public BigDecimal getAdvertisementBTL() {
		return advertisementBTL;
	}

	public void setAdvertisementBTL(BigDecimal advertisementBTL) {
		this.advertisementBTL = advertisementBTL;
	}

	public BigDecimal getConsumableMarketing() {
		return consumableMarketing;
	}

	public void setConsumableMarketing(BigDecimal consumableMarketing) {
		this.consumableMarketing = consumableMarketing;
	}

	public BigDecimal getLogisticCharges() {
		return logisticCharges;
	}

	public void setLogisticCharges(BigDecimal logisticCharges) {
		this.logisticCharges = logisticCharges;
	}

	public BigDecimal getEnergyElectricity() {
		return energyElectricity;
	}

	public void setEnergyElectricity(BigDecimal energyElectricity) {
		this.energyElectricity = energyElectricity;
	}

	public BigDecimal getEnergyDG() {
		return energyDG;
	}

	public void setEnergyDG(BigDecimal energyDG) {
		this.energyDG = energyDG;
	}

	public BigDecimal getWaterCharges() {
		return waterCharges;
	}

	public void setWaterCharges(BigDecimal waterCharges) {
		this.waterCharges = waterCharges;
	}

	public BigDecimal getCommunicationInternet() {
		return communicationInternet;
	}

	public void setCommunicationInternet(BigDecimal communicationInternet) {
		this.communicationInternet = communicationInternet;
	}

	public BigDecimal getCommunicationTelephone() {
		return communicationTelephone;
	}

	public void setCommunicationTelephone(BigDecimal communicationTelephone) {
		this.communicationTelephone = communicationTelephone;
	}

	public BigDecimal getCommunicationILL() {
		return communicationILL;
	}

	public void setCommunicationILL(BigDecimal communicationILL) {
		this.communicationILL = communicationILL;
	}

	public BigDecimal getCreditCardTransactionCharges() {
		return creditCardTransactionCharges;
	}

	public void setCreditCardTransactionCharges(BigDecimal creditCardTransactionCharges) {
		this.creditCardTransactionCharges = creditCardTransactionCharges;
	}

	public BigDecimal getVoucherTransactionCharges() {
		return voucherTransactionCharges;
	}

	public void setVoucherTransactionCharges(BigDecimal voucherTransactionCharges) {
		this.voucherTransactionCharges = voucherTransactionCharges;
	}

	public BigDecimal getWalletsTransactionCharges() {
		return walletsTransactionCharges;
	}

	public void setWalletsTransactionCharges(BigDecimal walletsTransactionCharges) {
		this.walletsTransactionCharges = walletsTransactionCharges;
	}

	public BigDecimal getCommissionChannelPartners() {
		return commissionChannelPartners;
	}

	public void setCommissionChannelPartners(BigDecimal commissionChannelPartners) {
		this.commissionChannelPartners = commissionChannelPartners;
	}

	public BigDecimal getCommissionChange() {
		return commissionChange;
	}

	public void setCommissionChange(BigDecimal commissionChange) {
		this.commissionChange = commissionChange;
	}

	public BigDecimal getPayrollProcessingFee() {
		return payrollProcessingFee;
	}

	public void setPayrollProcessingFee(BigDecimal payrollProcessingFee) {
		this.payrollProcessingFee = payrollProcessingFee;
	}

	public BigDecimal getNewsPaper() {
		return newsPaper;
	}

	public void setNewsPaper(BigDecimal newsPaper) {
		this.newsPaper = newsPaper;
	}

	public BigDecimal getLocalConveynece() {
		return localConveynece;
	}

	public void setLocalConveynece(BigDecimal localConveynece) {
		this.localConveynece = localConveynece;
	}

	public BigDecimal getStaffWelfareExpenses() {
		return staffWelfareExpenses;
	}

	public void setStaffWelfareExpenses(BigDecimal staffWelfareExpenses) {
		this.staffWelfareExpenses = staffWelfareExpenses;
	}

	public BigDecimal getCourierCharges() {
		return courierCharges;
	}

	public void setCourierCharges(BigDecimal courierCharges) {
		this.courierCharges = courierCharges;
	}

	public BigDecimal getPrintingAndStationary() {
		return printingAndStationary;
	}

	public void setPrintingAndStationary(BigDecimal printingAndStationary) {
		this.printingAndStationary = printingAndStationary;
	}

	public BigDecimal getBusinessPromotion() {
		return businessPromotion;
	}

	public void setBusinessPromotion(BigDecimal businessPromotion) {
		this.businessPromotion = businessPromotion;
	}

	public BigDecimal getLegalChargesCafe() {
		return legalChargesCafe;
	}

	public void setLegalChargesCafe(BigDecimal legalChargesCafe) {
		this.legalChargesCafe = legalChargesCafe;
	}

	public BigDecimal getLegalCharges() {
		return legalCharges;
	}

	public void setLegalCharges(BigDecimal legalCharges) {
		this.legalCharges = legalCharges;
	}

	public BigDecimal getProfessionalCharges() {
		return professionalCharges;
	}

	public void setProfessionalCharges(BigDecimal professionalCharges) {
		this.professionalCharges = professionalCharges;
	}

	public BigDecimal getCenvatReversal() {
		return cenvatReversal;
	}

	public void setCenvatReversal(BigDecimal cenvatReversal) {
		this.cenvatReversal = cenvatReversal;
	}

	public BigDecimal getPropertyTax() {
		return propertyTax;
	}

	public void setPropertyTax(BigDecimal propertyTax) {
		this.propertyTax = propertyTax;
	}

	public BigDecimal getOpeningLicencesFees() {
		return openingLicencesFees;
	}

	public void setOpeningLicencesFees(BigDecimal openingLicencesFees) {
		this.openingLicencesFees = openingLicencesFees;
	}

	public BigDecimal getRegistrationCharges() {
		return registrationCharges;
	}

	public void setRegistrationCharges(BigDecimal registrationCharges) {
		this.registrationCharges = registrationCharges;
	}

	public BigDecimal getStampDutyCharges() {
		return stampDutyCharges;
	}

	public void setStampDutyCharges(BigDecimal stampDutyCharges) {
		this.stampDutyCharges = stampDutyCharges;
	}

	public BigDecimal getDesigningFees() {
		return designingFees;
	}

	public void setDesigningFees(BigDecimal designingFees) {
		this.designingFees = designingFees;
	}

	public BigDecimal getBuildingMaintenance() {
		return buildingMaintenance;
	}

	public void setBuildingMaintenance(BigDecimal buildingMaintenance) {
		this.buildingMaintenance = buildingMaintenance;
	}

	public BigDecimal getCleaningCharges() {
		return cleaningCharges;
	}

	public void setCleaningCharges(BigDecimal cleaningCharges) {
		this.cleaningCharges = cleaningCharges;
	}

	public BigDecimal getComputerMaintenance() {
		return computerMaintenance;
	}

	public void setComputerMaintenance(BigDecimal computerMaintenance) {
		this.computerMaintenance = computerMaintenance;
	}

	public BigDecimal getPestControlCharges() {
		return pestControlCharges;
	}

	public void setPestControlCharges(BigDecimal pestControlCharges) {
		this.pestControlCharges = pestControlCharges;
	}

	public BigDecimal getEquipmentMaintenance() {
		return equipmentMaintenance;
	}

	public void setEquipmentMaintenance(BigDecimal equipmentMaintenance) {
		this.equipmentMaintenance = equipmentMaintenance;
	}

	public BigDecimal getProntoAMC() {
		return prontoAMC;
	}

	public void setProntoAMC(BigDecimal prontoAMC) {
		this.prontoAMC = prontoAMC;
	}

	public BigDecimal getMaintenanceSalary() {
		return maintenanceSalary;
	}

	public void setMaintenanceSalary(BigDecimal maintenanceSalary) {
		this.maintenanceSalary = maintenanceSalary;
	}

	public BigDecimal getDgRental() {
		return dgRental;
	}

	public void setDgRental(BigDecimal dgRental) {
		this.dgRental = dgRental;
	}

	public BigDecimal getEdcRental() {
		return edcRental;
	}

	public void setEdcRental(BigDecimal edcRental) {
		this.edcRental = edcRental;
	}

	public BigDecimal getSystemRental() {
		return systemRental;
	}

	public void setSystemRental(BigDecimal systemRental) {
		this.systemRental = systemRental;
	}

	public BigDecimal getRoRental() {
		return roRental;
	}

	public void setRoRental(BigDecimal roRental) {
		this.roRental = roRental;
	}

	public BigDecimal getInsuranceCharges() {
		return insuranceCharges;
	}

	public void setInsuranceCharges(BigDecimal insuranceCharges) {
		this.insuranceCharges = insuranceCharges;
	}

	public BigDecimal getPropertyFixRent() {
		return propertyFixRent;
	}

	public void setPropertyFixRent(BigDecimal propertyFixRent) {
		this.propertyFixRent = propertyFixRent;
	}

	public BigDecimal getRevenueShare() {
		return revenueShare;
	}

	public void setRevenueShare(BigDecimal revenueShare) {
		this.revenueShare = revenueShare;
	}

	public BigDecimal getFixCAM() {
		return fixCAM;
	}

	public void setFixCAM(BigDecimal fixCAM) {
		this.fixCAM = fixCAM;
	}

	public BigDecimal getChillingCharges() {
		return chillingCharges;
	}

	public void setChillingCharges(BigDecimal chillingCharges) {
		this.chillingCharges = chillingCharges;
	}

	public BigDecimal getMarketingCharges() {
		return marketingCharges;
	}

	public void setMarketingCharges(BigDecimal marketingCharges) {
		this.marketingCharges = marketingCharges;
	}

	public BigDecimal getPettyCashRentals() {
		return pettyCashRentals;
	}

	public void setPettyCashRentals(BigDecimal pettyCashRentals) {
		this.pettyCashRentals = pettyCashRentals;
	}

	public BigDecimal getPettyTravelExpense() {
		return pettyTravelExpense;
	}

	public void setPettyTravelExpense(BigDecimal pettyTravelExpense) {
		this.pettyTravelExpense = pettyTravelExpense;
	}

	public BigDecimal getMusicRentals() {
		return musicRentals;
	}

	public void setMusicRentals(BigDecimal musicRentals) {
		this.musicRentals = musicRentals;
	}

	public BigDecimal getInternetPartnerRental() {
		return internetPartnerRental;
	}

	public void setInternetPartnerRental(BigDecimal internetPartnerRental) {
		this.internetPartnerRental = internetPartnerRental;
	}

	public BigDecimal getSupportOperations() {
		return supportOperations;
	}

	public void setSupportOperations(BigDecimal supportOperations) {
		this.supportOperations = supportOperations;
	}

	public BigDecimal getSupportHeadOffice() {
		return supportHeadOffice;
	}

	public void setSupportHeadOffice(BigDecimal supportHeadOffice) {
		this.supportHeadOffice = supportHeadOffice;
	}

	public BigDecimal getTechologyPlatformCharges() {
		return techologyPlatformCharges;
	}

	public void setTechologyPlatformCharges(BigDecimal techologyPlatformCharges) {
		this.techologyPlatformCharges = techologyPlatformCharges;
	}

	public BigDecimal getTechologyTraining() {
		return techologyTraining;
	}

	public void setTechologyTraining(BigDecimal techologyTraining) {
		this.techologyTraining = techologyTraining;
	}

	public BigDecimal getTechologyMail() {
		return techologyMail;
	}

	public void setTechologyMail(BigDecimal techologyMail) {
		this.techologyMail = techologyMail;
	}

	public BigDecimal getTechologyOthers() {
		return techologyOthers;
	}

	public void setTechologyOthers(BigDecimal techologyOthers) {
		this.techologyOthers = techologyOthers;
	}

	public BigDecimal getCorporateMarketingDigital() {
		return corporateMarketingDigital;
	}

	public void setCorporateMarketingDigital(BigDecimal corporateMarketingDigital) {
		this.corporateMarketingDigital = corporateMarketingDigital;
	}

	public BigDecimal getCorporateMarketingAdvOffline() {
		return corporateMarketingAdvOffline;
	}

	public void setCorporateMarketingAdvOffline(BigDecimal corporateMarketingAdvOffline) {
		this.corporateMarketingAdvOffline = corporateMarketingAdvOffline;
	}

	public BigDecimal getCorporateMarketingAdvOnline() {
		return corporateMarketingAdvOnline;
	}

	public void setCorporateMarketingAdvOnline(BigDecimal corporateMarketingAdvOnline) {
		this.corporateMarketingAdvOnline = corporateMarketingAdvOnline;
	}

	public BigDecimal getCorporateMarketingOutdoor() {
		return corporateMarketingOutdoor;
	}

	public void setCorporateMarketingOutdoor(BigDecimal corporateMarketingOutdoor) {
		this.corporateMarketingOutdoor = corporateMarketingOutdoor;
	}

	public BigDecimal getCorporateMarketingPnS() {
		return corporateMarketingPnS;
	}

	public void setCorporateMarketingPnS(BigDecimal corporateMarketingPnS) {
		this.corporateMarketingPnS = corporateMarketingPnS;
	}

	public BigDecimal getCorporateMarketingAgencyFees() {
		return corporateMarketingAgencyFees;
	}

	public void setCorporateMarketingAgencyFees(BigDecimal corporateMarketingAgencyFees) {
		this.corporateMarketingAgencyFees = corporateMarketingAgencyFees;
	}

	public BigDecimal getStockVariance() {
		return stockVariance;
	}

	public void setStockVariance(BigDecimal stockVariance) {
		this.stockVariance = stockVariance;
	}

	public BigDecimal getTechnologyVariable() {
		return technologyVariable;
	}

	public void setTechnologyVariable(BigDecimal technologyVariable) {
		this.technologyVariable = technologyVariable;
	}

	public BigDecimal getMarketingLocalStoreVariable() {
		return marketingLocalStoreVariable;
	}

	public void setMarketingLocalStoreVariable(BigDecimal marketingLocalStoreVariable) {
		this.marketingLocalStoreVariable = marketingLocalStoreVariable;
	}

	public BigDecimal getSupportVariable() {
		return supportVariable;
	}

	public void setSupportVariable(BigDecimal supportVariable) {
		this.supportVariable = supportVariable;
	}

	public BigDecimal getMaintenanceVariable() {
		return maintenanceVariable;
	}

	public void setMaintenanceVariable(BigDecimal maintenanceVariable) {
		this.maintenanceVariable = maintenanceVariable;
	}

	public BigDecimal getCorporateMarketingVariable() {
		return corporateMarketingVariable;
	}

	public void setCorporateMarketingVariable(BigDecimal corporateMarketingVariable) {
		this.corporateMarketingVariable = corporateMarketingVariable;
	}

	public BigDecimal getFixedCostVariable() {
		return fixedCostVariable;
	}

	public void setFixedCostVariable(BigDecimal fixedCostVariable) {
		this.fixedCostVariable = fixedCostVariable;
	}

	public BigDecimal getManpowerVariable() {
		return manpowerVariable;
	}

	public void setManpowerVariable(BigDecimal manpowerVariable) {
		this.manpowerVariable = manpowerVariable;
	}

	public BigDecimal getSupplyChainVariable() {
		return supplyChainVariable;
	}

	public void setSupplyChainVariable(BigDecimal supplyChainVariable) {
		this.supplyChainVariable = supplyChainVariable;
	}

	public BigDecimal getDeliveryChargesVariable() {
		return deliveryChargesVariable;
	}

	public void setDeliveryChargesVariable(BigDecimal deliveryChargesVariable) {
		this.deliveryChargesVariable = deliveryChargesVariable;
	}

	public BigDecimal getAnyOtherVariable1() {
		return anyOtherVariable1;
	}

	public void setAnyOtherVariable1(BigDecimal anyOtherVariable1) {
		this.anyOtherVariable1 = anyOtherVariable1;
	}

	public BigDecimal getAnyOtherVariable2() {
		return anyOtherVariable2;
	}

	public void setAnyOtherVariable2(BigDecimal anyOtherVariable2) {
		this.anyOtherVariable2 = anyOtherVariable2;
	}

	public BigDecimal getAnyOtherVariable3() {
		return anyOtherVariable3;
	}

	public void setAnyOtherVariable3(BigDecimal anyOtherVariable3) {
		this.anyOtherVariable3 = anyOtherVariable3;
	}

	public String getOnRevenueShare() {
		return onRevenueShare;
	}

	public void setOnRevenueShare(String onRevenueShare) {
		this.onRevenueShare = onRevenueShare;
	}

	public BigDecimal getElectricityMeter1FixedCharge() {
		return electricityMeter1FixedCharge;
	}

	public void setElectricityMeter1FixedCharge(BigDecimal electricityMeter1FixedCharge) {
		this.electricityMeter1FixedCharge = electricityMeter1FixedCharge;
	}

	public BigDecimal getElectricityMeter1PerUnitCharge() {
		return electricityMeter1PerUnitCharge;
	}

	public void setElectricityMeter1PerUnitCharge(BigDecimal electricityMeter1PerUnitCharge) {
		this.electricityMeter1PerUnitCharge = electricityMeter1PerUnitCharge;
	}

	public BigDecimal getElectricityMeter1TaxPercentage() {
		return electricityMeter1TaxPercentage;
	}

	public void setElectricityMeter1TaxPercentage(BigDecimal electricityMeter1TaxPercentage) {
		this.electricityMeter1TaxPercentage = electricityMeter1TaxPercentage;
	}

	public BigDecimal getElectricityMeter1OtherCharge() {
		return electricityMeter1OtherCharge;
	}

	public void setElectricityMeter1OtherCharge(BigDecimal electricityMeter1OtherCharge) {
		this.electricityMeter1OtherCharge = electricityMeter1OtherCharge;
	}

	public BigDecimal getElectricityMeter2FixedCharge() {
		return electricityMeter2FixedCharge;
	}

	public void setElectricityMeter2FixedCharge(BigDecimal electricityMeter2FixedCharge) {
		this.electricityMeter2FixedCharge = electricityMeter2FixedCharge;
	}

	public BigDecimal getElectricityMeter2PerUnitCharge() {
		return electricityMeter2PerUnitCharge;
	}

	public void setElectricityMeter2PerUnitCharge(BigDecimal electricityMeter2PerUnitCharge) {
		this.electricityMeter2PerUnitCharge = electricityMeter2PerUnitCharge;
	}

	public BigDecimal getElectricityMeter2TaxPercentage() {
		return electricityMeter2TaxPercentage;
	}

	public void setElectricityMeter2TaxPercentage(BigDecimal electricityMeter2TaxPercentage) {
		this.electricityMeter2TaxPercentage = electricityMeter2TaxPercentage;
	}

	public BigDecimal getElectricityMeter2OtherCharge() {
		return electricityMeter2OtherCharge;
	}

	public void setElectricityMeter2OtherCharge(BigDecimal electricityMeter2OtherCharge) {
		this.electricityMeter2OtherCharge = electricityMeter2OtherCharge;
	}

	public BigDecimal getElectricityMeter3FixedCharge() {
		return electricityMeter3FixedCharge;
	}

	public void setElectricityMeter3FixedCharge(BigDecimal electricityMeter3FixedCharge) {
		this.electricityMeter3FixedCharge = electricityMeter3FixedCharge;
	}

	public BigDecimal getElectricityMeter3PerUnitCharge() {
		return electricityMeter3PerUnitCharge;
	}

	public void setElectricityMeter3PerUnitCharge(BigDecimal electricityMeter3PerUnitCharge) {
		this.electricityMeter3PerUnitCharge = electricityMeter3PerUnitCharge;
	}

	public BigDecimal getElectricityMeter3TaxPercentage() {
		return electricityMeter3TaxPercentage;
	}

	public void setElectricityMeter3TaxPercentage(BigDecimal electricityMeter3TaxPercentage) {
		this.electricityMeter3TaxPercentage = electricityMeter3TaxPercentage;
	}

	public BigDecimal getElectricityMeter3OtherCharge() {
		return electricityMeter3OtherCharge;
	}

	public void setElectricityMeter3OtherCharge(BigDecimal electricityMeter3OtherCharge) {
		this.electricityMeter3OtherCharge = electricityMeter3OtherCharge;
	}

	public BigDecimal getDgMeterFixedCharge() {
		return dgMeterFixedCharge;
	}

	public void setDgMeterFixedCharge(BigDecimal dgMeterFixedCharge) {
		this.dgMeterFixedCharge = dgMeterFixedCharge;
	}

	public BigDecimal getDgMeterPerUnitCharge() {
		return dgMeterPerUnitCharge;
	}

	public void setDgMeterPerUnitCharge(BigDecimal dgMeterPerUnitCharge) {
		this.dgMeterPerUnitCharge = dgMeterPerUnitCharge;
	}

	public BigDecimal getDgMeterTaxPercentage() {
		return dgMeterTaxPercentage;
	}

	public void setDgMeterTaxPercentage(BigDecimal dgMeterTaxPercentage) {
		this.dgMeterTaxPercentage = dgMeterTaxPercentage;
	}

	public BigDecimal getDgMeterOtherCharge() {
		return dgMeterOtherCharge;
	}

	public void setDgMeterOtherCharge(BigDecimal dgMeterOtherCharge) {
		this.dgMeterOtherCharge = dgMeterOtherCharge;
	}

}
