package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.Date;

public class OrderStatusUpdate implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 29814304356357912L;
	private Integer orderId;
	private Integer partnerId;
	private Integer customerId;
	private OrderStatus status;
	private Date date;

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public OrderStatus getStatus() {
		return status;
	}

	public void setStatus(OrderStatus status) {
		this.status = status;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public Integer getPartnerId() {
		return partnerId;
	}

	public void setPartnerId(Integer partnerId) {
		this.partnerId = partnerId;
	}

	
}
