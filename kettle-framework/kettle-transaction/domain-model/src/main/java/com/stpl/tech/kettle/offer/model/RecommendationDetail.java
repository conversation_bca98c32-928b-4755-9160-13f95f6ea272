package com.stpl.tech.kettle.offer.model;

public class RecommendationDetail {

	private int optionResultId;
	private int optionResultEventId;
	private Integer id;
	private String name;
	private String desc;
	private boolean offer;
	private Integer discount;
	private String couponCode;
	private boolean availed;
	private Integer quantity;
	private String appliedBy;
	private Integer orderId;

	public int getOptionResultId() {
		return optionResultId;
	}

	public void setOptionResultId(int optionResultId) {
		this.optionResultId = optionResultId;
	}

	public int getOptionResultEventId() {
		return optionResultEventId;
	}

	public void setOptionResultEventId(int optionResultEventId) {
		this.optionResultEventId = optionResultEventId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer firstRecommendation) {
		this.id = firstRecommendation;
	}

	public boolean isOffer() {
		return offer;
	}

	public void setOffer(boolean offer) {
		this.offer = offer;
	}

	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	public boolean isAvailed() {
		return availed;
	}

	public void setAvailed(boolean availed) {
		this.availed = availed;
	}

	public String getAppliedBy() {
		return appliedBy;
	}

	public void setAppliedBy(String appliedBy) {
		this.appliedBy = appliedBy;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	
	public Integer getDiscount() {
		return discount;
	}

	public void setDiscount(Integer discount) {
		this.discount = discount;
	}
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String description) {
		this.desc = description;
	}

	
}
