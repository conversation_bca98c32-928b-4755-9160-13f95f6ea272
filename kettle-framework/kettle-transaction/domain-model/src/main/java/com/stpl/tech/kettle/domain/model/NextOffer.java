package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.List;

public class NextOffer implements Serializable {
	private static final long serialVersionUID = -4649921243503268976L;
	private Integer brandId;
	private Integer customerCampaignOfferDetailId;
	private String contactNumber;
	private Integer customerId;
	private String firstName;
	private boolean available;
	private String offerCode;
	private String validityFrom;
	private String validityTill;
	private String text;
	private String customerType;
	private String contentUrl;
	private String notificationType;
	private Boolean isExistingOffer;
	private Integer daysLeft;
	private String channelPartner;
	private Integer channelPartnerId;
	private String couponType;
	private Integer maxUsage;
	private String tnc;
	private String crmAppBannerUrl;
	private int minBillValue;
	private int offerValue;
	private String offerValueType;
	private List<Integer> productList;

	public NextOffer() {
		super();
	}

	public NextOffer(String firstName, String text, String validityTill, String offerCode, String channelPartner){
		this.firstName = firstName;
		this.text = text;
		this.validityTill = validityTill;
		this.offerCode = offerCode;
		this.channelPartner = channelPartner;
	}

	public NextOffer(Integer brandId, String contactNumber,Integer customerId,String firstName, boolean available, String offerCode, String validityFrom, String validityTill, String text,
			String customerType, String contentUrl, String notificationType, String channelPartner,String couponType, Integer channelPartnerId) {
		super();
		this.brandId = brandId;
		this.contactNumber = contactNumber;
		this.customerId = customerId;
		this.firstName = firstName;
		this.available = available;
		this.offerCode = offerCode;
		this.validityFrom = validityFrom;
		this.validityTill = validityTill;
		this.text = text;
		this.customerType = customerType;
		this.contentUrl = contentUrl;
		this.notificationType = notificationType;
		this.channelPartner = channelPartner;
		this.couponType= couponType;
		this.channelPartnerId = channelPartnerId;
	}



	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public String getFirstName() {
		return firstName;
	}

	public String getCrmAppBannerUrl() {
		return crmAppBannerUrl;
	}

	public void setCrmAppBannerUrl(String crmAppBannerUrl) {
		this.crmAppBannerUrl = crmAppBannerUrl;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public boolean isAvailable() {
		return available;
	}

	public void setAvailable(boolean available) {
		this.available = available;
	}

	public String getOfferCode() {
		return offerCode;
	}

	public void setOfferCode(String offerCode) {
		this.offerCode = offerCode;
	}

	public String getValidityFrom() {
		return validityFrom;
	}

	public void setValidityFrom(String validityFrom) {
		this.validityFrom = validityFrom;
	}

	public String getValidityTill() {
		return validityTill;
	}

	public void setValidityTill(String validityTill) {
		this.validityTill = validityTill;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public String getCustomerType() {
		return customerType;
	}

	public void setCustomerType(String customerType) {
		this.customerType = customerType;
	}

	public String getContentUrl() {
		return contentUrl;
	}

	public void setContentUrl(String contentUrl) {
		this.contentUrl = contentUrl;
	}

	public String getNotificationType() {
		return notificationType;
	}

	public void setNotificationType(String notificationType) {
		this.notificationType = notificationType;
	}

	public Boolean getExistingOffer() {
		return isExistingOffer;
	}

	public void setExistingOffer(Boolean existingOffer) {
		isExistingOffer = existingOffer;
	}

	public Integer getDaysLeft() {
		return daysLeft;
	}

	public void setDaysLeft(Integer daysLeft) {
		this.daysLeft = daysLeft;
	}

	public String getChannelPartner() {
		return channelPartner;
	}

	public void setChannelPartner(String channelPartner) {
		this.channelPartner = channelPartner;
	}

	public String getCouponType() {
		return couponType;
	}

	public void setCouponType(String couponType) {
		this.couponType = couponType;
	}

	public Integer getChannelPartnerId() {
		return channelPartnerId;
	}

	public void setChannelPartnerId(Integer channelPartnerId) {
		this.channelPartnerId = channelPartnerId;
	}

	public Integer getCustomerCampaignOfferDetailId() {
		return customerCampaignOfferDetailId;
	}

	public void setCustomerCampaignOfferDetailId(Integer customerCampaignOfferDetailId) {
		this.customerCampaignOfferDetailId = customerCampaignOfferDetailId;
	}

	public Integer getMaxUsage() {
		return maxUsage;
	}

	public void setMaxUsage(Integer maxUsage) {
		this.maxUsage = maxUsage;
	}

	public String getTnc() {
		return tnc;
	}

	public void setTnc(String tnc) {
		this.tnc = tnc;
	}

	public int getMinBillValue() {
		return minBillValue;
	}

	public void setMinBillValue(int minBillValue) {
		this.minBillValue = minBillValue;
	}

	public int getOfferValue() {
		return offerValue;
	}

	public void setOfferValue(int offerValue) {
		this.offerValue = offerValue;
	}

	public String getOfferValueType() {
		return offerValueType;
	}

	public void setOfferValueType(String offerValueType) {
		this.offerValueType = offerValueType;
	}

	public List<Integer> getProductList() {
		return productList;
	}

	public void setProductList(List<Integer> productList) {
		this.productList = productList;
	}

	@Override
	public String toString() {
		return "NextOffer{" +
				"brandId=" + brandId +
				", customerCampaignOfferDetailId=" + customerCampaignOfferDetailId +
				", contactNumber='" + contactNumber + '\'' +
				", customerId=" + customerId +
				", firstName='" + firstName + '\'' +
				", available=" + available +
				", offerCode='" + offerCode + '\'' +
				", validityFrom='" + validityFrom + '\'' +
				", validityTill='" + validityTill + '\'' +
				", text='" + text + '\'' +
				", customerType='" + customerType + '\'' +
				", contentUrl='" + contentUrl + '\'' +
				", notificationType='" + notificationType + '\'' +
				", isExistingOffer=" + isExistingOffer +
				", daysLeft=" + daysLeft +
				", channelPartner='" + channelPartner + '\'' +
				", channelPartnerId=" + channelPartnerId +
				", couponType='" + couponType + '\'' +
				", maxUsage=" + maxUsage +
				", tnc='" + tnc + '\'' +
				", crmAppBannerUrl='" + crmAppBannerUrl + '\'' +
				'}';
	}
}
