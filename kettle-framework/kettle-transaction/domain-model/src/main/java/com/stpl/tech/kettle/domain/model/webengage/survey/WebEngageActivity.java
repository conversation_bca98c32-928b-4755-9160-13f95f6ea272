/**
 * 
 */
package com.stpl.tech.kettle.domain.model.webengage.survey;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class WebEngageActivity implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8354066137584053932L;

	private String platform;

	private String browserVersion;

	private String browser;

	private String activityOn;

	private String country;

	private String city;

	private String ip;

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public String getBrowserVersion() {
		return browserVersion;
	}

	public void setBrowserVersion(String browserVersion) {
		this.browserVersion = browserVersion;
	}

	public String getBrowser() {
		return browser;
	}

	public void setBrowser(String browser) {
		this.browser = browser;
	}

	public String getActivityOn() {
		return activityOn;
	}

	public void setActivityOn(String activityOn) {
		this.activityOn = activityOn;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	@Override
	public String toString() {
		return "ClassPojo [platform = " + platform + ", browserVersion = " + browserVersion + ", browser = " + browser
				+ ", activityOn = " + activityOn + ", country = " + country + ", city = " + city + ", ip = " + ip + "]";
	}
}
