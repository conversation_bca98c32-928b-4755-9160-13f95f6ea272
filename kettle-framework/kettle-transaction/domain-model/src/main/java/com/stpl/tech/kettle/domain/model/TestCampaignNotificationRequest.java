package com.stpl.tech.kettle.domain.model;

public class TestCampaignNotificationRequest{
    private String customerType;
    private String strategy;
    private Integer journey;
    private String couponDescription;
    private String contactNumber;
    private String firstName;
    private Integer validityInDays ;
    private Boolean couponClone;
    private String prefix;
    private String sourceCoupon;
    private String notificationType;

    public TestCampaignNotificationRequest() {
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public Integer getJourney() {
        return journey;
    }

    public void setJourney(Integer journey) {
        this.journey = journey;
    }

    public String getCouponDescription() {
        return couponDescription;
    }

    public void setCouponDescription(String couponDescription) {
        this.couponDescription = couponDescription;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public Boolean getCouponClone() {
        return couponClone;
    }

    public void setCouponClone(Boolean couponClone) {
        this.couponClone = couponClone;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public Integer getValidityInDays() {
        return validityInDays;
    }

    public void setValidityInDays(Integer validityInDays) {
        this.validityInDays = validityInDays;
    }

    public String getSourceCoupon() {
        return sourceCoupon;
    }

    public void setSourceCoupon(String sourceCoupon) {
        this.sourceCoupon = sourceCoupon;
    }
}
