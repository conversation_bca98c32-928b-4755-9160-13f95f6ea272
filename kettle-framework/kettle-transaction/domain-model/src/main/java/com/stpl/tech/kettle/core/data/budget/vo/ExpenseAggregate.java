/**
 *
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class ExpenseAggregate {


    private BigDecimal vehicleRegularMaintenance = new BigDecimal(0d);
    private BigDecimal parkingCharges = new BigDecimal(0d);

    private BigDecimal buildingMaintenance = new BigDecimal(0d);
    private BigDecimal computerMaintenance = new BigDecimal(0d);
    private BigDecimal equipmentMaintenance = new BigDecimal(0d);

    private BigDecimal expenseOthers = new BigDecimal(0d);

    private BigDecimal energyDGRunningCafe = new BigDecimal(0d);
    private BigDecimal marketingNPI = new BigDecimal(0d);


    //	private BigDecimal travellingExpenseODC = new BigDecimal(0d);
    private BigDecimal travellingExpense = new BigDecimal(0d);
    private BigDecimal waterChargesCafe = new BigDecimal(0d);
    //TODO Do Plumbing for maintenancePestControlCafe
    private BigDecimal maintenancePestControlCafe = new BigDecimal(0d);
    private BigDecimal conveyanceOdc = new BigDecimal(0d);

    private BigDecimal conveyanceOperation = new BigDecimal(0d);
    private BigDecimal marketingDataAnalysis = new BigDecimal(0d);
    private BigDecimal conveyanceOthers = new BigDecimal(0d);
    private BigDecimal cogsOthers = new BigDecimal(0d);
    private BigDecimal conveyanceMarketing = new BigDecimal(0d);


    private BigDecimal fuelChargesCafe = new BigDecimal(0d);

    private BigDecimal commissionChangeCafe = new BigDecimal(0d);

    private BigDecimal photoCopyExpensesCafe = new BigDecimal(0d);

    private BigDecimal printingAndStationaryCafe = new BigDecimal(0d);

    private BigDecimal staffWelfareExpensesCafe = new BigDecimal(0d);


    private BigDecimal cleaningChargesCafe = new BigDecimal(0d);

    private BigDecimal businessPromotionCafe = new BigDecimal(0d);

    private BigDecimal courierChargesCafe = new BigDecimal(0d);

    private BigDecimal newsPaperCafe = new BigDecimal(0d);


    public BigDecimal getVehicleRegularMaintenance() {
        return vehicleRegularMaintenance;
    }

    public void setVehicleRegularMaintenance(BigDecimal vehicleRegularMaintenance) {
        this.vehicleRegularMaintenance = vehicleRegularMaintenance;
    }

    public BigDecimal getParkingCharges() {
        return parkingCharges;
    }

    public void setParkingCharges(BigDecimal parkingCharges) {
        this.parkingCharges = parkingCharges;
    }


    public BigDecimal getBuildingMaintenance() {
        return buildingMaintenance;
    }

    public void setBuildingMaintenance(BigDecimal buildingMaintenance) {
        this.buildingMaintenance = buildingMaintenance;
    }

    public BigDecimal getComputerMaintenance() {
        return computerMaintenance;
    }

    public void setComputerMaintenance(BigDecimal computerMaintenance) {
        this.computerMaintenance = computerMaintenance;
    }

    public BigDecimal getEquipmentMaintenance() {
        return equipmentMaintenance;
    }

    public void setEquipmentMaintenance(BigDecimal equipmentMaintenance) {
        this.equipmentMaintenance = equipmentMaintenance;
    }

    public BigDecimal getExpenseOthers() {
        return expenseOthers;
    }

    public void setExpenseOthers(BigDecimal expenseOthers) {
        this.expenseOthers = expenseOthers;
    }


    public BigDecimal getEnergyDGRunningCafe() {
        return energyDGRunningCafe;
    }

    public void setEnergyDGRunningCafe(BigDecimal energyDGRunningCafe) {
        this.energyDGRunningCafe = energyDGRunningCafe;
    }

    public BigDecimal getMarketingNPI() {
        return marketingNPI;
    }

    public void setMarketingNPI(BigDecimal marketingNPI) {
        this.marketingNPI = marketingNPI;
    }

//	public BigDecimal getTravellingExpenseODC() {
//		return travellingExpenseODC;
//	}
//
//	public void setTravellingExpenseODC(BigDecimal travellingExpenseODC) {
//		this.travellingExpenseODC = travellingExpenseODC;
//	}

    public BigDecimal getTravellingExpense() {
        return travellingExpense;
    }

    public void setTravellingExpense(BigDecimal travellingExpense) {
        this.travellingExpense = travellingExpense;
    }

    public BigDecimal getWaterChargesCafe() {
        return waterChargesCafe;
    }

    public void setWaterChargesCafe(BigDecimal waterChargesCafe) {
        this.waterChargesCafe = waterChargesCafe;
    }

    public BigDecimal getMaintenancePestControlCafe() {
        return maintenancePestControlCafe;
    }

    public void setMaintenancePestControlCafe(BigDecimal maintenancePestControlCafe) {
        this.maintenancePestControlCafe = maintenancePestControlCafe;
    }

    public BigDecimal getConveyanceOdc() {
        return conveyanceOdc;
    }

    public void setConveyanceOdc(BigDecimal conveyanceOdc) {
        this.conveyanceOdc = conveyanceOdc;
    }

    public BigDecimal getFuelChargesCafe() {
        return fuelChargesCafe;
    }

    public void setFuelChargesCafe(BigDecimal fuelChargesCafe) {
        this.fuelChargesCafe = fuelChargesCafe;
    }

    public BigDecimal getCommissionChangeCafe() {
        return commissionChangeCafe;
    }

    public void setCommissionChangeCafe(BigDecimal commissionChangeCafe) {
        this.commissionChangeCafe = commissionChangeCafe;
    }

    public BigDecimal getPhotoCopyExpensesCafe() {
        return photoCopyExpensesCafe;
    }

    public void setPhotoCopyExpensesCafe(BigDecimal photoCopyExpensesCafe) {
        this.photoCopyExpensesCafe = photoCopyExpensesCafe;
    }

    public BigDecimal getPrintingAndStationaryCafe() {
        return printingAndStationaryCafe;
    }

    public void setPrintingAndStationaryCafe(BigDecimal printingAndStationaryCafe) {
        this.printingAndStationaryCafe = printingAndStationaryCafe;
    }

    public BigDecimal getStaffWelfareExpensesCafe() {
        return staffWelfareExpensesCafe;
    }

    public void setStaffWelfareExpensesCafe(BigDecimal staffWelfareExpensesCafe) {
        this.staffWelfareExpensesCafe = staffWelfareExpensesCafe;
    }

    public BigDecimal getCleaningChargesCafe() {
        return cleaningChargesCafe;
    }

    public void setCleaningChargesCafe(BigDecimal cleaningChargesCafe) {
        this.cleaningChargesCafe = cleaningChargesCafe;
    }

    public BigDecimal getBusinessPromotionCafe() {
        return businessPromotionCafe;
    }

    public void setBusinessPromotionCafe(BigDecimal businessPromotionCafe) {
        this.businessPromotionCafe = businessPromotionCafe;
    }

    public BigDecimal getCourierChargesCafe() {
        return courierChargesCafe;
    }

    public void setCourierChargesCafe(BigDecimal courierChargesCafe) {
        this.courierChargesCafe = courierChargesCafe;
    }

    public BigDecimal getNewsPaperCafe() {
        return newsPaperCafe;
    }

    public void setNewsPaperCafe(BigDecimal newsPaperCafe) {
        this.newsPaperCafe = newsPaperCafe;
    }

    public BigDecimal getConveyanceOperation() {
        return conveyanceOperation;
    }

    public void setConveyanceOperation(BigDecimal conveyanceOperation) {
        this.conveyanceOperation = conveyanceOperation;
    }

    public BigDecimal getMarketingDataAnalysis() {
        return marketingDataAnalysis;
    }

    public void setMarketingDataAnalysis(BigDecimal marketingDataAnalysis) {
        this.marketingDataAnalysis = marketingDataAnalysis;
    }

    public BigDecimal getConveyanceOthers() {
        return conveyanceOthers;
    }

    public void setConveyanceOthers(BigDecimal conveyanceOthers) {
        this.conveyanceOthers = conveyanceOthers;
    }

    public BigDecimal getCogsOthers() {
        return cogsOthers;
    }

    public void setCogsOthers(BigDecimal cogsOthers) {
        this.cogsOthers = cogsOthers;
    }

    public BigDecimal getConveyanceMarketing() {
        return conveyanceMarketing;
    }

    public void setConveyanceMarketing(BigDecimal conveyanceMarketing) {
        this.conveyanceMarketing = conveyanceMarketing;
    }
}
