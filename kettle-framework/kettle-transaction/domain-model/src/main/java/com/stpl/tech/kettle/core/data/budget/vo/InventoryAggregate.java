/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class InventoryAggregate {

	private BigDecimal dineInCogs;
	private BigDecimal deliveryCogs;
	private BigDecimal employeeMealCogs;
	private BigDecimal stockVariance;
	private BigDecimal zeroVariance;
	private BigDecimal variancePCC;
	private BigDecimal varianceYC;
	private BigDecimal varianceOthers;

	private BigDecimal dineInCogsTax;
	private BigDecimal deliveryCogsTax;
	private BigDecimal employeeMealCogsTax;
	private BigDecimal stockVarianceTax;
	private BigDecimal zeroVarianceTax;
	private BigDecimal variancePCCTax;
	private BigDecimal varianceYCTax;
	private BigDecimal varianceOthersTax;


	private BigDecimal stockVarianceMTD;
	private BigDecimal zeroVarianceMTD;
	private BigDecimal variancePccMTD;
	private BigDecimal varianceYcMTD;
	private BigDecimal varianceOthersMTD;

	public BigDecimal getDineInCogs() {
		return dineInCogs;
	}

	public void setDineInCogs(BigDecimal dineInCogs) {
		this.dineInCogs = dineInCogs;
	}

	public BigDecimal getDeliveryCogs() {
		return deliveryCogs;
	}

	public void setDeliveryCogs(BigDecimal deliveryCogs) {
		this.deliveryCogs = deliveryCogs;
	}

	public BigDecimal getEmployeeMealCogs() {
		return employeeMealCogs;
	}

	public void setEmployeeMealCogs(BigDecimal employeeMealCogs) {
		this.employeeMealCogs = employeeMealCogs;
	}

	public BigDecimal getStockVariance() {
		return stockVariance;
	}

	public void setStockVariance(BigDecimal stockVariance) {
		this.stockVariance = stockVariance;
	}

	public BigDecimal getStockVarianceMTD() {
		return stockVarianceMTD;
	}

	public void setStockVarianceMTD(BigDecimal stockVarianceMTD) {
		this.stockVarianceMTD = stockVarianceMTD;
	}

	public BigDecimal getDineInCogsTax() {
		return dineInCogsTax;
	}

	public void setDineInCogsTax(BigDecimal dineInCogsTax) {
		this.dineInCogsTax = dineInCogsTax;
	}

	public BigDecimal getDeliveryCogsTax() {
		return deliveryCogsTax;
	}

	public void setDeliveryCogsTax(BigDecimal deliveryCogsTax) {
		this.deliveryCogsTax = deliveryCogsTax;
	}

	public BigDecimal getEmployeeMealCogsTax() {
		return employeeMealCogsTax;
	}

	public void setEmployeeMealCogsTax(BigDecimal employeeMealCogsTax) {
		this.employeeMealCogsTax = employeeMealCogsTax;
	}

	public BigDecimal getStockVarianceTax() {
		return stockVarianceTax;
	}

	public void setStockVarianceTax(BigDecimal stockVarianceTax) {
		this.stockVarianceTax = stockVarianceTax;
	}

	public BigDecimal getZeroVariance() {
		return zeroVariance;
	}

	public void setZeroVariance(BigDecimal zeroVariance) {
		this.zeroVariance = zeroVariance;
	}

	public BigDecimal getVariancePCC() {
		return variancePCC;
	}

	public void setVariancePCC(BigDecimal variancePCC) {
		this.variancePCC = variancePCC;
	}

	public BigDecimal getVarianceYC() {
		return varianceYC;
	}

	public void setVarianceYC(BigDecimal varianceYC) {
		this.varianceYC = varianceYC;
	}

	public BigDecimal getVarianceOthers() {
		return varianceOthers;
	}

	public void setVarianceOthers(BigDecimal varianceOthers) {
		this.varianceOthers = varianceOthers;
	}

	public BigDecimal getZeroVarianceMTD() {
		return zeroVarianceMTD;
	}

	public void setZeroVarianceMTD(BigDecimal zeroVarianceMTD) {
		this.zeroVarianceMTD = zeroVarianceMTD;
	}

	public BigDecimal getVariancePccMTD() {
		return variancePccMTD;
	}

	public void setVariancePccMTD(BigDecimal variancePccMTD) {
		this.variancePccMTD = variancePccMTD;
	}

	public BigDecimal getVarianceYcMTD() {
		return varianceYcMTD;
	}

	public void setVarianceYcMTD(BigDecimal varianceYcMTD) {
		this.varianceYcMTD = varianceYcMTD;
	}

	public BigDecimal getVarianceOthersMTD() {
		return varianceOthersMTD;
	}

	public void setVarianceOthersMTD(BigDecimal varianceOthersMTD) {
		this.varianceOthersMTD = varianceOthersMTD;
	}

	public BigDecimal getZeroVarianceTax() {
		return zeroVarianceTax;
	}

	public void setZeroVarianceTax(BigDecimal zeroVarianceTax) {
		this.zeroVarianceTax = zeroVarianceTax;
	}

	public BigDecimal getVariancePCCTax() {
		return variancePCCTax;
	}

	public void setVariancePCCTax(BigDecimal variancePCCTax) {
		this.variancePCCTax = variancePCCTax;
	}

	public BigDecimal getVarianceYCTax() {
		return varianceYCTax;
	}

	public void setVarianceYCTax(BigDecimal varianceYCTax) {
		this.varianceYCTax = varianceYCTax;
	}

	public BigDecimal getVarianceOthersTax() {
		return varianceOthersTax;
	}

	public void setVarianceOthersTax(BigDecimal varianceOthersTax) {
		this.varianceOthersTax = varianceOthersTax;
	}
}
