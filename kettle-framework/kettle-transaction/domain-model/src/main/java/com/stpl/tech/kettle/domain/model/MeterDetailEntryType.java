//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.27 at 05:06:36 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for MeterDetailEntryType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="MeterDetailEntryType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="DAY_START"/&gt;
 *     &lt;enumeration value="DAY_CLOSE"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "MeterDetailEntryType")
@XmlEnum
public enum MeterDetailEntryType {

    @XmlEnumValue("DAY_START")
    DAY_START("DAY_START"),
    @XmlEnumValue("DAY_CLOSE")
    DAY_CLOSE("DAY_CLOSE");
    private final String value;

    MeterDetailEntryType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static MeterDetailEntryType fromValue(String v) {
        for (MeterDetailEntryType c: MeterDetailEntryType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
