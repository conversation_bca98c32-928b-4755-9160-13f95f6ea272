/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.28 at 05:05:25 PM IST 
//


package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="sfx_order_id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="order_status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="allot_time" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="dispatch_time" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="delivery_time" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="cancel_reason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="rider_name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="rider_contact" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "sfxOrderId",
    "orderStatus",
    "allotTime",
    "dispatchTime",
    "deliveryTime",
    "cancelReason",
    "riderName",
    "riderContact"
})
@XmlRootElement(name = "SFXCallbackObject")
@JsonIgnoreProperties(ignoreUnknown=true)
public class SFXCallbackObject implements CallbackObject{

	@XmlElement(name = "sfx_order_id", required = true)
	@JsonProperty("sfx_order_id")
	protected String sfxOrderId;
	@XmlElement(name = "order_status", required = true)
	@JsonProperty("order_status")
	protected String orderStatus;
	@XmlElement(name = "allot_time", required = true)
	@JsonProperty("allot_time")
	protected String allotTime;
	@XmlElement(name = "dispatch_time", required = true)
	@JsonProperty("dispatch_time")
	protected String dispatchTime;
	@XmlElement(name = "delivery_time", required = true)
	@JsonProperty("delivery_time")
	protected String deliveryTime;
	@XmlElement(name = "cancel_reason", required = true)
	@JsonProperty("cancel_reason")
	protected String cancelReason;
	@XmlElement(name = "rider_name", required = true)
	@JsonProperty("rider_name")
	protected String riderName;
	@XmlElement(name = "rider_contact", required = true)
	@JsonProperty("rider_contact")
	protected String riderContact;

	
	@Override
	public String toString() {
		return "SFXCallbackObject [sfxOrderId=" + sfxOrderId + ", orderStatus=" + orderStatus + ", allotTime="
				+ allotTime + ", dispatchTime=" + dispatchTime + ", deliveryTime=" + deliveryTime + ", cancelReason="
				+ cancelReason + ", riderName=" + riderName + ", riderContact=" + riderContact + "]";
	}
	
    /**
     * Gets the value of the sfxOrderId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSfxOrderId() {
        return sfxOrderId;
    }

    /**
     * Sets the value of the sfxOrderId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSfxOrderId(String value) {
        this.sfxOrderId = value;
    }

    /**
     * Gets the value of the orderStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderStatus() {
        return orderStatus;
    }

    /**
     * Sets the value of the orderStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderStatus(String value) {
        this.orderStatus = value;
    }

    /**
     * Gets the value of the allotTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAllotTime() {
        return allotTime;
    }

    /**
     * Sets the value of the allotTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAllotTime(String value) {
        this.allotTime = value;
    }

    /**
     * Gets the value of the dispatchTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDispatchTime() {
        return dispatchTime;
    }

    /**
     * Sets the value of the dispatchTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDispatchTime(String value) {
        this.dispatchTime = value;
    }

    /**
     * Gets the value of the deliveryTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeliveryTime() {
        return deliveryTime;
    }

    /**
     * Sets the value of the deliveryTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliveryTime(String value) {
        this.deliveryTime = value;
    }

    /**
     * Gets the value of the cancelReason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCancelReason() {
        return cancelReason;
    }

    /**
     * Sets the value of the cancelReason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCancelReason(String value) {
        this.cancelReason = value;
    }

    /**
     * Gets the value of the riderName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRiderName() {
        return riderName;
    }

    /**
     * Sets the value of the riderName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRiderName(String value) {
        this.riderName = value;
    }

    /**
     * Gets the value of the riderContact property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRiderContact() {
        return riderContact;
    }

    /**
     * Sets the value of the riderContact property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRiderContact(String value) {
        this.riderContact = value;
    }

}
