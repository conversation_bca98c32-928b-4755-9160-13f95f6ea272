/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.28 at 05:05:25 PM IST 
//


package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="event_type" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="event_id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="task_id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="event_timestamp" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="eta" type="{http://www.w3schools.com}DUNEtaResponse"/&gt;
 *         &lt;element name="price" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="total_time" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="cancelled_by" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="cancellation_reason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="runner" type="{http://www.w3schools.com}DUNRunner"/&gt;
 *         &lt;element name="request_timestamp" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
	"event_type",
	"event_id",
    "task_id",
    "state",
    "event_timestamp",
    "eta",
    "price",
    "total_time",
    "cancelled_by",
    "cancellation_reason",
    "runner",
    "request_timestamp"
})
@XmlRootElement(name = "DUNCallbackObject")
@JsonIgnoreProperties(ignoreUnknown=true)
public class DUNCallbackObject implements CallbackObject{

	@XmlElement(name = "event_type", required = true)
	@JsonProperty("event_type")
	protected String event_type;
	@XmlElement(name = "event_id", required = true)
	@JsonProperty("event_id")
	protected String event_id;
	@XmlElement(name = "task_id", required = true)
	@JsonProperty("task_id")
	protected String task_id;
	@XmlElement(name = "state", required = true)
	@JsonProperty("state")
	protected String state;
	@XmlElement(name = "cancelled_by")
	@JsonProperty("cancelled_by")
	protected String cancelled_by;
	@XmlElement(name = "cancellation_reason")
	@JsonProperty("cancellation_reason")
	protected String cancellation_reason;
	@XmlElement(name = "event_timestamp", required = true)
	@JsonProperty("event_timestamp")
	protected long event_timestamp;
	@XmlElement(name = "eta")
	@JsonProperty("eta")
	protected DUNEtaResponse eta;
	@XmlElement(name = "runner")
	@JsonProperty("runner")
	protected DUNRunner runner;
	@XmlElement(name = "price")
	@JsonProperty("price")
	protected String price;
	@XmlElement(name = "total_time")
	@JsonProperty("total_time")
	protected String total_time;
	@XmlElement(name = "request_timestamp", required = true)
	@JsonProperty("request_timestamp")
	protected long request_timestamp;
	
	public String getTask_id() {
		return task_id;
	}
	public void setTask_id(String task_id) {
		this.task_id = task_id;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getCancelled_by() {
		return cancelled_by;
	}
	public void setCancelled_by(String cancelled_by) {
		this.cancelled_by = cancelled_by;
	}
	public String getCancellation_reason() {
		return cancellation_reason;
	}
	public void setCancellation_reason(String cancellation_reason) {
		this.cancellation_reason = cancellation_reason;
	}
	public long getEvent_timestamp() {
		return event_timestamp;
	}
	public void setEvent_timestamp(long event_timestamp) {
		this.event_timestamp = event_timestamp;
	}
	public DUNEtaResponse getEta() {
		return eta;
	}
	public void setEta(DUNEtaResponse eta) {
		this.eta = eta;
	}
	public DUNRunner getRunner() {
		return runner;
	}
	public void setRunner(DUNRunner runner) {
		this.runner = runner;
	}
	public String getPrice() {
		return price;
	}
	public void setPrice(String price) {
		this.price = price;
	}
	public String getTotal_time() {
		return total_time;
	}
	public void setTotal_time(String total_time) {
		this.total_time = total_time;
	}
	public long getRequest_timestamp() {
		return request_timestamp;
	}
	public void setRequest_timestamp(long request_timestamp) {
		this.request_timestamp = request_timestamp;
	}

}
