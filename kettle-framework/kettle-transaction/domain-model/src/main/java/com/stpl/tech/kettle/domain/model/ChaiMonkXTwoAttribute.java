/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown=true)
public class ChaiMonkXTwoAttribute {

    @JsonProperty("DEVICE")
    private Integer DEVICE;

    @JsonProperty("MAX_STEPS")
    private Integer MAX_STEPS;

    @JsonProperty("STEPS")
    private List<ChaiMonkXTwoRecipeStep> STEPS;
}
