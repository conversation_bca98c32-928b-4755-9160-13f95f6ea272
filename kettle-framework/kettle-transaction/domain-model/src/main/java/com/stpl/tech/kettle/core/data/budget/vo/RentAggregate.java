/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class RentAggregate {

	private boolean onRevenueShare;
	private BigDecimal propertyFixRent;
	private BigDecimal revenueShare;
	private BigDecimal revenueShareDineIn;
	private BigDecimal revenueShareDelivery;

	public boolean isOnRevenueShare() {
		return onRevenueShare;
	}

	public void setOnRevenueShare(boolean onRevenueShare) {
		this.onRevenueShare = onRevenueShare;
	}

	public BigDecimal getPropertyFixRent() {
		return propertyFixRent;
	}

	public void setPropertyFixRent(BigDecimal propertyFixRent) {
		this.propertyFixRent = propertyFixRent;
	}

	public BigDecimal getRevenueShare() {
		return revenueShare;
	}

	public void setRevenueShare(BigDecimal revenueShare) {
		this.revenueShare = revenueShare;
	}

	public BigDecimal getRevenueShareDineIn() {
		return revenueShareDineIn;
	}

	public void setRevenueShareDineIn(BigDecimal revenueShareDineIn) {
		this.revenueShareDineIn = revenueShareDineIn;
	}

	public BigDecimal getRevenueShareDelivery() {
		return revenueShareDelivery;
	}

	public void setRevenueShareDelivery(BigDecimal revenueShareDelivery) {
		this.revenueShareDelivery = revenueShareDelivery;
	}



}
