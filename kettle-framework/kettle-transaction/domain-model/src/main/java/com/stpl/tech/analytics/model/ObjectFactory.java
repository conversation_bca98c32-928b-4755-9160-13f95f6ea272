//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.10 at 07:11:28 PM IST 
//

package com.stpl.tech.analytics.model;

import javax.xml.bind.annotation.XmlRegistry;

/**
 * This object contains factory methods for each Java content interface and Java
 * element interface generated in the com.stpl.tech.analytics.model package.
 * <p>
 * An ObjectFactory allows you to programatically construct new instances of the
 * Java representation for XML content. The Java representation of XML content
 * can consist of schema derived interfaces and classes representing the binding
 * of schema type definitions, element declarations and model groups. Factory
 * methods for each of these are provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

	/**
	 * Create a new ObjectFactory that can be used to create new instances of
	 * schema derived classes for package: com.stpl.tech.analytics.model
	 * 
	 */
	public ObjectFactory() {
	}

	/**
	 * Create an instance of {@link CategoryReportData }
	 * 
	 */
	public CategoryReportData createCategoryReportData() {
		return new CategoryReportData();
	}

	/**
	 * Create an instance of {@link AggregateSaleData }
	 * 
	 */
	public AggregateSaleData createAggregateSaleData() {
		return new AggregateSaleData();
	}

	/**
	 * Create an instance of {@link UnitReportData }
	 * 
	 */
	public UnitReportData createUnitReportData() {
		return new UnitReportData();
	}

	/**
	 * Create an instance of {@link ProductCategoryReportData }
	 * 
	 */
	public ProductCategoryReportData createProductCategoryReportData() {
		return new ProductCategoryReportData();
	}

	/**
	 * Create an instance of {@link AggregateQuantityData }
	 * 
	 */
	public AggregateQuantityData createAggregateQuantityData() {
		return new AggregateQuantityData();
	}

	/**
	 * Create an instance of {@link ProductSubCategoryReportData }
	 * 
	 */
	public ProductSubCategoryReportData createProductSubCategoryReportData() {
		return new ProductSubCategoryReportData();
	}

	/**
	 * Create an instance of {@link SubCategoryReportData }
	 * 
	 */
	public SubCategoryReportData createSubCategoryReportData() {
		return new SubCategoryReportData();
	}

	/**
	 * Create an instance of {@link ProductReportData }
	 * 
	 */
	public ProductReportData createProductReportData() {
		return new ProductReportData();
	}

	/**
	 * Create an instance of {@link NumericData }
	 * 
	 */
	public NumericData createNumericData() {
		return new NumericData();
	}

	/**
	 * Create an instance of {@link DecimalData }
	 * 
	 */
	public DecimalData createDecimalData() {
		return new DecimalData();
	}

}
