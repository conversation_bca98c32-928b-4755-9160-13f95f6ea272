package com.stpl.tech.kettle.offer.data.model;

import java.math.BigDecimal;
import java.util.Date;

public class CustomerOfferData {

	private String contactNumber;
	private String customerName;
	private int customerId;
	private int unitId;
	private String unitName;
	private BigDecimal amount;
	private String region;
	private String couponCode;
	private String message;
	private String validUntil;
	private String url;
	private int couponDetailId;
	private int offerDetailId;
	private Date numberVerificationTime;

	public CustomerOfferData(String contactNumber, String customerName, int customerId, int unitId, String unitName,
			BigDecimal amount, String region,Date numberVerificationTime) {
		super();
		this.contactNumber = contactNumber;
		this.customerName = customerName;
		this.customerId = customerId;
		this.unitId = unitId;
		this.unitName = unitName;
		this.amount = amount;
		this.region = region;
		this.numberVerificationTime = numberVerificationTime;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public int getCustomerId() {
		return customerId;
	}

	public void setCustomerId(int customerId) {
		this.customerId = customerId;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((contactNumber == null) ? 0 : contactNumber.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CustomerOfferData other = (CustomerOfferData) obj;
		if (contactNumber == null) {
			if (other.contactNumber != null)
				return false;
		} else if (!contactNumber.equals(other.contactNumber))
			return false;
		return true;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public int getCouponDetailId() {
		return couponDetailId;
	}

	public void setCouponDetailId(int couponDetailId) {
		this.couponDetailId = couponDetailId;
	}

	public int getOfferDetailId() {
		return offerDetailId;
	}

	public void setOfferDetailId(int offerDetailId) {
		this.offerDetailId = offerDetailId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Date getNumberVerificationTime() {
		return numberVerificationTime;
	}

	public void setNumberVerificationTime(Date numberVerificationTime) {
		this.numberVerificationTime = numberVerificationTime;
	}


	public String getValidUntil() {
		return validUntil;
	}

	public void setValidUntil(String validUntil) {
		this.validUntil = validUntil;
	}

	@Override
	public String toString() {
		return "CustomerOfferData [contactNumber=" + contactNumber + ", customerName=" + customerName + ", customerId="
				+ customerId + ", unitId=" + unitId + ", unitName=" + unitName + ", amount=" + amount + ", region="
				+ region + ", couponCode=" + couponCode + ", message=" + message + ", url=" + url + ", couponDetailId="
				+ couponDetailId + ", offerDetailId=" + offerDetailId + "]";
	}


}
