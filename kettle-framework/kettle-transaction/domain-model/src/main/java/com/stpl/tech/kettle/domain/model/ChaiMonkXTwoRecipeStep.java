/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown=true)
public class ChaiMonkXTwoRecipeStep {

    @JsonProperty("STEP_NO")
    private Integer STEP_NO;

    @JsonProperty("CTRL_MODE")
    private Integer CTRL_MODE;

    @JsonProperty("VESSEL_TYPE_DETECTION")
    private Integer VESSEL_TYPE_DETECTION;

    @JsonProperty("VESSEL_TYPE")
    private Integer VESSEL_TYPE;

    @JsonProperty("VESSEL_SENSE_DELAY")
    private Integer VESSEL_SENSE_DELAY;

    @JsonProperty("DISPENSE_TYPE")
    private Integer DISPENSE_TYPE;

    @JsonProperty("LIQUID_INDEX")
    private Integer LIQUID_INDEX;

    @JsonProperty("LIQUID_QTY")
    private Integer LIQUID_QTY;

    @JsonProperty("BOIL_COUNT")
    private Integer BOIL_COUNT;

    @JsonProperty("SETTLING_TIME")
    private Integer SETTLING_TIME;

    @JsonProperty("vesselOffPosition")
    private Date vesselOffPosition;

    @JsonProperty("SPICE_SENSE_DELAY")
    private Integer SPICE_SENSE_DELAY;

    private Date startTime;
    private Date completedTime;
    private Date failedTime;
}
