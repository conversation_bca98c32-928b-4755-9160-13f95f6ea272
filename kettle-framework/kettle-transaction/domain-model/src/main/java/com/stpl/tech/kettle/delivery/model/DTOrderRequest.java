/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.09.08 at 01:35:16 PM IST 
//


package com.stpl.tech.kettle.delivery.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="paymentmode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="address" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customernumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="lat" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="lon" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ordername" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="area" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pincode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="restaurantid" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="restname" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="restaurantcontact" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="restAddress" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "paymentmode",
    "address",
    "amount",
    "customernumber",
    "lat",
    "lon",
    "name",
    "ordername",
    "status",
    "area",
    "pincode",
    "city",
    "restaurantid",
    "restname",
    "restaurantcontact",
    "restAddress"
})
@XmlRootElement(name = "DTOrderRequest")
@JsonIgnoreProperties
public class DTOrderRequest implements DTRequest {

    @XmlElement(required = true)
    protected String paymentmode;
    @XmlElement(required = true)
    protected String address;
    @XmlElement(required = true)
    protected String amount;
    @XmlElement(required = true)
    protected String customernumber;
    @XmlElement(required = true)
    protected String lat;
    @XmlElement(required = true)
    protected String lon;
    @XmlElement(required = true)
    protected String name;
    @XmlElement(required = true)
    protected String ordername;
    @XmlElement(required = true)
    protected String status;
    @XmlElement(required = true)
    protected String area;
    @XmlElement(required = true)
    protected String pincode;
    @XmlElement(required = true)
    protected String city;
    @XmlElement(required = true)
    protected String restaurandid;
    @XmlElement(required = true)
    protected String restname;
    @XmlElement(required = true)
    protected String restaurantcontact;
    @XmlElement(required = true)
    protected String restAddress;

    /**
     * Gets the value of the paymentmode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPaymentmode() {
        return paymentmode;
    }

    /**
     * Sets the value of the paymentmode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPaymentmode(String value) {
        this.paymentmode = value;
    }

    /**
     * Gets the value of the address property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddress() {
        return address;
    }

    /**
     * Sets the value of the address property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddress(String value) {
        this.address = value;
    }

    /**
     * Gets the value of the amount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAmount() {
        return amount;
    }

    /**
     * Sets the value of the amount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAmount(String value) {
        this.amount = value;
    }

    /**
     * Gets the value of the customernumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomernumber() {
        return customernumber;
    }

    /**
     * Sets the value of the customernumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomernumber(String value) {
        this.customernumber = value;
    }

    /**
     * Gets the value of the lat property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLat() {
        return lat;
    }

    /**
     * Sets the value of the lat property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLat(String value) {
        this.lat = value;
    }

    /**
     * Gets the value of the lon property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLon() {
        return lon;
    }

    /**
     * Sets the value of the lon property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLon(String value) {
        this.lon = value;
    }

    /**
     * Gets the value of the name property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the ordername property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrdername() {
        return ordername;
    }

    /**
     * Sets the value of the ordername property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrdername(String value) {
        this.ordername = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the area property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getArea() {
        return area;
    }

    /**
     * Sets the value of the area property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setArea(String value) {
        this.area = value;
    }

    /**
     * Gets the value of the pincode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPincode() {
        return pincode;
    }

    /**
     * Sets the value of the pincode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPincode(String value) {
        this.pincode = value;
    }

    /**
     * Gets the value of the city property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCity() {
        return city;
    }

    /**
     * Sets the value of the city property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCity(String value) {
        this.city = value;
    }

    /**
     * Gets the value of the restaurantid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRestaurandid() {
        return restaurandid;
    }

    /**
     * Sets the value of the restaurantid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRestaurandid(String value) {
        this.restaurandid = value;
    }

    /**
     * Gets the value of the restname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRestname() {
        return restname;
    }

    /**
     * Sets the value of the restname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRestname(String value) {
        this.restname = value;
    }

    /**
     * Gets the value of the restaurantcontact property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRestaurantcontact() {
        return restaurantcontact;
    }

    /**
     * Sets the value of the restaurantcontact property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRestaurantcontact(String value) {
        this.restaurantcontact = value;
    }

    /**
     * Gets the value of the restAddress property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRestAddress() {
        return restAddress;
    }

    /**
     * Sets the value of the restAddress property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRestAddress(String value) {
        this.restAddress = value;
    }

    @Override
    public void assignRestaurantId(String restaurantId) {
        this.restaurandid = restaurantId;
    }

}
