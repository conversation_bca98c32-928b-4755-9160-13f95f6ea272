package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.Date;

public class SubscriptionViewData {

    private Integer customerId;
    private String customerName;
    private String customerNumber;
    private Integer subscriptionId;
    private Date planStartDate;
    private Date planEndDate;
    private String subscriptionPlanCode;
    private String subscriptionName;
    private String offerDescription;
    private Integer validityDays;
    private Integer nThDay;
    private Integer totalSaving ;
    private BigDecimal price ;
    private String buyLink;

    public SubscriptionViewData() {
    }

    public SubscriptionViewData(Integer customerId, Integer subscriptionId, Date planStartDate, String subscriptionPlanCode) {
        this.customerId = customerId;
        this.subscriptionId = subscriptionId;
        this.planStartDate = planStartDate;
        this.subscriptionPlanCode = subscriptionPlanCode;
    }

    public SubscriptionViewData(Integer customerId, Integer subscriptionId, Date planStartDate, Date planEndDate, String subscriptionPlanCode) {
        this.customerId = customerId;
        this.subscriptionId = subscriptionId;
        this.planStartDate = planStartDate;
        this.planEndDate = planEndDate;
        this.subscriptionPlanCode = subscriptionPlanCode;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getCustomerNumber() {
        return customerNumber;
    }

    public void setCustomerNumber(String customerNumber) {
        this.customerNumber = customerNumber;
    }

    public Integer getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(Integer subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public Date getPlanStartDate() {
        return planStartDate;
    }

    public void setPlanStartDate(Date planStartDate) {
        this.planStartDate = planStartDate;
    }

    public String getSubscriptionPlanCode() {
        return subscriptionPlanCode;
    }

    public void setSubscriptionPlanCode(String subscriptionPlanCode) {
        this.subscriptionPlanCode = subscriptionPlanCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Date getPlanEndDate() {
        return planEndDate;
    }

    public void setPlanEndDate(Date planEndDate) {
        this.planEndDate = planEndDate;
    }

    public String getOfferDescription() {
        return offerDescription;
    }

    public void setOfferDescription(String offerDescription) {
        this.offerDescription = offerDescription;
    }

    public Integer getValidityDays() {
        return validityDays;
    }

    public void setValidityDays(Integer validityDays) {
        this.validityDays = validityDays;
    }

    public Integer getnThDay() {
        return nThDay;
    }

    public void setnThDay(Integer nThDay) {
        this.nThDay = nThDay;
    }

    public String getSubscriptionName() {
        return subscriptionName;
    }

    public void setSubscriptionName(String subscriptionName) {
        this.subscriptionName = subscriptionName;
    }

    public Integer getTotalSaving() {
        return totalSaving;
    }

    public void setTotalSaving(Integer totalSaving) {
        this.totalSaving = totalSaving;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getBuyLink() {
        return buyLink;
    }

    public void setBuyLink(String buyLink) {
        this.buyLink = buyLink;
    }
}
