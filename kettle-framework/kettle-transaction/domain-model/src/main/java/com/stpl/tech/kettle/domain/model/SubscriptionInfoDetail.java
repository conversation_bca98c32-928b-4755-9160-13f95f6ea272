package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class SubscriptionInfoDetail implements Serializable {
    private static final long serialVersionUID = -6314976048575109390L;
    private Integer customerId;
    private String subscriptionCode;
    private boolean hasSubscription;
    private Date startDate;
    private Date endDate;
    private Integer daysLeft;
    private BigDecimal overAllSaving;
    private int remainingChai;
    private boolean eligible;

    public SubscriptionInfoDetail() {
    }

    public SubscriptionInfoDetail(Integer customerId, String subscriptionCode, boolean hasSubscription, Date startDate, Date endDate,BigDecimal overAllSaving,Integer daysLeft) {
        this.customerId = customerId;
        this.subscriptionCode = subscriptionCode;
        this.hasSubscription = hasSubscription;
        this.startDate = startDate;
        this.endDate = endDate;
        this.overAllSaving = overAllSaving;
        this.daysLeft = daysLeft;
    }

    public SubscriptionInfoDetail(Integer customerId, String subscriptionCode, boolean hasSubscription, Date startDate, Date endDate,BigDecimal overAllSaving,Integer daysLeft, int remainingChai) {
        this.customerId = customerId;
        this.subscriptionCode = subscriptionCode;
        this.hasSubscription = hasSubscription;
        this.startDate = startDate;
        this.endDate = endDate;
        this.overAllSaving = overAllSaving;
        this.daysLeft = daysLeft;
        this.remainingChai= remainingChai;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getSubscriptionCode() {
        return subscriptionCode;
    }

    public void setSubscriptionCode(String subscriptionCode) {
        this.subscriptionCode = subscriptionCode;
    }

    public boolean isHasSubscription() {
        return hasSubscription;
    }

    public void setHasSubscription(boolean hasSubscription) {
        this.hasSubscription = hasSubscription;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getOverAllSaving() {
        return overAllSaving;
    }

    public void setOverAllSaving(BigDecimal overAllSaving) {
        this.overAllSaving = overAllSaving;
    }

    public Integer getDaysLeft() {
        return daysLeft;
    }

    public void setDaysLeft(Integer daysLeft) {
        this.daysLeft = daysLeft;
    }

    public boolean isEligible() {
        return eligible;
    }

    public void setEligible(boolean eligible) {
        this.eligible = eligible;
    }

    public int getRemainingChai() {
        return remainingChai;
    }

    public void setRemainingChai(int remainingChai) {
        this.remainingChai = remainingChai;
    }
}
