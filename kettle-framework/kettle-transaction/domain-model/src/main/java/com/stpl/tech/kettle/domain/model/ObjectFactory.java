/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.04.09 at 06:52:19 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.kettle.referral.model.ReferentInfo;
import com.stpl.tech.master.recipe.read.model.RecipeDetailVO;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.stpl.tech.kettle.domain.model package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.stpl.tech.kettle.domain.model
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link UnitClosure }
     * 
     */
    public UnitClosure createUnitClosure() {
        return new UnitClosure();
    }

    /**
     * Create an instance of {@link ClosureStatus }
     * 
     */
    public ClosureStatus createClosureStatus() {
        return new ClosureStatus();
    }

    /**
     * Create an instance of {@link ClosurePaymentDetail }
     * 
     */
    public ClosurePaymentDetail createClosurePaymentDetail() {
        return new ClosurePaymentDetail();
    }

    /**
     * Create an instance of {@link ReportDef }
     * 
     */
    public ReportDef createReportDef() {
        return new ReportDef();
    }

    /**
     * Create an instance of {@link ReportAttribute }
     * 
     */
    public ReportAttribute createReportAttribute() {
        return new ReportAttribute();
    }

    /**
     * Create an instance of {@link ReportExecution }
     * 
     */
    public ReportExecution createReportExecution() {
        return new ReportExecution();
    }

    /**
     * Create an instance of {@link ReportStatus }
     * 
     */
    public ReportStatus createReportStatus() {
        return new ReportStatus();
    }

    /**
     * Create an instance of {@link ReportStatusEvent }
     * 
     */
    public ReportStatusEvent createReportStatusEvent() {
        return new ReportStatusEvent();
    }

    /**
     * Create an instance of {@link Customer }
     * 
     */
    public Customer createCustomer() {
        return new Customer();
    }

    /**
     * Create an instance of {@link CustomerOffer }
     * 
     */
    public CustomerOffer createCustomerOffer() {
        return new CustomerOffer();
    }

    /**
     * Create an instance of {@link InventoryUpdateEvent }
     * 
     */
    public InventoryUpdateEvent createInventoryUpdateEvent() {
        return new InventoryUpdateEvent();
    }

    /**
     * Create an instance of {@link ProductInventory }
     * 
     */
    public ProductInventory createProductInventory() {
        return new ProductInventory();
    }

    /**
     * Create an instance of {@link InventoryEventData }
     * 
     */
    public InventoryEventData createInventoryEventData() {
        return new InventoryEventData();
    }

    /**
     * Create an instance of {@link TransitionStateData }
     * 
     */
    public TransitionStateData createTransitionStateData() {
        return new TransitionStateData();
    }

    /**
     * Create an instance of {@link StateData }
     * 
     */
    public StateData createStateData() {
        return new StateData();
    }

    /**
     * Create an instance of {@link TransitionState }
     * 
     */
    public TransitionState createTransitionState() {
        return new TransitionState();
    }

    /**
     * Create an instance of {@link TransitionData }
     * 
     */
    public TransitionData createTransitionData() {
        return new TransitionData();
    }

    /**
     * Create an instance of {@link TransitionEvent }
     * 
     */
    public TransitionEvent createTransitionEvent() {
        return new TransitionEvent();
    }


    /**
     * Create an instance of {@link Order }
     * 
     */
    public Order createOrder() {
        return new Order();
    }

    /**
     * Create an instance of {@link SubscriptionEvent }
     * 
     */
    public SubscriptionEvent createSubscriptionEvent() {
        return new SubscriptionEvent();
    }

    /**
     * Create an instance of {@link SubscriptionStatusEvents }
     * 
     */
    public SubscriptionStatusEvents createSubscriptionStatusEvents() {
        return new SubscriptionStatusEvents();
    }

    /**
     * Create an instance of {@link Subscription }
     * 
     */
    public Subscription createSubscription() {
        return new Subscription();
    }

    /**
     * Create an instance of {@link PullPacket }
     * 
     */
    public PullPacket createPullPacket() {
        return new PullPacket();
    }

    /**
     * Create an instance of {@link PullPacketDenomination }
     * 
     */
    public PullPacketDenomination createPullPacketDenomination() {
        return new PullPacketDenomination();
    }

    /**
     * Create an instance of {@link PullSettlementDenomination }
     * 
     */
    public PullSettlementDenomination createPullSettlementDenomination() {
        return new PullSettlementDenomination();
    }

    /**
     * Create an instance of {@link OrderPaymentDenomination }
     * 
     */
    public OrderPaymentDenomination createOrderPaymentDenomination() {
        return new OrderPaymentDenomination();
    }

    /**
     * Create an instance of {@link PullSettlementDetail }
     * 
     */
    public PullSettlementDetail createPullSettlementDetail() {
        return new PullSettlementDetail();
    }

    /**
     * Create an instance of {@link ActionDetail }
     * 
     */
    public ActionDetail createActionDetail() {
        return new ActionDetail();
    }

    /**
     * Create an instance of {@link Vendor }
     * 
     */
    public Vendor createVendor() {
        return new Vendor();
    }

    /**
     * Create an instance of {@link InventoryThresholdData }
     * 
     */
    public InventoryThresholdData createInventoryThresholdData() {
        return new InventoryThresholdData();
    }

    /**
     * Create an instance of {@link OrderItem }
     * 
     */
    public OrderItem createOrderItem() {
        return new OrderItem();
    }

    /**
     * Create an instance of {@link EnquiryItem }
     * 
     */
    public EnquiryItem createEnquiryItem() {
        return new EnquiryItem();
    }

    /**
     * Create an instance of {@link DiscountDetail }
     * 
     */
    public DiscountDetail createDiscountDetail() {
        return new DiscountDetail();
    }

    /**
     * Create an instance of {@link ComplimentaryDetail }
     * 
     */
    public ComplimentaryDetail createComplimentaryDetail() {
        return new ComplimentaryDetail();
    }

    /**
     * Create an instance of {@link OrderMetadata }
     * 
     */
    public OrderMetadata createOrderMetadata() {
        return new OrderMetadata();
    }

    /**
     * Create an instance of {@link TransactionDetail }
     * 
     */
    public TransactionDetail createTransactionDetail() {
        return new TransactionDetail();
    }

    /**
     * Create an instance of {@link PercentageDetail }
     * 
     */
    public PercentageDetail createPercentageDetail() {
        return new PercentageDetail();
    }

    /**
     * Create an instance of {@link Settlement }
     * 
     */
    public Settlement createSettlement() {
        return new Settlement();
    }

    /**
     * Create an instance of {@link WorkstationLog }
     * 
     */
    public WorkstationLog createWorkstationLog() {
        return new WorkstationLog();
    }

    /**
     * Create an instance of {@link AssemblyLog }
     * 
     */
    public AssemblyLog createAssemblyLog() {
        return new AssemblyLog();
    }

    /**
     * Create an instance of {@link UnitExpense }
     * 
     */
    public UnitExpense createUnitExpense() {
        return new UnitExpense();
    }

    /**
     * Create an instance of {@link ExpenseUpdateEvent }
     * 
     */
    public ExpenseUpdateEvent createExpenseUpdateEvent() {
        return new ExpenseUpdateEvent();
    }

    /**
     * Create an instance of {@link UnitExpenseDrilldown }
     * 
     */
    public UnitExpenseDrilldown createUnitExpenseDrilldown() {
        return new UnitExpenseDrilldown();
    }

    public CustomerInfoDineIn createCustomerInfoDineIn() {
        return new CustomerInfoDineIn();
    }

    public CustomerLoyaltyEntry createCustomerLoyaltyEntry() {
        return new CustomerLoyaltyEntry();
    }

    public CustomerCashPacketLog createCustomerCashPacketLog() {
        return new CustomerCashPacketLog();
    }

    public ReferentInfo createReferentInfo() {
        return new ReferentInfo();
    }

    public CustomerFavChaiMappingVO createCustomerFavChaiMappingVO(){
        return new CustomerFavChaiMappingVO();
    }

    public FavChaiCustomizationDetail createFavChaiCustomizationDetail(){
        return new FavChaiCustomizationDetail();
    }

    public FavouriteChai createFavChaiDineIn(){
        return new FavouriteChai();
    }
    public CartOrderItem createCartOrderItem(){
        return new CartOrderItem();
    }
    public FavChaiProductDetails createFavChaiProductDetails() {
        return new FavChaiProductDetails();
    }

    public Addon createAddon() {
        return new Addon();
    }

    public Options createOptionsData() {
        return new Options();
    }

    public SaveCustomerFavChaiRequest createSaveCustomerFavChaiRequest() {
        return new SaveCustomerFavChaiRequest();
    }

    public SelectedOrderItem createdSelectedOrderItem() {
        return new SelectedOrderItem();
    }

    public OrderItemComposition createOrderItemComposition() {
        return new OrderItemComposition();
    }

    public RecipeDetailVO createRecipeDetails() {
        return new RecipeDetailVO();
    }
}
