/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.model;

import java.math.BigDecimal;

import com.stpl.tech.master.domain.model.IdCodeName;

public class CreditBillData {

	private int orderId;
	private String generatedOrderId;
	private BigDecimal totalAmount = new BigDecimal(0);
	private BigDecimal creditAmount = new BigDecimal(0);
	private int employeeId;
	private IdCodeName channelPartner;
	private String orderRemark;

	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	public String getGeneratedOrderId() {
		return generatedOrderId;
	}

	public void setGeneratedOrderId(String generatedOrderId) {
		this.generatedOrderId = generatedOrderId;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public BigDecimal getCreditAmount() {
		return creditAmount;
	}

	public void setCreditAmount(BigDecimal creditAmount) {
		this.creditAmount = creditAmount;
	}

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	public IdCodeName getChannelPartner() {
		return channelPartner;
	}

	public void setChannelPartner(IdCodeName channelPartner) {
		this.channelPartner = channelPartner;
	}

	public String getOrderRemark() {
		return orderRemark;
	}

	public void setOrderRemark(String orderRemark) {
		this.orderRemark = orderRemark;
	}

}
