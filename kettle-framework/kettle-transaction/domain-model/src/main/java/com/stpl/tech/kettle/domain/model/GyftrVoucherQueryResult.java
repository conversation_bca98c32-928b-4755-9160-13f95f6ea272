package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "DurationEndDate", "DurationStartDate", "ErrorCode", "ErrorMsg", "LastConsumedDate",
		"LastConsumedShopcode", "Message", "ProductCode", "ProductName", "ResultType", "Status", "Value",
		"VoucherNumber", "VoucherType" })
public class GyftrVoucherQueryResult implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -9162335506792490125L;

	@JsonProperty("DurationEndDate")
	private String endDate;
	@JsonProperty("DurationStartDate")
	private String startDate;
	@JsonProperty("ErrorCode")
	private String errorCode;
	@JsonProperty("ErrorMsg")
	private String erroMsg;
	@JsonProperty("LastConsumedDate")
	private String lastConsumedDate;
	@JsonProperty("LastConsumedShopCode")
	private String lastConsumedShopCode;
	@JsonProperty("Message")
	private String message;
	@JsonProperty("ProductCode")
	private String productCode;
	@JsonProperty("ProductName")
	private String productName;
	@JsonProperty("ResultType")
	private String resultType = null;
	@JsonProperty("Status")
	private String status;
	@JsonProperty("Value")
	private String value;
	@JsonProperty("VoucherNumber")
	private String voucherNumber;
	@JsonProperty("VoucherType")
	private String voucherType;

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErroMsg() {
		return erroMsg;
	}

	public void setErroMsg(String erroMsg) {
		this.erroMsg = erroMsg;
	}

	public String getLastConsumedDate() {
		return lastConsumedDate;
	}

	public void setLastConsumedDate(String lastConsumedDate) {
		this.lastConsumedDate = lastConsumedDate;
	}

	public String getLastConsumedShopCode() {
		return lastConsumedShopCode;
	}

	public void setLastConsumedShopCode(String lastConsumedShopCode) {
		this.lastConsumedShopCode = lastConsumedShopCode;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	@JsonProperty("ResultType")
	public String getResultType() {
		return resultType;
	}

	@JsonProperty("ResultType")
	public void setResultType(String resultType) {
		this.resultType = resultType;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getVoucherNumber() {
		return voucherNumber;
	}

	public void setVoucherNumber(String voucherNumber) {
		this.voucherNumber = voucherNumber;
	}

	public String getVoucherType() {
		return voucherType;
	}

	public void setVoucherType(String voucherType) {
		this.voucherType = voucherType;
	}

	@Override
	public String toString() {
		return "GyftrVoucherQueryResult [endDate=" + endDate + ", startDate=" + startDate + ", errorCode=" + errorCode
				+ ", erroMsg=" + erroMsg + ", lastConsumedDate=" + lastConsumedDate + ", lastConsumedShopCode="
				+ lastConsumedShopCode + ", message=" + message + ", productCode=" + productCode + ", productName="
				+ productName + ", resultType=" + resultType + ", status=" + status + ", value=" + value
				+ ", voucherNumber=" + voucherNumber + ", voucherType=" + voucherType + "]";
	}

}
