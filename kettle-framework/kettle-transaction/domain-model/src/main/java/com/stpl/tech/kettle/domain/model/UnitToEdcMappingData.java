package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnitToEdcMappingData implements Serializable {


    private static final long serialVersionUID = -1295216438501530023L;

    private Integer id;
    private Integer unitId;
    private String partnerName;
    private String status;
    private String merchantId;
    private String tId;
    private String secretKey;
    private String terminalId;
    private  String merchantKey;
    private  String version;
}
