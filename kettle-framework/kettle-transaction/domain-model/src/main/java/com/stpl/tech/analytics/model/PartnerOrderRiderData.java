package com.stpl.tech.analytics.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
		"partner_id",
        "order_id",
        "rider_phone",
        "rbt",
        "is_high_temp",
        "rbtCheck",
        "maskCheck",
        "is_rider_wearing_mask"
})
public class PartnerOrderRiderData {

	@JsonProperty("partner_id")
	private Integer partner_id;
	@JsonProperty("order_id")
	private String order_id;
	@JsonProperty("rider_phone")
	private String rider_phone;
	@JsonProperty("rbt")
	private float rbt;
	@JsonProperty("is_high_temp")
	private boolean is_high_temp;
	@JsonProperty("is_rider_wearing_mask")
	private String is_rider_wearing_mask;
	@JsonProperty("rbtCheck")
	private boolean rbtCheck;
	@JsonProperty("maskCheck")
	private boolean maskCheck;

	public Integer getPartner_id() {
		return partner_id;
	}

	public void setPartner_id(Integer partner_id) {
		this.partner_id = partner_id;
	}

	public String getOrder_id() {
		return order_id;
	}

	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}

	public String getRider_phone() {
		return rider_phone;
	}

	public void setRider_phone(String rider_phone) {
		this.rider_phone = rider_phone;
	}

	public float getRbt() {
		return rbt;
	}

	public void setRbt(float rbt) {
		this.rbt = rbt;
	}

	public boolean isIs_high_temp() {
		return is_high_temp;
	}

	public void setIs_high_temp(boolean is_high_temp) {
		this.is_high_temp = is_high_temp;
	}

	public String getIs_rider_wearing_mask() {
		return is_rider_wearing_mask;
	}

	public void setIs_rider_wearing_mask(String is_rider_wearing_mask) {
		this.is_rider_wearing_mask = is_rider_wearing_mask;
	}

	public boolean isRbtCheck() {
		return rbtCheck;
	}

	public void setRbtCheck(boolean rbtCheck) {
		this.rbtCheck = rbtCheck;
	}

	public boolean isMaskCheck() {
		return maskCheck;
	}

	public void setMaskCheck(boolean maskCheck) {
		this.maskCheck = maskCheck;
	}

}
