package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class OrderNPS implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2917360304179497758L;
	protected Integer orderId;
	protected String feedbackType;
	protected Integer customerId;
	protected Integer rating;
	protected String question;
	protected String response;
	protected String unitName;
	protected Integer unitId;
	protected Date feedbackTime;
	protected List<OrderNPSResponse> questions;

	public OrderNPS() {
	}

	public OrderNPS(Integer orderId, String feedbackType, Integer customerId, Integer rating, String question,
			String response, String unitName, Integer unitId, Date feedbackTime, List<OrderNPSResponse> questions) {
		this.orderId = orderId;
		this.feedbackType = feedbackType;
		this.customerId = customerId;
		this.rating = rating;
		this.question = question;
		this.response = response;
		this.unitName = unitName;
		this.unitId = unitId;
		this.feedbackTime = feedbackTime;
		this.questions = questions;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public String getFeedbackType() {
		return feedbackType;
	}

	public void setFeedbackType(String feedbackType) {
		this.feedbackType = feedbackType;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public Integer getRating() {
		return rating;
	}

	public void setRating(Integer rating) {
		this.rating = rating;
	}

	public String getQuestion() {
		return question;
	}

	public void setQuestion(String question) {
		this.question = question;
	}

	public String getResponse() {
		return response;
	}

	public void setResponse(String response) {
		this.response = response;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public Date getFeedbackTime() {
		return feedbackTime;
	}

	public void setFeedbackTime(Date feedbackTime) {
		this.feedbackTime = feedbackTime;
	}

	public List<OrderNPSResponse> getQuestions() {
		return questions;
	}

	public void setQuestions(List<OrderNPSResponse> questions) {
		this.questions = questions;
	}
}
