/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.03.22 at 01:07:13 PM IST
//

package com.stpl.tech.kettle.domain.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

/**
 * <p>
 * Java class for OrderMetadata complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="OrderMetadata"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="metadataId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="orderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="attributeName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="attributeValue" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OrderMetadata", propOrder = { "metadataId", "orderId", "attributeName", "attributeValue" })
@Document
public class OrderMetadata implements Serializable{
	/**
	 *
	 */
	private static final long serialVersionUID = -9176250779625403395L;

	@Id
	private String _id;
/*
	@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/
	@Field
	protected int metadataId;
	@Field
	protected int orderId;
	@XmlElement(required = true)
	@Field
	protected String attributeName;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String attributeValue;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

/*
	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}
*/

	/**
	 * Gets the value of the metadataId property.
	 *
	 */
	public int getMetadataId() {
		return metadataId;
	}

	/**
	 * Sets the value of the metadataId property.
	 *
	 */
	public void setMetadataId(int value) {
		this.metadataId = value;
	}

	/**
	 * Gets the value of the orderId property.
	 *
	 */
	public int getOrderId() {
		return orderId;
	}

	/**
	 * Sets the value of the orderId property.
	 *
	 */
	public void setOrderId(int value) {
		this.orderId = value;
	}

	/**
	 * Gets the value of the attributeName property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getAttributeName() {
		return attributeName;
	}

	/**
	 * Sets the value of the attributeName property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setAttributeName(String value) {
		this.attributeName = value;
	}

	/**
	 * Gets the value of the attributeValue property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getAttributeValue() {
		return attributeValue;
	}

	/**
	 * Sets the value of the attributeValue property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setAttributeValue(String value) {
		this.attributeValue = value;
	}

}
