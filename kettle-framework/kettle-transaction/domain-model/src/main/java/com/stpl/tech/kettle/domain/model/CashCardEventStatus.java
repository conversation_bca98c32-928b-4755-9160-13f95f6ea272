//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.05.17 at 12:08:33 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CashCardEventStatus.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="CashCardEventStatus"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="CARD_VALIDATION_FAILED"/&gt;
 *     &lt;enumeration value="CARD_ACTIVATION_FAILED"/&gt;
 *     &lt;enumeration value="CARD_ACTIVATION_SUCCESS"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "CashCardEventStatus")
@XmlEnum
public enum CashCardEventStatus {

    @XmlEnumValue("CARD_VALIDATION_FAILED")
    CARD___VALIDATION___FAILED("CARD_VALIDATION_FAILED"),
    @XmlEnumValue("CARD_ACTIVATION_FAILED")
    CARD___ACTIVATION___FAILED("CARD_ACTIVATION_FAILED"),
    @XmlEnumValue("CARD_ACTIVATION_SUCCESS")
    CARD___ACTIVATION___SUCCESS("CARD_ACTIVATION_SUCCESS");
    private final String value;

    CashCardEventStatus(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static CashCardEventStatus fromValue(String v) {
        for (CashCardEventStatus c: CashCardEventStatus.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
