/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are @Field protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.03.22 at 01:07:13 PM IST
//

package com.stpl.tech.kettle.sales.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.domain.model.EnquiryItem;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.master.CustomJsonDateDeserializer;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.Adapter5;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Document(collection = "SalesRecord")
public class SalesRecord implements Serializable {


	/**
	 *
	 */
	private static final long serialVersionUID = -5795449675707457693L;

	@Id
	private String _id;

	//Load
	@Field
	protected Integer orderId;
	//Load
	@Field
	protected String generateOrderId;
	//Load
	@Field
	protected String externalOrderId;
	//Load
	@Field
	protected Integer unitOrderId;

	//Load
	@Field
	protected Integer unitId;
	//Load
	@Field
	protected String unitName;
	//Load
	@Field
	protected Integer areaManagerId;
	//Load
	@Field
	protected String areaManagerName;
	//Load
	@Field
	protected String unitRegion;
	//Load
	@Field
	protected String unitCategory;
	//Load
	@Field
	protected String unitSubCategory;
	//Load
	@Field
	protected String city;
	//Load
	@Field
	protected String state;
	//Load
	@Field
	protected Integer terminalId;
	//Load
	@Field
	protected Integer tableNumber;

	//Load
	@Field
	protected Integer customerId;
	//Load
	@Field
	protected String customerName;
	//Load
	@Field
	protected String contactNumber;
	//Load
	@Field
	protected Boolean numberVerified;
	//Load
	@Field
	protected String emailId;
	//Load
	@Field
	protected Boolean emailVerified;
	//Load
	@Field
	protected Boolean newCustomer;

	//Calculate
	@Field
	protected Integer startingLoyaltyPoints;
	//Calculate
	@Field
	protected Integer acquiredLoyaltyPoints;
	//Calculate
	@Field
	protected Integer redeemedLoyaltyPoints;
	//Calculate
	@Field
	protected Integer finalLoyaltyPoints;
	//Calculate
	@Field
	protected Boolean availableSignupOffer;
	//Calculate
	@Field
	protected Boolean availedSignupOffer;
	//Calculate
	@Field
	protected Boolean availableRedemption;
	//Calculate
	@Field
	protected Boolean availedRedemption;
	//Calculate
	@Field
	protected Integer previousOrderId;
	//Calculate
	@Field
	protected Integer previousOrderCount;
	//Calculate
	@Field
	protected Integer previousAPC;

	//Calculate
	@Field
	protected Integer totalGCPurchases;
	//Calculate
	@Field
	protected String lastOrderSource;
	//Calculate
	@Field
	protected Boolean customerOrderedOnline;
	@Field
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	protected Date lastOrderDate;
	@Field
	protected Integer daysGapSinceLastOrder;
	@Field
	protected String lastDeliveryLocation;
	@Field
	protected Integer lastDeliveryUnitId;
	@Field
	protected Integer lastDineInUnitId;
	@Field
	protected String apcTrend;
	/*
	 * @Field protected Boolean triedNewProduct; @Field protected Integer
	 * recommendedProduct;
	 *
	 * @Field protected Boolean requestedFeedBack; @Field protected Boolean
	 * requestedNPS;
	 *
	 * @Field protected Boolean requestedSignupVerification; @Field protected
	 * Boolean requestedEmailVerificaton; @Field protected Integer
	 * providedFeedBack; @Field protected Integer providedNPS; @Field protected
	 * Boolean verifiedSignup; @Field protected Boolean verifiedEmail;
	 */

	@Field
	protected Boolean partnerOrder;
	@Field
	protected Integer channelPartnerId;
	@Field
	protected String channelPartnerName;
	@Field
	protected Boolean takeAway;
	@Field
	protected int employeeId;
	@Field
	protected String employeeName;

	@Field
	protected String orderSource;

	@Field
	protected String partnerOrderId;
	@Field
	protected String orderStatus;

	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal saleAmount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal totalAmount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal taxableAmount;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	@Field
	protected Integer discountCode;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String discountReason;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal discountPercentage;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal discountValue;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal promotionalOffer;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal totalDiscount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal paidAmount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal roundOffValue;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal savings;

	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal tax;
	@Field
	protected String offerCode;

	@Field
	protected int billCreationSeconds;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@Field
	protected Date billingServerTime;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@Field
	protected Date businessDate;

	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@Field
	protected Date saleDate;

	@Field

	protected Integer date;
	@Field
	protected Integer month;
	@Field
	protected Integer year;
	@Field
	protected String monthString;
	@Field
	protected String dayOfWeek;
	@Field
	protected String dayPart;
	@Field
	protected String weekPart;
	@Field
	protected Integer saleCycle;

	@Field
	protected List<OrderItem> orders;
	@Field
	protected List<EnquiryItem> enquiryItems;
	@Field
	protected List<Settlement> settlements;

	@Field
	protected Integer deliveryPartner;
	@Field
	protected String deliveryPartnerName;

	@Field
	protected String cancellationReason;
	@Field
	protected Integer cancellationReasonId;
	@Field
	protected String bookedWastage;
	@Field
	protected Integer cancellationApprovedBy;
	@Field
	protected Integer cancellationGeneratedBy;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@Field
	protected Date cancellationTime;
	@Field
	protected Integer cancellationLinkedOrderId;
	@Field
	protected Boolean unsatisfiedCustomer;
	@Field
	protected Boolean ppe;
	@Field
	protected String orderRemark;
	@Field
	protected Integer deliveryAddressId;
	@Field
	protected String deliveryLocation;
	@Field
	protected String deliverySubLocation;
	@Field
	protected String deliveryAddressType;
	@Field
	protected Boolean outOfDeliveryArea;

	@Field
	protected Boolean employeeMeal;
	@Field
	protected Integer employeeIdForMeal;
	@Field
	protected String employeeNameForMeal;

	@Field
	protected String orderType;
	@Field
	protected Boolean manualBill;
	@Field
	protected Integer manualBillBookNo;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public String getGenerateOrderId() {
		return generateOrderId;
	}

	public void setGenerateOrderId(String generateOrderId) {
		this.generateOrderId = generateOrderId;
	}

	public String getExternalOrderId() {
		return externalOrderId;
	}

	public void setExternalOrderId(String externalOrderId) {
		this.externalOrderId = externalOrderId;
	}

	public Integer getUnitOrderId() {
		return unitOrderId;
	}

	public void setUnitOrderId(Integer unitOrderId) {
		this.unitOrderId = unitOrderId;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public Integer getAreaManagerId() {
		return areaManagerId;
	}

	public void setAreaManagerId(Integer areaManagerId) {
		this.areaManagerId = areaManagerId;
	}

	public String getAreaManagerName() {
		return areaManagerName;
	}

	public void setAreaManagerName(String areaManagerName) {
		this.areaManagerName = areaManagerName;
	}

	public String getUnitRegion() {
		return unitRegion;
	}

	public void setUnitRegion(String unitRegion) {
		this.unitRegion = unitRegion;
	}

	public String getUnitCategory() {
		return unitCategory;
	}

	public void setUnitCategory(String unitCategory) {
		this.unitCategory = unitCategory;
	}

	public String getUnitSubCategory() {
		return unitSubCategory;
	}

	public void setUnitSubCategory(String unitSubCategory) {
		this.unitSubCategory = unitSubCategory;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Integer getTerminalId() {
		return terminalId;
	}

	public void setTerminalId(Integer terminalId) {
		this.terminalId = terminalId;
	}

	public Integer getTableNumber() {
		return tableNumber;
	}

	public void setTableNumber(Integer tableNumber) {
		this.tableNumber = tableNumber;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public Boolean getNumberVerified() {
		return numberVerified;
	}

	public void setNumberVerified(Boolean numberVerified) {
		this.numberVerified = numberVerified;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public Boolean getEmailVerified() {
		return emailVerified;
	}

	public void setEmailVerified(Boolean emailVerified) {
		this.emailVerified = emailVerified;
	}

	public Boolean getNewCustomer() {
		return newCustomer;
	}

	public void setNewCustomer(Boolean newCustomer) {
		this.newCustomer = newCustomer;
	}

	public Integer getStartingLoyaltyPoints() {
		return startingLoyaltyPoints;
	}

	public void setStartingLoyaltyPoints(Integer startingLoyaltyPoints) {
		this.startingLoyaltyPoints = startingLoyaltyPoints;
	}

	public Integer getAcquiredLoyaltyPoints() {
		return acquiredLoyaltyPoints;
	}

	public void setAcquiredLoyaltyPoints(Integer acquiredLoyaltyPoints) {
		this.acquiredLoyaltyPoints = acquiredLoyaltyPoints;
	}

	public Integer getRedeemedLoyaltyPoints() {
		return redeemedLoyaltyPoints;
	}

	public void setRedeemedLoyaltyPoints(Integer redeemedLoyaltyPoints) {
		this.redeemedLoyaltyPoints = redeemedLoyaltyPoints;
	}

	public Integer getFinalLoyaltyPoints() {
		return finalLoyaltyPoints;
	}

	public void setFinalLoyaltyPoints(Integer finalLoyaltyPoints) {
		this.finalLoyaltyPoints = finalLoyaltyPoints;
	}

	public Boolean getAvailableSignupOffer() {
		return availableSignupOffer;
	}

	public void setAvailableSignupOffer(Boolean availableSignupOffer) {
		this.availableSignupOffer = availableSignupOffer;
	}

	public Boolean getAvailedSignupOffer() {
		return availedSignupOffer;
	}

	public void setAvailedSignupOffer(Boolean availedSignupOffer) {
		this.availedSignupOffer = availedSignupOffer;
	}

	public Boolean getAvailableRedemption() {
		return availableRedemption;
	}

	public void setAvailableRedemption(Boolean availableRedemption) {
		this.availableRedemption = availableRedemption;
	}

	public Boolean getAvailedRedemption() {
		return availedRedemption;
	}

	public void setAvailedRedemption(Boolean availedRedemption) {
		this.availedRedemption = availedRedemption;
	}

	public Integer getPreviousOrderId() {
		return previousOrderId;
	}

	public void setPreviousOrderId(Integer previousOrderId) {
		this.previousOrderId = previousOrderId;
	}

	public Integer getPreviousOrderCount() {
		return previousOrderCount;
	}

	public void setPreviousOrderCount(Integer previousOrderCount) {
		this.previousOrderCount = previousOrderCount;
	}

	public Integer getPreviousAPC() {
		return previousAPC;
	}

	public void setPreviousAPC(Integer previousAPC) {
		this.previousAPC = previousAPC;
	}

	public Integer getTotalGCPurchases() {
		return totalGCPurchases;
	}

	public void setTotalGCPurchases(Integer totalGCPurchases) {
		this.totalGCPurchases = totalGCPurchases;
	}

	public String getLastOrderSource() {
		return lastOrderSource;
	}

	public void setLastOrderSource(String lastOrderSource) {
		this.lastOrderSource = lastOrderSource;
	}

	public Boolean getCustomerOrderedOnline() {
		return customerOrderedOnline;
	}

	public void setCustomerOrderedOnline(Boolean customerOrderedOnline) {
		this.customerOrderedOnline = customerOrderedOnline;
	}

	public Date getLastOrderDate() {
		return lastOrderDate;
	}

	public void setLastOrderDate(Date lastOrderDate) {
		this.lastOrderDate = lastOrderDate;
	}

	public Integer getDaysGapSinceLastOrder() {
		return daysGapSinceLastOrder;
	}

	public void setDaysGapSinceLastOrder(Integer daysGapSinceLastOrder) {
		this.daysGapSinceLastOrder = daysGapSinceLastOrder;
	}

	public String getLastDeliveryLocation() {
		return lastDeliveryLocation;
	}

	public void setLastDeliveryLocation(String lastDeliveryLocation) {
		this.lastDeliveryLocation = lastDeliveryLocation;
	}

	public Integer getLastDeliveryUnitId() {
		return lastDeliveryUnitId;
	}

	public void setLastDeliveryUnitId(Integer lastDeliveryUnitId) {
		this.lastDeliveryUnitId = lastDeliveryUnitId;
	}

	public Integer getLastDineInUnitId() {
		return lastDineInUnitId;
	}

	public void setLastDineInUnitId(Integer lastDineInUnitId) {
		this.lastDineInUnitId = lastDineInUnitId;
	}

	public String getApcTrend() {
		return apcTrend;
	}

	public void setApcTrend(String apcTrend) {
		this.apcTrend = apcTrend;
	}

	public Boolean getPartnerOrder() {
		return partnerOrder;
	}

	public void setPartnerOrder(Boolean partnerOrder) {
		this.partnerOrder = partnerOrder;
	}


	public Boolean getTakeAway() {
		return takeAway;
	}

	public void setTakeAway(Boolean takeAway) {
		this.takeAway = takeAway;
	}

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	public String getPartnerOrderId() {
		return partnerOrderId;
	}

	public void setPartnerOrderId(String partnerOrderId) {
		this.partnerOrderId = partnerOrderId;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String status) {
		this.orderStatus = status;
	}

	public BigDecimal getSaleAmount() {
		return saleAmount;
	}

	public void setSaleAmount(BigDecimal saleAmount) {
		this.saleAmount = saleAmount;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public BigDecimal getTaxableAmount() {
		return taxableAmount;
	}

	public void setTaxableAmount(BigDecimal taxableAmount) {
		this.taxableAmount = taxableAmount;
	}

	public Integer getDiscountCode() {
		return discountCode;
	}

	public void setDiscountCode(Integer discountCode) {
		this.discountCode = discountCode;
	}

	public String getDiscountReason() {
		return discountReason;
	}

	public void setDiscountReason(String discountReason) {
		this.discountReason = discountReason;
	}

	public BigDecimal getDiscountPercentage() {
		return discountPercentage;
	}

	public void setDiscountPercentage(BigDecimal discountPercentage) {
		this.discountPercentage = discountPercentage;
	}

	public BigDecimal getDiscountValue() {
		return discountValue;
	}

	public void setDiscountValue(BigDecimal discountValue) {
		this.discountValue = discountValue;
	}

	public BigDecimal getPromotionalOffer() {
		return promotionalOffer;
	}

	public void setPromotionalOffer(BigDecimal promotionalOffer) {
		this.promotionalOffer = promotionalOffer;
	}

	public BigDecimal getTotalDiscount() {
		return totalDiscount;
	}

	public void setTotalDiscount(BigDecimal totalDiscount) {
		this.totalDiscount = totalDiscount;
	}

	public BigDecimal getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(BigDecimal paidAmount) {
		this.paidAmount = paidAmount;
	}

	public BigDecimal getRoundOffValue() {
		return roundOffValue;
	}

	public void setRoundOffValue(BigDecimal roundOffValue) {
		this.roundOffValue = roundOffValue;
	}

	public BigDecimal getSavings() {
		return savings;
	}

	public void setSavings(BigDecimal savings) {
		this.savings = savings;
	}

	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	public String getOfferCode() {
		return offerCode;
	}

	public void setOfferCode(String offerCode) {
		this.offerCode = offerCode;
	}

	public int getBillCreationSeconds() {
		return billCreationSeconds;
	}

	public void setBillCreationSeconds(int billCreationSeconds) {
		this.billCreationSeconds = billCreationSeconds;
	}

	public Date getBillingServerTime() {
		return billingServerTime;
	}

	public void setBillingServerTime(Date billingServerTime) {
		this.billingServerTime = billingServerTime;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public Date getSaleDate() {
		return saleDate;
	}

	public void setSaleDate(Date saleDate) {
		this.saleDate = saleDate;
	}

	public Integer getDate() {
		return date;
	}

	public void setDate(Integer date) {
		this.date = date;
	}

	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}

	public String getMonthString() {
		return monthString;
	}

	public void setMonthString(String monthString) {
		this.monthString = monthString;
	}

	public String getDayOfWeek() {
		return dayOfWeek;
	}

	public void setDayOfWeek(String dayOfWeek) {
		this.dayOfWeek = dayOfWeek;
	}

	public String getDayPart() {
		return dayPart;
	}

	public void setDayPart(String dayPart) {
		this.dayPart = dayPart;
	}

	public String getWeekPart() {
		return weekPart;
	}

	public void setWeekPart(String weekPart) {
		this.weekPart = weekPart;
	}

	public Integer getSaleCycle() {
		return saleCycle;
	}

	public void setSaleCycle(Integer saleCycle) {
		this.saleCycle = saleCycle;
	}

	public List<OrderItem> getOrders() {
		return orders;
	}

	public void setOrders(List<OrderItem> orders) {
		this.orders = orders;
	}

	public List<EnquiryItem> getEnquiryItems() {
		return enquiryItems;
	}

	public void setEnquiryItems(List<EnquiryItem> enquiryItems) {
		this.enquiryItems = enquiryItems;
	}

	public List<Settlement> getSettlements() {
		return settlements;
	}

	public void setSettlements(List<Settlement> settlements) {
		this.settlements = settlements;
	}

	public Integer getDeliveryPartner() {
		return deliveryPartner;
	}

	public void setDeliveryPartner(Integer deliveryPartner) {
		this.deliveryPartner = deliveryPartner;
	}

	public String getDeliveryPartnerName() {
		return deliveryPartnerName;
	}

	public void setDeliveryPartnerName(String deliveryPartnerName) {
		this.deliveryPartnerName = deliveryPartnerName;
	}

	public String getCancellationReason() {
		return cancellationReason;
	}

	public void setCancellationReason(String cancellationReason) {
		this.cancellationReason = cancellationReason;
	}

	public Integer getCancellationReasonId() {
		return cancellationReasonId;
	}

	public void setCancellationReasonId(Integer cancellationReasonId) {
		this.cancellationReasonId = cancellationReasonId;
	}

	public String getBookedWastage() {
		return bookedWastage;
	}

	public void setBookedWastage(String bookedWastage) {
		this.bookedWastage = bookedWastage;
	}

	public Integer getCancellationApprovedBy() {
		return cancellationApprovedBy;
	}

	public void setCancellationApprovedBy(Integer cancellationApprovedBy) {
		this.cancellationApprovedBy = cancellationApprovedBy;
	}

	public Integer getCancellationGeneratedBy() {
		return cancellationGeneratedBy;
	}

	public void setCancellationGeneratedBy(Integer cancellationGeneratedBy) {
		this.cancellationGeneratedBy = cancellationGeneratedBy;
	}

	public Date getCancellationTime() {
		return cancellationTime;
	}

	public void setCancellationTime(Date cancellationTime) {
		this.cancellationTime = cancellationTime;
	}

	public Integer getCancellationLinkedOrderId() {
		return cancellationLinkedOrderId;
	}

	public void setCancellationLinkedOrderId(Integer cancellationLinkedOrderId) {
		this.cancellationLinkedOrderId = cancellationLinkedOrderId;
	}

	public Boolean getUnsatisfiedCustomer() {
		return unsatisfiedCustomer;
	}

	public void setUnsatisfiedCustomer(Boolean unsatisfiedCustomer) {
		this.unsatisfiedCustomer = unsatisfiedCustomer;
	}

	public Boolean getPPE() { return this.ppe; }

	public void setPPE(Boolean ppe) { this.ppe = ppe; }

	public String getOrderRemark() {
		return orderRemark;
	}

	public void setOrderRemark(String orderRemark) {
		this.orderRemark = orderRemark;
	}

	public Integer getDeliveryAddressId() {
		return deliveryAddressId;
	}

	public void setDeliveryAddressId(Integer deliveryAddressId) {
		this.deliveryAddressId = deliveryAddressId;
	}

	public String getDeliveryLocation() {
		return deliveryLocation;
	}

	public void setDeliveryLocation(String deliveryLocation) {
		this.deliveryLocation = deliveryLocation;
	}

	public String getDeliverySubLocation() {
		return deliverySubLocation;
	}

	public void setDeliverySubLocation(String deliverySubLocation) {
		this.deliverySubLocation = deliverySubLocation;
	}

	public String getDeliveryAddressType() {
		return deliveryAddressType;
	}

	public void setDeliveryAddressType(String deliveryAddressType) {
		this.deliveryAddressType = deliveryAddressType;
	}

	public Boolean getOutOfDeliveryArea() {
		return outOfDeliveryArea;
	}

	public void setOutOfDeliveryArea(Boolean outOfDeliveryArea) {
		this.outOfDeliveryArea = outOfDeliveryArea;
	}

	public Boolean getEmployeeMeal() {
		return employeeMeal;
	}

	public void setEmployeeMeal(Boolean employeeMeal) {
		this.employeeMeal = employeeMeal;
	}

	public Integer getEmployeeIdForMeal() {
		return employeeIdForMeal;
	}

	public void setEmployeeIdForMeal(Integer employeeIdForMeal) {
		this.employeeIdForMeal = employeeIdForMeal;
	}

	public String getEmployeeNameForMeal() {
		return employeeNameForMeal;
	}

	public void setEmployeeNameForMeal(String employeeNameForMeal) {
		this.employeeNameForMeal = employeeNameForMeal;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public Boolean getManualBill() {
		return manualBill;
	}

	public void setManualBill(Boolean manualBill) {
		this.manualBill = manualBill;
	}

	public Integer getManualBillBookNo() {
		return manualBillBookNo;
	}

	public void setManualBillBookNo(Integer manualBillBookNo) {
		this.manualBillBookNo = manualBillBookNo;
	}



}
