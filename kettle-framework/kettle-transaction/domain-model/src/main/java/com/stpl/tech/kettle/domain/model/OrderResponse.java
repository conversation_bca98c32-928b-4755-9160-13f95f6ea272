package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.Map;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "order", "customer", "unit", "deliveryDetail" })
@XmlRootElement(name = "OrderResponse")
@Document
public class OrderResponse implements Serializable {
	/**
	 *
	 */
	private static final long serialVersionUID = -7134496687115315120L;
	@Id
	private String _id;

	/*@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/

	@XmlElement(required = true)
	@Field
	private Order order;
	@XmlElement(required = false, nillable = false)
	@Field
	private Customer customer;
	@XmlElement(required = true)
	@Field
	private UnitBasicDetail unit;
	@XmlElement(required = true)
	@Field
	private DeliveryResponse deliveryDetail;
	private boolean realTimeDataPublished;
	private boolean canceledOrderPushed;

	public Map<NotificationType, Boolean> getCommunicationType() {
		return communicationType;
	}

	public void setCommunicationType(Map<NotificationType, Boolean> communicationType) {
		this.communicationType = communicationType;
	}

	private Map<NotificationType,Boolean> communicationType;

	public OrderResponse() {
		super();
	}

	public OrderResponse(UnitBasicDetail unit, Order order, Customer customer, DeliveryResponse deliveryDetails,Map<NotificationType,Boolean> communicationType) {
		super();
		this.order = order;
		this.customer = customer;
		this.unit = unit;
		this.deliveryDetail = deliveryDetails;
		this.communicationType = communicationType;

	}

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/*public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}*/

	public Order getOrder() {
		return order;
	}

	public void setOrder(Order order) {
		this.order = order;
	}

	public Customer getCustomer() {
		return customer;
	}

	public void setCustomer(Customer customer) {
		this.customer = customer;
	}

	public UnitBasicDetail getUnit() {
		return unit;
	}

	public void setUnit(UnitBasicDetail unit) {
		this.unit = unit;
	}

	public DeliveryResponse getDeliveryDetail() {
		return deliveryDetail;
	}

	public void setDeliveryDetail(DeliveryResponse deliveryDetail) {
		this.deliveryDetail = deliveryDetail;
	}

	public boolean isRealTimeDataPublished() {
		return realTimeDataPublished;
	}

	public void setRealTimeDataPublished(boolean realTimeDataPublished) {
		this.realTimeDataPublished = realTimeDataPublished;
	}

	public boolean isCanceledOrderPushed() {
		return canceledOrderPushed;
	}

	public void setCanceledOrderPushed(boolean canceledOrderPushed) {
		this.canceledOrderPushed = canceledOrderPushed;
	}
}
