/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.23 at 12:37:04 PM IST 
//

package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * Java class for HLDAddress complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="HLDAddress"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="sub_locality" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="locality" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pin_code" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "HLDAddress", propOrder = { "sub_Locality", "locality", "city", "state", "pin_Code" })
public class HLDAddress {

	@XmlElement(name = "sub_locality", required = true)
	@JsonProperty("sub_locality")
	protected String sub_Locality;
	@XmlElement(required = true)
	@JsonProperty("locality")
	protected String locality;
	@XmlElement(required = true)
	@JsonProperty("city")
	protected String city;
	@XmlElement(required = true)
	@JsonProperty("state")
	protected String state;
	@XmlElement(name = "pin_code", required = true)
	@JsonProperty("pin_code")
	protected String pin_Code;

	/**
	 * Gets the value of the sub_Locality property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSub_Locality() {
		return sub_Locality;
	}

	/**
	 * Sets the value of the sub_Locality property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSub_Locality(String value) {
		this.sub_Locality = value;
	}

	/**
	 * Gets the value of the locality property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getLocality() {
		return locality;
	}

	/**
	 * Sets the value of the locality property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setLocality(String value) {
		this.locality = value;
	}

	/**
	 * Gets the value of the city property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCity() {
		return city;
	}

	/**
	 * Sets the value of the city property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCity(String value) {
		this.city = value;
	}

	/**
	 * Gets the value of the state property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getState() {
		return state;
	}

	/**
	 * Sets the value of the state property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setState(String value) {
		this.state = value;
	}

	/**
	 * Gets the value of the pin_Code property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPin_Code() {
		return pin_Code;
	}

	/**
	 * Sets the value of the pin_Code property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPin_Code(String value) {
		this.pin_Code = value;
	}

}
