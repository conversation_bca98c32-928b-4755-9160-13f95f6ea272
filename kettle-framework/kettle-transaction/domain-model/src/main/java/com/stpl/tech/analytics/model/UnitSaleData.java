/**
 * 
 */
package com.stpl.tech.analytics.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter1;

/**
 * <AUTHOR>
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitSaleData", propOrder = { "ticket", "sales", "apc" })
public class UnitSaleData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1346285426209365867L;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	private int ticket;
	private BigDecimal sales = new BigDecimal(0);
	private BigDecimal apc = new BigDecimal(0);

	public int getTicket() {
		return ticket;
	}

	public void setTicket(int ticket) {
		this.ticket = ticket;
	}

	public BigDecimal getSales() {
		return sales;
	}

	public void setSales(BigDecimal sales) {
		this.sales = sales;
	}

	public BigDecimal getApc() {
		return apc;
	}

	public void setApc(BigDecimal apc) {
		this.apc = apc;
	}

}
