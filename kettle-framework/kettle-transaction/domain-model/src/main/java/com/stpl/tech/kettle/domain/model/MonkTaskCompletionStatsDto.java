/*
 * Created By Shanmukh
 */
package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedHashMap;

@Data
public class MonkTaskCompletionStatsDto implements Serializable {
    private Integer monkTaskCompletionStatsId;
    private Integer orderId;
    private Integer taskId;
    private Integer unitId;
    private Integer actualBoilCount;
    private Integer expectedBoilCount;
    private Integer totalTimeForPouring;
    private Integer totalTimeForAddPatti;
    private Integer totalTimeForBrewing;
    private Integer totalTimeForTaskCompletion;
    private Integer actualWaterDispensed;
    private Integer expectedWaterToDispense;
    private Integer actualMilkDispensed;
    private Integer expectedMilkToDispense;
    @JsonProperty("finalQuantityWithIngredients")
    private Integer finalQuantityWithIngredients;
    private Integer boilsDetectedBeforeAddPatti;
    private Integer minimumTargetWeight;
    private BigDecimal firmwareVersion;
    private String isActualCompletionLog;
    private Date logAddTime;
    private String recipe;
    private String isManualTask;
    private String isClubbed;
    @JsonProperty("clubbedWithTask")
    private Integer clubbedWithTask;
    private Date logAddTimeAtServer;
    private BigDecimal recipeQuantity;
    private BigDecimal taskQuantity;
    private String recipeVersionAtMonk;
    @JsonProperty("recipeRegionAtMonk")
    private String recipeRegionAtMonk;
    private String actualRecipeVersionOfUnit;
    private String actualRecipeRegionOfUnit;
    @JsonProperty("isSplit")
    private String isSplit;
    @JsonProperty("linkedTaskId")
    private Integer linkedTaskId;
    @JsonProperty("monkName")
    private String monkName;
    @JsonProperty("hotStationVersion")
    private String hotStationVersion;
    @JsonProperty("assemblyVersion")
    private String assemblyVersion;
    @JsonProperty("weightCalibrationPoint")
    private BigDecimal weightCalibrationPoint;
    @JsonProperty("forcefullyRemoved")
    private String forcefullyRemoved;
    @JsonProperty("totalMonks")
    private Integer totalMonks;
    @JsonProperty("activeMonks")
    private Integer activeMonks;
    @JsonProperty("deActivatedMonks")
    private Integer deActivatedMonks;
    @JsonProperty("connectedMonks")
    private Integer connectedMonks;
    @JsonProperty("notConnectedMonks")
    private Integer notConnectedMonks;
    @JsonProperty("activeButNotConnectedMonks")
    private Integer activeButNotConnectedMonks;
    @JsonProperty("underCleaningMonks")
    private Integer underCleaningMonks;
    @JsonProperty("underResetMonks")
    private Integer underResetMonks;
    private Integer expectedMilkToDispenseAfterAddons;
    private Integer expectedWaterToDispenseAfterAddons;
    private String recipeAfterAddons;
    private LinkedHashMap<String, Integer> recipeLevelAddons;
}
