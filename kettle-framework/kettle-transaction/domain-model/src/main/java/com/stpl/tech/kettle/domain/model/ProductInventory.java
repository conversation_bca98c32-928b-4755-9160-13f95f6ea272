/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.21 at 05:44:11 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;


/**
 * <p>Java class for ProductInventory complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ProductInventory"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="unit" type="{http://www.w3schools.com}UnitBasicDetail"/&gt;
 *         &lt;element name="product" type="{http://www.w3schools.com}ProductBasicDetail"/&gt;
 *         &lt;element name="lastStockOutTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastUpdatedTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="thresholdData" type="{http://www.w3schools.com}InventoryThresholdData"/&gt;
 *         &lt;element name="quantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductInventory", propOrder = {
    "unit",
    "product",
    "lastStockOutTime",
    "lastUpdatedTime",
    "thresholdData",
    "quantity"
})
public class ProductInventory implements Comparable<ProductInventory> {

    @XmlElement(required = true)
    protected UnitBasicDetail unit;
    @XmlElement(required = true)
    protected ProductBasicDetail product;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date lastStockOutTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdatedTime;
    @XmlElement(required = true, nillable = true)
    protected InventoryThresholdData thresholdData;
    protected int quantity;
    protected int expireQuantity;

    /**
     * Gets the value of the unit property.
     * 
     * @return
     *     possible object is
     *     {@link UnitBasicDetail }
     *     
     */
    public UnitBasicDetail getUnit() {
        return unit;
    }

    /**
     * Sets the value of the unit property.
     * 
     * @param value
     *     allowed object is
     *     {@link UnitBasicDetail }
     *     
     */
    public void setUnit(UnitBasicDetail value) {
        this.unit = value;
    }

    /**
     * Gets the value of the product property.
     * 
     * @return
     *     possible object is
     *     {@link ProductBasicDetail }
     *     
     */
    public ProductBasicDetail getProduct() {
        return product;
    }

    /**
     * Sets the value of the product property.
     * 
     * @param value
     *     allowed object is
     *     {@link ProductBasicDetail }
     *     
     */
    public void setProduct(ProductBasicDetail value) {
        this.product = value;
    }

    /**
     * Gets the value of the lastStockOutTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getLastStockOutTime() {
        return lastStockOutTime;
    }

    /**
     * Sets the value of the lastStockOutTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastStockOutTime(Date value) {
        this.lastStockOutTime = value;
    }

    /**
     * Gets the value of the lastUpdatedTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    /**
     * Sets the value of the lastUpdatedTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastUpdatedTime(Date value) {
        this.lastUpdatedTime = value;
    }

    /**
     * Gets the value of the thresholdData property.
     * 
     * @return
     *     possible object is
     *     {@link InventoryThresholdData }
     *     
     */
    public InventoryThresholdData getThresholdData() {
        return thresholdData;
    }

    /**
     * Sets the value of the thresholdData property.
     * 
     * @param value
     *     allowed object is
     *     {@link InventoryThresholdData }
     *     
     */
    public void setThresholdData(InventoryThresholdData value) {
        this.thresholdData = value;
    }

    /**
     * Gets the value of the quantity property.
     * 
     */
    public int getQuantity() {
        return quantity;
    }

    /**
     * Sets the value of the quantity property.
     * 
     */
    public void setQuantity(int value) {
        this.quantity = value;
    }

	public int getExpireQuantity() {
		return expireQuantity;
	}

	public void setExpireQuantity(int expireQuantity) {
		this.expireQuantity = expireQuantity;
	}

	@Override
	public int compareTo(ProductInventory o) {
		int result;
		if(this.getUnit().getName().compareTo(o.getUnit().getName())==0){
			result = this.getProduct().getDetail().getName().compareTo(o.getProduct().getDetail().getName());			
		}else{
			result = this.getUnit().getName().compareTo(o.getUnit().getName());
		}
		return result;
	}
	
}
