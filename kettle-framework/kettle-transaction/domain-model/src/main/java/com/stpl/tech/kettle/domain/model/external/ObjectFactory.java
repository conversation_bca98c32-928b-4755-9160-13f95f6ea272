//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.08.22 at 07:47:15 PM IST 
//


package com.stpl.tech.kettle.domain.model.external;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.stpl.tech.kettle.domain.model.external package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.stpl.tech.kettle.domain.model.external
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link EventDetail }
     * 
     */
    public EventDetail createEventDetail() {
        return new EventDetail();
    }

    /**
     * Create an instance of {@link FormResponse }
     * 
     */
    public FormResponse createFormResponse() {
        return new FormResponse();
    }

    /**
     * Create an instance of {@link FormDefinition }
     * 
     */
    public FormDefinition createFormDefinition() {
        return new FormDefinition();
    }

    /**
     * Create an instance of {@link FormField }
     * 
     */
    public FormField createFormField() {
        return new FormField();
    }

    /**
     * Create an instance of {@link FormResponseData }
     * 
     */
    public FormResponseData createFormResponseData() {
        return new FormResponseData();
    }

    /**
     * Create an instance of {@link ChoiceData }
     * 
     */
    public ChoiceData createChoiceData() {
        return new ChoiceData();
    }

    /**
     * Create an instance of {@link MultipleChoiceData }
     * 
     */
    public MultipleChoiceData createMultipleChoiceData() {
        return new MultipleChoiceData();
    }

    /**
     * Create an instance of {@link HiddenAttributes }
     * 
     */
    public HiddenAttributes createHiddenAttributes() {
        return new HiddenAttributes();
    }

}
