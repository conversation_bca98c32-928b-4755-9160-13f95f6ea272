/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class DirectCost {

	private BigDecimal salary;
	private BigDecimal salaryIncentive;
	private BigDecimal salesIncentive;
	private BigDecimal depreciationOfBike;
	private BigDecimal waterCharges;
	private BigDecimal propertyTax;
	private BigDecimal openingLicencesFees;
	private BigDecimal registrationCharges;
	private BigDecimal stampDutyCharges;
	private BigDecimal designingFees;
//	private BigDecimal prontoAMC;
//	private BigDecimal dgRental;
//	private BigDecimal edcRental;
//	private BigDecimal systemRental;
//	private BigDecimal roRental;
	private BigDecimal fixCAM;
	private BigDecimal chillingCharges;
	private BigDecimal marketingCharges;
//	private BigDecimal pettyCashRentals;
//	private BigDecimal musicRentals;
	private BigDecimal internetPartnerRental;
	private BigDecimal supportOpsManagement;
//	private BigDecimal techologyPlatformCharges;
//	private BigDecimal techologyOthers;
	private BigDecimal technologyVariable;
	private BigDecimal energyElectricity = new BigDecimal(0d);
	private BigDecimal energyDGRunning = new BigDecimal(0d);
	private BigDecimal businessPromotion;
	private BigDecimal businessPromotionSR;
	private BigDecimal roundedOff;
	private BigDecimal shortAndExcess;
	private BigDecimal roAMC;
//	private BigDecimal insuranceAssets;
//	private BigDecimal insuranceCGL;
	private BigDecimal insuranceDnO;
	private BigDecimal badDebtsWrittenOff;
	private BigDecimal serviceChargesPaid;
	private BigDecimal insuranceVehicle;
//	private BigDecimal othersAMC;
//	private BigDecimal othersMaintenance;
	private BigDecimal byodCharges;
	private BigDecimal carLease;
	private BigDecimal driverSalary;
	private BigDecimal gratuity;
//	private BigDecimal insurnaceAccidental;
//	private BigDecimal insurnaceMedical;
	private BigDecimal supportsOpsTurnover;
//	private BigDecimal employeeFacilitationExpenses;
	private BigDecimal telephoneSR;
	private BigDecimal vehicleRunningAndMaintSR;
	private BigDecimal employeeStockOptionExpense;
	private BigDecimal employerContributionLWF;
	private BigDecimal esicEmployerCont;
	private BigDecimal leaveTravelReimbursement;
	private BigDecimal pfAdministrationCharges;
	private BigDecimal pfEmployerCont;
	private BigDecimal quarterlyIncentive;
	private BigDecimal supportAudit;
	private BigDecimal supportCCC;
	private BigDecimal supportIT;
	private BigDecimal supportMaintenance;
	private BigDecimal supportCommWH;

	private BigDecimal preOpeningConsumable;

	private BigDecimal preOpeningOthers;

	private BigDecimal preOpeningRent;

	private BigDecimal preOpeningSalary;

	private BigDecimal bankCharges;

	private BigDecimal interestOnLoan;

	private BigDecimal intrestOnTDSorGST;

	private BigDecimal interestOnFDR;

	private BigDecimal profitSaleMutualFunds;

	private BigDecimal interestIncomeTaxRefund;

	private BigDecimal miscIncome;

	private BigDecimal discountReceived;

	private BigDecimal interiorDesigningCharge;

	private BigDecimal scrape;
	private BigDecimal serviceCharges;
	private BigDecimal serviceChargesFICO;

	private BigDecimal fuelChargesCafe;

	private BigDecimal bonusAttendance;
	private BigDecimal bonusJoining;
	private BigDecimal bonusReferral;
	private BigDecimal bonusHoliday;
	private BigDecimal bonusOthers;
	private BigDecimal allowanceRemoteLocation;
	private BigDecimal allowanceEmployeeBenefit;
	private BigDecimal allowanceCityCompensatory;
	private BigDecimal allowanceMonk;
	private BigDecimal allowanceOthers;
	private BigDecimal noticePeriodBuyout;
	private BigDecimal noticePeriodDeduction;
	private BigDecimal relocationExpenses;
	private BigDecimal stipendExpenses;
	private BigDecimal trainingCostRecovery;
	private BigDecimal severancePay;
	private BigDecimal labourCharges;

	private BigDecimal cancellationChargesChannelPartners;

	private BigDecimal fixedParkingCharges = new BigDecimal(0d);
	private BigDecimal cogsLogistics = new BigDecimal(0d);
	private BigDecimal preOpeningCamEleWater = new BigDecimal(0d);
	private BigDecimal preOpeningRegistrationCharges = new BigDecimal(0d);
	private BigDecimal preOpeningStampDutyCharges = new BigDecimal(0d);
	private BigDecimal preOpeningConsumableTax = new BigDecimal(0d);
	private BigDecimal support = new BigDecimal(0d);
//	private BigDecimal corporateMarketingChannelPartner = new BigDecimal(0d);
	private BigDecimal preOpeningEleWater = new BigDecimal(0d);
	private BigDecimal preOpeningCam = new BigDecimal(0d);
	private BigDecimal interestOnFixedDepositFICO = new BigDecimal(0d);
//	private BigDecimal interestOnTermLoan = new BigDecimal(0d);
	private BigDecimal liabilityNoLongerRequiredWrittenBack = new BigDecimal(0d);
	private BigDecimal amortizationOfIntangibleAssets = new BigDecimal(0d);

	public BigDecimal getSalary() {
		return salary;
	}

	public void setSalary(BigDecimal salary) {
		this.salary = salary;
	}

	public BigDecimal getSalaryIncentive() {
		return salaryIncentive;
	}

	public void setSalaryIncentive(BigDecimal salaryIncentive) {
		this.salaryIncentive = salaryIncentive;
	}

	public BigDecimal getSalesIncentive() {
		return salesIncentive;
	}

	public void setSalesIncentive(BigDecimal salesIncentive) {
		this.salesIncentive = salesIncentive;
	}

	public BigDecimal getDepreciationOfBike() {
		return depreciationOfBike;
	}

	public void setDepreciationOfBike(BigDecimal depreciationOfBike) {
		this.depreciationOfBike = depreciationOfBike;
	}

	public BigDecimal getWaterCharges() {
		return waterCharges;
	}

	public void setWaterCharges(BigDecimal waterCharges) {
		this.waterCharges = waterCharges;
	}

	public BigDecimal getPropertyTax() {
		return propertyTax;
	}

	public void setPropertyTax(BigDecimal propertyTax) {
		this.propertyTax = propertyTax;
	}

	public BigDecimal getOpeningLicencesFees() {
		return openingLicencesFees;
	}

	public void setOpeningLicencesFees(BigDecimal openingLicencesFees) {
		this.openingLicencesFees = openingLicencesFees;
	}

	public BigDecimal getRegistrationCharges() {
		return registrationCharges;
	}

	public void setRegistrationCharges(BigDecimal registrationCharges) {
		this.registrationCharges = registrationCharges;
	}

	public BigDecimal getStampDutyCharges() {
		return stampDutyCharges;
	}

	public void setStampDutyCharges(BigDecimal stampDutyCharges) {
		this.stampDutyCharges = stampDutyCharges;
	}

	public BigDecimal getDesigningFees() {
		return designingFees;
	}

	public void setDesigningFees(BigDecimal designingFees) {
		this.designingFees = designingFees;
	}

	/*public BigDecimal getProntoAMC() {
		return prontoAMC;
	}

	public void setProntoAMC(BigDecimal prontoAMC) {
		this.prontoAMC = prontoAMC;
	}

	public BigDecimal getDgRental() {
		return dgRental;
	}

	public void setDgRental(BigDecimal dgRental) {
		this.dgRental = dgRental;
	}

	public BigDecimal getEdcRental() {
		return edcRental;
	}

	public void setEdcRental(BigDecimal edcRental) {
		this.edcRental = edcRental;
	}

	public BigDecimal getSystemRental() {
		return systemRental;
	}

	public void setSystemRental(BigDecimal systemRental) {
		this.systemRental = systemRental;
	}

	public BigDecimal getRoRental() {
		return roRental;
	}

	public void setRoRental(BigDecimal roRental) {
		this.roRental = roRental;
	}

	public BigDecimal getInsuranceAssets() {
		return insuranceAssets;
	}

	public void setInsuranceAssets(BigDecimal insuranceAssets) {
		this.insuranceAssets = insuranceAssets;
	}

	public BigDecimal getInsuranceCGL() {
		return insuranceCGL;
	}

	public void setInsuranceCGL(BigDecimal insuranceCGL) {
		this.insuranceCGL = insuranceCGL;
	}*/

	public BigDecimal getInsuranceDnO() {
		return insuranceDnO;
	}

	public void setInsuranceDnO(BigDecimal insuranceDnO) {
		this.insuranceDnO = insuranceDnO;
	}

	public BigDecimal getFixCAM() {
		return fixCAM;
	}

	public void setFixCAM(BigDecimal fixCAM) {
		this.fixCAM = fixCAM;
	}

	public BigDecimal getChillingCharges() {
		return chillingCharges;
	}

	public void setChillingCharges(BigDecimal chillingCharges) {
		this.chillingCharges = chillingCharges;
	}

	public BigDecimal getMarketingCharges() {
		return marketingCharges;
	}

	public void setMarketingCharges(BigDecimal marketingCharges) {
		this.marketingCharges = marketingCharges;
	}

	/*public BigDecimal getPettyCashRentals() {
		return pettyCashRentals;
	}

	public void setPettyCashRentals(BigDecimal pettyCashRentals) {
		this.pettyCashRentals = pettyCashRentals;
	}

	public BigDecimal getMusicRentals() {
		return musicRentals;
	}

	public void setMusicRentals(BigDecimal musicRentals) {
		this.musicRentals = musicRentals;
	}*/

	public BigDecimal getInternetPartnerRental() {
		return internetPartnerRental;
	}

	public void setInternetPartnerRental(BigDecimal internetPartnerRental) {
		this.internetPartnerRental = internetPartnerRental;
	}

	public BigDecimal getSupportOpsManagement() {
		return supportOpsManagement;
	}

	public void setSupportOpsManagement(BigDecimal supportOperations) {
		this.supportOpsManagement = supportOperations;
	}

	/*public BigDecimal getTechologyPlatformCharges() {
		return techologyPlatformCharges;
	}

	public void setTechologyPlatformCharges(BigDecimal techologyPlatformCharges) {
		this.techologyPlatformCharges = techologyPlatformCharges;
	}

	public BigDecimal getTechologyOthers() {
		return techologyOthers;
	}

	public void setTechologyOthers(BigDecimal techologyOthers) {
		this.techologyOthers = techologyOthers;
	}*/

	public BigDecimal getTechnologyVariable() {
		return technologyVariable;
	}

	public void setTechnologyVariable(BigDecimal technologyVariable) {
		this.technologyVariable = technologyVariable;
	}

	public BigDecimal getEnergyElectricity() {
		return energyElectricity;
	}

	public void setEnergyElectricity(BigDecimal energyElectricity) {
		this.energyElectricity = energyElectricity;
	}

	public BigDecimal getEnergyDGRunning() {
		return energyDGRunning;
	}

	public void setEnergyDGRunning(BigDecimal energyDGRunning) {
		this.energyDGRunning = energyDGRunning;
	}

	public BigDecimal getBadDebtsWrittenOff() {
		return badDebtsWrittenOff;
	}

	public void setBadDebtsWrittenOff(BigDecimal badDebtsWrittenOff) {
		this.badDebtsWrittenOff = badDebtsWrittenOff;
	}

	public BigDecimal getServiceChargesPaid() {
		return serviceChargesPaid;
	}

	public void setServiceChargesPaid(BigDecimal serviceChargesPaid) {
		this.serviceChargesPaid = serviceChargesPaid;
	}

	public BigDecimal getInsuranceVehicle() {
		return insuranceVehicle;
	}

	public void setInsuranceVehicle(BigDecimal insuranceVehicle) {
		this.insuranceVehicle = insuranceVehicle;
	}

/*	public BigDecimal getOthersAMC() {
		return othersAMC;
	}

	public void setOthersAMC(BigDecimal othersAMC) {
		this.othersAMC = othersAMC;
	}

	public BigDecimal getOthersMaintenance() {
		return othersMaintenance;
	}

	public void setOthersMaintenance(BigDecimal othersMaintenance) {
		this.othersMaintenance = othersMaintenance;
	}*/

	public BigDecimal getByodCharges() {
		return byodCharges;
	}

	public void setByodCharges(BigDecimal byodCharges) {
		this.byodCharges = byodCharges;
	}

	public BigDecimal getCarLease() {
		return carLease;
	}

	public void setCarLease(BigDecimal carLease) {
		this.carLease = carLease;
	}

	public BigDecimal getDriverSalary() {
		return driverSalary;
	}

	public void setDriverSalary(BigDecimal driverSalary) {
		this.driverSalary = driverSalary;
	}

	public BigDecimal getGratuity() {
		return gratuity;
	}

	public void setGratuity(BigDecimal gratuity) {
		this.gratuity = gratuity;
	}

	/*public BigDecimal getInsurnaceAccidental() {
		return insurnaceAccidental;
	}

	public void setInsurnaceAccidental(BigDecimal insurnaceAccidental) {
		this.insurnaceAccidental = insurnaceAccidental;
	}

	public BigDecimal getInsurnaceMedical() {
		return insurnaceMedical;
	}

	public void setInsurnaceMedical(BigDecimal insurnaceMedical) {
		this.insurnaceMedical = insurnaceMedical;
	}*/

	public BigDecimal getSupportsOpsTurnover() {
		return supportsOpsTurnover;
	}

	public void setSupportsOpsTurnover(BigDecimal supportsOpsTurnover) {
		this.supportsOpsTurnover = supportsOpsTurnover;
	}

/*	public BigDecimal getEmployeeFacilitationExpenses() {
		return employeeFacilitationExpenses;
	}

	public void setEmployeeFacilitationExpenses(BigDecimal employeeFacilitationExpenses) {
		this.employeeFacilitationExpenses = employeeFacilitationExpenses;
	}*/

	public BigDecimal getTelephoneSR() {
		return telephoneSR;
	}

	public void setTelephoneSR(BigDecimal telephoneSR) {
		this.telephoneSR = telephoneSR;
	}

	public BigDecimal getVehicleRunningAndMaintSR() {
		return vehicleRunningAndMaintSR;
	}

	public void setVehicleRunningAndMaintSR(BigDecimal vehicleRunningAndMaintSR) {
		this.vehicleRunningAndMaintSR = vehicleRunningAndMaintSR;
	}

	public BigDecimal getEmployeeStockOptionExpense() {
		return employeeStockOptionExpense;
	}

	public void setEmployeeStockOptionExpense(BigDecimal employeeStockOptionExpense) {
		this.employeeStockOptionExpense = employeeStockOptionExpense;
	}

	public BigDecimal getEmployerContributionLWF() {
		return employerContributionLWF;
	}

	public void setEmployerContributionLWF(BigDecimal employerContributionLWF) {
		this.employerContributionLWF = employerContributionLWF;
	}

	public BigDecimal getEsicEmployerCont() {
		return esicEmployerCont;
	}

	public void setEsicEmployerCont(BigDecimal esicEmployerCont) {
		this.esicEmployerCont = esicEmployerCont;
	}

	public BigDecimal getLeaveTravelReimbursement() {
		return leaveTravelReimbursement;
	}

	public void setLeaveTravelReimbursement(BigDecimal leaveTravelReimbursement) {
		this.leaveTravelReimbursement = leaveTravelReimbursement;
	}

	public BigDecimal getPfAdministrationCharges() {
		return pfAdministrationCharges;
	}

	public void setPfAdministrationCharges(BigDecimal pfAdministrationCharges) {
		this.pfAdministrationCharges = pfAdministrationCharges;
	}

	public BigDecimal getPfEmployerCont() {
		return pfEmployerCont;
	}

	public void setPfEmployerCont(BigDecimal pfEmployerCont) {
		this.pfEmployerCont = pfEmployerCont;
	}

	public BigDecimal getQuarterlyIncentive() {
		return quarterlyIncentive;
	}

	public void setQuarterlyIncentive(BigDecimal quarterlyIncentive) {
		this.quarterlyIncentive = quarterlyIncentive;
	}

	public BigDecimal getSupportAudit() {
		return supportAudit;
	}

	public void setSupportAudit(BigDecimal supportAudit) {
		this.supportAudit = supportAudit;
	}

	public BigDecimal getSupportCCC() {
		return supportCCC;
	}

	public void setSupportCCC(BigDecimal supportCCC) {
		this.supportCCC = supportCCC;
	}

	public BigDecimal getSupportIT() {
		return supportIT;
	}

	public void setSupportIT(BigDecimal supportIT) {
		this.supportIT = supportIT;
	}

	public BigDecimal getSupportMaintenance() {
		return supportMaintenance;
	}

	public void setSupportMaintenance(BigDecimal supportMaintenance) {
		this.supportMaintenance = supportMaintenance;
	}

	public BigDecimal getSupportCommWH() {
		return supportCommWH;
	}

	public void setSupportCommWH(BigDecimal supportCommWH) {
		this.supportCommWH = supportCommWH;
	}

	public BigDecimal getPreOpeningConsumable() {
		return preOpeningConsumable;
	}

	public void setPreOpeningConsumable(BigDecimal preOpeningConsumable) {
		this.preOpeningConsumable = preOpeningConsumable;
	}

	public BigDecimal getPreOpeningOthers() {
		return preOpeningOthers;
	}

	public void setPreOpeningOthers(BigDecimal preOpeningOthers) {
		this.preOpeningOthers = preOpeningOthers;
	}

	public BigDecimal getPreOpeningRent() {
		return preOpeningRent;
	}

	public void setPreOpeningRent(BigDecimal preOpeningRent) {
		this.preOpeningRent = preOpeningRent;
	}

	public BigDecimal getPreOpeningSalary() {
		return preOpeningSalary;
	}

	public void setPreOpeningSalary(BigDecimal preOpeningSalary) {
		this.preOpeningSalary = preOpeningSalary;
	}

	public BigDecimal getBankCharges() {
		return bankCharges;
	}

	public void setBankCharges(BigDecimal bankCharges) {
		this.bankCharges = bankCharges;
	}

	public BigDecimal getInterestOnLoan() {
		return interestOnLoan;
	}

	public void setInterestOnLoan(BigDecimal interestOnLoan) {
		this.interestOnLoan = interestOnLoan;
	}

	public BigDecimal getIntrestOnTDSorGST() {
		return intrestOnTDSorGST;
	}

	public void setIntrestOnTDSorGST(BigDecimal intrestOnTDSorGST) {
		this.intrestOnTDSorGST = intrestOnTDSorGST;
	}

	public BigDecimal getInterestOnFDR() {
		return interestOnFDR;
	}

	public void setInterestOnFDR(BigDecimal interestOnFDR) {
		this.interestOnFDR = interestOnFDR;
	}

	public BigDecimal getProfitSaleMutualFunds() {
		return profitSaleMutualFunds;
	}

	public void setProfitSaleMutualFunds(BigDecimal profitSaleMutualFunds) {
		this.profitSaleMutualFunds = profitSaleMutualFunds;
	}

	public BigDecimal getInterestIncomeTaxRefund() {
		return interestIncomeTaxRefund;
	}

	public void setInterestIncomeTaxRefund(BigDecimal interestIncomeTaxRefund) {
		this.interestIncomeTaxRefund = interestIncomeTaxRefund;
	}

	public BigDecimal getMiscIncome() {
		return miscIncome;
	}

	public void setMiscIncome(BigDecimal miscIncome) {
		this.miscIncome = miscIncome;
	}

	public BigDecimal getDiscountReceived() {
		return discountReceived;
	}

	public void setDiscountReceived(BigDecimal discountReceived) {
		this.discountReceived = discountReceived;
	}

	public BigDecimal getInteriorDesigningCharge() {
		return interiorDesigningCharge;
	}

	public void setInteriorDesigningCharge(BigDecimal interiorDesigningCharge) {
		this.interiorDesigningCharge = interiorDesigningCharge;
	}

	public BigDecimal getScrape() {
		return scrape;
	}

	public void setScrape(BigDecimal scrape) {
		this.scrape = scrape;
	}

	public BigDecimal getServiceCharges() {
		return serviceCharges;
	}

	public void setServiceCharges(BigDecimal serviceCharges) {
		this.serviceCharges = serviceCharges;
	}

	public BigDecimal getServiceChargesFICO() {
		return serviceChargesFICO;
	}

	public void setServiceChargesFICO(BigDecimal serviceChargesFICO) {
		this.serviceChargesFICO = serviceChargesFICO;
	}

	public BigDecimal getBusinessPromotion() {
		return businessPromotion;
	}

	public void setBusinessPromotion(BigDecimal businessPromotion) {
		this.businessPromotion = businessPromotion;
	}

	public BigDecimal getBusinessPromotionSR() {
		return businessPromotionSR;
	}

	public void setBusinessPromotionSR(BigDecimal businessPromotionSR) {
		this.businessPromotionSR = businessPromotionSR;
	}

	public BigDecimal getRoundedOff() {
		return roundedOff;
	}

	public void setRoundedOff(BigDecimal roundedOff) {
		this.roundedOff = roundedOff;
	}

	public BigDecimal getShortAndExcess() {
		return shortAndExcess;
	}

	public void setShortAndExcess(BigDecimal shortAndExcess) {
		this.shortAndExcess = shortAndExcess;
	}

	public BigDecimal getRoAMC() {
		return roAMC;
	}

	public void setRoAMC(BigDecimal roAMC) {
		this.roAMC = roAMC;
	}

	public BigDecimal getFuelChargesCafe() {
		return fuelChargesCafe;
	}

	public void setFuelChargesCafe(BigDecimal fuelChargesCafe) {
		this.fuelChargesCafe = fuelChargesCafe;
	}

	public BigDecimal getBonusAttendance() {
		return bonusAttendance;
	}

	public void setBonusAttendance(BigDecimal bonusAttendance) {
		this.bonusAttendance = bonusAttendance;
	}

	public BigDecimal getBonusJoining() {
		return bonusJoining;
	}

	public void setBonusJoining(BigDecimal bonusJoining) {
		this.bonusJoining = bonusJoining;
	}

	public BigDecimal getBonusReferral() {
		return bonusReferral;
	}

	public void setBonusReferral(BigDecimal bonusReferral) {
		this.bonusReferral = bonusReferral;
	}

	public BigDecimal getBonusHoliday() {
		return bonusHoliday;
	}

	public void setBonusHoliday(BigDecimal bonusHoliday) {
		this.bonusHoliday = bonusHoliday;
	}

	public BigDecimal getBonusOthers() {
		return bonusOthers;
	}

	public void setBonusOthers(BigDecimal bonusOthers) {
		this.bonusOthers = bonusOthers;
	}

	public BigDecimal getAllowanceRemoteLocation() {
		return allowanceRemoteLocation;
	}

	public void setAllowanceRemoteLocation(BigDecimal allowanceRemoteLocation) {
		this.allowanceRemoteLocation = allowanceRemoteLocation;
	}

	public BigDecimal getAllowanceEmployeeBenefit() {
		return allowanceEmployeeBenefit;
	}

	public void setAllowanceEmployeeBenefit(BigDecimal allowanceEmployeeBenefit) {
		this.allowanceEmployeeBenefit = allowanceEmployeeBenefit;
	}

	public BigDecimal getAllowanceCityCompensatory() {
		return allowanceCityCompensatory;
	}

	public void setAllowanceCityCompensatory(BigDecimal allowanceCityCompensatory) {
		this.allowanceCityCompensatory = allowanceCityCompensatory;
	}

	public BigDecimal getAllowanceMonk() {
		return allowanceMonk;
	}

	public void setAllowanceMonk(BigDecimal allowanceMonk) {
		this.allowanceMonk = allowanceMonk;
	}

	public BigDecimal getAllowanceOthers() {
		return allowanceOthers;
	}

	public void setAllowanceOthers(BigDecimal allowanceOthers) {
		this.allowanceOthers = allowanceOthers;
	}

	public BigDecimal getNoticePeriodBuyout() {
		return noticePeriodBuyout;
	}

	public void setNoticePeriodBuyout(BigDecimal noticePeriodBuyout) {
		this.noticePeriodBuyout = noticePeriodBuyout;
	}

	public BigDecimal getNoticePeriodDeduction() {
		return noticePeriodDeduction;
	}

	public void setNoticePeriodDeduction(BigDecimal noticePeriodDeduction) {
		this.noticePeriodDeduction = noticePeriodDeduction;
	}

	public BigDecimal getRelocationExpenses() {
		return relocationExpenses;
	}

	public void setRelocationExpenses(BigDecimal relocationExpenses) {
		this.relocationExpenses = relocationExpenses;
	}

	public BigDecimal getStipendExpenses() {
		return stipendExpenses;
	}

	public void setStipendExpenses(BigDecimal stipendExpenses) {
		this.stipendExpenses = stipendExpenses;
	}

	public BigDecimal getTrainingCostRecovery() {
		return trainingCostRecovery;
	}

	public void setTrainingCostRecovery(BigDecimal trainingCostRecovery) {
		this.trainingCostRecovery = trainingCostRecovery;
	}

	public BigDecimal getSeverancePay() {
		return severancePay;
	}

	public void setSeverancePay(BigDecimal severancePay) {
		this.severancePay = severancePay;
	}

	public BigDecimal getLabourCharges() {
		return labourCharges;
	}

	public void setLabourCharges(BigDecimal labourCharges) {
		this.labourCharges = labourCharges;
	}

	public BigDecimal getCancellationChargesChannelPartners() {
		return cancellationChargesChannelPartners;
	}

	public void setCancellationChargesChannelPartners(BigDecimal cancellationChargesChannelPartners) {
		this.cancellationChargesChannelPartners = cancellationChargesChannelPartners;
	}

	public BigDecimal getFixedParkingCharges() {
		return fixedParkingCharges;
	}

	public void setFixedParkingCharges(BigDecimal fixedParkingCharges) {
		this.fixedParkingCharges = fixedParkingCharges;
	}

	public BigDecimal getCogsLogistics() {
		return cogsLogistics;
	}

	public void setCogsLogistics(BigDecimal COGsLogistics) {
		this.cogsLogistics = COGsLogistics;
	}

	public BigDecimal getPreOpeningCamEleWater() {
		return preOpeningCamEleWater;
	}

	public void setPreOpeningCamEleWater(BigDecimal preOpeningCamEleWater) {
		this.preOpeningCamEleWater = preOpeningCamEleWater;
	}

	public BigDecimal getPreOpeningRegistrationCharges() {
		return preOpeningRegistrationCharges;
	}

	public void setPreOpeningRegistrationCharges(BigDecimal preOpeningRegistrationCharges) {
		this.preOpeningRegistrationCharges = preOpeningRegistrationCharges;
	}

	public BigDecimal getPreOpeningStampDutyCharges() {
		return preOpeningStampDutyCharges;
	}

	public void setPreOpeningStampDutyCharges(BigDecimal preOpeningStampDutyCharges) {
		this.preOpeningStampDutyCharges = preOpeningStampDutyCharges;
	}

	public BigDecimal getPreOpeningConsumableTax() {
		return preOpeningConsumableTax;
	}

	public void setPreOpeningConsumableTax(BigDecimal preOpeningConsumableTax) {
		this.preOpeningConsumableTax = preOpeningConsumableTax;
	}

	public BigDecimal getSupport() {
		return support;
	}

	public void setSupport(BigDecimal support) {
		this.support = support;
	}

	/*public BigDecimal getCorporateMarketingChannelPartner() {
		return corporateMarketingChannelPartner;
	}

	public void setCorporateMarketingChannelPartner(BigDecimal corporateMarketingChannelPartner) {
		this.corporateMarketingChannelPartner = corporateMarketingChannelPartner;
	}*/

	public BigDecimal getPreOpeningEleWater() {
		return preOpeningEleWater;
	}

	public void setPreOpeningEleWater(BigDecimal preOpeningEleWater) {
		this.preOpeningEleWater = preOpeningEleWater;
	}

	public BigDecimal getPreOpeningCam() {
		return preOpeningCam;
	}

	public void setPreOpeningCam(BigDecimal preOpeningCam) {
		this.preOpeningCam = preOpeningCam;
	}

	public BigDecimal getInterestOnFixedDepositFICO() {
		return interestOnFixedDepositFICO;
	}

	public void setInterestOnFixedDepositFICO(BigDecimal interestOnFixedDepositFICO) {
		this.interestOnFixedDepositFICO = interestOnFixedDepositFICO;
	}

	/*public BigDecimal getInterestOnTermLoan() {
		return interestOnTermLoan;
	}

	public void setInterestOnTermLoan(BigDecimal interestOnTermLoan) {
		this.interestOnTermLoan = interestOnTermLoan;
	}*/

	public BigDecimal getLiabilityNoLongerRequiredWrittenBack() {
		return liabilityNoLongerRequiredWrittenBack;
	}

	public void setLiabilityNoLongerRequiredWrittenBack(BigDecimal liabilityNoLongerRequiredWrittenBack) {
		this.liabilityNoLongerRequiredWrittenBack = liabilityNoLongerRequiredWrittenBack;
	}

	public BigDecimal getAmortizationOfIntangibleAssets() {
		return amortizationOfIntangibleAssets;
	}

	public void setAmortizationOfIntangibleAssets(BigDecimal amortizationOfIntangibleAssets) {
		this.amortizationOfIntangibleAssets = amortizationOfIntangibleAssets;
	}
}
