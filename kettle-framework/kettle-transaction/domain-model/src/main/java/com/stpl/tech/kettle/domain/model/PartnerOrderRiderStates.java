package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PartnerOrderRiderStates {
    RIDER_ASSIGNED("rider-assigned", "CONFIRMED","ASSIGNED"),
    RIDER_ARRIVED("rider-arrived", "ARRIVED","REACHED_MERCHANT"),
    ORDER_PICKED_UP("pickedup", "PICKEDUP","PICKED_UP"),
    ORDER_REACHED_CUSTOMER("","","REACHED_CUSTOMER"),
    ORDER_DELIVERED("delivered", "DELIVERED","DELIVERED");
    private final String zomato;
    private final String swiggy;
    private final String magicpin;

}
