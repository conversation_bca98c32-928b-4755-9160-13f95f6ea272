package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonPropertyOrder({ "vQueryVoucherResult" })
public class GyftrQueryVoucher implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3501290404977358672L;

	@JsonProperty("vQueryVoucherResult")
	List<GyftrVoucherQueryResult> vQueryVoucherResult = new ArrayList<>();

	public List<GyftrVoucherQueryResult> getvQueryVoucherResult() {
		return vQueryVoucherResult;
	}

	public void setvQueryVoucherResult(List<GyftrVoucherQueryResult> vQueryVoucherResult) {
		this.vQueryVoucherResult = vQueryVoucherResult;
	}

	@Override
	public String toString() {
		return "GyftrQueryVoucher [vQueryVoucherResult=" + vQueryVoucherResult + "]";
	}

}
