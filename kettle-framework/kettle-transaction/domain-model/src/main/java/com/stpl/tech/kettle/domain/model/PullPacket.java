/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.02.11 at 01:20:58 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;


/**
 * <p>Java class for PullPacket complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PullPacket"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="pullPacketId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3schools.com}Employee"/&gt;
 *         &lt;element name="witnessedBy" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="paymentMode" type="{http://www.w3schools.com}PaymentMode"/&gt;
 *         &lt;element name="pullAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pullPacketStatus" type="{http://www.w3schools.com}PullPacketStatus"/&gt;
 *         &lt;element name="pullDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="pullPendingReason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pullSource" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pullPacketUnit" type="{http://www.w3schools.com}UnitBasicDetail"/&gt;
 *         &lt;element name="closurePaymentDetail" type="{http://www.w3schools.com}ClosurePaymentDetail"/&gt;
 *         &lt;element name="pullDenominations" type="{http://www.w3schools.com}PullPacketDenomination" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="pullSettlementDetail" type="{http://www.w3schools.com}PullSettlementDetail"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PullPacket", propOrder = {
    "pullPacketId",
    "createdBy",
    "createdByName",
    "witnessedBy",
    "paymentMode",
    "pullAmount",
    "comment",
    "pullPacketStatus",
    "pullDate",
    "pullPendingReason",
    "pullSource",
    "pullPacketUnit",
    "closurePaymentDetail",
    "pullDenominations",
    "pullSettlementDetail"
})
public class PullPacket {

    protected int pullPacketId;
    @XmlElement(required = true)
    protected int createdBy;
    @XmlElement(required = true)
    protected String createdByName;
    @XmlElement(required = true, nillable = true)
    protected String witnessedBy;
    @XmlElement(required = true)
    protected PaymentMode paymentMode;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal pullAmount;
    @XmlElement(required = true, nillable = true)
    protected String comment;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PullPacketStatus pullPacketStatus;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date pullDate;
    @XmlElement(required = true, nillable = true)
    protected String pullPendingReason;
    @XmlElement(required = true)
    protected String pullSource;
    @XmlElement(required = true)
    protected UnitBasicDetail pullPacketUnit;
    @XmlElement(required = true)
    protected ClosurePaymentDetail closurePaymentDetail;
    protected List<PullPacketDenomination> pullDenominations;
    @XmlElement(required = true, nillable = true)
    protected PullSettlementDetail pullSettlementDetail;

    /**
     * Gets the value of the pullPacketId property.
     * 
     */
    public int getPullPacketId() {
        return pullPacketId;
    }

    /**
     * Sets the value of the pullPacketId property.
     * 
     */
    public void setPullPacketId(int value) {
        this.pullPacketId = value;
    }

    /**
     * Gets the value of the createdBy property.
     * 
     * @return
     *     possible object is
     *     {@link Employee }
     *     
     */
    public int getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCreatedBy(int value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the witnessedBy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWitnessedBy() {
        return witnessedBy;
    }

    /**
     * Sets the value of the witnessedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWitnessedBy(String value) {
        this.witnessedBy = value;
    }

    /**
     * Gets the value of the paymentMode property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentMode }
     *     
     */
    public PaymentMode getPaymentMode() {
        return paymentMode;
    }

    /**
     * Sets the value of the paymentMode property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentMode }
     *     
     */
    public void setPaymentMode(PaymentMode value) {
        this.paymentMode = value;
    }

    /**
     * Gets the value of the pullAmount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getPullAmount() {
        return pullAmount;
    }

    /**
     * Sets the value of the pullAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPullAmount(BigDecimal value) {
        this.pullAmount = value;
    }

    /**
     * Gets the value of the comment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the pullPacketStatus property.
     * 
     * @return
     *     possible object is
     *     {@link PullPacketStatus }
     *     
     */
    public PullPacketStatus getPullPacketStatus() {
        return pullPacketStatus;
    }

    /**
     * Sets the value of the pullPacketStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link PullPacketStatus }
     *     
     */
    public void setPullPacketStatus(PullPacketStatus value) {
        this.pullPacketStatus = value;
    }

    /**
     * Gets the value of the pullDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getPullDate() {
        return pullDate;
    }

    /**
     * Sets the value of the pullDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPullDate(Date value) {
        this.pullDate = value;
    }

    /**
     * Gets the value of the pullPendingReason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPullPendingReason() {
        return pullPendingReason;
    }

    /**
     * Sets the value of the pullPendingReason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPullPendingReason(String value) {
        this.pullPendingReason = value;
    }

    /**
     * Gets the value of the pullSource property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPullSource() {
        return pullSource;
    }

    /**
     * Sets the value of the pullSource property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPullSource(String value) {
        this.pullSource = value;
    }

    /**
     * Gets the value of the pullPacketUnit property.
     * 
     * @return
     *     possible object is
     *     {@link UnitBasicDetail }
     *     
     */
    public UnitBasicDetail getPullPacketUnit() {
        return pullPacketUnit;
    }

    /**
     * Sets the value of the pullPacketUnit property.
     * 
     * @param value
     *     allowed object is
     *     {@link UnitBasicDetail }
     *     
     */
    public void setPullPacketUnit(UnitBasicDetail value) {
        this.pullPacketUnit = value;
    }

    /**
     * Gets the value of the closurePaymentDetail property.
     * 
     * @return
     *     possible object is
     *     {@link ClosurePaymentDetail }
     *     
     */
    public ClosurePaymentDetail getClosurePaymentDetail() {
        return closurePaymentDetail;
    }

    /**
     * Sets the value of the closurePaymentDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link ClosurePaymentDetail }
     *     
     */
    public void setClosurePaymentDetail(ClosurePaymentDetail value) {
        this.closurePaymentDetail = value;
    }

    /**
     * Gets the value of the pullDenominations property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the pullDenominations property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPullDenominations().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PullPacketDenomination }
     * 
     * 
     */
    public List<PullPacketDenomination> getPullDenominations() {
        if (pullDenominations == null) {
            pullDenominations = new ArrayList<PullPacketDenomination>();
        }
        return this.pullDenominations;
    }

    /**
     * Gets the value of the pullSettlementDetail property.
     * 
     * @return
     *     possible object is
     *     {@link PullSettlementDetail }
     *     
     */
    public PullSettlementDetail getPullSettlementDetail() {
        return pullSettlementDetail;
    }

    /**
     * Sets the value of the pullSettlementDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link PullSettlementDetail }
     *     
     */
    public void setPullSettlementDetail(PullSettlementDetail value) {
        this.pullSettlementDetail = value;
    }

	public String getCreatedByName() {
		return createdByName;
	}

	public void setCreatedByName(String createdByName) {
		this.createdByName = createdByName;
	}

    
}
