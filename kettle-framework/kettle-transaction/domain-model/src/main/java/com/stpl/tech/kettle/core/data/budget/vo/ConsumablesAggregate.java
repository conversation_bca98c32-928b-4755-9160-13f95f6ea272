/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.math.BigDecimal;

import com.stpl.tech.util.excelparser.annotations.ExcelField;

/**
 * <AUTHOR>
 *
 */
public class ConsumablesAggregate {

	private BigDecimal consumable = BigDecimal.ZERO;
	private BigDecimal consumableUtility = BigDecimal.ZERO;
	private BigDecimal consumableStationary = BigDecimal.ZERO;
	private BigDecimal consumableUniform = BigDecimal.ZERO;
	private BigDecimal consumableEquipment = BigDecimal.ZERO;
	private BigDecimal consumableCutlery = BigDecimal.ZERO;
	private BigDecimal consumableDisposable = BigDecimal.ZERO;

	private BigDecimal consumableLhi = BigDecimal.ZERO;
	private BigDecimal consumableIt = BigDecimal.ZERO;
	private BigDecimal consumableMaintenance = BigDecimal.ZERO;
	private BigDecimal consumableOfficeEquipment = BigDecimal.ZERO;
	private BigDecimal consumableChaiMonk = BigDecimal.ZERO;
	private BigDecimal consumableKitchenEquipment = BigDecimal.ZERO;

	private BigDecimal fixedAssets = BigDecimal.ZERO;
	private BigDecimal fixedAssetsCapex = BigDecimal.ZERO;
	private BigDecimal consumableMarketing = BigDecimal.ZERO;
	private BigDecimal consumableOthers = BigDecimal.ZERO;

	private BigDecimal consumableTax = BigDecimal.ZERO;
    
	private BigDecimal fixedAssetsTax = BigDecimal.ZERO;
	private BigDecimal fixedAssetsCapexTax = BigDecimal.ZERO;
	private BigDecimal consumableMarketingTax = BigDecimal.ZERO;
	private BigDecimal consumableOthersTax = BigDecimal.ZERO;

	private BigDecimal fixedAssetsEquipment = BigDecimal.ZERO;
	private BigDecimal fixedAssetFurniture = BigDecimal.ZERO;
	private BigDecimal fixedAssetsIT = BigDecimal.ZERO;
	private BigDecimal fixedAssetsKitchenEquipment = BigDecimal.ZERO;
	private BigDecimal fixedAssetsOfficeEquipment = BigDecimal.ZERO;
	private BigDecimal fixedAssetsVehicle= BigDecimal.ZERO;
	private BigDecimal fixedAssetsOthersSubCategory = BigDecimal.ZERO;

	/*
	  Added to save and calculate asset related depreciation, loss and damage
	 */
	private BigDecimal fixedAssetDepreciation = BigDecimal.ZERO;
	private BigDecimal fixedAssetLost = BigDecimal.ZERO;
	private BigDecimal fixedAssetDamage = BigDecimal.ZERO;

    private BigDecimal fixedAssetsEquipmentTax;
	private BigDecimal fixedAssetFurnitureTax;
	private BigDecimal fixedAssetsItTax;
	private BigDecimal fixedAssetsKitchenEquipmentTax;
	private BigDecimal fixedAssetsOfficeEquipmentTax;
	private BigDecimal fixedAssetsVehicleTax;
	private BigDecimal fixedAssetsOthersSubCategoryCafeTax;
	private BigDecimal fixedAssetsEquipmentHq;
	private BigDecimal fixedAssetFurnitureHq;
	private BigDecimal fixedAssetsItHq;
	private BigDecimal fixedAssetsKitchenEquipmentHq;
	private BigDecimal fixedAssetsOfficeEquipmentHq;
	private BigDecimal fixedAssetsVehicleHq;
	private BigDecimal fixedAssetsOthersSubCategoryHq;
	private BigDecimal fixedAssetsEquipmentHqTax;
	private BigDecimal fixedAssetFurnitureHqTax;
	private BigDecimal fixedAssetsItHqTax;
	private BigDecimal fixedAssetsKitchenEquipmentHqTax;
	private BigDecimal fixedAssetsOfficeEquipmentHqTax;
	private BigDecimal fixedAssetsVehicleHqTax;
	private BigDecimal fixedAssetsOthersSubCategoryHqTax;

	private BigDecimal consumableUtilityTax = BigDecimal.ZERO;
	private BigDecimal consumableStationaryTax = BigDecimal.ZERO;
	private BigDecimal consumableUniformTax = BigDecimal.ZERO;
	private BigDecimal consumableEquipmentTax = BigDecimal.ZERO;
	private BigDecimal consumableCutleryTax = BigDecimal.ZERO;
	private BigDecimal consumableDisposableTax = BigDecimal.ZERO;

	private BigDecimal consumableLhiTax = BigDecimal.ZERO;
	private BigDecimal consumableItTax = BigDecimal.ZERO;
	private BigDecimal consumableMaintenanceTax = BigDecimal.ZERO;
	private BigDecimal consumableOfficeEquipmentTax = BigDecimal.ZERO;
	private BigDecimal consumableChaiMonkTax = BigDecimal.ZERO;
	private BigDecimal consumableKitchenEquipmentTax = BigDecimal.ZERO;

	private BigDecimal depreciationFurnitureFixture = BigDecimal.ZERO;
	private BigDecimal depreciationOfficeEquipment = BigDecimal.ZERO;
	private BigDecimal depreciationKitchenEquipment = BigDecimal.ZERO;
	private BigDecimal depreciationEquipment = BigDecimal.ZERO;
	private BigDecimal depreciationIt = BigDecimal.ZERO;
	private BigDecimal depreciationVehicle = BigDecimal.ZERO;
	private BigDecimal depreciationOthers = BigDecimal.ZERO;


	public BigDecimal getConsumable() {
		return consumable;
	}

	public void setConsumable(BigDecimal consumable) {
		this.consumable = consumable;
	}

	public BigDecimal getConsumableUtility() {
		return consumableUtility;
	}

	public void setConsumableUtility(BigDecimal consumableUtility) {
		this.consumableUtility = consumableUtility;
	}

	public BigDecimal getConsumableStationary() {
		return consumableStationary;
	}

	public void setConsumableStationary(BigDecimal consumableStationary) {
		this.consumableStationary = consumableStationary;
	}

	public BigDecimal getConsumableUniform() {
		return consumableUniform;
	}

	public void setConsumableUniform(BigDecimal consumableUniform) {
		this.consumableUniform = consumableUniform;
	}

	public BigDecimal getConsumableEquipment() {
		return consumableEquipment;
	}

	public void setConsumableEquipment(BigDecimal consumableEquipment) {
		this.consumableEquipment = consumableEquipment;
	}

	public BigDecimal getConsumableCutlery() {
		return consumableCutlery;
	}

	public void setConsumableCutlery(BigDecimal consumableCutlery) {
		this.consumableCutlery = consumableCutlery;
	}

	public BigDecimal getConsumableDisposable() {
		return consumableDisposable;
	}

	public void setConsumableDisposable(BigDecimal consumableDisposable) {
		this.consumableDisposable = consumableDisposable;
	}

	public BigDecimal getFixedAssets() {
		return fixedAssets;
	}

	public void setFixedAssets(BigDecimal fixedAssets) {
		this.fixedAssets = fixedAssets;
	}

	public BigDecimal getConsumableMarketing() {
		return consumableMarketing;
	}

	public void setConsumableMarketing(BigDecimal consumableMarketing) {
		this.consumableMarketing = consumableMarketing;
	}

	public BigDecimal getConsumableOthers() {
		return consumableOthers;
	}

	public void setConsumableOthers(BigDecimal consumableOthers) {
		this.consumableOthers = consumableOthers;
	}

	public BigDecimal getConsumableTax() {
		return consumableTax;
	}

	public void setConsumableTax(BigDecimal consumableTax) {
		this.consumableTax = consumableTax;
	}

	public BigDecimal getFixedAssetsTax() {
		return fixedAssetsTax;
	}

	public void setFixedAssetsTax(BigDecimal fixedAssetsTax) {
		this.fixedAssetsTax = fixedAssetsTax;
	}

	public BigDecimal getConsumableMarketingTax() {
		return consumableMarketingTax;
	}

	public void setConsumableMarketingTax(BigDecimal consumableMarketingTax) {
		this.consumableMarketingTax = consumableMarketingTax;
	}

	public BigDecimal getConsumableOthersTax() {
		return consumableOthersTax;
	}

	public void setConsumableOthersTax(BigDecimal consumableOthersTax) {
		this.consumableOthersTax = consumableOthersTax;
	}

	public BigDecimal getFixedAssetsCapex() {
		return fixedAssetsCapex;
	}

	public void setFixedAssetsCapex(BigDecimal fixedAssetsCapex) {
		this.fixedAssetsCapex = fixedAssetsCapex;
	}

	public BigDecimal getFixedAssetsCapexTax() {
		return fixedAssetsCapexTax;
	}

	public void setFixedAssetsCapexTax(BigDecimal fixedAssetsCapexTax) {
		this.fixedAssetsCapexTax = fixedAssetsCapexTax;
	}

	public BigDecimal getFixedAssetsEquipment() {
		return fixedAssetsEquipment;
	}

	public void setFixedAssetsEquipment(BigDecimal fixedAssetsEquipment) {
		this.fixedAssetsEquipment = fixedAssetsEquipment;
	}

	public BigDecimal getFixedAssetFurniture() {
		return fixedAssetFurniture;
	}

	public void setFixedAssetFurniture(BigDecimal fixedAssetFurniture) {
		this.fixedAssetFurniture = fixedAssetFurniture;
	}

	public BigDecimal getFixedAssetsIT() {
		return fixedAssetsIT;
	}

	public void setFixedAssetsIT(BigDecimal fixedAssetsIT) {
		this.fixedAssetsIT = fixedAssetsIT;
	}

	public BigDecimal getFixedAssetsKitchenEquipment() {
		return fixedAssetsKitchenEquipment;
	}

	public void setFixedAssetsKitchenEquipment(BigDecimal fixedAssetsKitchenEquipment) {
		this.fixedAssetsKitchenEquipment = fixedAssetsKitchenEquipment;
	}

	public BigDecimal getFixedAssetsOfficeEquipment() {
		return fixedAssetsOfficeEquipment;
	}

	public void setFixedAssetsOfficeEquipment(BigDecimal fixedAssetsOfficeEquipment) {
		this.fixedAssetsOfficeEquipment = fixedAssetsOfficeEquipment;
	}

	public BigDecimal getFixedAssetsVehicle() {
		return fixedAssetsVehicle;
	}

	public void setFixedAssetsVehicle(BigDecimal fixedAssetsVehicle) {
		this.fixedAssetsVehicle = fixedAssetsVehicle;
	}

	public BigDecimal getFixedAssetsOthersSubCategory() {
		return fixedAssetsOthersSubCategory;
	}

	public void setFixedAssetsOthersSubCategory(BigDecimal fixedAssetsOthersSubCategory) {
		this.fixedAssetsOthersSubCategory = fixedAssetsOthersSubCategory;
	}

	public BigDecimal getFixedAssetDepreciation() {
		return fixedAssetDepreciation;
	}

	public void setFixedAssetDepreciation(BigDecimal fixedAssetDepreciation) {
		this.fixedAssetDepreciation = fixedAssetDepreciation;
	}

	public BigDecimal getFixedAssetLost() {
		return fixedAssetLost;
	}

	public void setFixedAssetLost(BigDecimal fixedAssetLost) {
		this.fixedAssetLost = fixedAssetLost;
	}

	public BigDecimal getFixedAssetDamage() {
		return fixedAssetDamage;
	}

	public void setFixedAssetDamage(BigDecimal fixedAssetDamage) {
		this.fixedAssetDamage = fixedAssetDamage;
	}

	public BigDecimal getFixedAssetsEquipmentTax() {
		return fixedAssetsEquipmentTax;
	}

	public void setFixedAssetsEquipmentTax(BigDecimal fixedAssetsEquipmentTax) {
		this.fixedAssetsEquipmentTax = fixedAssetsEquipmentTax;
	}

	public BigDecimal getFixedAssetFurnitureTax() {
		return fixedAssetFurnitureTax;
	}

	public void setFixedAssetFurnitureTax(BigDecimal fixedAssetFurnitureTax) {
		this.fixedAssetFurnitureTax = fixedAssetFurnitureTax;
	}

	public BigDecimal getFixedAssetsItTax() {
		return fixedAssetsItTax;
	}

	public void setFixedAssetsItTax(BigDecimal fixedAssetsItTax) {
		this.fixedAssetsItTax = fixedAssetsItTax;
	}

	public BigDecimal getFixedAssetsKitchenEquipmentTax() {
		return fixedAssetsKitchenEquipmentTax;
	}

	public void setFixedAssetsKitchenEquipmentTax(BigDecimal fixedAssetsKitchenEquipmentTax) {
		this.fixedAssetsKitchenEquipmentTax = fixedAssetsKitchenEquipmentTax;
	}

	public BigDecimal getFixedAssetsOfficeEquipmentTax() {
		return fixedAssetsOfficeEquipmentTax;
	}

	public void setFixedAssetsOfficeEquipmentTax(BigDecimal fixedAssetsOfficeEquipmentTax) {
		this.fixedAssetsOfficeEquipmentTax = fixedAssetsOfficeEquipmentTax;
	}

	public BigDecimal getFixedAssetsVehicleTax() {
		return fixedAssetsVehicleTax;
	}

	public void setFixedAssetsVehicleTax(BigDecimal fixedAssetsVehicleTax) {
		this.fixedAssetsVehicleTax = fixedAssetsVehicleTax;
	}

	public BigDecimal getFixedAssetsOthersSubCategoryCafeTax() {
		return fixedAssetsOthersSubCategoryCafeTax;
	}

	public void setFixedAssetsOthersSubCategoryCafeTax(BigDecimal fixedAssetsOthersSubCategoryCafeTax) {
		this.fixedAssetsOthersSubCategoryCafeTax = fixedAssetsOthersSubCategoryCafeTax;
	}

	public BigDecimal getFixedAssetsEquipmentHq() {
		return fixedAssetsEquipmentHq;
	}

	public void setFixedAssetsEquipmentHq(BigDecimal fixedAssetsEquipmentHq) {
		this.fixedAssetsEquipmentHq = fixedAssetsEquipmentHq;
	}

	public BigDecimal getFixedAssetFurnitureHq() {
		return fixedAssetFurnitureHq;
	}

	public void setFixedAssetFurnitureHq(BigDecimal fixedAssetFurnitureHq) {
		this.fixedAssetFurnitureHq = fixedAssetFurnitureHq;
	}

	public BigDecimal getFixedAssetsItHq() {
		return fixedAssetsItHq;
	}

	public void setFixedAssetsItHq(BigDecimal fixedAssetsItHq) {
		this.fixedAssetsItHq = fixedAssetsItHq;
	}

	public BigDecimal getFixedAssetsKitchenEquipmentHq() {
		return fixedAssetsKitchenEquipmentHq;
	}

	public void setFixedAssetsKitchenEquipmentHq(BigDecimal fixedAssetsKitchenEquipmentHq) {
		this.fixedAssetsKitchenEquipmentHq = fixedAssetsKitchenEquipmentHq;
	}

	public BigDecimal getFixedAssetsOfficeEquipmentHq() {
		return fixedAssetsOfficeEquipmentHq;
	}

	public void setFixedAssetsOfficeEquipmentHq(BigDecimal fixedAssetsOfficeEquipmentHq) {
		this.fixedAssetsOfficeEquipmentHq = fixedAssetsOfficeEquipmentHq;
	}

	public BigDecimal getFixedAssetsVehicleHq() {
		return fixedAssetsVehicleHq;
	}

	public void setFixedAssetsVehicleHq(BigDecimal fixedAssetsVehicleHq) {
		this.fixedAssetsVehicleHq = fixedAssetsVehicleHq;
	}

	public BigDecimal getFixedAssetsOthersSubCategoryHq() {
		return fixedAssetsOthersSubCategoryHq;
	}

	public void setFixedAssetsOthersSubCategoryHq(BigDecimal fixedAssetsOthersSubCategoryHq) {
		this.fixedAssetsOthersSubCategoryHq = fixedAssetsOthersSubCategoryHq;
	}

	public BigDecimal getFixedAssetsEquipmentHqTax() {
		return fixedAssetsEquipmentHqTax;
	}

	public void setFixedAssetsEquipmentHqTax(BigDecimal fixedAssetsEquipmentHqTax) {
		this.fixedAssetsEquipmentHqTax = fixedAssetsEquipmentHqTax;
	}

	public BigDecimal getFixedAssetFurnitureHqTax() {
		return fixedAssetFurnitureHqTax;
	}

	public void setFixedAssetFurnitureHqTax(BigDecimal fixedAssetFurnitureHqTax) {
		this.fixedAssetFurnitureHqTax = fixedAssetFurnitureHqTax;
	}

	public BigDecimal getFixedAssetsItHqTax() {
		return fixedAssetsItHqTax;
	}

	public void setFixedAssetsItHqTax(BigDecimal fixedAssetsItHqTax) {
		this.fixedAssetsItHqTax = fixedAssetsItHqTax;
	}

	public BigDecimal getFixedAssetsKitchenEquipmentHqTax() {
		return fixedAssetsKitchenEquipmentHqTax;
	}

	public void setFixedAssetsKitchenEquipmentHqTax(BigDecimal fixedAssetsKitchenEquipmentHqTax) {
		this.fixedAssetsKitchenEquipmentHqTax = fixedAssetsKitchenEquipmentHqTax;
	}

	public BigDecimal getFixedAssetsOfficeEquipmentHqTax() {
		return fixedAssetsOfficeEquipmentHqTax;
	}

	public void setFixedAssetsOfficeEquipmentHqTax(BigDecimal fixedAssetsOfficeEquipmentHqTax) {
		this.fixedAssetsOfficeEquipmentHqTax = fixedAssetsOfficeEquipmentHqTax;
	}

	public BigDecimal getFixedAssetsVehicleHqTax() {
		return fixedAssetsVehicleHqTax;
	}

	public void setFixedAssetsVehicleHqTax(BigDecimal fixedAssetsVehicleHqTax) {
		this.fixedAssetsVehicleHqTax = fixedAssetsVehicleHqTax;
	}

	public BigDecimal getFixedAssetsOthersSubCategoryHqTax() {
		return fixedAssetsOthersSubCategoryHqTax;
	}

	public void setFixedAssetsOthersSubCategoryHqTax(BigDecimal fixedAssetsOthersSubCategoryHqTax) {
		this.fixedAssetsOthersSubCategoryHqTax = fixedAssetsOthersSubCategoryHqTax;
	}

	public BigDecimal getConsumableUtilityTax() {
		return consumableUtilityTax;
	}

	public void setConsumableUtilityTax(BigDecimal consumableUtilityTax) {
		this.consumableUtilityTax = consumableUtilityTax;
	}

	public BigDecimal getConsumableStationaryTax() {
		return consumableStationaryTax;
	}

	public void setConsumableStationaryTax(BigDecimal consumableStationaryTax) {
		this.consumableStationaryTax = consumableStationaryTax;
	}

	public BigDecimal getConsumableUniformTax() {
		return consumableUniformTax;
	}

	public void setConsumableUniformTax(BigDecimal consumableUniformTax) {
		this.consumableUniformTax = consumableUniformTax;
	}

	public BigDecimal getConsumableEquipmentTax() {
		return consumableEquipmentTax;
	}

	public void setConsumableEquipmentTax(BigDecimal consumableEquipmentTax) {
		this.consumableEquipmentTax = consumableEquipmentTax;
	}

	public BigDecimal getConsumableCutleryTax() {
		return consumableCutleryTax;
	}

	public void setConsumableCutleryTax(BigDecimal consumableCutleryTax) {
		this.consumableCutleryTax = consumableCutleryTax;
	}

	public BigDecimal getConsumableDisposableTax() {
		return consumableDisposableTax;
	}

	public void setConsumableDisposableTax(BigDecimal consumableDisposableTax) {
		this.consumableDisposableTax = consumableDisposableTax;
	}

	public BigDecimal getConsumableLhi() {
		return consumableLhi;
	}

	public void setConsumableLhi(BigDecimal consumableLhi) {
		this.consumableLhi = consumableLhi;
	}

	public BigDecimal getConsumableIt() {
		return consumableIt;
	}

	public void setConsumableIt(BigDecimal consumableIt) {
		this.consumableIt = consumableIt;
	}

	public BigDecimal getConsumableMaintenance() {
		return consumableMaintenance;
	}

	public void setConsumableMaintenance(BigDecimal consumableMaintenance) {
		this.consumableMaintenance = consumableMaintenance;
	}

	public BigDecimal getConsumableOfficeEquipment() {
		return consumableOfficeEquipment;
	}

	public void setConsumableOfficeEquipment(BigDecimal consumableOfficeEquipment) {
		this.consumableOfficeEquipment = consumableOfficeEquipment;
	}

	public BigDecimal getConsumableKitchenEquipment() {
		return consumableKitchenEquipment;
	}

	public void setConsumableKitchenEquipment(BigDecimal consumableKitchenEquipment) {
		this.consumableKitchenEquipment = consumableKitchenEquipment;
	}

	public BigDecimal getConsumableChaiMonk() {
		return consumableChaiMonk;
	}

	public void setConsumableChaiMonk(BigDecimal consumableChaiMonk) {
		this.consumableChaiMonk = consumableChaiMonk;
	}

	public BigDecimal getConsumableLhiTax() {
		return consumableLhiTax;
	}

	public void setConsumableLhiTax(BigDecimal consumableLhiTax) {
		this.consumableLhiTax = consumableLhiTax;
	}

	public BigDecimal getConsumableItTax() {
		return consumableItTax;
	}

	public void setConsumableItTax(BigDecimal consumableItTax) {
		this.consumableItTax = consumableItTax;
	}

	public BigDecimal getConsumableMaintenanceTax() {
		return consumableMaintenanceTax;
	}

	public void setConsumableMaintenanceTax(BigDecimal consumableMaintenanceTax) {
		this.consumableMaintenanceTax = consumableMaintenanceTax;
	}

	public BigDecimal getConsumableOfficeEquipmentTax() {
		return consumableOfficeEquipmentTax;
	}

	public void setConsumableOfficeEquipmentTax(BigDecimal consumableOfficeEquipmentTax) {
		this.consumableOfficeEquipmentTax = consumableOfficeEquipmentTax;
	}

	public BigDecimal getConsumableKitchenEquipmentTax() {
		return consumableKitchenEquipmentTax;
	}

	public void setConsumableKitchenEquipmentTax(BigDecimal consumableKitchenEquipmentTax) {
		this.consumableKitchenEquipmentTax = consumableKitchenEquipmentTax;
	}

	public BigDecimal getConsumableChaiMonkTax() {
		return consumableChaiMonkTax;
	}

	public void setConsumableChaiMonkTax(BigDecimal consumableChaiMonkTax) {
		this.consumableChaiMonkTax = consumableChaiMonkTax;
	}

	public BigDecimal getDepreciationFurnitureFixture() {
		return depreciationFurnitureFixture;
	}

	public void setDepreciationFurnitureFixture(BigDecimal depreciationFurnitureFixture) {
		this.depreciationFurnitureFixture = depreciationFurnitureFixture;
	}

	public BigDecimal getDepreciationOfficeEquipment() {
		return depreciationOfficeEquipment;
	}

	public void setDepreciationOfficeEquipment(BigDecimal depreciationOfficeEquipment) {
		this.depreciationOfficeEquipment = depreciationOfficeEquipment;
	}

	public BigDecimal getDepreciationKitchenEquipment() {
		return depreciationKitchenEquipment;
	}

	public void setDepreciationKitchenEquipment(BigDecimal depreciationKitchenEquipment) {
		this.depreciationKitchenEquipment = depreciationKitchenEquipment;
	}

	public BigDecimal getDepreciationEquipment() {
		return depreciationEquipment;
	}

	public void setDepreciationEquipment(BigDecimal depreciationEquipment) {
		this.depreciationEquipment = depreciationEquipment;
	}

	public BigDecimal getDepreciationIt() {
		return depreciationIt;
	}

	public void setDepreciationIt(BigDecimal depreciationIt) {
		this.depreciationIt = depreciationIt;
	}

	public BigDecimal getDepreciationVehicle() {
		return depreciationVehicle;
	}

	public void setDepreciationVehicle(BigDecimal depreciationVehicle) {
		this.depreciationVehicle = depreciationVehicle;
	}

	public BigDecimal getDepreciationOthers() {
		return depreciationOthers;
	}

	public void setDepreciationOthers(BigDecimal depreciationOthers) {
		this.depreciationOthers = depreciationOthers;
	}
}
