package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class FavChaiProductDetails implements Serializable {


    private static final long serialVersionUID = 2622939037033650591L;
    private BigDecimal price; // price

    private String dimension; // dim object . name

    private Integer productId; // 10, 11, 12 -> based on milk selection

    private String productName; // Full Doodh etc

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
}
