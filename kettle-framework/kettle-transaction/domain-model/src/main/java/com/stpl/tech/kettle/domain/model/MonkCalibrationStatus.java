package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Date;

public class MonkCalibrationStatus implements Serializable {

    private static final long serialVersionUID = -4255434487702889704L;

    private Integer unitId;
    private Date unitOpenTime;
    private Integer monkNo;
    private boolean isCalibrated;
    private Date lastCalibrationTime;
    private boolean isCalibrationFailed;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Date getUnitOpenTime() {
        return unitOpenTime;
    }

    public void setUnitOpenTime(Date unitOpenTime) {
        this.unitOpenTime = unitOpenTime;
    }

    public Integer getMonkNo() {
        return monkNo;
    }

    public void setMonkNo(Integer monkNo) {
        this.monkNo = monkNo;
    }

    @JsonProperty("isCalibrated")
    public boolean isCalibrated() {
        return isCalibrated;
    }

    public void setCalibrated(boolean calibrated) {
        isCalibrated = calibrated;
    }

    public Date getLastCalibrationTime() {
        return lastCalibrationTime;
    }

    public void setLastCalibrationTime(Date lastCalibrationTime) {
        this.lastCalibrationTime = lastCalibrationTime;
    }

    @JsonProperty("isCalibrationFailed")
    public boolean isCalibrationFailed() {
        return isCalibrationFailed;
    }

    public void setCalibrationFailed(boolean calibrationFailed) {
        isCalibrationFailed = calibrationFailed;
    }

    @Override
    public String toString() {
        return "MonkCalibrationStatus{" +
                "unitId=" + unitId +
                ", unitOpenTime=" + unitOpenTime +
                ", monkNo=" + monkNo +
                ", isCalibrated=" + isCalibrated +
                ", lastCalibrationTime=" + lastCalibrationTime +
                ", isCalibrationFailed=" + isCalibrationFailed +
                '}';
    }
}
