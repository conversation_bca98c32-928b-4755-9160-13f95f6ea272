package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderDetailEventRequest {

    private String partnerOrderId;
    private String issueType;
    private String type;
    private String complaintTime;
    private String complaintItem;
    private String previousItems;
    private String modifiedItems;
    private boolean publishToFamePilot;

}
