package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderOHC {

	private String transType;
	private String servedAt;
	private String billNo;
	private String billDate;
	private BigDecimal totalBillAmt;
	private BigDecimal payableAmt;
	private BigDecimal roundAmt;
	private BigDecimal taxAmt;
	private List<OrderItemOHC> items = new ArrayList<>();
	List<PaymentsOHC> payments = new ArrayList<>();

	public String getTransType() {
		return transType;
	}

	public void setTransType(String transType) {
		this.transType = transType;
	}

	public String getServedAt() {
		return servedAt;
	}

	public void setServedAt(String servedAt) {
		this.servedAt = servedAt;
	}

	public String getBillNo() {
		return billNo;
	}

	public void setBillNo(String billNo) {
		this.billNo = billNo;
	}

	public String getBillDate() {
		return billDate;
	}

	public void setBillDate(String billDate) {
		this.billDate = billDate;
	}

	public BigDecimal getTotalBillAmt() {
		return totalBillAmt;
	}

	public void setTotalBillAmt(BigDecimal totalBillAmt) {
		this.totalBillAmt = totalBillAmt;
	}

	public BigDecimal getPayableAmt() {
		return payableAmt;
	}

	public void setPayableAmt(BigDecimal payableAmt) {
		this.payableAmt = payableAmt;
	}

	public BigDecimal getRoundAmt() {
		return roundAmt;
	}

	public void setRoundAmt(BigDecimal roundAmt) {
		this.roundAmt = roundAmt;
	}

	public BigDecimal getTaxAmt() {
		return taxAmt;
	}

	public void setTaxAmt(BigDecimal taxAmt) {
		this.taxAmt = taxAmt;
	}

	public List<OrderItemOHC> getItems() {
		return items;
	}

	public void setItems(List<OrderItemOHC> items) {
		this.items = items;
	}

	public List<PaymentsOHC> getPayments() {
		return payments;
	}
	
	
	public void setPayments(List<PaymentsOHC> payments) {
		this.payments = payments;
	}

}
