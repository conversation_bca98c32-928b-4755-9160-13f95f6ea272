/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class WastageAggregate {

	private BigDecimal unsatifiedCustomerCost;
	private BigDecimal ppeCost;
	private BigDecimal expiryWastage;
	private BigDecimal wastageOther;
	private BigDecimal marketingAndSampling;
	private BigDecimal trainingCogs;

	private BigDecimal unsatifiedCustomerCostTax;
	private BigDecimal ppeCostTax;
	private BigDecimal expiryWastageTax;
	private BigDecimal wastageOtherTax;
	private BigDecimal marketingAndSamplingTax;
	private BigDecimal trainingCogsTax;

	public BigDecimal getUnsatifiedCustomerCost() {
		return unsatifiedCustomerCost;
	}

	public void setUnsatifiedCustomerCost(BigDecimal unsatifiedCustomerCost) {
		this.unsatifiedCustomerCost = unsatifiedCustomerCost;
	}

	public BigDecimal getPPECost() { return this.ppeCost; }

	public void setPPECost(BigDecimal ppeCost) {
		this.ppeCost = ppeCost;
	}

	public BigDecimal getExpiryWastage() {
		return expiryWastage;
	}

	public void setExpiryWastage(BigDecimal expiryWastage) {
		this.expiryWastage = expiryWastage;
	}

	public BigDecimal getWastageOther() {
		return wastageOther;
	}

	public void setWastageOther(BigDecimal wastageOther) {
		this.wastageOther = wastageOther;
	}

	public BigDecimal getMarketingAndSampling() {
		return marketingAndSampling;
	}

	public void setMarketingAndSampling(BigDecimal marketingAndSampling) {
		this.marketingAndSampling = marketingAndSampling;
	}

	public BigDecimal getTrainingCogs() {
		return trainingCogs;
	}

	public void setTrainingCogs(BigDecimal trainingCogs) {
		this.trainingCogs = trainingCogs;
	}

	public BigDecimal getUnsatifiedCustomerCostTax() {
		return unsatifiedCustomerCostTax;
	}

	public void setUnsatifiedCustomerCostTax(BigDecimal unsatifiedCustomerTax) {
		this.unsatifiedCustomerCostTax = unsatifiedCustomerTax;
	}

	public BigDecimal getPPECostTax() { return this.ppeCostTax; }

	public void setPPECostTax(BigDecimal ppeCostTax) {
		this.ppeCostTax = ppeCostTax;
	}

	public BigDecimal getExpiryWastageTax() {
		return expiryWastageTax;
	}

	public void setExpiryWastageTax(BigDecimal expiryWastageTax) {
		this.expiryWastageTax = expiryWastageTax;
	}

	public BigDecimal getWastageOtherTax() {
		return wastageOtherTax;
	}

	public void setWastageOtherTax(BigDecimal wastageOtherTax) {
		this.wastageOtherTax = wastageOtherTax;
	}

	public BigDecimal getMarketingAndSamplingTax() {
		return marketingAndSamplingTax;
	}

	public void setMarketingAndSamplingTax(BigDecimal marketingAndSamplingTax) {
		this.marketingAndSamplingTax = marketingAndSamplingTax;
	}

	public BigDecimal getTrainingCogsTax() {
		return trainingCogsTax;
	}

	public void setTrainingCogsTax(BigDecimal trainingCogsTax) {
		this.trainingCogsTax = trainingCogsTax;
	}

}
