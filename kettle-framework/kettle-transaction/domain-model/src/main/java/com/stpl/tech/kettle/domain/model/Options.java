package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class Options implements Serializable {

    private static final long serialVersionUID = 5808665856552569827L;

    private Integer id; // prod id, prod id

    private String name; // name, alias

    private String type; // calsssification only for addon

    private String size; // dimension name, uom

    private BigDecimal qty;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public String getSize() {
        return size;
    }

    public BigDecimal getQty() {
        return qty;
    }
}
