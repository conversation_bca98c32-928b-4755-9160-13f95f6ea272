/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.01.05 at 01:47:45 PM IST
//


package com.stpl.tech.kettle.delivery.model;

import com.stpl.tech.master.domain.model.Adapter1;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.Date;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="deliveryPartnerId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="orderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="generatedOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deliveryTaskId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deliveryStatus" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="statusUpdateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="deliveryBoyName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deliveryBoyPhoneNum" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="failureCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="failureMessage" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "deliveryPartnerId",
    "unitId",
    "orderId",
    "generatedOrderId",
    "deliveryTaskId",
    "deliveryStatus",
    "statusUpdateTime",
    "deliveryBoyName",
    "deliveryBoyPhoneNum",
    "deliveryBoyId",
    "failureCode",
    "failureMessage",
    "allotedNo",
    "byAssemblyScreen"
})
@XmlRootElement(name = "DeliveryResponse")
@Document
public class DeliveryResponse implements Serializable {

    /**
	 *
	 */
	private static final long serialVersionUID = -2956299836577946525L;
	@Id
	private String _id;

	/*@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/
    @Field
    protected int deliveryPartnerId;
    @XmlElement(required = true)
    @Field
    protected Integer deliveryDetailId;
    @Field
    protected int unitId;
    @Field
    protected int orderId;
    @XmlElement(required = true)
    @Field
    protected String generatedOrderId;
    @XmlElement(required = true)
    @Field
    protected String deliveryTaskId;
    @Field
    protected int deliveryStatus;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    @Field
    protected Date statusUpdateTime;
    @XmlElement(required = true)
    @Field
    protected String deliveryBoyName;
    @XmlElement(required = true)
    @Field
    protected String deliveryBoyPhoneNum;
    @XmlElement(required = true)
    @Field
    protected Integer deliveryBoyId;
    @XmlElement(required = true)
    @Field
    protected String failureCode;
    @XmlElement(required = true)
    @Field
    protected String failureMessage;
    @XmlElement(required = true)
    @Field
    protected String allotedNo;
    protected boolean byAssemblyScreen=true;

    public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

/*
	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}
*/

	/**
     * Gets the value of the deliveryPartnerId property.
     *
     */
    public int getDeliveryPartnerId() {
        return deliveryPartnerId;
    }

    /**
     * Sets the value of the deliveryPartnerId property.
     *
     */
    public void setDeliveryPartnerId(int value) {
        this.deliveryPartnerId = value;
    }

    /**
     * Gets the value of the unitId property.
     *
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     *
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the orderId property.
     *
     */
    public int getOrderId() {
        return orderId;
    }

    /**
     * Sets the value of the orderId property.
     *
     */
    public void setOrderId(int value) {
        this.orderId = value;
    }

    /**
     * Gets the value of the generatedOrderId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getGeneratedOrderId() {
        return generatedOrderId;
    }

    /**
     * Sets the value of the generatedOrderId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setGeneratedOrderId(String value) {
        this.generatedOrderId = value;
    }

    /**
     * Gets the value of the deliveryTaskId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getDeliveryTaskId() {
        return deliveryTaskId;
    }

    /**
     * Sets the value of the deliveryTaskId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setDeliveryTaskId(String value) {
        this.deliveryTaskId = value;
    }

    /**
     * Gets the value of the deliveryStatus property.
     *
     */
    public int getDeliveryStatus() {
        return deliveryStatus;
    }

    /**
     * Sets the value of the deliveryStatus property.
     *
     */
    public void setDeliveryStatus(int value) {
        this.deliveryStatus = value;
    }

    /**
     * Gets the value of the statusUpdateTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getStatusUpdateTime() {
        return statusUpdateTime;
    }

    /**
     * Sets the value of the statusUpdateTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setStatusUpdateTime(Date value) {
        this.statusUpdateTime = value;
    }

    /**
     * Gets the value of the deliveryBoyName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getDeliveryBoyName() {
        return deliveryBoyName;
    }

    /**
     * Sets the value of the deliveryBoyName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setDeliveryBoyName(String value) {
        this.deliveryBoyName = value;
    }

    /**
     * Gets the value of the deliveryBoyPhoneNum property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getDeliveryBoyPhoneNum() {
        return deliveryBoyPhoneNum;
    }

    /**
     * Sets the value of the deliveryBoyPhoneNum property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setDeliveryBoyPhoneNum(String value) {
        this.deliveryBoyPhoneNum = value;
    }

    /**
     * Gets the value of the failureCode property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getFailureCode() {
        return failureCode;
    }

    /**
     * Sets the value of the failureCode property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setFailureCode(String value) {
        this.failureCode = value;
    }

    /**
     * Gets the value of the failureMessage property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getFailureMessage() {
        return failureMessage;
    }

    /**
     * Sets the value of the failureMessage property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setFailureMessage(String value) {
        this.failureMessage = value;
    }

	public Integer getDeliveryBoyId() {
		return deliveryBoyId;
	}

	public void setDeliveryBoyId(Integer deliveryBoyId) {
		this.deliveryBoyId = deliveryBoyId;
	}

	public Integer getDeliveryDetailId() {
		return deliveryDetailId;
	}

	public void setDeliveryDetailId(Integer deliveryDetailId) {
		this.deliveryDetailId = deliveryDetailId;
	}

	public String getAllotedNo() {
		return allotedNo;
	}

	public void setAllotedNo(String allotedNo) {
		this.allotedNo = allotedNo;
	}

	public boolean isByAssemblyScreen() {
		return byAssemblyScreen;
	}

	public void setByAssemblyScreen(boolean byAssemblyScreen) {
		this.byAssemblyScreen = byAssemblyScreen;
	}

	@Override
	public String toString() {
		return "DeliveryResponse [_id=" + _id + ", deliveryPartnerId=" + deliveryPartnerId + ", deliveryDetailId="
				+ deliveryDetailId + ", unitId=" + unitId + ", orderId=" + orderId + ", generatedOrderId="
				+ generatedOrderId + ", deliveryTaskId=" + deliveryTaskId + ", deliveryStatus=" + deliveryStatus
				+ ", statusUpdateTime=" + statusUpdateTime + ", deliveryBoyName=" + deliveryBoyName
				+ ", deliveryBoyPhoneNum=" + deliveryBoyPhoneNum + ", deliveryBoyId=" + deliveryBoyId + ", failureCode="
				+ failureCode + ", failureMessage=" + failureMessage + " allotedNo=" + allotedNo + " byAssemblyScreen  "
				+ byAssemblyScreen + "]";
	}


}
