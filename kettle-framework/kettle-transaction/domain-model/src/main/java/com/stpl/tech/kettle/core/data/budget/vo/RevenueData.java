/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class RevenueData {

	private Integer ticket = 0;
	private BigDecimal revenue = new BigDecimal(0d);
	private BigDecimal apc = new BigDecimal(0d);
	private BigDecimal gmv = new BigDecimal(0d);
	private BigDecimal discount = new BigDecimal(0d);
	private BigDecimal discountLoyalty = new BigDecimal(0d);
	private BigDecimal discountMarketing = new BigDecimal(0d);
	private BigDecimal discountOps = new BigDecimal(0d);
	private BigDecimal discountBd = new BigDecimal(0d);
	private BigDecimal discountEmployeeFico = new BigDecimal(0d);
	
	private Integer dineInTicket = 0;
	private BigDecimal dineInSales = new BigDecimal(0d);
	private BigDecimal dineInApc = new BigDecimal(0d);
	private BigDecimal dineInGmv = new BigDecimal(0d);
	private BigDecimal dineInDiscount = new BigDecimal(0d);
	private BigDecimal dineInDiscountLoyalty = new BigDecimal(0d);
	private BigDecimal dineInDiscountMarketing = new BigDecimal(0d);
	private BigDecimal dineInDiscountOps = new BigDecimal(0d);
	private BigDecimal dineInDiscountBd = new BigDecimal(0d);
	private BigDecimal dineInDiscountEmployeeFico = new BigDecimal(0d);

	private Integer deliveryTicket = 0;
	private BigDecimal deliverySales = new BigDecimal(0d);
	private BigDecimal deliveryApc = new BigDecimal(0d);
	private BigDecimal deliveryGmv = new BigDecimal(0d);
	private BigDecimal deliveryDiscount = new BigDecimal(0d);
	private BigDecimal deliveryDiscountLoyalty = new BigDecimal(0d);
	private BigDecimal deliveryDiscountMarketing = new BigDecimal(0d);
	private BigDecimal deliveryDiscountOps = new BigDecimal(0d);
	private BigDecimal deliveryDiscountBd = new BigDecimal(0d);
	private BigDecimal deliveryDiscountEmployeeFico = new BigDecimal(0d);

	private BigDecimal giftCardSale = new BigDecimal(0d);
	private BigDecimal giftCardNetSale = new BigDecimal(0d);
	private BigDecimal giftCardRedemption = new BigDecimal(0d);
	private BigDecimal giftCardDiscount= new BigDecimal(0d);

	private BigDecimal netSales = new BigDecimal(0d);
	private BigDecimal deliveryGiftCardSale = new BigDecimal(0d);
	private BigDecimal deliveryGiftCardRedemption = new BigDecimal(0d);
	private BigDecimal dineInGiftCardSale = new BigDecimal(0d);
	private BigDecimal dineInGiftCardRedemption = new BigDecimal(0d);
	private BigDecimal dineInGiftCardNetSale = new BigDecimal(0d);
	private BigDecimal deliveryGiftCardNetSale = new BigDecimal(0d);
	private BigDecimal dineInNetSales = new BigDecimal(0d);
	private BigDecimal deliveryNetSales = new BigDecimal(0d);

	private Integer employeeMealTicket = 0;
	private BigDecimal employeeMealSales = new BigDecimal(0d);
	private BigDecimal employeeMealGmv = new BigDecimal(0d);
	private BigDecimal netRevenue = new BigDecimal(0d);

	private BigDecimal empDiscountLoyalty = new BigDecimal(0d);
	private BigDecimal empDiscountMarketing = new BigDecimal(0d);
	private BigDecimal empDiscountOps = new BigDecimal(0d);
	private BigDecimal empDiscountBd = new BigDecimal(0d);
	private BigDecimal empDiscountEmployeeFico = new BigDecimal(0d);
	
	
	public Integer getTicket() {
		return ticket;
	}

	public void setTicket(Integer ticket) {
		this.ticket = ticket;
	}

	public BigDecimal getRevenue() {
		return revenue;
	}

	public void setRevenue(BigDecimal sales) {
		this.revenue = sales;
	}

	public BigDecimal getApc() {
		return apc;
	}

	public void setApc(BigDecimal apc) {
		this.apc = apc;
	}

	public BigDecimal getGmv() {
		return gmv;
	}

	public void setGmv(BigDecimal gmv) {
		this.gmv = gmv;
	}

	public BigDecimal getDiscount() {
		return discount;
	}

	public void setDiscount(BigDecimal discount) {
		this.discount = discount;
	}
	
	

	public BigDecimal getDiscountLoyalty() {
		return discountLoyalty;
	}

	public void setDiscountLoyalty(BigDecimal discountLoyalty) {
		this.discountLoyalty = discountLoyalty;
	}

	public BigDecimal getDiscountMarketing() {
		return discountMarketing;
	}

	public void setDiscountMarketing(BigDecimal discountMarketing) {
		this.discountMarketing = discountMarketing;
	}

	public BigDecimal getDiscountOps() {
		return discountOps;
	}

	public void setDiscountOps(BigDecimal discountOps) {
		this.discountOps = discountOps;
	}

	public BigDecimal getDiscountBd() {
		return discountBd;
	}

	public void setDiscountBd(BigDecimal discountBd) {
		this.discountBd = discountBd;
	}

	public BigDecimal getDiscountEmployeeFico() {
		return discountEmployeeFico;
	}

	public void setDiscountEmployeeFico(BigDecimal discountEmployeeFico) {
		this.discountEmployeeFico = discountEmployeeFico;
	}

	public Integer getDineInTicket() {
		return dineInTicket;
	}

	public void setDineInTicket(Integer dineInTicket) {
		this.dineInTicket = dineInTicket;
	}

	public BigDecimal getDineInSales() {
		return dineInSales;
	}

	public void setDineInSales(BigDecimal dineInSales) {
		this.dineInSales = dineInSales;
	}

	public BigDecimal getDineInApc() {
		return dineInApc;
	}

	public void setDineInApc(BigDecimal dineInApc) {
		this.dineInApc = dineInApc;
	}

	public BigDecimal getDineInGmv() {
		return dineInGmv;
	}

	public void setDineInGmv(BigDecimal dineInGmv) {
		this.dineInGmv = dineInGmv;
	}

	public BigDecimal getDineInDiscount() {
		return dineInDiscount;
	}

	public void setDineInDiscount(BigDecimal dineInDiscount) {
		this.dineInDiscount = dineInDiscount;
	}


	public BigDecimal getDineInDiscountLoyalty() {
		return dineInDiscountLoyalty;
	}

	public void setDineInDiscountLoyalty(BigDecimal dineInDiscountLoyalty) {
		this.dineInDiscountLoyalty = dineInDiscountLoyalty;
	}

	public BigDecimal getDineInDiscountMarketing() {
		return dineInDiscountMarketing;
	}

	public void setDineInDiscountMarketing(BigDecimal dineInDiscountMarketing) {
		this.dineInDiscountMarketing = dineInDiscountMarketing;
	}

	public BigDecimal getDineInDiscountOps() {
		return dineInDiscountOps;
	}

	public void setDineInDiscountOps(BigDecimal dineInDiscountOps) {
		this.dineInDiscountOps = dineInDiscountOps;
	}

	public BigDecimal getDineInDiscountBd() {
		return dineInDiscountBd;
	}

	public void setDineInDiscountBd(BigDecimal dineInDiscountBd) {
		this.dineInDiscountBd = dineInDiscountBd;
	}

	public BigDecimal getDineInDiscountEmployeeFico() {
		return dineInDiscountEmployeeFico;
	}

	public void setDineInDiscountEmployeeFico(BigDecimal dineInDiscountEmployeeFico) {
		this.dineInDiscountEmployeeFico = dineInDiscountEmployeeFico;
	}

	public Integer getDeliveryTicket() {
		return deliveryTicket;
	}

	public void setDeliveryTicket(Integer deliveryTicket) {
		this.deliveryTicket = deliveryTicket;
	}

	public BigDecimal getDeliverySales() {
		return deliverySales;
	}

	public void setDeliverySales(BigDecimal deliverySales) {
		this.deliverySales = deliverySales;
	}

	public BigDecimal getDeliveryApc() {
		return deliveryApc;
	}

	public void setDeliveryApc(BigDecimal deliveryApc) {
		this.deliveryApc = deliveryApc;
	}

	public BigDecimal getDeliveryGmv() {
		return deliveryGmv;
	}

	public void setDeliveryGmv(BigDecimal deliveryGmv) {
		this.deliveryGmv = deliveryGmv;
	}

	public BigDecimal getDeliveryDiscount() {
		return deliveryDiscount;
	}

	public void setDeliveryDiscount(BigDecimal deliveryDiscount) {
		this.deliveryDiscount = deliveryDiscount;
	}


	public BigDecimal getDeliveryDiscountLoyalty() {
		return deliveryDiscountLoyalty;
	}

	public void setDeliveryDiscountLoyalty(BigDecimal deliveryDiscountLoyalty) {
		this.deliveryDiscountLoyalty = deliveryDiscountLoyalty;
	}

	public BigDecimal getDeliveryDiscountMarketing() {
		return deliveryDiscountMarketing;
	}

	public void setDeliveryDiscountMarketing(BigDecimal deliveryDiscountMarketing) {
		this.deliveryDiscountMarketing = deliveryDiscountMarketing;
	}

	public BigDecimal getDeliveryDiscountOps() {
		return deliveryDiscountOps;
	}

	public void setDeliveryDiscountOps(BigDecimal deliveryDiscountOps) {
		this.deliveryDiscountOps = deliveryDiscountOps;
	}

	public BigDecimal getDeliveryDiscountBd() {
		return deliveryDiscountBd;
	}

	public void setDeliveryDiscountBd(BigDecimal deliveryDiscountBd) {
		this.deliveryDiscountBd = deliveryDiscountBd;
	}

	public BigDecimal getDeliveryDiscountEmployeeFico() {
		return deliveryDiscountEmployeeFico;
	}

	public void setDeliveryDiscountEmployeeFico(BigDecimal deliveryDiscountEmployeeFico) {
		this.deliveryDiscountEmployeeFico = deliveryDiscountEmployeeFico;
	}

	public BigDecimal getGiftCardSale() {
		return giftCardSale;
	}

	public void setGiftCardSale(BigDecimal giftCardSale) {
		this.giftCardSale = giftCardSale;
	}

	public BigDecimal getGiftCardRedemption() {
		return giftCardRedemption;
	}

	public void setGiftCardRedemption(BigDecimal giftCardRedemption) {
		this.giftCardRedemption = giftCardRedemption;
	}

	public Integer getEmployeeMealTicket() {
		return employeeMealTicket;
	}

	public void setEmployeeMealTicket(Integer employeeMealTicket) {
		this.employeeMealTicket = employeeMealTicket;
	}

	public BigDecimal getEmployeeMealSales() {
		return employeeMealSales;
	}

	public void setEmployeeMealSales(BigDecimal employeeMealSales) {
		this.employeeMealSales = employeeMealSales;
	}
	
	public BigDecimal getEmployeeMealGmv() {
		return employeeMealGmv;
	}

	public void setEmployeeMealGmv(BigDecimal employeeMealGmv) {
		this.employeeMealGmv = employeeMealGmv;
	}

	public BigDecimal getGiftCardNetSale() {
		return giftCardNetSale;
	}

	public void setGiftCardNetSale(BigDecimal giftCardNetSale) {
		this.giftCardNetSale = giftCardNetSale;
	}

	public BigDecimal getNetSales() {
		return netSales;
	}

	public void setNetSales(BigDecimal netSales) {
		this.netSales = netSales;
	}

	public BigDecimal getDineInGiftCardNetSale() {
		return dineInGiftCardNetSale;
	}

	public void setDineInGiftCardNetSale(BigDecimal dineInGiftCardNetSale) {
		this.dineInGiftCardNetSale = dineInGiftCardNetSale;
	}

	public BigDecimal getDeliveryGiftCardNetSale() {
		return deliveryGiftCardNetSale;
	}

	public void setDeliveryGiftCardNetSale(BigDecimal deliveryGiftCardNetSale) {
		this.deliveryGiftCardNetSale = deliveryGiftCardNetSale;
	}

	public BigDecimal getDineInNetSales() {
		return dineInNetSales;
	}

	public void setDineInNetSales(BigDecimal dineInNetSales) {
		this.dineInNetSales = dineInNetSales;
	}

	public BigDecimal getDeliveryNetSales() {
		return deliveryNetSales;
	}

	public void setDeliveryNetSales(BigDecimal deliveryNetSales) {
		this.deliveryNetSales = deliveryNetSales;
	}

	public BigDecimal getDeliveryGiftCardSale() {
		return deliveryGiftCardSale;
	}

	public void setDeliveryGiftCardSale(BigDecimal deliveryGiftCardSale) {
		this.deliveryGiftCardSale = deliveryGiftCardSale;
	}

	public BigDecimal getDeliveryGiftCardRedemption() {
		return deliveryGiftCardRedemption;
	}

	public void setDeliveryGiftCardRedemption(BigDecimal deliveryGiftCardRedemption) {
		this.deliveryGiftCardRedemption = deliveryGiftCardRedemption;
	}

	public BigDecimal getDineInGiftCardSale() {
		return dineInGiftCardSale;
	}

	public void setDineInGiftCardSale(BigDecimal dineInGiftCardSale) {
		this.dineInGiftCardSale = dineInGiftCardSale;
	}

	public BigDecimal getDineInGiftCardRedemption() {
		return dineInGiftCardRedemption;
	}

	public void setDineInGiftCardRedemption(BigDecimal dineInGiftCardRedemption) {
		this.dineInGiftCardRedemption = dineInGiftCardRedemption;
	}

	public BigDecimal getNetRevenue() {
		return netRevenue;
	}

	public void setNetRevenue(BigDecimal netSales) {
		this.netRevenue = netSales;
	}

	public BigDecimal getEmpDiscountLoyalty() {
		return empDiscountLoyalty;
	}

	public void setEmpDiscountLoyalty(BigDecimal empDiscountLoyalty) {
		this.empDiscountLoyalty = empDiscountLoyalty;
	}

	public BigDecimal getEmpDiscountMarketing() {
		return empDiscountMarketing;
	}

	public void setEmpDiscountMarketing(BigDecimal empDiscountMarketing) {
		this.empDiscountMarketing = empDiscountMarketing;
	}

	public BigDecimal getEmpDiscountOps() {
		return empDiscountOps;
	}

	public void setEmpDiscountOps(BigDecimal empDiscountOps) {
		this.empDiscountOps = empDiscountOps;
	}

	public BigDecimal getEmpDiscountBd() {
		return empDiscountBd;
	}

	public void setEmpDiscountBd(BigDecimal empDiscountBd) {
		this.empDiscountBd = empDiscountBd;
	}

	public BigDecimal getEmpDiscountEmployeeFico() {
		return empDiscountEmployeeFico;
	}

	public void setEmpDiscountEmployeeFico(BigDecimal empDiscountEmployeeFico) {
		this.empDiscountEmployeeFico = empDiscountEmployeeFico;
	}

	public BigDecimal getGiftCardDiscount() {
		return giftCardDiscount;
	}

	public void setGiftCardDiscount(BigDecimal giftCardDiscount) {
		this.giftCardDiscount = giftCardDiscount;
	}

	
	
}

