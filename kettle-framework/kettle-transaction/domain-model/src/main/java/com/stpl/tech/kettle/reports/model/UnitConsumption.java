/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.model;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

public class UnitConsumption extends AbstractConsumption implements ConsumptionData {

	private int billCount;

    private Map<String,Integer> billCountByCategory;

	private Map<String,Integer> billCountBySubCategory;

	private int complimentaryBillCount;

	private BigDecimal grossAmount = new BigDecimal(0.0d);

	private Map<Integer, CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>> items = new TreeMap<Integer, CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>>();

/*	private Map<String, SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<AddonConsumption>>>> addonItems = new TreeMap<String, SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<AddonConsumption>>>>();
*/
	/**
	 * @param name
	 */
	public UnitConsumption(String name) {
		super(name);
	}

	/**
	 * @return the items
	 */
	public Map<Integer, CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>> getItems() {
		return items;
	}

	/**
	 * @param items
	 *            the items to set
	 */
	public void setItems(
			Map<Integer, CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>> items) {
		this.items = items;
	}

	/**
	 * @return the items
	 */
	public Collection<CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>> getAllItems() {
		return items.values();
	}

	/**
	 * @return the addonItems
	 *//*
	public Map<String, SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<AddonConsumption>>>> getAddonItems() {
		return addonItems;
	}*/

	/**
	 * @param addonItems
	 *            the items to set
	 *//*
	public void setAddonItems(
			Map<String, SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<AddonConsumption>>>> addonItems) {
		this.addonItems = addonItems;
	}

	*//**
	 * @return the addonItems
	 *//*
	public Collection<SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<AddonConsumption>>>> getAllAddonItems() {
		return addonItems.values();
	}
*/
	/**
	 * @return the billCount
	 */
	public int getBillCount() {
		return billCount;
	}

	/**
	 * @param billCount
	 *            the billCount to set
	 */
	public void setBillCount(int billCount) {
		this.billCount = billCount;
	}

	/**
	 * @return the grossAmount
	 */
	public BigDecimal getGrossAmount() {
		return grossAmount;
	}

	/**
	 * @param grossAmount
	 *            the grossAmount to set
	 */
	public void setGrossAmount(BigDecimal grossAmount) {
		this.grossAmount = grossAmount;
	}

	public int getComplimentaryBillCount() {
		return complimentaryBillCount;
	}

	public void setComplimentaryBillCount(int complimentaryBillCount) {
		this.complimentaryBillCount = complimentaryBillCount;
	}


    public Map<String, Integer> getBillCountByCategory() {
        return billCountByCategory;
    }

    public void setBillCountByCategory(Map<String, Integer> billCountByCategory) {
        this.billCountByCategory = billCountByCategory;
    }

    public Map<String, Integer> getBillCountBySubCategory() {
        return billCountBySubCategory;
    }

    public void setBillCountBySubCategory(Map<String, Integer> billCountBySubCategory) {
        this.billCountBySubCategory = billCountBySubCategory;
    }
}
