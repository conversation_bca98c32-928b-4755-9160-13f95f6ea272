//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.10 at 07:11:28 PM IST
//

package com.stpl.tech.analytics.model;

import com.stpl.tech.master.domain.model.Adapter2;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * Java class for NumericData complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="NumericData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="min" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="avg" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="max" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="current" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="minDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="maxDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "NumericData", propOrder = { "min", "avg", "max", "current", "minDate", "maxDate" })
@Document
public class NumericData  implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 2674827930167388952L;
	@Id
	private String _id;

	/*@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;
	*/
	@Field
	protected int min = 0;
	@Field
	protected int avg = 0;
	@Field
	protected int max = 0;
	@Field
	protected int current = 0;
	@Field
	protected int target = 0;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date minDate;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date maxDate;
	@XmlElement(required = true)
	@Field
	protected boolean trendUpward = false;


	/**
	 * @return the _id
	 */
	public String get_id() {
		return _id;
	}

	/**
	 * @param _id the _id to set
	 */
	public void set_id(String _id) {
		this._id = _id;
	}
/*

	*/
/**
	 * @return the version
	 *//*

	public Long getVersion() {
		return version;
	}

	*/
/**
	 * @param version the version to set
	 *//*

	public void setVersion(Long version) {
		this.version = version;
	}

	*/
/**
	 * @return the detachAll
	 *//*

	public String getDetachAll() {
		return detachAll;
	}

	*/
/**
	 * @param detachAll the detachAll to set
	 *//*

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}
*/


	/**
	 * Gets the value of the min property.
	 *
	 */
	public int getMin() {
		return min;
	}

	/**
	 * Sets the value of the min property.
	 *
	 */
	public void setMin(int value) {
		this.min = value;
	}

	/**
	 * Gets the value of the avg property.
	 *
	 */
	public int getAvg() {
		return avg;
	}

	/**
	 * Sets the value of the avg property.
	 *
	 */
	public void setAvg(int value) {
		this.avg = value;
	}

	/**
	 * Gets the value of the max property.
	 *
	 */
	public int getMax() {
		return max;
	}

	/**
	 * Sets the value of the max property.
	 *
	 */
	public void setMax(int value) {
		this.max = value;
	}

	/**
	 * Gets the value of the current property.
	 *
	 */
	public int getCurrent() {
		return current;
	}

	/**
	 * Sets the value of the current property.
	 *
	 */
	public void setCurrent(int value) {
		this.current = value;
	}

	/**
	 * Gets the value of the minDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getMinDate() {
		return minDate;
	}

	/**
	 * Sets the value of the minDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setMinDate(Date value) {
		this.minDate = value;
	}

	/**
	 * Gets the value of the maxDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getMaxDate() {
		return maxDate;
	}

	/**
	 * Sets the value of the maxDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setMaxDate(Date value) {
		this.maxDate = value;
	}

	public void updateCurrent(int value) {
		current = current + value;
	}

	public boolean isTrendUpward() {
		return trendUpward;
	}

	public void setTrendUpward(boolean trendUpward) {
		this.trendUpward = trendUpward;
	}

	/**
	 * @return the target
	 */
	public int getTarget() {
		return target;
	}

	/**
	 * @param target the target to set
	 */
	public void setTarget(int target) {
		this.target = target;
	}

	@Override
	public String toString() {
		return "NumericData [min=" + min + ", avg=" + avg + ", max=" + max + ", current=" + current + ", target="
				+ target + ", minDate=" + minDate + ", maxDate=" + maxDate + ", trendUpward=" + trendUpward + "]";
	}


}
