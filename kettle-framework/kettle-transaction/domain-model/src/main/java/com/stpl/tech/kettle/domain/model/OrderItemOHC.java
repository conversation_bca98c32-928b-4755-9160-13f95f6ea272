package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class OrderItemOHC {

	private boolean isInventory;
	private String code;
	private String desc;
	private String productCategory;
	private BigDecimal mrp;
	private BigDecimal unitSellPrice;
	private BigDecimal units;
	private BigDecimal discountUnits;
	private BigDecimal saleAmt;
	private BigDecimal taxableAmt;
	private BigDecimal netItemAmt;
	private BigDecimal netDiscAmt;
	private BigDecimal netTaxAmt;
	private boolean taxInclusive;
	private BigDecimal lineItemRoundOff;
	private List<TaxesOHC> taxes = new ArrayList<>();

	public boolean isInventory() {
		return isInventory;
	}

	public void setInventory(boolean isInventory) {
		this.isInventory = isInventory;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getProductCategory() {
		return productCategory;
	}

	public void setProductCategory(String productCategory) {
		this.productCategory = productCategory;
	}

	public BigDecimal getMrp() {
		return mrp;
	}

	public void setMrp(BigDecimal mrp) {
		this.mrp = mrp;
	}

	public BigDecimal getUnitSellPrice() {
		return unitSellPrice;
	}

	public void setUnitSellPrice(BigDecimal unitSellPrice) {
		this.unitSellPrice = unitSellPrice;
	}

	public BigDecimal getUnits() {
		return units;
	}

	public void setUnits(BigDecimal units) {
		this.units = units;
	}

	public BigDecimal getDiscountUnits() {
		return discountUnits;
	}

	public void setDiscountUnits(BigDecimal discountUnits) {
		this.discountUnits = discountUnits;
	}

	public BigDecimal getSaleAmt() {
		return saleAmt;
	}

	public void setSaleAmt(BigDecimal saleAmt) {
		this.saleAmt = saleAmt;
	}

	public BigDecimal getTaxableAmt() {
		return taxableAmt;
	}

	public void setTaxableAmt(BigDecimal taxableAmt) {
		this.taxableAmt = taxableAmt;
	}

	public BigDecimal getNetItemAmt() {
		return netItemAmt;
	}

	public void setNetItemAmt(BigDecimal netItemAmt) {
		this.netItemAmt = netItemAmt;
	}

	public BigDecimal getNetDiscAmt() {
		return netDiscAmt;
	}

	public void setNetDiscAmt(BigDecimal netDiscAmt) {
		this.netDiscAmt = netDiscAmt;
	}

	public BigDecimal getNetTaxAmt() {
		return netTaxAmt;
	}

	public void setNetTaxAmt(BigDecimal netTaxAmt) {
		this.netTaxAmt = netTaxAmt;
	}

	public boolean isTaxInclusive() {
		return taxInclusive;
	}

	public void setTaxInclusive(boolean taxInclusive) {
		this.taxInclusive = taxInclusive;
	}

	public BigDecimal getLineItemRoundOff() {
		return lineItemRoundOff;
	}

	public void setLineItemRoundOff(BigDecimal lineItemRoundOff) {
		this.lineItemRoundOff = lineItemRoundOff;
	}

	public List<TaxesOHC> getTaxes() {
		return taxes;
	}

	public void setTaxes(List<TaxesOHC> taxes) {
		this.taxes = taxes;
	}

}
