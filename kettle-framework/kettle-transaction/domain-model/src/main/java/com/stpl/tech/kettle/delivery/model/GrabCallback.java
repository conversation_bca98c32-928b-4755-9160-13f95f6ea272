/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.09.05 at 12:05:55 PM IST 
//


package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="orderStatus" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="grabOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="clientOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="merchantBillNo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="merchantId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="riderName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="riderPhone" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="riderLatitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="riderLongitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="expectedDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="dttm" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "orderStatus",
    "grabOrderId",
    "clientOrderId",
    "merchantBillNo",
    "merchantId",
    "riderName",
    "riderPhone",
    "riderLatitude",
    "riderLongitude",
    "expectedDeliveryTime",
    "dttm"
})
@XmlRootElement(name = "GrabCallback")
public class GrabCallback implements CallbackObject{

    @XmlElement(required = true)
    protected String orderStatus;
    @XmlElement(required = true)
    protected String grabOrderId;
    @XmlElement(required = true)
    protected String clientOrderId;
    @XmlElement(required = true)
    protected String merchantBillNo;
    @XmlElement(required = true)
    protected String merchantId;
    @XmlElement(required = true)
    protected String riderName;
    @XmlElement(required = true)
    protected String riderPhone;
    @XmlElement(required = true)
    protected String riderLatitude;
    @XmlElement(required = true)
    protected String riderLongitude;
    @XmlElement(required = true)
    protected String expectedDeliveryTime;
    @XmlElement(required = true)
    protected String dttm;

    /**
     * Gets the value of the orderStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderStatus() {
        return orderStatus;
    }

    /**
     * Sets the value of the orderStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderStatus(String value) {
        this.orderStatus = value;
    }

    /**
     * Gets the value of the grabOrderId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGrabOrderId() {
        return grabOrderId;
    }

    /**
     * Sets the value of the grabOrderId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGrabOrderId(String value) {
        this.grabOrderId = value;
    }

    /**
     * Gets the value of the clientOrderId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClientOrderId() {
        return clientOrderId;
    }

    /**
     * Sets the value of the clientOrderId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClientOrderId(String value) {
        this.clientOrderId = value;
    }

    /**
     * Gets the value of the merchantBillNo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMerchantBillNo() {
        return merchantBillNo;
    }

    /**
     * Sets the value of the merchantBillNo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMerchantBillNo(String value) {
        this.merchantBillNo = value;
    }

    /**
     * Gets the value of the merchantId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * Sets the value of the merchantId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMerchantId(String value) {
        this.merchantId = value;
    }

    /**
     * Gets the value of the riderName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRiderName() {
        return riderName;
    }

    /**
     * Sets the value of the riderName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRiderName(String value) {
        this.riderName = value;
    }

    /**
     * Gets the value of the riderPhone property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRiderPhone() {
        return riderPhone;
    }

    /**
     * Sets the value of the riderPhone property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRiderPhone(String value) {
        this.riderPhone = value;
    }

    /**
     * Gets the value of the riderLatitude property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRiderLatitude() {
        return riderLatitude;
    }

    /**
     * Sets the value of the riderLatitude property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRiderLatitude(String value) {
        this.riderLatitude = value;
    }

    /**
     * Gets the value of the riderLongitude property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRiderLongitude() {
        return riderLongitude;
    }

    /**
     * Sets the value of the riderLongitude property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRiderLongitude(String value) {
        this.riderLongitude = value;
    }

    /**
     * Gets the value of the expectedDeliveryTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExpectedDeliveryTime() {
        return expectedDeliveryTime;
    }

    /**
     * Sets the value of the expectedDeliveryTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExpectedDeliveryTime(String value) {
        this.expectedDeliveryTime = value;
    }

    /**
     * Gets the value of the dttm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDttm() {
        return dttm;
    }

    /**
     * Sets the value of the dttm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDttm(String value) {
        this.dttm = value;
    }

}
