/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.master.domain.model.UnitCategory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionRequest {

	private int orderId;

	private String generatedOrderId;

	private int approvedBy;

	private String reason;

	private OrderStatus orderStatus;

	private UnitCategory unitCategory;

	private Integer unitId;

	private Integer channelPartner;

	private UnitCategory orderSource;

	private Boolean refund;
	
	private Integer reasonId;
	
	private String wastageType;
	
	private Boolean noTimeConstraint;

	public Boolean isRefund() {
		return refund;
	}

	public void setRefund(Boolean refund) {
		this.refund = refund;
	}

	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	public int getApprovedBy() {
		return approvedBy;
	}

	public void setApprovedBy(int approvedBy) {
		this.approvedBy = approvedBy;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public OrderStatus getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(OrderStatus orderStatus) {
		this.orderStatus = orderStatus;
	}

	public UnitCategory getUnitCategory() {
		return unitCategory;
	}

	public void setUnitCategory(UnitCategory unitCategory) {
		this.unitCategory = unitCategory;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public String getGeneratedOrderId() {
		return generatedOrderId;
	}

	public void setGeneratedOrderId(String generatedOrderId) {
		this.generatedOrderId = generatedOrderId;
	}

	public UnitCategory getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(UnitCategory orderSource) {
		this.orderSource = orderSource;
	}

	/**
	 * @return the channelPartner
	 */
	public Integer getChannelPartner() {
		return channelPartner;
	}

	/**
	 * @param channelPartner the channelPartner to set
	 */
	public void setChannelPartner(Integer channelPartner) {
		this.channelPartner = channelPartner;
	}

	public Integer getReasonId() {
		return reasonId;
	}

	public void setReasonId(Integer reasonId) {
		this.reasonId = reasonId;
	}

	public String getWastageType() {
		return wastageType;
	}

	public void setBookWastage(String wastageType) {
		this.wastageType = wastageType;
	}

	public Boolean getRefund() {
		return refund;
	}

	public Boolean getNoTimeConstraint() {
		return noTimeConstraint;
	}

	public void setNoTimeConstraint(Boolean noTimeConstraint) {
		this.noTimeConstraint = noTimeConstraint;
	}
	
}
