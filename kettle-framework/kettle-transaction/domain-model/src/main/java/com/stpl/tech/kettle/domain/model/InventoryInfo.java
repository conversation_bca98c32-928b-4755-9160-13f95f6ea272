/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.21 at 05:44:11 PM IST 
//

package com.stpl.tech.kettle.domain.model;

import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonInclude;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "InvenoryInfo", propOrder = { "id", "quantity" })
public class InventoryInfo {

	protected int id;
	protected int quantity;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	protected String name;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	protected Integer ex;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	protected Map<String, Integer> dim;

	public InventoryInfo() {
	}

	public InventoryInfo(int id, int quantity) {
		super();
		this.id = id;
		this.quantity = quantity;
	}

	public InventoryInfo(int id, int quantity, int ex) {
		super();
		this.id = id;
		this.quantity = quantity;
		this.ex = ex;
	}

	public InventoryInfo(int id, String name, int quantity, int expireQuantity) {
		super();
		this.id = id;
		this.quantity = quantity;
		this.ex = expireQuantity;
		this.name = name;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getEx() {
		return ex;
	}

	public void setEx(Integer ex) {
		this.ex = ex;
	}

	public Map<String, Integer> getDim() {
		return dim;
	}

	public void setDim(Map<String, Integer> dim) {
		this.dim = dim;
	}

}
