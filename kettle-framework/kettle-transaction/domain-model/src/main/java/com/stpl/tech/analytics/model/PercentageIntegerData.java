/**
 * 
 */
package com.stpl.tech.analytics.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter1;

/**
 * <AUTHOR>
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PercentageIntegerData", propOrder = { "percentage", "above", "below", "beyond" })
public class PercentageIntegerData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -190857796214549102L;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	private BigDecimal percentage;
	private int above;
	private int below;
	private int beyond;

	public BigDecimal getPercentage() {
		return percentage;
	}

	public void setPercentage(BigDecimal percentage) {
		this.percentage = percentage;
	}

	public int getAbove() {
		return above;
	}

	public void setAbove(int above) {
		this.above = above;
	}

	public int getBelow() {
		return below;
	}

	public void setBelow(int below) {
		this.below = below;
	}

	public int getBeyond() {
		return beyond;
	}

	public void setBeyond(int beyond) {
		this.beyond = beyond;
	}

}
