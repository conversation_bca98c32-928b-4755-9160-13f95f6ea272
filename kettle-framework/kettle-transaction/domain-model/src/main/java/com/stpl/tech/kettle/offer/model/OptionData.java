package com.stpl.tech.kettle.offer.model;

public class OptionData implements Comparable<OptionData> {

	private Integer unitId;
	private RuleData rule;
	private Option option;
	private int optionResultDataId;
	private int countWithDiscount;
	private int countWithoutDiscount;
	private boolean lastDiscounted;

	public OptionData() {
		super();
	}

	public OptionData(Integer unitId, RuleData rule, Option option) {
		super();
		this.unitId = unitId;
		this.rule = rule;
		this.option = option;
	}

	/**
	 * @return the unitId
	 */
	public Integer getUnitId() {
		return unitId;
	}

	/**
	 * @param unitId the unitId to set
	 */
	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public RuleData getRule() {
		return rule;
	}

	public void setRule(RuleData rule) {
		this.rule = rule;
	}

	public Option getOption() {
		return option;
	}

	public void setOption(Option option) {
		this.option = option;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((option == null) ? 0 : option.hashCode());
		result = prime * result + ((rule == null) ? 0 : rule.hashCode());
		result = prime * result + ((unitId == null) ? 0 : unitId.hashCode());
		return result;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		OptionData other = (OptionData) obj;
		if (option == null) {
			if (other.option != null)
				return false;
		} else if (!option.equals(other.option))
			return false;
		if (rule != other.rule)
			return false;
		if (unitId == null) {
			if (other.unitId != null)
				return false;
		} else if (!unitId.equals(other.unitId))
			return false;
		return true;
	}

	public int getCountWithDiscount() {
		return countWithDiscount;
	}

	public void setCountWithDiscount(int countWithDiscount) {
		this.countWithDiscount = countWithDiscount;
	}

	public int getCountWithoutDiscount() {
		return countWithoutDiscount;
	}

	public void setCountWithoutDiscount(int countWithoutDiscount) {
		this.countWithoutDiscount = countWithoutDiscount;
	}

	public boolean isLastDiscounted() {
		return lastDiscounted;
	}

	public void setLastDiscounted(boolean lastDiscounted) {
		this.lastDiscounted = lastDiscounted;
	}

	public int getOptionResultDataId() {
		return optionResultDataId;
	}

	public void setOptionResultDataId(int optionResultDataId) {
		this.optionResultDataId = optionResultDataId;
	}

	@Override
	public int compareTo(OptionData o) {
		return (this.countWithDiscount + this.countWithoutDiscount) - (o.countWithDiscount + o.countWithoutDiscount);
	}

}
