package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {

})
public class CustomerWalletEntryList {

   List<CustomerWalletEntry> list;

    public CustomerWalletEntryList() {
    }

    public CustomerWalletEntryList(List<CustomerWalletEntry> list) {
        this.list = list;
    }

    public List<CustomerWalletEntry> getList() {
        return list;
    }

    public void setList(List<CustomerWalletEntry> list) {
        this.list = list;
    }
}
