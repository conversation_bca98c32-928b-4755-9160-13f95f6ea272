package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {

})
public class CustomerCashPacketLogList {

    List<CustomerCashPacketLog> list;

    List<CustomerCashPacketLog> expList;

    public CustomerCashPacketLogList() {
    }

    public CustomerCashPacketLogList(List<CustomerCashPacketLog> list) {
        this.list = list;
    }


    public CustomerCashPacketLogList(List<CustomerCashPacketLog> list, List<CustomerCashPacketLog> expList) {
        this.list = list;
        this.expList = expList;
    }

    public List<CustomerCashPacketLog> getList() {
        return list;
    }

    public void setList(List<CustomerCashPacketLog> list) {
        this.list = list;
    }

    public List<CustomerCashPacketLog> getExpList() {
        return expList;
    }

    public void setExpList(List<CustomerCashPacketLog> expList) {
        this.expList = expList;
    }
}
