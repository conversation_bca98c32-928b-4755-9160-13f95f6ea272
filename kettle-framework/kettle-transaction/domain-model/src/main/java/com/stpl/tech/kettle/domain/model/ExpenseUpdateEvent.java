/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.04.10 at 02:53:07 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter2;


/**
 * <p>Java class for ExpenseUpdateEvent complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ExpenseUpdateEvent"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="eventId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="addedByUserId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="addedByUserName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="updatedByUserId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="updatedByUserName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="eventTimestamp" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}IterationType"/&gt;
 *         &lt;element name="year" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="iterationNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="noOfRows" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="startOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="endOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="inputFileName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="storedFileName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="errorMessage" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="expenses" type="{http://www.w3schools.com}UnitExpense" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExpenseUpdateEvent", propOrder = {
    "eventId",
    "addedByUserId",
    "addedByUserName",
    "updatedByUserId",
    "updatedByUserName",
    "eventTimestamp",
    "description",
    "type",
    "year",
    "iterationNumber",
    "status",
    "noOfRows",
    "startOrderId",
    "endOrderId",
    "inputFileName",
    "storedFileName",
    "errorMessage",
    "expenses"
})
public class ExpenseUpdateEvent {

    protected int eventId;
    protected int addedByUserId;
    @XmlElement(required = true)
    protected String addedByUserName;
    protected int updatedByUserId;
    @XmlElement(required = true)
    protected String updatedByUserName;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date eventTimestamp;
    @XmlElement(required = true)
    protected String description;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected IterationType type;
    protected int year;
    protected int iterationNumber;
    @XmlElement(required = true)
    protected String status;
    protected int noOfRows;
    protected int startOrderId;
    protected int endOrderId;
    @XmlElement(required = true)
    protected String inputFileName;
    @XmlElement(required = true)
    protected String storedFileName;
    @XmlElement(required = true, nillable = true)
    protected String errorMessage;
    @XmlElement(nillable = true)
    protected List<UnitExpense> expenses;
    protected Date startDate;
    protected Date endDate;

    /**
     * Gets the value of the eventId property.
     * 
     */
    public int getEventId() {
        return eventId;
    }

    /**
     * Sets the value of the eventId property.
     * 
     */
    public void setEventId(int value) {
        this.eventId = value;
    }

    /**
     * Gets the value of the addedByUserId property.
     * 
     */
    public int getAddedByUserId() {
        return addedByUserId;
    }

    /**
     * Sets the value of the addedByUserId property.
     * 
     */
    public void setAddedByUserId(int value) {
        this.addedByUserId = value;
    }

    /**
     * Gets the value of the addedByUserName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddedByUserName() {
        return addedByUserName;
    }

    /**
     * Sets the value of the addedByUserName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddedByUserName(String value) {
        this.addedByUserName = value;
    }

    /**
     * Gets the value of the updatedByUserId property.
     * 
     */
    public int getUpdatedByUserId() {
        return updatedByUserId;
    }

    /**
     * Sets the value of the updatedByUserId property.
     * 
     */
    public void setUpdatedByUserId(int value) {
        this.updatedByUserId = value;
    }

    /**
     * Gets the value of the updatedByUserName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUpdatedByUserName() {
        return updatedByUserName;
    }

    /**
     * Sets the value of the updatedByUserName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdatedByUserName(String value) {
        this.updatedByUserName = value;
    }

    /**
     * Gets the value of the eventTimestamp property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getEventTimestamp() {
        return eventTimestamp;
    }

    /**
     * Sets the value of the eventTimestamp property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEventTimestamp(Date value) {
        this.eventTimestamp = value;
    }

    /**
     * Gets the value of the description property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the value of the description property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescription(String value) {
        this.description = value;
    }

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link IterationType }
     *     
     */
    public IterationType getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link IterationType }
     *     
     */
    public void setType(IterationType value) {
        this.type = value;
    }

    /**
     * Gets the value of the year property.
     * 
     */
    public int getYear() {
        return year;
    }

    /**
     * Sets the value of the year property.
     * 
     */
    public void setYear(int value) {
        this.year = value;
    }

    /**
     * Gets the value of the iterationNumber property.
     * 
     */
    public int getIterationNumber() {
        return iterationNumber;
    }

    /**
     * Sets the value of the iterationNumber property.
     * 
     */
    public void setIterationNumber(int value) {
        this.iterationNumber = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the noOfRows property.
     * 
     */
    public int getNoOfRows() {
        return noOfRows;
    }

    /**
     * Sets the value of the noOfRows property.
     * 
     */
    public void setNoOfRows(int value) {
        this.noOfRows = value;
    }

    /**
     * Gets the value of the startOrderId property.
     * 
     */
    public int getStartOrderId() {
        return startOrderId;
    }

    /**
     * Sets the value of the startOrderId property.
     * 
     */
    public void setStartOrderId(int value) {
        this.startOrderId = value;
    }

    /**
     * Gets the value of the endOrderId property.
     * 
     */
    public int getEndOrderId() {
        return endOrderId;
    }

    /**
     * Sets the value of the endOrderId property.
     * 
     */
    public void setEndOrderId(int value) {
        this.endOrderId = value;
    }

    /**
     * Gets the value of the inputFileName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInputFileName() {
        return inputFileName;
    }

    /**
     * Sets the value of the inputFileName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInputFileName(String value) {
        this.inputFileName = value;
    }

    /**
     * Gets the value of the storedFileName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStoredFileName() {
        return storedFileName;
    }

    /**
     * Sets the value of the storedFileName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStoredFileName(String value) {
        this.storedFileName = value;
    }

    /**
     * Gets the value of the errorMessage property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * Sets the value of the errorMessage property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setErrorMessage(String value) {
        this.errorMessage = value;
    }

    /**
     * Gets the value of the expenses property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the expenses property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getExpenses().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link UnitExpense }
     * 
     * 
     */
    public List<UnitExpense> getExpenses() {
        if (expenses == null) {
            expenses = new ArrayList<UnitExpense>();
        }
        return this.expenses;
    }

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

    
}
