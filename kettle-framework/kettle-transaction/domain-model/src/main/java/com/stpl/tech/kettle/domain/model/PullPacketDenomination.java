/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.02.21 at 10:38:24 AM IST 
//


package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;


/**
 * <p>Java class for PullPacketDenomination complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PullPacketDenomination"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="pullDenominationId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="packetCount" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="looseCurrencyCount" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="denominationDetail" type="{http://www.w3schools.com}DenominationDetail"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PullPacketDenomination", propOrder = {
    "pullDenominationId",
    "packetCount",
    "looseCurrencyCount",
    "totalAmount",
    "denominationDetail"
})
public class PullPacketDenomination {

    protected int pullDenominationId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer packetCount;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer looseCurrencyCount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal totalAmount;
    @XmlElement(required = true)
    protected DenominationDetail denominationDetail;

    /**
     * Gets the value of the pullDenominationId property.
     * 
     */
    public int getPullDenominationId() {
        return pullDenominationId;
    }

    /**
     * Sets the value of the pullDenominationId property.
     * 
     */
    public void setPullDenominationId(int value) {
        this.pullDenominationId = value;
    }

    /**
     * Gets the value of the packetCount property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPacketCount() {
        return packetCount;
    }

    /**
     * Sets the value of the packetCount property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPacketCount(Integer value) {
        this.packetCount = value;
    }

    /**
     * Gets the value of the looseCurrencyCount property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getLooseCurrencyCount() {
        return looseCurrencyCount;
    }

    /**
     * Sets the value of the looseCurrencyCount property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setLooseCurrencyCount(Integer value) {
        this.looseCurrencyCount = value;
    }

    /**
     * Gets the value of the totalAmount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * Sets the value of the totalAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTotalAmount(BigDecimal value) {
        this.totalAmount = value;
    }

    /**
     * Gets the value of the denominationDetail property.
     * 
     * @return
     *     possible object is
     *     {@link DenominationDetail }
     *     
     */
    public DenominationDetail getDenominationDetail() {
        return denominationDetail;
    }

    /**
     * Sets the value of the denominationDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link DenominationDetail }
     *     
     */
    public void setDenominationDetail(DenominationDetail value) {
        this.denominationDetail = value;
    }

}
