package com.stpl.tech.kettle.domain.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@Builder(toBuilder = true)
public class OrderItemCancellationRequest {

    private Integer tableRequestId;

    private Integer orderId;

    private String generatedOrderId;

    private Integer orderItemId;

    private Boolean wastage;

    private String cancellationReason;

    private Integer cancelledBy;

    private Boolean serviceChargeItem;

}
