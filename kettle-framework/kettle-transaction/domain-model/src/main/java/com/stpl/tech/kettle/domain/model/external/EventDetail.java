//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.08.22 at 07:47:15 PM IST 
//


package com.stpl.tech.kettle.domain.model.external;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="event_id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="event_type" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="form_response" type="{http://www.w3schools.com}FormResponse"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "eventId",
    "eventType",
    "formResponse"
})
@XmlRootElement(name = "EventDetail")
public class EventDetail {

    @XmlElement(name = "event_id", required = true)
    @JsonProperty("event_id")
    protected String eventId;
    @XmlElement(name = "event_type", required = true)
    @JsonProperty("event_type")
    protected String eventType;
    @XmlElement(name = "form_response", required = true)
    @JsonProperty("form_response")
    protected FormResponse formResponse;

    /**
     * Gets the value of the eventId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEventId() {
        return eventId;
    }

    /**
     * Sets the value of the eventId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEventId(String value) {
        this.eventId = value;
    }

    /**
     * Gets the value of the eventType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEventType() {
        return eventType;
    }

    /**
     * Sets the value of the eventType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEventType(String value) {
        this.eventType = value;
    }

    /**
     * Gets the value of the formResponse property.
     * 
     * @return
     *     possible object is
     *     {@link FormResponse }
     *     
     */
    public FormResponse getFormResponse() {
        return formResponse;
    }

    /**
     * Sets the value of the formResponse property.
     * 
     * @param value
     *     allowed object is
     *     {@link FormResponse }
     *     
     */
    public void setFormResponse(FormResponse value) {
        this.formResponse = value;
    }

}
