package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

public class OrderComplaintResponse implements Serializable {

    private String code;

    private String status;

    public OrderComplaintResponse() {
    }

    public OrderComplaintResponse(String code, String status) {
        this.code = code;
        this.status = status;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
