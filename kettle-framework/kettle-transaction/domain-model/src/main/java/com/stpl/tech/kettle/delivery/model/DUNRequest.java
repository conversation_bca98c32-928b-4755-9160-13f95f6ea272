package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="request_id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pickup_details" type="{http://www.w3.org/2001/XMLSchema}DUNAddressDetails"/&gt;
 *         &lt;element name="drop_details" type="{http://www.w3.org/2001/XMLSchema}DUNAddressDetails"/&gt;
 *         &lt;element name="sender_details" type="{http://www.w3schools.com}DUNUserDetails"/&gt;
 *         &lt;element name="receiver_details" type="{http://www.w3schools.com}DUNUserDetails"/&gt;
 *         &lt;element name="package_content" type="{http://www.w3schools.com}package_content" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DUNRequest", propOrder = {
    "request_id",
    "pickup_details",
    "drop_details",
    "sender_details",
    "receiver_details",
    "package_content"
})
@XmlRootElement(name = "DUNRequest")
public class DUNRequest implements DUNGenericRequest {

	@XmlElement(name = "request_id", required = true)
	@JsonProperty("request_id")
	protected String  request_id;
	
	@XmlElement(name = "pickup_details", required = true)
	@JsonProperty("pickup_details")
    protected DUNAddressDetails pickup_details;
	
	
	@XmlElement(name = "drop_details", required = true)
	@JsonProperty("drop_details")
	protected DUNAddressDetails drop_details;
	
	@XmlElement(name = "sender_details", required = true)
	@JsonProperty("sender_details")
	protected DUNUserDetails sender_details;
	
	@XmlElement(name = "receiver_details", required = true)
	@JsonProperty("receiver_details")
	protected DUNUserDetails receiver_details;
	
	@XmlElement(name = "package_content", required = true)
	@JsonProperty("package_content")
	protected String[] package_content;

	public String getRequest_id() {
		return request_id;
	}

	public void setRequest_id(String request_id) {
		this.request_id = request_id;
	}

	public DUNAddressDetails getPickup_details() {
		return pickup_details;
	}

	public void setPickup_details(DUNAddressDetails pickup_details) {
		this.pickup_details = pickup_details;
	}

	public DUNAddressDetails getDrop_details() {
		return drop_details;
	}

	public void setDrop_details(DUNAddressDetails drop_details) {
		this.drop_details = drop_details;
	}

	public DUNUserDetails getSender_details() {
		return sender_details;
	}

	public void setSender_details(DUNUserDetails sender_details) {
		this.sender_details = sender_details;
	}

	public DUNUserDetails getReceiver_details() {
		return receiver_details;
	}

	public void setReceiver_details(DUNUserDetails receiver_details) {
		this.receiver_details = receiver_details;
	}

	public String[] getPackage_content() {
		return package_content;
	}

	public void setPackage_content(String[] package_content) {
		this.package_content = package_content;
	}

}

