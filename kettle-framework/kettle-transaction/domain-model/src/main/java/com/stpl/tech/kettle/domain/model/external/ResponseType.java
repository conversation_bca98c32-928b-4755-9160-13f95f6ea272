//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.08.22 at 06:07:41 PM IST 
//


package com.stpl.tech.kettle.domain.model.external;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ResponseType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="ResponseType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="text"/&gt;
 *     &lt;enumeration value="choice"/&gt;
 *     &lt;enumeration value="number"/&gt;
 *     &lt;enumeration value="choices"/&gt;
 *     &lt;enumeration value="email"/&gt;
 *     &lt;enumeration value="url"/&gt;
 *     &lt;enumeration value="date"/&gt;
 *     &lt;enumeration value="file_url"/&gt;
 *     &lt;enumeration value="boolean"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "ResponseType")
@XmlEnum
public enum ResponseType {

    @XmlEnumValue("text")
    text("text"),
    @XmlEnumValue("choice")
    choice("choice"),
    @XmlEnumValue("number")
    number("number"),
    @XmlEnumValue("choices")
    choices("choices"),
    @XmlEnumValue("email")
    email("email"),
    @XmlEnumValue("url")
    url("url"),
    @XmlEnumValue("date")
    date("date"),
    @XmlEnumValue("boolean")
    bool("boolean"),
    @XmlEnumValue("file_url")
    file_url("file_url"),
    @XmlEnumValue("phone_number")
    phone_number("phone_number"),
    @XmlEnumValue("short_text")
    short_text("short_text");
    
    private final String value;

    ResponseType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static ResponseType fromValue(String v) {
        for (ResponseType c: ResponseType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
