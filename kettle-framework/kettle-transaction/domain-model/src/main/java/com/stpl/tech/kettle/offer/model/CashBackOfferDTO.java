package com.stpl.tech.kettle.offer.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CashBackOfferDTO implements Serializable {

    private static final long serialVersionUID = -8023634172775247835L;
    private Integer unitId;

    private List<Integer> unitIds;
    private int lagDays;
    private int validityInDays;

    private Date offerStartDate;
    private Date offerEndDate;

    private String offerScope;
    private Integer maxNumberOfOrder;
    private BigDecimal cashbackPercentage;
    private String offerStatus;
}
