/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.23 at 12:37:04 PM IST 
//

package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * Java class for SFXRider complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SFXRider"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="rider_name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="rider_phone" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="rider_location" type="{http://www.w3schools.com}SFXRiderLocation"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SFXRider", propOrder = { "rider_Name", "rider_Phone", "rider_Location" })
@JsonIgnoreProperties(ignoreUnknown=true)
public class SFXRider {

	@XmlElement(name = "rider_name", required = true)
	@JsonProperty("rider_name")
	protected String rider_Name;
	@XmlElement(name = "rider_phone", required = true)
	@JsonProperty("rider_phone")
	protected String rider_Phone;
	@XmlElement(name = "rider_location", required = true)
	@JsonProperty("rider_location")
	protected SFXRiderLocation rider_Location;

	/**
	 * Gets the value of the rider_Name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getRider_Name() {
		return rider_Name;
	}

	/**
	 * Sets the value of the rider_Name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setRider_Name(String value) {
		this.rider_Name = value;
	}

	/**
	 * Gets the value of the rider_Phone property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getRider_Phone() {
		return rider_Phone;
	}

	/**
	 * Sets the value of the rider_Phone property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setRider_Phone(String value) {
		this.rider_Phone = value;
	}

	/**
	 * Gets the value of the rider_Location property.
	 * 
	 * @return possible object is {@link SFXRiderLocation }
	 * 
	 */
	public SFXRiderLocation getRider_Location() {
		return rider_Location;
	}

	/**
	 * Sets the value of the rider_Location property.
	 * 
	 * @param value
	 *            allowed object is {@link SFXRiderLocation }
	 * 
	 */
	public void setRider_Location(SFXRiderLocation value) {
		this.rider_Location = value;
	}

}
