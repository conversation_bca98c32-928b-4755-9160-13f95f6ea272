/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.23 at 12:37:04 PM IST 
//

package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pilot_name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pilot_phone" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pilot_latitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pilot_longitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="order_code" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="eta" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="delivery_eta" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "id", "pilot_Name", "pilot_Phone", "pilot_Latitude", "pilot_Longitude", "order_Code",
		"state", "eta", "delivery_Eta" })
@XmlRootElement(name = "OPNResponse")
@JsonIgnoreProperties(ignoreUnknown = true)
public class OPNResponse implements DeliveryPartnerResponse {

	@XmlElement(required = true)
	@JsonProperty("id")
	protected String id;
	@XmlElement(name = "pilot_name", required = true)
	@JsonProperty("pilot_name")
	protected String pilot_Name;
	@XmlElement(name = "pilot_phone", required = true)
	@JsonProperty("pilot_phone")
	protected String pilot_Phone;
	@XmlElement(name = "pilot_latitude", required = true)
	@JsonProperty("pilot_latitude")
	protected String pilot_Latitude;
	@XmlElement(name = "pilot_longitude", required = true)
	@JsonProperty("pilot_longitude")
	protected String pilot_Longitude;
	@XmlElement(name = "order_code", required = true)
	@JsonProperty("order_code")
	protected String order_Code;
	@XmlElement(required = true)
	@JsonProperty("state")
	protected String state;
	@XmlElement(required = true)
	@JsonProperty("eta")
	protected String eta;
	@XmlElement(name = "delivery_eta", required = true)
	@JsonProperty("delivery_eta")
	protected String delivery_Eta;

	/**
	 * Gets the value of the id property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setId(String value) {
		this.id = value;
	}

	/**
	 * Gets the value of the pilot_Name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPilot_Name() {
		return pilot_Name;
	}

	/**
	 * Sets the value of the pilot_Name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPilot_Name(String value) {
		this.pilot_Name = value;
	}

	/**
	 * Gets the value of the pilot_Phone property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPilot_Phone() {
		return pilot_Phone;
	}

	/**
	 * Sets the value of the pilot_Phone property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPilot_Phone(String value) {
		this.pilot_Phone = value;
	}

	/**
	 * Gets the value of the pilot_Latitude property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPilot_Latitude() {
		return pilot_Latitude;
	}

	/**
	 * Sets the value of the pilot_Latitude property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPilot_Latitude(String value) {
		this.pilot_Latitude = value;
	}

	/**
	 * Gets the value of the pilot_Longitude property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPilot_Longitude() {
		return pilot_Longitude;
	}

	/**
	 * Sets the value of the pilot_Longitude property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPilot_Longitude(String value) {
		this.pilot_Longitude = value;
	}

	/**
	 * Gets the value of the order_Code property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOrder_Code() {
		return order_Code;
	}

	/**
	 * Sets the value of the order_Code property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOrder_Code(String value) {
		this.order_Code = value;
	}

	/**
	 * Gets the value of the state property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getState() {
		return state;
	}

	/**
	 * Sets the value of the state property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setState(String value) {
		this.state = value;
	}

	/**
	 * Gets the value of the eta property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEta() {
		return eta;
	}

	/**
	 * Sets the value of the eta property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEta(String value) {
		this.eta = value;
	}

	/**
	 * Gets the value of the delivery_Eta property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDelivery_Eta() {
		return delivery_Eta;
	}

	/**
	 * Sets the value of the delivery_Eta property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDelivery_Eta(String value) {
		this.delivery_Eta = value;
	}

}
