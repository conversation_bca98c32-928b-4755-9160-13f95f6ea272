//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

import com.stpl.tech.master.recipe.model.UnitOfMeasure;

@XmlType(name = "DeliveryReason")
@XmlEnum
public enum DeliveryReason {

	MissedCallResponse("Order delivered marked by SDP missed call response", 1), 
	PosReset("Order delivered marked by POS screen reset", 2 ), 
	AssemblyScreen("Order delivered marked by assembly screen", 3);

	private final String  reason;
	private final int reasonId;

	private DeliveryReason(String reason, int reasonId) {
		this.reason = reason;
		this.reasonId = reasonId;
	}

	public String getReason() {
		return reason;
	}

	public int getReasonId() {
		return reasonId;
	}

}
