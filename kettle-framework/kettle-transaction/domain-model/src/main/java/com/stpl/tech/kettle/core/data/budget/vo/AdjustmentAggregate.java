/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class AdjustmentAggregate {

	private BigDecimal revenueAdjustment;
	private BigDecimal costAdjustment;

	public BigDecimal getRevenueAdjustment() {
		return revenueAdjustment;
	}

	public void setRevenueAdjustment(BigDecimal propertyFixRent) {
		this.revenueAdjustment = propertyFixRent;
	}

	public BigDecimal getCostAdjustment() {
		return costAdjustment;
	}

	public void setCostAdjustment(BigDecimal revenueShare) {
		this.costAdjustment = revenueShare;
	}

}
