package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.Date;

public class CustomerEmailData {

	private Integer customerId;
	private Integer overallOrders;
	private Integer overallVisits;
	private BigDecimal overallSavings;
	private String availedSignupOffer;
	private Date signupOfferExpiryTime;
	private Integer acquiredLoyaltyPoints;
	private BigDecimal walletBalance;
	private BigDecimal chaayosCashBalance;
	private String membershipAvailable;
	private String membershipPlan;
	private Date membershipEndDate;

	public CustomerEmailData() {

	}

	public CustomerEmailData(Integer customerId, Integer overallOrders, Integer overallVisits,
			BigDecimal overallSavings, String availedSignupOffer, Date signupOfferExpiryTime,
			Integer acquiredLoyaltyPoints, BigDecimal walletBalance, BigDecimal chaayosCashBalance,
			String membershipAvailable, String membershipPlan, Date membershipEndDate) {
		super();
		this.customerId = customerId;
		this.overallOrders = overallOrders;
		this.overallVisits = overallVisits;
		this.overallSavings = overallSavings;
		this.availedSignupOffer = availedSignupOffer;
		this.signupOfferExpiryTime = signupOfferExpiryTime;
		this.acquiredLoyaltyPoints = acquiredLoyaltyPoints;
		this.walletBalance = walletBalance;
		this.chaayosCashBalance = chaayosCashBalance;
		this.membershipAvailable = membershipAvailable;
		this.membershipPlan = membershipPlan;
		this.membershipEndDate = membershipEndDate;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public Integer getOverallOrders() {
		return overallOrders;
	}

	public void setOverallOrders(Integer overallOrders) {
		this.overallOrders = overallOrders;
	}

	public Integer getOverallVisits() {
		return overallVisits;
	}

	public void setOverallVisits(Integer overallVisits) {
		this.overallVisits = overallVisits;
	}

	public BigDecimal getOverallSavings() {
		return overallSavings;
	}

	public void setOverallSavings(BigDecimal overallSavings) {
		this.overallSavings = overallSavings;
	}

	public String getAvailedSignupOffer() {
		return availedSignupOffer;
	}

	public void setAvailedSignupOffer(String availedSignupOffer) {
		this.availedSignupOffer = availedSignupOffer;
	}

	public Date getSignupOfferExpiryTime() {
		return signupOfferExpiryTime;
	}

	public void setSignupOfferExpiryTime(Date signupOfferExpiryTime) {
		this.signupOfferExpiryTime = signupOfferExpiryTime;
	}

	public Integer getAcquiredLoyaltyPoints() {
		return acquiredLoyaltyPoints;
	}

	public void setAcquiredLoyaltyPoints(Integer acquiredLoyaltyPoints) {
		this.acquiredLoyaltyPoints = acquiredLoyaltyPoints;
	}

	public BigDecimal getWalletBalance() {
		return walletBalance;
	}

	public void setWalletBalance(BigDecimal walletBalance) {
		this.walletBalance = walletBalance;
	}

	public BigDecimal getChaayosCashBalance() {
		return chaayosCashBalance;
	}

	public void setChaayosCashBalance(BigDecimal chaayosCashBalance) {
		this.chaayosCashBalance = chaayosCashBalance;
	}

	public String getMembershipAvailable() {
		return membershipAvailable;
	}

	public void setMembershipAvailable(String membershipAvailable) {
		this.membershipAvailable = membershipAvailable;
	}

	public String getMembershipPlan() {
		return membershipPlan;
	}

	public void setMembershipPlan(String membershipPlan) {
		this.membershipPlan = membershipPlan;
	}

	public Date getMembershipEndDate() {
		return membershipEndDate;
	}

	public void setMembershipEndDate(Date membershipEndDate) {
		this.membershipEndDate = membershipEndDate;
	}

	@Override
	public String toString() {
		return "CustomerEmailData [customerId=" + customerId + ", overallOrders=" + overallOrders + ", overallVisits="
				+ overallVisits + ", overallSavings=" + overallSavings + ", availedSignupOffer=" + availedSignupOffer
				+ ", signupOfferExpiryTime=" + signupOfferExpiryTime + ", acquiredLoyaltyPoints="
				+ acquiredLoyaltyPoints + ", walletBalance=" + walletBalance + ", chaayosCashBalance="
				+ chaayosCashBalance + ", membershipAvailable=" + membershipAvailable + ", membershipPlan="
				+ membershipPlan + ", membershipEndDate=" + membershipEndDate + "]";
	}

}
