package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.master.domain.model.Adapter1;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.Date;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {

})
public class CustomerLoyaltyEntry {

    protected String type;
    protected String unit;
    protected boolean added;
    protected String codeType;
    protected String code;
    protected String status;
    protected Integer points;
    private Date time;
    protected Integer total;
    protected String channelPartner;
    protected String senderName;
    protected Integer senderId;
    protected Integer eventId;
    protected String receiverName;
    protected String transferStatus;
    protected Integer senderBalance;
    protected Integer receiverBalance;
    protected String loyaltyEventStatus;

    protected Integer redeemedPoints;


    public CustomerLoyaltyEntry() {
    }

    public
    CustomerLoyaltyEntry(String type, String unit, boolean added, String codeType,
                                String code, String status, Integer points, Date time, Integer total,
                                String channelPartner, String senderName, Integer senderId, String receiverName,
                                String transferStatus,Integer eventId,Integer senderBalance,Integer receiverBalance,
                                String loyaltyEventStatus,Integer redeemedPoints) {
        this.type = type;
        this.unit = unit;
        this.added = added;
        this.codeType = codeType;
        this.code = code;
        this.status = status;
        this.points = points;
        this.time = time;
        this.total = total;
        this.channelPartner = channelPartner;
        this.senderName = senderName;
        this.senderId = senderId;
        this.receiverName = receiverName;
        this.transferStatus = transferStatus;
        this.eventId=eventId;
        this.senderBalance=senderBalance;
        this.receiverBalance=receiverBalance;
        this.loyaltyEventStatus=loyaltyEventStatus;
        this.redeemedPoints = redeemedPoints;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public boolean isAdded() {
        return added;
    }

    public void setAdded(boolean added) {
        this.added = added;
    }

    public String getCodeType() {
        return codeType;
    }

    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public String getChannelPartner() {
        return channelPartner;
    }

    public void setChannelPartner(String channelPartner) {
        this.channelPartner = channelPartner;
    }

    public String getSenderName() {
        return senderName;
    }


    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public Integer getSenderId() {
        return senderId;
    }

    public void setSenderId(Integer senderId) {
        this.senderId = senderId;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getTransferStatus() {
        return transferStatus;
    }

    public void setTransferStatus(String transferStatus) {
        this.transferStatus = transferStatus;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getSenderBalance() {
        return senderBalance;
    }

    public void setSenderBalance(Integer senderBalance) {
        this.senderBalance = senderBalance;
    }

    public Integer getReceiverBalance() {
        return receiverBalance;
    }

    public void setReceiverBalance(Integer receiverBalance) {
        this.receiverBalance = receiverBalance;
    }
    public String getLoyaltyEventStatus() {
        return loyaltyEventStatus;
    }

    public void setLoyaltyEventStatus(String loyaltyEventStatus) {
        this.loyaltyEventStatus = loyaltyEventStatus;
    }


    public Integer getRedeemedPoints() {
        return redeemedPoints;
    }

    public void setRedeemedPoints(Integer redeemedPoints) {
        this.redeemedPoints = redeemedPoints;
    }
}
