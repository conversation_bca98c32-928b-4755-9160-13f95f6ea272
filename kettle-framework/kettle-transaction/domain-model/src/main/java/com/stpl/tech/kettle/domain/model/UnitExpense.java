/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.08.08 at 01:41:47 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;


/**
 * <p>Java class for UnitExpense complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="UnitExpense"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="unitExpenseId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="year" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="iterationNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="expenseUpdateEventId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="updatedBy" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="lastUpdateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="manpower" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="netSalesAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="gmvAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalTickets" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="netTickets" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="cogs" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="consumablesAndUtilities" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="marketingAndSampling" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="unsatifiedCustomerCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="employeeMeal" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="wastageAndExpired" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="deliveryCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="electricity" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="water" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="rentDG" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="chargesDG" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="creditCardCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="amexCardCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="sodexoCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="tktRestaurantCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="channelPartnerCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="scmRental" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="edcMachine" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="freightOutward" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="convenyance" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="staffWelfare" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="changeCommission" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="courier" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="printingAndStationery" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="miscExp" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="parkingCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="cleaningCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="newspaper" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="rent" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="fixedRent" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="rentPercentage" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="camCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="internet" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="telephone" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="opsCostPercentage" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="opsCostTotal" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="kitchenCostPercentage" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="kitchenCostTotal" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="repairAndMaintenanceMinor" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="repairAndMaintenanceMajor" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="manualAdjustments" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="ebitdaPercentage" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="comments" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customerCareCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="maintenanceTeamCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="trainingTeamCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="itTeamCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="consumablesStationary" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="consumablesUniform" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="consumablesEquipment" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="consumablesCutlery" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="msp" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="systemRent" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="insurance" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="paytmCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="mobikwikCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="FreeChargeCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="deliveryTotalTickets" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="deliveryNetTickets" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="deliveryGMV" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="deliverySales" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="deliveryCOGS" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="deliveryUnsatisfiedCustomerCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="deliverySampleingAndMarketingCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="deliveryPaytmCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitExpense", propOrder = {
    "unitExpenseId",
    "unitId",
    "unitName",
    "type",
    "year",
    "iterationNumber",
    "expenseUpdateEventId",
    "updatedBy",
    "lastUpdateTime",
    "manpower",
    "netSalesAmount",
    "gmvAmount",
    "totalTickets",
    "netTickets",
    "cogs",
    "consumablesAndUtilities",
    "marketingAndSampling",
    "unsatifiedCustomerCost",
        "ppeCost",
    "employeeMeal",
    "wastageAndExpired",
    "deliveryCost",
    "electricity",
    "water",
    "rentDG",
    "chargesDG",
    "creditCardCharges",
    "amexCardCharges",
    "sodexoCharges",
    "tktRestaurantCharges",
    "channelPartnerCharges",
    "scmRental",
    "edcMachine",
    "freightOutward",
    "convenyance",
    "staffWelfare",
    "changeCommission",
    "courier",
    "printingAndStationery",
    "miscExp",
    "parkingCharges",
    "cleaningCharges",
    "newspaper",
    "rent",
    "fixedRent",
    "rentPercentage",
    "camCharges",
    "internet",
    "telephone",
    "opsCostPercentage",
    "opsCostTotal",
    "kitchenCostPercentage",
    "kitchenCostTotal",
    "repairAndMaintenanceMinor",
    "repairAndMaintenanceMajor",
    "manualAdjustments",
    "totalCost",
    "ebitdaPercentage",
    "comments",
    "customerCareCost",
    "maintenanceTeamCost",
    "trainingTeamCost",
    "itTeamCost",
    "consumablesStationary",
    "consumablesUniform",
    "consumablesEquipment",
    "consumablesCutlery",
    "msp",
    "systemRent",
    "insurance",
    "paytmCharges",
    "mobikwikCharges",
    "freeChargeCharges",
    "deliveryTotalTickets",
    "deliveryNetTickets",
    "deliveryGMV",
    "deliverySales",
    "deliveryCOGS",
    "deliveryUnsatisfiedCustomerCost",
        "deliveryPPECost",
    "deliverySampleingAndMarketingCost",
    "deliveryPaytmCharges"
})
public class UnitExpense {

    protected int unitExpenseId;
    protected int unitId;
    @XmlElement(required = true)
    protected String unitName;
    @XmlElement(required = true)
    protected String type;
    protected int year;
    protected int iterationNumber;
    protected int expenseUpdateEventId;
    @XmlElement(required = true)
    protected String updatedBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdateTime;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal manpower;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal netSalesAmount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal gmvAmount;
    protected int totalTickets;
    protected int netTickets;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal cogs;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal consumablesAndUtilities;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal marketingAndSampling;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal unsatifiedCustomerCost;
    @JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal ppeCost;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal employeeMeal;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal wastageAndExpired;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal deliveryCost;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal electricity;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal water;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal rentDG;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal chargesDG;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal creditCardCharges;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal amexCardCharges;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal sodexoCharges;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal tktRestaurantCharges;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal channelPartnerCharges;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal scmRental;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal edcMachine;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal freightOutward;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal convenyance;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal staffWelfare;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal changeCommission;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal courier;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal printingAndStationery;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal miscExp;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal parkingCharges;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
   @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal cleaningCharges;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal newspaper;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal rent;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal fixedRent;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal rentPercentage;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal camCharges;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal internet;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal telephone;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal opsCostPercentage;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal opsCostTotal;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal kitchenCostPercentage;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal kitchenCostTotal;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal repairAndMaintenanceMinor;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal repairAndMaintenanceMajor;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal manualAdjustments;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal totalCost;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal ebitdaPercentage;
    @XmlElement(required = true)
    protected String comments;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal customerCareCost;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal maintenanceTeamCost;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
   @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal trainingTeamCost;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal itTeamCost;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal consumablesStationary;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal consumablesUniform;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal consumablesEquipment;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal consumablesCutlery;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal msp;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal systemRent;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal insurance;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal paytmCharges;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal mobikwikCharges;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(name = "FreeChargeCharges", required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal freeChargeCharges;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal deliveryTotalTickets;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal deliveryNetTickets;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal deliveryGMV;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal deliverySales;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal deliveryCOGS;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal deliveryUnsatisfiedCustomerCost;
    @JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal deliveryPPECost;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal deliverySampleingAndMarketingCost;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal deliveryPaytmCharges;

    /**
     * Gets the value of the unitExpenseId property.
     * 
     */
    public int getUnitExpenseId() {
        return unitExpenseId;
    }

    /**
     * Sets the value of the unitExpenseId property.
     * 
     */
    public void setUnitExpenseId(int value) {
        this.unitExpenseId = value;
    }

    /**
     * Gets the value of the unitId property.
     * 
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     * 
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the unitName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnitName() {
        return unitName;
    }

    /**
     * Sets the value of the unitName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnitName(String value) {
        this.unitName = value;
    }

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the year property.
     * 
     */
    public int getYear() {
        return year;
    }

    /**
     * Sets the value of the year property.
     * 
     */
    public void setYear(int value) {
        this.year = value;
    }

    /**
     * Gets the value of the iterationNumber property.
     * 
     */
    public int getIterationNumber() {
        return iterationNumber;
    }

    /**
     * Sets the value of the iterationNumber property.
     * 
     */
    public void setIterationNumber(int value) {
        this.iterationNumber = value;
    }

    /**
     * Gets the value of the expenseUpdateEventId property.
     * 
     */
    public int getExpenseUpdateEventId() {
        return expenseUpdateEventId;
    }

    /**
     * Sets the value of the expenseUpdateEventId property.
     * 
     */
    public void setExpenseUpdateEventId(int value) {
        this.expenseUpdateEventId = value;
    }

    /**
     * Gets the value of the updatedBy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets the value of the updatedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdatedBy(String value) {
        this.updatedBy = value;
    }

    /**
     * Gets the value of the lastUpdateTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * Sets the value of the lastUpdateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastUpdateTime(Date value) {
        this.lastUpdateTime = value;
    }

    /**
     * Gets the value of the manpower property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getManpower() {
        return manpower;
    }

    /**
     * Sets the value of the manpower property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setManpower(BigDecimal value) {
        this.manpower = value;
    }

    /**
     * Gets the value of the netSalesAmount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getNetSalesAmount() {
        return netSalesAmount;
    }

    /**
     * Sets the value of the netSalesAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNetSalesAmount(BigDecimal value) {
        this.netSalesAmount = value;
    }

    /**
     * Gets the value of the gmvAmount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getGmvAmount() {
        return gmvAmount;
    }

    /**
     * Sets the value of the gmvAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGmvAmount(BigDecimal value) {
        this.gmvAmount = value;
    }

    /**
     * Gets the value of the totalTickets property.
     * 
     */
    public int getTotalTickets() {
        return totalTickets;
    }

    /**
     * Sets the value of the totalTickets property.
     * 
     */
    public void setTotalTickets(int value) {
        this.totalTickets = value;
    }

    /**
     * Gets the value of the netTickets property.
     * 
     */
    public int getNetTickets() {
        return netTickets;
    }

    /**
     * Sets the value of the netTickets property.
     * 
     */
    public void setNetTickets(int value) {
        this.netTickets = value;
    }

    /**
     * Gets the value of the cogs property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getCogs() {
        return cogs;
    }

    /**
     * Sets the value of the cogs property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCogs(BigDecimal value) {
        this.cogs = value;
    }

    /**
     * Gets the value of the consumablesAndUtilities property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getConsumablesAndUtilities() {
        return consumablesAndUtilities;
    }

    /**
     * Sets the value of the consumablesAndUtilities property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setConsumablesAndUtilities(BigDecimal value) {
        this.consumablesAndUtilities = value;
    }

    /**
     * Gets the value of the marketingAndSampling property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getMarketingAndSampling() {
        return marketingAndSampling;
    }

    /**
     * Sets the value of the marketingAndSampling property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMarketingAndSampling(BigDecimal value) {
        this.marketingAndSampling = value;
    }

    /**
     * Gets the value of the unsatifiedCustomerCost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getUnsatifiedCustomerCost() {
        return unsatifiedCustomerCost;
    }

    /**
     * Sets the value of the unsatifiedCustomerCost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnsatifiedCustomerCost(BigDecimal value) {
        this.unsatifiedCustomerCost = value;
    }

    public BigDecimal getPPECost() { return this.ppeCost; }

    public void setPPECost(BigDecimal value) {
        this.ppeCost = value;
    }

    /**
     * Gets the value of the employeeMeal property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getEmployeeMeal() {
        return employeeMeal;
    }

    /**
     * Sets the value of the employeeMeal property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmployeeMeal(BigDecimal value) {
        this.employeeMeal = value;
    }

    /**
     * Gets the value of the wastageAndExpired property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getWastageAndExpired() {
        return wastageAndExpired;
    }

    /**
     * Sets the value of the wastageAndExpired property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWastageAndExpired(BigDecimal value) {
        this.wastageAndExpired = value;
    }

    /**
     * Gets the value of the deliveryCost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getDeliveryCost() {
        return deliveryCost;
    }

    /**
     * Sets the value of the deliveryCost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliveryCost(BigDecimal value) {
        this.deliveryCost = value;
    }

    /**
     * Gets the value of the electricity property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getElectricity() {
        return electricity;
    }

    /**
     * Sets the value of the electricity property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setElectricity(BigDecimal value) {
        this.electricity = value;
    }

    /**
     * Gets the value of the water property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getWater() {
        return water;
    }

    /**
     * Sets the value of the water property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWater(BigDecimal value) {
        this.water = value;
    }

    /**
     * Gets the value of the rentDG property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getRentDG() {
        return rentDG;
    }

    /**
     * Sets the value of the rentDG property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRentDG(BigDecimal value) {
        this.rentDG = value;
    }

    /**
     * Gets the value of the chargesDG property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getChargesDG() {
        return chargesDG;
    }

    /**
     * Sets the value of the chargesDG property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChargesDG(BigDecimal value) {
        this.chargesDG = value;
    }

    /**
     * Gets the value of the creditCardCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getCreditCardCharges() {
        return creditCardCharges;
    }

    /**
     * Sets the value of the creditCardCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreditCardCharges(BigDecimal value) {
        this.creditCardCharges = value;
    }

    /**
     * Gets the value of the amexCardCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getAmexCardCharges() {
        return amexCardCharges;
    }

    /**
     * Sets the value of the amexCardCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAmexCardCharges(BigDecimal value) {
        this.amexCardCharges = value;
    }

    /**
     * Gets the value of the sodexoCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getSodexoCharges() {
        return sodexoCharges;
    }

    /**
     * Sets the value of the sodexoCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSodexoCharges(BigDecimal value) {
        this.sodexoCharges = value;
    }

    /**
     * Gets the value of the tktRestaurantCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getTktRestaurantCharges() {
        return tktRestaurantCharges;
    }

    /**
     * Sets the value of the tktRestaurantCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTktRestaurantCharges(BigDecimal value) {
        this.tktRestaurantCharges = value;
    }

    /**
     * Gets the value of the channelPartnerCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getChannelPartnerCharges() {
        return channelPartnerCharges;
    }

    /**
     * Sets the value of the channelPartnerCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChannelPartnerCharges(BigDecimal value) {
        this.channelPartnerCharges = value;
    }

    /**
     * Gets the value of the scmRental property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getScmRental() {
        return scmRental;
    }

    /**
     * Sets the value of the scmRental property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setScmRental(BigDecimal value) {
        this.scmRental = value;
    }

    /**
     * Gets the value of the edcMachine property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getEdcMachine() {
        return edcMachine;
    }

    /**
     * Sets the value of the edcMachine property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEdcMachine(BigDecimal value) {
        this.edcMachine = value;
    }

    /**
     * Gets the value of the freightOutward property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getFreightOutward() {
        return freightOutward;
    }

    /**
     * Sets the value of the freightOutward property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFreightOutward(BigDecimal value) {
        this.freightOutward = value;
    }

    /**
     * Gets the value of the convenyance property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getConvenyance() {
        return convenyance;
    }

    /**
     * Sets the value of the convenyance property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setConvenyance(BigDecimal value) {
        this.convenyance = value;
    }

    /**
     * Gets the value of the staffWelfare property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getStaffWelfare() {
        return staffWelfare;
    }

    /**
     * Sets the value of the staffWelfare property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStaffWelfare(BigDecimal value) {
        this.staffWelfare = value;
    }

    /**
     * Gets the value of the changeCommission property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getChangeCommission() {
        return changeCommission;
    }

    /**
     * Sets the value of the changeCommission property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChangeCommission(BigDecimal value) {
        this.changeCommission = value;
    }

    /**
     * Gets the value of the courier property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getCourier() {
        return courier;
    }

    /**
     * Sets the value of the courier property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCourier(BigDecimal value) {
        this.courier = value;
    }

    /**
     * Gets the value of the printingAndStationery property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getPrintingAndStationery() {
        return printingAndStationery;
    }

    /**
     * Sets the value of the printingAndStationery property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrintingAndStationery(BigDecimal value) {
        this.printingAndStationery = value;
    }

    /**
     * Gets the value of the miscExp property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getMiscExp() {
        return miscExp;
    }

    /**
     * Sets the value of the miscExp property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMiscExp(BigDecimal value) {
        this.miscExp = value;
    }

    /**
     * Gets the value of the parkingCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getParkingCharges() {
        return parkingCharges;
    }

    /**
     * Sets the value of the parkingCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setParkingCharges(BigDecimal value) {
        this.parkingCharges = value;
    }

    /**
     * Gets the value of the cleaningCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getCleaningCharges() {
        return cleaningCharges;
    }

    /**
     * Sets the value of the cleaningCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCleaningCharges(BigDecimal value) {
        this.cleaningCharges = value;
    }

    /**
     * Gets the value of the newspaper property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getNewspaper() {
        return newspaper;
    }

    /**
     * Sets the value of the newspaper property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNewspaper(BigDecimal value) {
        this.newspaper = value;
    }

    /**
     * Gets the value of the rent property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getRent() {
        return rent;
    }

    /**
     * Sets the value of the rent property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRent(BigDecimal value) {
        this.rent = value;
    }

    /**
     * Gets the value of the fixedRent property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getFixedRent() {
        return fixedRent;
    }

    /**
     * Sets the value of the fixedRent property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFixedRent(BigDecimal value) {
        this.fixedRent = value;
    }

    /**
     * Gets the value of the rentPercentage property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getRentPercentage() {
        return rentPercentage;
    }

    /**
     * Sets the value of the rentPercentage property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRentPercentage(BigDecimal value) {
        this.rentPercentage = value;
    }

    /**
     * Gets the value of the camCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getCamCharges() {
        return camCharges;
    }

    /**
     * Sets the value of the camCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCamCharges(BigDecimal value) {
        this.camCharges = value;
    }

    /**
     * Gets the value of the internet property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getInternet() {
        return internet;
    }

    /**
     * Sets the value of the internet property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInternet(BigDecimal value) {
        this.internet = value;
    }

    /**
     * Gets the value of the telephone property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getTelephone() {
        return telephone;
    }

    /**
     * Sets the value of the telephone property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTelephone(BigDecimal value) {
        this.telephone = value;
    }

    /**
     * Gets the value of the opsCostPercentage property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getOpsCostPercentage() {
        return opsCostPercentage;
    }

    /**
     * Sets the value of the opsCostPercentage property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOpsCostPercentage(BigDecimal value) {
        this.opsCostPercentage = value;
    }

    /**
     * Gets the value of the opsCostTotal property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getOpsCostTotal() {
        return opsCostTotal;
    }

    /**
     * Sets the value of the opsCostTotal property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOpsCostTotal(BigDecimal value) {
        this.opsCostTotal = value;
    }

    /**
     * Gets the value of the kitchenCostPercentage property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getKitchenCostPercentage() {
        return kitchenCostPercentage;
    }

    /**
     * Sets the value of the kitchenCostPercentage property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKitchenCostPercentage(BigDecimal value) {
        this.kitchenCostPercentage = value;
    }

    /**
     * Gets the value of the kitchenCostTotal property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getKitchenCostTotal() {
        return kitchenCostTotal;
    }

    /**
     * Sets the value of the kitchenCostTotal property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKitchenCostTotal(BigDecimal value) {
        this.kitchenCostTotal = value;
    }

    /**
     * Gets the value of the repairAndMaintenanceMinor property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getRepairAndMaintenanceMinor() {
        return repairAndMaintenanceMinor;
    }

    /**
     * Sets the value of the repairAndMaintenanceMinor property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRepairAndMaintenanceMinor(BigDecimal value) {
        this.repairAndMaintenanceMinor = value;
    }

    /**
     * Gets the value of the repairAndMaintenanceMajor property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getRepairAndMaintenanceMajor() {
        return repairAndMaintenanceMajor;
    }

    /**
     * Sets the value of the repairAndMaintenanceMajor property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRepairAndMaintenanceMajor(BigDecimal value) {
        this.repairAndMaintenanceMajor = value;
    }

    /**
     * Gets the value of the manualAdjustments property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getManualAdjustments() {
        return manualAdjustments;
    }

    /**
     * Sets the value of the manualAdjustments property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setManualAdjustments(BigDecimal value) {
        this.manualAdjustments = value;
    }

    /**
     * Gets the value of the totalCost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getTotalCost() {
        return totalCost;
    }

    /**
     * Sets the value of the totalCost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTotalCost(BigDecimal value) {
        this.totalCost = value;
    }

    /**
     * Gets the value of the ebitdaPercentage property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getEbitdaPercentage() {
        return ebitdaPercentage;
    }

    /**
     * Sets the value of the ebitdaPercentage property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEbitdaPercentage(BigDecimal value) {
        this.ebitdaPercentage = value;
    }

    /**
     * Gets the value of the comments property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComments() {
        return comments;
    }

    /**
     * Sets the value of the comments property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComments(String value) {
        this.comments = value;
    }

    /**
     * Gets the value of the customerCareCost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getCustomerCareCost() {
        return customerCareCost;
    }

    /**
     * Sets the value of the customerCareCost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerCareCost(BigDecimal value) {
        this.customerCareCost = value;
    }

    /**
     * Gets the value of the maintenanceTeamCost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getMaintenanceTeamCost() {
        return maintenanceTeamCost;
    }

    /**
     * Sets the value of the maintenanceTeamCost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMaintenanceTeamCost(BigDecimal value) {
        this.maintenanceTeamCost = value;
    }

    /**
     * Gets the value of the trainingTeamCost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getTrainingTeamCost() {
        return trainingTeamCost;
    }

    /**
     * Sets the value of the trainingTeamCost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTrainingTeamCost(BigDecimal value) {
        this.trainingTeamCost = value;
    }

    /**
     * Gets the value of the itTeamCost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getItTeamCost() {
        return itTeamCost;
    }

    /**
     * Sets the value of the itTeamCost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setItTeamCost(BigDecimal value) {
        this.itTeamCost = value;
    }

    /**
     * Gets the value of the consumablesStationary property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getConsumablesStationary() {
        return consumablesStationary;
    }

    /**
     * Sets the value of the consumablesStationary property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setConsumablesStationary(BigDecimal value) {
        this.consumablesStationary = value;
    }

    /**
     * Gets the value of the consumablesUniform property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getConsumablesUniform() {
        return consumablesUniform;
    }

    /**
     * Sets the value of the consumablesUniform property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setConsumablesUniform(BigDecimal value) {
        this.consumablesUniform = value;
    }

    /**
     * Gets the value of the consumablesEquipment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getConsumablesEquipment() {
        return consumablesEquipment;
    }

    /**
     * Sets the value of the consumablesEquipment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setConsumablesEquipment(BigDecimal value) {
        this.consumablesEquipment = value;
    }

    /**
     * Gets the value of the consumablesCutlery property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getConsumablesCutlery() {
        return consumablesCutlery;
    }

    /**
     * Sets the value of the consumablesCutlery property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setConsumablesCutlery(BigDecimal value) {
        this.consumablesCutlery = value;
    }

    /**
     * Gets the value of the msp property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getMsp() {
        return msp;
    }

    /**
     * Sets the value of the msp property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsp(BigDecimal value) {
        this.msp = value;
    }

    /**
     * Gets the value of the systemRent property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getSystemRent() {
        return systemRent;
    }

    /**
     * Sets the value of the systemRent property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSystemRent(BigDecimal value) {
        this.systemRent = value;
    }

    /**
     * Gets the value of the insurance property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getInsurance() {
        return insurance;
    }

    /**
     * Sets the value of the insurance property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInsurance(BigDecimal value) {
        this.insurance = value;
    }

    /**
     * Gets the value of the paytmCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getPaytmCharges() {
        return paytmCharges;
    }

    /**
     * Sets the value of the paytmCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPaytmCharges(BigDecimal value) {
        this.paytmCharges = value;
    }

    /**
     * Gets the value of the mobikwikCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getMobikwikCharges() {
        return mobikwikCharges;
    }

    /**
     * Sets the value of the mobikwikCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMobikwikCharges(BigDecimal value) {
        this.mobikwikCharges = value;
    }

    /**
     * Gets the value of the freeChargeCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getFreeChargeCharges() {
        return freeChargeCharges;
    }

    /**
     * Sets the value of the freeChargeCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFreeChargeCharges(BigDecimal value) {
        this.freeChargeCharges = value;
    }

    /**
     * Gets the value of the deliveryTotalTickets property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getDeliveryTotalTickets() {
        return deliveryTotalTickets;
    }

    /**
     * Sets the value of the deliveryTotalTickets property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliveryTotalTickets(BigDecimal value) {
        this.deliveryTotalTickets = value;
    }

    /**
     * Gets the value of the deliveryNetTickets property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getDeliveryNetTickets() {
        return deliveryNetTickets;
    }

    /**
     * Sets the value of the deliveryNetTickets property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliveryNetTickets(BigDecimal value) {
        this.deliveryNetTickets = value;
    }

    /**
     * Gets the value of the deliveryGMV property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getDeliveryGMV() {
        return deliveryGMV;
    }

    /**
     * Sets the value of the deliveryGMV property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliveryGMV(BigDecimal value) {
        this.deliveryGMV = value;
    }

    /**
     * Gets the value of the deliverySales property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getDeliverySales() {
        return deliverySales;
    }

    /**
     * Sets the value of the deliverySales property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliverySales(BigDecimal value) {
        this.deliverySales = value;
    }

    /**
     * Gets the value of the deliveryCOGS property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getDeliveryCOGS() {
        return deliveryCOGS;
    }

    /**
     * Sets the value of the deliveryCOGS property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliveryCOGS(BigDecimal value) {
        this.deliveryCOGS = value;
    }

    /**
     * Gets the value of the deliveryUnsatisfiedCustomerCost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getDeliveryUnsatisfiedCustomerCost() {
        return deliveryUnsatisfiedCustomerCost;
    }

    /**
     * Sets the value of the deliveryUnsatisfiedCustomerCost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliveryUnsatisfiedCustomerCost(BigDecimal value) {
        this.deliveryUnsatisfiedCustomerCost = value;
    }

    public BigDecimal getDeliveryPPECost() { return deliveryPPECost; }

    public void setDeliveryPPECost(BigDecimal value) { this.deliveryPPECost = value; }

    /**
     * Gets the value of the deliverySampleingAndMarketingCost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getDeliverySampleingAndMarketingCost() {
        return deliverySampleingAndMarketingCost;
    }

    /**
     * Sets the value of the deliverySampleingAndMarketingCost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliverySampleingAndMarketingCost(BigDecimal value) {
        this.deliverySampleingAndMarketingCost = value;
    }

    /**
     * Gets the value of the deliveryPaytmCharges property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getDeliveryPaytmCharges() {
        return deliveryPaytmCharges;
    }

    /**
     * Sets the value of the deliveryPaytmCharges property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliveryPaytmCharges(BigDecimal value) {
        this.deliveryPaytmCharges = value;
    }

}
