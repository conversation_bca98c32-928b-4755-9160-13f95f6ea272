/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.01.09 at 05:35:16 PM IST 
//

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * Java class for DiscountDetail complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="DiscountDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="discountCode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="discountReason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="discount" type="{http://www.w3schools.com}PercentageDetail"/&gt;
 *         &lt;element name="promotionalOffer" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalDiscount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DiscountDetail", propOrder = { "discountCode", "discountReason", "discount", "promotionalOffer",
		"totalDiscount" })
public class DiscountDetail implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -3624088464502553964L;

	@Id
	private String _id;

	/*
	@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;
	*/

	@XmlElement(required = true, type = Integer.class, nillable = true)
	@Field
	protected Integer discountCode;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String discountReason;
	@XmlElement(required = true, nillable = true)
	@Field
	protected PercentageDetail discount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal promotionalOffer;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal totalDiscount;

	@XmlElement(required = false, type = Integer.class, nillable = true)
	@Field
	protected Integer qtyDiscountApplied;


	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

/*	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}*/

	/**
	 * Gets the value of the discountCode property.
	 * 
	 * @return possible object is {@link Integer }
	 * 
	 */
	public Integer getDiscountCode() {
		return discountCode;
	}

	/**
	 * Sets the value of the discountCode property.
	 * 
	 * @param value
	 *            allowed object is {@link Integer }
	 * 
	 */
	public void setDiscountCode(Integer value) {
		this.discountCode = value;
	}

	/**
	 * Gets the value of the discountReason property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDiscountReason() {
		return discountReason;
	}

	/**
	 * Sets the value of the discountReason property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDiscountReason(String value) {
		this.discountReason = value;
	}

	/**
	 * Gets the value of the discount property.
	 * 
	 * @return possible object is {@link PercentageDetail }
	 * 
	 */
	public PercentageDetail getDiscount() {
		return discount;
	}

	/**
	 * Sets the value of the discount property.
	 * 
	 * @param value
	 *            allowed object is {@link PercentageDetail }
	 * 
	 */
	public void setDiscount(PercentageDetail value) {
		this.discount = value;
	}

	/**
	 * Gets the value of the promotionalOffer property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getPromotionalOffer() {
		return promotionalOffer;
	}

	/**
	 * Sets the value of the promotionalOffer property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setPromotionalOffer(BigDecimal value) {
		this.promotionalOffer = value;
	}

	/**
	 * Gets the value of the totalDiscount property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getTotalDiscount() {
		return totalDiscount;
	}

	/**
	 * Sets the value of the totalDiscount property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setTotalDiscount(BigDecimal value) {
		this.totalDiscount = value;
	}

	public Integer getQtyDiscountApplied() {
		return qtyDiscountApplied;
	}

	public void setQtyDiscountApplied(Integer qtyDiscountApplied) {
		this.qtyDiscountApplied = qtyDiscountApplied;
	}

}
