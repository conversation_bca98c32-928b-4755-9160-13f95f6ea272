package com.stpl.tech.kettle.domain.model;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class OrderDetailTrim implements Serializable {

    private static final long serialVersionUID = -2112404154890526140L;
    private String generatedOrderId;

    private int channelPartnerId;

    private String orderType;

    private Integer brandId;

    private Integer manualBillBookNo;

    private Integer tableNumber;

    private String orderSource;

    private Date billGenerationTime;

    private BigDecimal taxableAmount;

    private BigDecimal settledAmount;

    private String settlementType;

    private Integer pointsRedeemed;

    private List<Settlement> settlements;

    private long pointsAcquired;

    public String getGeneratedOrderId() {
        return generatedOrderId;
    }

    public void setGeneratedOrderId(String generatedOrderId) {
        this.generatedOrderId = generatedOrderId;
    }

    public int getChannelPartnerId() {
        return channelPartnerId;
    }

    public void setChannelPartnerId(int channelPartnerId) {
        this.channelPartnerId = channelPartnerId;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Integer getManualBillBookNo() {
        return manualBillBookNo;
    }

    public void setManualBillBookNo(Integer manualBillBookNo) {
        this.manualBillBookNo = manualBillBookNo;
    }

    public Integer getTableNumber() {
        return tableNumber;
    }

    public void setTableNumber(Integer tableNumber) {
        this.tableNumber = tableNumber;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public Date getBillGenerationTime() {
        return billGenerationTime;
    }

    public void setBillGenerationTime(Date billGenerationTime) {
        this.billGenerationTime = billGenerationTime;
    }

    public BigDecimal getTaxableAmount() {
        return taxableAmount;
    }

    public void setTaxableAmount(BigDecimal taxableAmount) {
        this.taxableAmount = taxableAmount;
    }

    public BigDecimal getSettledAmount() {
        return settledAmount;
    }

    public void setSettledAmount(BigDecimal settledAmount) {
        this.settledAmount = settledAmount;
    }

    public String getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }

    public Integer getPointsRedeemed() {
        return pointsRedeemed;
    }

    public void setPointsRedeemed(Integer pointsRedeemed) {
        this.pointsRedeemed = pointsRedeemed;
    }

    public List<Settlement> getSettlements() {
        return settlements;
    }

    public void setSettlements(List<Settlement> settlements) {
        this.settlements = settlements;
    }
    public long getPointsAcquired() {
        return pointsAcquired;
    }

    public void setPointsAcquired(long pointsAcquired) {
        this.pointsAcquired = pointsAcquired;
    }
}
