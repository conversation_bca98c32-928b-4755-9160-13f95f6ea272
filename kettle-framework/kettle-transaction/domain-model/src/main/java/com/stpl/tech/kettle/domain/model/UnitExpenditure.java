package com.stpl.tech.kettle.domain.model;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class UnitExpenditure {
	private int id;
	private String calculationType;
	private String category;
	private Object current;
	private Object value;
	private Object budget;
	private String label;
	private boolean split;
	private int order;
	private String restrictionType;
	private List<String> entitys = new ArrayList<>();

	@Override
	public String toString() {
		return "UnitExpenditure [id=" + id + ", calculationType=" + calculationType + ", category=" + category
				+ ", current=" + current + ", value=" + value + ", budget=" + budget + ", label=" + label + ", order=" + order + ", entitys="
				+ entitys + "]";
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getCalculationType() {
		return calculationType;
	}

	public void setCalculationType(String calculationType) {
		this.calculationType = calculationType;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public Object getValue() {
		return value;
	}

	public void setValue(Object value) {
		this.value = value;
	}

	public Object getBudget() {
		return budget;
	}

	public void setBudget(Object budget) {
		this.budget = budget;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public int getOrder() {
		return order;
	}

	public void setOrder(int order) {
		this.order = order;
	}

	@JsonIgnore
	public List<String> getEntitys() {
		return entitys;
	}

	public void setEntitys(List<String> entitys) {
		this.entitys = entitys;
	}

	public Object getCurrent() {
		return current;
	}

	public void setCurrent(Object current) {
		this.current = current;
	}

	@JsonIgnore
	public String getRestrictionType() {
		return restrictionType;
	}

	public void setRestrictionType(String restrictionType) {
		this.restrictionType = restrictionType;
	}

	public boolean isSplit() {
		return split;
	}

	public void setSplit(boolean split) {
		this.split = split;
	}

	
}