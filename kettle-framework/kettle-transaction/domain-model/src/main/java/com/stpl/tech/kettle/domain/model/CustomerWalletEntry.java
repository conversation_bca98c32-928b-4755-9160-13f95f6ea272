package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;
import java.util.Date;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {

})
public class CustomerWalletEntry {

    protected String type;

    protected BigDecimal amount;

    protected Date time;

    protected BigDecimal offerAmount;

    protected BigDecimal balance;

    protected Integer orderId;

    public CustomerWalletEntry() {
    }

    public CustomerWalletEntry(String type, BigDecimal amount, Date time, BigDecimal offerAmount, BigDecimal balance, Integer purchaseOrderId) {
        this.type = type;
        this.amount = amount;
        this.time = time;
        this.offerAmount = offerAmount;
        this.balance = balance;
        this.orderId = purchaseOrderId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public BigDecimal getOfferAmount() {
        return offerAmount;
    }

    public void setOfferAmount(BigDecimal offerAmount) {
        this.offerAmount = offerAmount;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }
}
