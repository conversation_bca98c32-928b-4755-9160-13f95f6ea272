//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.08.22 at 07:47:15 PM IST 
//


package com.stpl.tech.kettle.domain.model.external;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * <p>Java class for FormResponseData complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="FormResponseData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}ResponseType"/&gt;
 *         &lt;element name="text" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="choice" type="{http://www.w3schools.com}ChoiceData"/&gt;
 *         &lt;element name="choices" type="{http://www.w3schools.com}MultipleChoiceData"/&gt;
 *         &lt;element name="number" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="field" type="{http://www.w3schools.com}FormField"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FormResponseData", propOrder = {
    "type",
    "text",
    "choice",
    "choices",
    "number",
    "field"
})
public class FormResponseData {

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected String type;
    @XmlElement(required = true)
    protected String text;
    @XmlElement(required = true)
    protected ChoiceData choice;
    @XmlElement(required = true)
    protected MultipleChoiceData choices;
    @XmlElement(required = false)
    protected Integer number;
    @XmlElement(required = true)
    protected FormField field;
    @XmlElement(required = true)
    protected String email;
    @XmlElement(required = true)
    protected String url;
    @XmlElement(required = true)
    @JsonProperty("file_url")
    protected String fileUrl;
    @XmlElement(required = true)
    @JsonProperty("phone_number")
    protected String phoneNumber;
    @XmlElement(required = true)
    @JsonProperty("boolean")
    protected Boolean booleanData;

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link ResponseType }
     *     
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link ResponseType }
     *     
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the text property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getText() {
        return text;
    }

    /**
     * Sets the value of the text property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setText(String value) {
        this.text = value;
    }

    /**
     * Gets the value of the choice property.
     * 
     * @return
     *     possible object is
     *     {@link ChoiceData }
     *     
     */
    public ChoiceData getChoice() {
        return choice;
    }

    /**
     * Sets the value of the choice property.
     * 
     * @param value
     *     allowed object is
     *     {@link ChoiceData }
     *     
     */
    public void setChoice(ChoiceData value) {
        this.choice = value;
    }

    /**
     * Gets the value of the choices property.
     * 
     * @return
     *     possible object is
     *     {@link MultipleChoiceData }
     *     
     */
    public MultipleChoiceData getChoices() {
        return choices;
    }

    /**
     * Sets the value of the choices property.
     * 
     * @param value
     *     allowed object is
     *     {@link MultipleChoiceData }
     *     
     */
    public void setChoices(MultipleChoiceData value) {
        this.choices = value;
    }

    /**
     * Gets the value of the number property.
     * 
     */
    public Integer getNumber() {
        return number;
    }

    /**
     * Sets the value of the number property.
     * 
     */
    public void setNumber(Integer value) {
        this.number = value;
    }

    /**
     * Gets the value of the field property.
     * 
     * @return
     *     possible object is
     *     {@link FormField }
     *     
     */
    public FormField getField() {
        return field;
    }

    /**
     * Sets the value of the field property.
     * 
     * @param value
     *     allowed object is
     *     {@link FormField }
     *     
     */
    public void setField(FormField value) {
        this.field = value;
    }

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getFileUrl() {
		return fileUrl;
	}

	public void setFileUrl(String fileUrl) {
		this.fileUrl = fileUrl;
	}

	public Boolean getBooleanData() {
		return booleanData;
	}

	public void setBooleanData(Boolean booleanData) {
		this.booleanData = booleanData;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}


	
}
