package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChaiCustomization implements Serializable {

    private static final long serialVersionUID = -7001872978716049578L;

    private Integer favChaiCustomizationDetailId;

    private Integer productId;

    private String name;

    private String type;//addonType

    private String productSourceSystem;

    private String dimension;

    private BigDecimal quantity;

    private String defaultSetting;

    private String shortCode;
    private String uom;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChaiCustomization that = (ChaiCustomization) o;
        return Objects.equals(getProductId(), that.getProductId()) && Objects.equals(getName(), that.getName()) && Objects.equals(getType(), that.getType()) && Objects.equals(getDimension(), that.getDimension()) && Objects.equals(getShortCode(), that.getShortCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getProductId(), getName(), getType(), getDimension(), getShortCode());
    }
}
