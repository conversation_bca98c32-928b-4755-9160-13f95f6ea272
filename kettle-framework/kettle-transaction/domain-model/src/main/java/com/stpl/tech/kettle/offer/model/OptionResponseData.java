package com.stpl.tech.kettle.offer.model;

import java.util.List;

public class OptionResponseData {

	private OrderData detail;
	private Integer category;
	private RuleData rule;
	private Option option;
	private int optionResultId;
	private String couponCode;
	private String couponDescription;
	private List<Integer> stockedOutProductIds;

	public OptionResponseData() {

	}

	public OptionResponseData(Integer category, RuleData rule, Option option, int optionResultId) {
		super();
		this.category = category;
		this.rule = rule;
		this.option = option;
		this.optionResultId = optionResultId;
	}

	public OptionResponseData(OptionData optionData, boolean giveOffer, int optionResultId) {
		this.option = optionData.getOption();
		this.rule = optionData.getRule();
		this.category = optionData.getUnitId();
		if (giveOffer) {
			this.couponCode = this.rule.getCoupon();
			this.couponDescription = this.rule.getCouponDescription();
		}
		this.optionResultId = optionResultId;
	}

	public Integer getCategory() {
		return category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}

	public RuleData getRule() {
		return rule;
	}

	public void setRule(RuleData rule) {
		this.rule = rule;
	}

	public Option getOption() {
		return option;
	}

	public void setOption(Option option) {
		this.option = option;
	}

	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	public String getCouponDescription() {
		return couponDescription;
	}

	public void setCouponDescription(String couponDescription) {
		this.couponDescription = couponDescription;
	}

	public int getOptionResultId() {
		return optionResultId;
	}

	public void setOptionResultId(int optionResultId) {
		this.optionResultId = optionResultId;
	}

	public OrderData getDetail() {
		return detail;
	}

	public void setDetail(OrderData detail) {
		this.detail = detail;
	}

	public List<Integer> getStockedOutProductIds() {
		return stockedOutProductIds;
	}

	public void setStockedOutProductIds(List<Integer> stocketOutProductIds) {
		this.stockedOutProductIds = stocketOutProductIds;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "OptionResponseData [detail=" + detail + ", category=" + category + ", rule=" + rule + ", option="
				+ option + ", optionResultId=" + optionResultId + "]";
	}

	
}
