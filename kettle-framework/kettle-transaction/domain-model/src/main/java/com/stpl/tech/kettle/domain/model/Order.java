/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.03.22 at 01:07:13 PM IST
//

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.CustomJsonDateDeserializer;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.Adapter5;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.CondimentItemData;
import com.stpl.tech.master.payment.model.ExternalOrder;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Java class for Order complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="Order"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="orderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="generateOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="campaignId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customerId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="employeeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="pointsRedeemed" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="source" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="sourceId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="hasParcel" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}OrderStatus"/&gt;
 *         &lt;element name="orders" type="{http://www.w3schools.com}OrderItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="enquiryItems" type="{http://www.w3schools.com}EnquiryItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="transactionDetail" type="{http://www.w3schools.com}TransactionDetail"/&gt;
 *         &lt;element name="printCount" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="settlementType" type="{http://www.w3schools.com}SettlementType"/&gt;
 *         &lt;element name="settlements" type="{http://www.w3schools.com}Settlement" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="terminalId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="tableNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="billStartTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="billCreationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="billCreationSeconds" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="billingServerTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="channelPartner" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="deliveryPartner" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="subscriptionDetail" type="{http://www.w3schools.com}Subscription"/&gt;
 *         &lt;element name="offerCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="reprints" type="{http://www.w3schools.com}ActionDetail" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="cancellationDetails" type="{http://www.w3schools.com}ActionDetail"/&gt;
 *         &lt;element name="orderRemark" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deliveryAddress" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="customerName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="tokenNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="containsSignupOffer" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="metadataList" type="{http://www.w3schools.com}OrderMetadata" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Order", propOrder = { "orderId", "generateOrderId", "unitOrderId", "campaignId", "customerId",
		"employeeId", "pointsRedeemed", "source", "sourceId", "hasParcel", "status", "orders", "enquiryItems",
		"transactionDetail", "printCount", "settlementType", "settlements", "unitId", "unitName", "tableNumber",
		"terminalId", "billStartTime", "billCreationTime", "billCreationSeconds", "billingServerTime", "channelPartner",
		"deliveryPartner", "subscriptionDetail", "offerCode", "reprints", "cancellationDetails", "orderRemark",
		"deliveryAddress", "customerName", "tokenNumber", "containsSignupOffer", "metadataList", "tempCode",
		"newCustomer", "pendingCash", "billBookNo", "ood", "orderDiscountData", "partnerCustomerId","itemCondiment" })
@Document(collection = "Order")
public class Order extends ExternalOrder implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = -7725662830415594342L;

	@Id
	private String _id;

	/*
	 * @Version
	 *
	 * @JsonIgnore private Long version;
	 *
	 *//**
		 * Added to avoid a runtime error whereby the detachAll property is checked for
		 * existence but not actually used.
		 *//*
			 * private String detachAll;
			 */
	@Indexed
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer orderId;
	@XmlElement(required = true, nillable = true)
	protected String generateOrderId;
	@XmlElement(required = true, nillable = true)
	protected String externalOrderId;
	@XmlElement(required = true, nillable = true)
	protected Integer optionResultEventId;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer unitOrderId;
	@XmlElement(required = true, nillable = true)
	protected String campaignId;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer customerId;
	protected int employeeId;
	protected int pointsRedeemed;
	protected long pointsAcquired;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal cashRedeemed;
	@XmlElement(required = true, nillable = true)
	protected String source;
	@XmlElement(required = true, nillable = true)
	protected String sourceId;
	@XmlElement(defaultValue = "false")
	protected boolean hasParcel;
	@Indexed
	@XmlElement(required = true, defaultValue = "INITIATED")
	@XmlSchemaType(name = "string")
	protected OrderStatus status;
	@XmlElement(required = true, defaultValue = "KETTLE_SERVICE")
	@XmlSchemaType(name = "string")
	protected ApplicationName application;
	protected List<OrderItem> orders;
	protected List<CondimentItemData> itemCondiment;
	protected List<EnquiryItem> enquiryItems;
	@XmlElement(required = true)
	protected TransactionDetail transactionDetail;
	protected int printCount;
	@XmlElement(required = true, defaultValue = "DEBIT")
	@XmlSchemaType(name = "string")
	protected SettlementType settlementType;
	protected List<Settlement> settlements;
	@Indexed
	protected int unitId;
	@XmlElement(required = true, nillable = true)
	protected String unitName;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer terminalId;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer tableNumber;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	protected Date billStartTime;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	protected Date billCreationTime;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	protected Date businessDate;
	protected int billCreationSeconds;
	@Indexed
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	protected Date billingServerTime;
	protected int channelPartner;
	protected int deliveryPartner;
	@XmlElement(required = true, nillable = true)
	protected Subscription subscriptionDetail;
	@XmlElement(required = true, nillable = true)
	protected String offerCode;
	protected List<ActionDetail> reprints;
	@XmlElement(required = true, nillable = true)
	protected ActionDetail cancellationDetails;
	@XmlElement(required = true, nillable = true)
	protected String orderRemark;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer deliveryAddress;
	@XmlElement(required = true, nillable = true)
	protected String customerName;
	protected boolean containsSignupOffer;
	@XmlElement(required = false, type = Boolean.class, nillable = true)
	protected Boolean employeeMeal;
	@XmlElement(required = false, type = Integer.class, nillable = true)
	protected Integer employeeIdForMeal;
	@XmlElement(required = false, nillable = true)
	protected String tempCode;
	protected List<OrderMetadata> metadataList;
	@XmlElement(required = false)
	protected boolean newCustomer;
	@XmlElement(required = false)
	protected boolean pendingCash;
	@XmlElement(required = false, type = Integer.class, nillable = true)
	protected Integer tokenNumber;
	@XmlElement(required = false, type = Integer.class, nillable = true)
	protected Integer linkedOrderId;

	protected Integer paymentDetailId;
	@XmlElement(required = true)
	protected Boolean awardLoyalty;
	protected String orderType;

	protected Integer billBookNo;
	/**
	 * ood : out of delivery flag marked while acknowledging from assembly screen
	 */
	protected boolean ood = false;
	protected Integer tableRequestId;
	protected boolean giftCardOrder;
	protected String qrLink;
	protected String qrHeader;
	protected String orderAttribute;
	@XmlElement(required = true)
	protected Integer brandId;
	protected List<OrderDiscountData> orderDiscountData;
	protected String partnerCustomerId;
	protected BigDecimal cashBackAwarded;
	protected Date cashBackStartDate;
	protected Date cashBackEndDate;
	protected Boolean cashBackReceived;

	protected int cashBackLagDays;
	protected String inAppFeedbackUrl;
	protected Integer rating;
	protected Integer maxRating;
	protected String feedbackType;
	protected Integer earnedLoyaltypoints;
	protected NextOffer nextOffer;
	private OrderInvoice invoice;
	private List<Integer> allowedPaymentIds;
	private Map<String,String> whatsappNotificationPayload = new HashMap<>();
	private String whatsappNotificationPayloadType;
	private Integer refOrderId;
	private String sourceVersion;
	private BigDecimal prepTime;

	private Date lastOrderStatusEventTime;

	private boolean bypassLoyateaAward;

	private Map<Integer, Integer> nonWastageItemMap;

	private String wastageType;

	private boolean skipLoyaltyProducts;

	private String offerAccountType;
	private String partnerCusId;

	private Boolean revalidate = false;

	private String revalidationReason;

	private boolean prioritizedOrder;
	private OrderItem serviceChargeItem;

	private Integer priceProfileId;

	private Integer priceProfileVersion;

	private Integer orderRefundDetailId;


	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/**
	 * Gets the value of the orderId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getOrderId() {
		return orderId;
	}

	/**
	 * Sets the value of the orderId property.
	 *
	 * @param value allowed object is {@link Integer }
	 *
	 */
	public void setOrderId(Integer value) {
		this.orderId = value;
	}

	/**
	 * Gets the value of the generateOrderId property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getGenerateOrderId() {
		return generateOrderId;
	}

	/**
	 * Sets the value of the generateOrderId property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setGenerateOrderId(String value) {
		this.generateOrderId = value;
	}

	public String getExternalOrderId() {
		return externalOrderId;
	}

	public void setExternalOrderId(String externalOrderId) {
		this.externalOrderId = externalOrderId;
	}

	/**
	 * Gets the value of the unitOrderId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getUnitOrderId() {
		return unitOrderId;
	}

	/**
	 * Sets the value of the unitOrderId property.
	 *
	 * @param value allowed object is {@link Integer }
	 *
	 */
	public void setUnitOrderId(Integer value) {
		this.unitOrderId = value;
	}

	/**
	 * Gets the value of the sourceId property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getCampaignId() {
		return campaignId;
	}

	/**
	 * Sets the value of the sourceId property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setCampaignId(String value) {
		this.campaignId = value;
	}

	/**
	 * Gets the value of the customerId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getCustomerId() {
		return customerId;
	}

	/**
	 * Sets the value of the customerId property.
	 *
	 * @param value allowed object is {@link Integer }
	 *
	 */
	public void setCustomerId(Integer value) {
		this.customerId = value;
	}

	/**
	 * Gets the value of the employeeId property.
	 *
	 */
	public int getEmployeeId() {
		return employeeId;
	}

	/**
	 * Sets the value of the employeeId property.
	 *
	 */
	public void setEmployeeId(int value) {
		this.employeeId = value;
	}

	/**
	 * Gets the value of the pointsRedeemed property.
	 *
	 */
	public int getPointsRedeemed() {
		return pointsRedeemed;
	}

	/**
	 * Sets the value of the pointsRedeemed property.
	 *
	 */
	public void setPointsRedeemed(int value) {
		this.pointsRedeemed = value;
	}

	public long getPointsAcquired() { return pointsAcquired; }

	public void setPointsAcquired(long pointsAcquired) { this.pointsAcquired = pointsAcquired; }

	/**
	 * Gets the value of the source property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getSource() {
		return source;
	}

	/**
	 * Sets the value of the source property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setSource(String value) {
		this.source = value;
	}

	/**
	 * Gets the value of the sourceId property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getSourceId() {
		return sourceId;
	}

	/**
	 * Sets the value of the sourceId property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setSourceId(String value) {
		this.sourceId = value;
	}

	/**
	 * Gets the value of the hasParcel property.
	 *
	 */
	public boolean isHasParcel() {
		return hasParcel;
	}

	/**
	 * Sets the value of the hasParcel property.
	 *
	 */
	public void setHasParcel(boolean value) {
		this.hasParcel = value;
	}

	/**
	 * Gets the value of the status property.
	 *
	 * @return possible object is {@link OrderStatus }
	 *
	 */
	public OrderStatus getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 *
	 * @param value allowed object is {@link OrderStatus }
	 *
	 */
	public void setStatus(OrderStatus value) {
		this.status = value;
	}

	/**
	 * Gets the value of the orders property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a snapshot.
	 * Therefore any modification you make to the returned list will be present
	 * inside the JAXB object. This is why there is not a <CODE>set</CODE> method
	 * for the orders property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getOrders().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link OrderItem }
	 *
	 *
	 */
	public List<OrderItem> getOrders() {
		if (orders == null) {
			orders = new ArrayList<OrderItem>();
		}
		return this.orders;
	}

	public List<CondimentItemData> getItemCondiment() {
		if(itemCondiment ==null){
			orders = new ArrayList<>();
		}
		return this.itemCondiment;
	}

	public void setItemCondiment(List<CondimentItemData> itemCondiment) {
		this.itemCondiment = itemCondiment;
	}

	/**
	 * Gets the value of the enquiryItems property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a snapshot.
	 * Therefore any modification you make to the returned list will be present
	 * inside the JAXB object. This is why there is not a <CODE>set</CODE> method
	 * for the enquiryItems property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getEnquiryItems().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link EnquiryItem }
	 *
	 *
	 */
	public List<EnquiryItem> getEnquiryItems() {
		if (enquiryItems == null) {
			enquiryItems = new ArrayList<EnquiryItem>();
		}
		return this.enquiryItems;
	}

	/**
	 * Gets the value of the transactionDetail property.
	 *
	 * @return possible object is {@link TransactionDetail }
	 *
	 */
	public TransactionDetail getTransactionDetail() {
		return transactionDetail;
	}

	/**
	 * Sets the value of the transactionDetail property.
	 *
	 * @param value allowed object is {@link TransactionDetail }
	 *
	 */
	public void setTransactionDetail(TransactionDetail value) {
		this.transactionDetail = value;
	}

	/**
	 * Gets the value of the printCount property.
	 *
	 */
	public int getPrintCount() {
		return printCount;
	}

	/**
	 * Sets the value of the printCount property.
	 *
	 */
	public void setPrintCount(int value) {
		this.printCount = value;
	}

	/**
	 * Gets the value of the settlementType property.
	 *
	 * @return possible object is {@link SettlementType }
	 *
	 */
	public SettlementType getSettlementType() {
		return settlementType;
	}

	/**
	 * Sets the value of the settlementType property.
	 *
	 * @param value allowed object is {@link SettlementType }
	 *
	 */
	public void setSettlementType(SettlementType value) {
		this.settlementType = value;
	}

	/**
	 * Gets the value of the settlements property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a snapshot.
	 * Therefore any modification you make to the returned list will be present
	 * inside the JAXB object. This is why there is not a <CODE>set</CODE> method
	 * for the settlements property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getSettlements().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link Settlement }
	 *
	 *
	 */
	public List<Settlement> getSettlements() {
		if (settlements == null) {
			settlements = new ArrayList<Settlement>();
		}
		return this.settlements;
	}

	/**
	 * Gets the value of the unitId property.
	 *
	 */
	public int getUnitId() {
		return unitId;
	}

	/**
	 * Sets the value of the unitId property.
	 *
	 */
	public void setUnitId(int value) {
		this.unitId = value;
	}

	/**
	 * Gets the value of the unitName property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getUnitName() {
		return unitName;
	}

	/**
	 * Sets the value of the unitName property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setUnitName(String value) {
		this.unitName = value;
	}

	/**
	 * Gets the value of the terminalId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getTerminalId() {
		return terminalId;
	}

	/**
	 * Sets the value of the terminalId property.
	 *
	 * @param value allowed object is {@link Integer }
	 *
	 */
	public void setTerminalId(Integer value) {
		this.terminalId = value;
	}

	public Integer getTableNumber() {
		return tableNumber;
	}

	public void setTableNumber(Integer tableNumber) {
		this.tableNumber = tableNumber;
	}

	/**
	 * Gets the value of the billStartTime property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getBillStartTime() {
		return billStartTime;
	}

	/**
	 * Sets the value of the billStartTime property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setBillStartTime(Date value) {
		this.billStartTime = value;
	}

	/**
	 * Gets the value of the billCreationTime property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getBillCreationTime() {
		return billCreationTime;
	}

	/**
	 * Sets the value of the billCreationTime property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setBillCreationTime(Date value) {
		this.billCreationTime = value;
	}

	/**
	 * Gets the value of the billCreationSeconds property.
	 *
	 */
	public int getBillCreationSeconds() {
		return billCreationSeconds;
	}

	/**
	 * Sets the value of the billCreationSeconds property.
	 *
	 */
	public void setBillCreationSeconds(int value) {
		this.billCreationSeconds = value;
	}

	/**
	 * Gets the value of the billingServerTime property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getBillingServerTime() {
		return billingServerTime;
	}

	/**
	 * Sets the value of the billingServerTime property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setBillingServerTime(Date value) {
		this.billingServerTime = value;
	}

	/**
	 * Gets the value of the channelPartner property.
	 *
	 */
	public int getChannelPartner() {
		return channelPartner;
	}

	/**
	 * Sets the value of the channelPartner property.
	 *
	 */
	public void setChannelPartner(int value) {
		this.channelPartner = value;
	}

	/**
	 * Gets the value of the deliveryPartner property.
	 *
	 */
	public int getDeliveryPartner() {
		return deliveryPartner;
	}

	/**
	 * Sets the value of the deliveryPartner property.
	 *
	 */
	public void setDeliveryPartner(int value) {
		this.deliveryPartner = value;
	}

	/**
	 * Gets the value of the subscriptionDetail property.
	 *
	 * @return possible object is {@link Subscription }
	 *
	 */
	public Subscription getSubscriptionDetail() {
		return subscriptionDetail;
	}

	/**
	 * Sets the value of the subscriptionDetail property.
	 *
	 * @param value allowed object is {@link Subscription }
	 *
	 */
	public void setSubscriptionDetail(Subscription value) {
		this.subscriptionDetail = value;
	}

	/**
	 * Gets the value of the offerCode property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getOfferCode() {
		return offerCode;
	}

	/**
	 * Sets the value of the offerCode property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setOfferCode(String value) {
		this.offerCode = value;
	}

	/**
	 * Gets the value of the reprints property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a snapshot.
	 * Therefore any modification you make to the returned list will be present
	 * inside the JAXB object. This is why there is not a <CODE>set</CODE> method
	 * for the reprints property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getReprints().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link ActionDetail
	 * }
	 *
	 *
	 */
	public List<ActionDetail> getReprints() {
		if (reprints == null) {
			reprints = new ArrayList<ActionDetail>();
		}
		return this.reprints;
	}

	/**
	 * Gets the value of the cancellationDetails property.
	 *
	 * @return possible object is {@link ActionDetail }
	 *
	 */
	public ActionDetail getCancellationDetails() {
		return cancellationDetails;
	}

	/**
	 * Sets the value of the cancellationDetails property.
	 *
	 * @param value allowed object is {@link ActionDetail }
	 *
	 */
	public void setCancellationDetails(ActionDetail value) {
		this.cancellationDetails = value;
	}

	/**
	 * Gets the value of the orderRemark property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getOrderRemark() {
		return orderRemark;
	}

	/**
	 * Sets the value of the orderRemark property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setOrderRemark(String value) {
		this.orderRemark = value;
	}

	/**
	 * Gets the value of the deliveryAddress property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getDeliveryAddress() {
		return deliveryAddress;
	}

	/**
	 * Sets the value of the deliveryAddress property.
	 *
	 * @param value allowed object is {@link Integer }
	 *
	 */
	public void setDeliveryAddress(Integer value) {
		this.deliveryAddress = value;
	}

	/**
	 * Gets the value of the customerName property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getCustomerName() {
		return customerName;
	}

	/**
	 * Sets the value of the customerName property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setCustomerName(String value) {
		this.customerName = value;
	}

	/**
	 * Gets the value of the metadataList property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a snapshot.
	 * Therefore any modification you make to the returned list will be present
	 * inside the JAXB object. This is why there is not a <CODE>set</CODE> method
	 * for the metadataList property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getMetadataList().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link OrderMetadata
	 * }
	 *
	 *
	 */
	public List<OrderMetadata> getMetadataList() {
		if (metadataList == null) {
			metadataList = new ArrayList<OrderMetadata>();
		}
		return this.metadataList;
	}

	/*
	 * public Long getVersion() { return version; }
	 *
	 * public void setVersion(Long version) { this.version = version; }
	 *
	 * public String getDetachAll() { return detachAll; }
	 *
	 * public void setDetachAll(String detachAll) { this.detachAll = detachAll; }
	 */

	public void setOrders(List<OrderItem> orders) {
		this.orders = orders;
	}

	public void setEnquiryItems(List<EnquiryItem> enquiryItems) {
		this.enquiryItems = enquiryItems;
	}

	public void setSettlements(List<Settlement> settlements) {
		this.settlements = settlements;
	}

	public void setReprints(List<ActionDetail> reprints) {
		this.reprints = reprints;
	}

	public void setMetadataList(List<OrderMetadata> metadataList) {
		this.metadataList = metadataList;
	}

	public boolean isContainsSignupOffer() {
		return containsSignupOffer;
	}

	public void setContainsSignupOffer(boolean containsSignupOffer) {
		this.containsSignupOffer = containsSignupOffer;
	}

	public Boolean isEmployeeMeal() {
		return employeeMeal;
	}

	public void setEmployeeMeal(Boolean employeeMeal) {
		this.employeeMeal = employeeMeal;
	}

	public Integer getEmployeeIdForMeal() {
		return employeeIdForMeal;
	}

	public void setEmployeeIdForMeal(Integer employeeIdForMeal) {
		this.employeeIdForMeal = employeeIdForMeal;
	}

	public String getTempCode() {
		return tempCode;
	}

	public void setTempCode(String tempCode) {
		this.tempCode = tempCode;
	}

	public boolean isNewCustomer() {
		return newCustomer;
	}

	public void setNewCustomer(boolean newCustomer) {
		this.newCustomer = newCustomer;
	}

	public boolean isPendingCash() {
		return pendingCash;
	}

	public void setPendingCash(boolean pendingCash) {
		this.pendingCash = pendingCash;
	}

	public ApplicationName getApplication() {
		return application;
	}

	public void setApplication(ApplicationName application) {
		this.application = application;
	}

	public Integer getOptionResultEventId() {
		return optionResultEventId;
	}

	public void setOptionResultEventId(Integer optionResultEventId) {
		this.optionResultEventId = optionResultEventId;
	}

	public Integer getPaymentDetailId() {
		return paymentDetailId;
	}

	public void setPaymentDetailId(Integer paymentDetail) {
		this.paymentDetailId = paymentDetail;
	}

	/**
	 * @return
	 */
	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public Integer getTokenNumber() {
		return tokenNumber;
	}

	public void setTokenNumber(Integer tokenNumber) {
		this.tokenNumber = tokenNumber;
	}

	public Integer getLinkedOrderId() {
		return linkedOrderId;
	}

	public void setLinkedOrderId(Integer linkedOrderId) {
		this.linkedOrderId = linkedOrderId;
	}

	public Integer getBillBookNo() {
		return billBookNo;
	}

	public void setBillBookNo(Integer billBookNo) {
		this.billBookNo = billBookNo;
	}

	public boolean isOod() {
		return ood;
	}

	public void setOod(boolean ood) {
		this.ood = ood;
	}

	public Boolean getAwardLoyalty() {
		return awardLoyalty;
	}

	public void setAwardLoyalty(Boolean awardLoyalty) {
		this.awardLoyalty = awardLoyalty;
	}

	public Integer getTableRequestId() {
		return tableRequestId;
	}

	public void setTableRequestId(Integer tableRequestId) {
		this.tableRequestId = tableRequestId;
	}

	public boolean isGiftCardOrder() {
		return giftCardOrder;
	}

	public void setGiftCardOrder(boolean giftCardOrder) {
		this.giftCardOrder = giftCardOrder;
	}

	public BigDecimal getCashRedeemed() {
		return cashRedeemed;
	}

	public void setCashRedeemed(BigDecimal cashRedeemed) {
		this.cashRedeemed = cashRedeemed;
	}

	public String getOrderAttribute() {
		return orderAttribute;
	}

	public void setOrderAttribute(String orderAttribute) {
		this.orderAttribute = orderAttribute;
	}

	public String getQrLink() {
		return qrLink;
	}

	public void setQrLink(String qrLink) {
		this.qrLink = qrLink;
	}

	public String getQrHeader() {
		return qrHeader;
	}

	public void setQrHeader(String qrHeader) {
		this.qrHeader = qrHeader;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public List<OrderDiscountData> getOrderDiscountData() {
		return orderDiscountData;
	}

	public void setOrderDiscountData(List<OrderDiscountData> orderDiscountData) {
		this.orderDiscountData = orderDiscountData;
	}

	public String getPartnerCustomerId() {
		return partnerCustomerId;
	}

	public void setPartnerCustomerId(String partnerCustomerId) {
		this.partnerCustomerId = partnerCustomerId;
	}

	public BigDecimal getCashBackAwarded() {
		return cashBackAwarded;
	}

	public void setCashBackAwarded(BigDecimal cashBackAwarded) {
		this.cashBackAwarded = cashBackAwarded;
	}

    public Date getCashBackStartDate() {
        return cashBackStartDate;
    }

    public void setCashBackStartDate(Date cashBackStartDate) {
        this.cashBackStartDate = cashBackStartDate;
    }

    public Date getCashBackEndDate() {
        return cashBackEndDate;
    }

    public void setCashBackEndDate(Date cashBackEndDate) {
        this.cashBackEndDate = cashBackEndDate;
    }

    public Boolean getCashBackReceived() {
        return cashBackReceived;
    }

    public void setCashBackReceived(Boolean cashBackReceived) {
        this.cashBackReceived = cashBackReceived;
    }

	public String getInAppFeedbackUrl() {
		return inAppFeedbackUrl;
	}

	public void setInAppFeedbackUrl(String inAppFeedbackUrl) {
		this.inAppFeedbackUrl = inAppFeedbackUrl;
	}

	public Integer getRating() {
		return rating;
	}

	public void setRating(Integer rating) {
		this.rating = rating;
	}
	public Integer getMaxRating() {
		return maxRating;
	}

	public void setMaxRating(Integer maxRating) {
		this.maxRating = maxRating;
	}

	public String getFeedbackType() {
		return feedbackType;
	}

	public void setFeedbackType(String feedbackType) {
		this.feedbackType = feedbackType;
	}

	public Integer getEarnedLoyaltypoints() {
		return earnedLoyaltypoints;
	}

	public void setEarnedLoyaltypoints(Integer earnedLoyaltypoints) {
		this.earnedLoyaltypoints = earnedLoyaltypoints;
	}

	public NextOffer getNextOffer() {
		return nextOffer;
	}

	public void setNextOffer(NextOffer nextOffer) {
		this.nextOffer = nextOffer;
	}

	public OrderInvoice getInvoice() {
		return invoice;
	}

	public void setInvoice(OrderInvoice invoice) {
		this.invoice = invoice;
	}

	public List<Integer> getAllowedPaymentIds() {
		return allowedPaymentIds;
	}

	public void setAllowedPaymentIds(List<Integer> allowedPaymentIds) {
		this.allowedPaymentIds = allowedPaymentIds;
	}

	public void setWhatsappNotificationPayload(Map<String, String> whatsappNotificationPayload) {
		this.whatsappNotificationPayload = whatsappNotificationPayload;
	}

	public void setWhatsappNotificationPayloadType(String whatsappNotificationPayloadType) {
		this.whatsappNotificationPayloadType = whatsappNotificationPayloadType;
	}

	public Map<String, String> getWhatsappNotificationPayload() {
		return whatsappNotificationPayload;
	}

	public String getWhatsappNotificationPayloadType() {
		return whatsappNotificationPayloadType;
	}

	public int getCashBackLagDays() {
		return cashBackLagDays;
	}

	public void setCashBackLagDays(int cashBackLagDays) {
		this.cashBackLagDays = cashBackLagDays;
	}

	public Integer getRefOrderId() {
		return refOrderId;
	}

	public void setRefOrderId(Integer refOrderId) {
		this.refOrderId = refOrderId;
	}

	public String getSourceVersion() {
		return sourceVersion;
	}

	public void setSourceVersion(String sourceVersion) {
		this.sourceVersion = sourceVersion;
	}

	public BigDecimal getPrepTime() {
		return prepTime;
	}

	public void setPrepTime(BigDecimal prepTime) {
		this.prepTime = prepTime;
	}

	public Date getLastOrderStatusEventTime() {
		return lastOrderStatusEventTime;
	}

	public void setLastOrderStatusEventTime(Date lastOrderStatusEventTime) {
		this.lastOrderStatusEventTime = lastOrderStatusEventTime;
	}
	public boolean isBypassLoyateaAward() {
		return bypassLoyateaAward;
	}

	public void setBypassLoyateaAward(boolean bypassLoyateaAward) {
		this.bypassLoyateaAward = bypassLoyateaAward;
	}

	public Map<Integer, Integer> getNonWastageItemMap() {
		return nonWastageItemMap;
	}

	public void setNonWastageItemMap(Map<Integer, Integer> nonWastageItemMap) {
		this.nonWastageItemMap = nonWastageItemMap;
	}

	public String getWastageType() {
		return wastageType;
	}

	public void setWastageType(String wastageType) {
		this.wastageType = wastageType;
	}

	public boolean isSkipLoyaltyProducts() {
		return skipLoyaltyProducts;
	}

	public void setSkipLoyaltyProducts(boolean skipLoyaltyProducts) {
		this.skipLoyaltyProducts = skipLoyaltyProducts;
	}

	public String getOfferAccountType() {
		return offerAccountType;
	}

	public void setOfferAccountType(String offerAccountType) {
		this.offerAccountType = offerAccountType;
	}

	public String getPartnerCusId() {
		return partnerCusId;
	}

	public void setPartnerCusId(String partnerCusId) {
		this.partnerCusId = partnerCusId;
	}

	public Boolean getRevalidate() { return revalidate; }

	public void setRevalidate(Boolean revalidate) { this.revalidate = revalidate; }

	public String getRevalidationReason() { return revalidationReason; }

	public void setRevalidationReason(String revalidationReason) { this.revalidationReason = revalidationReason; }

	public boolean getPrioritizedOrder() {
		return prioritizedOrder;
	}

	public void setPrioritizedOrder(Boolean prioritizedOrder) {
		this.prioritizedOrder = prioritizedOrder;
	}

	public OrderItem getServiceChargeItem() {
		return serviceChargeItem;
	}

	public Integer getPriceProfileId() {
		return priceProfileId;
	}

	public void setPriceProfileId(Integer priceProfileId) {
		this.priceProfileId = priceProfileId;
	}

	public Integer getPriceProfileVersion() {
		return priceProfileVersion;
	}

	public void setPriceProfileVersion(Integer priceProfileVersion) {
		this.priceProfileVersion = priceProfileVersion;
	}

	public void setServiceChargeItem(OrderItem serviceChargeItem) {
		this.serviceChargeItem = serviceChargeItem;
	}

	public Integer getOrderRefundDetailId() {
		return orderRefundDetailId;
	}

	public void setOrderRefundDetailId(Integer orderRefundDetailId) {
		this.orderRefundDetailId = orderRefundDetailId;
	}
}
