/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.02.29 at 04:02:18 PM IST
//

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Java class for Settlement complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="Settlement"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="settlementId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="mode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="modeDetail" type="{http://www.w3schools.com}PaymentMode"/&gt;
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="extraVouchers" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="denominations" type="{http://www.w3schools.com}OrderPaymentDenomination" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="externalTransactionId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Settlement", propOrder = {
    "settlementId",
    "mode",
    "modeDetail",
    "amount",
    "extraVouchers",
    "denominations",
    "edited",
    "editedBy",
    "oldMode"
})
@Document
public class Settlement implements Serializable{

	/**
	 *
	 */
	private static final long serialVersionUID = 5068507138606482424L;
	@Id
	private String _id;
/*
	@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/

	@Field
	protected int settlementId;
	@Field
	protected int mode;
	@XmlElement(required = true, nillable = true)
	@Field
	protected PaymentMode modeDetail;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal amount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal extraVouchers;
    @XmlElement(required = false)
    protected boolean edited;
    @XmlElement(required = false)
    protected String editedBy;
    @XmlElement(required = false)
    protected PaymentMode oldMode;
	@Field
	protected List<OrderPaymentDenomination> denominations;
	@Field
	protected List<ExternalSettlement> externalSettlements;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/*public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}*/

	/**
	 * Gets the value of the settlementId property.
	 *
	 */
	public int getSettlementId() {
		return settlementId;
	}

	/**
	 * Sets the value of the settlementId property.
	 *
	 */
	public void setSettlementId(int value) {
		this.settlementId = value;
	}

	/**
	 * Gets the value of the mode property.
	 *
	 */
	public int getMode() {
		return mode;
	}

	/**
	 * Sets the value of the mode property.
	 *
	 */
	public void setMode(int value) {
		this.mode = value;
	}

	/**
	 * Gets the value of the modeDetail property.
	 *
	 * @return possible object is {@link PaymentMode }
	 *
	 */
	public PaymentMode getModeDetail() {
		return modeDetail;
	}

	/**
	 * Sets the value of the modeDetail property.
	 *
	 * @param value
	 *            allowed object is {@link PaymentMode }
	 *
	 */
	public void setModeDetail(PaymentMode value) {
		this.modeDetail = value;
	}

	/**
	 * Gets the value of the amount property.
	 *
	 * @return possible object is {@link BigDecimal }
	 *
	 */
	public BigDecimal getAmount() {
		return amount;
	}

	/**
	 * Sets the value of the amount property.
	 *
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 *
	 */
	public void setAmount(BigDecimal value) {
		this.amount = value;
	}

	/**
	 * Gets the value of the extraVouchers property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public BigDecimal getExtraVouchers() {
		return extraVouchers;
	}

	/**
	 * Sets the value of the extraVouchers property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setExtraVouchers(BigDecimal value) {
		this.extraVouchers = value;
	}

	/**
	 * Gets the value of the denominations property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the denominations property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getDenominations().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link OrderPaymentDenomination }
	 *
	 *
	 */
	public List<OrderPaymentDenomination> getDenominations() {
		if (denominations == null) {
			denominations = new ArrayList<OrderPaymentDenomination>();
		}
		return this.denominations;
	}

	public List<ExternalSettlement> getExternalSettlements() {
		if (externalSettlements == null) {
			externalSettlements = new ArrayList<ExternalSettlement>();
		}
		return this.externalSettlements;
	}

	public void setExternalSettlements(List<ExternalSettlement> externalSettlements) {
		this.externalSettlements = externalSettlements;
	}

	public boolean isEdited() {
		return edited;
	}

	public void setEdited(boolean edited) {
		this.edited = edited;
	}

	public String getEditedBy() {
		return editedBy;
	}

	public void setEditedBy(String editedBy) {
		this.editedBy = editedBy;
	}

	public PaymentMode getOldMode() {
		return oldMode;
	}

	public void setOldMode(PaymentMode oldMode) {
		this.oldMode = oldMode;
	}


}
