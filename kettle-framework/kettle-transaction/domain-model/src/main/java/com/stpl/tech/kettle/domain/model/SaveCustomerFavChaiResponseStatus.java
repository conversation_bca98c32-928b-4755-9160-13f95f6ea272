package com.stpl.tech.kettle.domain.model;

import java.util.HashMap;
import java.util.Map;

public enum SaveCustomerFavChaiResponseStatus {



    SAVED_MY_FAV_CHAI("Saved my favourite Chai successfully !",PersistenceType.SAVED_FOR_SELF),
    SAVED_FAMILY_MEMBER_CHAI("Saved family member's chai successfully ", PersistenceType.SAVED_FOR_FAMILY),
    SAVED_OTHERS_FAV_CHAI("Saved others favourite chai succefully!",PersistenceType.SAVED_FOR_OTHERS),
    UPDATED_MY_FAV_CHAI("Updated my favourite Chai successfully!",PersistenceType.UPDATED_FOR_SELF),
    UPDATED_OTHERS_FAV_CHAI("Updated my favourite Chai successfully!",PersistenceType.UPDATED_FOR_OTHERS),
    UPDATED_MY_FAMILY_MEMBER_CHAI("Updated my family member's chai successfully", PersistenceType.UPDATED_FOR_FAMILY),
    UNABLE_TO_SAVE_FAV_CHAI("Unable to save customer fav chai",PersistenceType.FAILED),
    MARKED_INACTIVE_MY_CHAI("Removed meri chai ", PersistenceType.MARKED_INACTIVE_FOR_SELF),
    MARKED_INACTIVE_FAMILY_MEMBER_CHAI("Removed family member's chai", PersistenceType.MARKED_INACTIVE_FOR_FAMILY),
    MARKED_INACTIVE_OTHERS_CHAI("Removed other's chai", PersistenceType.MARKED_INACTIVE_FOR_OTHERS);

    private static final Map<String, SaveCustomerFavChaiResponseStatus> responseMap;

    static {
        responseMap = new HashMap<>();
        for(SaveCustomerFavChaiResponseStatus  val : values()) {
            responseMap.put(val.getPersistenceType().toString(), val);
        }
    }

    public static SaveCustomerFavChaiResponseStatus getFavChaiResponse(String reasonString) {
        String key = reasonString.toUpperCase();
        if (responseMap.containsKey(key)) {
            return responseMap.get(key);
        }
        return null;
    }


    private final String message;
    private final PersistenceType persistenceType;

    SaveCustomerFavChaiResponseStatus(String message, PersistenceType persistenceType) {
        this.message = message;
        this.persistenceType = persistenceType;
    }

    public String getMessage() {
        return message;
    }
    public PersistenceType getPersistenceType() {
        return persistenceType;
    }
}
