/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for InventoryEventData complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="InventoryEventData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="quantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="thresholdQuantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}InventoryEventType"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "InventoryEventData", propOrder = { "unitId", "productId", "quantity", "thresholdQuantity", "type" })
public class InventoryEventData {

	protected int unitId;
	protected int productId;
	protected int quantity;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer thresholdQuantity;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected InventoryEventType type;
	protected int expireQuantity;

	/**
	 * Gets the value of the unitId property.
	 * 
	 */
	public int getUnitId() {
		return unitId;
	}

	/**
	 * Sets the value of the unitId property.
	 * 
	 */
	public void setUnitId(int value) {
		this.unitId = value;
	}

	/**
	 * Gets the value of the productId property.
	 * 
	 */
	public int getProductId() {
		return productId;
	}

	/**
	 * Sets the value of the productId property.
	 * 
	 */
	public void setProductId(int value) {
		this.productId = value;
	}

	/**
	 * Gets the value of the quantity property.
	 * 
	 */
	public int getQuantity() {
		return quantity;
	}

	/**
	 * Sets the value of the quantity property.
	 * 
	 */
	public void setQuantity(int value) {
		this.quantity = value;
	}

	/**
	 * Gets the value of the thresholdQuantity property.
	 * 
	 * @return possible object is {@link Integer }
	 * 
	 */
	public Integer getThresholdQuantity() {
		return thresholdQuantity;
	}

	/**
	 * Sets the value of the thresholdQuantity property.
	 * 
	 * @param value allowed object is {@link Integer }
	 * 
	 */
	public void setThresholdQuantity(Integer value) {
		this.thresholdQuantity = value;
	}

	/**
	 * Gets the value of the type property.
	 * 
	 * @return possible object is {@link InventoryEventType }
	 * 
	 */
	public InventoryEventType getType() {
		return type;
	}

	/**
	 * Sets the value of the type property.
	 * 
	 * @param value allowed object is {@link InventoryEventType }
	 * 
	 */
	public void setType(InventoryEventType value) {
		this.type = value;
	}

	public int getExpireQuantity() {
		return expireQuantity;
	}

	public void setExpireQuantity(int expireQuantity) {
		this.expireQuantity = expireQuantity;
	}

}
