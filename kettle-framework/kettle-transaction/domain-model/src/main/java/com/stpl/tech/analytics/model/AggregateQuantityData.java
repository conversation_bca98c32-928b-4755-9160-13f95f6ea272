//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.10 at 07:11:28 PM IST
//

package com.stpl.tech.analytics.model;

import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.domain.model.UnitCategory;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

/**
 * <p>
 * Java class for AggregateQuantityData complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="AggregateQuantityData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="total" type="{http://www.w3schools.com}NumericData"/&gt;
 *         &lt;element name="accountableComplimentary" type="{http://www.w3schools.com}NumericData"/&gt;
 *         &lt;element name="nonAccountableComplimentary" type="{http://www.w3schools.com}NumericData"/&gt;
 *         &lt;element name="deliveryTotal" type="{http://www.w3schools.com}NumericData"/&gt;
 *         &lt;element name="deliveryAccountableComplimentary" type="{http://www.w3schools.com}NumericData"/&gt;
 *         &lt;element name="deliveryNonAccountableComplimentary" type="{http://www.w3schools.com}NumericData"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AggregateQuantityData", propOrder = { "total", "dineIn", "delivery" })
@Document
public class AggregateQuantityData implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = -7090819572516825056L;
	@Id
	private String _id;

	/*
	 * @Version
	 *
	 * @JsonIgnore private Long version;
	 *
	 *//**
		 * Added to avoid a runtime error whereby the detachAll property is
		 * checked for existence but not actually used.
		 *//*
		 * private String detachAll;
		 */
	@XmlElement(required = true)
	@Field
	protected NumericData total = new NumericData();
	@XmlElement(required = true)
	@Field
	protected NumericData dineIn = new NumericData();
	@XmlElement(required = true)
	@Field
	protected NumericData takeAway = new NumericData();
	@XmlElement(required = true)
	@Field
	protected NumericData delivery = new NumericData();

	/**
	 * @return the _id
	 */
	public String get_id() {
		return _id;
	}

	/**
	 * @param _id
	 *            the _id to set
	 */
	public void set_id(String _id) {
		this._id = _id;
	}
	/*

		*/
	/**
	 * @return the version
	 *//*
		 *
		 * public Long getVersion() { return version; }
		 *
		 */
	/**
	 * @param version
	 *            the version to set
	 *//*
		 *
		 * public void setVersion(Long version) { this.version = version; }
		 *
		 */
	/**
	 * @return the detachAll
	 *//*
		 *
		 * public String getDetachAll() { return detachAll; }
		 *
		 */
	/**
	 * @param detachAll
	 *            the detachAll to set
	 *//*
		 *
		 * public void setDetachAll(String detachAll) { this.detachAll =
		 * detachAll; }
		 */

	/**
	 * Gets the value of the total property.
	 *
	 * @return possible object is {@link NumericData }
	 *
	 */
	public NumericData getTotal() {
		return total;
	}

	/**
	 * Sets the value of the total property.
	 *
	 * @param value
	 *            allowed object is {@link NumericData }
	 *
	 */
	public void setTotal(NumericData value) {
		this.total = value;
	}

	/**
	 * Gets the value of the deliveryTotal property.
	 *
	 * @return possible object is {@link NumericData }
	 *
	 */
	public NumericData getDineIn() {
		return dineIn;
	}

	/**
	 * Sets the value of the deliveryTotal property.
	 *
	 * @param value
	 *            allowed object is {@link NumericData }
	 *
	 */
	public void setDineIn(NumericData value) {
		this.dineIn = value;
	}

	/**
	 * Gets the value of the deliveryAccountableComplimentary property.
	 *
	 * @return possible object is {@link NumericData }
	 *
	 */
	public NumericData getDelivery() {
		return delivery;
	}

	/**
	 * Sets the value of the deliveryAccountableComplimentary property.
	 *
	 * @param value
	 *            allowed object is {@link NumericData }
	 *
	 */
	public void setDelivery(NumericData value) {
		this.delivery = value;
	}

	public NumericData getTakeAway() {
		return takeAway;
	}

	public void setTakeAway(NumericData takeAway) {
		this.takeAway = takeAway;
	}

	public void addOrderItem(UnitCategory category, OrderStatus status, OrderItem order) {
		int multiplier = 1;
		if (OrderStatus.CANCELLED.equals(status) || OrderStatus.CANCELLED_REQUESTED.equals(status)) {
			multiplier = -1;
		}
		this.total.current = this.total.current + order.getQuantity() * multiplier;
		if (UnitCategory.CAFE.equals(category)) {
			this.dineIn.current = this.dineIn.current + order.getQuantity() * multiplier;
		} else if (UnitCategory.COD.equals(category) || UnitCategory.DELIVERY.equals(category)) {
			this.delivery.current = this.delivery.current + order.getQuantity() * multiplier;
		} else if (UnitCategory.TAKE_AWAY.equals(category)) {
			this.takeAway.current = this.takeAway.current + order.getQuantity() * multiplier;
		}

	}
}
