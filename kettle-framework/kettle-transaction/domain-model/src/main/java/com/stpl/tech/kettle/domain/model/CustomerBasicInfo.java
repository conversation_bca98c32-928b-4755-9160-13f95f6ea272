package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name="CustomerBasicInfo",propOrder = {
        "customerId","name","emailId","contactNumber","newCustomer"
})
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerBasicInfo implements Serializable {

    private static final long serialVersionUID = -4796816988177491340L;

    @XmlElement(required = true,type=String.class)
    @Field
    @JsonProperty(value="id",required = true)
    private int customerId;
    @XmlElement(required = true)
    @Field
    @JsonProperty(value="name")
    private String name;
    @Field
    @JsonProperty(value="email")
    private String emailId;
    @Field
    @JsonProperty(value="contact",required = true)
    private String contactNumber;
    @Field
    private boolean newCustomer;

    public CustomerBasicInfo(int customerId, String name) {
        this.customerId = customerId;
        this.name = name;
    }
    public CustomerBasicInfo(){

    }

    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public boolean isNewCustomer() {
        return newCustomer;
    }

    public void setNewCustomer(boolean newCustomer) {
        this.newCustomer = newCustomer;
    }
}
