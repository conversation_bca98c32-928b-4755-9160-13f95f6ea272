//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.10 at 07:11:28 PM IST
//

package com.stpl.tech.analytics.model;

import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.util.AppUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * Java class for AggregateSaleData complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="AggregateSaleData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="netSale" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="deliveryNetSale" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="gmv" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="deliveryGmv" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="apc" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="foodCapture" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="customerCapture" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="deliveryApc" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="tickets" type="{http://www.w3schools.com}int"/&gt;
 *         &lt;element name="deliveryTickets" type="{http://www.w3schools.com}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AggregateSaleData", propOrder = { "total", "delivery", "dineIn", "food", "nonVeg", "custom", "cold",
		"meals", "cakes", "giftCards", "merchandise", "beverage", "newCustWithNoPriPrd", "seasonal", "customer",
		"newCustomer" ,"seasonalProductOne","seasonalProductTwo", "seasonalProductThree","unitSubscriptionCount", "specificUnitReportData"})
@Document
public class AggregatePenetrationData implements Serializable, Cloneable {

	/**
	 *
	 */
	private static final long serialVersionUID = -2995931521596081355L;

	@Id
	private String _id;

	@XmlElement(required = true)
	@Field
	protected int total = 0;
	@XmlElement(required = true)
	@Field
	protected int delivery = 0;
	@XmlElement(required = true)
	@Field
	protected int dineIn = 0;
	@XmlElement(required = true)
	@Field
	protected int food = 0;
	@XmlElement(required = true)
	@Field
	protected int nonVeg = 0;
	@XmlElement(required = true)
	@Field
	protected int cold = 0;
	@XmlElement(required = true)
	@Field
	protected int hot = 0;
	@XmlElement(required = true)
	@Field
	protected int meals = 0;
	@XmlElement(required = true)
	@Field
	protected int cakes = 0;
	@XmlElement(required = true)
	@Field
	protected int giftCards = 0;
	@XmlElement(required = true)
	@Field
	protected int totalTicketForGC = 0;
	@XmlElement(required = true)
	@Field
	protected int merchandise = 0;
	@XmlElement(required = true)
	@Field
	protected int beverage = 0;
	@XmlElement(required = true)
	@Field
	protected int newCustWithNoPriPrd = 0;
	@XmlElement(required = true)
	@Field
	protected int seasonal = 0;
	@XmlElement(required = true)
	@Field
	protected int customer = 0;
	@XmlElement(required = true)
	@Field
	protected int newCustomer = 0;

	@XmlElement(required = true)
	@Field
	protected int seasonalProductOne = 0;

	@XmlElement(required = true)
	@Field
	protected int seasonalProductTwo = 0;

	@XmlElement(required = true)
	@Field
	protected int seasonalProductThree = 0;



	@XmlElement(required = true)
	@Field
	protected int unitSubscriptionCount = 0;

	@XmlElement(required = true)
	@Field
	protected int regular = 0;
	@XmlElement(required = true)
	@Field
	protected int full = 0;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal merchandiseSales = new BigDecimal(0);

	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal netSale = new BigDecimal(0);
	private UnitReportData specificUnitReportData;
	private UnitReportData overallSystemWideReportData;

	public UnitReportData getSpecificUnitReportData() {
		return specificUnitReportData;
	}

	public void setSpecificUnitReportData(UnitReportData specificUnitReportData) {
		this.specificUnitReportData = specificUnitReportData;
	}

	public UnitReportData getOverallSystemWideReportData() {
		return overallSystemWideReportData;
	}

	public void setOverallSystemWideReportData(UnitReportData overallSystemWideReportData) {
		this.overallSystemWideReportData = overallSystemWideReportData;
	}

	/**
	 * @return the _id
	 */
	public String get_id() {
		return _id;
	}

	/**
	 * @param _id
	 *            the _id to set
	 */
	public void set_id(String _id) {
		this._id = _id;
	}

	/**
	 * Gets the value of the tickets property.
	 *
	 * @return possible object is {@link int }
	 *
	 */
	public int getTotal() {
		return total;
	}

	/**
	 * Sets the value of the tickets property.
	 *
	 * @param value
	 *            allowed object is {@link int }
	 *
	 */
	public void setTotal(int value) {
		this.total = value;
	}

	/**
	 * Gets the value of the deliveryTickets property.
	 *
	 * @return possible object is {@link int }
	 *
	 */
	public int getDelivery() {
		return delivery;
	}

	/**
	 * Sets the value of the deliveryTickets property.
	 *
	 * @param value
	 *            allowed object is {@link int }
	 *
	 */
	public void setDelivery(int value) {
		this.delivery = value;
	}

	public int getFood() {
		return food;
	}

	public void setFood(int foodTickets) {
		this.food = foodTickets;
	}

	public int getCold() {
		return cold;
	}

	public void setCold(int coldTickets) {
		this.cold = coldTickets;
	}

	public int getSeasonal() {
		return seasonal;
	}

	public void setSeasonal(int seasonalCount) {
		this.seasonal = seasonalCount;
	}

	public int getCustomer() {
		return customer;
	}

	public void setCustomer(int customerTickets) {
		this.customer = customerTickets;
	}

	public int getNonVeg() {
		return nonVeg;
	}

	public void setNonVeg(int nonVegTickets) {
		this.nonVeg = nonVegTickets;
	}

	public int getDineIn() {
		return dineIn;
	}

	public void setDineIn(int dineInTickets) {
		this.dineIn = dineInTickets;
	}

	public int getMeals() {
		return meals;
	}

	public void setMeals(int meals) {
		this.meals = meals;
	}

	public int getCakes() {
		return cakes;
	}

	public void setCakes(int cakes) {
		this.cakes = cakes;
	}

	public int getGiftCards() {
		return giftCards;
	}

	public void setGiftCards(int giftCards) {
		this.giftCards = giftCards;
	}

	public int getMerchandise() {
		return merchandise;
	}

	public void setMerchandise(int merchandise) {
		this.merchandise = merchandise;
	}

	public int getBeverage() {
		return beverage;
	}

	public void setBeverage(int beverage) {
		this.beverage = beverage;
	}

	public int getNewCustWithNoPriPrd() {
		return newCustWithNoPriPrd;
	}

	public void setNewCustWithNoPriPrd(int newCustWithNoPriPrd) {
		this.newCustWithNoPriPrd = newCustWithNoPriPrd;
	}

	public int getNewCustomer() {
		return newCustomer;
	}

	public void setNewCustomer(int newCustomer) {
		this.newCustomer = newCustomer;
	}

	public int getSeasonalProductOne() {
		return seasonalProductOne;
	}

	public void setSeasonalProductOne(int seasonalProductOne) {
		this.seasonalProductOne = seasonalProductOne;
	}

	public int getSeasonalProductTwo() {
		return seasonalProductTwo;
	}

	public void setSeasonalProductTwo(int seasonalProductTwo) {
		this.seasonalProductTwo = seasonalProductTwo;
	}

	public int getSeasonalProductThree() {
		return seasonalProductThree;
	}

	public void setSeasonalProductThree(int seasonalProductThree) {
		this.seasonalProductThree = seasonalProductThree;
	}

	public int getUnitSubscriptionCount() {
		return unitSubscriptionCount;
	}

	public void setUnitSubscriptionCount(int unitSubscriptionCount) {
		this.unitSubscriptionCount = unitSubscriptionCount;
	}

	public int getHot() {
		return hot;
	}

	public void setHot(int hot) {
		this.hot = hot;
	}


	public int getRegular() {
		return regular;
	}


	public int getTotalTicketForGC() {
		return totalTicketForGC;
	}

	public void setTotalTicketForGC(int totalTicketForGC) {
		this.totalTicketForGC = totalTicketForGC;
	}

	public void setRegular(int regular) {
		this.regular = regular;
	}

	public int getFull() {
		return full;
	}

	public void setFull(int full) {
		this.full = full;
	}

	public void add(AggregatePenetrationData p) {
		this.hot = this.hot + p.hot;
		this.newCustomer = this.newCustomer + p.newCustomer;
		this.newCustWithNoPriPrd = this.newCustWithNoPriPrd + p.newCustWithNoPriPrd;
		this.beverage = this.beverage + p.beverage;
		this.cakes = this.cakes + p.cakes;
		this.cold = this.cold + p.cold;
		this.customer = this.customer + p.customer;
		this.delivery = this.delivery + p.delivery;
		this.dineIn = this.dineIn + p.dineIn;
		this.food = this.food + p.food;
		this.giftCards = this.giftCards + p.giftCards;
		this.meals = this.meals + p.meals;
		this.merchandise = this.merchandise + p.merchandise;
		this.nonVeg = this.nonVeg + p.nonVeg;
		this.seasonal = this.seasonal + p.seasonal;
		this.total = this.total + p.total;
		this.regular = this.regular + p.regular;
		this.full = this.full + p.full;
		this.merchandiseSales = AppUtils.add(this.merchandiseSales, p.merchandiseSales);
		this.netSale = AppUtils.add(this.netSale, p.netSale);
		this.seasonalProductOne=this.seasonalProductOne+p.seasonalProductOne;
		this.seasonalProductTwo=this.seasonalProductTwo+p.seasonalProductTwo;
		this.seasonalProductThree=this.seasonalProductThree+p.seasonalProductThree;
		this.unitSubscriptionCount= this.unitSubscriptionCount+p.unitSubscriptionCount;


	}


	public BigDecimal getMerchandiseSales() {
		return merchandiseSales;
	}

	public void setMerchandiseSales(BigDecimal current) {
		this.merchandiseSales = current;
	}

	public Object clone() throws CloneNotSupportedException {
		return super.clone();
	}

	public BigDecimal getNetSale() {
		return netSale;
	}

	public void setNetSale(BigDecimal netSales) {
		this.netSale = netSales;
	}

}
