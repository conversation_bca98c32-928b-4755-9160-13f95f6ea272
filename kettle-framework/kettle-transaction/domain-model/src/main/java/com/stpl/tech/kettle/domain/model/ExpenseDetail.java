//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.02.16 at 02:22:44 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.domain.model.IdCodeName;


/**
 * <p>Java class for ExpenseDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ExpenseDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="expenseCategory" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="expenseType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="expenseTypeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="budgetCategory" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="budgetCategoryId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="accountableInPnL" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="source" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="errorType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="errorMsg" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}ExpenseStatus"/&gt;
 *         &lt;element name="createdOn" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="cancelledBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="cancelledOn" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="cancel" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="cancellationReason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ExpenseDetail", propOrder = {
    "id",
    "unitId",
    "expenseCategory",
    "expenseType",
    "expenseTypeId",
    "budgetCategory",
    "budgetCategoryId",
    "accountableInPnL",
    "amount",
    "comment",
    "source",
    "errorType",
    "errorMsg",
    "createdBy",
    "status",
    "createdOn",
    "cancelledBy",
    "cancelledOn",
    "cancel",
    "cancellationReason"
})
public class ExpenseDetail {

    protected int id;
    @XmlElement(required = true)
    protected IdCodeName unitId;
    @XmlElement(required = true)
    protected String expenseCategory;
    @XmlElement(required = true)
    protected String expenseType;
    protected int expenseTypeId;
    @XmlElement(required = true)
    protected String budgetCategory;
    protected int budgetCategoryId;
    protected boolean accountableInPnL;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal amount;
    @XmlElement(required = true)
    protected String comment;
    @XmlElement(required = true)
    protected String source;
    @XmlElement(required = true)
    protected String errorType;
    @XmlElement(required = true)
    protected String errorMsg;
    @XmlElement(required = true)
    protected IdCodeName createdBy;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected ExpenseStatus status;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date createdOn;
    @XmlElement(required = true)
    protected IdCodeName cancelledBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date cancelledOn;
    protected boolean cancel;
    @XmlElement(required = true)
    protected String cancellationReason;

    /**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the unitId property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setUnitId(IdCodeName value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the expenseCategory property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExpenseCategory() {
        return expenseCategory;
    }

    /**
     * Sets the value of the expenseCategory property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExpenseCategory(String value) {
        this.expenseCategory = value;
    }

    /**
     * Gets the value of the expenseType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExpenseType() {
        return expenseType;
    }

    /**
     * Sets the value of the expenseType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExpenseType(String value) {
        this.expenseType = value;
    }

    /**
     * Gets the value of the expenseTypeId property.
     * 
     */
    public int getExpenseTypeId() {
        return expenseTypeId;
    }

    /**
     * Sets the value of the expenseTypeId property.
     * 
     */
    public void setExpenseTypeId(int value) {
        this.expenseTypeId = value;
    }

    /**
     * Gets the value of the budgetCategory property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBudgetCategory() {
        return budgetCategory;
    }

    /**
     * Sets the value of the budgetCategory property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBudgetCategory(String value) {
        this.budgetCategory = value;
    }

    /**
     * Gets the value of the budgetCategoryId property.
     * 
     */
    public int getBudgetCategoryId() {
        return budgetCategoryId;
    }

    /**
     * Sets the value of the budgetCategoryId property.
     * 
     */
    public void setBudgetCategoryId(int value) {
        this.budgetCategoryId = value;
    }

    /**
     * Gets the value of the accountableInPnL property.
     * 
     */
    public boolean isAccountableInPnL() {
        return accountableInPnL;
    }

    /**
     * Sets the value of the accountableInPnL property.
     * 
     */
    public void setAccountableInPnL(boolean value) {
        this.accountableInPnL = value;
    }

    /**
     * Gets the value of the amount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * Sets the value of the amount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAmount(BigDecimal value) {
        this.amount = value;
    }

    /**
     * Gets the value of the comment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the source property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSource() {
        return source;
    }

    /**
     * Sets the value of the source property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSource(String value) {
        this.source = value;
    }

    /**
     * Gets the value of the errorType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getErrorType() {
        return errorType;
    }

    /**
     * Sets the value of the errorType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setErrorType(String value) {
        this.errorType = value;
    }

    /**
     * Gets the value of the errorMsg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * Sets the value of the errorMsg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setErrorMsg(String value) {
        this.errorMsg = value;
    }

    /**
     * Gets the value of the createdBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setCreatedBy(IdCodeName value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link ExpenseStatus }
     *     
     */
    public ExpenseStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link ExpenseStatus }
     *     
     */
    public void setStatus(ExpenseStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the createdOn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getCreatedOn() {
        return createdOn;
    }

    /**
     * Sets the value of the createdOn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreatedOn(Date value) {
        this.createdOn = value;
    }

    /**
     * Gets the value of the cancelledBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getCancelledBy() {
        return cancelledBy;
    }

    /**
     * Sets the value of the cancelledBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setCancelledBy(IdCodeName value) {
        this.cancelledBy = value;
    }

    /**
     * Gets the value of the cancelledOn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getCancelledOn() {
        return cancelledOn;
    }

    /**
     * Sets the value of the cancelledOn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCancelledOn(Date value) {
        this.cancelledOn = value;
    }

    /**
     * Gets the value of the cancel property.
     * 
     */
    public boolean isCancel() {
        return cancel;
    }

    /**
     * Sets the value of the cancel property.
     * 
     */
    public void setCancel(boolean value) {
        this.cancel = value;
    }

    /**
     * Gets the value of the cancellationReason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCancellationReason() {
        return cancellationReason;
    }

    /**
     * Sets the value of the cancellationReason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCancellationReason(String value) {
        this.cancellationReason = value;
    }

}
