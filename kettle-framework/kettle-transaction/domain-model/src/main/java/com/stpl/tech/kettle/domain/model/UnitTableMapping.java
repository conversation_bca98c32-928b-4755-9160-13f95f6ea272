package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class UnitTableMapping implements Serializable, Comparable<UnitTableMapping> {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private int tableRequestId;
	private int unitId;
	private int tableNumber;
	private Integer customerId;
	private String contact;
	private String customerName;
	private Integer totalOrders;
	private Integer totalAmount;
	private TableStatus tableStatus;
	private List<TableOrder> orders;

	public UnitTableMapping() {
		// TODO Auto-generated constructor stub
	}

	public UnitTableMapping(int tableRequestId, int unitId, int tableNumber, Integer customerId, String customerName,
			Integer totalOrders, Integer totalAmount, TableStatus status, String contact) {
		super();
		this.tableRequestId = tableRequestId;
		this.unitId = unitId;
		this.tableNumber = tableNumber;
		this.customerId = customerId;
		this.customerName = customerName;
		this.totalOrders = totalOrders;
		this.totalAmount = totalAmount;
		this.tableStatus = status;
		this.contact = contact;
	}

	public UnitTableMapping(int unitId, int tableNumber, TableStatus status) {
		super();
		this.unitId = unitId;
		this.tableNumber = tableNumber;
		this.tableStatus = status;
	}

	public int getTableRequestId() {
		return tableRequestId;
	}

	public void setTableRequestId(int tableRequestId) {
		this.tableRequestId = tableRequestId;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public int getTableNumber() {
		return tableNumber;
	}

	public void setTableNumber(int tableNumber) {
		this.tableNumber = tableNumber;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public Integer getTotalOrders() {
		return totalOrders;
	}

	public void setTotalOrders(Integer totalOrders) {
		this.totalOrders = totalOrders;
	}

	public Integer getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(Integer totalAmount) {
		this.totalAmount = totalAmount;
	}

	public TableStatus getTableStatus() {
		return tableStatus;
	}

	public void setTableStatus(TableStatus tableStatus) {
		this.tableStatus = tableStatus;
	}

	public List<TableOrder> getOrders() {
		if (orders == null) {
			orders = new ArrayList<>();
		}
		return orders;
	}

	public void setOrders(List<TableOrder> orders) {
		this.orders = orders;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	@Override
	public int compareTo(UnitTableMapping o) {
		return Integer.valueOf(this.getTableNumber()).compareTo(o.getTableNumber());
	}

}
