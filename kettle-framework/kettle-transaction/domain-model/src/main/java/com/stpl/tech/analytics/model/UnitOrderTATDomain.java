package com.stpl.tech.analytics.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UnitOrderTATDomain implements Serializable {

    private static final long serialVersionUID = -143925834784606413L;

    private int unitId;

    private int brandId;

    private String type;

    private int totalOrders;

    private int delayedOrders;

    private boolean delayOrdrsExcd;

    private BigDecimal avgTAT;

    private boolean tatExcd;

    private Date businessDate;
}
