//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.12 at 06:15:55 PM IST
//

package com.stpl.tech.analytics.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Java class for anonymous complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="year" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="month" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="weekDay" type="{http://www.w3schools.com}UnitReportData" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="weekEnd" type="{http://www.w3schools.com}UnitReportData" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "year", "month", "weekDay", "weekEnd" })
@XmlRootElement(name = "TargetReportData")
@Document(collection = "TargetReportData")
public class TargetReportData implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = -8589946658652317854L;

	@Id
	private String _id;

	/*@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/
	@Field
	protected int year;
	@XmlElement(required = true)
	@Field
	protected String month;
	@Field
	protected List<UnitReportData> weekDay;
	@Field
	protected List<UnitReportData> weekEnd;

	/**
	 * @return the _id
	 */
	public String get_id() {
		return _id;
	}

	/**
	 * @param _id
	 *            the _id to set
	 */
	public void set_id(String _id) {
		this._id = _id;
	}

	/**
	 * @return the version
	public Long getVersion() {
		return version;
	}

	*//**
	 * @param version
	 *            the version to set
	 *//*
	public void setVersion(Long version) {
		this.version = version;
	}

	*//**
	 * @return the detachAll
	 *//*
	public String getDetachAll() {
		return detachAll;
	}

	*//**
	 * @param detachAll
	 *            the detachAll to set
	 *//*
	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}
*/
	/**
	 * Gets the value of the year property.
	 *
	 */
	public int getYear() {
		return year;
	}

	/**
	 * Sets the value of the year property.
	 *
	 */
	public void setYear(int value) {
		this.year = value;
	}

	/**
	 * Gets the value of the month property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getMonth() {
		return month;
	}

	/**
	 * Sets the value of the month property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setMonth(String value) {
		this.month = value;
	}

	/**
	 * Gets the value of the weekDay property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the weekDay property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getWeekDay().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link UnitReportData }
	 *
	 *
	 */
	public List<UnitReportData> getWeekDay() {
		if (weekDay == null) {
			weekDay = new ArrayList<UnitReportData>();
		}
		return this.weekDay;
	}

	/**
	 * Gets the value of the weekEnd property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the weekEnd property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getWeekEnd().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link UnitReportData }
	 *
	 *
	 */
	public List<UnitReportData> getWeekEnd() {
		if (weekEnd == null) {
			weekEnd = new ArrayList<UnitReportData>();
		}
		return this.weekEnd;
	}

}
