/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.19 at 02:10:56 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for InventoryEventType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="InventoryEventType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="STOCK_IN"/&gt;
 *     &lt;enumeration value="WASTAGE"/&gt;
 *     &lt;enumeration value="SNAPSHOT"/&gt;
 *     &lt;enumeration value="TRANSFER_OUT"/&gt;
 *     &lt;enumeration value="UPDATE"/&gt;
 *     &lt;enumeration value="STOCK_OUT"/&gt;
 *     &lt;enumeration value="ORDER_PUNCH"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "InventoryEventType")
@XmlEnum
public enum InventoryEventType {

    @XmlEnumValue("STOCK_IN")
    STOCK_IN("STOCK_IN"),
    WASTAGE("WASTAGE"),
    SNAPSHOT("SNAPSHOT"),
    @XmlEnumValue("TRANSFER_OUT")
    TRANSFER_OUT("TRANSFER_OUT"),
    UPDATE("UPDATE"),
    @XmlEnumValue("STOCK_OUT")
    STOCK_OUT("STOCK_OUT"),
    @XmlEnumValue("ORDER_PUNCH")
    ORDER_PUNCH("ORDER_PUNCH"),
    @XmlEnumValue("CAFE_WASTAGE")
    CAFE_WASTAGE("CAFE_WASTAGE");
    private final String value;

    InventoryEventType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static InventoryEventType fromValue(String v) {
        for (InventoryEventType c: InventoryEventType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
