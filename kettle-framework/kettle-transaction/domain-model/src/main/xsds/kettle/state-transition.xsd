<?xml version="1.0"?>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">

	<xs:element name="TransitionStateData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="states" type="StateData" minOccurs="1"
					maxOccurs="unbounded" />
				<xs:element name="transitions" type="TransitionState" minOccurs="1"
					maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="TransitionData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="fromStateCode" type="xs:string" />
				<xs:element name="toStateCode" type="xs:string" />
				<xs:element name="event" type="TransitionEvent" />
				<xs:element name="status" type="TransitionStatus" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="TransitionState">
		<xs:sequence>
			<xs:element name="state" type="xs:string" />
			<xs:element name="nextStates" type="xs:string" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StateData">
		<xs:sequence>
			<xs:element name="stateCode" type="xs:string" />
			<xs:element name="stateName" type="xs:string" />
			<xs:element name="stateType" type="StateType" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TransitionEvent">
		<xs:sequence>
			<xs:element name="eventCode" type="xs:string" />
			<xs:element name="eventName" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="TransitionStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SUCCESS" />
			<xs:enumeration value="FAILURE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="StateType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="START" />
			<xs:enumeration value="INTERMEDIATE" />
			<xs:enumeration value="TRANSIENT" />
			<xs:enumeration value="END" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
