<?xml version="1.0"?>
<!-- ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL ~ __________________ 
	~ ~ [2015] - [2017] Sunshine Teahouse Private Limited ~ All Rights Reserved. 
	~ ~ NOTICE: All information contained herein is, and remains ~ the property 
	of Sunshine Teahouse Private Limited and its suppliers, ~ if any. The intellectual 
	and technical concepts contained ~ herein are proprietary to Sunshine Teahouse 
	Private Limited ~ and its suppliers, and are protected by trade secret or 
	copyright law. ~ Dissemination of this information or reproduction of this 
	material ~ is strictly forbidden unless prior written permission is obtained 
	~ from Sunshine Teahouse Private Limited. -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">
	<xs:element name="Unit">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="name" type="xs:string" />
				<xs:element name="region" type="UnitRegion" />
				<xs:element name="family" type="UnitCategory" />
				<xs:element name="subCategory" type="UnitSubCategory" />
				<xs:element name="status" type="UnitStatus" />
				<xs:element name="unitEmail" type="xs:string" />
				<xs:element name="startDate" type="xs:date" nillable="true" />
				<xs:element name="lastBusinessDate" type="xs:date"
					nillable="true" />
				<xs:element name="tin" type="xs:string" />
				<xs:element name="division" type="Division" />
				<xs:element name="address" type="Address" />
				<xs:element name="cloneUnitId" type="xs:int" nillable="true" />
				<xs:element name="inventoryCloneUnitId" type="xs:int"
					nillable="true" />
				<xs:element name="noOfTerminals" type="xs:int" default="1"
					nillable="false" />
				<xs:element name="noOfTakeawayTerminals" type="xs:int"
					default="0" nillable="false" />
				<xs:element name="workstationEnabled" type="xs:boolean" />
				<xs:element name="noOfTables" type="xs:int" default="1"
					nillable="false" />
				<!--<xs:element name="products" type="Product" minOccurs="1" maxOccurs="unbounded"/> -->
				<xs:element name="deliveryPartners" type="PartnerDetail"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="taxProfiles" type="TaxProfile"
					minOccurs="1" maxOccurs="unbounded" />
				<xs:element name="operationalHours" type="UnitHours"
					minOccurs="1" maxOccurs="7" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="UnitHours">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="dayOfTheWeekNumber" type="xs:int" />
			<xs:element name="dayOfTheWeek" type="xs:string" />
			<xs:element name="noOfShifts" type="xs:int" />
			<xs:element name="isOperational" type="xs:boolean" />
			<xs:element name="hasDelivery" type="xs:boolean" />
			<xs:element name="hasDineIn" type="xs:boolean" />
			<xs:element name="hasTakeAway" type="xs:boolean" />
			<xs:element name="dineInOpeningTime" type="xs:time" />
			<xs:element name="dineInClosingTime" type="xs:time" />
			<xs:element name="deliveryOpeningTime" type="xs:time" />
			<xs:element name="deliveryClosingTime" type="xs:time" />
			<xs:element name="takeAwayOpeningTime" type="xs:time" />
			<xs:element name="takeAwayClosingTime" type="xs:time" />
			<xs:element name="shiftOneHandoverTime" type="xs:time" />
			<xs:element name="shiftTwoHandoverTime" type="xs:time" />
			<xs:element name="status" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:element name="TransactionMetadata">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="categories" type="ListData" minOccurs="0"
					maxOccurs="unbounded" />
				<xs:element name="discountCodes" type="ListData" />
				<xs:element name="complimentaryCodes" type="ListData" />
				<xs:element name="addOns" type="AddonList" minOccurs="1"
					maxOccurs="unbounded" />
				<xs:element name="paymentModes" type="PaymentMode"
					minOccurs="1" maxOccurs="unbounded" />
				<xs:element name="channelPartner" type="IdCodeName"
					minOccurs="1" maxOccurs="unbounded" />
				<xs:element name="deliveryPartner" type="IdCodeName"
					minOccurs="1" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="UnitClosure">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="unitId" type="xs:int" />
				<xs:element name="businessDate" type="xs:date" />
				<xs:element name="startTime" type="xs:date" />
				<xs:element name="endTime" type="xs:date" />
				<xs:element name="lastOrderId" type="xs:int" />
				<xs:element name="startOrderId" type="xs:int" />
				<xs:element name="employeeId" type="xs:int" />
				<xs:element name="comment" type="xs:string" />
				<xs:element name="currentStatus" type="ClosureStatus" />
				<xs:element name="allStatus" type="ClosureStatus"
					minOccurs="1" maxOccurs="unbounded" />
				<xs:element name="paymentClosures" type="ClosurePaymentDetail"
					minOccurs="1" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="ReportDef">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="name" type="xs:string" />
				<xs:element name="description" type="xs:string" />
				<xs:element name="type" type="xs:string" />
				<xs:element name="autoTriggered" type="xs:boolean" />
				<xs:element name="frequency" type="xs:string" />
				<xs:element name="multipleSection" type="xs:boolean" />
				<xs:element name="attributes" type="ReportAttribute"
					minOccurs="1" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="ReportExecution">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="reportDefId" type="xs:int" />
				<xs:element name="unitId" type="xs:int" />
				<xs:element name="startTime" type="xs:date" />
				<xs:element name="endtime" type="xs:date" />
				<xs:element name="statuses" type="ReportStatus"
					minOccurs="1" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="ReportStatusEvent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="unitId" type="xs:int" />
				<xs:element name="userId" type="xs:int" />
				<xs:element name="startTime" type="xs:date" />
				<xs:element name="startOrderId" type="xs:int" />
				<xs:element name="endOrderId" type="xs:int" />
				<xs:element name="tenantId" type="xs:string" />
				<xs:element name="terminalId" type="xs:int" />
				<xs:element name="fileNum" type="xs:int" />
				<xs:element name="businessDate" type="xs:date" />
				<xs:element name="eventStatus" type="xs:string" />
				<xs:element name="description" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="Customer">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="firstName" type="xs:string" />
				<xs:element name="middleName" type="xs:string" />
				<xs:element name="lastName" type="xs:string" />
				<xs:element name="countryCode" type="xs:string" />
				<xs:element name="contactNumber" type="xs:string" />
				<xs:element name="emailId" type="xs:string" />
				<xs:element name="emailVerified" type="xs:boolean" />
				<xs:element name="loyaltyPoints" type="xs:int" />
				<xs:element name="registrationUnitId" type="xs:int"
					nillable="true" />
				<xs:element name="acquisitionSource" type="xs:string" />
				<xs:element name="acquisitionToken" type="xs:string" />
				<xs:element name="contactNumberVerified" type="xs:boolean" />
				<xs:element name="availedSignupOffer" type="xs:boolean" />
				<xs:element name="lastOrderId" type="xs:int" />
				<xs:element name="orderCount" type="xs:int" />
				<xs:element name="lastOrderTime" type="xs:date" />
				<xs:element name="smsSubscriber" type="xs:boolean" />
				<xs:element name="emailSubscriber" type="xs:boolean" />
				<xs:element name="loyaltySubscriber" type="xs:boolean" />
				<xs:element name="addresses" type="Address" minOccurs="0"
					maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="CustomerOffer">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="offerDetailId" type="xs:int" />
				<xs:element name="customerId" type="xs:int" />
				<xs:element name="offerCode" type="xs:string" />
				<xs:element name="availTime" type="xs:date" />
				<xs:element name="availed" type="xs:boolean" />
				<xs:element name="orderId" type="xs:int" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="InventoryUpdateEvent">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="currentInventory" type="ProductInventory"
					minOccurs="1" maxOccurs="unbounded" />
				<xs:element name="updatedInventory" type="InventoryEventData"
					minOccurs="1" maxOccurs="unbounded" />
				<xs:element name="type" type="InventoryEventType" />
				<xs:element name="comment" type="xs:string" nillable="true" />
				<xs:element name="updateTime" type="xs:date" />
				<xs:element name="businessDate" type="xs:date" />
				<xs:element name="updatedBy" type="xs:int" />
				<xs:element name="unitId" type="xs:int" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="ReportAttribute">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="code" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="type" type="AttributeType" />
			<xs:element name="stringValue" type="xs:string" />
			<xs:element name="integerValue" type="xs:int" nillable="true" />
			<xs:element name="longValue" type="xs:long" nillable="true" />
			<xs:element name="doubleValue" type="xs:decimal" nillable="true" />
			<xs:element name="booleanValue" type="xs:boolean"
				nillable="true" />
			<xs:element name="dateValue" type="xs:date" nillable="true" />
			<xs:element name="timestampValue" type="xs:date" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ReportStatus">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="executionId" type="xs:int" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="updateTimestamp" type="xs:date" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Employee">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="gender" type="xs:string" />
			<xs:element name="primaryContact" type="xs:string" />
			<xs:element name="secondaryContact" type="xs:string" />
			<xs:element name="biometricId" type="xs:string" nillable="true" />
			<xs:element name="department" type="Department" />
			<xs:element name="designation" type="Designation" />
			<xs:element name="currentAddress" type="Address" />
			<xs:element name="permanentAddress" type="Address" />
			<xs:element name="employmentType" type="EmploymentType" />
			<xs:element name="employmentStatus" type="EmploymentStatus" />
			<xs:element name="joiningDate" type="xs:date" />
			<xs:element name="reportingManager" type="Employee" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Order">
		<xs:sequence>
			<xs:element name="orderId" type="xs:int" nillable="true" />
			<xs:element name="generateOrderId" type="xs:string"
				nillable="true" />
			<xs:element name="unitOrderId" type="xs:int" nillable="true" />
			<xs:element name="campaignId" type="xs:string" nillable="true" />
			<xs:element name="customerId" type="xs:int" nillable="true" />
			<xs:element name="employeeId" type="xs:int" />
			<xs:element name="pointsRedeemed" type="xs:int" />
			<xs:element name="source" type="xs:string" nillable="true" />
			<xs:element name="sourceId" type="xs:string" nillable="true" />
			<xs:element name="hasParcel" type="xs:boolean" default="false" />
			<xs:element name="status" type="OrderStatus" default="INITIATED" />
			<xs:element name="orders" type="OrderItem" minOccurs="0"
				maxOccurs="unbounded" />
			<xs:element name="enquiryItems" type="EnquiryItem"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="transactionDetail" type="TransactionDetail" />
			<xs:element name="printCount" type="xs:int" />
			<xs:element name="settlementType" type="SettlementType"
				default="DEBIT" />
			<xs:element name="settlements" type="Settlement"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="unitName" type="xs:string" nillable="true" />
			<xs:element name="terminalId" type="xs:int" nillable="true" />
			<xs:element name="billStartTime" type="xs:date" />
			<xs:element name="billCreationTime" type="xs:date" />
			<xs:element name="billCreationSeconds" type="xs:int" />
			<xs:element name="billingServerTime" type="xs:date" />
			<xs:element name="channelPartner" type="xs:int" />
			<xs:element name="deliveryPartner" type="xs:int" />
			<xs:element name="subscriptionDetail" type="Subscription"
				nillable="true" />
			<xs:element name="offerCode" type="xs:string" nillable="true" />
			<xs:element name="reprints" type="ActionDetail" minOccurs="0"
				maxOccurs="unbounded" />
			<xs:element name="cancellationDetails" type="ActionDetail"
				nillable="true" />
			<xs:element name="orderRemark" type="xs:string" nillable="true" />
			<xs:element name="deliveryAddress" type="xs:int" nillable="true" />
			<xs:element name="customerName" type="xs:string" nillable="true" />
			<xs:element name="metadataList" type="OrderMetadata"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SubscriptionEvent">
		<xs:sequence>
			<xs:element name="subscriptionEventId" type="xs:int" />
			<xs:element name="subscriptionDetail" type="Order" />
			<xs:element name="orderDetail" type="Order" />
			<xs:element name="status" type="SubscriptionEventStatus" />
			<xs:element name="retryCount" type="xs:int" />
			<xs:element name="eventId" type="xs:int" />
			<xs:element name="originalDeliveryTime" type="xs:date" />
			<xs:element name="actualDeliveryTime" type="xs:date" />
			<xs:element name="eventSource" type="SubscriptionEventSource"
				default="AUTOMATED" />
			<xs:element name="reason" type="xs:string" />
			<xs:element name="addTime" type="xs:date" />
			<xs:element name="regularTimeChanged" type="xs:boolean" />
			<xs:element name="eventDate" type="xs:date" />
			<xs:element name="eventTime" type="xs:date" />
			<xs:element name="lastUpdateTime" type="xs:date" />
			<xs:element name="timeUpdated" type="xs:boolean" default="false" />
			<xs:element name="remark" type="xs:string" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SubscriptionStatusEvents">
		<xs:sequence>
			<xs:element name="subscriptionEventId" type="xs:int" />
			<xs:element name="subscriptionDetail" type="xs:int" />
			<xs:element name="eventStatus" type="xs:string" />
			<xs:element name="eventType" type="xs:string" />
			<xs:element name="reasonText" type="xs:string" />
			<xs:element name="generatedBy" type="xs:int" />
			<xs:element name="addTime" type="xs:date" />
			<xs:element name="eventStartDate" type="xs:date" />
			<xs:element name="eventEndDate" type="xs:date" />
			<xs:element name="lastUpdateTime" type="xs:date" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Subscription">
		<xs:sequence>
			<xs:element name="subscriptionId" type="xs:int" />
			<xs:element name="subscriptionStatus" type="SubscriptionStatus" />
			<xs:element name="startDate" type="xs:date" />
			<xs:element name="endDate" type="xs:date" />
			<xs:element name="type" type="SubscriptionType" />
			<xs:element name="emailNotification" type="xs:boolean" />
			<xs:element name="smsNotification" type="xs:boolean" />
			<xs:element name="automatedDelivery" type="xs:boolean"
				default="true" />
			<xs:element name="daysOfTheMonth" type="xs:int" minOccurs="0"
				maxOccurs="7" />
			<xs:element name="daysOfTheWeek" type="xs:int" minOccurs="0"
				maxOccurs="7" />
			<xs:element name="timeOfTheDay" type="xs:int" minOccurs="1"
				maxOccurs="96" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PullPacket">
		<xs:sequence>
			<xs:element name="pullPacketId" type="xs:int" />
			<xs:element name="createdBy" type="Employee" />
			<xs:element name="witnessedBy" type="xs:string" nillable="true" />
			<xs:element name="paymentMode" type="PaymentMode" />
			<xs:element name="pullAmount" type="xs:decimal" />
			<xs:element name="comment" type="xs:string" nillable="true" />
			<xs:element name="pullPacketStatus" type="PullPacketStatus" />
			<xs:element name="pullDate" type="xs:date" />
			<xs:element name="pullPendingReason" type="xs:string"
				nillable="true" />
			<xs:element name="pullSource" type="xs:string" />
			<xs:element name="pullPacketUnit" type="UnitBasicDetail" />
			<xs:element name="closurePaymentDetail" type="ClosurePaymentDetail" />
			<xs:element name="pullDenominations" type="PullPacketDenomination"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="pullSettlementDetail" type="PullSettlementDetail"
				nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PullPacketDenomination">
		<xs:sequence>
			<xs:element name="pullDenominationId" type="xs:int" />
			<xs:element name="packetCount" type="xs:int" nillable="true" />
			<xs:element name="looseCurrencyCount" type="xs:int"
				nillable="true" />
			<xs:element name="totalAmount" type="xs:decimal" nillable="true" />
			<xs:element name="denominationDetail" type="DenominationDetail" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PullSettlementDenomination">
		<xs:sequence>
			<xs:element name="settlementDenominationId" type="xs:int" />
			<xs:element name="packetCount" type="xs:int" nillable="true" />
			<xs:element name="looseCurrencyCount" type="xs:int"
				nillable="true" />
			<xs:element name="totalAmount" type="xs:decimal" nillable="true" />
			<xs:element name="denominationDetail" type="DenominationDetail" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DenominationDetail">
		<xs:sequence>
			<xs:element name="denominationId" type="xs:int" />
			<xs:element name="denominationCode" type="xs:string" />
			<xs:element name="denominationText" type="xs:string" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="displayOrder" type="xs:int" />
			<xs:element name="denominationValue" type="xs:int" />
			<xs:element name="bundleSize" type="xs:int" />
			<xs:element name="paymentMode" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderPaymentDenomination">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="orderId" type="xs:int" />
			<xs:element name="settlementId" type="xs:int" />
			<xs:element name="denominationDetailId" type="xs:int" />
			<xs:element name="count" type="xs:int" />
			<xs:element name="totalAmount" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="PullPacketStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INITIATED" />
			<xs:enumeration value="CREATED" />
			<xs:enumeration value="TRANSFERRED" />
			<xs:enumeration value="SETTLED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ClosureStatus">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="closureId" type="xs:int" />
			<xs:element name="state" type="ClosureState" />
			<xs:element name="updateTime" type="xs:date" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PullSettlementDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="settlementUnit" type="UnitBasicDetail" />
			<xs:element name="settlementTime" type="xs:dateTime"
				nillable="true" />
			<xs:element name="settlementServiceProvider" type="xs:string"
				nillable="true" />
			<xs:element name="settlementAmount" type="xs:decimal"
				nillable="true" />
			<xs:element name="settlementType" type="PaymentMode" />
			<xs:element name="settlementServiceProviderReceipt" type="xs:string"
				nillable="true" />
			<xs:element name="unsettledAmount" type="xs:decimal"
				nillable="true" />
			<xs:element name="totalAmount" type="xs:decimal" />
			<xs:element name="closingAmount" type="xs:decimal"
				nillable="true" />
			<xs:element name="settlementStatus" type="SettlementStatus" />
			<xs:element name="settlementClosingReceipt" type="xs:string"
				nillable="true" />
			<xs:element name="settlementReceiptPath" type="xs:string"
				nillable="true" />
			<xs:element name="pullDetails" type="PullPacket"
				minOccurs="1" maxOccurs="unbounded" />
			<xs:element name="settlementDenominations" type="PullSettlementDenomination"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="SettlementStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="UNSETTLED" />
			<xs:enumeration value="SETTLED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ActionDetail">
		<xs:sequence>
			<xs:element name="reason" type="xs:string" nillable="true" />
			<xs:element name="approvedBy" type="xs:int" nillable="true" />
			<xs:element name="generatedBy" type="xs:int" nillable="true" />
			<xs:element name="actionTime" type="xs:date" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ClosurePaymentDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="closureId" type="xs:int" />
			<xs:element name="reconState" type="ReconState" nillable="true" />
			<xs:element name="paymentModeId" type="xs:int" />
			<xs:element name="expectedAmount" type="xs:decimal" />
			<xs:element name="actualAmount" type="xs:decimal" />
			<xs:element name="comment" type="xs:string" />
			<xs:element name="gmv" type="xs:decimal" />
			<xs:element name="discount" type="xs:decimal" />
			<xs:element name="netSalesAmount" type="xs:decimal" />
			<xs:element name="roundOff" type="xs:decimal" />
			<xs:element name="totalAmount" type="xs:decimal" />
			<xs:element name="billCount" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ListData">
		<xs:sequence>
			<xs:element name="detail" type="IdCodeName" />
			<xs:element name="content" type="IdCodeName" minOccurs="1"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AddonList">
		<xs:sequence>
			<xs:element name="detail" type="IdCodeName" />
			<xs:element name="content" type="AddonData" minOccurs="1"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PaymentMode">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="type" type="xs:string" />
			<xs:element name="settlementType" type="xs:string" />
			<xs:element name="generatePull" type="xs:boolean" />
			<xs:element name="denominations" type="DenominationDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Department">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="division" type="Division" />
			<xs:element name="designations" type="Designation"
				minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Designation">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="Address">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="line1" type="xs:string" />
			<xs:element name="line2" type="xs:string" nillable="true" />
			<xs:element name="line3" type="xs:string" nillable="true" />
			<xs:element name="locality" type="xs:string" />
			<xs:element name="city" type="xs:string" />
			<xs:element name="state" type="xs:string" />
			<xs:element name="country" type="xs:string" />
			<xs:element name="zipCode" type="xs:string" nillable="true" />
			<xs:element name="contact1" type="xs:string" />
			<xs:element name="contact2" type="xs:string" nillable="true" />
			<xs:element name="addressType" type="xs:string" />
			<xs:element name="company" type="xs:string" nillable="true" />
			<xs:element name="latitude" type="xs:string" nillable="true" />
			<xs:element name="longitude" type="xs:string" nillable="true" />
			<xs:element name="preferredAddress" type="xs:boolean"
				nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Division">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="category" type="xs:string" />
			<xs:element name="company" type="Company" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Company">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="cin" type="xs:string" />
			<xs:element name="serviceTaxNumber" type="xs:string" />
			<xs:element name="websiteAddress" type="xs:string" />
			<xs:element name="registeredAddress" type="Address" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="BillType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MRP" />
			<xs:enumeration value="NET_PRICE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ProductPrice">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="dimension" type="xs:string" />
			<xs:element name="price" type="xs:decimal" />
			<xs:element name="cost" type="xs:decimal" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ProductInventory">
		<xs:sequence>
			<xs:element name="unit" type="UnitBasicDetail" />
			<xs:element name="product" type="ProductBasicDetail" />
			<xs:element name="lastStockOutTime" type="xs:date"
				nillable="true" />
			<xs:element name="lastUpdatedTime" type="xs:date"
				nillable="true" />
			<xs:element name="thresholdData" type="InventoryThresholdData"
				nillable="true" />
			<xs:element name="quantity" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="InventoryEventData">
		<xs:sequence>
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="productId" type="xs:int" />
			<xs:element name="quantity" type="xs:int" />
			<xs:element name="thresholdQuantity" type="xs:int"
				nillable="true" />
			<xs:element name="type" type="InventoryEventType" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="InventoryThresholdData">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="productId" type="xs:int" />
			<xs:element name="dayOfTheWeek" type="xs:int" />
			<xs:element name="minQuantity" type="xs:int" />
			<xs:element name="maxQuantity" type="xs:int" />
			<xs:element name="avgQuantity" type="xs:int" />
			<xs:element name="totalQuantity" type="xs:int" />
			<xs:element name="totalDays" type="xs:int" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="lastUpdateTime" type="xs:date" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ProductBasicDetail">
		<xs:sequence>
			<xs:element name="detail" type="IdCodeName" />
			<xs:element name="type" type="xs:int" />
			<xs:element name="subType" type="xs:int" />
			<xs:element name="isInventoryTracked" type="xs:boolean" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitBasicDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="status" type="UnitStatus" />
			<xs:element name="noOfTerminal" type="xs:int" />
			<xs:element name="noOfTakeawayTerminals" type="xs:int" />
			<xs:element name="category" type="UnitCategory" />
			<xs:element name="region" type="UnitRegion" />
			<xs:element name="city" type="xs:string" />
			<xs:element name="latitude" type="xs:string" nillable="true" />
			<xs:element name="longitude" type="xs:string" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderItem">
		<xs:sequence>
			<xs:element name="itemId" type="xs:int" />
			<xs:element name="productId" type="xs:int" />
			<xs:element name="productName" type="xs:string" />
			<xs:element name="productCategory" type="IdCodeName" />
			<xs:element name="productSubCategory" type="IdCodeName" />
			<xs:element name="quantity" type="xs:int" />
			<xs:element name="price" type="xs:decimal" />
			<xs:element name="totalAmount" type="xs:decimal" />
			<xs:element name="amount" type="xs:decimal" />
			<xs:element name="discountDetail" type="DiscountDetail"
				nillable="true" />
			<xs:element name="complimentaryDetail" type="ComplimentaryDetail" />
			<xs:element name="addons" type="AddonData" nillable="true"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="dimension" type="xs:string" nillable="true" />
			<xs:element name="billType" type="BillType" default="NET_PRICE" />
			<xs:element name="recipeId" type="xs:int" />
			<xs:element name="itemCode" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EnquiryItem">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="orderedQuantity" type="xs:int" />
			<xs:element name="availableQuantity" type="xs:int" />
			<xs:element name="replacementServed" type="xs:boolean" />
			<xs:element name="linkedOrderId" type="xs:int" />
			<xs:element name="linkedCustomerId" type="xs:int" />
			<xs:element name="linkedUnitId" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DiscountDetail">
		<xs:sequence>
			<xs:element name="discountCode" type="xs:int" nillable="true" />
			<xs:element name="discountReason" type="xs:string"
				nillable="true" />
			<xs:element name="discount" type="PercentageDetail"
				nillable="true" />
			<xs:element name="promotionalOffer" type="xs:decimal"
				nillable="true" />
			<xs:element name="totalDiscount" type="xs:decimal"
				nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ComplimentaryDetail">
		<xs:sequence>
			<xs:element name="isComplimentary" type="xs:boolean"
				default="false" />
			<xs:element name="reasonCode" type="xs:int" nillable="true" />
			<xs:element name="reason" type="xs:string" nillable="true" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="IdCodeName">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="code" type="xs:string" />
			<xs:element name="shortCode" type="xs:string" nillable="true" />
			<xs:element name="type" type="xs:string" nillable="true" />
			<xs:element name="status" type="xs:string" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AddonData">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="code" type="xs:string" />
			<xs:element name="shortCode" type="xs:string" nillable="true" />
			<xs:element name="linkedProductId" type="xs:int" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderFeedback">
		<xs:sequence>
			<xs:element name="deliveryId" type="xs:string" />
			<xs:element name="generatedOrderId" type="xs:string" />
			<xs:element name="customerName" type="xs:string" />
			<xs:element name="customerContact" type="xs:string" />
			<xs:element name="billAmount" type="xs:float" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OrderMetadata">
		<xs:sequence>
			<xs:element name="metadataId" type="xs:int" />
			<xs:element name="orderId" type="xs:int" />
			<xs:element name="attributeName" type="xs:string" />
			<xs:element name="attributeValue" type="xs:string"
				nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TransactionDetail">
		<xs:sequence>
			<xs:element name="saleAmount" type="xs:decimal" />
			<xs:element name="totalAmount" type="xs:decimal" />
			<xs:element name="taxableAmount" type="xs:decimal" />
			<xs:element name="discountDetail" type="DiscountDetail"
				nillable="true" />
			<xs:element name="paidAmount" type="xs:decimal" />
			<xs:element name="roundOffValue" type="xs:decimal" />
			<xs:element name="savings" type="xs:decimal" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PercentageDetail">
		<xs:sequence>
			<xs:element name="percentage" type="xs:decimal" />
			<xs:element name="value" type="xs:decimal" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PartnerDetail">
		<xs:sequence>
			<xs:element name="detail" type="IdCodeName" />
			<xs:element name="priority" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="SettlementType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DEBIT" />
			<xs:enumeration value="CREDIT" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="Settlement">
		<xs:sequence>
			<xs:element name="settlementId" type="xs:int" />
			<xs:element name="mode" type="xs:int" />
			<xs:element name="modeDetail" type="PaymentMode" nillable="true" />
			<xs:element name="amount" type="xs:decimal" />
			<xs:element name="extraVouchers" type="xs:decimal"
				nillable="true" />
			<xs:element name="denominations" type="OrderPaymentDenomination"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="externalTransactionId" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxProfile">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="profileId" type="xs:int" />
			<xs:element name="percentage" type="xs:decimal" />
			<xs:element name="type" type="TaxType" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="status" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitProductMappingData">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="unit" type="IdCodeName" />
			<xs:element name="product" type="IdCodeName" />
			<xs:element name="price" type="ProductPrice" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="WorkstationLog">
		<xs:sequence>
			<xs:element name="workstationLogId" type="xs:int" />
			<xs:element name="orderId" type="xs:int" nillable="false" />
			<xs:element name="itemId" type="xs:long" nillable="false" />
			<xs:element name="billCreationTime" type="xs:long" />
			<xs:element name="unitId" type="xs:int" nillable="false" />
			<xs:element name="type" type="xs:string" nillable="false" />
			<xs:element name="employeeId" type="xs:int" nillable="false" />
			<xs:element name="productId" type="xs:int" nillable="false" />
			<xs:element name="itemQuantity" type="xs:int" />
			<xs:element name="dimension" type="xs:string" nillable="false" />
			<xs:element name="orderSource" type="xs:string" />
			<xs:element name="timeToAcknowledge" type="xs:int" />
			<xs:element name="timeToStart" type="xs:int" />
			<xs:element name="timeToProcess" type="xs:int" />
			<xs:element name="timeToCancel" type="xs:int" />
			<xs:element name="timeToProcessByMachine" type="xs:int" />
			<xs:element name="cooktopStation" type="xs:int" />
			<xs:element name="stationTasksForOrder" type="xs:int" />
			<xs:element name="dispatched" type="xs:boolean" />
			<xs:element name="cancelled" type="xs:boolean" />
			<xs:element name="monkName" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AssemblyLog">
		<xs:sequence>
			<xs:element name="assemblyLogId" type="xs:int" />
			<xs:element name="billingServerTime" type="xs:long" />
			<xs:element name="unitId" type="xs:int" nillable="false" />
			<xs:element name="orderSource" type="xs:string" />
			<xs:element name="channelPartner" type="xs:string" />
			<xs:element name="deliveryPartner" type="xs:string" />
			<xs:element name="timeToAcknowledge" type="xs:int" />
			<xs:element name="timeToProcessHot" type="xs:int" />
			<xs:element name="timeToProcessCold" type="xs:int" />
			<xs:element name="timeToProcessFood" type="xs:int" />
			<xs:element name="timeToProcessByWorkstations" type="xs:int" />
			<xs:element name="timeToReadyForDispatch" type="xs:int" />
			<xs:element name="timeToDispatch" type="xs:int" />
			<xs:element name="cooktopStation" type="xs:int" />
			<xs:element name="stationEventsForOrder" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitExpense">
		<xs:sequence>
			<xs:element name="unitExpenseId" type="xs:int" />
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="unitName" type="xs:string" />
			<xs:element name="type" type="xs:string" />
			<xs:element name="year" type="xs:int" />
			<xs:element name="iterationNumber" type="xs:int" />
			<xs:element name="expenseUpdateEventId" type="xs:int" />
			<xs:element name="updatedBy" type="xs:string" />
			<xs:element name="lastUpdateTime" type="xs:date" />
			<xs:element name="manpower" type="xs:decimal" />
			<xs:element name="netSalesAmount" type="xs:decimal" />
			<xs:element name="gmvAmount" type="xs:decimal" />
			<xs:element name="totalTickets" type="xs:int" />
			<xs:element name="netTickets" type="xs:int" />
			<xs:element name="cogs" type="xs:decimal" />
			<xs:element name="consumablesAndUtilities" type="xs:decimal" />
			<xs:element name="marketingAndSampling" type="xs:decimal" />
			<xs:element name="unsatifiedCustomerCost" type="xs:decimal" />
			<xs:element name="employeeMeal" type="xs:decimal" />
			<xs:element name="wastageAndExpired" type="xs:decimal" />
			<xs:element name="deliveryCost" type="xs:decimal" />
			<xs:element name="electricity" type="xs:decimal" />
			<xs:element name="water" type="xs:decimal" />
			<xs:element name="rentDG" type="xs:decimal" />
			<xs:element name="chargesDG" type="xs:decimal" />
			<xs:element name="creditCardCharges" type="xs:decimal" />
			<xs:element name="amexCardCharges" type="xs:decimal" />
			<xs:element name="sodexoCharges" type="xs:decimal" />
			<xs:element name="tktRestaurantCharges" type="xs:decimal" />
			<xs:element name="channelPartnerCharges" type="xs:decimal" />
			<xs:element name="scmRental" type="xs:decimal" />
			<xs:element name="edcMachine" type="xs:decimal" />
			<xs:element name="freightOutward" type="xs:decimal" />
			<xs:element name="convenyance" type="xs:decimal" />
			<xs:element name="staffWelfare" type="xs:decimal" />
			<xs:element name="changeCommission" type="xs:decimal" />
			<xs:element name="courier" type="xs:decimal" />
			<xs:element name="printingAndStationery" type="xs:decimal" />
			<xs:element name="miscExp" type="xs:decimal" />
			<xs:element name="parkingCharges" type="xs:decimal" />
			<xs:element name="cleaningCharges" type="xs:decimal" />
			<xs:element name="newspaper" type="xs:decimal" />
			<xs:element name="rent" type="xs:decimal" />
			<xs:element name="fixedRent" type="xs:decimal" />
			<xs:element name="rentPercentage" type="xs:decimal" />
			<xs:element name="camCharges" type="xs:decimal" />
			<xs:element name="internet" type="xs:decimal" />
			<xs:element name="telephone" type="xs:decimal" />
			<xs:element name="opsCostPercentage" type="xs:decimal" />
			<xs:element name="opsCostTotal" type="xs:decimal" />
			<xs:element name="kitchenCostPercentage" type="xs:decimal" />
			<xs:element name="kitchenCostTotal" type="xs:decimal" />
			<xs:element name="repairAndMaintenanceMinor" type="xs:decimal" />
			<xs:element name="repairAndMaintenanceMajor" type="xs:decimal" />
			<xs:element name="manualAdjustments" type="xs:decimal" />
			<xs:element name="totalCost" type="xs:decimal" />
			<xs:element name="ebitdaPercentage" type="xs:decimal" />
			<xs:element name="comments" type="xs:string" />
			<xs:element name="customerCareCost" type="xs:decimal" />
			<xs:element name="maintenanceTeamCost" type="xs:decimal" />
			<xs:element name="trainingTeamCost" type="xs:decimal" />
			<xs:element name="itTeamCost" type="xs:decimal" />
			<xs:element name="consumablesStationary" type="xs:decimal" />
			<xs:element name="consumablesUniform" type="xs:decimal" />
			<xs:element name="consumablesEquipment" type="xs:decimal" />
			<xs:element name="consumablesCutlery" type="xs:decimal" />
			<xs:element name="msp" type="xs:decimal" />
			<xs:element name="systemRent" type="xs:decimal" />
			<xs:element name="insurance" type="xs:decimal" />
			<xs:element name="paytmCharges" type="xs:decimal" />
			<xs:element name="mobikwikCharges" type="xs:decimal" />
			<xs:element name="FreeChargeCharges" type="xs:decimal" />
			<xs:element name="deliveryTotalTickets" type="xs:decimal" />
			<xs:element name="deliveryNetTickets" type="xs:decimal" />
			<xs:element name="deliveryGMV" type="xs:decimal" />
			<xs:element name="deliverySales" type="xs:decimal" />
			<xs:element name="deliveryCOGS" type="xs:decimal" />
			<xs:element name="deliveryUnsatisfiedCustomerCost" type="xs:decimal" />
			<xs:element name="deliverySampleingAndMarketingCost" type="xs:decimal" />
			<xs:element name="deliveryPaytmCharges" type="xs:decimal" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ExpenseUpdateEvent">
		<xs:sequence>
			<xs:element name="eventId" type="xs:int" />
			<xs:element name="addedByUserId" type="xs:int" />
			<xs:element name="addedByUserName" type="xs:string" />
			<xs:element name="updatedByUserId" type="xs:int" />
			<xs:element name="updatedByUserName" type="xs:string" />
			<xs:element name="eventTimestamp" type="xs:date" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="type" type="IterationType" />
			<xs:element name="year" type="xs:int" />
			<xs:element name="iterationNumber" type="xs:int" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="noOfRows" type="xs:int" />
			<xs:element name="startOrderId" type="xs:int" />
			<xs:element name="endOrderId" type="xs:int" />
			<xs:element name="inputFileName" type="xs:string" />
			<xs:element name="storedFileName" type="xs:string" />
			<xs:element name="errorMessage" type="xs:string" nillable="true" />
			<xs:element name="expenses" type="UnitExpense" minOccurs="0"
				maxOccurs="unbounded" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitExpenseDrilldown">
		<xs:sequence>
			<xs:element name="drilldownId" type="xs:int" />
			<xs:element name="unitExpenseDetailId" type="xs:int" />
			<xs:element name="expenseUpdateEventId" type="xs:int" />
			<xs:element name="referenceId" type="xs:int" />
			<xs:element name="referenceName" type="xs:string" />
			<xs:element name="expenseType" type="xs:string" />
			<xs:element name="expenseValue" type="xs:decimal" />
			<xs:element name="noOfTickets" type="xs:int" />
			<xs:element name="expenseRate" type="xs:decimal" />
			<xs:element name="adjustments" type="xs:decimal" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="InventoryLog">
		<xs:sequence>
			<xs:element name="inventoryLogId" type="xs:int" />
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="productId" type="xs:int" />
			<xs:element name="employeeeId" type="xs:int" />
			<xs:element name="eventType" type="InventoryEventType" />
			<xs:element name="updateTime" type="xs:date" />
			<xs:element name="reasonCode" type="InventoryEventType" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitMetadata">
		<xs:sequence>
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="deliveryPartners" type="PartnerDetail"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="lastBusinessDate" type="xs:date"
				nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CustomerNotfication">
		<xs:sequence>
			<xs:element name="notificationId" type="xs:int" />
			<xs:element name="contact" type="xs:string" />
			<xs:element name="message" type="xs:string" />
			<xs:element name="serviceClient" type="xs:string" />
			<xs:element name="type" type="xs:string" />
			<xs:element name="notificationSent" type="xs:boolean" />
			<xs:element name="notificationTime" type="xs:date" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CashCard">
		<xs:sequence>
			<xs:element name="itemId" type="xs:int" />
			<xs:element name="productName" type="xs:string" />
			<xs:element name="cardValue" type="xs:int" />
			<xs:element name="cardNumber" type="xs:string" />
			<xs:element name="isValid" type="xs:boolean" nillable="true" />
			<xs:element name="error" type="xs:string" nillable="true" />
			<xs:element name="buyerId" type="xs:int" nillable="true" />
			<xs:element name="customerId" type="xs:int" nillable="true" />
			<xs:element name="empId" type="xs:int" nillable="true" />
			<xs:element name="unitId" type="xs:int" nillable="true" />
			<xs:element name="terminalId" type="xs:int" nillable="true" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="ExpenseDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="unitId" type="IdCodeName" />
			<xs:element name="expenseCategory" type="xs:string" />
			<xs:element name="expenseType" type="xs:string" />
			<xs:element name="expenseTypeId" type="xs:int" />
			<xs:element name="budgetCategory" type="xs:string" />
			<xs:element name="budgetCategoryId" type="xs:int" />
			<xs:element name="accountableInPnL" type="xs:boolean" />
			<xs:element name="amount" type="xs:decimal" />
			<xs:element name="comment" type="xs:string" />
			<xs:element name="source" type="xs:string" />
			<xs:element name="errorType" type="xs:string" />
			<xs:element name="errorMsg" type="xs:string" />
			<xs:element name="createdBy" type="IdCodeName" />
			<xs:element name="status" type="ExpenseStatus" />
			<xs:element name="createdOn" type="xs:date" />
			<xs:element name="cancelledBy" type="IdCodeName" />
			<xs:element name="cancelledOn" type="xs:date" />
			<xs:element name="cancel" type="xs:boolean" />
			<xs:element name="cancellationReason" type="xs:string" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="MeterDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="billType" type="xs:string" />
			<xs:element name="meterNo" type="xs:int" />
			<xs:element name="lastReading" type="xs:int" />
			<xs:element name="currentReading" type="xs:int" />
			<xs:element name="calculationIndex" type="CalculationIndexStatus" />
			<xs:element name="createdBy" type="IdCodeName" />
			<xs:element name="status" type="ExpenseStatus" />
			<xs:element name="createdOn" type="xs:date" />
			<xs:element name="cancelledBy" type="IdCodeName" />
			<xs:element name="cancelledOn" type="xs:date" />
			<xs:element name="cancel" type="xs:boolean" />
			<xs:element name="entryType" type="MeterDetailEntryType" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="UnitBudgetExceeded">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="unitId" type="IdCodeName" />
			<xs:element name="expenseLabel" type="xs:string" />
			<xs:element name="expenseType" type="xs:string" />
			<xs:element name="budgetCategory" type="xs:string" />
			<xs:element name="expenseSource" type="xs:string" />
			<xs:element name="notificationType" type="xs:string" />
			<xs:element name="requestedAmount" type="xs:decimal" />
			<xs:element name="currentAmount" type="xs:decimal" />
			<xs:element name="budgetAmount" type="xs:decimal" />
			<xs:element name="createdBy" type="IdCodeName" />
			<xs:element name="createdOn" type="xs:date" />
		</xs:sequence>
	</xs:complexType>

	<xs:simpleType name="CashCardEventStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CARD_VALIDATION_FAILED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="EmploymentStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="IN_ACTIVE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="EmploymentType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FULL_TIME" />
			<xs:enumeration value="PART_TIME" />
			<xs:enumeration value="CONSULTANT" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="OrderStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INITIATED" />
			<xs:enumeration value="CREATED" />
			<xs:enumeration value="PROCESSING" />
			<xs:enumeration value="READY_TO_PARTIALLY_DISPATCH" />
			<xs:enumeration value="READY_TO_DISPATCH" />
			<xs:enumeration value="SETTLED" />
			<xs:enumeration value="CANCELLED_REQUESTED" />
			<xs:enumeration value="CANCELLED" />
			<xs:enumeration value="CLOSED" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="SubscriptionStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INITIATED" />
			<xs:enumeration value="CREATED" />
			<xs:enumeration value="ON_HOLD" />
			<xs:enumeration value="CANCELLED" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="SubscriptionEventStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CREATED" />
			<xs:enumeration value="SUCCESS" />
			<xs:enumeration value="FAILED" />
			<xs:enumeration value="CANCELLED" />
			<xs:enumeration value="SKIPPED" />
			<xs:enumeration value="ON_HOLD" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="SubscriptionEventSource" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MANUAL" />
			<xs:enumeration value="AUTOMATED" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="AttributeType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="LONG" />
			<xs:enumeration value="STRING" />
			<xs:enumeration value="INTEGER" />
			<xs:enumeration value="DOUBLE" />
			<xs:enumeration value="BOOLEAN" />
			<xs:enumeration value="DATE" />
			<xs:enumeration value="TIMESTAMP" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ClosureState" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INITIATED" />
			<xs:enumeration value="IN_PROGRESS" />
			<xs:enumeration value="CANCELLED" />
			<xs:enumeration value="SUCCESSFUL" />
			<xs:enumeration value="FAILED" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ReconState" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="HAS_BREAKS" />
			<xs:enumeration value="NO_BREAKS" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="TaxType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NET_PRICE_VAT" />
			<xs:enumeration value="MRP_VAT" />
			<xs:enumeration value="SURCHARGE" />
			<xs:enumeration value="SERVICE_TAX" />
			<xs:enumeration value="SB_CESS" />
			<xs:enumeration value="KK_CESS" />
			<xs:enumeration value="GST" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="InventoryEventType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="STOCK_IN" />
			<xs:enumeration value="WASTAGE" />
			<xs:enumeration value="SNAPSHOT" />
			<xs:enumeration value="TRANSFER_OUT" />
			<xs:enumeration value="UPDATE" />
			<xs:enumeration value="STOCK_OUT" />
			<xs:enumeration value="ORDER_PUNCH" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ApplicationType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="WorkStation" />
			<xs:enumeration value="Customer" />
			<xs:enumeration value="Transaction" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UnitCategory" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CAFE" />
			<xs:enumeration value="DELIVERY" />
			<xs:enumeration value="TAKE_AWAY" />
			<xs:enumeration value="COD" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UnitSubCategory" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="BPC" />
			<xs:enumeration value="COD" />
			<xs:enumeration value="HSC" />
			<xs:enumeration value="MALL" />
			<xs:enumeration value="TG" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UnitStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="IN_ACTIVE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UnitRegion" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NCR" />
			<xs:enumeration value="MUMBAI" />
			<xs:enumeration value="CHANDIGARH" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ProductStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="IN_ACTIVE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="SubscriptionType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MONTHLY" />
			<xs:enumeration value="WEEKLY" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="OfferMetaDataType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PRODUCT" />
			<xs:enumeration value="PRODUCT_SUB_CATEGORY" />
			<xs:enumeration value="PRODUCT_CATEGORY" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="PartnerType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INTERNAL" />
			<xs:enumeration value="EXTERNAL" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="IterationType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MOM" />
			<xs:enumeration value="WOW" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ManualBillBookStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CREATED" />
			<xs:enumeration value="ACTIVATED" />
			<xs:enumeration value="DEACTIVATED" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="PaymentType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CASH" />
			<xs:enumeration value="HAPPAY" />
			<xs:enumeration value="INTERNAL" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ExpenseStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="CANCELLED" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="CalculationIndexStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FIRST" />
			<xs:enumeration value="MID" />
			<xs:enumeration value="LAST" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="MeterDetailEntryType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DAY_START" />
			<xs:enumeration value="DAY_CLOSE" />
		</xs:restriction>
	</xs:simpleType>

</xs:schema>
