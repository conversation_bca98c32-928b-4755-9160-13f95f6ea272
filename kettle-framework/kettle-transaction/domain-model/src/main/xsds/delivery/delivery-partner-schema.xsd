<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">

	<xs:element name="DeliveryResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="deliveryPartnerId" type="xs:int" />
				<xs:element name="unitId" type="xs:int" />
				<xs:element name="orderId" type="xs:int" />
				<xs:element name="generatedOrderId" type="xs:string" />
				<xs:element name="deliveryTaskId" type="xs:string" />
				<xs:element name="deliveryStatus" type="xs:int" />
				<xs:element name="statusUpdateTime" type="xs:date" />
				<xs:element name="deliveryBoyName" type="xs:string" />
				<xs:element name="deliveryBoyPhoneNum" type="xs:string" />
				<xs:element name="failureCode" type="xs:string"/>
				<xs:element name="failureMessage" type="xs:string"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>		
	
	<xs:element name="SFXRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="store_code" type="xs:string" />
				<xs:element name="callback_url" type="xs:string" />
				<xs:element name="pickup_contact_number" type="xs:string" />
				<xs:element name="order_details" type="SFXOrder" />
				<xs:element name="customer_details" type="SFXCustomer" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="HLDRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="order_id" type="xs:string" />
				<xs:element name="task_id" type="xs:string" />
				<xs:element name="merchant_id" type="xs:int" />
				<xs:element name="restaurant" type="HLDAddrMetadata" />
				<xs:element name="customer" type="HLDAddrMetadata" />
				<xs:element name="totalAmount" type="xs:float" />
				<xs:element name="collectable_amount" type="xs:float" />
				<xs:element name="delivery_type" type="xs:string" />
				<xs:element name="pickup_time" type="xs:string" />
				<xs:element name="slot_order" type="HLDSlotOrder" />
				
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="HLDCancelRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="status" type="xs:string" />
				<xs:element name="reason" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="OPNRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="merchant_id" type="xs:int" />
				<xs:element name="amount" type="xs:float" />
				<xs:element name="amount_to_pay" type="xs:float" />
				<xs:element name="phone" type="xs:string" />
				<xs:element name="address" type="xs:string" />
				<xs:element name="order_code" type="xs:string" />
				<xs:element name="locality" type="xs:string" />
				<xs:element name="callback_url" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	
	
	<xs:element name="HLDResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="task_id" type="xs:string"/>
				<xs:element name="status" type="xs:string"/>
				<xs:element name="delivery_boy_name" type="xs:string" />
				<xs:element name="delivery_boy_phone" type="xs:string" />
				<xs:element name="delivery_boy_latitude" type="xs:string" />
				<xs:element name="delivery_boy_longitude" type="xs:string" />
				<xs:element name="estimated_pickup_time" type="xs:string"/>
				<xs:element name="estimated_delivery_time" type="xs:string"/>
				<xs:element name="error" type="HLDErrorResponse" />
				<xs:element name="errorMessage" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	
	<xs:complexType name="HLDErrorResponse">
		<xs:sequence>
			<xs:element name="code" type="xs:string"/>
			<xs:element name="msg" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	
	<xs:element name="OPNResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:string"/>
				<xs:element name="pilot_name" type="xs:string" />
				<xs:element name="pilot_phone" type="xs:string" />
				<xs:element name="pilot_latitude" type="xs:string" />
				<xs:element name="pilot_longitude" type="xs:string" />
				<xs:element name="order_code" type="xs:string" />
				<xs:element name="state" type="xs:string" />
				<xs:element name="eta" type="xs:string" />
				<xs:element name="delivery_eta" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	
	<xs:element name="OPNErrorResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="statusCode" type="xs:string"/>
				<xs:element name="message" type="xs:string"/>
				<xs:element name="error" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	
	<xs:element name="SFXCallbackObject">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="sfx_order_id" type="xs:string"/>
				<xs:element name="order_status" type="xs:string"/>
				<xs:element name="allot_time" type="xs:string"/>
				<xs:element name="dispatch_time" type="xs:string"/>
				<xs:element name="delivery_time" type="xs:string"/>
				<xs:element name="cancel_reason" type="xs:string"/>
				<xs:element name="rider_name" type="xs:string"/>
				<xs:element name="rider_contact" type="xs:string"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	
	<xs:element name="SFXResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="message" type="xs:string"/>
				<xs:element name="data" type="SFXOrderResponse"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	
	<xs:element name="SFXErrorResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="message" type="xs:string"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	
	<xs:element name="AuthorizationObject">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="token" type="xs:string"/>
				<xs:element name="tokenSecret" type="xs:string"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	
	<xs:element name="OPNMerchant">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="app_merchant_id" type="xs:string" />
				<xs:element name="name" type="xs:string" />
				<xs:element name="phone" type="xs:string" />
				<xs:element name="address" type="xs:string" />
				<xs:element name="email" type="xs:string" />
				<xs:element name="latitude" type="xs:string" />
				<xs:element name="longitude" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="OPNCallbackObject">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="order_code" type="xs:string" />
				<xs:element name="state" type="xs:string" />
				<xs:element name="pilot_name" type="xs:string" />
				<xs:element name="pilot_phone" type="xs:string" />
				<xs:element name="pilot_latitude" type="xs:string" />
				<xs:element name="pilot_longitude" type="xs:string" />
				<xs:element name="time" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:complexType name="SFXOrderResponse">
		<xs:sequence>
			<xs:element name="sfx_order_id" type="xs:long"/>
			<xs:element name="store_code" type="xs:string" />
			<xs:element name="pickup_contact_number" type="xs:string"/>
			<xs:element name="order_details" type="SFXOrder" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="customer_details" type="SFXCustomer"/>
			<xs:element name="rider_details" type="SFXRider"/>			
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="SFXRider">
		<xs:sequence>
			<xs:element name="rider_name" type="xs:string"/>
			<xs:element name="rider_phone" type="xs:string"/>
			<xs:element name="rider_location" type="SFXRiderLocation" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="SFXRiderLocation">
		<xs:sequence>
			<xs:element name="latitude" type="xs:string"/>
			<xs:element name="longitude" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="SFXOrder">
		<xs:sequence>
			<xs:element name="client_order_id" type="xs:string" />
			<xs:element name="order_value" type="xs:float" />
			<xs:element name="paid" type="xs:string" />
			<xs:element name="preparation_time" type="xs:int" />
			<xs:element name="allot_time" type="xs:string" />
			<xs:element name="scheduled_time" type="xs:string" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="SFXCustomer">
		<xs:sequence>
			<xs:element name="name" type="xs:string" />
			<xs:element name="contact_number" type="xs:string" />
			<xs:element name="address_line_1" type="xs:string" />
			<xs:element name="address_line_2" type="xs:string" />
			<xs:element name="city" type="xs:string" />
			<xs:element name="latitude" type="xs:string" />
			<xs:element name="longitude" type="xs:string" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="HLDAddrMetadata">
		<xs:sequence>
			<xs:element name="name" type="xs:string" />
			<xs:element name="phone" type="xs:string" />
			<xs:element name="lat" type="xs:string" />
			<xs:element name="lng" type="xs:string" />
			<xs:element name="delivery_subzone" type="xs:string" />
			<xs:element name="address" type="HLDAddress" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="HLDAddress">
		<xs:sequence>
			<xs:element name="sub_locality" type="xs:string" />
			<xs:element name="locality" type="xs:string" />
			<xs:element name="city" type="xs:string" />
			<xs:element name="state" type="xs:string" />
			<xs:element name="pin_code" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="HLDSlotOrder">
		<xs:sequence>
			<xs:element name="start" type="xs:string" />
			<xs:element name="end" type="xs:string" />
		</xs:sequence>
	</xs:complexType>

	<!-- Grab Schema Starts-->

	<xs:element name="GrabMerchant">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="clientId" type="xs:string" />
				<xs:element name="merchantId" type="xs:string" />
				<xs:element name="merchantName" type="xs:string" />
				<xs:element name="merchantAddress" type="xs:string" />
				<xs:element name="merchantLat" type="xs:string" />
				<xs:element name="merchantLong" type="xs:string" />
				<xs:element name="merchantContact" type="xs:string" />
				<xs:element name="merchantType" type="xs:string" />
				<xs:element name="merchantVerified" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="GrabOrderRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="merchantId" type="xs:string" />
				<xs:element name="prepTime" type="xs:string" />
				<xs:element name="customerPhone" type="xs:string" />
				<xs:element name="customerName" type="xs:string" />
				<xs:element name="customerAddressLine1" type="xs:string" />
				<xs:element name="customerAddressLine2" type="xs:string" />
				<xs:element name="customerAddressLandMark" type="xs:string" />
				<xs:element name="billAmount" type="xs:string" />
				<xs:element name="billNo" type="xs:string" />
				<xs:element name="orderType" type="xs:string" />
				<xs:element name="amountPaidByClient" type="xs:string" />
				<xs:element name="amountCollectedInCash" type="xs:string" />
				<xs:element name="comments" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="GrabGenericRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="clientId" type="xs:string" />
				<xs:element name="orderId" type="xs:string" />
				<xs:element name="clientOrderId" type="xs:string" />
				<xs:element name="dttm" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="GrabCancellationRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="comment" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>


	<xs:element name="GrabCallback">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="orderStatus" type="xs:string" />
				<xs:element name="grabOrderId" type="xs:string" />
				<xs:element name="clientOrderId" type="xs:string" />
				<xs:element name="merchantBillNo" type="xs:string" />
				<xs:element name="merchantId" type="xs:string" />
				<xs:element name="riderName" type="xs:string" />
				<xs:element name="riderPhone" type="xs:string" />
				<xs:element name="riderLatitude" type="xs:string" />
				<xs:element name="riderLongitude" type="xs:string" />
				<xs:element name="expectedDeliveryTime" type="xs:string" />
				<xs:element name="dttm" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="GrabOrderResponse">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="success" type="xs:string" />
				<xs:element name="orderId" type="xs:string" />
				<xs:element name="clientOrderId" type="xs:string" />
				<xs:element name="expectedDeliveryTime" type="xs:string" />
				<xs:element name="timestamp" type="xs:string" />
				<xs:element name="errorCode" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<!-- Grab Schema ends here-->


	<!-- Delivery Track Schema Starts-->

	<xs:element name="DTOrderRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="paymentmode" type="xs:string" />
				<xs:element name="address" type="xs:string" />
				<xs:element name="amount" type="xs:string" />
				<xs:element name="customernumber" type="xs:string" />
				<xs:element name="lat" type="xs:string" />
				<xs:element name="lon" type="xs:string" />
				<xs:element name="name" type="xs:string" />
				<xs:element name="ordername" type="xs:string" />
				<xs:element name="status" type="xs:string" />
				<xs:element name="area" type="xs:string" />
				<xs:element name="pincode" type="xs:string" />
				<xs:element name="city" type="xs:string" />
				<xs:element name="restaurandid" type="xs:string" />
				<xs:element name="restname" type="xs:string" />
				<xs:element name="restaurantcontact" type="xs:string" />
				<xs:element name="restAddress" type="xs:string"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="DTCancellationRequest">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="orderId" type="xs:string" />
				<xs:element name="status" type="xs:string" />
				<xs:element name="userId" type="xs:string" />
				<xs:element name="usertype" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:element name="DTCallback">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="order_id" type="xs:string" />
				<xs:element name="status" type="xs:string" />
				<xs:element name="driver_name" type="xs:string" />
				<xs:element name="driver_number" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>


	<xs:complexType name="DTOrderResponse">
		<xs:sequence>
			<xs:element name="result" type="xs:string" />
			<xs:element name="message" type="xs:string" />
			<xs:element name="orderId" type="xs:string" />
		</xs:sequence>
	</xs:complexType>

	<!-- Delivery Track ends here-->


</xs:schema>