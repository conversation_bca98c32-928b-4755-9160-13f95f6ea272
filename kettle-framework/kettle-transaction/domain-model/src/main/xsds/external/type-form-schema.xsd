<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">

	<xs:element name="EventDetail">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="event_id" type="xs:string" />
				<xs:element name="event_type" type="xs:string" />
				<xs:element name="form_response" type="FormResponse" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>		
	
		<xs:complexType name="FormResponse">
			<xs:sequence>
				<xs:element name="form_id" type="xs:string" />
				<xs:element name="token" type="xs:string" />
				<xs:element name="submitted_at" type="xs:date" />
				<xs:element name="hidden" type="HiddenAttributes" />
				<xs:element name="definition" type="FormDefinition" />
				<xs:element name="answers" type="FormResponseData" minOccurs="0" maxOccurs="unbounded"/>
				
			</xs:sequence>
		</xs:complexType>
	<xs:complexType name="FormDefinition">
			<xs:sequence>
				<xs:element name="id" type="xs:string" />
				<xs:element name="title" type="xs:string" />
				<xs:element name="fields" type="FormField" minOccurs="1" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
		<xs:complexType name="FormField">
			<xs:sequence>
				<xs:element name="id" type="xs:string" />
				<xs:element name="title" type="xs:string" />
				<xs:element name="type" type="FieldType" />
			</xs:sequence>
		</xs:complexType>
		<xs:complexType name="FormResponseData">
			<xs:sequence>
				<xs:element name="type" type="ResponseType" />
				<xs:element name="text" type="xs:string" />
				<xs:element name="choice" type="ChoiceData" />
				<xs:element name="choices" type="MultipleChoiceData" />
				<xs:element name="number" type="xs:int" />
				<xs:element name="field" type="FormField" />
			</xs:sequence>
		</xs:complexType>
		<xs:complexType name="ChoiceData">
			<xs:sequence>
				<xs:element name="label" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
			<xs:complexType name="MultipleChoiceData">
			<xs:sequence>
				<xs:element name="labels" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="other" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	<xs:simpleType name="FieldType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="short_text" />
			<xs:enumeration value="multiple_choice" />
			<xs:enumeration value="rating" />
			<xs:enumeration value="long_text" />
			<xs:enumeration value="picture_choice" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ResponseType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="text" />
			<xs:enumeration value="choice" />
			<xs:enumeration value="number" />
			<xs:enumeration value="choices" />
			<xs:enumeration value="email" />
			<xs:enumeration value="url" />
			<xs:enumeration value="date" />
			<xs:enumeration value="file_url" />
			<xs:enumeration value="boolean" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="HiddenAttributes">
			<xs:sequence>
				<xs:element name="name" type="xs:string" />
				<xs:element name="number" type="xs:string" />
				<xs:element name="unit" type="xs:string" />
				<xs:element name="token" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
</xs:schema>