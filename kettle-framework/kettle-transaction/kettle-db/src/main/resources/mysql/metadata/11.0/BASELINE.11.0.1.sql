CREATE TABLE KETTLE_DEV.CUSTOMER_PRODUCT_FEEDBACK(
CUSTOMER_PRODUCT_FEEDBACK_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
CUSTOMER_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
RATING INTEGER NOT NULL,
RATING_SOURCE VARCHAR(20) NOT NULL,
RATING_SOURCE_ID INTEGER NULL,
UPDATE_TIME TIMESTAMP NULL,
RATING_STATUS VARCHAR(15) NULL,
ADD_TIME TIMESTAMP NULL
);


CREATE INDEX CUSTOMER_PRODUCT_FEEDBACK_CUSTOMER_ID ON KETTLE_DEV.CUSTOMER_PRODUCT_FEEDBACK(CUSTOMER_ID) USING BTREE;
CREATE INDEX CUSTOMER_PRODUCT_FEEDBACK_PRODUCT_ID ON KETTLE_DEV.CUSTOMER_PRODUCT_FEEDBACK(PRODUCT_ID) USING BTREE;
CREATE INDEX CUSTOMER_PRODUCT_FEEDBACK_RATING ON KETTLE_DEV.CUSTOMER_PRODUCT_FEEDBACK(RATING) USING BTREE;
CREATE INDEX CUSTOMER_PRODUCT_FEEDBACK_RATING_SOURCE ON KETTLE_DEV.CUSTOMER_PRODUCT_FEEDBACK(RATING_SOURCE) USING BTREE;
CREATE INDEX CUSTOMER_PRODUCT_FEEDBACK_RATING_STATUS ON KETTLE_DEV.CUSTOMER_PRODUCT_FEEDBACK(RATING_STATUS) USING BTREE;
CREATE INDEX CUSTOMER_PRODUCT_FEEDBACK_ADD_TIME ON KETTLE_DEV.CUSTOMER_PRODUCT_FEEDBACK(ADD_TIME) USING BTREE;

CREATE TABLE KETTLE_JOBS_DEV.AGGREGATED_RESULT_DEFINITION(
	AGGREGATED_RESULT_DEFINITION_ID INT NOT NULL AUTO_INCREMENT,
    UNIT_ID INT  ,
    UNIT_NAME VARCHAR(255),
    REFERENCE_NUMBER VARCHAR(255),
    STATE VARCHAR(100),
    STATE_CODE VARCHAR(10),
    PARTICULARS VARCHAR(255),
    TOTAL_SALES DECIMAL(16,6),
    ACCOUNT_CODE VARCHAR(255),
    JOB_ID INT,
    EXECUTION_ID INT,
    EXECUTION_TIME TIMESTAMP,
    TYPE_OF_DATA VARCHAR(40),
    STATUS VARCHAR(10),
    STEP_ID INT,
    PRIMARY KEY (AGGREGATED_RESULT_DEFINITION_ID)
);

CREATE TABLE KETTLE_JOBS_DEV.JOB_DEFINITION(
	JOB_ID INT NOT NULL AUTO_INCREMENT,
	JOB_NAME VARCHAR(50),
    JOB_DESCRIPTION VARCHAR(255),
    JOB_TYPE VARCHAR(50),
    JOB_FILE_NAME VARCHAR(255),
    JOB_STATUS VARCHAR(10),
    PRIMARY KEY (JOB_ID)
);


CREATE TABLE KETTLE_JOBS_DEV.JOB_EXECUTION_DEFINITION(
	JOB_EXECUTION_DEFINITION_ID INT NOT NULL AUTO_INCREMENT,
    JOB_ID INT ,
    EXECUTION_TIME TIMESTAMP ,
    EXECUTED_BY INT,
    JOB_EXECUTION_STATUS VARCHAR(50),
    PRIMARY KEY (JOB_EXECUTION_DEFINITION_ID),
    FOREIGN KEY (JOB_ID) REFERENCES KETTLE_JOBS_DEV.JOB_DEFINITION(JOB_ID)
);

CREATE TABLE KETTLE_JOBS_DEV.JOB_EXECUTION_ERROR_DEFINITION(
	JOB_EXECUTION_ERROR_DEFINITION_ID INT NOT NULL AUTO_INCREMENT,
    JOB_EXECUTION_DEFINITION_ID INT,
    ERROR_TYPE VARCHAR(100),
    ERROR_VALUE VARCHAR(500),
    ERROR_CODE VARCHAR(100),
    PRIMARY KEY (JOB_EXECUTION_ERROR_DEFINITION_ID),
    FOREIGN KEY (JOB_EXECUTION_DEFINITION_ID) REFERENCES KETTLE_JOBS_DEV.JOB_EXECUTION_DEFINITION(JOB_EXECUTION_DEFINITION_ID)
);

CREATE TABLE KETTLE_JOBS_DEV.JOBS_PARAM_DEFINITION(
	PARAM_ID INT AUTO_INCREMENT,
    PARAM_NAME VARCHAR(50) UNIQUE,
	PARAM_CODE VARCHAR(40),
    PARAM_DESCRIPTION VARCHAR(255),
    PARAM_TYPE VARCHAR(40),
    PARAM_CATEGORY VARCHAR(40),
    PRIMARY KEY (PARAM_ID)
);

CREATE TABLE KETTLE_JOBS_DEV.JOBS_PARAM_DEFINITION_MAPPING(
	JOBS_PARAM_MAPPING_ID INT AUTO_INCREMENT,
    JOB_ID INT,
    PARAM_ID INT,
    STATUS VARCHAR(10),
    PRIMARY KEY(JOBS_PARAM_MAPPING_ID),
    FOREIGN KEY (JOB_ID) REFERENCES KETTLE_JOBS_DEV.JOB_DEFINITION(JOB_ID),
    FOREIGN KEY (PARAM_ID) REFERENCES KETTLE_JOBS_DEV.JOBS_PARAM_DEFINITION(PARAM_ID)
);

CREATE TABLE KETTLE_JOBS_DEV.JOB_EXECUTION_PARAM_MAPPING(
	JOB_EXECUTION_PARAM_MAPPING_ID INT AUTO_INCREMENT,
    JOB_EXECUTION_DEFINITION_ID INT,
    PARAM_ID INT,
    PARAM_NAME VARCHAR(40),
    PARAM_VALUE VARCHAR(255),
    PRIMARY KEY (JOB_EXECUTION_PARAM_MAPPING_ID)
);

