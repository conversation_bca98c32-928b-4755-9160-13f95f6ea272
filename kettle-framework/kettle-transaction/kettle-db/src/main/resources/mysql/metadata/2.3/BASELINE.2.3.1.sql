DROP PROCEDURE SP_UPDATE_EXPENSE_DETAIL_DATA;

DE<PERSON><PERSON><PERSON>ER $$

CREATE PROCEDURE `SP_UPDATE_EXPENSE_DETAIL_DATA`(IN EXPENSE_EVENT_ID INTEGER)

proc_label : BEGIN

UPDATE KETTLE.UNIT_EXPENSE_DETAIL ued

LEFT OUTER JOIN

    (SELECT

			ud.UNIT_ID,

            ud.UNIT_NAME,

            EXPENSE_EVENT_ID AS EVENT_ID,

            COALESCE(q.CREDIT_CARD_CHARGES, 0) CREDIT_CARD_CHARGES,

            COALESCE(q.AMEX_CHARGES, 0) AMEX_CHARGES,

            COALESCE(q.SODEXO_CHARGES, 0) SODEXO_CHARGES,

            COALESCE(q.TICKET_RESTAURENT_CHARGES, 0) TICKET_RESTAURENT_CHARGES,

            COALESCE(q.PAYTM_CHARGES, 0) PAYTM_CHARGES,

            COALESCE(q.PAYTM_ONLINE_CHARGES, 0) PAYTM_ONLINE_CHARGES,

            COALESCE(q.MOBIKWIK_CHARGES, 0) MOBIKWIK_CHARGES,

            COALESCE(q.MOBIKWIK_ONLINE_CHARGES, 0) MOBIKWIK_ONLINE_CHARGES,

            COALESCE(q.RAZORPAY_CHARGES, 0) RAZORPAY_CHARGES,

            COALESCE(q.FREE_CHARGE_CHARGES, 0) FREE_CHARGE_CHARGES,

            COALESCE(w.CHANNEL_PARTNER_COST, 0) CHANNEL_PARTNER_COST,

            COALESCE(e.DELIVERY_PARTNER_COST, 0) DELIVERY_PARTNER_COST,

            COALESCE(r.TOTAL_TICKET, 0) TOTAL_TICKET,

            COALESCE(r.TKT, 0) TKT,

            COALESCE(r.SALES, 0) SALES,

            COALESCE(t.EMPLOYEE_MEAL, 0) EMPLOYEE_MEAL,

            COALESCE(t.UNSATISFIED_QUANTITY, 0) UNSATISFIED_QUANTITY,

            COALESCE(t.SAMPLING_MARKETING, 0) SAMPLING_MARKETING,

            COALESCE(t.GMV, 0) GMV,

            COALESCE(t.COGS_1, 0) + COALESCE(y.COGS_2, 0) AS TOTAL_COGS,

            COALESCE(r.DISCOUNT, 0) + COALESCE(r.PROMOTIONAL, 0) AS DISCOUNT

    FROM

        KETTLE_MASTER.UNIT_DETAIL ud

    LEFT JOIN (SELECT

        od.UNIT_ID,

            eue.EVENT_ID,

            SUM(CASE

                WHEN ose.PAYMENT_MODE_ID = 2 THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS CREDIT_CARD_CHARGES,

            SUM(CASE

                WHEN ose.PAYMENT_MODE_ID = 3 THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS AMEX_CHARGES,

            SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (4 , 7) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS SODEXO_CHARGES,

            SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (5 , 8) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS TICKET_RESTAURENT_CHARGES,

            SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (11) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS PAYTM_CHARGES,

			SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (13) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS PAYTM_ONLINE_CHARGES,

			SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (14) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS MOBIKWIK_CHARGES,

            SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (15) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS MOBIKWIK_ONLINE_CHARGES,

            SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (12) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS RAZORPAY_CHARGES,

            SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (16) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS FREE_CHARGE_CHARGES

    FROM

        KETTLE.ORDER_DETAIL od

    LEFT JOIN KETTLE.ORDER_SETTLEMENT ose ON od.ORDER_ID = ose.ORDER_ID

    LEFT JOIN KETTLE_MASTER.PAYMENT_MODE pm ON ose.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

            AND eue.EVENT_ID = EXPENSE_EVENT_ID

    GROUP BY od.UNIT_ID , eue.EVENT_ID) q ON ud.UNIT_ID = q.UNIT_ID

    LEFT JOIN (SELECT

        a.UNIT_ID,

            a.EVENT_ID,

            SUM(a.CHANNEL_PARTNER_COST) AS CHANNEL_PARTNER_COST

    FROM

        (SELECT

        od.UNIT_ID,

            eue.EVENT_ID,

            cp.PARTNER_DISPLAY_NAME,

            TRUNCATE(SUM(COALESCE(od.TAXABLE_AMOUNT,0)) * cp.COMMISSION_RATE / 100, 0) AS CHANNEL_PARTNER_COST

    FROM

        KETTLE.ORDER_DETAIL od

    LEFT JOIN KETTLE.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

            AND eue.EVENT_ID = EXPENSE_EVENT_ID

    GROUP BY od.UNIT_ID , eue.EVENT_ID , cp.PARTNER_DISPLAY_NAME) AS a

    GROUP BY a.UNIT_ID , a.EVENT_ID) AS w ON ud.UNIT_ID = w.UNIT_ID

        AND q.EVENT_ID = w.EVENT_ID

    LEFT JOIN (SELECT

        a.UNIT_ID,

            a.EVENT_ID,

            SUM(a.DELIVERY_PARTNER_COST) AS DELIVERY_PARTNER_COST

    FROM

        (SELECT

        od.UNIT_ID,

            eue.EVENT_ID,

            cp.PARTNER_DISPLAY_NAME,

            COALESCE(SUM(CASE

                WHEN

                    od.TOTAL_AMOUNT <> '0.00'

                        AND od.ORDER_SOURCE = 'COD'

                THEN

                    1

                ELSE 0

            END) * cp.PER_DELIVERY_COST, 0) AS DELIVERY_PARTNER_COST

    FROM

        KETTLE.ORDER_DETAIL od

    LEFT JOIN KETTLE.DELIVERY_PARTNER cp ON od.DELIVERY_PARTNER_ID = cp.PARTNER_ID

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

            AND eue.EVENT_ID = EXPENSE_EVENT_ID

    GROUP BY od.UNIT_ID , eue.EVENT_ID , cp.PARTNER_DISPLAY_NAME) AS a

    GROUP BY a.UNIT_ID , a.EVENT_ID) AS e ON ud.UNIT_ID = e.UNIT_ID

        AND q.EVENT_ID = e.EVENT_ID

    LEFT JOIN (SELECT

    x.UNIT_ID UNIT_ID,

    x.EVENT_ID EVENT_ID,

    CASE

        WHEN p.GIFT_CARD_TICKETS IS NULL THEN x.TOTAL_TICKET

        ELSE x.TOTAL_TICKET - p.GIFT_CARD_TICKETS

    END TOTAL_TICKET,

     CASE

        WHEN p.GIFT_CARD_TICKETS IS NULL THEN x.TKT

        ELSE x.TKT - p.GIFT_CARD_TICKETS

    END TKT,

    CASE

        WHEN p.GIFT_CARD_AMOUNT IS NULL THEN x.SALES

        ELSE x.SALES - p.GIFT_CARD_AMOUNT

    END SALES,

    x.DISCOUNT DISCOUNT,

    x.PROMOTIONAL PROMOTIONAL

FROM

    (SELECT

        od.UNIT_ID UNIT_ID,

            eue.EVENT_ID EVENT_ID,

            COUNT(*) AS TOTAL_TICKET,

            SUM(CASE

                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1

                ELSE 0

            END) AS TKT,

            SUM(COALESCE(od.TAXABLE_AMOUNT, 0)) AS SALES,

            SUM(COALESCE(od.DISCOUNT_AMOUNT, 0)) AS DISCOUNT,

            SUM(COALESCE(od.PROMOTIONAL_DISCOUNT, 0)) AS PROMOTIONAL

    FROM

        KETTLE.ORDER_DETAIL od

    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

            AND eue.EVENT_ID = EXPENSE_EVENT_ID

   GROUP BY od.UNIT_ID , eue.EVENT_ID) x

        LEFT OUTER JOIN

    (SELECT

        a.UNIT_ID,

            a.EVENT_ID,

            SUM(CASE

                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1

                ELSE 0

            END) GIFT_CARD_TICKETS,

            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT

    FROM

        (SELECT

        oi.ORDER_ID,

            eue.EVENT_ID,

            od.UNIT_ID,

            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,

            od.SETTLED_AMOUNT

    FROM

        KETTLE.ORDER_ITEM oi

    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

            AND eue.EVENT_ID = EXPENSE_EVENT_ID

            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)

    GROUP BY oi.ORDER_ID , eue.EVENT_ID , od.UNIT_ID) a

    GROUP BY a.EVENT_ID , a.UNIT_ID) p ON p.UNIT_ID = x.UNIT_ID

        AND p.EVENT_ID = x.EVENT_ID) AS r ON ud.UNIT_ID = r.UNIT_ID

        AND q.EVENT_ID = r.EVENT_ID

    LEFT JOIN (SELECT

        c.UNIT_ID,

            c.EVENT_ID,

            SUM(c.COGS) AS COGS_1,

            SUM(c.EMPLOYEE_MEAL) AS EMPLOYEE_MEAL,

            SUM(c.UNSATISFIED_QUANTITY) AS UNSATISFIED_QUANTITY,

            SUM(c.SAMPLING_MARKETING) AS SAMPLING_MARKETING,

            SUM(c.GMV) AS GMV,

            SUM(c.NEWGMV) AS NEWGMV

    FROM

        (SELECT

        a.UNIT_ID,

            a.EVENT_ID,

            a.PRODUCT_ID,

            a.DIMENSION,

            ( CASE WHEN a.ORDER_SOURCE = 'COD'

            THEN

				COALESCE(a.SOLD_QUANTITY,0) * COALESCE(b.COD_COST,0)

			ELSE

				COALESCE(a.SOLD_QUANTITY,0) * COALESCE(b.COST,0)

			END ) AS COGS,

			( CASE WHEN a.ORDER_SOURCE = 'COD'

            THEN

				COALESCE(a.EMPLOYEE_MEAL,0) * COALESCE(b.COD_COST,0)

			ELSE

				COALESCE(a.EMPLOYEE_MEAL,0) * COALESCE(b.COST,0)

			END ) AS EMPLOYEE_MEAL,

			( CASE WHEN a.ORDER_SOURCE = 'COD'

            THEN

				COALESCE(a.UNSATISFIED_QUANTITY,0) * COALESCE(b.COD_COST,0)

			ELSE

				COALESCE(a.UNSATISFIED_QUANTITY,0) * COALESCE(b.COST,0)

			END ) AS UNSATISFIED_QUANTITY,

			( CASE WHEN a.ORDER_SOURCE = 'COD'

            THEN

				COALESCE(a.SAMPLING_MARKETING,0) * COALESCE(b.COD_COST,0)

			ELSE

				COALESCE(a.SAMPLING_MARKETING,0) * COALESCE(b.COST,0)

			END ) AS SAMPLING_MARKETING,

            COALESCE(a.GMV,0)GMV,

            COALESCE(a.SOLD_QUANTITY,0) * b.PRICE AS NEWGMV

    FROM

        (SELECT

        od.UNIT_ID,

            eue.EVENT_ID,

            pd.PRODUCT_ID,

            rl2.RL_ID AS DIMENSION,

            oi.PRICE,

			od.ORDER_SOURCE,

            SUM(CASE

                WHEN

						IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL

                        OR (IS_COMPLIMENTARY = 'Y'

                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106))

                THEN

                    oi.QUANTITY

                ELSE 0

            END) SOLD_QUANTITY,

            SUM(CASE

                WHEN

                    IS_COMPLIMENTARY = 'Y'

                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2100

                THEN

                    oi.QUANTITY

                ELSE 0

            END) EMPLOYEE_MEAL,

            SUM(CASE

                WHEN

                    IS_COMPLIMENTARY = 'Y'

                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102

                THEN

                    oi.QUANTITY

                ELSE 0

            END) UNSATISFIED_QUANTITY,

            SUM(CASE

                WHEN

                    IS_COMPLIMENTARY = 'Y'

                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) IN (2105 , 2106)

                THEN

                    oi.QUANTITY

                ELSE 0

            END) SAMPLING_MARKETING,

            SUM(CASE

                WHEN

					pd.PRODUCT_TYPE <> 8 AND (

                    IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL

                        OR (IS_COMPLIMENTARY = 'Y'

                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)))

                        AND pd.PRODUCT_ID NOT IN (1026 , 1027, 1048)

                THEN

                    oi.QUANTITY * oi.PRICE

                ELSE 0

            END) GMV

    FROM

        KETTLE.ORDER_DETAIL od

    LEFT JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID

    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID

    LEFT JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID

    LEFT JOIN KETTLE_MASTER.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID

    LEFT JOIN KETTLE_MASTER.REF_LOOKUP rl2 ON oi.DIMENSION = rl2.RL_CODE

        AND rl2.RTL_ID = pd.DIMENSION_CODE

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

            AND eue.EVENT_ID = EXPENSE_EVENT_ID

    GROUP BY od.UNIT_ID , eue.EVENT_ID , pd.PRODUCT_ID , rl2.RL_ID , oi.PRICE, od.ORDER_SOURCE) AS a

    INNER JOIN

		(SELECT

			upm.UNIT_ID,

            upm.PRODUCT_ID,

            upp.DIMENSION_CODE,

            upp.PRICE,

            upp.COST,

			upp.COD_COST

    FROM

        KETTLE_MASTER.UNIT_PRODUCT_MAPPING upm

    LEFT JOIN KETTLE_MASTER.UNIT_PRODUCT_PRICING upp ON upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID) AS b

                ON a.UNIT_ID = b.UNIT_ID

        AND a.PRODUCT_ID = b.PRODUCT_ID

        AND a.DIMENSION = b.DIMENSION_CODE) AS c

    GROUP BY c.UNIT_ID , c.EVENT_ID) AS t

                ON ud.UNIT_ID = t.UNIT_ID

        AND q.EVENT_ID = t.EVENT_ID

    LEFT JOIN (SELECT

        p.UNIT_ID,

		p.EVENT_ID,

		SUM(

		CASE

		WHEN p.ORDER_SOURCE = 'COD'

            THEN

				COALESCE(p.QUANTITY,0) * COALESCE(b.COD_COST,0)

			ELSE

				COALESCE(p.QUANTITY,0) * COALESCE(b.COST,0)

			END) AS COGS_2

    FROM

        (SELECT

        od.UNIT_ID,

            eue.EVENT_ID,

            pd1.PRODUCT_ID,

            (CASE

                WHEN pd1.DIMENSION_CODE = 1 THEN 1

                WHEN

                    pd1.DIMENSION_CODE = 2

                        AND od.ORDER_SOURCE = 'CAFE'

                THEN

                    21

                WHEN

                    pd1.DIMENSION_CODE = 2

                        AND od.ORDER_SOURCE = 'COD'

                THEN

                    24

                WHEN pd1.DIMENSION_CODE = 3 THEN 30

                WHEN

                    pd1.DIMENSION_CODE = 11

                        AND od.ORDER_SOURCE = 'CAFE'

                THEN

                    1101

                WHEN

                    pd1.DIMENSION_CODE = 11

                        AND od.ORDER_SOURCE = 'COD'

                THEN

                    1102

                WHEN

                    pd1.DIMENSION_CODE = 34

                        AND od.ORDER_SOURCE = 'CAFE'

                THEN

                    3401

                WHEN

                    pd1.DIMENSION_CODE = 34

                        AND od.ORDER_SOURCE = 'COD'

                THEN

                    3402

                ELSE 1

            END) AS DIMENSIONS,

            SUM(oi.QUANTITY) AS QUANTITY,

			od.ORDER_SOURCE

    FROM

        KETTLE.ORDER_DETAIL od

    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID

    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID

    INNER JOIN KETTLE.ORDER_ITEM_ADDON oia ON oi.ORDER_ITEM_ID = oia.ORDER_ITEM_ID

    INNER JOIN KETTLE.ADDON_PRODUCT_DATA apd ON oia.ADDON_ID = apd.ADDON_ID

    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON pd1.PRODUCT_ID = apd.PRODUCT_ID

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

            AND eue.EVENT_ID = EXPENSE_EVENT_ID

            AND pd.PRODUCT_TYPE = 8

            AND apd.ADDON_ID IS NOT NULL

    GROUP BY od.UNIT_ID , eue.EVENT_ID , pd1.PRODUCT_ID , DIMENSIONS , od.ORDER_SOURCE) AS p

    INNER JOIN (SELECT

        upm.UNIT_ID,

            upm.PRODUCT_ID,

            upp.DIMENSION_CODE,

            upp.PRICE,

            upp.COST,

			upp.COD_COST

    FROM

        KETTLE_MASTER.UNIT_PRODUCT_MAPPING upm

    LEFT JOIN KETTLE_MASTER.UNIT_PRODUCT_PRICING upp ON upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID) AS b

                ON p.UNIT_ID = b.UNIT_ID

        AND p.PRODUCT_ID = b.PRODUCT_ID

        AND p.DIMENSIONS = b.DIMENSION_CODE

    GROUP BY p.UNIT_ID , p.EVENT_ID) AS y ON ud.UNIT_ID = y.UNIT_ID

        AND q.EVENT_ID = y.EVENT_ID) AS z

ON   ued.UNIT_ID = z.UNIT_ID

        AND ued.EXPENSE_UPDATE_EVENT_ID = z.EVENT_ID       

SET

    ued.GMV_AMOUNT = COALESCE(z.GMV, 0),

    ued.NET_SALES_AMOUNT = COALESCE(z.SALES, 0),

    ued.NET_TICKETS = COALESCE(z.TKT, 0),

    ued.TOTAL_TICKETS = COALESCE(z.TOTAL_TICKET, 0),

    ued.COGS = COALESCE(z.TOTAL_COGS, 0),

    ued.MARKETING_AND_SAMPLING = COALESCE(z.SAMPLING_MARKETING, 0),

    ued.UNSATISFIED_CUSTOMER_COST = COALESCE(z.UNSATISFIED_QUANTITY, 0),

    ued.EMPLOYEE_MEAL = COALESCE(z.EMPLOYEE_MEAL, 0),

    ued.DELIVERY_COST = COALESCE(z.DELIVERY_PARTNER_COST, 0),

    ued.CREDIT_CARD_CHARGES = COALESCE(z.CREDIT_CARD_CHARGES, 0),

    ued.AMEX_CARD_CHARGES = COALESCE(z.AMEX_CHARGES, 0),

    ued.SODEXO_CHARGES = COALESCE(z.SODEXO_CHARGES, 0),

    ued.TKT_RESTAURANT_CHARGES = COALESCE(z.TICKET_RESTAURENT_CHARGES, 0),

    ued.PAYTM_CHARGES = COALESCE(z.PAYTM_CHARGES, 0),

    ued.PAYTM_ONLINE_CHARGES = COALESCE(z.PAYTM_ONLINE_CHARGES, 0),

    ued.MOBIKWIK_CHARGES = COALESCE(z.MOBIKWIK_CHARGES, 0),

    ued.MOBIKWIK_ONLINE_CHARGES = COALESCE(z.MOBIKWIK_ONLINE_CHARGES, 0),

    ued.RAZORPAY_CHARGES = COALESCE(z.RAZORPAY_CHARGES, 0),

    ued.FREE_CHARGE_CHARGES = COALESCE(z.FREE_CHARGE_CHARGES, 0),

    ued.CHANNEL_PARTNER = COALESCE(z.CHANNEL_PARTNER_COST, 0),

    ued.NET_DISCOUNT = COALESCE(z.DISCOUNT, 0),

    ued.KITCHEN_COST_TOTAL = COALESCE(z.SALES, 0) * ued.KITCHEN_COST_PERCENTAGE / 100,

    ued.OPS_COST_TOTAL = COALESCE(z.SALES, 0) * ued.OPS_COST_PERCENTAGE / 100,

    ued.RENT = GREATEST( COALESCE(ued.FIXED_RENT, 0) , ( COALESCE(ued.RENT_PERCENTAGE, 0) * COALESCE(z.SALES, 0) / 100))

    where ued.EXPENSE_UPDATE_EVENT_ID = EXPENSE_EVENT_ID

;



UPDATE KETTLE.UNIT_EXPENSE_DETAIL ued

LEFT OUTER JOIN

    (SELECT

        ud.UNIT_ID,

            ud.UNIT_NAME,

			EXPENSE_EVENT_ID AS EVENT_ID,

            COALESCE(q.PAYTM_CHARGES, 0) PAYTM_CHARGES,

            COALESCE(q.PAYTM_ONLINE_CHARGES, 0) PAYTM_ONLINE_CHARGES,

            COALESCE(q.MOBIKWIK_CHARGES, 0) MOBIKWIK_CHARGES,

            COALESCE(q.MOBIKWIK_ONLINE_CHARGES, 0) MOBIKWIK_ONLINE_CHARGES,

            COALESCE(q.RAZORPAY_CHARGES, 0) RAZORPAY_CHARGES,

            COALESCE(q.FREE_CHARGE_CHARGES, 0) FREE_CHARGE_CHARGES,

            COALESCE(r.TOTAL_TICKET, 0) TOTAL_TICKET,

            COALESCE(r.TKT, 0) TKT,

            COALESCE(r.SALES, 0) SALES,

            COALESCE(t.UNSATISFIED_QUANTITY, 0) UNSATISFIED_QUANTITY,

            COALESCE(t.SAMPLING_MARKETING, 0) SAMPLING_MARKETING,

            COALESCE(t.GMV, 0) GMV,

            COALESCE(t.COGS_1, 0) + COALESCE(y.COGS_2, 0) AS TOTAL_COGS

    FROM

        KETTLE_MASTER.UNIT_DETAIL ud

    LEFT JOIN (SELECT

        od.UNIT_ID,

            eue.EVENT_ID,

            SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (11) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS PAYTM_CHARGES,

			SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (13) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS PAYTM_ONLINE_CHARGES,

			SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (14) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS MOBIKWIK_CHARGES,

			SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (15) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS MOBIKWIK_ONLINE_CHARGES,

            SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (12) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS RAZORPAY_CHARGES,

            SUM(CASE

                WHEN ose.PAYMENT_MODE_ID IN (16) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100

                ELSE 0

            END) AS FREE_CHARGE_CHARGES

    FROM

        KETTLE.ORDER_DETAIL od

    LEFT JOIN KETTLE.ORDER_SETTLEMENT ose ON od.ORDER_ID = ose.ORDER_ID

    LEFT JOIN KETTLE_MASTER.PAYMENT_MODE pm ON ose.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

        AND od.ORDER_SOURCE = 'COD'

            AND eue.EVENT_ID = EXPENSE_EVENT_ID

    GROUP BY od.UNIT_ID , eue.EVENT_ID) q ON ud.UNIT_ID = q.UNIT_ID

    LEFT JOIN (SELECT

    x.UNIT_ID UNIT_ID,

    x.EVENT_ID EVENT_ID,

    CASE

        WHEN p.GIFT_CARD_TICKETS IS NULL THEN x.TOTAL_TICKET

        ELSE x.TOTAL_TICKET - p.GIFT_CARD_TICKETS

    END TOTAL_TICKET,

     CASE

        WHEN p.GIFT_CARD_TICKETS IS NULL THEN x.TKT

        ELSE x.TKT - p.GIFT_CARD_TICKETS

    END TKT,

    CASE

        WHEN p.GIFT_CARD_AMOUNT IS NULL THEN x.SALES

        ELSE x.SALES - p.GIFT_CARD_AMOUNT

    END SALES,

    x.DISCOUNT DISCOUNT,

    x.PROMOTIONAL PROMOTIONAL

FROM

    (SELECT

        od.UNIT_ID UNIT_ID,

            eue.EVENT_ID EVENT_ID,

            COUNT(*) AS TOTAL_TICKET,

            SUM(CASE

                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1

                ELSE 0

            END) AS TKT,

            SUM(COALESCE(od.TAXABLE_AMOUNT, 0)) AS SALES,

            SUM(COALESCE(od.DISCOUNT_AMOUNT, 0)) AS DISCOUNT,

            SUM(COALESCE(od.PROMOTIONAL_DISCOUNT, 0)) AS PROMOTIONAL

    FROM

        KETTLE.ORDER_DETAIL od

    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

        AND od.ORDER_SOURCE = 'COD'

            AND eue.EVENT_ID = EXPENSE_EVENT_ID

    GROUP BY od.UNIT_ID , eue.EVENT_ID) x

        LEFT OUTER JOIN

    (SELECT

        a.UNIT_ID,

            a.EVENT_ID,

            SUM(CASE

                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1

                ELSE 0

            END) GIFT_CARD_TICKETS,

            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT

    FROM

        (SELECT

        oi.ORDER_ID,

            eue.EVENT_ID,

            od.UNIT_ID,

            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,

            od.SETTLED_AMOUNT

    FROM

        KETTLE.ORDER_ITEM oi

    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

        AND od.ORDER_SOURCE = 'COD'

        AND eue.EVENT_ID = EXPENSE_EVENT_ID

            AND oi.PRODUCT_ID IN (1026 , 1027, 1048)

    GROUP BY oi.ORDER_ID , eue.EVENT_ID , od.UNIT_ID) a

    GROUP BY a.EVENT_ID , a.UNIT_ID) p ON p.UNIT_ID = x.UNIT_ID

        AND p.EVENT_ID = x.EVENT_ID) AS r ON ud.UNIT_ID = r.UNIT_ID

        AND q.EVENT_ID = r.EVENT_ID

    LEFT JOIN (SELECT

        c.UNIT_ID,

            c.EVENT_ID,

            SUM(c.COGS) AS COGS_1,

            SUM(c.UNSATISFIED_QUANTITY) AS UNSATISFIED_QUANTITY,

            SUM(c.SAMPLING_MARKETING) AS SAMPLING_MARKETING,

            SUM(c.GMV) AS GMV,

            SUM(c.NEWGMV) AS NEWGMV

    FROM

        (SELECT

        a.UNIT_ID,

            a.EVENT_ID,

            a.PRODUCT_ID,

            a.DIMENSION,

			( CASE WHEN a.ORDER_SOURCE = 'COD'

            THEN

				COALESCE(a.SOLD_QUANTITY,0) * COALESCE(b.COD_COST,0)

			ELSE

				COALESCE(a.SOLD_QUANTITY,0) * COALESCE(b.COST,0)

			END ) AS COGS,

			( CASE WHEN a.ORDER_SOURCE = 'COD'

            THEN

				COALESCE(a.UNSATISFIED_QUANTITY,0) * COALESCE(b.COD_COST,0)

			ELSE

				COALESCE(a.UNSATISFIED_QUANTITY,0) * COALESCE(b.COST,0)

			END ) AS UNSATISFIED_QUANTITY,

			( CASE WHEN a.ORDER_SOURCE = 'COD'

            THEN

				COALESCE(a.SAMPLING_MARKETING,0) * COALESCE(b.COD_COST,0)

			ELSE

				COALESCE(a.SAMPLING_MARKETING,0) * COALESCE(b.COST,0)

			END ) AS SAMPLING_MARKETING,

            COALESCE(a.GMV,0)GMV,

            COALESCE(a.SOLD_QUANTITY,0) * b.PRICE AS NEWGMV

    FROM

        (SELECT

        od.UNIT_ID,

            eue.EVENT_ID,

            pd.PRODUCT_ID,

            rl2.RL_ID AS DIMENSION,

            oi.PRICE,

			od.ORDER_SOURCE,

            SUM(CASE

			WHEN

				IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL

				OR (IS_COMPLIMENTARY = 'Y'

				AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106))

			THEN

                    oi.QUANTITY

                ELSE 0

            END) SOLD_QUANTITY,

            SUM(CASE

                WHEN

                    IS_COMPLIMENTARY = 'Y'

                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2100

                THEN

                    oi.QUANTITY

                ELSE 0

            END) EMPLOYEE_MEAL,

            SUM(CASE

                WHEN

                    IS_COMPLIMENTARY = 'Y'

                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102

                THEN

                    oi.QUANTITY

                ELSE 0

            END) UNSATISFIED_QUANTITY,

            SUM(CASE

                WHEN

                    IS_COMPLIMENTARY = 'Y'

                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) IN (2105 , 2106)

                THEN

                    oi.QUANTITY

                ELSE 0

            END) SAMPLING_MARKETING,

            SUM(CASE

                WHEN

					pd.PRODUCT_TYPE <> 8 AND (

                    IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL

                        OR (IS_COMPLIMENTARY = 'Y'

                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)))

                        AND pd.PRODUCT_ID NOT IN (1026 , 1027, 1048)

                THEN

                    oi.QUANTITY * oi.PRICE

                ELSE 0

            END) GMV

    FROM

        KETTLE.ORDER_DETAIL od

    LEFT JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID

    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID

    LEFT JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID

    LEFT JOIN KETTLE_MASTER.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID

    LEFT JOIN KETTLE_MASTER.REF_LOOKUP rl2 ON oi.DIMENSION = rl2.RL_CODE

        AND rl2.RTL_ID = pd.DIMENSION_CODE

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

        AND od.ORDER_SOURCE = 'COD'

            AND eue.EVENT_ID = EXPENSE_EVENT_ID

    GROUP BY od.UNIT_ID , eue.EVENT_ID , pd.PRODUCT_ID , rl2.RL_ID , oi.PRICE, od.ORDER_SOURCE) AS a

    INNER JOIN

			(SELECT

			upm.UNIT_ID,

            upm.PRODUCT_ID,

            upp.DIMENSION_CODE,

            upp.PRICE,

            upp.COST,

			upp.COD_COST

    FROM

        KETTLE_MASTER.UNIT_PRODUCT_MAPPING upm

    LEFT JOIN KETTLE_MASTER.UNIT_PRODUCT_PRICING upp ON upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID) AS b

                ON a.UNIT_ID = b.UNIT_ID

        AND a.PRODUCT_ID = b.PRODUCT_ID

       AND a.DIMENSION = b.DIMENSION_CODE) AS c

    GROUP BY c.UNIT_ID , c.EVENT_ID) AS t

                ON ud.UNIT_ID = t.UNIT_ID

        AND q.EVENT_ID = t.EVENT_ID

    LEFT JOIN (SELECT

        p.UNIT_ID,

		p.EVENT_ID,

		SUM(

		CASE

			WHEN p.ORDER_SOURCE = 'COD'

            THEN

					COALESCE(p.QUANTITY,0) * COALESCE(b.COD_COST,0)

				ELSE

					COALESCE(p.QUANTITY,0) * COALESCE(b.COST,0)

			END) AS COGS_2

    FROM

        (SELECT

        od.UNIT_ID,

            eue.EVENT_ID,

            pd1.PRODUCT_ID,

            (CASE

                WHEN pd1.DIMENSION_CODE = 1 THEN 1

                WHEN

                    pd1.DIMENSION_CODE = 2

                        AND od.ORDER_SOURCE = 'CAFE'

                THEN

                    21

                WHEN

                    pd1.DIMENSION_CODE = 2

                        AND od.ORDER_SOURCE = 'COD'

                THEN

                    24

                WHEN pd1.DIMENSION_CODE = 3 THEN 30

                WHEN

                    pd1.DIMENSION_CODE = 11

                        AND od.ORDER_SOURCE = 'CAFE'

                THEN

                    1101

                WHEN

                    pd1.DIMENSION_CODE = 11

                        AND od.ORDER_SOURCE = 'COD'

                THEN

                    1102

                WHEN

                    pd1.DIMENSION_CODE = 34

                        AND od.ORDER_SOURCE = 'CAFE'

                THEN

                    3401

                WHEN

                    pd1.DIMENSION_CODE = 34

                        AND od.ORDER_SOURCE = 'COD'

                THEN

                    3402

                ELSE 1

            END) AS DIMENSIONS,

            SUM(oi.QUANTITY) AS QUANTITY,

			od.ORDER_SOURCE

    FROM

        KETTLE.ORDER_DETAIL od

    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID

    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID

    INNER JOIN KETTLE.ORDER_ITEM_ADDON oia ON oi.ORDER_ITEM_ID = oia.ORDER_ITEM_ID

    INNER JOIN KETTLE.ADDON_PRODUCT_DATA apd ON oia.ADDON_ID = apd.ADDON_ID

    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd1 ON pd1.PRODUCT_ID = apd.PRODUCT_ID

    INNER JOIN KETTLE.EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID

        AND od.ORDER_ID <= eue.END_ORDER_ID

    WHERE

        od.ORDER_STATUS <> 'CANCELLED'

			AND od.ORDER_SOURCE = 'COD'

			AND eue.EVENT_ID = EXPENSE_EVENT_ID

            AND pd.PRODUCT_TYPE = 8

            AND apd.ADDON_ID IS NOT NULL

    GROUP BY od.UNIT_ID , eue.EVENT_ID , pd1.PRODUCT_ID , DIMENSIONS , od.ORDER_SOURCE) AS p

    INNER JOIN (SELECT

        upm.UNIT_ID,

            upm.PRODUCT_ID,

            upp.DIMENSION_CODE,

            upp.PRICE,

            upp.COST,

			upp.COD_COST

    FROM

        KETTLE_MASTER.UNIT_PRODUCT_MAPPING upm

    LEFT JOIN KETTLE_MASTER.UNIT_PRODUCT_PRICING upp ON upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID) AS b

                ON p.UNIT_ID = b.UNIT_ID

        AND p.PRODUCT_ID = b.PRODUCT_ID

        AND p.DIMENSIONS = b.DIMENSION_CODE

    GROUP BY p.UNIT_ID , p.EVENT_ID) AS y ON ud.UNIT_ID = y.UNIT_ID

        AND q.EVENT_ID = y.EVENT_ID) AS z

ON   ued.UNIT_ID = z.UNIT_ID

        AND ued.EXPENSE_UPDATE_EVENT_ID = z.EVENT_ID       

SET

    ued.DEL_GMV = COALESCE(z.GMV, 0),

    ued.DEL_SALES = COALESCE(z.SALES, 0),

    ued.DEL_NET_TICKETS = COALESCE(z.TKT, 0),

    ued.DEL_TOTAL_TICKETS = COALESCE(z.TOTAL_TICKET, 0),

    ued.DEL_COGS = COALESCE(z.TOTAL_COGS, 0),

    ued.DEL_MARKETING_AND_SAMPLING = COALESCE(z.SAMPLING_MARKETING, 0),

    ued.DEL_UNSATISFIED_CUSTOMER_COST = COALESCE(z.UNSATISFIED_QUANTITY, 0),

    ued.DEL_PAYTM_CHARGES = COALESCE(z.PAYTM_CHARGES, 0),

    ued.DEL_PAYTM_ONLINE_CHARGES = COALESCE(z.PAYTM_ONLINE_CHARGES, 0),

    ued.DEL_MOBIKWIK_CHARGES = COALESCE(z.MOBIKWIK_CHARGES, 0),

    ued.DEL_MOBIKWIK_ONLINE_CHARGES = COALESCE(z.MOBIKWIK_ONLINE_CHARGES, 0),

    ued.DEL_RAZORPAY_CHARGES = COALESCE(z.RAZORPAY_CHARGES, 0),

    ued.DEL_FREE_CHARGE_CHARGES = COALESCE(z.FREE_CHARGE_CHARGES, 0)

    where ued.EXPENSE_UPDATE_EVENT_ID = EXPENSE_EVENT_ID

;




UPDATE KETTLE.UNIT_EXPENSE_DETAIL ued

SET

    ued.TOTAL_COST = (COALESCE(ued.COGS, 0) + COALESCE(ued.MANPOWER, 0) + COALESCE(ued.CONSUMABLES_AND_UTILITIES, 0) + COALESCE(ued.MARKETING_AND_SAMPLING, 0) + COALESCE(ued.UNSATISFIED_CUSTOMER_COST, 0) + COALESCE(ued.EMPLOYEE_MEAL, 0) + COALESCE(ued.WASTAGE_AND_EXPIRED, 0) + COALESCE(ued.DELIVERY_COST, 0) + COALESCE(ued.ELECTRICITY, 0) + COALESCE(ued.WATER, 0) + COALESCE(ued.DG_RENT, 0) + COALESCE(ued.DG_CHARGES, 0) + COALESCE(ued.CREDIT_CARD_CHARGES, 0) + COALESCE(ued.AMEX_CARD_CHARGES, 0) + COALESCE(ued.SODEXO_CHARGES, 0) + COALESCE(ued.TKT_RESTAURANT_CHARGES, 0) + COALESCE(ued.CHANNEL_PARTNER, 0) + COALESCE(ued.SCM_RENTEL, 0) + COALESCE(ued.EDC_MACHINE, 0) + COALESCE(ued.FREIGHT_OUTWARD, 0) + COALESCE(ued.CONVENYANCE, 0) + COALESCE(ued.STAFF_WELFARE, 0) + COALESCE(ued.CHANGE_COMMISSION, 0) + COALESCE(ued.COURIER, 0) + COALESCE(ued.PRINTING_AND_STATIONARY, 0) + COALESCE(ued.MISC_EXP, 0) + COALESCE(ued.PARKING_CHARGES, 0) + COALESCE(ued.CLEANING_CHARGES, 0) + COALESCE(ued.NEWSPAPER, 0) + COALESCE(ued.RENT, 0) + COALESCE(ued.CAM_CAHRGES, 0) + COALESCE(ued.INTERNET, 0) + COALESCE(ued.TELEPHONE, 0) + COALESCE(ued.OPS_COST_TOTAL, 0) + COALESCE(ued.KITCHEN_COST_TOTAL, 0) + COALESCE(ued.REPAIR_AND_MAINTENANCE_MINOR, 0) + COALESCE(ued.REPAIR_AND_MAINTENANCE_MAJOR, 0) + COALESCE(ued.MANUAL_ADJUSTMENT, 0) + COALESCE(ued.CUSTOMER_CARE_COST, 0) + COALESCE(ued.MAINTENANCE_TEAM_COST, 0) + COALESCE(ued.TRAINING_TEAM_COST, 0) + COALESCE(ued.IT_TEAM_COST, 0))

WHERE

    EXPENSE_UPDATE_EVENT_ID = EXPENSE_EVENT_ID;


UPDATE KETTLE.UNIT_EXPENSE_DETAIL ued

SET

    ued.EBIDTA_PERCENTAGE = TRUNCATE((COALESCE(ued.NET_SALES_AMOUNT, 0) - COALESCE(ued.TOTAL_COST, 0)) * 100 / (CASE

            WHEN COALESCE(ued.NET_SALES_AMOUNT, 0) = 0 THEN 1

            ELSE ued.NET_SALES_AMOUNT

        END),

        2)

WHERE

    EXPENSE_UPDATE_EVENT_ID = EXPENSE_EVENT_ID;


END$$

DELIMITER ;