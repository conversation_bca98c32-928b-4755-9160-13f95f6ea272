ALTER TABLE KETTLE_DEV.CUSTOMER_ADDRESS_INFO
ADD COLUMN CONTACT VARCHAR(10) ,
ADD COLUMN EMAIL VARCHAR(50) ;

DROP TABLE IF EXISTS KETTLE_JOBS_DUMP.AGGREGATED_RESULT_DEFINITION;

USE KETTLE_JOBS_DUMP;

CREATE TABLE `AGGREGATED_RESULT_DEFINITION` (
       `AGGREGATED_RESULT_DEFINITION_ID` int(11) NOT NULL AUTO_INCREMENT,
       `BUSINESS_DATE` varchar(40) DEFAULT NULL,
       `UNIT_ID` int(11) DEFAULT NULL,
       `UNIT_NAME` varchar(255) DEFAULT NULL,
       `BUSINESS_COST_CENTRE` varchar(255) DEFAULT NULL,
       `REFERENCE_NUMBER` varchar(255) DEFAULT NULL,
       `TRANSACTION_NUMBER` varchar(255) DEFAULT NULL,
       `NARRATION` varchar(255) DEFAULT NULL,
       `STATE` varchar(100) DEFAULT NULL,
       `STATE_CODE` varchar(255) DEFAULT NULL,
       `PARTICULARS` varchar(255) DEFAULT NULL,
       `TOTAL_SALES` decimal(16,6) DEFAULT NULL,
       `IS_DEBIT` varchar(10) DEFAULT NULL,
       `ACCOUNT_CODE` varchar(255) DEFAULT NULL,
       `KEY_TYPE` varchar(40) DEFAULT NULL,
       `KEY_ID` varchar(40) DEFAULT NULL,
       `JOB_ID` int(11) DEFAULT NULL,
       `JOB_EXECUTION_ID` int(11) DEFAULT NULL,
       `JOB_EXECUTION_TIME` timestamp NULL DEFAULT NULL,
       `TYPE_OF_DATA` varchar(40) DEFAULT NULL,
       `DATA_STATUS` varchar(10) DEFAULT NULL,
       `STEP_ID` int(11) DEFAULT NULL,
       PRIMARY KEY (`AGGREGATED_RESULT_DEFINITION_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=1298 DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE KETTLE_JOBS_DEV.JOB_DEFINITION;
SET FOREIGN_KEY_CHECKS = 1;

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('1', 'SALES DATA JOB', 'SALES DATA JOB', 'ADHOC', 'jobs/tally/sales_data_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('2', 'TAX DATA JOB', 'TAX DATA JOB', 'ADHOC', 'jobs/tally/tax_data_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('3', 'CHANNEL PARTNER COLLECTION JOB', 'CHANNEL PARTNER COLLECTION JOB', 'ADHOC', 'jobs/tally/channel_partner_collection_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('4', 'GIFT CARD SALES JOB', 'GIFT CARD SALES JOB', 'ADHOC', 'jobs/tally/gift_card_sales_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('5', 'GIFT CARD DISCOUNT JOB', 'GIFT CARD DISCOUNT JOB', 'ADHOC', 'jobs/tally/gift_card_discount_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('6', 'ROUND_OFF', 'ROUND OFF JOB', 'ADHOC', 'jobs/tally/round_off_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('7', 'PETTY CASH JOB', 'PETTY CASH JOB', 'ADHOC', 'jobs/tally/petty_cash_job.kjb', 'ACTIVE');


INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('8', 'SALES DATA CORRECTION JOB', 'SALES DATA CORRECTION JOB', 'ADHOC', 'jobs/tally/sales_data_correction_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('9', 'GOODS SERVICE JOB', 'GOODS SERVICE JOB', 'ADHOC', 'jobs/tally/goods_service_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('10', 'PETTY CASH HAPPAY JOB', 'PETTY CASH HAPPAY JOB', 'ADHOC', 'jobs/tally/petty_cash_happay_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('11', 'CASH MANAGEMENT JOB', 'CASH MANAGEMENT JOB', 'ADHOC', 'jobs/tally/cash_management_job.kjb', 'ACTIVE');


INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '9', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '9', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('3', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('3', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('3', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('3', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('3', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('3', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('3', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('3', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('3', '9', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('4', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('4', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('4', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('4', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('4', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('4', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('4', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('4', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('4', '9', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('5', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('5', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('5', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('5', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('5', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('5', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('5', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('5', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('5', '9', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('6', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('6', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('6', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('6', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('6', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('6', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('6', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('6', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('6', '9', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('7', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('7', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('7', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('7', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('7', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('7', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('7', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('7', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('7', '9', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('8', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('8', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('8', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('8', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('8', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('8', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('8', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('8', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('8', '9', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('9', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('9', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('9', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('9', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('9', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('9', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('9', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('9', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('9', '9', 'ACTIVE');


INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('10', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('10', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('10', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('10', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('10', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('10', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('10', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('10', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('10', '9', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('11', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('11', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('11', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('11', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('11', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('11', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('11', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('11', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('11', '9', 'ACTIVE');

UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='CLEANING_CHARGES' WHERE BUDGET_CATEGORY='CLEANING_EXPENSE';
UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='COMMISSION_CHANGE_CAFE' WHERE BUDGET_CATEGORY='COMMISSIONS_CHANGE';
UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='FUEL_CHARGES_CAFE' WHERE BUDGET_CATEGORY='FUEL_CHARGES';
UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='BUILDING_MAINTENANCE_CAFE' WHERE BUDGET_CATEGORY='MAINTENANCE_BUILDING';
UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='COMPUTER_IT_MAINTENANCE_CAFE' WHERE BUDGET_CATEGORY='MAINTENANCE_COMPUTER';
UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='EQUIPMENT_MAINTENANCE_CAFE' WHERE BUDGET_CATEGORY='MAINTENANCE_EQUIPMENT';
UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='MAINTENANCE_PEST_CONTROL_CAFE' WHERE BUDGET_CATEGORY='MAINTENANCE_PEST_CONTROL';
UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='VEHICLE_REGULAR_MAINTENANCE_CAFE' WHERE BUDGET_CATEGORY='MAINTENANCE_VEHICLE';
UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='PARKING_CHARGES_CAFE' WHERE BUDGET_CATEGORY='PARKING_CHARGES';
UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='PRINTING_AND_STATIONARY' WHERE BUDGET_CATEGORY='PRINTING_STATIONERY';
UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='STAFF_WELFARE_EXPENSES' WHERE BUDGET_CATEGORY='STAFF_EXPENSES';
UPDATE KETTLE_DEV.VOUCHER_DATA SET BUDGET_CATEGORY='RND_ENGINEERING_EXPENSE' WHERE BUDGET_CATEGORY='R_N_D_ENGINEERING_EXPENSE';

UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='CLEANING_CHARGES' WHERE BUDGET_CATEGORY='CLEANING_EXPENSE';
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='COMMISSION_CHANGE_CAFE' WHERE BUDGET_CATEGORY='COMMISSIONS_CHANGE';
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='FUEL_CHARGES_CAFE' WHERE BUDGET_CATEGORY='FUEL_CHARGES';
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='BUILDING_MAINTENANCE_CAFE' WHERE BUDGET_CATEGORY='MAINTENANCE_BUILDING';
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='COMPUTER_IT_MAINTENANCE_CAFE' WHERE BUDGET_CATEGORY='MAINTENANCE_COMPUTER';
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='EQUIPMENT_MAINTENANCE_CAFE' WHERE BUDGET_CATEGORY='MAINTENANCE_EQUIPMENT';
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='MAINTENANCE_PEST_CONTROL_CAFE' WHERE BUDGET_CATEGORY='MAINTENANCE_PEST_CONTROL';
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='VEHICLE_REGULAR_MAINTENANCE_CAFE' WHERE BUDGET_CATEGORY='MAINTENANCE_VEHICLE';
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='PARKING_CHARGES_CAFE' WHERE BUDGET_CATEGORY='PARKING_CHARGES';
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='PRINTING_AND_STATIONARY' WHERE BUDGET_CATEGORY='PRINTING_STATIONERY';
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='STAFF_WELFARE_EXPENSES' WHERE BUDGET_CATEGORY='STAFF_EXPENSES';
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET BUDGET_CATEGORY='RND_ENGINEERING_EXPENSE' WHERE BUDGET_CATEGORY='R_N_D_ENGINEERING_EXPENSE';



ALTER TABLE `KETTLE_DEV`.`CUSTOMER_INFO`
ADD COLUMN `APP_ID` VARCHAR(55) NULL AFTER `ANNIVERSARY`;

ALTER TABLE `KETTLE_DEV`.`CUSTOMER_INFO`
CHANGE COLUMN `APP_ID` `CUSTOMER_APP_ID` VARCHAR(55) NULL DEFAULT NULL ;
