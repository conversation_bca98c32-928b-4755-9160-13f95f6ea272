CREATE TABLE KETTLE_DEV.RULES_DATA(
RULE_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
RULE_NAME VARCHAR(30) NOT NULL,
RULE_DESCRIPTION VARCHAR(255) NOT NULL,
DAY_SLOT VARCHAR(15) NOT NULL,
RULE_STATUS VARCHAR(15) NOT NULL);

CREATE TABLE KETTLE_DEV.RULES_OPTION_DATA(
OPTION_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
RULE_ID INTEGER NOT NULL ,
OPTION_ID INTEGER NOT NULL ,
OPTION_TYPE VARCHAR(10) NOT NULL ,
OPTION_1_PRODUCT_ID INTEGER NULL,
OPTION_2_PRODUCT_ID INTEGER NULL);

CREATE TABLE KETTLE_DEV.RULES_OPTION_RESULT_DATA(
OPTION_RESULT_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
OPTION_DATA_ID INTEGER NOT NULL,
RESULT_CATEGORY VARCHAR(10) NOT NULL,
COUNT_WITH_DISCOUNT INTEGER NULL,
COUNT_WITHOUT_DISCOUNT INTEGER NULL,
AVAILED_WITH_DISCOUNT INTEGER NULL,
AVAILED_WITHOUT_DISCOUNT INTEGER NULL
);

CREATE TABLE KETTLE_DEV.RULES_EVENT_DATA(
RULES_EVENT_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
OPTION_RESULT_DATA_ID INTEGER NOT NULL,
PRODUCT_LIST VARCHAR(255) NOT NULL,
AVAILED VARCHAR(1) NULL,
COUPON_CODE VARCHAR(20) NULL,
TRIGGERED_BY VARCHAR(20) NULL,
EVENT_TIME TIMESTAMP NULL,
ORDER_ID INTEGER NULL,
QUANTITY INTEGER NULL,
NEW_CUSTOMER VARCHAR(1) NULL
);
