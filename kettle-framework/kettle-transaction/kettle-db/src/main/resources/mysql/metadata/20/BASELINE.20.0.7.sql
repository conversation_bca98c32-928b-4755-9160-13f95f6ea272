CREATE TABLE KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(
    CAMPAIGN_JOURNEY_ID INT(11)  NOT NULL PRIMARY KEY AUTO_INCREMENT,
    SESSION_START_TIME VARCHAR(19),
    UTM_SOURCE VARCHAR(20),
    UTM_MEDIUM VARCHAR(20),
    UTM_CAMPAIGN VARCHAR(20),
    CUSTOMER_ID INT(11),
    CONTACT_NUMBER VARCHAR(12),
    FINAL_STATE VARCHAR(30),
    JOURNEY_TYPE VARCHAR(30)
);

CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_CAMPAIGN_JOURNEY_ID ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(CAMPAIGN_JOURNEY_ID) USING BTREE;
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_UTM_SOURCE ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(UTM_SOURCE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_UTM_MEDIUM ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(UTM_MEDIUM) USING BTREE;
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_UTM_CAMPAIGN ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(UTM_CAMPAIGN) USING BTREE;
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_CUSTOMER_ID ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(CUSTOMER_ID) USING BTREE;
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_CONTACT_NUMBER ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(CONTACT_NUMBER) USING BTREE;
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_FINAL_STATE ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(FINAL_STATE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_JOURNEY_TYPE ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(JOURNEY_TYPE) USING BTREE;

ALTER TABLE `KETTLE_DEV`.`CUSTOMER_CAMPAIGN_JOURNEY`
    ADD COLUMN `ORDER_ID` INT(11) NULL AFTER `JOURNEY_TYPE`,
ADD COLUMN `CAMPAIGN_ID` INT(11) NULL AFTER `ORDER_ID`;

CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_ORDER_ID ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(ORDER_ID) USING BTREE;
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_CAMPAIGN_ID ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(CAMPAIGN_ID) USING BTREE;


ALTER TABLE KETTLE_DEV.MONTHLY_TARGETS_DATA
    ADD COLUMN SEASONAL_PRODUCT_3 INT(11);

DROP TABLE KETTLE_DEV.CAFE_STATUS_CHANNEL_PARTNER ;
CREATE TABLE KETTLE_DEV.CAFE_STATUS_CHANNEL_PARTNER (
                                                        `ID` INT(10) NOT NULL AUTO_INCREMENT,
                                                        `UNIT_ID` INT(11) NOT NULL,
                                                        `UNIT_NAME` VARCHAR(255) NULL,
                                                        `UNIT_REGION` VARCHAR(50) NULL,
                                                        `UNIT_CITY` VARCHAR(128) NULL,
                                                        `IS_CAFE_LIVE` VARCHAR(45) NULL,
                                                        `LAST_UPDATED_TIME` VARCHAR(45) NULL,
                                                        `UPDATION_REQUEST` VARCHAR(45) ,
                                                        `CHANNEL_PARTNER`  VARCHAR(45) NULL,
                                                        `BRAND_NAME` VARCHAR(255) NULL,
                                                        `STATUS_CODE` INT(11) NULL,
                                                        `BRAND_ID` INT(11) NULL,
                                                        `EMPLOYEE_ID` INT(11) NULL,
                                                        `APPLICATION_SOURCE` VARCHAR(45) NULL,
                                                        `CHANNEL_RESTAURANT_ID` VARCHAR(45) ,
                                                        `STATUS_MESSAGE` VARCHAR(255) NULL,
                                                        `STATUS_UPDATE` VARCHAR(255) NULL,
                                                        `PARTNER_ID` INT(10) NULL ,
                                                        PRIMARY KEY (`ID`));



CREATE TABLE KETTLE_DEV.CUSTOMER_CONTACT_INFO_MAPPING(
ID int(11) NOT NULL AUTO_INCREMENT,
CUSTOMER_ID int(11) NOT NULL,
OLD_CONTACT_NUMBER varchar(12) NOT NULL,
NEW_CONTACT_NUMBER varchar(12) NOT NULL,
PRIMARY KEY (ID));

ALTER TABLE KETTLE_DEV.CUSTOMER_INFO ADD COLUMN IS_DELETED VARCHAR(1) DEFAULT "N";

ALTER TABLE KETTLE_DEV.CASH_CARD_EVENT
    ADD COLUMN OPENING_BALANCE DECIMAL(15,2),
ADD COLUMN CLOSING_BALANCE DECIMAL(15,2);

ALTER TABLE KETTLE_DEV.LOYALTY_EVENTS
    ADD COLUMN OPENING_BALANCE INT,
ADD COLUMN CLOSING_BALANCE INT;

CREATE INDEX UNIT_ID ON KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA(UNIT_ID) USING BTREE;
CREATE INDEX BRAND_ID ON KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA(BRAND_ID) USING BTREE;
CREATE INDEX PRODUCT_ID ON KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA(PRODUCT_ID) USING BTREE;
CREATE INDEX DIMENSION ON KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA(DIMENSION) USING BTREE;
CREATE INDEX QUANTITY ON KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA(QUANTITY) USING BTREE;
CREATE INDEX BUSINESS_DATE ON KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA(BUSINESS_DATE) USING BTREE;
CREATE INDEX UPT ON KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA(UPT) USING BTREE;
CREATE INDEX NET_SALES ON KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA(NET_SALES) USING BTREE;
CREATE INDEX CALCULATED_ORDER_SOURCE ON KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA(CALCULATED_ORDER_SOURCE) USING BTREE;
