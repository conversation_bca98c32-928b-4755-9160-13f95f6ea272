CREATE TABLE KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA(
ID INTEGER AUTO_INCREMENT PRIMARY KEY NOT NULL,
UNIT_ID INTEGER NOT NULL,
BRAND_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
DIM<PERSON><PERSON>ON VARCHAR(25) NOT NULL,
QUANTITY INTEGER  NOT NULL,
BUSINESS_DATE DATE NOT NULL,
DAY_OF_WEEK INTEGER NOT NULL,
UPT DECIMAL(10,5)  NULL,
NET_SALES INTEGER NOT NULL,
GMV INTEGER NULL,
DATA_TYPE VARCHAR(20)  NOT NULL
);


CREATE TABLE KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST(
ID INTEGER AUTO_INCREMENT PRIMARY KEY NOT NULL,
UNIT_ID INTEGER NOT NULL,
BRAND_ID INTEGER NOT NULL,
DAY_OF_WEEK INTEGER NOT NULL,
TARGET_DATE DATE NOT NULL,
TARGET_SALE INTEGER NOT NULL,
CALCULATION_DATES VARCHAR(500) NULL,
STATUS VARCHAR(10) NOT NULL
);

CREATE TABLE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(
ID INTEGER AUTO_INCREMENT PRIMARY KEY NOT NULL,
REQUEST_ID INTEGER NOT NULL,
BUSINESS_DATE DATE NOT NULL,
DAY_OF_WEEK INTEGER NOT NULL,
UNIT_ID INTEGER NOT NULL,
BRAND_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
DIMENSION VARCHAR(25) NOT NULL,
AVG_UPT DECIMAL(10,4) NOT NULL,
NET_SALES INTEGER NOT NULL,
TOTAL_SALE INTEGER NOT  NULL,
AVG_PRICE DECIMAL(10,2) NOT NULL,
SUGGESTED_QUANTITY DECIMAL(10,2) NULL,
CATEGORY_BUFFER_SUGGESTED_QUANTITY DECIMAL(10,2) NULL,
ROUND_SUGST_QUANTITY INTEGER  NULL,
SUGGESTED_SALES INTEGER NULL,
DATA_TYPE VARCHAR(20)  NOT NULL
);

ALTER TABLE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA
ADD COLUMN CATEGORY_BUFFER_APPLIED VARCHAR(20) NULL,
ADD COLUMN QUANTITY_INCREMENT INTEGER NULL;


ALTER TABLE `KETTLE_SCM_DEV`.`ESTIMATE_REQUEST_DATA`
CHANGE COLUMN `SUGGESTED_SALES` `SUGGESTED_SALES` DECIMAL(10,4) NULL DEFAULT NULL;


USE `KETTLE_DEV`;
DROP procedure IF EXISTS `DAY_CLOSE_ESTIMATE_DATA_PROC`;

DELIMITER $$
USE `KETTLE_DEV`$$
CREATE  PROCEDURE `DAY_CLOSE_ESTIMATE_DATA_PROC`(IN IN_UNIT_ID INTEGER, IN IN_BIZ_DATE DATE, IN_MAX_TAXABLE_AMOUNT INTEGER)
BEGIN
DECLARE TOTAL_NET_SALES INT DEFAULT 0;
DELETE FROM KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA
WHERE
    UNIT_ID = IN_UNIT_ID
    AND BUSINESS_DATE = IN_BIZ_DATE;
# inserting
INSERT INTO KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA (
UNIT_ID ,
BRAND_ID ,
PRODUCT_ID ,
DIMENSION ,
QUANTITY ,
BUSINESS_DATE ,
DAY_OF_WEEK ,
UPT ,
NET_SALES ,
GMV ,
DATA_TYPE
)

SELECT m.UNIT_ID,m.BRAND_ID,m.PRODUCT_ID,m.DIMENSION,SUM(m.QUANTITY),m.BUSINESS_DATE,DAYOFWEEK(m.BUSINESS_DATE),NULL,SUM(m.NET_SALE),SUM(m.GMV),m.DATA_TYPE
FROM(
SELECT
	od.BRAND_ID,
	od.BUSINESS_DATE,
    od.UNIT_ID,DAYOFWEEK(od.BUSINESS_DATE) AS DAYOFWEEK,
    oi.PRODUCT_ID,
    SUM(oi.QUANTITY) AS QUANTITY,
	oi.DIMENSION,
    SUM(oi.AMOUNT_PAID) AS NET_SALE,
    SUM(oi.PRICE * oi.QUANTITY) AS GMV,
    'CALCULATED' DATA_TYPE
    FROM KETTLE_DEV.ORDER_DETAIL od
INNER JOIN KETTLE_DEV.ORDER_ITEM oi ON od.ORDER_ID=oi.ORDER_ID
WHERE od.BUSINESS_DATE = IN_BIZ_DATE
	AND od.UNIT_ID=IN_UNIT_ID
    -- AND od.BRAND_ID=1
    AND oi.COMBO_CONSTITUENT<> 'Y'
    AND oi.TAX_CODE NOT IN ( 'COMBO', 'GIFT_CARD')
    AND od.ORDER_STATUS <> 'CANCELLED'
	AND od.SETTLED_AMOUNT < IN_MAX_TAXABLE_AMOUNT
	AND oi.PARENT_ITEM_ID IS NULL
    AND od.IS_GIFT_CARD_ORDER <>"Y"
GROUP BY oi.PRODUCT_ID,oi.DIMENSION,od.BRAND_ID

UNION ALL

SELECT
	od.BRAND_ID,
	od.BUSINESS_DATE,
    od.UNIT_ID,DAYOFWEEK(od.BUSINESS_DATE) AS DAYOFWEEK,
    b.PRODUCT_ID,
    SUM(b.QUANTITY) AS QUANTITY,
    b.DIMENSION,
    SUM((b.PRICE * b.QUANTITY) / a.TOTAL_AMOUNT * a.AMOUNT_PAID) AS NET_SALE,
    SUM(b.PRICE * b.QUANTITY) GMV,
    'CALCULATED' DATA_TYPE


FROM
    (SELECT
        a.PARENT_ITEM_ID, a.TOTAL_AMOUNT, b.AMOUNT_PAID
    FROM
        (SELECT
        oi.PARENT_ITEM_ID PARENT_ITEM_ID,
            SUM(oi.QUANTITY * oi.PRICE) TOTAL_AMOUNT
    FROM
        KETTLE_DEV.ORDER_DETAIL od, KETTLE_DEV.ORDER_ITEM oi
    WHERE
        od.ORDER_ID = oi.ORDER_ID
            AND od.BUSINESS_DATE = IN_BIZ_DATE
            AND od.UNIT_ID = IN_UNIT_ID
            AND oi.PARENT_ITEM_ID IS NOT NULL
    GROUP BY oi.PARENT_ITEM_ID) a, KETTLE_DEV.ORDER_ITEM b
    WHERE
        a.PARENT_ITEM_ID = b.ORDER_ITEM_ID) a,
    KETTLE_DEV.ORDER_ITEM b,
    KETTLE_DEV.ORDER_DETAIL od
WHERE
    a.PARENT_ITEM_ID = b.PARENT_ITEM_ID
    and b.ORDER_ID = od.ORDER_ID
    GROUP BY b.PRODUCT_ID,b.DIMENSION,od.BRAND_ID)m
GROUP BY m.PRODUCT_ID,m.DIMENSION,m.BRAND_ID;



SELECT
    SUM(NET_SALES)
INTO TOTAL_NET_SALES FROM
    KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA
WHERE
    UNIT_ID = IN_UNIT_ID
        AND BUSINESS_DATE = IN_BIZ_DATE;

UPDATE KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA
SET
    UPT = QUANTITY * 1000 / TOTAL_NET_SALES
WHERE
    UNIT_ID = IN_UNIT_ID
        AND BUSINESS_DATE = IN_BIZ_DATE;

SELECT
    *
FROM
    KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA where BUSINESS_DATE = IN_BIZ_DATE AND UNIT_ID = IN_UNIT_ID;

END$$

DELIMITER ;




USE `KETTLE_SCM_DEV`;
DROP procedure IF EXISTS `ESTIMATE_QUERY_REQUEST_PROC`;

DELIMITER $$
USE `KETTLE_SCM_DEV`$$
CREATE  PROCEDURE `ESTIMATE_QUERY_REQUEST_PROC`(IN_BRAND_ID INTEGER,IN_UNIT_ID INTEGER,IN_TARGET_DATE DATE,IN_TARGET_SALE INTEGER,IN_CALCULATION_DATES VARCHAR(500))
BEGIN
UPDATE KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST
SET STATUS = 'IN_ACTIVE'
WHERE UNIT_ID= IN_UNIT_ID AND BRAND_ID=IN_BRAND_ID AND TARGET_DATE = IN_TARGET_DATE;

INSERT INTO KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST (
UNIT_ID ,
DAY_OF_WEEK ,
TARGET_DATE ,
BRAND_ID,
TARGET_SALE ,
CALCULATION_DATES,
STATUS
)
 VALUES(
 IN_UNIT_ID,DAYOFWEEK(IN_TARGET_DATE),IN_TARGET_DATE,IN_BRAND_ID,IN_TARGET_SALE,IN_CALCULATION_DATES,'ACTIVE');


SELECT ID  FROM KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST EQ
WHERE EQ.UNIT_ID=IN_UNIT_ID AND EQ.BRAND_ID=IN_BRAND_ID AND EQ.TARGET_DATE = IN_TARGET_DATE AND EQ.STATUS ='ACTIVE';

-- SELECT REQUEST_ID;


END$$

DELIMITER ;




USE `KETTLE_SCM_DEV`;
DROP procedure IF EXISTS `PROC_ESTIMATE_REQUEST_DATA`;

DELIMITER $$
USE `KETTLE_SCM_DEV`$$
CREATE  PROCEDURE `PROC_ESTIMATE_REQUEST_DATA`(IN_BRAND_ID INTEGER,IN_UNIT_ID INTEGER,IN_TARGET_DATE DATE,IN_TARGET_SALE INTEGER,IN_CALCULATION_DATES VARCHAR(500),IN_CATEGORY_BUFFER INTEGER, IN_BUFFER INTEGER,IN_PRODUCT_IDS varchar(500),IN IN_REQUEST_ID INTEGER)
BEGIN
DECLARE TOTAL_NET_SALES INT DEFAULT 0;
DECLARE EXTRA_TARGET_SALES INT DEFAULT 0;
DECLARE UPDATED_QUANTITY INT DEFAULT 0;
DECLARE UPDATED_SALES INT DEFAULT 0;
DECLARE PRICE INT DEFAULT 0;
DECLARE UNIQUE_IDENTIFIER INT DEFAULT 0;
DECLARE COUNTER INT DEFAULT 0;
DECLARE MAX_COUNTER INT DEFAULT 0;
DECLARE INCREMENT INT DEFAULT 0;




-- UPDATE ESTIMATE_QUERY_REQUEST
-- SET STATUS = 'IN_ACTIVE'
-- WHERE UNIT_ID= UNIT_ID AND TARGET_DATE = TARGET_DATE;

-- INSERT INTO ESTIMATE_QUERY_REQUEST (
-- UNIT_ID ,
-- DAY_OF_WEEK ,
-- TARGET_DATE ,
-- TARGET_SALE ,
-- CALCULATION_DATES,
-- STATUS
-- )
 -- VALUES(
 -- UNIT_ID,DAYOFWEEK(TARGET_DATE),TARGET_DATE,TARGET_SALE,SUBSTRING_INDEX(CALCULATION_DATES, ',', DATE_COUNT),'ACTIVE');



-- DELETE FROM ESTIMATE_REQUEST_DATA
-- WHERE UNIT_ID=UNIT_ID AND BUSINESS_DATE = TARGET_DATE;

-- SELECT eqr.ID
-- INTO REQUEST_ID
-- FROM KETTLE_DEV.ESTIMATE_QUERY_REQUEST eqr
  --   WHERE eqr.UNIT_ID = UNIT_ID AND eqr.TARGET_DATE=TARGET_DATE AND eqr.STATUS='ACTIVE';

INSERT INTO KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(
REQUEST_ID,
BUSINESS_DATE,
DAY_OF_WEEK,
UNIT_ID,
BRAND_ID,
PRODUCT_ID,
DIMENSION,
AVG_UPT,
NET_SALES,
AVG_PRICE,
TOTAL_SALE,
SUGGESTED_QUANTITY,
CATEGORY_BUFFER_SUGGESTED_QUANTITY,
ROUND_SUGST_QUANTITY,
SUGGESTED_SALES,
DATA_TYPE,
QUANTITY_INCREMENT
)
SELECT
      IN_REQUEST_ID,
     IN_TARGET_DATE,DAYOFWEEK(IN_TARGET_DATE),
      m.UNIT_ID,m.BRAND_ID,m.PRODUCT_ID,m.DIMENSION, m.AVG_UPT,SUM(m.NET_SALES),m.AVG_PRICE,IN_TARGET_SALE ,m.SGST_QTY,NULL,NULL,NULL,'CALCULATED',0
FROM(
SELECT
	DC.BRAND_ID,
	DC.BUSINESS_DATE,
    DC.UNIT_ID,
    DC.PRODUCT_ID,
    DC.NET_SALES,
    SUM(DC.QUANTITY) AS TOTAL_QUANTITY,
	DC.DIMENSION,
    SUM(DC.NET_SALES)/SUM(DC.QUANTITY) AS AVG_PRICE,
    IN_TARGET_SALE*AVG(DC.UPT)/1000 AS SGST_QTY,
    AVG(DC.UPT) AS AVG_UPT,
    'CALCULATED' DATA_TYPE
    FROM KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA DC, (SELECT
    upm.UNIT_ID, upm.PRODUCT_ID, rl.RL_CODE DIMENSION
FROM
    KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING upm,
    KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING upp,
    KETTLE_MASTER_DEV.REF_LOOKUP rl
WHERE
    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
    AND upp.DIMENSION_CODE = rl.RL_ID
        AND upm.UNIT_ID = IN_UNIT_ID
        AND upm.PRODUCT_STATUS = 'ACTIVE'
        AND upp.PRICING_STATUS = 'ACTIVE') AP
          WHERE FIND_IN_SET(DC.BUSINESS_DATE ,IN_CALCULATION_DATES)>0
               AND DC.UNIT_ID=IN_UNIT_ID
               AND DC.BRAND_ID=IN_BRAND_ID
               AND DC.UNIT_ID =IN_UNIT_ID
               AND AP.PRODUCT_ID = DC.PRODUCT_ID
               and AP.DIMENSION = DC.DIMENSION
    GROUP BY DC.PRODUCT_ID,DC.DIMENSION,DC.BRAND_ID)m
    GROUP BY m.PRODUCT_ID,m.DIMENSION,m.BRAND_ID;




UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
SET
    ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY = ER.SUGGESTED_QUANTITY + (IN_CATEGORY_BUFFER * ER.SUGGESTED_QUANTITY) / 100,
    ER.CATEGORY_BUFFER_APPLIED = 'Y'
WHERE
    FIND_IN_SET(ER.PRODUCT_ID,
            IN_PRODUCT_IDS) > 0
        AND UNIT_ID = IN_UNIT_ID
        AND BUSINESS_DATE = IN_TARGET_DATE
        AND ER.REQUEST_ID = IN_REQUEST_ID
        AND ER.BRAND_ID = IN_BRAND_ID;

    UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
SET
    ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY = ER.SUGGESTED_QUANTITY,
    ER.CATEGORY_BUFFER_APPLIED = 'N'
WHERE
   ER.UNIT_ID = IN_UNIT_ID
        AND BUSINESS_DATE = IN_TARGET_DATE
        AND ER.REQUEST_ID = IN_REQUEST_ID
        AND ER.CATEGORY_BUFFER_APPLIED IS NULL
        AND ER.BRAND_ID = IN_BRAND_ID;

UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
SET
    ER.ROUND_SUGST_QUANTITY = CEILING(ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY),
    ER.SUGGESTED_SALES = ER.AVG_PRICE * CEILING(ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY)
WHERE
    ER.UNIT_ID = IN_UNIT_ID
        AND ER.BUSINESS_DATE = IN_TARGET_DATE
        AND ER.REQUEST_ID = IN_REQUEST_ID
        AND ER.BRAND_ID = IN_BRAND_ID;



SELECT SUM(SUGGESTED_SALES)
INTO TOTAL_NET_SALES
FROM KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA  ER
WHERE ER.UNIT_ID = IN_UNIT_ID AND ER.BUSINESS_DATE =IN_TARGET_DATE AND ER.REQUEST_ID=IN_REQUEST_ID AND ER.BRAND_ID = IN_BRAND_ID;

SET EXTRA_TARGET_SALES=IN_TARGET_SALE+ (IN_BUFFER* IN_TARGET_SALE)/100;

SELECT COUNT(*)
INTO MAX_COUNTER
FROM KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA  ER
WHERE ER.UNIT_ID = IN_UNIT_ID AND ER.BUSINESS_DATE =IN_TARGET_DATE AND ER.REQUEST_ID=IN_REQUEST_ID AND ER.BRAND_ID = IN_BRAND_ID;

IF IN_BUFFER>0 THEN
        WHILE TOTAL_NET_SALES < EXTRA_TARGET_SALES DO

            SELECT ER.ROUND_SUGST_QUANTITY,ER.SUGGESTED_SALES,ER.AVG_PRICE,ER.ID,ER.QUANTITY_INCREMENT
            INTO UPDATED_QUANTITY,UPDATED_SALES,PRICE,UNIQUE_IDENTIFIER,INCREMENT
            FROM KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA  ER
            WHERE ER.UNIT_ID =IN_UNIT_ID AND ER.BUSINESS_DATE= IN_TARGET_DATE AND ER.REQUEST_ID=IN_REQUEST_ID AND ER.BRAND_ID = IN_BRAND_ID
            ORDER BY ER.ROUND_SUGST_QUANTITY DESC LIMIT COUNTER,1;
            SET COUNTER=COUNTER+1;
            SET UPDATED_QUANTITY= UPDATED_QUANTITY+1;
            SET UPDATED_SALES= UPDATED_SALES+PRICE;
            SET TOTAL_NET_SALES= TOTAL_NET_SALES+PRICE;

            UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA  ER
            SET ER.QUANTITY_INCREMENT=  INCREMENT+1,
            ER.ROUND_SUGST_QUANTITY=UPDATED_QUANTITY,
            ER.SUGGESTED_SALES=UPDATED_SALES
            WHERE ER.ID=UNIQUE_IDENTIFIER;

            IF COUNTER=MAX_COUNTER THEN
              SET COUNTER=0;
            END IF;
            END WHILE;
          END IF;

SELECT
A.PRODUCT_ID,
A.DIMENSION,
A.CATEGORY_ID,
A.CATEGORY,
COALESCE (B.ROUND_SUGST_QUANTITY,0) QUANTITY,
A.UNIT_ID,
COALESCE (B.SUGGESTED_SALES,0) SUGGESTED_SALES
 FROM (
        SELECT
    UPM.UNIT_ID, UPM.PRODUCT_ID, RL.RL_CODE DIMENSION,RTL.RTL_NAME CATEGORY,RTL.RTL_ID CATEGORY_ID
FROM
    KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING UPM,
    KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING UPP,
    KETTLE_MASTER_DEV.REF_LOOKUP RL,
    KETTLE_MASTER_DEV.PRODUCT_DETAIL PD,
    KETTLE_MASTER_DEV.REF_LOOKUP_TYPE RTL

WHERE
    UPM.UNIT_PROD_REF_ID = UPP.UNIT_PROD_REF_ID
    AND UPP.DIMENSION_CODE = RL.RL_ID
    AND UPM.PRODUCT_ID = PD.PRODUCT_ID AND PD.PRODUCT_TYPE = RTL.RTL_ID
        AND UPM.UNIT_ID = IN_UNIT_ID
        AND UPM.PRODUCT_STATUS = 'ACTIVE'
        AND UPP.PRICING_STATUS = 'ACTIVE') A LEFT OUTER JOIN ( SELECT
    *
FROM
    KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
WHERE
        ER.UNIT_ID = IN_UNIT_ID
        AND ER.REQUEST_ID = IN_REQUEST_ID)B
        ON A.UNIT_ID = B.UNIT_ID
        AND A.PRODUCT_ID = B.PRODUCT_ID
        AND B.BRAND_ID=IN_BRAND_ID
        AND A.DIMENSION = B.DIMENSION
        ORDER BY A.CATEGORY,A.PRODUCT_ID;

END$$

DELIMITER ;









CREATE INDEX PRODUCT_ID_DAY_CLOSE_INDEX ON KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA (PRODUCT_ID) USING BTREE;
CREATE INDEX DIMENSION_DAY_CLOSE_INDEX ON KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA (DIMENSION) USING BTREE;
CREATE INDEX REQUEST_ID_ESTIMATE_REQUEST ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA (REQUEST_ID) USING BTREE;
CREATE INDEX PRODUCT_ID_ESTIMATE_REQUEST ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA (PRODUCT_ID) USING BTREE;
CREATE INDEX DIMENSION_ESTIMATE_REQUEST ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA (DIMENSION) USING BTREE;
CREATE INDEX PARENT_ITEM_ID_ORDER_ITEM ON KETTLE_DEV.ORDER_ITEM (PARENT_ITEM_ID) USING BTREE;





USE `KETTLE_SCM_DEV`;
DROP procedure IF EXISTS `PROC_ESTIMATE_AGGREGATE_DATA`;

DELIMITER $$
USE `KETTLE_SCM_DEV`$$
CREATE  PROCEDURE `PROC_ESTIMATE_AGGREGATE_DATA`(IN IN_REQUEST_ID varchar(255), IN IN_UNIT_ID INTEGER)
BEGIN

SELECT
A.PRODUCT_ID,
A.DIMENSION,
A.CATEGORY_ID,
A.CATEGORY,
SUM(COALESCE (B.ROUND_SUGST_QUANTITY,0)) QUANTITY,
A.UNIT_ID,
SUM(COALESCE (B.SUGGESTED_SALES,0)) SUGGESTED_SALES
 FROM (
        SELECT
    UPM.UNIT_ID, UPM.PRODUCT_ID, RL.RL_CODE DIMENSION,RTL.RTL_NAME CATEGORY,RTL.RTL_ID CATEGORY_ID
FROM
    KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING UPM,
    KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING UPP,
    KETTLE_MASTER_DEV.REF_LOOKUP RL,
    KETTLE_MASTER_DEV.PRODUCT_DETAIL PD,
    KETTLE_MASTER_DEV.REF_LOOKUP_TYPE RTL
WHERE
    UPM.UNIT_PROD_REF_ID = UPP.UNIT_PROD_REF_ID
    AND UPP.DIMENSION_CODE = RL.RL_ID
    AND UPM.PRODUCT_ID = PD.PRODUCT_ID AND PD.PRODUCT_TYPE = RTL.RTL_ID
        AND UPM.UNIT_ID = IN_UNIT_ID
        AND UPM.PRODUCT_STATUS = 'ACTIVE'
        AND UPP.PRICING_STATUS = 'ACTIVE') A LEFT OUTER JOIN ( SELECT
    *
FROM
    KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
WHERE
        ER.UNIT_ID = IN_UNIT_ID
        AND FIND_IN_SET(ER.REQUEST_ID, IN_REQUEST_ID)>0)B
        ON A.UNIT_ID = B.UNIT_ID
        AND A.PRODUCT_ID = B.PRODUCT_ID
        AND A.DIMENSION = B.DIMENSION
GROUP BY A.PRODUCT_ID,
A.DIMENSION,
A.CATEGORY_ID,
A.CATEGORY,  A.UNIT_ID
ORDER BY A.CATEGORY,A.PRODUCT_ID  ;

END$$

DELIMITER ;


ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN REVENUE_SHARE_DINE_IN DECIMAL(10,2) NULL,
ADD COLUMN REVENUE_SHARE_DELIVERY DECIMAL(10,2) NULL,
ADD COLUMN REVENUE_SHARE_DINE_IN_PERCENTAGE DECIMAL(10,2) NULL,
ADD COLUMN REVENUE_SHARE_DELIVERY_PERCENTAGE DECIMAL(10,2) NULL;

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN REVENUE_SHARE_DINE_IN DECIMAL(10,2) NULL,
ADD COLUMN REVENUE_SHARE_DELIVERY DECIMAL(10,2) NULL;

DROP TABLE IF EXISTS KETTLE_DEV.UNIT_MANPOWER_BUDGET_DETAIL ;
CREATE TABLE KETTLE_DEV.UNIT_MANPOWER_BUDGET_DETAIL (
    UNIT_MANPOWER_BUDGET_DETAIL_ID INT(11) NOT NULL AUTO_INCREMENT,
    UNIT_ID INT(11) DEFAULT NULL,
    CALCULATION_MONTH INT(11) DEFAULT NULL,
    CALCULATION_YEAR INT(11) DEFAULT NULL,
    UNIT_NAME VARCHAR(100) DEFAULT NULL,
    SALARY DECIMAL(10 , 2 ) DEFAULT NULL,
    SALARY_INCENTIVE DECIMAL(10 , 2 ) DEFAULT NULL,
    PERFORMANCE_INCENTIVE DECIMAL(10 , 2 ) DEFAULT NULL,
    BYOD_CHARGES DECIMAL(20 , 2 ) DEFAULT NULL,
    CAR_LEASE_SR DECIMAL(20 , 2 ) DEFAULT NULL,
    DRIVER_SALARY_SR DECIMAL(20 , 2 ) DEFAULT NULL,
    GRATUITY DECIMAL(20 , 2 ) DEFAULT NULL,
    INSUARANCE_ACCIDENTAL DECIMAL(20 , 2 ) DEFAULT NULL,
    INSUARANCE_MEDICAL DECIMAL(20 , 2 ) DEFAULT NULL,
    SUPPORT_OPS_TURNOVER DECIMAL(20 , 2 ) DEFAULT NULL,
    EMPLOYEE_FACILITATION_EXPENSES DECIMAL(20 , 2 ) DEFAULT NULL,
    TELEPHONE_SR DECIMAL(20 , 2 ) DEFAULT NULL,
    VEHICLE_RUNNING_AND_MAINT_SR DECIMAL(20 , 2 ) DEFAULT NULL,
    EMPLOYEE_STOCK_OPTION_EXPENSE DECIMAL(20 , 2 ) DEFAULT NULL,
    EMPLOYER_CONTRIBUTION_LWF DECIMAL(20 , 2 ) DEFAULT NULL,
    ESIC_EMPLOYER_CONT DECIMAL(20 , 2 ) DEFAULT NULL,
    LEAVE_TRAVEL_REIMBURSEMENT DECIMAL(20 , 2 ) DEFAULT NULL,
    PF_ADMINISTRATION_CHARGES DECIMAL(20 , 2 ) DEFAULT NULL,
    PF_EMPLOYER_CONT DECIMAL(20 , 2 ) DEFAULT NULL,
    QUATERLY_INCENTIVE DECIMAL(20 , 2 ) DEFAULT NULL,
    PRE_OPEINING_SALARY DECIMAL(20 , 2 ) DEFAULT NULL,
    BONUS_ATTENDANCE DECIMAL(20 , 2 ) DEFAULT NULL,
    BONUS_JOINING DECIMAL(20 , 2 ) DEFAULT NULL,
    BONUS_REFERRAL DECIMAL(20 , 2 ) DEFAULT NULL,
    ALLOWANCE_REMOTE_LOCATION DECIMAL(20 , 2 ) DEFAULT NULL,
    ALLOWANCE_CITY_COMPENSATORY DECIMAL(20 , 2 ) DEFAULT NULL,
    BONUS_HOLIDAY DECIMAL(20 , 2 ) DEFAULT NULL,
    ALLOWANCE_MONK DECIMAL(20 , 2 ) DEFAULT NULL,
    ALLOWANCE_EMPLOYEE_BENEFIT DECIMAL(20 , 2 ) DEFAULT NULL,
    BONUS_OTHERS DECIMAL(20 , 2 ) DEFAULT NULL,
    ALLOWANCE_OTHERS DECIMAL(20 , 2 ) DEFAULT NULL,
    NOTICE_PERIOD_BUY_OUT DECIMAL(20 , 2 ) DEFAULT NULL,
    NOTICE_PERIOD_DEDUCTION DECIMAL(20 , 2 ) DEFAULT NULL,
    RELOCATION_EXPENSES DECIMAL(20 , 2 ) DEFAULT NULL,
    STIPEND_EXPENSE DECIMAL(20 , 2 ) DEFAULT NULL,
    TRAINING_COST_RECOVERY DECIMAL(20 , 2 ) DEFAULT NULL,
    SEVERANCE_PAY DECIMAL(20 , 2 ) DEFAULT NULL,
    LABOUR_CHARGES DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_SALARY DECIMAL(10 , 2 ) DEFAULT NULL,
    OLD_SALARY_INCENTIVE DECIMAL(10 , 2 ) DEFAULT NULL,
    OLD_PERFORMANCE_INCENTIVE DECIMAL(10 , 2 ) DEFAULT NULL,
    OLD_BYOD_CHARGES DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_CAR_LEASE_SR DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_DRIVER_SALARY_SR DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_GRATUITY DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_INSUARANCE_ACCIDENTAL DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_INSUARANCE_MEDICAL DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_SUPPORT_OPS_TURNOVER DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_EMPLOYEE_FACILITATION_EXPENSES DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_TELEPHONE_SR DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_VEHICLE_RUNNING_AND_MAINT_SR DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_EMPLOYEE_STOCK_OPTION_EXPENSE DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_EMPLOYER_CONTRIBUTION_LWF DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_ESIC_EMPLOYER_CONT DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_LEAVE_TRAVEL_REIMBURSEMENT DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_PF_ADMINISTRATION_CHARGES DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_PF_EMPLOYER_CONT DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_QUATERLY_INCENTIVE DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_PRE_OPEINING_SALARY DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_BONUS_ATTENDANCE DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_BONUS_JOINING DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_BONUS_REFERRAL DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_ALLOWANCE_REMOTE_LOCATION DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_ALLOWANCE_CITY_COMPENSATORY DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_BONUS_HOLIDAY DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_ALLOWANCE_MONK DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_ALLOWANCE_EMPLOYEE_BENEFIT DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_BONUS_OTHERS DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_ALLOWANCE_OTHERS DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_NOTICE_PERIOD_BUY_OUT DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_NOTICE_PERIOD_DEDUCTION DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_RELOCATION_EXPENSES DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_STIPEND_EXPENSE DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_TRAINING_COST_RECOVERY DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_SEVERANCE_PAY DECIMAL(20 , 2 ) DEFAULT NULL,
    OLD_LABOUR_CHARGES DECIMAL(20 , 2 ) DEFAULT NULL,
    BUDGET_STATUS VARCHAR(15) DEFAULT NULL,
    UPDATED_BY VARCHAR(100) DEFAULT NULL,
    UPDATE_TIME TIMESTAMP NULL,
    PRIMARY KEY (UNIT_MANPOWER_BUDGET_DETAIL_ID),
    KEY UNIT_BUDGETORY_DETAIL_UNIT_ID (UNIT_ID) USING BTREE,
    KEY UNIT_BUDGETORY_DETAIL_CALCULATION_MONTH (CALCULATION_MONTH) USING BTREE,
    KEY UNIT_BUDGETORY_DETAIL_CALCULATION_YEAR (CALCULATION_YEAR) USING BTREE,
    KEY UNIT_BUDGETORY_DETAIL_UNIT_NAME (UNIT_NAME) USING BTREE,
    KEY UNIT_BUDGETORY_DETAIL_BUDGET_STATUS (BUDGET_STATUS) USING BTREE
);

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL ADD COLUMN  UPDATED_BY varchar(100) NULL,
 ADD COLUMN   UPDATE_TIME TIMESTAMP NULL;

  ALTER TABLE KETTLE_DEV.UNIT_MANPOWER_BUDGET_DETAIL
  ADD COLUMN   SALES_INCENTIVE DECIMAL(10 , 2 ) DEFAULT NULL,
  ADD COLUMN   OLD_SALES_INCENTIVE DECIMAL(10 , 2 ) DEFAULT NULL;

  ALTER TABLE KETTLE_DEV.UNIT_MANPOWER_BUDGET_DETAIL MODIFY UPDATED_BY INT(11);
 
 
 ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
    ADD COLUMN   BONUS_ATTENDANCE DECIMAL(20 , 2 ) DEFAULT NULL,
    ADD COLUMN   BONUS_JOINING DECIMAL(20 , 2 ) DEFAULT NULL,
    ADD COLUMN   BONUS_REFERRAL DECIMAL(20 , 2 ) DEFAULT NULL,
    ADD COLUMN   ALLOWANCE_REMOTE_LOCATION DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    ALLOWANCE_CITY_COMPENSATORY DECIMAL(20 , 2 ) DEFAULT NULL,
    ADD COLUMN   BONUS_HOLIDAY DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    ALLOWANCE_MONK DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    ALLOWANCE_EMPLOYEE_BENEFIT DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    BONUS_OTHERS DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    ALLOWANCE_OTHERS DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    NOTICE_PERIOD_BUY_OUT DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    NOTICE_PERIOD_DEDUCTION DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    RELOCATION_EXPENSES DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    STIPEND_EXPENSE DECIMAL(20 , 2 ) DEFAULT NULL,
  ADD COLUMN     TRAINING_COST_RECOVERY DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    SEVERANCE_PAY DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    LABOUR_CHARGES DECIMAL(20 , 2 ) DEFAULT NULL;

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
    ADD COLUMN   BONUS_ATTENDANCE DECIMAL(20 , 2 ) DEFAULT NULL,
    ADD COLUMN   BONUS_JOINING DECIMAL(20 , 2 ) DEFAULT NULL,
    ADD COLUMN   BONUS_REFERRAL DECIMAL(20 , 2 ) DEFAULT NULL,
    ADD COLUMN   ALLOWANCE_REMOTE_LOCATION DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    ALLOWANCE_CITY_COMPENSATORY DECIMAL(20 , 2 ) DEFAULT NULL,
    ADD COLUMN   BONUS_HOLIDAY DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    ALLOWANCE_MONK DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    ALLOWANCE_EMPLOYEE_BENEFIT DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    BONUS_OTHERS DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    ALLOWANCE_OTHERS DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    NOTICE_PERIOD_BUY_OUT DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    NOTICE_PERIOD_DEDUCTION DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    RELOCATION_EXPENSES DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    STIPEND_EXPENSE DECIMAL(20 , 2 ) DEFAULT NULL,
  ADD COLUMN     TRAINING_COST_RECOVERY DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    SEVERANCE_PAY DECIMAL(20 , 2 ) DEFAULT NULL,
   ADD COLUMN    LABOUR_CHARGES DECIMAL(20 , 2 ) DEFAULT NULL;