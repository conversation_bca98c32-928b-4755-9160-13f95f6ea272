/*
 * QUERY FOR OFFER "Rs 50 off on a minimum bill of Rs300"
 */

INSERT INTO OFFER_DETAIL_DATA (	OFFER_DETAIL_ID,
								OFFER_CATEGORY,
								OFFER_TYPE,
								OFFER_TEXT,
								OFFER_DESCRIPTION,
								START_DATE,
								END_DATE,
								OFFER_STATUS,
								MIN_VALUE,
								VALIDATE_CUSTOMER,
								INCLUDE_TAXES,
								PRIORITY,
								OFFER_SCOPE,
								MIN_ITEM_COUNT,
								QUANTITY_LIMIT,
								LOYALTY_LIMIT,
								OFFER_VALUE)
								
VALUES(	'1',
		'BILL',
		'FLAT_BILL_STRATEGY',
		'Rs. 50 OFF on a minimum bill of Rs. 300',
		'Get Rs. 50 OFF on a minimum bill of Rs. 300',
		'2016-02-04',
		'2016-03-31',
		'ACTIVE',
		'300',
		'Y',
		'Y',
		'1',
		'CUSTOMER',
		'1',
		'1',
		'0',
		'50');
		
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI01',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'25000',
		'ACTIVE',
		'0',
		'N');
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI02',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'25000',
		'ACTIVE',
		'0',
		'N');
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI03',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'10000',
		'ACTIVE',
		'0',
		'N');
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI04',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'20000',
		'ACTIVE',
		'0',
		'N');
		
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI05',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'15000',
		'ACTIVE',
		'0',
		'N');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI06',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'25000',
		'ACTIVE',
		'0',
		'N');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI07',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'25000',
		'ACTIVE',
		'0',
		'N');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI08',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'25000',
		'ACTIVE',
		'0',
		'N');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI09',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'20000',
		'ACTIVE',
		'0',
		'N');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI10',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'15000',
		'ACTIVE',
		'0',
		'N');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI11',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'25000',
		'ACTIVE',
		'0',
		'N');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI12',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'20000',
		'ACTIVE',
		'0',
		'N');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI14',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'20000',
		'ACTIVE',
		'0',
		'N');


INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI15',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'20000',
		'ACTIVE',
		'0',
		'N');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI16',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'20000',
		'ACTIVE',
		'0',
		'N');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI17',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'20000',
		'ACTIVE',
		'0',
		'N');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI18',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'25000',
		'ACTIVE',
		'0',
		'N');
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI19',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'25000',
		'ACTIVE',
		'0',
		'N');
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI20',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'20000',
		'ACTIVE',
		'0',
		'N');		
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI22',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'25000',
		'ACTIVE',
		'0',
		'N');			
		
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI23',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'20000',
		'ACTIVE',
		'0',
		'N');	
		
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI24',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'20000',
		'ACTIVE',
		'0',
		'N');			
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI25',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'10000',
		'ACTIVE',
		'0',
		'N');			
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI26',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'20000',
		'ACTIVE',
		'0',
		'N');		

		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI27',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'25000',
		'ACTIVE',
		'0',
		'N');				
		
		
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI28',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'15000',
		'ACTIVE',
		'0',
		'N');				

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI29',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'15000',
		'ACTIVE',
		'0',
		'N');			
		
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('1',
		'CHAI30',
		'2016-02-04',
		'2016-03-31',
		'Y',
		'Y',
		'25000',
		'ACTIVE',
		'0',
		'N');	
		
		
		
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('1','UNIT_REGION','MUMBAI','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('2','UNIT_REGION','MUMBAI','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('3','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('4','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('5','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('6','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('7','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('8','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('9','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('10','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('11','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('12','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('13','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('14','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('15','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('16','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('17','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('18','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('19','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('20','UNIT_REGION','NCR','java.lang.Integer','1','1');

INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('21','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('22','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('23','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('24','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('25','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('26','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('27','UNIT_REGION','NCR','java.lang.Integer','1','1');
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
VALUES('28','UNIT_REGION','MUMBAI','java.lang.Integer','1','1');



INSERT INTO `COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`, `MANUAL_OVERRIDE`) VALUES ('1', 'CHAI31', '2016-02-04', '2016-03-31', 'Y', 'Y', '25000', 'ACTIVE', '0', 'N');
INSERT INTO `COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`) VALUES ('1', 'CHAI32', '2016-02-04', '2016-03-31', 'Y', 'Y', '25000', 'ACTIVE', '0');
INSERT INTO `COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`) VALUES ('1', 'CHAI33', '2016-02-04', '2016-03-31', 'Y', 'Y', '25000', 'ACTIVE', '0');
INSERT INTO `COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`) VALUES ('1', 'CHAI34', '2016-02-04', '2016-03-31', 'Y', 'Y', '25000', 'ACTIVE', '0');
INSERT INTO `COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`) VALUES ('1', 'CHAI35', '2016-02-04', '2016-03-31', 'Y', 'Y', '25000', 'ACTIVE', '0');
INSERT INTO `COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`) VALUES ('1', 'CHAI36', '2016-02-04', '2016-03-31', 'Y', 'Y', '25000', 'ACTIVE', '0');
INSERT INTO `COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`) VALUES ('1', 'CHAI37', '2016-02-04', '2016-03-31', 'Y', 'Y', '25000', 'ACTIVE', '0');
INSERT INTO `COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`) VALUES ('1', 'CHAI38', '2016-02-04', '2016-03-31', 'Y', 'Y', '25000', 'ACTIVE', '0');
INSERT INTO `COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`) VALUES ('1', 'CHAI39', '2016-02-04', '2016-03-31', 'Y', 'Y', '25000', 'ACTIVE', '0');
INSERT INTO `COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`) VALUES ('1', 'CHAI40', '2016-02-04', '2016-03-31', 'Y', 'Y', '25000', 'ACTIVE', '0');



INSERT INTO `COUPON_DETAIL_MAPPING_DATA` (`COUPON_DETAIL_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `MAPPING_DATA_TYPE`, `MIN_VALUE`, `MAPPING_GROUP`) VALUES ('29', 'UNIT_REGION', 'NCR', 'java.lang.Integer', '1', '1');
INSERT INTO `COUPON_DETAIL_MAPPING_DATA` (`COUPON_DETAIL_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `MAPPING_DATA_TYPE`, `MIN_VALUE`) VALUES ('30', 'UNIT_REGION', 'NCR', 'java.lang.Integer', '1');
INSERT INTO `COUPON_DETAIL_MAPPING_DATA` (`COUPON_DETAIL_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `MAPPING_DATA_TYPE`, `MIN_VALUE`) VALUES ('31', 'UNIT_REGION', 'NCR', 'java.lang.Integer', '1');
INSERT INTO `COUPON_DETAIL_MAPPING_DATA` (`COUPON_DETAIL_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `MAPPING_DATA_TYPE`, `MIN_VALUE`) VALUES ('32', 'UNIT_REGION', 'NCR', 'java.lang.Integer', '1');
INSERT INTO `COUPON_DETAIL_MAPPING_DATA` (`COUPON_DETAIL_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `MAPPING_DATA_TYPE`, `MIN_VALUE`) VALUES ('33', 'UNIT_REGION', 'NCR', 'java.lang.Integer', '1');
INSERT INTO `COUPON_DETAIL_MAPPING_DATA` (`COUPON_DETAIL_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `MAPPING_DATA_TYPE`, `MIN_VALUE`) VALUES ('34', 'UNIT_REGION', 'NCR', 'java.lang.Integer', '1');
INSERT INTO `COUPON_DETAIL_MAPPING_DATA` (`COUPON_DETAIL_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `MAPPING_DATA_TYPE`, `MIN_VALUE`) VALUES ('35', 'UNIT_REGION', 'NCR', 'java.lang.Integer', '1');
INSERT INTO `COUPON_DETAIL_MAPPING_DATA` (`COUPON_DETAIL_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `MAPPING_DATA_TYPE`, `MIN_VALUE`) VALUES ('36', 'UNIT_REGION', 'NCR', 'java.lang.Integer', '1');
INSERT INTO `COUPON_DETAIL_MAPPING_DATA` (`COUPON_DETAIL_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `MAPPING_DATA_TYPE`, `MIN_VALUE`) VALUES ('37', 'UNIT_REGION', 'NCR', 'java.lang.Integer', '1');
INSERT INTO `COUPON_DETAIL_MAPPING_DATA` (`COUPON_DETAIL_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `MAPPING_DATA_TYPE`, `MIN_VALUE`) VALUES ('38', 'UNIT_REGION', 'NCR', 'java.lang.Integer', '1');
