INSERT INTO LOYALTY_EVENTS (CUSTOMER_ID, TRANSACTION_TYPE, TRANSACTION_POINTS, TRANSACTION_CODE_TYPE, TRANSACTION_CODE, TRANSACTION_STATUS, TRANSACTION_TIME)
VALUES(7484, 'DEBIT',1800, 'Addition', 'OUTLET_VISIT','SUCCESS', current_timestamp);

update LOYALTY_SCORE
set ACQUIRED_POINTS = ACQUIRED_POINTS + 1800,
CUMULATIVE_POINTS = CUMULATIVE_POINTS + 1800
where CUSTOMER_ID = 7484;

INSERT INTO LOYALTY_EVENTS (CUSTOMER_ID, TRANSACTION_TYPE, TRANSACTION_POINTS, TRANSACTION_CODE_TYPE, TRANSACTION_CODE, TRANSACTION_STATUS, TRANSACTION_TIME)
VALUES(14171, 'DEBIT',1800, 'Addition', 'OUTLET_VISIT','SUCCESS', current_timestamp);

update <PERSON>O<PERSON><PERSON><PERSON>_SCORE
set ACQUIRED_POINTS = ACQUIRED_POINTS + 1800,
CUMULATIVE_POINTS = CUMULATIVE_POINTS + 1800
where CUSTOMER_ID = 14171;
