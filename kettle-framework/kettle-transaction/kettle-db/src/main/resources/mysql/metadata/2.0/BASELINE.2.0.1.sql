ALTER TABLE ORDER_ITEM_ADDON
ADD COLUMN PRODUCT_SOURCE_SYSTEM VARCHAR(15) NULL;

ALTER TABLE ORDER_ITEM_ADDON
ADD COLUMN DIMENSION VARCHAR(15) NULL;

ALTER TABLE ORDER_ITEM_ADDON
ADD COLUMN QUANTITY DECIMAL(10, 2) NULL;

ALTER TABLE ORDER_ITEM_ADDON
ADD COLUMN ADDON_NAME VARCHAR(55) NULL;

ALTER TABLE ORDER_ITEM_ADDON
ADD COLUMN ADDON_TYPE VARCHAR(15) NULL;

ALTER TABLE ORDER_ITEM_ADDON
ADD COLUMN UNIT_OF_MEASURE VARCHAR(10) NULL;

ALTER TABLE ORDER_ITEM
ADD COLUMN COMBO_CONSTITUENT VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE ORDER_ITEM
ADD COLUMN PARENT_ITEM_ID INTEGER NULL;

ALTER TABLE ORDER_ITEM_ADDON
DROP FOREIGN KEY `ORDER_ITEM_ADDON_ibfk_1`;


INSERT INTO COMPLIMENTARY_CODE (`COMP_ID`, `COMP_CODE`, `NAME`, `DESCRIPTION`, `STATUS`, `IS_ACCOUNTABLE`) 
VALUES ('1', 'Combo', 'Combo', 'Complimentary Codes', 'ACTIVE', 'Y');

ALTER TABLE SUBSCRIPTION_ITEM_ADDON
ADD COLUMN PRODUCT_SOURCE_SYSTEM VARCHAR(15) NULL;

ALTER TABLE SUBSCRIPTION_ITEM_ADDON
ADD COLUMN DIMENSION VARCHAR(15) NULL;

ALTER TABLE SUBSCRIPTION_ITEM_ADDON
ADD COLUMN QUANTITY DECIMAL(10, 2) NULL;

ALTER TABLE SUBSCRIPTION_ITEM_ADDON
ADD COLUMN ADDON_NAME VARCHAR(55) NULL;

ALTER TABLE SUBSCRIPTION_ITEM_ADDON
ADD COLUMN ADDON_TYPE VARCHAR(15) NULL;

ALTER TABLE SUBSCRIPTION_ITEM_ADDON
ADD COLUMN UNIT_OF_MEASURE VARCHAR(10) NULL;

ALTER TABLE SUBSCRIPTION_ITEM
ADD COLUMN COMBO_CONSTITUENT VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE SUBSCRIPTION_ITEM
ADD COLUMN PARENT_ITEM_ID INTEGER NULL;

ALTER TABLE SUBSCRIPTION_ITEM_ADDON
DROP FOREIGN KEY `SUBSCRIPTION_ITEM_ADDON_ibfk_2`,
DROP FOREIGN KEY `SUBSCRIPTION_ITEM_ADDON_ibfk_1`;

ALTER TABLE ORDER_ITEM_ADDON
ADD COLUMN DEFAULT_SETTING VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE SUBSCRIPTION_ITEM_ADDON
ADD COLUMN DEFAULT_SETTING VARCHAR(1) NOT NULL DEFAULT 'N';

update ORDER_ITEM_ADDON
set PRODUCT_SOURCE_SYSTEM = 'ADDON'
where PRODUCT_SOURCE_SYSTEM is null;


INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('969', 'Adrak', 'Adrak', '12', '1202', 'ACTIVE', NULL, '2016-06-19', '2025-06-19', 'CH120074', '1', 'NET_PRICE', '27', '2016-06-11 19:34:35', '2030-12-01 00:00:00', 'N', 'FREE_ADDON', 'A', 'N', 'N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('970','Ajwain','Ajwain','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120075','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','AJ','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('971','Cinnamon','Cinnamon','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120076','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','C','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('972','Eliachi','Eliachi','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120077','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','E','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('973','Green Chilli Mayo','Green Chilli Mayo','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120078','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','GCM','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('974','Hari Mirch','Hari Mirch','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120079','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','HM','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('975','Hari Chutni','Hari Chutni','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120080','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','HC','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('976','Honey','Honey','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120081','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','HNY','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('977','Honey Garlic Mayo','Honey Garlic Mayo','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120082','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','HGM','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('978','Kali Mirch','Kali Mirch','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120083','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','KM','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('979','Kesar','Kesar','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120084','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','KES','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('980','Laung','Laung','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120085','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','L','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('981','Masala','Masala','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120086','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','M','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('982','Mint','Mint','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120087','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','MNT','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('983','Moti Eliachi','Moti Eliachi','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120088','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','ME','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('984','Mulethi','Mulethi','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120089','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','ML','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('985','Red Chilli Mayo','Red Chilli Mayo','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120090','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','RCM','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('986','Tulsi','Tulsi','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120091','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','T','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('987','Saunf','Saunf','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120092','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','S','N','N');
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,  `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `EMPLOYEE_MEAL_COMPONENT`,`SUPPORTS_VARIANT_LEVEL_ORDERING`) VALUES('988','Sab Kuch','Sab Kuch','12','1202','ACTIVE',NULL,'2016-06-19','2030-06-19','CH120093','1','NET_PRICE','27','2015-01-01 00:00:00','2030-12-01 00:00:00','N','FREE_ADDON','SK','N','N');

UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='DC' WHERE `PRODUCT_ID`='10';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='DDK' WHERE `PRODUCT_ID`='12';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='CUT' WHERE `PRODUCT_ID`='30';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='DPK' WHERE `PRODUCT_ID`='50';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='KUL' WHERE `PRODUCT_ID`='80';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='LIT' WHERE `PRODUCT_ID`='300';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='ML' WHERE `PRODUCT_ID`='450';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='MS' WHERE `PRODUCT_ID`='460';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='SIC' WHERE `PRODUCT_ID`='500';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='GRC' WHERE `PRODUCT_ID`='510';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='PC' WHERE `PRODUCT_ID`='520';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='NS' WHERE `PRODUCT_ID`='530';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='SCC' WHERE `PRODUCT_ID`='540';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='CBS' WHERE `PRODUCT_ID`='560';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='WBC' WHERE `PRODUCT_ID`='570';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='WKP' WHERE `PRODUCT_ID`='590';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='2M' WHERE `PRODUCT_ID`='610';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='ALO' WHERE `PRODUCT_ID`='620';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='BS' WHERE `PRODUCT_ID`='630';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='CSP' WHERE `PRODUCT_ID`='631';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='KP' WHERE `PRODUCT_ID`='640';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='BMK' WHERE `PRODUCT_ID`='641';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='EB' WHERE `PRODUCT_ID`='650';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='PS' WHERE `PRODUCT_ID`='651';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='CS' WHERE `PRODUCT_ID`='652';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='POH' WHERE `PRODUCT_ID`='660';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='VP' WHERE `PRODUCT_ID`='670';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='BMV' WHERE `PRODUCT_ID`='671';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='BB' WHERE `PRODUCT_ID`='680';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='BM' WHERE `PRODUCT_ID`='690';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='BBC' WHERE `PRODUCT_ID`='740';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='BAC' WHERE `PRODUCT_ID`='750';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='CRC' WHERE `PRODUCT_ID`='760';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='LMC' WHERE `PRODUCT_ID`='770';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='RSK' WHERE `PRODUCT_ID`='780';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `PRODUCT_CLASSIFICATION`='PAID_ADDON' WHERE `PRODUCT_ID`='860';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `PRODUCT_CLASSIFICATION`='PAID_ADDON' WHERE `PRODUCT_ID`='861';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `PRODUCT_CLASSIFICATION`='PAID_ADDON' WHERE `PRODUCT_ID`='862';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `PRODUCT_CLASSIFICATION`='PAID_ADDON' WHERE `PRODUCT_ID`='863';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `PRODUCT_CLASSIFICATION`='PAID_ADDON' WHERE `PRODUCT_ID`='864';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='ECP' WHERE `PRODUCT_ID`='862';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='ECS' WHERE `PRODUCT_ID`='863';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='ERS' WHERE `PRODUCT_ID`='864';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `SHORT_CODE`='GF' WHERE `PRODUCT_ID`='868';

UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='969';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='987';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='986';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='984';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='983';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='982';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='981';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='980';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='979';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='978';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='976';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='974';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='972';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='971';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='970';
UPDATE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` SET `DIMENSION_CODE`='2' WHERE `PRODUCT_ID`='988';

delete from UNIT_PRODUCT_PRICING
where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING where PRODUCT_ID IN (
select PRODUCT_ID from PRODUCT_DETAIL where PRODUCT_TYPE = 12 and PRODUCT_SUB_TYPE = 1202
)
)
;
delete from UNIT_PRODUCT_MAPPING where PRODUCT_ID IN (
select PRODUCT_ID from PRODUCT_DETAIL where PRODUCT_TYPE = 12 and PRODUCT_SUB_TYPE = 1202
);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID,PRODUCT_STATUS, LAST_UPDATE_TMSTMP,PRODUCT_START_DATE  ,
PRODUCT_END_DATE)
select ud.UNIT_ID, pd.PRODUCT_ID, 'ACTIVE','2016-06-01 00:00:00',
'2016-01-01', '9999-12-01'  from PRODUCT_DETAIL pd, UNIT_DETAIL ud
where PRODUCT_TYPE = 12 and PRODUCT_SUB_TYPE = 1202;


INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID, DIMENSION_CODE,LAST_UPDATE_TMSTMP, PRICE,COST  ,
COD_COST)
select upm.UNIT_PROD_REF_ID, rl.RL_ID, '2016-06-01 00:00:00',
'0.00','0.00','0.00'  from PRODUCT_DETAIL pd, UNIT_DETAIL ud,
UNIT_PRODUCT_MAPPING upm, REF_LOOKUP_TYPE rlt, REF_LOOKUP rl
where pd.PRODUCT_TYPE = 12 and pd.PRODUCT_SUB_TYPE = 1202
and ud.UNIT_ID = upm.UNIT_ID
and pd.PRODUCT_ID = upm.PRODUCT_ID
and pd.DIMENSION_CODE = rlt.RTL_ID
and rl.RTL_ID = rlt.RTL_ID
;