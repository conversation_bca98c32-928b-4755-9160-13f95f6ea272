ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN CONSUMABLE DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN CONSUMABLE DECIMAL(10,2) NULL;

UPDATE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL 
SET 
    CONSUMABLE = COALESCE(CONSUMABLE_CUTLERY, 0) + COALESCE(CONSUMABLE_EQUIPMENT, 0) + COALESCE(CONSUMABLE_STATIONARY, 0) + COALESCE(CONSUMABLE_UNIFORM, 0) + COALESCE(CONSUMABLE_UTILITY, 0);

UPDATE KETTLE_DEV.UNIT_BUDGETORY_DETAIL 
SET 
    CONSUMABLE = COALESCE(CONSUMABLE_CUTLERY, 0) + COALESCE(CONSUMABLE_EQUIPMENT, 0) + COALESCE(CONSUMABLE_STATIONARY, 0) + COALESCE(CONSUMABLE_UNIFORM, 0) + COALESCE(CONSUMABLE_UTILITY, 0);

    
ALTER TABLE KETTLE_DEV.ORDER_ENQUIRY_ITEM
ADD COLUMN ENQUIRY_ORDER_ID VARCHAR(30);

CREATE INDEX ENQUIRY_ORDER_ID_ORDER_ENQUIRY_ITEM ON KETTLE_DEV.ORDER_ENQUIRY_ITEM(ENQUIRY_ORDER_ID) USING BTREE;


CREATE TABLE KETTLE_DEV.ORDER_ITEM_CONSUMABLE_DATA (
CONSUMABLE_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
UNIT_ID INTEGER NOT NULL,
CONSUMPTION_MONTH INTEGER NOT NULL,
CONSUMPTION_YEAR INTEGER NOT NULL,
MENU_PRODUCT_ID  INTEGER NOT NULL,
MENU_PRODUCT_NAME VARCHAR(200) NOT NULL,
MENU_PRODUCT_QUANTITY INTEGER NOT NULL,
MENU_PRODUCT_DIMENSION VARCHAR(50) NOT NULL,
SCM_PRODUCT_ID INTEGER NOT NULL,
SCM_PRODUCT_NAME VARCHAR(200) NOT NULL,
SCM_PRODUCT_QUANTITY DECIMAL(20,6),
SCM_PRODUCT_UOM  VARCHAR(10) NOT NULL
);

ALTER TABLE KETTLE_DEV.ORDER_ITEM_CONSUMABLE_DATA ADD COLUMN ORDER_SOURCE VARCHAR(10) NULL;