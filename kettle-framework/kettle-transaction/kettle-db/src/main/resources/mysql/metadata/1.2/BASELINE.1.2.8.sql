ALTER TABLE UNIT_SEQUENCE_ID
ADD COLUMN DATA_SOURCE VARCHAR(15) NOT NULL DEFAULT 'CAFE';

DROP TABLE IF EXISTS ORDER_STATUS_EVENT;

CREATE TABLE ORDER_STATUS_EVENT (
ORDER_STATUS_ID INTEGER NOT NULL AUTO_INCREMENT,
ORDER_ID INTEGER NOT NULL,
FROM_STATUS VARCHAR(15) NOT NULL,
TO_STATUS VARCHAR(15) NOT NULL,
GENERATED_BY INTEGER NOT NULL,
APPROVED_BY INTEGER NOT NULL,
REASON_TEXT VARCHAR(100) NOT NULL,
UPDATE_TIME TIMESTAMP NOT NULL,
TRANSITION_STATUS VARCHAR(15) NOT NULL,
ERROR_TRACE VARCHAR(5000),
PRIMARY KEY(ORDER_STATUS_ID)
);

ALTER TABLE ORDER_STATUS_EVENT MODIFY COLUMN FROM_STATUS VARCHAR(30);
ALTER TABLE ORDER_STATUS_EVENT MODIFY COLUMN TO_STATUS VARCHAR(30);