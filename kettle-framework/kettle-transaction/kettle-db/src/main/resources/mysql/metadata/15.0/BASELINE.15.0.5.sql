DROP  PROCEDURE KETTLE_DEV.`DAY_CLOSE_ESTIMATE_DATA_PROC`;

DEL<PERSON>ITER $$
CREATE PROCEDURE KETTLE_DEV.`DAY_CLOSE_ESTIMATE_DATA_PROC`(IN IN_UNIT_ID INTEGER, IN IN_BIZ_DATE DATE, IN_MAX_TAXABLE_AMOUNT INTEGER)
BEGIN
DECLARE TOTAL_NET_SALES INT DEFAULT 0;
DELETE FROM KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA
WHERE
    UNIT_ID = IN_UNIT_ID
    AND BUSINESS_DATE = IN_BIZ_DATE;
# inserting
INSERT INTO KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA (
UNIT_ID ,
BRAND_ID ,
PRODUCT_ID ,
DIMENSION ,
QUANTITY ,
BUSINESS_DATE ,
DAY_OF_WEEK ,
UPT ,
NET_SALES ,
GMV ,
DATA_TYPE
)

SELECT m.UNIT_ID,m.BRAND_ID,m.PRODUCT_ID,m.DIMENSION,<PERSON><PERSON>(m.QUANTITY),m.BUSINESS_DATE,DAYOFWEEK(m.BUSINESS_DATE),NULL,SUM(m.NET_SALE),SUM(m.GMV),m.DATA_TYPE
FROM(
SELECT
	od.BRAND_ID,
	od.BUSINESS_DATE,
    od.UNIT_ID,DAYOFWEEK(od.BUSINESS_DATE) AS DAYOFWEEK,
    oi.PRODUCT_ID,
    SUM(oi.QUANTITY) AS QUANTITY,
	oi.DIMENSION,
    SUM(oi.AMOUNT_PAID) AS NET_SALE,
    SUM(oi.PRICE * oi.QUANTITY) AS GMV,
    'CALCULATED' DATA_TYPE
    FROM KETTLE_DEV.ORDER_DETAIL od
INNER JOIN KETTLE_DEV.ORDER_ITEM oi ON od.ORDER_ID=oi.ORDER_ID
WHERE od.BUSINESS_DATE = IN_BIZ_DATE
	AND od.UNIT_ID=IN_UNIT_ID
    -- AND od.BRAND_ID=1
    AND oi.COMBO_CONSTITUENT<> 'Y'
    AND oi.TAX_CODE NOT IN ( 'COMBO', 'GIFT_CARD')
    AND od.ORDER_STATUS <> 'CANCELLED'
	AND od.SETTLED_AMOUNT < IN_MAX_TAXABLE_AMOUNT
	AND oi.PARENT_ITEM_ID IS NULL
    AND od.IS_GIFT_CARD_ORDER <>"Y"
GROUP BY oi.PRODUCT_ID,oi.DIMENSION,od.BRAND_ID

UNION ALL

SELECT
	od.BRAND_ID,
	od.BUSINESS_DATE,
    od.UNIT_ID,DAYOFWEEK(od.BUSINESS_DATE) AS DAYOFWEEK,
    b.PRODUCT_ID,
    SUM(b.QUANTITY) AS QUANTITY,
    b.DIMENSION,
    SUM((b.PRICE * b.QUANTITY) / a.TOTAL_AMOUNT * a.AMOUNT_PAID) AS NET_SALE,
    SUM(b.PRICE * b.QUANTITY) GMV,
    'CALCULATED' DATA_TYPE


FROM
    (SELECT
        a.PARENT_ITEM_ID, a.TOTAL_AMOUNT, b.AMOUNT_PAID
    FROM
        (SELECT
        oi.PARENT_ITEM_ID PARENT_ITEM_ID,
            SUM(oi.QUANTITY * oi.PRICE) TOTAL_AMOUNT
    FROM
        KETTLE_DEV.ORDER_DETAIL od, KETTLE_DEV.ORDER_ITEM oi
    WHERE
        od.ORDER_ID = oi.ORDER_ID
            AND od.BUSINESS_DATE = IN_BIZ_DATE
            AND od.UNIT_ID = IN_UNIT_ID
            AND oi.PARENT_ITEM_ID IS NOT NULL
    GROUP BY oi.PARENT_ITEM_ID) a, KETTLE_DEV.ORDER_ITEM b
    WHERE
        a.PARENT_ITEM_ID = b.ORDER_ITEM_ID) a,
    KETTLE_DEV.ORDER_ITEM b,
    KETTLE_DEV.ORDER_DETAIL od
WHERE
    a.PARENT_ITEM_ID = b.PARENT_ITEM_ID
    and b.ORDER_ID = od.ORDER_ID
    GROUP BY b.PRODUCT_ID,b.DIMENSION,od.BRAND_ID)m
GROUP BY m.PRODUCT_ID,m.DIMENSION,m.BRAND_ID;



SELECT
    SUM(NET_SALES)
INTO TOTAL_NET_SALES FROM
    KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA
WHERE
    UNIT_ID = IN_UNIT_ID
        AND BUSINESS_DATE = IN_BIZ_DATE;

UPDATE KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA
SET
    UPT = QUANTITY * 1000 / TOTAL_NET_SALES
WHERE
    UNIT_ID = IN_UNIT_ID
        AND BUSINESS_DATE = IN_BIZ_DATE;


END$$
DELIMITER ;