ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL
ADD COLUMN CAMPAIGN_ID INTEGER NULL;

CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_CAMPAIGN_ID ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(CAMPAIGN_ID) USING BTREE;

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL
DROP COLUMN CAMPAIGN_SOURCE,DROP COLUMN CAMPAIGN_TYPE;

UPDATE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL
SET CAMPAIGN_ID =1 WHERE CAMPAIGN_ID IS NULL;

CREATE TABLE KETTLE_ARCHIVE_DUMP.`COUPON_DETAIL_DATA_2022_01_01` (
  `COUPON_DETAIL_ID` int(11) NOT NULL AUTO_INCREMENT,
  `OF<PERSON><PERSON>_DETAIL_ID` int(11) NOT NULL,
  `COUPON_CODE` varchar(20) NOT NULL,
  `START_DATE` date NOT NULL,
  `END_DATE` date NOT NULL,
  `COUPON_REUSE` varchar(1) NOT NULL,
  `CUSTOMER_REUSE` varchar(1) NOT NULL,
  `MAX_USAGE` int(11) DEFAULT NULL,
  `COUPON_STATUS` varchar(15) NOT NULL DEFAULT 'INACTIVE',
  `USAGE_COUNT` int(11) NOT NULL DEFAULT '0',
  `MANUAL_OVERRIDE` varchar(1) NOT NULL DEFAULT 'N',
  PRIMARY KEY (`COUPON_DETAIL_ID`),
  UNIQUE KEY `COUPON_CODE_UNIQUE` (`COUPON_CODE`),
  KEY `ARCHIVE_COUPON_DETAIL_DATA_COUPON_CODE` (`COUPON_CODE`(10)) USING BTREE,
  KEY `ARCHIVE_COUPON_DETAIL_DATA_OFFER_DETAIL_ID` (`OFFER_DETAIL_ID`) USING BTREE,
  KEY `ARCHIVE_COUPON_DETAIL_DATA_START_DATE` (`START_DATE`) USING BTREE,
  KEY `ARCHIVE_COUPON_DETAIL_DATA_END_DATE` (`END_DATE`) USING BTREE,
  KEY `ARCHIVE_COUPON_DETAIL_DATA_COUPON_STATUS` (`COUPON_STATUS`) USING BTREE
);
CREATE TABLE KETTLE_ARCHIVE_DUMP.`COUPON_DETAIL_MAPPING_DATA_2022_01_01` (
  `COUPON_DETAIL_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `COUPON_DETAIL_ID` int(11) NOT NULL,
  `MAPPING_TYPE` varchar(50) NOT NULL,
  `MAPPING_VALUE` varchar(100) NOT NULL,
  `MAPPING_DATA_TYPE` varchar(100) NOT NULL,
  `MIN_VALUE` varchar(10) NOT NULL DEFAULT '1',
  `MAPPING_GROUP` int(11) NOT NULL DEFAULT '1',
  `DIMENSION` varchar(15) DEFAULT NULL,
  `STATUS` varchar(10) DEFAULT 'ACTIVE',
  PRIMARY KEY (`COUPON_DETAIL_MAPPING_ID`),
  KEY `ARCHIVE_COUPON_DETAIL_ID_COUPON_DETAIL_MAPPING_DATA` (`COUPON_DETAIL_ID`) USING BTREE,
  KEY `ARCHIVE_COUPON_DETAIL_MAPPING_DATA_MAPPING_VALUE` (`MAPPING_VALUE`) USING BTREE,
  KEY `ARCHIVE_COUPON_DETAIL_MAPPING_DATA_MAPPING_TYPE` (`MAPPING_TYPE`) USING BTREE
);


CREATE TABLE KETTLE_ARCHIVE_DUMP.COUPONS_USED(COUPON_CODE VARCHAR(30) NULL, COUPON_DETAIL_ID INTEGER NULL);
CREATE INDEX COUPONS_USED_COUPON_CODE ON  KETTLE_ARCHIVE_DUMP.COUPONS_USED(COUPON_CODE) USING BTREE;
CREATE INDEX COUPONS_USED_COUPON_DETAIL_ID ON  KETTLE_ARCHIVE_DUMP.COUPONS_USED(COUPON_DETAIL_ID) USING BTREE;
 
INSERT INTO  KETTLE_ARCHIVE_DUMP.COUPONS_USED(COUPON_CODE) 
SELECT DISTINCT OFFER_CODE FROM KETTLE_DUMP.ORDER_DETAIL;

UPDATE KETTLE_ARCHIVE_DUMP.COUPONS_USED cu, KETTLE_MASTER_DUMP.COUPON_DETAIL_DATA cd
SET cu.COUPON_DETAIL_ID = cd.COUPON_DETAIL_ID
where cu.COUPON_CODE = cd.COUPON_CODE;

INSERT INTO  KETTLE_ARCHIVE_DUMP.COUPON_DETAIL_DATA_2022_01_01(COUPON_DETAIL_ID, OFFER_DETAIL_ID, COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE)
select cd.COUPON_DETAIL_ID, OFFER_DETAIL_ID, cd.COUPON_CODE, START_DATE, END_DATE, COUPON_REUSE, CUSTOMER_REUSE, MAX_USAGE, COUPON_STATUS, USAGE_COUNT, MANUAL_OVERRIDE from KETTLE_MASTER_DUMP.COUPON_DETAIL_DATA cd
LEFT OUTER JOIN KETTLE_ARCHIVE_DUMP.COUPONS_USED cu
on cd.COUPON_DETAIL_ID = cu.COUPON_DETAIL_ID
WHERE cu.COUPON_DETAIL_ID IS NULL
AND cd.END_DATE <= '2022-01-01'
;

INSERT INTO KETTLE_ARCHIVE_DUMP.`COUPON_DETAIL_MAPPING_DATA_2022_01_01`(COUPON_DETAIL_MAPPING_ID, COUPON_DETAIL_ID, MAPPING_TYPE, MAPPING_VALUE, MAPPING_DATA_TYPE, MIN_VALUE, MAPPING_GROUP, DIMENSION, STATUS)
select cm.COUPON_DETAIL_MAPPING_ID, cm.COUPON_DETAIL_ID, cm.MAPPING_TYPE, cm.MAPPING_VALUE, cm.MAPPING_DATA_TYPE, cm.MIN_VALUE, cm.MAPPING_GROUP, cm.DIMENSION, cm.STATUS from 
KETTLE_MASTER_DUMP.`COUPON_DETAIL_MAPPING_DATA` cm
INNER JOIN 
KETTLE_MASTER_DUMP.COUPON_DETAIL_DATA cd
ON cm.COUPON_DETAIL_ID = cd.COUPON_DETAIL_ID
LEFT OUTER JOIN KETTLE_ARCHIVE_DUMP.COUPONS_USED cu
on cd.COUPON_DETAIL_ID = cu.COUPON_DETAIL_ID
WHERE cu.COUPON_DETAIL_ID IS NULL
AND cd.END_DATE <= '2022-01-01';

delete cm from 
KETTLE_MASTER_DUMP.`COUPON_DETAIL_MAPPING_DATA` cm
INNER JOIN 
KETTLE_MASTER_DUMP.COUPON_DETAIL_DATA cd
ON cm.COUPON_DETAIL_ID = cd.COUPON_DETAIL_ID
LEFT OUTER JOIN KETTLE_ARCHIVE_DUMP.COUPONS_USED cu
on cd.COUPON_DETAIL_ID = cu.COUPON_DETAIL_ID
WHERE cu.COUPON_DETAIL_ID IS NULL
AND cd.END_DATE <= '2022-01-01';

delete cd from KETTLE_MASTER_DUMP.COUPON_DETAIL_DATA cd
LEFT OUTER JOIN KETTLE_ARCHIVE_DUMP.COUPONS_USED cu
on cd.COUPON_DETAIL_ID = cu.COUPON_DETAIL_ID
WHERE cu.COUPON_DETAIL_ID IS NULL
AND cd.END_DATE <= '2022-01-01'
;

ALTER TABLE KETTLE_MASTER_DEV.COUPON_DETAIL_DATA
ADD COLUMN  MAX_CUSTOMER_USAGE INTEGER NULL;

