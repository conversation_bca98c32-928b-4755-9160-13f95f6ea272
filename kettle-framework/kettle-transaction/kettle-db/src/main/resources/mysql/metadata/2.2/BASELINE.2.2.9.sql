CREATE INDEX UNIT_CATEGORY_UNIT_DETAIL ON KETTLE_MASTER_DUMP.UNIT_DETAIL (UNIT_CATEGORY) USING BTREE;
;

CREATE INDEX UNIT_STATUS_UNIT_DETAIL ON KETTLE_MASTER_DUMP.UNIT_DETAIL (UNIT_STATUS) USING BTREE;
;

CREATE INDEX UNIT_ID_ORDER_DETAIL ON KETTLE_DUMP.ORDER_DETAIL (UNIT_ID) USING BTREE;
;

CREATE INDEX ORDER_STATUS_ORDER_DETAIL ON KETTLE_DUMP.ORDER_DETAIL (ORDER_STATUS) USING BTREE;
;

CREATE INDEX BILLING_SERVER_TIME_ORDER_DETAIL ON KETTLE_DUMP.ORDER_DETAIL (BILLING_SERVER_TIME) USING BTREE;
;

CREATE INDEX PRODUCT_ID_ORDER_ITEM ON KETTLE_DUMP.ORDER_ITEM (PRODUCT_ID) USING BTREE;
;

CREATE INDEX PRODUCT_TYPE_PRODUCT_DETAIL ON KETTLE_MASTER_DUMP.PRODUCT_DETAIL (PRODUCT_TYPE) USING BTREE;
;


ALTER TABLE KETTLE.ORDER_FEEDBACK_DETAIL
MODIFY COLUMN PRODUCT_IDS VARCHAR(150) NULL;