delete from UNIT_PRODUCT_PRICING where UNIT_PROD_REF_ID IN
 (select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING where UNIT_ID = 12017               
and PRODUCT_ID NOT IN (10,20,40,60,80,100,130,140,170,272,271,300,450,460,470,500,540,610,620,630,640,650,660,670,680,690));
 
delete from UNIT_PRODUCT_PRICING where UNIT_PROD_REF_ID IN (select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING where UNIT_ID = 12017
and PRODUCT_ID IN (10,60,100,130,140,170,272,271))
and DIMENSION_CODE <> 21;

delete from UNIT_PRODUCT_PRICING where UNIT_PROD_REF_ID IN (select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING where UNIT_ID = 12017
and PRODUCT_ID IN (20))
and DIMENSION_CODE<>1;

delete from UNIT_PRODUCT_PRICING where UNIT_PROD_REF_ID IN (select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING where UNIT_ID = 12017
and PR<PERSON>UCT_ID IN (40))
and DIM<PERSON><PERSON>ON_CODE<>1;
 
delete from UNIT_PRODUCT_PRICING where UNIT_PROD_REF_ID IN (select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING where UNIT_ID = 12017
and PRODUCT_ID IN (80))
and DIMENSION_CODE<>1101;

delete from UNIT_PRODUCT_PRICING where UNIT_PROD_REF_ID IN (select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING where UNIT_ID = 12017
and PRODUCT_ID IN (300,450,460,470))
and DIMENSION_CODE <> 30;

delete from UNIT_PRODUCT_PRICING where UNIT_PROD_REF_ID IN (select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING where UNIT_ID = 12017
and PRODUCT_ID IN (500,540,610,620,630,640,650,660,670,680,690))
and DIMENSION_CODE <> 1;
 
delete from UNIT_PRODUCT_MAPPING where UNIT_ID = 12017
and PRODUCT_ID NOT IN (10,20,40,60,80,100,130,140,170,271,272,300,450,460,470,500,540,610,620,630,640,650,660,670,680,690);
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 35.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 10
and upp.DIMENSION_CODE = 21;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 35.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 20
and upp.DIMENSION_CODE = 1;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 49.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 40
and upp.DIMENSION_CODE = 1;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 65.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 60
and upp.DIMENSION_CODE = 21;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 49.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 80
and upp.DIMENSION_CODE = 1101;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 65.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 100
and upp.DIMENSION_CODE = 21;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 85.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 130
and upp.DIMENSION_CODE = 21;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 85.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 140
and upp.DIMENSION_CODE = 21;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 55.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 170
and upp.DIMENSION_CODE = 21;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 75.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 272
and upp.DIMENSION_CODE = 21;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 65.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 271
and upp.DIMENSION_CODE = 21;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 65.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 300
and upp.DIMENSION_CODE = 30;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 65.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 450
and upp.DIMENSION_CODE = 30;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 49.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 460
and upp.DIMENSION_CODE = 30;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 49.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 470
and upp.DIMENSION_CODE = 30;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 109.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 500
and upp.DIMENSION_CODE = 1;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 89.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 540
and upp.DIMENSION_CODE = 1;
 
update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 69.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 610
and upp.DIMENSION_CODE = 1;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 69.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 620
and upp.DIMENSION_CODE = 1;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 69.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 630
and upp.DIMENSION_CODE = 1;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 69.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 640
and upp.DIMENSION_CODE = 1;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 69.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 650
and upp.DIMENSION_CODE = 1;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 49.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 660
and upp.DIMENSION_CODE = 1;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 49.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 670
and upp.DIMENSION_CODE = 1;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 49.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 680
and upp.DIMENSION_CODE = 1;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set PRICE = 39.00
where 
upp.UNIT_PROD_REF_ID = upm. UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and upm.UNIT_ID = 12017
and pd.PRODUCT_ID = 690
and upp.DIMENSION_CODE = 1;










#PROMENADE based offers as on 9/03/2016

INSERT INTO  OFFER_DETAIL_DATA  (OFFER_CATEGORY ,  OFFER_TYPE ,  OFFER_TEXT ,  OFFER_DESCRIPTION ,  START_DATE ,  END_DATE ,  OFFER_STATUS ,  MIN_VALUE ,  VALIDATE_CUSTOMER ,  INCLUDE_TAXES ,  PRIORITY ,  OFFER_SCOPE ,  MIN_ITEM_COUNT ,  QUANTITY_LIMIT ,  LOYALTY_LIMIT ,  OFFER_VALUE ) 
	VALUES ('BILL', 'FLAT_BILL_STRATEGY', 'Rs.250 off', 'Get Rs. 250 OFF on a minimum bill of Rs. 500', '2016-03-12', '2016-04-30', 'ACTIVE', '500', 'Y', 'Y', '0', 'INTERNAL', '1', '1', '0', '250');

INSERT INTO  COUPON_DETAIL_DATA  (OFFER_DETAIL_ID ,  COUPON_CODE ,  START_DATE ,  END_DATE ,  COUPON_REUSE ,  CUSTOMER_REUSE ,  MAX_USAGE ,  COUPON_STATUS ,  USAGE_COUNT ,  MANUAL_OVERRIDE ) 
	VALUES ('35', 'PROM22', '2016-03-12', '2016-04-30', 'Y', 'Y', '100000', 'ACTIVE', '0', 'N');

INSERT INTO  COUPON_DETAIL_MAPPING_DATA  ( COUPON_DETAIL_ID ,  MAPPING_TYPE ,  MAPPING_VALUE ,  MAPPING_DATA_TYPE ,  MIN_VALUE ,  MAPPING_GROUP ,  STATUS ) 
	VALUES ('19082', 'UNIT', '12019', 'java.lang.Integer', '1', '1', 'ACTIVE');

	
	
INSERT INTO  OFFER_DETAIL_DATA  (  OFFER_CATEGORY ,  OFFER_TYPE ,  OFFER_TEXT ,  OFFER_DESCRIPTION ,  START_DATE ,  END_DATE ,  OFFER_STATUS ,  MIN_VALUE ,  VALIDATE_CUSTOMER ,  INCLUDE_TAXES ,  PRIORITY ,  OFFER_SCOPE ,  MIN_ITEM_COUNT ,  QUANTITY_LIMIT ,  LOYALTY_LIMIT ,  OFFER_VALUE ) VALUES ( 'ITEM', 'PERCENTAGE_ITEM_STRATEGY', 'Desi Chai Free', 'Get a Desi Chai free on your order', '2016-03-12', '2016-04-30', 'ACTIVE', '0', 'N', 'Y', '0', 'INTERNAL', '2', '1', '0', '100');
INSERT INTO  COUPON_DETAIL_DATA  (  OFFER_DETAIL_ID ,  COUPON_CODE ,  START_DATE ,  END_DATE ,  COUPON_REUSE ,  CUSTOMER_REUSE ,  MAX_USAGE ,  COUPON_STATUS ,  USAGE_COUNT ,  MANUAL_OVERRIDE ) VALUES ( '36', 'CHAIDM16', '2016-03-12', '2016-04-30', 'Y', 'Y', '100000', 'ACTIVE', '0', 'N');

INSERT INTO  COUPON_DETAIL_MAPPING_DATA  (  COUPON_DETAIL_ID ,  MAPPING_TYPE ,  MAPPING_VALUE ,  MAPPING_DATA_TYPE ,  MIN_VALUE ,  MAPPING_GROUP ,  DIMENSION ,  STATUS ) VALUES ('19083', 'PRODUCT', '10', 'java.lang.Integer', '1', '1', 'Regular', 'ACTIVE');

INSERT INTO  OFFER_METADATA  (OFFER_ID ,  MAPPING_TYPE ,  MAPPING_VALUE ,  STATUS ) VALUES ('36', 'PRODUCT', '10', 'ACTIVE');
