CREATE INDEX FEEDBACK_SOURCE_ORDER_FEEDBACK_DETAIL ON KETTLE_DUMP.ORDER_FEEDBACK_DETAIL(FEEDBACK_SOURCE) USING BTREE;
CREATE INDEX FEEDBACK_STATUS_ORDER_FEEDBAC<PERSON>_DETAIL ON KETTLE_DUMP.ORDER_FEEDBACK_DETAIL(FEEDBACK_STATUS) USING BTREE;
CREATE INDEX EVENT_STATUS_ORDER_FEEDBACK_EVENT ON KETTLE_DUMP.ORDER_FEEDBACK_EVENT(EVENT_STATUS) USING BTREE;
CREATE INDEX LATEST_FEEDBACK_INFO_ID_ORDER_FEEDBACK_DETAIL ON KETTLE_DUMP.ORDER_FEEDBACK_DETAIL(LATEST_FEEDBACK_INFO_ID) USING BTREE;
CREATE INDEX LATEST_FEEDBACK_INFO_ID_ORDER_FEEDBACK_EVENT ON KETTLE_DUMP.ORDER_FEEDBACK_EVENT(LATEST_FEEDBACK_INFO_ID) USING BTREE;
CREATE INDEX FEEDBACK_ID_ORDER_FEEDBACK_EVENT ON KETTLE_DUMP.ORDER_FEEDBACK_EVENT(FEEDBACK_ID) USING BTREE;

CREATE INDEX FEEDBACK_INFO_ID_FEEDBACK_RESPONSE ON KETTLE_DUMP.FEEDBACK_RESPONSE(FEEDBACK_INFO_ID) USING BTREE;
CREATE INDEX FEEDBACK_FIELD_ID_FEEDBACK_RESPONSE ON KETTLE_DUMP.FEEDBACK_RESPONSE(FEEDBACK_FIELD_ID) USING BTREE;
CREATE INDEX FEEDBACK_RESPONSE_ID_OFEEDBACK_RESPONSE_DATA ON KETTLE_DUMP.FEEDBACK_RESPONSE_DATA(FEEDBACK_RESPONSE_ID) USING BTREE;

CREATE INDEX RESPONSE_DATA_OFEEDBACK_RESPONSE_DATA ON KETTLE_DUMP.FEEDBACK_RESPONSE_DATA(RESPONSE_DATA) USING BTREE;

ALTER TABLE KETTLE_DUMP.FEEDBACK_RESPONSE_DATA
ADD COLUMN RESPONSE_CATEGORY VARCHAR(30) NULL;
CREATE INDEX RESPONSE_CATEGORY_OFEEDBACK_RESPONSE_DATA ON KETTLE_DUMP.FEEDBACK_RESPONSE_DATA(RESPONSE_CATEGORY) USING BTREE;

ALTER TABLE KETTLE_DUMP.FEEDBACK_RESPONSE_DATA
ADD COLUMN TO_BE_CONSIDERED VARCHAR(1) NULL;
CREATE INDEX TO_BE_CONSIDERED_FEEDBACK_RESPONSE_DATA ON KETTLE_DUMP.FEEDBACK_RESPONSE_DATA(TO_BE_CONSIDERED) USING BTREE;

ALTER TABLE KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE
ADD COLUMN CAFE_AMBIENCE_ISSUES INTEGER NULL;
ALTER TABLE KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE
ADD COLUMN CAFE_BEVERAGE_ISSUES INTEGER NULL;
ALTER TABLE KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE
ADD COLUMN CAFE_FOOD_ISSUES INTEGER NULL;
ALTER TABLE KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE
ADD COLUMN CAFE_SERVICE_ISSUES INTEGER NULL;
ALTER TABLE KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE
ADD COLUMN DELIVERY_AMBIENCE_ISSUES INTEGER NULL;
ALTER TABLE KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE
ADD COLUMN DELIVERY_BEVERAGE_ISSUES INTEGER NULL;
ALTER TABLE KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE
ADD COLUMN DELIVERY_FOOD_ISSUES INTEGER NULL;
ALTER TABLE KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE
ADD COLUMN DELIVERY_SERVICE_ISSUES INTEGER NULL;

update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = 'NA' where RESPONSE_DATA IN ('No issues','No issues. I liked it','No issues. It was all great','No issues. My order was on time and in one go','There was no issue with my Delivery', '0') and RESPONSE_CATEGORY IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = RESPONSE_DATA where RESPONSE_DATA IN ('1','2','3','4','5') and RESPONSE_CATEGORY IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = 'Ambience' where RESPONSE_DATA IN ('Ambience','I did not like cleanliness / hygiene at the cafe','I did not like cleanliness / hygiene of the washroom','I did not like cleanliness / hygiene of the kitchen area','Kitchen area was messy ','Music wasn\'t audible in the cafe ','The Café I visited wasn\'t clean','There were pests/flies in the cafe','The Café didn\'t smell good','Restroom wasn\'t hygienic enough','Café window/outside glass wasn\'t clean') and RESPONSE_CATEGORY IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = 'Service' where RESPONSE_DATA IN ('I was called more than once to collect my order','Internal staff communication was too loud and interfering ','My order was delayed','Service','The order taker could not interact well','The order taker did not help me make a choice','Could not pay online on app / website','Delivery boy did not bring condiments','Delivery boy did not have the right change','Had to enquire about order status','Incorrect / Incomplete order was delivered','Order reached late','I was served on the table','The order taker was not groomed well','Correct amount or money/change wasn\'t returned ','Cafe staff did not assist to make an informed choice','Café staff wasn\'t properly dressed','Couldn\'t find a table in stipulated time','Expected wait time wasn\'t communicated ','I expected to be served on the table ','I received an incomplete order ','Placement of order ','Receiving of order ','The closing time of the Café should be increased','The wait time to place an order was high ','The menu was too confusing','Wi-fi didn\'t connect properly','Delivery boy did not behave well','Cafe staff wasn\'t well groomed ','I was called more than twice to collect my order','I wasn\'t able to get the condiments ','I wasn\'t given my free Loyal-Tea Chai','Registering the phone number is a cumbersome process ','Delivery ','The Loyal-Tea program is too complex','My order reached late','Food wasn\'t packaged properly','Had to follow-up on the order status') and RESPONSE_CATEGORY IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = 'Chai/ Beverages' where RESPONSE_DATA IN ('Chai/ Beverages','I did not like my Chai','I did not like my Cold Beverage','Chai/Food was served cold','My Chai/Food didn\'t taste great','Quality of Chai/Food served ','Kulhad was broken/beverage cup was leaking ','My Chai had a layer of Malai') and RESPONSE_CATEGORY IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = 'Food' where RESPONSE_DATA IN ('Food','I did not like my Food') and RESPONSE_CATEGORY IS NULL;

update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set TO_BE_CONSIDERED = 'N' where RESPONSE_DATA 
IN('No issues','No issues. I liked it','No issues. It was all great','No issues. My order was on time and in one go','There was no issue with my Delivery'
) AND TO_BE_CONSIDERED  IS NULL;

update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set TO_BE_CONSIDERED = 'N' where RESPONSE_DATA 
NOT IN('1','2','3','4','5','No issues','No issues. I liked it','No issues. It was all great','No issues. My order was on time and in one go','There was no issue with my Delivery'
) AND FEEDBACK_RATING IN ('4','5')
AND TO_BE_CONSIDERED  IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set TO_BE_CONSIDERED = 'Y' where TO_BE_CONSIDERED IS NULL;
