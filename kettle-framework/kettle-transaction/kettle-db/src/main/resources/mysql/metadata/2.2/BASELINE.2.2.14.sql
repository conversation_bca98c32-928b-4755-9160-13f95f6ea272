DROP PROCEDURE `DA<PERSON><PERSON>_CONSOLIDATED_CSAT_SCORE`;
<PERSON><PERSON><PERSON><PERSON>ER $$
CREATE PROCEDURE `DAILY_CONSOLIDATED_CSAT_SCORE`(
IN BIZ_DATE DATE,
IN ORDER_START_TIME TIMESTAMP, 
IN ORDER_END_TIME TIMESTAMP, 
IN FEEDBACK_START_TIME TIMESTAMP, 
IN FEEDBACK_END_TIME TIMESTAMP,
ORDER_DAYS INTEGER, 
FEEDBACK_DAYS INTEGER, 
IN CSAT_TYPE VARCHAR(20))
proc_label : BEGIN

update KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE
set IS_CURRENT_SCORE = 'N'
where IS_CURRENT_SCORE = 'Y'
and CSAT_SCORE_TYPE = CSAT_TYPE;

INSERT INTO KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE (UNIT_ID,CSAT_SCORE_TYPE, ADD_TIME,<PERSON>AT_YEAR,CS<PERSON>_MONTH,
CSAT_DAY,<PERSON>AT_WEEK,CSAT_DATE,NO_OF_ORDER_DAYS, NO_OF_FEEDBACK_DAYS, IS_CURRENT_SCORE)
SELECT 
    ud.UNIT_ID,
    CSAT_TYPE,
    CURRENT_TIMESTAMP,
    YEAR(BIZ_DATE),
    MONTH(BIZ_DATE),
    DAY(BIZ_DATE),
    WEEK(BIZ_DATE),
    BIZ_DATE,
	ORDER_DAYS,
    FEEDBACK_DAYS,
    'Y'
FROM KETTLE_MASTER_DUMP.UNIT_DETAIL ud
where ud.UNIT_STATUS = 'ACTIVE'
and ud.UNIT_CATEGORY IN ('CAFE', 'DELIVERY')
;
 
update KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE ofd, (SELECT 
    ofd.UNIT_ID,
    min(ofd.FEEDBACK_ID) START_FEEDBACK_ID,
    max(ofd.FEEDBACK_ID) END_FEEDBACK_ID,
    COUNT(*) TOTAL_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN ofd.FEEDBACK_STATUS = 'COMPLETED' THEN 1
        ELSE 0
    END) TOTAL_RECEIVED_FEEDBACK,
    CAST(AVG(ofd.FEEDBACK_RATING) AS DECIMAL(10,2) )CUMULATIVE_FEEDBACK_RATING,
    SUM(CASE
        WHEN ofd.ORDER_SOURCE <> 'COD' THEN 1
        ELSE 0
    END) DINE_IN_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN
            ofd.ORDER_SOURCE <> 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            1
        ELSE 0
    END) DINE_IN_RECEIVED_FEEDBACK,
    CAST(AVG(CASE
        WHEN
            ofd.ORDER_SOURCE <> 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            ofd.FEEDBACK_RATING
        ELSE NULL
    END) AS DECIMAL(10,2) ) DINE_IN_FEEDBACK_RATING,
    SUM(CASE
        WHEN ofd.ORDER_SOURCE = 'COD' THEN 1
        ELSE 0
    END) DELIVERY_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN
            ofd.ORDER_SOURCE = 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            1
        ELSE 0
    END) DELIVERY_RECEIVED_FEEDBACK,
    CAST(AVG(CASE
        WHEN
            ofd.ORDER_SOURCE = 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            ofd.FEEDBACK_RATING
        ELSE NULL
    END) AS DECIMAL(10,2) ) DELIVERY_FEEDBACK_RATING
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL ofd ON ofd.ORDER_ID = od.ORDER_ID
WHERE
    (ofd.FEEDBACK_TIME IS NULL
        && ofd.FEEDBACK_STATUS = 'CREATED')
        OR (ofd.FEEDBACK_TIME >= FEEDBACK_START_TIME
        AND ofd.FEEDBACK_TIME < FEEDBACK_END_TIME
        AND ofd.FEEDBACK_STATUS = 'COMPLETED')
        AND od.BILLING_SERVER_TIME >= ORDER_START_TIME
        AND od.BILLING_SERVER_TIME < ORDER_END_TIME
GROUP BY ofd.UNIT_ID ) a
set ofd.REQUESTED_FEEDBACK = a.TOTAL_REQUESTED_FEEDBACK,
    ofd.RECEIVED_FEEDBACK = a.TOTAL_RECEIVED_FEEDBACK,
    ofd.REQUESTED_DINE_IN_FEEDBACK = a.DINE_IN_REQUESTED_FEEDBACK,
	ofd.REQUESTED_DELIVERY_FEEDBACK = a.DELIVERY_REQUESTED_FEEDBACK,
    ofd.RECEIVED_DINE_IN_FEEDBACK = a.DINE_IN_RECEIVED_FEEDBACK,   
    ofd.RECEIVED_DELIVERY_FEEDBACK = a.DELIVERY_RECEIVED_FEEDBACK,
    ofd.DINE_IN_SCORE = a.DINE_IN_FEEDBACK_RATING,
    ofd.DELIVERY_SCORE = a.DELIVERY_FEEDBACK_RATING,
    ofd.CUMULATIVE_SCORE = a.CUMULATIVE_FEEDBACK_RATING,
    ofd.START_FEEDBACK_ID = a.START_FEEDBACK_ID,
    ofd.END_FEEDBACK_ID = a.END_FEEDBACK_ID
    where a.UNIT_ID = ofd.UNIT_ID
    and ofd.CSAT_SCORE_TYPE = CSAT_TYPE
    and ofd.IS_CURRENT_SCORE = 'Y';
    
update KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE ofd, KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE ofd1
set
ofd.TOTAL_REQUESTED_FEEDBACK = ofd1.REQUESTED_FEEDBACK,
ofd.TOTAL_RECEIVED_FEEDBACK = ofd1.RECEIVED_FEEDBACK,
ofd.TOTAL_REQUESTED_DINE_IN_FEEDBACK = ofd1.REQUESTED_DINE_IN_FEEDBACK,
ofd.TOTAL_REQUESTED_DELIVERY_FEEDBACK = ofd1.REQUESTED_DELIVERY_FEEDBACK,
ofd.TOTAL_RECEIVED_DINE_IN_FEEDBACK = ofd1.RECEIVED_DINE_IN_FEEDBACK,
ofd.TOTAL_RECEIVED_DELIVERY_FEEDBACK = ofd1.RECEIVED_DELIVERY_FEEDBACK,
ofd.CONSOLIDATED_DINE_IN_SCORE = ofd1.DINE_IN_SCORE,
ofd.CONSOLIDATED_DELIVERY_SCORE = ofd1.DELIVERY_SCORE,
ofd.CONSOLIDATED_SCORE = ofd1.CUMULATIVE_SCORE
where ofd.UNIT_ID = ofd1.UNIT_ID
and ofd.CSAT_SCORE_TYPE = CSAT_TYPE
and ofd.IS_CURRENT_SCORE = 'Y'
and ofd1.CSAT_SCORE_TYPE = 'CUMULATIVE'
and ofd1.IS_CURRENT_SCORE = 'Y';

update KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE ofd, (
select m.UNIT_ID,
	SUM(CASE WHEN m.ORDER_SOURCE <> 'COD' AND m.RESPONSE_CATEGORY = 'Ambience' then m.FEEDBACK_COUNT ELSE 0 END) CAFE_AMBIENCE_ISSUES,
	SUM(CASE WHEN m.ORDER_SOURCE <> 'COD' AND m.RESPONSE_CATEGORY = 'Chai/ Beverages' then m.FEEDBACK_COUNT ELSE 0 END) CAFE_BEVERAGE_ISSUES,
	SUM(CASE WHEN m.ORDER_SOURCE <> 'COD' AND m.RESPONSE_CATEGORY = 'Food' then m.FEEDBACK_COUNT ELSE 0 END) CAFE_FOOD_ISSUES,
	SUM(CASE WHEN m.ORDER_SOURCE <> 'COD' AND m.RESPONSE_CATEGORY = 'Service' then m.FEEDBACK_COUNT ELSE 0 END) CAFE_SERVICE_ISSUES,
	SUM(CASE WHEN m.ORDER_SOURCE = 'COD' AND m.RESPONSE_CATEGORY = 'Ambience' then m.FEEDBACK_COUNT ELSE 0 END) DELIVERY_AMBIENCE_ISSUES,
	SUM(CASE WHEN m.ORDER_SOURCE = 'COD' AND m.RESPONSE_CATEGORY = 'Chai/ Beverages' then m.FEEDBACK_COUNT ELSE 0 END) DELIVERY_BEVERAGE_ISSUES,
	SUM(CASE WHEN m.ORDER_SOURCE = 'COD' AND m.RESPONSE_CATEGORY = 'Food' then m.FEEDBACK_COUNT ELSE 0 END) DELIVERY_FOOD_ISSUES,
	SUM(CASE WHEN m.ORDER_SOURCE = 'COD' AND m.RESPONSE_CATEGORY = 'Service' then m.FEEDBACK_COUNT ELSE 0 END) DELIVERY_SERVICE_ISSUES
from (
SELECT 
    ofd.UNIT_ID,
    ofd.ORDER_SOURCE,
    frd.RESPONSE_CATEGORY,
    COUNT(distinct ofd.FEEDBACK_ID) FEEDBACK_COUNT
FROM
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL ofd,
    KETTLE_DUMP.ORDER_FEEDBACK_EVENT ofe,
    KETTLE_DUMP.FEEDBACK_INFO fi,
    KETTLE_DUMP.FEEDBACK_RESPONSE fr,
    KETTLE_DUMP.FEEDBACK_FIELD ff,
    KETTLE_DUMP.FEEDBACK_RESPONSE_DATA frd
WHERE
    ofd.FEEDBACK_STATUS = 'COMPLETED'
        AND ofd.LATEST_FEEDBACK_INFO_ID = fi.FEEDBACK_INFO_ID
        AND ofe.LATEST_FEEDBACK_INFO_ID = fi.FEEDBACK_INFO_ID
        AND ofd.FEEDBACK_ID = ofe.FEEDBACK_ID
		AND ofd.FEEDBACK_TIME >= FEEDBACK_START_TIME
        AND ofd.FEEDBACK_TIME < FEEDBACK_END_TIME
        AND ofe.EVENT_STATUS = 'SUCCESSFUL'
        AND fi.FEEDBACK_INFO_ID = fr.FEEDBACK_INFO_ID
        AND fr.FEEDBACK_FIELD_ID = ff.FEEDBACK_FIELD_ID
        AND fr.FEEDBACK_RESPONSE_ID = frd.FEEDBACK_RESPONSE_ID
        AND frd.TO_BE_CONSIDERED = 'Y'
        AND frd.FEEDBACK_RATING IN('1','2','3')
        AND frd.RESPONSE_CATEGORY NOT IN ('1','2','3','4','5', 'NA')
GROUP BY ofd.UNIT_ID , ofd.ORDER_SOURCE , frd.RESPONSE_CATEGORY
) m
group by m.UNIT_ID ) a
set ofd.CAFE_AMBIENCE_ISSUES = a.CAFE_AMBIENCE_ISSUES,
    ofd.CAFE_BEVERAGE_ISSUES = a.CAFE_BEVERAGE_ISSUES,
    ofd.CAFE_FOOD_ISSUES = a.CAFE_FOOD_ISSUES,
	ofd.CAFE_SERVICE_ISSUES = a.CAFE_SERVICE_ISSUES,
    ofd.DELIVERY_AMBIENCE_ISSUES = a.DELIVERY_AMBIENCE_ISSUES,   
    ofd.DELIVERY_BEVERAGE_ISSUES = a.DELIVERY_BEVERAGE_ISSUES,
    ofd.DELIVERY_FOOD_ISSUES = a.DELIVERY_FOOD_ISSUES,
    ofd.DELIVERY_SERVICE_ISSUES = a.DELIVERY_SERVICE_ISSUES
    where a.UNIT_ID = ofd.UNIT_ID
	and ofd.CSAT_SCORE_TYPE = CSAT_TYPE
	and ofd.IS_CURRENT_SCORE = 'Y';

END$$
DELIMITER ;

DROP PROCEDURE OVERALL_CONSOLIDATED_CSAT_SCORE;
DELIMITER $$
CREATE PROCEDURE `OVERALL_CONSOLIDATED_CSAT_SCORE`(
IN BIZ_DATE DATE)
proc_label : BEGIN

update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = 'NA' where RESPONSE_DATA IN ('No issues','No issues. I liked it','No issues. It was all great','No issues. My order was on time and in one go','There was no issue with my Delivery', '0') and RESPONSE_CATEGORY IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = RESPONSE_DATA where RESPONSE_DATA IN ('1','2','3','4','5') and RESPONSE_CATEGORY IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = 'Ambience' where RESPONSE_DATA IN ('Ambience','I did not like cleanliness / hygiene at the cafe','I did not like cleanliness / hygiene of the washroom','I did not like cleanliness / hygiene of the kitchen area','Kitchen area was messy ','Music wasn\'t audible in the cafe ','The Café I visited wasn\'t clean','There were pests/flies in the cafe','The Café didn\'t smell good','Restroom wasn\'t hygienic enough','Café window/outside glass wasn\'t clean') and RESPONSE_CATEGORY IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = 'Service' where RESPONSE_DATA IN ('I was called more than once to collect my order','Internal staff communication was too loud and interfering ','My order was delayed','Service','The order taker could not interact well','The order taker did not help me make a choice','Could not pay online on app / website','Delivery boy did not bring condiments','Delivery boy did not have the right change','Had to enquire about order status','Incorrect / Incomplete order was delivered','Order reached late','I was served on the table','The order taker was not groomed well','Correct amount or money/change wasn\'t returned ','Cafe staff did not assist to make an informed choice','Café staff wasn\'t properly dressed','Couldn\'t find a table in stipulated time','Expected wait time wasn\'t communicated ','I expected to be served on the table ','I received an incomplete order ','Placement of order ','Receiving of order ','The closing time of the Café should be increased','The wait time to place an order was high ','The menu was too confusing','Wi-fi didn\'t connect properly','Delivery boy did not behave well','Cafe staff wasn\'t well groomed ','I was called more than twice to collect my order','I wasn\'t able to get the condiments ','I wasn\'t given my free Loyal-Tea Chai','Registering the phone number is a cumbersome process ','Delivery ','The Loyal-Tea program is too complex','My order reached late','Food wasn\'t packaged properly','Had to follow-up on the order status') and RESPONSE_CATEGORY IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = 'Chai/ Beverages' where RESPONSE_DATA IN ('Chai/ Beverages','I did not like my Chai','I did not like my Cold Beverage','Chai/Food was served cold','My Chai/Food didn\'t taste great','Quality of Chai/Food served ','Kulhad was broken/beverage cup was leaking ','My Chai had a layer of Malai') and RESPONSE_CATEGORY IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set RESPONSE_CATEGORY = 'Food' where RESPONSE_DATA IN ('Food','I did not like my Food') and RESPONSE_CATEGORY IS NULL;

update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set TO_BE_CONSIDERED = 'N' where RESPONSE_DATA 
IN('No issues','No issues. I liked it','No issues. It was all great','No issues. My order was on time and in one go','There was no issue with my Delivery'
) AND TO_BE_CONSIDERED  IS NULL;

update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set TO_BE_CONSIDERED = 'N' where RESPONSE_DATA 
NOT IN('1','2','3','4','5', 'No issues','No issues. I liked it','No issues. It was all great','No issues. My order was on time and in one go','There was no issue with my Delivery'
) AND FEEDBACK_RATING IN ('4','5')
AND TO_BE_CONSIDERED  IS NULL;
update KETTLE_DUMP.FEEDBACK_RESPONSE_DATA set TO_BE_CONSIDERED = 'Y' where TO_BE_CONSIDERED IS NULL;

INSERT INTO KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE (UNIT_ID,CSAT_SCORE_TYPE, ADD_TIME,CSAT_YEAR,CSAT_MONTH,
CSAT_DAY,CSAT_WEEK,CSAT_DATE,NO_OF_ORDER_DAYS, NO_OF_FEEDBACK_DAYS, IS_CURRENT_SCORE)
SELECT 
    ud.UNIT_ID,
    'CUMULATIVE',
    CURRENT_TIMESTAMP,
    YEAR(BIZ_DATE),
    MONTH(BIZ_DATE),
    DAY(BIZ_DATE),
    WEEK(BIZ_DATE),
    BIZ_DATE,
	-1,
    -1,
    'Y'
FROM KETTLE_MASTER_DUMP.UNIT_DETAIL ud left outer join KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE ccs
on ud.UNIT_ID = ccs.UNIT_ID and ccs.CSAT_SCORE_TYPE = 'CUMULATIVE'
where ud.UNIT_STATUS = 'ACTIVE'
and ud.UNIT_CATEGORY IN ('CAFE', 'DELIVERY')
and ccs.UNIT_ID is null
;
 
update KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE ofd, (SELECT 
    ofd.UNIT_ID,
    min(ofd.FEEDBACK_ID) START_FEEDBACK_ID,
    max(ofd.FEEDBACK_ID) END_FEEDBACK_ID,
    COUNT(*) TOTAL_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN ofd.FEEDBACK_STATUS = 'COMPLETED' THEN 1
        ELSE 0
    END) TOTAL_RECEIVED_FEEDBACK,
    CAST(AVG(ofd.FEEDBACK_RATING) AS DECIMAL(10,2)) CUMULATIVE_FEEDBACK_RATING,
    SUM(CASE
        WHEN ofd.ORDER_SOURCE <> 'COD' THEN 1
        ELSE 0
    END) DINE_IN_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN
            ofd.ORDER_SOURCE <> 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            1
        ELSE 0
    END) DINE_IN_RECEIVED_FEEDBACK,
    CAST(AVG(CASE
        WHEN
            ofd.ORDER_SOURCE <> 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            ofd.FEEDBACK_RATING
        ELSE NULL
    END) AS DECIMAL(10,2)) DINE_IN_FEEDBACK_RATING,
    SUM(CASE
        WHEN ofd.ORDER_SOURCE = 'COD' THEN 1
        ELSE 0
    END) DELIVERY_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN
            ofd.ORDER_SOURCE = 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            1
        ELSE 0
    END) DELIVERY_RECEIVED_FEEDBACK,
    CAST(AVG(CASE
        WHEN
            ofd.ORDER_SOURCE = 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            ofd.FEEDBACK_RATING
        ELSE NULL
    END) AS DECIMAL(10,2)) DELIVERY_FEEDBACK_RATING
FROM
       KETTLE_DUMP.ORDER_FEEDBACK_DETAIL ofd 
WHERE
    ofd.FEEDBACK_STATUS IN('CREATED','COMPLETED')
       
GROUP BY ofd.UNIT_ID ) a
set ofd.REQUESTED_FEEDBACK = a.TOTAL_REQUESTED_FEEDBACK,
    ofd.RECEIVED_FEEDBACK = a.TOTAL_RECEIVED_FEEDBACK,
    ofd.REQUESTED_DINE_IN_FEEDBACK = a.DINE_IN_REQUESTED_FEEDBACK,
	ofd.REQUESTED_DELIVERY_FEEDBACK = a.DELIVERY_REQUESTED_FEEDBACK,
    ofd.RECEIVED_DINE_IN_FEEDBACK = a.DINE_IN_RECEIVED_FEEDBACK,   
    ofd.RECEIVED_DELIVERY_FEEDBACK = a.DELIVERY_RECEIVED_FEEDBACK,
    ofd.DINE_IN_SCORE = a.DINE_IN_FEEDBACK_RATING,
    ofd.DELIVERY_SCORE = a.DELIVERY_FEEDBACK_RATING,
    ofd.CUMULATIVE_SCORE = a.CUMULATIVE_FEEDBACK_RATING,
    ofd.START_FEEDBACK_ID = a.START_FEEDBACK_ID,
    ofd.END_FEEDBACK_ID = a.END_FEEDBACK_ID
    where a.UNIT_ID = ofd.UNIT_ID
    and ofd.CSAT_SCORE_TYPE = 'CUMULATIVE'
    and ofd.IS_CURRENT_SCORE = 'Y';
    
    update KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE ofd, (select m.UNIT_ID,
SUM(CASE WHEN m.ORDER_SOURCE <> 'COD' AND m.RESPONSE_CATEGORY = 'Ambience' then m.FEEDBACK_COUNT ELSE 0 END) CAFE_AMBIENCE_ISSUES,
SUM(CASE WHEN m.ORDER_SOURCE <> 'COD' AND m.RESPONSE_CATEGORY = 'Chai/ Beverages' then m.FEEDBACK_COUNT ELSE 0 END) CAFE_BEVERAGE_ISSUES,
SUM(CASE WHEN m.ORDER_SOURCE <> 'COD' AND m.RESPONSE_CATEGORY = 'Food' then m.FEEDBACK_COUNT ELSE 0 END) CAFE_FOOD_ISSUES,
SUM(CASE WHEN m.ORDER_SOURCE <> 'COD' AND m.RESPONSE_CATEGORY = 'Service' then m.FEEDBACK_COUNT ELSE 0 END) CAFE_SERVICE_ISSUES,
SUM(CASE WHEN m.ORDER_SOURCE = 'COD' AND m.RESPONSE_CATEGORY = 'Ambience' then m.FEEDBACK_COUNT ELSE 0 END) DELIVERY_AMBIENCE_ISSUES,
SUM(CASE WHEN m.ORDER_SOURCE = 'COD' AND m.RESPONSE_CATEGORY = 'Chai/ Beverages' then m.FEEDBACK_COUNT ELSE 0 END) DELIVERY_BEVERAGE_ISSUES,
SUM(CASE WHEN m.ORDER_SOURCE = 'COD' AND m.RESPONSE_CATEGORY = 'Food' then m.FEEDBACK_COUNT ELSE 0 END) DELIVERY_FOOD_ISSUES,
SUM(CASE WHEN m.ORDER_SOURCE = 'COD' AND m.RESPONSE_CATEGORY = 'Service' then m.FEEDBACK_COUNT ELSE 0 END) DELIVERY_SERVICE_ISSUES
from (
SELECT 
    ofd.UNIT_ID,
    ofd.ORDER_SOURCE,
    frd.RESPONSE_CATEGORY,
    COUNT(distinct ofd.FEEDBACK_ID) FEEDBACK_COUNT
FROM
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL ofd,
    KETTLE_DUMP.ORDER_FEEDBACK_EVENT ofe,
    KETTLE_DUMP.FEEDBACK_INFO fi,
    KETTLE_DUMP.FEEDBACK_RESPONSE fr,
    KETTLE_DUMP.FEEDBACK_FIELD ff,
    KETTLE_DUMP.FEEDBACK_RESPONSE_DATA frd
WHERE
    ofd.FEEDBACK_STATUS = 'COMPLETED'
        AND ofd.LATEST_FEEDBACK_INFO_ID = fi.FEEDBACK_INFO_ID
        AND ofe.LATEST_FEEDBACK_INFO_ID = fi.FEEDBACK_INFO_ID
        AND ofd.FEEDBACK_ID = ofe.FEEDBACK_ID
        AND ofe.EVENT_STATUS = 'SUCCESSFUL'
        AND fi.FEEDBACK_INFO_ID = fr.FEEDBACK_INFO_ID
        AND fr.FEEDBACK_FIELD_ID = ff.FEEDBACK_FIELD_ID
        AND fr.FEEDBACK_RESPONSE_ID = frd.FEEDBACK_RESPONSE_ID
        AND frd.TO_BE_CONSIDERED = 'Y'
        AND frd.FEEDBACK_RATING IN('1','2','3')
        AND frd.RESPONSE_CATEGORY NOT IN ('1','2','3','4','5','NA')
GROUP BY ofd.UNIT_ID , ofd.ORDER_SOURCE , frd.RESPONSE_CATEGORY
) m
group by m.UNIT_ID ) a
set ofd.CAFE_AMBIENCE_ISSUES = a.CAFE_AMBIENCE_ISSUES,
    ofd.CAFE_BEVERAGE_ISSUES = a.CAFE_BEVERAGE_ISSUES,
    ofd.CAFE_FOOD_ISSUES = a.CAFE_FOOD_ISSUES,
	ofd.CAFE_SERVICE_ISSUES = a.CAFE_SERVICE_ISSUES,
    ofd.DELIVERY_AMBIENCE_ISSUES = a.DELIVERY_AMBIENCE_ISSUES,   
    ofd.DELIVERY_BEVERAGE_ISSUES = a.DELIVERY_BEVERAGE_ISSUES,
    ofd.DELIVERY_FOOD_ISSUES = a.DELIVERY_FOOD_ISSUES,
    ofd.DELIVERY_SERVICE_ISSUES = a.DELIVERY_SERVICE_ISSUES
    where a.UNIT_ID = ofd.UNIT_ID
    and ofd.CSAT_SCORE_TYPE = 'CUMULATIVE'
    and ofd.IS_CURRENT_SCORE = 'Y';
   
 END$$
DELIMITER ;
