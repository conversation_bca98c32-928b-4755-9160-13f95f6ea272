ALTER TABLE KETTLE_DEV.LOYALTY_TRANSFER ADD COLUMN (RECEIVER_INITIAL_POINTS INT(10));

ALTER TABLE KETTLE_DEV.CUSTOMER_ADDRESS_INFO ADD COLUMN LATITUDE VARCHAR(30) NULL,
ADD COLUMN LONGITUDE VARCHAR(30) NULL;

ALTER TABLE KETTLE_DEV.LOYALTY_TRANSFER ADD COLUMN (SENDER_BALANCE INT(10));
ALTER TABLE KETTLE_DEV.LOYALTY_TRANSFER ADD COLUMN (RECEIVER_BALANCE INT(10));

ALTER TABLE KETTLE_DEV.ORDER_ITEM ADD COLUMN TAX_DEDUCTED_BY_PARTNER VARCHAR(1) NOT NULL DEFAULT 'N', ALGORITHM=INPLACE, LOCK=NONE;

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JO<PERSON>_<PERSON>AM<PERSON>`, `JO<PERSON>_DESCRIPTION`, `JO<PERSON>_<PERSON><PERSON><PERSON>`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('UNIT_INTRA_DAY_SALES_DATA_JOB', 'UNIT_INTRA_DAY_SALES_DATA_job', 'ADHOC', 'jobs/knock/UNIT_INTRA_DAY_SALES_DATA_job.kjb', 'ACTIVE');


INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (API, STATUS) VALUES ('channel-partner.partner-metadata-management.activate-cafe-for-unit', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('AVAILABILITY_JOB', 'AVAILABILITY_job', 'ADHOC', 'jobs/inventory/availability_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('SUGGESTED_ORDER_QUNATITY_JOB', 'suggested_order_qunatity_job', 'ADHOC', 'jobs/inventory/suggested_order_qunatity_job.kjb', 'ACTIVE');

CREATE TABLE `KETTLE_DEV`.`PARTNER_UNIT_PRODUCT_STOCK_DATA` (
   `KEY_ID` int(11) NOT NULL AUTO_INCREMENT,
   `BUSINESS_DATE` date DEFAULT NULL,
   `CALCULATION_DATE` date DEFAULT NULL,
   `CAFE_OPENING` datetime DEFAULT NULL,
   `CAFE_CLOSING` datetime DEFAULT NULL,
   `UNIT_ID` int(11) DEFAULT NULL,
   `UNIT_NAME` varchar(40) DEFAULT NULL,
   `PRODUCT_ID` int(11) DEFAULT NULL,
   `PRODUCT_NAME` varchar(255) DEFAULT NULL,
   `DOWNTIME` int(11) DEFAULT NULL,
   `START_TIME` datetime DEFAULT NULL,
   `CLOSE_TIME` datetime DEFAULT NULL,
   `DIMENSION` varchar(20) DEFAULT NULL,
   `END_HOUR` int(11) DEFAULT NULL,
   `START_HOUR` int(11) DEFAULT NULL,
   `TOTAL_TIME` int(11) DEFAULT NULL,
   `WEIGHT` decimal(16,6) DEFAULT NULL,
   `BRAND_ID` int(11) DEFAULT NULL,
   PRIMARY KEY (`KEY_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=1299778 DEFAULT CHARSET=latin1;
