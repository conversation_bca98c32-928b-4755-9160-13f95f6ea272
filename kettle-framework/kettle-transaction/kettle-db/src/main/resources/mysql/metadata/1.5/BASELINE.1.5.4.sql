ALTER TABLE UNIT_PRODUCT_INVENTORY 
ADD COLUMN PRODUCT_INVENTORY_STATUS VARCHAR(15) NOT NULL DEFAULT 'ACTIVE';

update SUBSCRIPTION_EVENT_ITEM set EVENT_ITEM_VALUE = 1 where EVENT_ITEM_TYPE = 'DAY_OF_WEEK' and EVENT_ITEM_VALUE = 7;
update SUBSCRIPTION_EVENT_ITEM set EVENT_ITEM_VALUE = 2 where EVENT_ITEM_TYPE = 'DAY_OF_WEEK' and EVENT_ITEM_VALUE = 1;
update SUBSCRIPTION_EVENT_ITEM set EVENT_ITEM_VALUE = 3 where EVENT_ITEM_TYPE = 'DAY_OF_WEEK' and EVENT_ITEM_VALUE = 2;
update SUBSCRIPTION_EVENT_ITEM set EVENT_ITEM_VALUE = 4 where EVENT_ITEM_TYPE = 'DAY_OF_WEEK' and EVENT_ITEM_VALUE = 3;
update SUBSCRIPTION_EVENT_ITEM set EVENT_ITEM_VALUE = 5 where EVENT_ITEM_TYPE = 'DAY_OF_WEEK' and EVENT_ITEM_VALUE = 4;
update SUBSCRIPTION_EVENT_ITEM set EVENT_ITEM_VALUE = 6 where EVENT_ITEM_TYPE = 'DAY_OF_WEEK' and EVENT_ITEM_VALUE = 5;
update SUBSCRIPTION_EVENT_ITEM set EVENT_ITEM_VALUE = 7 where EVENT_ITEM_TYPE = 'DAY_OF_WEEK' and EVENT_ITEM_VALUE = 6;

