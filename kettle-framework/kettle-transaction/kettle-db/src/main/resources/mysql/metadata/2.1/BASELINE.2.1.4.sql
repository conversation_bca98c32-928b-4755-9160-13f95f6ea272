CREATE TABLE KETTLE_DEV.ORDER_FEEDBACK_DETAIL
(
FEEDBACK_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ORDER_ID INTEGER NOT NULL,
ORDER_SOURCE VARCHAR(20) NOT NULL,
UNIT_ID INTEGER NOT NULL,
CUSTOMER_ID INTEGER NOT NULL,
CUSTOMER_NAME VARCHAR(100) NULL,
CONTACT_NUMBER VARCHAR(15) NOT NULL,
EMAIL_ID VARCHAR(100) NULL,
FEEDBACK_SOURCE VARCHAR(15) NULL,
FEEDBACK_STATUS VARCHAR(20) NOT NULL,
FEED<PERSON><PERSON><PERSON>_CREATION_TIME TIMESTAMP NOT NULL,
FEEDBACK_TIME TIMESTAMP NULL,
FEEDBACK_UNIT_ID INTEGER NULL,
LATEST_FEEDBACK_INFO_ID INTEGER NULL
);

CREATE TABLE KETTLE_DEV.ORDER_FEEDBACK_EVENT
(
FEEDBACK_EVENT_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
FEEDBACK_ID INTEGER NOT NULL,
EVENT_SHORT_URL VARCHAR(150) NULL,
EVENT_SHORT_URL_ID VARCHAR(50) NULL,
EVENT_LONG_URL VARCHAR(500) NULL,
EVENT_SOURCE VARCHAR(20)  NOT NULL,
EVENT_STATUS VARCHAR(20) NULL,
EVENT_GENERATION_TIME TIMESTAMP NOT NULL,
EVENT_TRIGGER_TIME TIMESTAMP NOT NULL,
EVENT_NOTIFICATION_TIME TIMESTAMP NULL,
EVENT_COMPLETION_TIME TIMESTAMP NULL,
LATEST_FEEDBACK_INFO_ID INTEGER NULL
);

CREATE TABLE KETTLE_DEV.FEEDBACK_RESPONSE_DATA
(
FEEDBACK_RESPONSE_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
FEEDBACK_RESPONSE_ID INTEGER NOT NULL,
RESPONSE_DATA VARCHAR(500) NULL
);

CREATE TABLE KETTLE_DEV.FEEDBACK_RESPONSE
(
FEEDBACK_RESPONSE_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
FEEDBACK_FIELD_ID INTEGER NOT NULL,
RESPONSE_TYPE VARCHAR(20) NULL
);

CREATE TABLE KETTLE_DEV.FEEDBACK_FORM
(
FORM_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
FORM_SOURCE_ID VARCHAR(100) NULL,
FORM_TITLE VARCHAR(500) NULL
);

CREATE TABLE KETTLE_DEV.FEEDBACK_FIELD
(
FEEDBACK_FIELD_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
FORM_ID INTEGER NOT NULL,
FEEDBACK_SOURCE_FIELD_ID INTEGER NOT NULL,
FIELD_TYPE VARCHAR(20) NULL,
FIELD_TITLE VARCHAR(500) NULL
);

CREATE TABLE KETTLE_DEV.FEEDBACK_INFO
(
FEEDBACK_INFO_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
FEEDBACK_ID INTEGER NOT NULL,
FEEDBACK_EVENT_ID INTEGER NOT NULL,
RESPONSE_ID VARCHAR(100) NULL,
FORM_ID INTEGER NOT NULL,
RESPONSE_TOKEN VARCHAR(100) NULL,
FEEDBACK_TIME TIMESTAMP NULL
);

ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_DETAIL
ADD COLUMN PRODUCT_IDS VARCHAR(60) NULL;

ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_DETAIL
ADD COLUMN FEEDBACK_RATING INTEGER NULL;

ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_EVENT
ADD COLUMN FEEDBACK_RATING INTEGER NULL;

ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_EVENT
ADD COLUMN EVENT_TYPE VARCHAR(15) NULL;

ALTER TABLE KETTLE_DEV.FEEDBACK_INFO
ADD COLUMN FEEDBACK_RATING INTEGER NULL;
