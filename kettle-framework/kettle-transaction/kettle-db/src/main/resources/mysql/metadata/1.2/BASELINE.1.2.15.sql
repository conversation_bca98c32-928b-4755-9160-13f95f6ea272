UPDATE ADDRESS_INFO SET `ADDRESS_LINE_1`='D-2, 2 floor', `ADDRESS_LINE_2`='Greenpark extension', `CITY`='New Delhi', `STATE`='New Delhi', `ZIPCODE`='110016', `CONTACT_NUM_1`='+91-9717449667', `CONTACT_NUM_2`='+91-9717449667' WHERE `ADDRESS_ID`='11000';

INSERT INTO ADDRESS_INFO
(`ADDRESS_ID`,`ADDRESS_LINE_1`,`ADDRESS_LINE_2`,`ADDRESS_LINE_3`,`CITY`,`STATE`,`COUNTRY`,`ZIPCODE`,`CONTACT_NUM_1`,`CONTACT_NUM_2`)
VALUES
(10054,'C-91,Suraj park','Rohini sec. -18',null,'New Delhi','New Delhi','India','110083','+91-9899791889','+91-8860100568'),
(10055,'H no:-4112/436','<PERSON><PERSON><PERSON>',null,'Gurgaon','Haryana','India','122001','+91-9560421434','+91-9911392089'),
(10056,'H.no.-766,Room no.-12','baba tent house,chackerpur',null,'Gurgaon','Haryana','India','122002','+91-9654242382','+91-7838167120');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10057', 'ROOM NO =264  RABALE KOLIWADA', 'NEAR GWALI HOSPITAL NEAR RABALE EAST', 'Mumbai', 'Maharashtra', 'India', '401209', '+91-8424915441', '+91-8424915441', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10058', 'ROOM NO : 573 MUKTI NAGAR', 'VAMAN PATIL MARG  CHEMBUR', 'Mumbai', 'Maharashtra', 'India', '401271', '+91-9773943376', '+91-9773943376', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10059', 'ROOM no : 406 , BEET APARTMNT C', 'BADRI PADA NALLASOPRA(E)', 'Mumbai', 'Maharashtra', 'India', '400701', '+91-8655402122', '+91-8655402122', 'RESIDENTIAL');

INSERT INTO EMPLOYEE_DETAIL
(`EMP_ID`,`EMP_NAME`,`EMP_GENDER`,`EMP_CURRENT_ADDR`,`EMP_PERMANENT_ADDR`,`EMP_CONTACT_NUM_1`,`EMP_CONTACT_NUM_2`,`DEPTARTMENT_ID`,
`DESIGNATION_ID`,`EMPLOYMENT_TYPE`,`EMPLOYMENT_STATUS`,`BIOMETRIC_IDENTIFIER`,`JOINING_DATE`,`TERMINATION_DATE`,`REPORTING_MANAGER_ID`)
VALUES
(100049,'Sandeep Mehla','M',10054,10054,'+91-9899791889','+91-8860100568',101,1003,'FULL_TIME','ACTIVE',null,'2015-10-19','9999-12-01',100002),
(100050,'Ayush Kumar','M',10055,10055,'+91-9560421434','+91-9911392089',101,1003,'FULL_TIME','ACTIVE',null,'2015-10-26','9999-12-01',100002),
(100051,'Ranjeet Singh','M',10056,10056,'+91-9654242382','+91-7838167120',101,1003,'FULL_TIME','ACTIVE',null,'2015-10-29','9999-12-01',100003);

INSERT INTO EMPLOYEE_DETAIL
(`EMP_ID`,`EMP_NAME`,`EMP_GENDER`,`EMP_CURRENT_ADDR`,`EMP_PERMANENT_ADDR`,`EMP_CONTACT_NUM_1`,`EMP_CONTACT_NUM_2`,`DEPTARTMENT_ID`,
`DESIGNATION_ID`,`EMPLOYMENT_TYPE`,`EMPLOYMENT_STATUS`,`BIOMETRIC_IDENTIFIER`,`JOINING_DATE`,`TERMINATION_DATE`,`REPORTING_MANAGER_ID`)
VALUES
(100052,'Ajay Kumar','M',10057,10057,'+91-8424915441','+91-8424915441',101,1002,'FULL_TIME','ACTIVE',null,'2015-10-29','9999-12-01',100037),
(100053,'Ajit Sawant','M',10058,10058,'+91-9773943376','+91-9773943376',101,1002,'FULL_TIME','ACTIVE',null,'2015-10-29','9999-12-01',100038),
(100054,'Pradeep Sathe','M',10059,10059,'+91-8655402122','+91-8655402122',101,1003,'FULL_TIME','ACTIVE',null,'2015-10-29','9999-12-01',100039);

INSERT INTO `EMPLOYEE_UNIT_MAPPING`
(`EMP_ID`,
`UNIT_ID`)
VALUES
(100049,10001),
(100050,10001),
(100051,10011);

INSERT INTO `EMPLOYEE_UNIT_MAPPING`
(`EMP_ID`,
`UNIT_ID`)
VALUES
(100052,10009),
(100053,10009),
(100054,10009);

INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
VALUES
(100049,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100050,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100051,'0UxQYmqlaaaY1jDVAZWNQQ==' );

INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
VALUES
(100052,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100053,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100054,'0UxQYmqlaaaY1jDVAZWNQQ==' );

UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='100';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='101';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='102';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='103';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='104';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='105';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='106';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='107';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='108';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-9893598230', `CONTACT_NUM_2`='+91-9893598230' WHERE `ADDRESS_ID`='109';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-9893598230', `CONTACT_NUM_2`='+91-9893598230' WHERE `ADDRESS_ID`='110';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='111';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='112';
UPDATE ADDRESS_INFO SET `CONTACT_NUM_1`='+91-1132551234', `CONTACT_NUM_2`='+91-1132551234' WHERE `ADDRESS_ID`='113';

UPDATE UNIT_DETAIL SET `UNIT_EMAIL`='<EMAIL>' WHERE `UNIT_ID`='10010';
UPDATE UNIT_DETAIL SET `NO_OF_TERMINALS`='1' WHERE `UNIT_ID`='10013';

