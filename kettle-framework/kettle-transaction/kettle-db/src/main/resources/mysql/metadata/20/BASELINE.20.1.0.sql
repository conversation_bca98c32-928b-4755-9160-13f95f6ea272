CREATE TABLE KETTLE_DEV.GAMIFIED_OFFER_DETAIL (
    SPECIAL_OFFER_ID int(11) NOT NULL AUTO_INCREMENT,
    CUSTOMER_ID int(11) DEFAULT NULL,
    CONTACT_NUMBER varchar(12) DEFAULT NULL,
    OFFER_TYPE varchar(30) DEFAULT NULL,
    CAMPAIGN_ID int(11) DEFAULT NULL,
    CAMPAIGN_STRATEGY varchar(30) DEFAULT NULL,
    RECORD_TIME timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UTM_SOURCE varchar(50) DEFAULT NULL,
    UTM_MEDIUM varchar(50) DEFAULT NULL,
    RECORD_STATUS varchar(50) DEFAULT NULL,
    CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID int(11) DEFAULT NULL,
    SUBSCRIPTION_PLAN_ID int(11) DEFAULT NULL,
    CASH_PACKET_ID int(11) DEFAULT NULL,
    MAX_USAGE int(11) DEFAULT NULL,
    START_DATE varchar(15) DEFAULT NULL,
    END_DATE varchar(15) DEFAULT NULL,
    COUPON_CODE varchar(30) DEFAULT NULL,
    OFFER_TEXT varchar(65) DEFAULT NULL,
    CASH_AMOUNT decimal(10,2) DEFAULT NULL,
    FLOW int(11) DEFAULT NULL,
    TNC varchar(500) DEFAULT NULL,
    PRIMARY KEY (SPECIAL_OFFER_ID),
    KEY SPECIAL_OFFER_DETAIL_CASH_AMOUNT (CASH_AMOUNT) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_SPECIAL_OFFER_ID (SPECIAL_OFFER_ID) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_CUSTOMER_ID (CUSTOMER_ID) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_CONTACT_NUMBER (CONTACT_NUMBER) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_OFFER_TYPE (OFFER_TYPE) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_RECORD_TIME (RECORD_TIME) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_UTM_SOURCE (UTM_SOURCE) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_UTM_MEDIUM (UTM_MEDIUM) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_RECORD_STATUS (RECORD_STATUS) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID (CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_SUBSCRIPTION_PLAN_ID (SUBSCRIPTION_PLAN_ID) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_MAX_USAGE (MAX_USAGE) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_START_DATE (START_DATE) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_COUPON_CODE (COUPON_CODE) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_OFFER_TEXT (OFFER_TEXT) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_FLOW (FLOW) USING BTREE,
    KEY SPECIAL_OFFER_DETAIL_TNC (TNC) USING BTREE,
    KEY idx_SPECIAL_OFFER_DETAIL_SPECIAL_OFFER_ID (SPECIAL_OFFER_ID) USING BTREE,
    KEY idx_SPECIAL_OFFER_DETAIL_OFFER_TYPE (OFFER_TYPE) USING BTREE,
    KEY idx_SPECIAL_OFFER_DETAIL_CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID (CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID) USING BTREE,
    KEY idx_SPECIAL_OFFER_DETAIL_SUBSCRIPTION_PLAN_ID (SUBSCRIPTION_PLAN_ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE KETTLE_DEV.DROOLS_DECISION_TABLE_DATA (
    DROOLS_DECISION_TABLE_ID int(11) NOT NULL AUTO_INCREMENT,
    TABLE_TYPE varchar(50) DEFAULT NULL,
    CREATION_TIME timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    FILE_NAME varchar(30) DEFAULT NULL,
    TABLE_STATUS varchar(12) DEFAULT NULL,
    PRIMARY KEY (DROOLS_DECISION_TABLE_ID)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE INDEX DROOLS_DECISION_TABLE_ID ON KETTLE_DEV.DROOLS_DECISION_TABLE_DATA(DROOLS_DECISION_TABLE_ID) USING BTREE;
CREATE INDEX TABLE_TYPE ON KETTLE_DEV.DROOLS_DECISION_TABLE_DATA(TABLE_TYPE) USING BTREE;
CREATE INDEX CREATION_TIME ON KETTLE_DEV.DROOLS_DECISION_TABLE_DATA(CREATION_TIME) USING BTREE;
CREATE INDEX FILE_NAME ON KETTLE_DEV.DROOLS_DECISION_TABLE_DATA(FILE_NAME) USING BTREE;
CREATE INDEX TABLE_STATUS ON KETTLE_DEV.DROOLS_DECISION_TABLE_DATA(TABLE_STATUS) USING BTREE;

CREATE INDEX WORKSTATION_MANUAL_TASK_TASK_TYPE ON KETTLE_DEV.WORKSTATION_MANUAL_TASK_DETAIL(TASK_TYPE) USING BTREE;
CREATE INDEX WORKSTATION_MANUAL_TASK_ORDER_TIME ON KETTLE_DEV.WORKSTATION_MANUAL_TASK_DETAIL(ORDER_TIME) USING BTREE;
CREATE INDEX WORKSTATION_MANUAL_TASK_UNIT_ID ON KETTLE_DEV.WORKSTATION_MANUAL_TASK_DETAIL(UNIT_ID) USING BTREE;
CREATE INDEX WORKSTATION_MANUAL_TASK_EMPLOYEE_ID ON KETTLE_DEV.WORKSTATION_MANUAL_TASK_DETAIL(EMPLOYEE_ID) USING BTREE;
CREATE INDEX WORKSTATION_MANUAL_TASK_ORDER_ID ON KETTLE_DEV.WORKSTATION_MANUAL_TASK_DETAIL(GENERATED_ORDER_ID) USING BTREE;
CREATE INDEX WORKSTATION_MANUAL_TASK_PRODUCT_ITEM_ID ON KETTLE_DEV.WORKSTATION_MANUAL_TASK_DETAIL(PRODUCT_ITEM_ID) USING BTREE;
CREATE INDEX WORKSTATION_MANUAL_TASK_DIMENSION ON KETTLE_DEV.WORKSTATION_MANUAL_TASK_DETAIL(DIMENSION) USING BTREE;
CREATE INDEX WORKSTATION_MANUAL_TASK_QUANTITY ON KETTLE_DEV.WORKSTATION_MANUAL_TASK_DETAIL(QUANTITY) USING BTREE;
CREATE INDEX WORKSTATION_MANUAL_TASK_MONK_NUMBER ON KETTLE_DEV.WORKSTATION_MANUAL_TASK_DETAIL(MONK_NUMBER) USING BTREE;
CREATE INDEX WORKSTATION_MANUAL_TASK_ERROR_TYPE ON KETTLE_DEV.WORKSTATION_MANUAL_TASK_DETAIL(ERROR_TYPE) USING BTREE;

ALTER TABLE KETTLE_DEV.GAMIFIED_OFFER_DETAIL
    ADD COLUMN OFFER_SOURCE VARCHAR(50) NOT NULL;
ALTER TABLE KETTLE_DEV.CUSTOMER_ADDRESS_INFO
    ADD COLUMN SOURCE VARCHAR(45);
ALTER TABLE KETTLE_DEV.CUSTOMER_ADDRESS_INFO
    ADD COLUMN SOURCE_ID VARCHAR(45);
    INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (API, STATUS) VALUES ('kettle-crm.shopify-resource.signup-new-shopify-customer', 'ACTIVE');
CREATE TABLE KETTLE_STAGE.GAME_LEADER_BOARD(
                                               GAME_LEADER_BOARD_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
                                               CAMPAIGN_ID INT(11),
                                               CUSTOMER_ID INT(11),
                                               REF_CODE varchar(15),
                                               GAME_SCORE INT(11),
                                               REF_SCORE INT(11),
                                               TOTAL_SCORE INT(11),
                                               GAME_PLAY_FREQUENCY INT(11)
);



CREATE TABLE KETTLE_DEV.WALLET_BUSINESS_COST_CENTER_MAPPING (
  WALLET_BCC_MAPPING_ID int(11) NOT NULL,
  WALLET_ID int(11) NOT NULL,
  BCC_ID int(11) NOT NULL,
  BCC_NAME varchar(45) DEFAULT NULL,
  BCC_TYPE varchar(45) DEFAULT NULL,
  BCC_CODE varchar(45) DEFAULT NULL,
  MAPPING_STATUS varchar(45) DEFAULT NULL,
  ADD_TIME datetime DEFAULT NULL,
  LAST_UPDATE_TIME datetime DEFAULT NULL,
  LAST_UPDATED_BY varchar(100) DEFAULT NULL,
  WALLET_TYPE varchar(45) DEFAULT NULL,
  CAN_ALLOCATE_COST_TO_CAFES varchar(45) DEFAULT NULL,
  PRIMARY KEY (WALLET_BCC_MAPPING_ID) AUTO_INCREMENT);


ALTER TABLE KETTLE_DEV.customer_address_info
    ADD COLUMN SOURCE VARCHAR(45);
ALTER TABLE KETTLE_DEV.customer_address_info
    ADD COLUMN SOURCE_ID VARCHAR(45);

ALTER TABLE KETTLE_DEV.WALLET_DATA
ADD COLUMN CAN_ALLOCATE_COST_TO_CAFES VARCHAR(45) NULL AFTER WALLET_TYPE;
ALTER TABLE KETTLE_DEV.WALLET_DATA
ADD COLUMN ASSOCIATED_ID INTEGER(6) NULL AFTER WALLET_TYPE;


CREATE TABLE KETTLE_DEV.VOUCHER_COST_CENTER_ALLOCATION (
  VOUCHER_ALLOCATION_ID INT AUTO_INCREMENT NOT NULL,
   VOUCHER_ID INT NOT NULL,
   BUSINESS_COST_CENTER_ID INT NOT NULL,
   BUSINESS_COST_CENTER VARCHAR(255) NOT NULL,
   ALLOCATED_ISSUED_AMOUNT DECIMAL NOT NULL,
   FORCE_ALLOCATION VARCHAR(255) NOT NULL,
   ISSUED_TIME DATETIME NOT NULL,
   CONSTRAINT PK_VOUCHER_COST_CENTER_ALLOCATION PRIMARY KEY (VOUCHER_ALLOCATION_ID)
);

ALTER TABLE KETTLE_DEV.GAMIFIED_OFFER_DETAIL
    ADD COLUMN REF_CODE VARCHAR(8) ;

ALTER TABLE KETTLE_DEV.GAME_LEADER_BOARD
    ADD COLUMN USER_NAME VARCHAR(15) NULL AFTER CUSTOMER_ID;

CREATE INDEX GAME_LEADER_BOARD_REF_CODE ON KETTLE_DEV.GAMIFIED_OFFER_DETAIL(REF_CODE) USING BTREE;
CREATE INDEX GAME_LEADER_BOARD_USER_NAME ON KETTLE_DEV.GAMIFIED_OFFER_DETAIL(USER_NAME) USING BTREE;
CREATE INDEX GAMIFIED_OFFER_DETAIL_OFFER_SOURCE ON KETTLE_DEV.GAMIFIED_OFFER_DETAIL(OFFER_SOURCE) USING BTREE;


CREATE TABLE KETTLE_DEV.WALLET_BUSINESS_COST_CENTER_MAPPING (
  WALLET_BCC_MAPPING_ID int(11) NOT NULL,
  WALLET_ID int(11) NOT NULL,
  BCC_ID int(11) NOT NULL,
  BCC_NAME varchar(45) DEFAULT NULL,
  BCC_TYPE varchar(45) DEFAULT NULL,
  BCC_CODE varchar(45) DEFAULT NULL,
  MAPPING_STATUS varchar(45) DEFAULT NULL,
  ADD_TIME datetime DEFAULT NULL,
  LAST_UPDATE_TIME datetime DEFAULT NULL,
  LAST_UPDATED_BY varchar(100) DEFAULT NULL,
  WALLET_TYPE varchar(45) DEFAULT NULL,
  CAN_ALLOCATE_COST_TO_CAFES varchar(45) DEFAULT NULL,
  PRIMARY KEY (WALLET_BCC_MAPPING_ID) AUTO_INCREMENT);

ALTER TABLE KETTLE_DEV.WALLET_DATA
ADD COLUMN CAN_ALLOCATE_COST_TO_CAFES VARCHAR(45) NULL AFTER WALLET_TYPE;
ALTER TABLE KETTLE_DEV.WALLET_DATA
ADD COLUMN ASSOCIATED_ID INTEGER(6) NULL AFTER WALLET_TYPE;


CREATE TABLE KETTLE_DEV.VOUCHER_COST_CENTER_ALLOCATION (
  VOUCHER_ALLOCATION_ID INT AUTO_INCREMENT NOT NULL,
   VOUCHER_ID INT NOT NULL,
   BUSINESS_COST_CENTER_ID INT NOT NULL,
   BUSINESS_COST_CENTER VARCHAR(255) NOT NULL,
   ALLOCATED_ISSUED_AMOUNT DECIMAL NOT NULL,
   FORCE_ALLOCATION VARCHAR(255) NOT NULL,
   ISSUED_TIME DATETIME NOT NULL,
   CONSTRAINT PK_VOUCHER_COST_CENTER_ALLOCATION PRIMARY KEY (VOUCHER_ALLOCATION_ID)
);

CREATE TABLE KETTLE_DEV.CUSTOMER_DATA_LOOKUP (
  ID int(11) NOT NULL,
  CONTACT_NUMBER varchar(255) DEFAULT NULL,
  EMAIL_ID varchar(255) DEFAULT NULL,
  CONTACT_NUMBER_DATA varchar(255) DEFAULT NULL,
  EMAIL_ID_DATA varchar(255) DEFAULT NULL,
  TYPE varchar(255)  NOT NULL,
  PRIMARY KEY (ID,TYPE),
  UNIQUE KEY UK_TD_TYPE (ID,TYPE),
  KEY index_contact_number (CONTACT_NUMBER) USING BTREE,
  KEY index_email (EMAIL_ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA
(ROLE_NAME,ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Data encryption access', 'Access to encrypt data', 'ACTIVE', '5');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('ADMN_DATA_ENC_MGT', '5', 'MENU', 'SHOW', 'Admin -> Data encryption access', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Data encryption access'),
(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_DATA_ENC_MGT'), 'ACTIVE', '120057', '2022-01-03 12:00:00');

CREATE TABLE KETTLE_DEV.MONTHLY_AOV_METADATA (
  `AOV_MAPPING_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `AOV` decimal(19,2) DEFAULT NULL,
  `BRAND_ID` int(11) DEFAULT NULL,
  `CALCULATED_AT` datetime DEFAULT NULL,
  `CALCULATION_START_TIME` datetime DEFAULT NULL,
  `MONTH` int(11) DEFAULT NULL,
  `YEAR` int(11) DEFAULT NULL,
  PRIMARY KEY (`AOV_MAPPING_ID`),
  KEY `MONTHLY_AOV_METADATA_BRAND_ID` (`BRAND_ID`) USING BTREE,
  KEY `MONTHLY_AOV_METADATA_MONTH` (`MONTH`) USING BTREE,
  KEY `MONTHLY_AOV_METADATA_YEAR` (`YEAR`) USING BTREE
);





CREATE TABLE KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL (
	CALIBRATION_EVENT_ID INT(11) NOT NULL AUTO_INCREMENT,
    UNIT_ID INT(11),
    ORDER_ID INT(11),
    ORDER_ITEM_ID INT(11),
    MONK_NO INT(11),
    PROCESS_STATUS VARCHAR(50),
	CALIBRATION_STATUS VARCHAR(50),
	ORDER_TIME TIMESTAMP NULL DEFAULT NULL,
    START_TIME TIMESTAMP NULL DEFAULT NULL,
    END_TIME TIMESTAMP NULL DEFAULT NULL,
    SERVER_TIME TIMESTAMP NULL DEFAULT NULL,
    EXPECTED_QUANTITY INT(11),
    MACHINE_QUANTITY INT(11),
    ENTERED_QUANTITY INT(11),
    MILK_QUANTITY INT(11),
    TOTAL_MONKS INT(11),
    ACTIVE_MONKS INT(11),
    IN_ACTIVE_MONKS INT(11),
    CALIBRATED_MONKS INT(11),
    ACTIVE_NON_CALIB_MONKS INT(11),
    PRIMARY KEY(CALIBRATION_EVENT_ID)
);

CREATE INDEX MONK_CALIBRATION_EVENT_UNIT_ID ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(UNIT_ID) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_ORDER_ID ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(ORDER_ID) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_ORDER_ITEM_ID ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(ORDER_ITEM_ID) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_MONK_NO ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(MONK_NO) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_PROCESS_STATUS ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(PROCESS_STATUS) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_CALIBRATION_STATUS ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(CALIBRATION_STATUS) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_ORDER_TIME ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(ORDER_TIME) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_START_TIME ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(START_TIME) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_END_TIME ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(END_TIME) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_SERVER_TIME ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(SERVER_TIME) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_EXPECTED_QUANTITY ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(EXPECTED_QUANTITY) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_MACHINE_QUANTITY ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(MACHINE_QUANTITY) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_ENTERED_QUANTITY ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(ENTERED_QUANTITY) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_MILK_QUANTITY ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(MILK_QUANTITY) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_TOTAL_MONKS ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(TOTAL_MONKS) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_ACTIVE_MONKS ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(ACTIVE_MONKS) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_IN_ACTIVE_MONKS ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(IN_ACTIVE_MONKS) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_CALIBRATED_MONKS ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(CALIBRATED_MONKS) USING BTREE;
CREATE INDEX MONK_CALIBRATION_EVENT_ACTIVE_NON_CALIB_MONKS ON KETTLE_DEV.MONK_CALIBRATION_EVENT_DETAIL(ACTIVE_NON_CALIB_MONKS) USING BTREE;
ALTER TABLE KETTLE_DEV.ORDER_COMMISSION
ADD COLUMN `STATUS` VARCHAR(45) NULL DEFAULT 'ACTIVE';

ALTER TABLE KETTLE_DEV.ORDER_COMMISSION
ADD COLUMN `RESTAURANT_GROSS_BILL` DECIMAL(19,2) NULL DEFAULT 0;

ALTER TABLE KETTLE_DEV.WALLET_DATA
ADD COLUMN IFSC_CODE VARCHAR(45) NULL AFTER CAN_ALLOCATE_COST_TO_CAFES,
ADD COLUMN CARD_NUMBER VARCHAR(45) NULL AFTER IFSC_CODE,
ADD COLUMN BANK_NAME VARCHAR(45) NULL AFTER CARD_NUMBER,
ADD COLUMN CARD_HOLDER_NAME VARCHAR(45) NULL AFTER BANK_NAME;

CREATE TABLE KETTLE_DEV.WALLET_APPROVER_MAPPING(WALLET_MAPPING_ID int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
WALLET_ID INT(11),
APPROVER_ID INT(11),
APPROVER_NAME VARCHAR(255),
MAPPIING_STATUS VARCHAR(255),
ADD_TIME TIMESTAMP,
UPDATED_BY VARCHAR(255)
);

ALTER TABLE KETTLE_DEV.WALLET_APPROVER_MAPPING
ADD COLUMN UPDATED_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER UPDATED_BY;

ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED
ADD COLUMN POR_IMAGES_DOC_IDS VARCHAR(255) NULL DEFAULT NULL AFTER INVOICE_ID;

ALTER TABLE  KETTLE_SCM_DEV.REQUEST_ORDER
ADD COLUMN `SPECIALIZED_URGENT_ORDER` VARCHAR(1) NULL DEFAULT 'N' AFTER BULK_ORDER;


ALTER TABLE `KETTLE_DEV`.`ORDER_DETAIL`
ADD COLUMN `REF_ORDER_ID` INT(11) NULL AFTER `COLLECTION_AMOUNT`;

ALTER TABLE `KETTLE_DEV`.`ORDER_DETAIL`
ADD COLUMN `SOURCE_VERSION` VARCHAR(10) NULL AFTER `REF_ORDER_ID`;

ALTER TABLE `KETTLE_DEV`.`LOYALTY_EVENTS`
ADD COLUMN `REASON` VARCHAR(200) NULL DEFAULT NULL;


ALTER TABLE `KETTLE_DEV`.`ORDER_ITEM`
ADD COLUMN `ORDER_ITEM_REMARK` VARCHAR(1000) NULL AFTER `SOURCE_SUB_CATEGORY`;
