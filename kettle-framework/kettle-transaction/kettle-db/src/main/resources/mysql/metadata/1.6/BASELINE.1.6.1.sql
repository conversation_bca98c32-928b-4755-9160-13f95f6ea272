CREATE TABLE WORKSTATION_LOG(
WORKSTATION_LOG_ID INT AUTO_INCREMENT PRIMARY KEY,
ORDER_ID INT NOT NULL,
ORDER_ITEM_ID BIGINT NOT NULL,
BILL_CREATION_TIME BIGINT,
UNIT_ID INT NOT NULL,
`TYPE` VARCHAR(30) NOT NULL,
<PERSON><PERSON><PERSON>OYEE_ID INT NOT NULL,
PRODUCT_ID INT NOT NULL,
ITEM_QUANTITY INT NOT NULL,
DIMENSION VARCHAR(30) NOT NULL,
ORDER_SOURCE VARCHAR(30),
TIME_TO_ACKNOWLEDGE INT,
TIME_TO_START INT,
TIME_TO_PROCESS INT,
TIME_TO_DISPATCH INT,
TIME_TO_PROCESS_BY_MACHINE INT,
COOKTOP_STATION INT,
STATION_TASK_FOR_ORDER INT,
IS_DISPATCHED boolean,
<PERSON><PERSON><PERSON>LED boolean);

CREATE TABLE ASSEMBLY_LOG_DATA(
ASSEMBLY_LOG_DATA_ID INT AUTO_INCREMENT PRIMARY KEY,
BILLING_SERVER_TIME BIGINT,
UNIT_ID INT NOT NULL,
ORDER_SOURCE VARCHAR(30),
CHANNEL_PARTNER VARCHAR(50),
DELIVERY_PARTNER VARCHAR(50),
TIME_TO_ACKNOWLEDGE INT,
TIME_TO_PROCESS_HOT INT,
TIME_TO_PROCESS_COLD INT,
TIME_TO_PROCESS_FOOD INT,
TIME_TO_PROCESS_BY_WORKSTATIONS INT,
TIME_TO_READY_FOR_DISPATCH INT,
TIME_TO_DISPATCH INT,
COOKTOP_STATION INT,
STATION_EVENTS_FOR_ORDER INT);