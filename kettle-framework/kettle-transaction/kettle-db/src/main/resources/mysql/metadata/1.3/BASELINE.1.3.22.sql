/*
 * QUERIES_FOR_CREATING_OFFER_RELATED_TABLES
 */

DROP TABLE IF EXISTS OFFER_DETAIL_DATA;
CREATE TABLE OFFER_DETAIL_DATA(
OFFER_DETAIL_ID INTEGER NOT NULL AUTO_INCREMENT,
OFFER_CATEGORY VARCHAR(30) NOT NULL,
OFFER_TYPE VARCHAR(30) NOT NULL,
OFFER_TEXT VARCHAR(500) NOT NULL,
OFFER_DESCRIPTION VARCHAR(1000) NOT NULL,
START_DATE DATE NOT NULL,
END_DATE  DATE NOT NULL,
OFFER_STATUS VARCHAR(15) NOT NULL,
MIN_VALUE INTEGER NOT NULL DEFAULT 0,
VALIDATE_CUSTOMER VARCHAR(1) NOT NULL DEFAULT 'Y',
INCLUDE_TAXES VARCHAR(1) NOT NULL DEFAULT 'N',
PRIORITY INTEGER,
OFFER_SCOPE VARCHAR(10) NOT NULL DEFAULT 'MASS',
MIN_ITEM_COUNT INTEGER NOT NULL DEFAULT 1,
QUANTITY_LIMIT INT NOT NULL DEFAULT 1,
LOYALTY_LIMIT INT NOT NULL DEFAULT 0,
OFFER_VALUE INT NOT NULL DEFAULT 0,
PRIMARY KEY (OFFER_DETAIL_ID)
);

DROP TABLE IF EXISTS COUPON_DETAIL_DATA;
CREATE TABLE COUPON_DETAIL_DATA(
COUPON_DETAIL_ID INTEGER NOT NULL AUTO_INCREMENT,
OFFER_DETAIL_ID INTEGER NOT NULL,
COUPON_CODE VARCHAR(20) NOT NULL,
START_DATE DATE NOT NULL,
END_DATE  DATE NOT NULL,
COUPON_REUSE VARCHAR(1) NOT NULL,
CUSTOMER_REUSE VARCHAR(1) NOT NULL,
MAX_USAGE INTEGER NULL,
COUPON_STATUS VARCHAR(15) NOT NULL DEFAULT 'INACTIVE',
USAGE_COUNT INTEGER NOT NULL DEFAULT 0,
MANUAL_OVERRIDE VARCHAR(1) NOT NULL DEFAULT 'N',
PRIMARY KEY (COUPON_DETAIL_ID)
);

DROP TABLE IF EXISTS COUPON_DETAIL_MAPPING_DATA;
CREATE TABLE COUPON_DETAIL_MAPPING_DATA(
COUPON_DETAIL_MAPPING_ID INTEGER NOT NULL AUTO_INCREMENT,
COUPON_DETAIL_ID INTEGER NOT NULL,
MAPPING_TYPE VARCHAR(50) NOT NULL,
MAPPING_VALUE VARCHAR(100) NOT NULL,
MAPPING_DATA_TYPE VARCHAR(100) NOT NULL,
MIN_VALUE VARCHAR(10) NOT NULL DEFAULT '1',
MAPPING_GROUP INTEGER NOT NULL DEFAULT 1,
PRIMARY KEY(COUPON_DETAIL_MAPPING_ID)
);

DROP TABLE IF EXISTS COUPON_CODE_USAGE_DATA;
CREATE TABLE COUPON_CODE_USAGE_DATA(
COUPON_CODE_USAGE_ID INTEGER NOT NULL AUTO_INCREMENT,
COUPON_DETAIL_ID INTEGER NOT NULL,
ORDER_ID INTEGER NOT NULL,
CUSTOMER_ID INTEGER NOT NULL,
DISCOUNT_AMOUNT DECIMAL(10,2) NULL,
COMPLIMENTARY_PRODUCT_ID INTEGER NULL,
COMPLIMENTARY_PRODUCT_QUANTITY INTEGER NULL,
ADD_TIME TIMESTAMP NOT NULL,
PRIMARY KEY(COUPON_CODE_USAGE_ID)
);

DROP TABLE IF EXISTS MARKETING_PARTNER;
CREATE TABLE MARKETING_PARTNER(
PARTNER_ID INTEGER NOT NULL AUTO_INCREMENT,
PARTNER_NAME VARCHAR(30) NOT NULL DEFAULT 'CHAAYOS',
PARTNER_TYPE VARCHAR(30) NOT NULL DEFAULT 'INTERNAL',
STATUS VARCHAR(10) NOT NULL DEFAULT 'ACTIVE',
PRIMARY KEY (PARTNER_ID)
);

DROP TABLE IF EXISTS OFFER_PARTNERS;
CREATE TABLE OFFER_PARTNERS(
MAPPING_ID INTEGER NOT NULL AUTO_INCREMENT,
PARTNER_ID INTEGER NOT NULL,
OFFER_ID INTEGER NOT NULL,
PRIMARY KEY (MAPPING_ID)
);

DROP TABLE IF EXISTS OFFER_METADATA;
CREATE TABLE OFFER_METADATA(
ID INTEGER NOT NULL AUTO_INCREMENT,
OFFER_ID INTEGER NOT NULL,
MAPPING_TYPE VARCHAR(30) NOT NULL,
MAPPING_VALUE VARCHAR(30) NOT NULL,
PRIMARY KEY (ID)
);



#ADDING_COLUMN_TO_ORDER_DETAIL_FOR_STORING_COUPON_CODE 
ALTER TABLE ORDER_DETAIL
ADD COLUMN OFFER_CODE VARCHAR(30);

#ADDING_COLUMN_TO_ORDER_DETAIL_FOR_STORING_SAVING_AMOUNT
ALTER TABLE ORDER_DETAIL
ADD COLUMN SAVING_AMOUNT DECIMAL(10,2);

#ADDING_COLUMN_TO_ORDER_ITEM_FOR_STORING_PROMOTIONAL_DISCOUNT
ALTER TABLE ORDER_ITEM
ADD COLUMN PROMOTIONAL_DISCOUNT DECIMAL(10,2);

UPDATE PAYMENT_MODE 
SET MODE_DESCRIPTION='Cash' 
WHERE PAYMENT_MODE_ID='1';
