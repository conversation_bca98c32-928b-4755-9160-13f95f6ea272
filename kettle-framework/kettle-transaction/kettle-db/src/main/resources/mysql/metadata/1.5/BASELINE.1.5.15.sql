#Coupons created in production as on 25-3-2016 for partners and chaayos employees

INSERT INTO OFFER_DETAIL_DATA ( 
								OFFER_CATEGORY, 
								OFFER_TYPE, 
								OFFER_TEXT, 
								OFFER_DESCRIPTION, 
								START_DATE, 
								END_DATE, 
								OFFER_STATUS, 
								MIN_VALUE, 
								VALIDATE_CUSTOMER, 
								INCLUDE_TAXES, 
								PR<PERSON>RITY,
								OFFER_SCOPE,
								MIN_ITEM_COUNT,
								QUANTITY_LIMIT,
								LOYALTY_LIMIT,
								OFFER_VALUE)
VALUES ( 
		'BILL', 
		'PERCENTAGE_BILL_STRATEGY', 
		'Awfis Space Solutions - 10% OFF', 
		'10% off on order for Awfis Space Solutions employees', 
		'2016-03-22',
		'2031-03-22', 
		'ACTIVE', 
		'0', 
		'Y', 
		'Y', 
		'0', 
		'INTERNAL', 
		'1', 
		'1',
		'0', 
		'10');


INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, 
								COUPON_CODE,
								START_DATE, 
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
SELECT OFFER_DETAIL_ID, 
		'AWD10',
		START_DATE, 
		END_DATE,
		'Y',
		'Y',
		'1000000', 
		'ACTIVE',
		'0', 
		'N' FROM OFFER_DETAIL_DATA WHERE OFFER_TEXT = 'Awfis Space Solutions - 10% OFF';


INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP,
										DIMENSION)
SELECT 	COUPON_DETAIL_ID,
		'UNIT_REGION',
		'MUMBAI',
		'java.lang.String',
		'1',
		'1',
		null
FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'AWD10';






INSERT INTO OFFER_DETAIL_DATA ( 
								OFFER_CATEGORY, 
								OFFER_TYPE, 
								OFFER_TEXT, 
								OFFER_DESCRIPTION, 
								START_DATE, 
								END_DATE, 
								OFFER_STATUS, 
								MIN_VALUE, 
								VALIDATE_CUSTOMER, 
								INCLUDE_TAXES, 
								PRIORITY,
								OFFER_SCOPE,
								MIN_ITEM_COUNT,
								QUANTITY_LIMIT,
								LOYALTY_LIMIT,
								OFFER_VALUE)
VALUES ( 
		'BILL', 
		'PERCENTAGE_BILL_STRATEGY', 
		'I Share Space - 10% OFF', 
		'10% off on order for I Share Space employees', 
		'2016-03-22',
		'2031-03-22', 
		'ACTIVE', 
		'0', 
		'Y', 
		'Y', 
		'0', 
		'INTERNAL', 
		'1', 
		'1',
		'0', 
		'10');


INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID, 
								COUPON_CODE,
								START_DATE, 
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
SELECT OFFER_DETAIL_ID, 
		'ISSD10',
		START_DATE, 
		END_DATE,
		'Y',
		'Y',
		'1000000', 
		'ACTIVE',
		'0', 
		'N' FROM OFFER_DETAIL_DATA WHERE OFFER_TEXT = 'I Share Space - 10% OFF';


INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP,
										DIMENSION)
SELECT 	COUPON_DETAIL_ID,
		'UNIT_REGION',
		'MUMBAI',
		'java.lang.String',
		'1',
		'1',
		null
FROM COUPON_DETAIL_DATA WHERE COUPON_CODE = 'ISSD10';





