#QUERY_TO_GET_FOREIGN_KEY_NAME
SELECT 
    * 
FROM
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE
    TABLE_NAME = 'ORDER_ITEM'
        AND REFERENCED_TABLE_NAME = 'REF_LOOKUP'
        AND COLUMN_NAME = 'COMPLIMENTARY_TYPE_ID';

#!!!WARNING!!!
#!!!QUERY_TO_DROP_FOREIGN_KEY!!!
ALTER TABLE ORDER_ITEM
DROP FOREIGN KEY USE_CONSTRAINT_NAME_FROM_ABOVE_QUERY;


#!!!WARNING!!!
#!!!QUERY_TO_ADD_FOREIGN_KEY!!!
ALTER TABLE ORDER_ITEM
ADD
    FOREIGN KEY FK_ORDER_ITEM_COMPLEMENTARY_CODE(COMPLIMENTARY_TYPE_ID)
	REFERENCES COMPLIMENTARY_CODE(COMP_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE;

SELECT 
    *
FROM
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE
    TABLE_NAME = 'ORDER_ITEM'
        AND REFERENCED_TABLE_NAME = 'COMPLIMENTARY_CODE'
        AND COLUMN_NAME = 'COMPLIMENTARY_TYPE_ID';
        

        
#QUERY_TO_ADD_NEW_SETTLEMENT_TYPES 
INSERT INTO PAYMENT_MODE (`PAYMENT_MODE_ID`, `MODE_NAME`, `MODE_TYPE`, `MODE_DESCRIPTION`, `SETTLEMENT_TYPE`) VALUES ('7', 'SodexoCard', 'CARD', 'Sodexo Card', 'CREDIT');
INSERT INTO PAYMENT_MODE (`PAYMENT_MODE_ID`, `MODE_NAME`, `MODE_TYPE`, `MODE_DESCRIPTION`, `SETTLEMENT_TYPE`) VALUES ('8', 'TicketRestaurantCard', 'CARD', 'Ticket Restaurant Card', 'CREDIT');
UPDATE PAYMENT_MODE SET `MODE_DESCRIPTION`='Ticket Restaurant Coupon' WHERE `PAYMENT_MODE_ID`='5';


