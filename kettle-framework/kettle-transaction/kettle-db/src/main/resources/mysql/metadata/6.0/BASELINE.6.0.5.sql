ALTER TABLE KETTLE_DEV.CUSTOMER_INFO 
ADD COLUMN IS_INTERNAL VARCHAR(1) NULL;

UPDATE KETTLE_DEV.CUSTOMER_INFO 
SET IS_INTERNAL = 'N';

CREATE INDEX CUSTOMER_INFO_IS_INTERNAL ON KETTLE_DEV.CUSTOMER_INFO(IS_INTERNAL) USING BTREE;


ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL ADD COLUMN EMPLOYEE_MEAL_GMV DECIMAL(10,6);

ALTER TABLE KETTLE_DEV.MENU_PRODUCT_COST_DETAIL ADD COLUMN QUANTITY INTEGER;
ALTER TABLE KETTLE_DEV.MENU_PRODUCT_COST_DETAIL ADD COLUMN PRICE DECIMAL(10,2);


ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN GIFT_CARD_OFFER  DECIMAL(10,2) NULL;
