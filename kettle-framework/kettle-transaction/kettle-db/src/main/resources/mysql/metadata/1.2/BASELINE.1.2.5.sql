INSERT INTO PRODUCT_DETAIL (PRODUCT_ID, PRODUCT_NAME, PRODUCT_DESCRIPTION, VENDOR_ID, PRODUCT_TYPE, PRODUCT_SUB_TYPE, PRODUCT_STATUS, ATTRIBUTE, PRODUCT_START_DATE, PRODUCT_END_DATE, PRODUCT_SKU_CODE, DIMENSION_CODE, PRICE_TYPE, ADDITIONAL_ITEM_TYPES, IN_TMSTMP, OUT_TMSTMP)
VALUES
(631,'Chaayos Special','Staple Street Food Of Bombay!',100,7,703,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011401',1,'NET_PRICE',26,'2015-10-24 00:00:00','2030-12-01 00:00:00'),
(641,'Bun Maska Keema','Melt-in Mouth Mutton Keema In A Homemade Pav',100,7,703,'ACTIVE','NON_VEG','2013-01-01','9999-12-01','CH1011402',1,'NET_PRICE',26,'2015-10-24 00:00:00','2030-12-01 00:00:00'),
(671,'Bun Maska Vada','A Bombay Favorite-Spicy Potato Vada With Hari Chutney And Lasun Chutney In A Homemade Pav',100,7,703,'ACTIVE','VEG','2013-01-01','9999-12-01','CH1011403',1,'NET_PRICE',26,'2015-10-24 00:00:00','2030-12-01 00:00:00');

update UNIT_PRODUCT_MAPPING
set PRODUCT_ID = 631
where UNIT_ID IN (10009,10010) and PRODUCT_ID = 630;

update UNIT_PRODUCT_MAPPING
set PRODUCT_ID = 641
where UNIT_ID IN (10009,10010) and PRODUCT_ID = 640;

update UNIT_PRODUCT_MAPPING
set PRODUCT_ID = 671
where UNIT_ID IN (10009,10010) and PRODUCT_ID = 670;

DELETE FROM EMPLOYEE_PASS_CODE WHERE `EMP_ID`='100027';
DELETE FROM EMPLOYEE_PASS_CODE WHERE `EMP_ID`='100028';
DELETE FROM EMPLOYEE_PASS_CODE WHERE `EMP_ID`='100029';


DELETE FROM EMPLOYEE_UNIT_MAPPING WHERE `EMP_ID`='100027';
DELETE FROM EMPLOYEE_UNIT_MAPPING WHERE `EMP_ID`='100028';
DELETE FROM EMPLOYEE_UNIT_MAPPING WHERE `EMP_ID`='100029';

UPDATE EMPLOYEE_DETAIL SET `REPORTING_MANAGER_ID`=null WHERE `EMP_ID`='100027';
UPDATE EMPLOYEE_DETAIL SET `REPORTING_MANAGER_ID`=null WHERE `EMP_ID`='100028';
UPDATE EMPLOYEE_DETAIL SET `REPORTING_MANAGER_ID`=null WHERE `EMP_ID`='100029';
DELETE FROM EMPLOYEE_DETAIL WHERE `EMP_ID`='100027';
DELETE FROM EMPLOYEE_DETAIL WHERE `EMP_ID`='100028';
DELETE FROM EMPLOYEE_DETAIL WHERE `EMP_ID`='100029';

DELETE FROM ADDRESS_INFO WHERE `ADDRESS_ID`='10031';
DELETE FROM ADDRESS_INFO WHERE `ADDRESS_ID`='10032';
DELETE FROM ADDRESS_INFO WHERE `ADDRESS_ID`='10033';
DELETE FROM ADDRESS_INFO WHERE `ADDRESS_ID`='10034';

INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10031', 'FLAT NO-412 C', 'GULMOHAR GREENS,MOHAN NAGAR', 'GHAZIABAD', 'UTTAR PRADESH', 'India', '201007', '+91-9971531030', '+91-9971531030', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10032', 'F-1 FINE HOME APARTMENTS', 'MAYUR VIHAR PHASE-1', 'New Delhi', 'New Delhi', 'India', '110091', '+91-9718220625', '+91-9718220625', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10033', 'House NO.-92A', 'yushuf sarai,opp.- Gautam nagar', 'New Delhi', 'New Delhi', 'India', '110016', '+91-9953959775', '+91-8527928259', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10034', 'k-99,khalivadi', 'Near RML hospital', 'New Delhi', 'New Delhi', 'India', '110001', '+91-7042583608', '+91-9810276546', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10035', 'G-15/9', 'Sector -15,Rohini', 'New Delhi', 'New Delhi', 'India', '110089', '+91-8010260357', '+91-8010260357', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10036', 'W-72/12,Tali Walan', 'Gali NO-10, Anand Parbat Indl.Area', 'New Delhi', 'New Delhi', 'India', '110005', '+91-8860009676', '+91-8860009676', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10037', 'C-6084,Sector-12', 'Raja Ji Puram', 'Lucknow', 'Uttar Pradesh', 'India', '226001', '+91-9643471589', '+91-9643471589', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10038', 'F-147,1st Floor,Sudershan Park', 'Moti Nagar,Raja Ji Puram', 'New Delhi', 'New Delhi', 'India', '110015', '+91-9899876368', '+91-9899819130', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10039', 'C-160,3rd Floor New Moti Nagar', 'Nr Bala ji Temple &Park', 'New Delhi', 'New Delhi', 'India', '110015', '+91-9899876368', '+91-9899819130', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10040', 'A block HNO:575', 'Gali No:11 Part-1 Mukand pur', 'New Delhi', 'New Delhi', 'India', '110042', '+91-9971080368', '+91-9971080368', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10041', '465,', 'Udyog Vihar Phase 5', 'Gurgaon', 'Haryana', 'India', '122016', '+91-3333333333', '+91-3333333333', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10042', 'Room No.-63', 'Rahima Bai Bagh , Opp.Khoja Khana Mahim', 'Mumbai', 'Maharshtra', 'India', '400016', '+91-9769898134', '+91-9773943376', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10043', 'Bhagat Singh Nagar, No.-1 Linking ', 'Goregan-west , Near Renuka Mata Mandir', 'Mumbai', 'Maharashtra', 'India', '400104', '+91-9833092216', '+91-9704530725', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10044', 'Bldg No.9,Flat No.201', 'Kailash Nagar,Vadavali', 'Mumbai', 'Maharashtra', 'India', '421501', '+91-9893598230', '+91-9893596077', 'RESIDENTIAL');

INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100027', 'Manish Sharma', 'M', '10031', '10031', '+91-9971531030', '+91-9971531030', '101', '1003', 'FULL_TIME', 'ACTIVE', '2015-09-10', '9999-12-01', '100012');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100028', 'Dhruv Sharma', 'M', '10032', '10032', '+91-9718220625', '+91-9718220625', '101', '1003', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100012');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100029', 'Nishant Chaudhary', 'M', '10033', '10033', '+91-9953959775', '+91-8527928259', '101', '1003', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100002');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100030', 'Updesh Singh', 'M', '10034', '10034', '+91-7042583608', '+91-9810276546', '101', '1003', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100002');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100031', 'Yogesh Sharma', 'M', '10035', '10035', '+91-8010260357', '+91-8010260357', '101', '1003', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100007');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100032', 'Nirmal Singh', 'M', '10036', '10036', '+91-8860009676', '+91-8860009676', '101', '1003', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100007');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100033', 'Shruti Mishra', 'F', '10037', '10037', '+91-9643471589', '+91-9643471589', '101', '1003', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100007');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100034', 'Bhawna', 'F', '10038', '10039', '+91-9899876368', '+91-9899819130', '101', '1003', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100008');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100035', 'Krishna Maurya', 'M', '10040', '10040', '+91-9971080368', '+91-9971080368', '101', '1003', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100008');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100036', 'Training 1', 'M', '10041', '10041', '+91-3333333333', '+91-3333333333', '101', '1003', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100000');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100037', 'Vineeth Nair', 'M', '10043', '10043', '+91-9893598230', '+91-9893596077', '101', '1002', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100000');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100038', 'Nilesh', 'M', '10042', '10042', '+91-9769898134', '+91-9773943376', '101', '1002', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100037');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100039', 'Jai Prakash', 'M', '10044', '10044', '+91-9833092216', '+91-9704530725', '101', '1002', 'FULL_TIME', 'ACTIVE', '2015-09-21', '9999-12-01', '100037');


INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('74', '100027', '10004');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('75', '100028', '10002');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('76', '100029', '10000');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('77', '100030', '10003');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('78', '100031', '10006');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('79', '100032', '10006');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('80', '100033', '10008');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('81', '100034', '10007');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('82', '100035', '10007');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('83', '100037', '10009');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('84', '100037', '10010');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('85', '100038', '10009');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('86', '100039', '10010');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('87', '100036', '10000');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('88', '100036', '10001');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('89', '100036', '10002');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('90', '100036', '10003');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('91', '100036', '10004');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('92', '100036', '10005');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('93', '100036', '10006');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('94', '100036', '10007');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('95', '100036', '10008');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('96', '100036', '10009');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('97', '100036', '10010');
INSERT INTO EMPLOYEE_UNIT_MAPPING (`EMP_UNIT_KEY_ID`, `EMP_ID`, `UNIT_ID`) VALUES ('98', '100036', '10011');

INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
VALUES
(100027,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100028,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100029,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100030,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100031,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100032,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100033,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100034,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100035,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100036,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100036,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100037,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100038,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100039,'0UxQYmqlaaaY1jDVAZWNQQ==' );

DELETE FROM EMPLOYEE_PASS_CODE WHERE `EMP_ID`='100036' and `EMP_SURROGATE_ID`='43';

INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('10045', 'House No. E-119', 'Aali Village, Badarpur', 'New Delhi', 'New Delhi', 'India', '110076', '+91-9891270596', '+91-9891270596', 'RESIDENTIAL');

INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('100040', 'Satyajeet Mandal', 'M', '10045', '10045', '+91-9891270596', '+91-9891270596', '101', '1003', 'FULL_TIME', 'ACTIVE', '2015-10-05', '9999-12-01', '100012');

INSERT INTO EMPLOYEE_UNIT_MAPPING ( `EMP_ID`, `UNIT_ID`) VALUES ('100040', '10002');

INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
VALUES
(100040,'0UxQYmqlaaaY1jDVAZWNQQ==' );

