
ALTER TABLE ORDER_DETAIL 
ADD COLUMN PRINT_COUNT INTEGER NOT NULL DEFAULT 1;

CREATE TABLE ORDER_RE_PRINT_DETAIL (
ORDER_PRINT_ID INTEGER NOT NULL AUTO_INCREMENT,
ORDER_ID INT NOT NULL,
PRINT_REASON VARCHAR(200) NOT NULL,
GENERATED_BY INTEGER NOT NULL,
APPROVED_BY INTEGER NOT NULL,
REPRINT_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
PRIMARY KEY (ORDER_PRINT_ID)
);

ALTER TABLE REF_LOOKUP
ADD COLUMN RL_SHORT_CODE VARCHAR(4) NULL;

ALTER TABLE ORDER_DETAIL
ADD COLUMN BILL_CANCELLATION_TIME TIMESTAMP NULL;

ALTER TABLE ORDER_DETAIL
ADD COLUMN CANCELLED_BY INTEGER NULL;

ALTER TABLE ORDER_DETAIL
ADD COLUMN CANCEL_APPROVED_BY INTEGER NULL;

ALTER TABLE REF_LOOKUP
ADD COLUMN RL_STATUS VARCHAR(10) NOT NULL DEFAULT 'ACTIVE';

ALTER TABLE LOYALTY_EVENTS MODIFY TRANSACTION_CODE_TYPE VARCHAR(100);

