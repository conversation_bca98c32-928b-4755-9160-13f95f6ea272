SET GLOBAL event_scheduler = ON;

DELIMITER $$

CREATE
   EVENT `RUN_CLM_LOAD_PROC`
   ON SCHEDULE EVERY 1 DAY STARTS '2017-02-01 04:00:00' ON COMPLETION PRESERVE ENABLE
   DO BEGIN
   
		call KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA();
		call KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_90_DAYS();
		call KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_30_DAYS();
		call KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_15_DAYS();
		call KETTLE_DUMP.SP_CALCULATE_CUSTOMER_DATA_FOR_LAST_7_DAYS();
       
   END $$

DELIMITER ;
