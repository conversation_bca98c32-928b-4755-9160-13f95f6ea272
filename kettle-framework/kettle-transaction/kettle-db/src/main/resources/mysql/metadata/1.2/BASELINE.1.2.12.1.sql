DELETE FROM UNIT_PRODUCT_PRICING
WHERE UNIT_PROD_REF_ID IN (select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING where UNIT_ID IN (11001,12001,12002,12003,12004,12005,12006,12007,12008,12009));

DELETE FROM UNIT_PRODUCT_MAPPING where UNIT_ID IN (11001,12001,12002,12003,12004,12005,12006,12007,12008,12009);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 11001,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 10006
and PRODUCT_ID  in (10,50,60,80,130,300,310,320,640,650,670,680,740,750,760,770,780,790,800,810);


update PRODUCT_DETA<PERSON> set IS_INVENTORY_TRACKED = 'Y' where PRODUCT_ID IN (10,50,60,80,130,300,310,320,640,650,670,680,740,750,760,770,780,790,800,810)
and PRODUCT_TYPE IN (7,10);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 10),23,'2015-01-01 00:00:00',84),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 10),24,'2015-01-01 00:00:00',210),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 50),23,'2015-01-01 00:00:00',84),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 50),24,'2015-01-01 00:00:00',210),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 60),23,'2015-01-01 00:00:00',158),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 60),24,'2015-01-01 00:00:00',395),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 80),1102,'2015-01-01 00:00:00',138),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 80),1103,'2015-01-01 00:00:00',345),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 130),23,'2015-01-01 00:00:00',198),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 130),24,'2015-01-01 00:00:00',495),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 300),30,'2015-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 310),30,'2015-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 320),30,'2015-01-01 00:00:00',95),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 640),1,'2015-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 650),1,'2015-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 670),1,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 680),1,'2015-01-01 00:00:00',59),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 740),1,'2015-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 750),1,'2015-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 760),1,'2015-01-01 00:00:00',109),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 770),1,'2015-01-01 00:00:00',89),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 780),1,'2015-01-01 00:00:00',20),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 790),1,'2015-01-01 00:00:00',39),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 800),1,'2015-01-01 00:00:00',45),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 11001 AND PRODUCT_ID = 810),1,'2015-01-01 00:00:00',55);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12001,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE 
from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12001;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12002,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12002;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12003,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12003;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12004,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12004;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12005,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12005;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12006,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12006;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12007,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12007;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12008,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12008;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 12009,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 11001;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 11001
and upm1.UNIT_ID = 12009;

