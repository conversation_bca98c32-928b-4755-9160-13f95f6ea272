ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN CONSUMABLE_UTILITY_TAX  DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN CONSUMABLE_STATIONARY_TAX  DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN CONSUMABLE_UNIFORM_TAX  DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN CONSUMABLE_EQUIPMENT_TAX  DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN CONSUMABLE_CUTLERY_TAX  DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN CONSUMABLE_DISPOSABLE_TAX  DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.CASH_CARD_OFFER ADD COLUMN PARTNER_ID INT NULL;
UPDATE KETTLE_DEV.CASH_CARD_OFFER SET PARTNER_ID = 1 WHERE PARTNER_ID IS NULL;