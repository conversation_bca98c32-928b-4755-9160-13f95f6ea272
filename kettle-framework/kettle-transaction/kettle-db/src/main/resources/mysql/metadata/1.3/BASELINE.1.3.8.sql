INSERT INTO DELIVERY_PARTNER(PARTNER_ID,PARTNER_CODE, PARTNER_DISPLAY_NAME)
VALUES (6, 'DELHIVERY','Delhivery');

DROP TABLE if exists DELIVERY_DETAIL;
CREATE TABLE DELIVERY_DETAIL (
	ID INTEGER NOT NULL AUTO_INCREMENT,
    DELIVERY_PARTNER_ID INTEGER NOT NULL,
    ORDER_ID INTEGER NOT NULL,
    DELIVERY_TASK_ID VARCHAR(30) NOT NULL,
    DELIVERY_STATUS VARCHAR(30) NOT NULL,
    STATUS_UPDATE_TMSTMP TIMESTAMP NOT NULL,
    DELIVERY_BOY_NAME VARCHAR(50),
    DELIVERY_BOY_PHONE_NUM VARCHAR(50),
    PRIMARY KEY(ID)
);

DROP TABLE if exists DELIVERY_API_MAPPINGS;

DROP TABLE if exists PARTNER_ATTRIBUTES;
CREATE TABLE PARTNER_ATTRIBUTES(
 ID INTEGER NOT NULL AUTO_INCREMENT,
 PARTNER_ID INTEGER NOT NULL,
 MAPPING_TYPE VARCHAR(30),
 MAPPING_VALUE VARCHAR(100),
 PARTNER_TYPE VARCHAR(20),
 PRIMARY KEY(ID)
);


DROP TABLE if exists DELIVERY_STATUS_EVENT;
CREATE TABLE DELIVERY_STATUS_EVENT (
	ID INTEGER NOT NULL AUTO_INCREMENT,
    DELIVERY_PARTNER_ID INTEGER NOT NULL,
    ORDER_ID INTEGER NOT NULL,
    DELIVERY_TASK_ID VARCHAR(30),
    DELIVERY_FROM_STATUS VARCHAR(30) ,
	DELIVERY_TO_STATUS VARCHAR(30) ,
    STATUS_UPDATE_TMSTMP TIMESTAMP ,
    STATUS_START_TMSTMP TIMESTAMP,
    TRANSISTION_STATUS VARCHAR(15),
    PRIMARY KEY(ID)
);

DROP TABLE if exists UNIT_TO_DELIVERY_MAPPINGS;
CREATE TABLE UNIT_TO_DELIVERY_MAPPINGS (
	ID INTEGER NOT NULL AUTO_INCREMENT,
	UNIT_ID INTEGER NOT NULL,
    DELIVERY_PARTNER_ID INTEGER NOT NULL,
    PRIORITY INTEGER NOT NULL,
    PRIMARY KEY(ID)
);

INSERT INTO PARTNER_ATTRIBUTES VALUES (1,6,'REQUEST_OBJECT','com.stpl.tech.kettle.delivery.model.HLDRequest','DELIVERY'),
(2,6,'REQUEST_OBJECT_ADAPTER','com.stpl.tech.kettle.delivery.adapter.HLDRequestAdapter','DELIVERY'),
(3,3,'REQUEST_OBJECT','com.stpl.tech.kettle.delivery.model.SFXRequest','DELIVERY'),
(4,3,'REQUEST_OBJECT_ADAPTER','com.stpl.tech.kettle.delivery.adapter.SFXRequestAdapter','DELIVERY'),
(5,4,'REQUEST_OBJECT','com.stpl.tech.kettle.delivery.model.OPNRequest','DELIVERY'),
(6,4,'REQUEST_OBJECT_ADAPTER','com.stpl.tech.kettle.delivery.adapter.OPNRequestAdapter','DELIVERY'),
(7,6,'EXECUTOR','com.stpl.tech.kettle.delivery.strategy.HLDExecutor','DELIVERY'),
(8,6,'RESPONSE_OBJECT','com.stpl.tech.kettle.delivery.adapter.HLDResponse','DELIVERY'),
(9,6,'RESPONSE_OBJECT_ADAPTER','com.stpl.kettle.delivery.adapter.HLDResponseAdapter','DELIVERY'),
(10,6,'AUTHORIZATION_TOKEN','9fd2da55-6c45-44cd-984b-350f61e84d0c','DELIVERY'),
(11,6,'AUTHORIZATION_TOKEN_SECRET',NULL,'DELIVERY'),
(12,3,'AUTHORIZATION_TOKEN','Token 1d5af5d04979d13b1e1f49c7e7d8e6c8040c1db4','DELIVERY'),
(13,3,'AUTHORIZATION_TOKEN_SECRET',NULL,'DELIVERY'),
(14,4,'AUTHORIZATION_TOKEN','TXLins52n3NGzpoxak','DELIVERY'),
(15,4,'AUTHORIZATION_TOKEN_SECRET','XOJsal934jOKJjnscf','DELIVERY'),
(16,4,'EXECUTOR','com.stpl.tech.kettle.delivery.strategy.OPNExecutor','DELIVERY'),
(17,3,'EXECUTOR','com.stpl.tech.kettle.delivery.strategy.SFXExecutor','DELIVERY'),
(18,3,'CREATE_ENDPOINT','http://api.shadowfax.in/api/v1/stores/orders/','DELIVERY'),
(19,4,'CREATE_ENDPOINT','/api/v1/orders','DELIVERY'),
(20,6,'CREATE_ENDPOINT','https://yaocm9m1dh.execute-api.us-east-1.amazonaws.com/test/order','DELIVERY'),
(21,4,'REGISTER_MERCHANT','/api/v1/merchants','DELIVERY'),
(22,3,'CANCEL_ENDPOINT','http://api.shadowfax.in/api/v1/stores/orders/<sfx_order_id>/status/','DELIVERY'),
(23,6,'CANCEL_ENDPOINT','https://yaocm9m1dh.execute-api.us-east-1.amazonaws.com/test/order/cancel?order_id=<order_id>','DELIVERY'),
(24, 3, 'CALLBACK_TOKEN', 'B326yxW0FJXGxa2fE1r1+A==', 'DELIVERY'),
(25, 4, 'CALLBACK_TOKEN', 'G7wiLl8rQXahOgVCcNTOpA==', 'DELIVERY'),
(26, 6, 'CALLBACK_TOKEN', 'A+PxNwcbyYXaSWy6fqw7gw==', 'DELIVERY'),
(27, 4, 'CANCEL_ENDPOINT', '/api/v1/orders/<order_code>', 'DELIVERY'),
(28, 6, 'REGISTER_MERCHANT', 'http://merchant.delhivery.com/merchant/', 'DELIVERY');


INSERT INTO UNIT_TO_DELIVERY_MAPPINGS (UNIT_ID,DELIVERY_PARTNER_ID,PRIORITY) (SELECT UNIT_ID,4,0 FROM UNIT_DETAIL);
INSERT INTO UNIT_TO_DELIVERY_MAPPINGS (UNIT_ID,DELIVERY_PARTNER_ID,PRIORITY) (SELECT UNIT_ID,3,5 FROM UNIT_DETAIL);
INSERT INTO UNIT_TO_DELIVERY_MAPPINGS (UNIT_ID,DELIVERY_PARTNER_ID,PRIORITY) (SELECT UNIT_ID,6,5 FROM UNIT_DETAIL);

ALTER TABLE DELIVERY_DETAIL ADD COLUMN GENERATED_ORDER_ID VARCHAR(20);


UPDATE PARTNER_ATTRIBUTES SET MAPPING_VALUE ='order/cancel?order_id=<order_id>' WHERE `ID`='23';
UPDATE PARTNER_ATTRIBUTES SET MAPPING_VALUE ='order' WHERE `ID`='20';

#to be executed only in production environment
UPDATE PARTNER_ATTRIBUTES SET MAPPING_VALUE='7fe1eb78-d8d3-4b7e-9b13-ed5dc13cd0e4' WHERE ID=10;



