ALTER TABLE KETTLE_DEV.ORDER_PAYMENT_DETAIL
ADD COLUMN MERCHANT_ID VARCHAR(50);

CREATE TABLE KETTLE_DEV.UNIT_PRODUCT_STOCK_DATA (
  KEY_ID INTEGER NOT NULL AUTO_INCREMENT,
  BUSINESS_DATE DATE,
  CALCULATION_DATE DATE ,
  CAFE_OPENING TIME ,
  CAFE_CLOSING TIME,
  UNIT_ID INTEGER,
  UNIT_NAME varchar(40),
  PRODUCT_ID INTEGER,
  PRODUCT_NAME varchar(255),
  DOWNTIME INTEGER,
  START_TIME TIME,
  CLOSE_TIME TIME,
  PRIMARY KEY (KEY_ID)
) ENGINE=InnoDB AUTO_INCREMENT=4411 DEFAULT CHARSET=latin1;


CREATE TABLE KETTLE_DEV.UNIT_PRODUCT_STOCK_EVENT_AGGREGATE (
  KEY_ID INTEGER NOT NULL AUTO_INCREMENT,
  BUSINESS_DATE DATE ,
  CALCULATION_DATE DATE ,
  CAFE_OPENING TIME ,
  CAFE_CLOSING TIME ,
  UNIT_ID INTEGER,
  PRODUCT_ID INTEGER,
  PRODUCT_NAME varchar(255),
  DOWNTIME INTEGER,
  OPERATION_TIME INTEGER,
  PERCENTAGE FLOAT ,
  INSTANCE INTEGER,
  PRIMARY KEY (KEY_ID)
) ENGINE=InnoDB AUTO_INCREMENT=4096 DEFAULT CHARSET=latin1;

CREATE TABLE KETTLE_DEV.EXCEPTION_DATE_ENTRY(
  KEY_ID INTEGER NOT NULL AUTO_INCREMENT,
  UNIT_ID INTEGER,
  UNIT_NAME VARCHAR(255),
  BUSINESS_DATE DATE,
  STATUS VARCHAR(20) ,
  UPDATED_BY VARCHAR(255),
  UPDATED_TIME VARCHAR(255),
  PRIMARY KEY (KEY_ID)
) ENGINE=InnoDB AUTO_INCREMENT=4096 DEFAULT CHARSET=latin1;

DROP TABLE KETTLE_DEV.EXCEPTION_DATE_ENTRY;