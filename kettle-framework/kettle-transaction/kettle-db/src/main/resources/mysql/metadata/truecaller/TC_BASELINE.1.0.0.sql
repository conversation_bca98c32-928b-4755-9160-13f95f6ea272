ALTER TABLE KETTLE_MASTER_DEV.UN<PERSON>_DETAIL ADD COLUMN TRUE_CALLER_ONBOARDING VARCHAR (25) DEFAULT 'DEFAULT';
ALTER TABLE KETTLE_DEV.CUSTOMER_INFO ADD COLUMN TRUE_CALLER_PROFILE_ID INTEGER;

DROP TABLE IF EXISTS KETTLE_DEV.TRUE_CALLER_REQUEST_DETAIL;
CREATE TABLE KETTLE_DEV.TRUE_CALLER_REQUEST_DETAIL(
  TRUE_CALLER_REQUEST_ID INTEGER(11) AUTO_INCREMENT NOT NULL UNIQUE,
  UNIT_ID INTEGER NOT NULL,
  TERMINAL INTEGER NOT NULL,
  CONTACT VARCHAR(10) NOT NULL,
  REQUEST_ID VARCHAR(50) NOT NULL,
  ACCESS_TOKEN VARCHAR(100) NULL,
  TRUE_CALLER_PROFILE_ID INTEGER,
  REQUESTED_AT TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  TOKE<PERSON>_RECEIVED_AT TIMESTAMP NULL,
  PR<PERSON><PERSON><PERSON>_FETCHED_AT TIMESTAMP NULL,
  SESSION_KEY VARCHAR(10),
  PRIMARY KEY (`TRUE_CALLER_REQUEST_ID`),
  INDEX `TRUE_CALLER_REQUEST_DETAIL_REQUEST_KEY` USING BTREE (`REQUEST_ID` ASC),
  INDEX `TRUE_CALLER_REQUEST_DETAIL_CONTACT_KEY` USING BTREE (`CONTACT` ASC)
);

ALTER TABLE KETTLE_DEV.TRUE_CALLER_REQUEST_DETAIL ADD COLUMN REQUEST_TYPE VARCHAR(20);

DROP TABLE IF EXISTS KETTLE_DEV.TRUE_CALLER_PROFILE_DETAIL;
CREATE TABLE KETTLE_DEV.TRUE_CALLER_PROFILE_DETAIL(
  TRUE_CALLER_PROFILE_ID INTEGER(11) AUTO_INCREMENT NOT NULL UNIQUE,
  PRIMARY_CONTACT VARCHAR(10),
  FIRST_NAME VARCHAR(50),
  LAST_NAME VARCHAR(50),
  GENDER VARCHAR (10),
  EMAIL_ID VARCHAR (150),
  FACEBOOK_ID VARCHAR (200),
  TWITTER_ID VARCHAR (200),
  WEBSITE VARCHAR (200),
  COMPANY_NAME VARCHAR (150),
  JOB_TITLE VARCHAR (100),
  PROFILE_TYPE VARCHAR (15),
  PRIVACY VARCHAR (15),
  ACTIVE VARCHAR (1),
  AVATAR_URL VARCHAR (200),
  OTHER_CONTACTS VARCHAR (60),
  PRIMARY KEY (`TRUE_CALLER_PROFILE_ID`),
  INDEX `TRUE_CALLER_PROFILE_DETAIL_CONTACT_KEY` USING BTREE (`PRIMARY_CONTACT` ASC)
);

DROP TABLE IF EXISTS KETTLE_DEV.TRUE_CALLER_ADDRESS_DETAIL;
CREATE TABLE KETTLE_DEV.TRUE_CALLER_ADDRESS_DETAIL(
  ADDRESS_ID INTEGER(11) AUTO_INCREMENT NOT NULL UNIQUE,
  COUNTRY_CODE VARCHAR (5),
  CITY VARCHAR (50),
  STREET VARCHAR (50),
  ZIPCODE VARCHAR (20),
  PROFILE_ID INTEGER (11) NOT NULL,
  PRIMARY KEY (`ADDRESS_ID`)
);

DROP TABLE IF EXISTS KETTLE_DEV.CUSTOMER_INFO_CHANGE_LOG;
CREATE TABLE KETTLE_DEV.CUSTOMER_INFO_CHANGE_LOG(
  LOG_ID INTEGER(11) AUTO_INCREMENT NOT NULL UNIQUE,
  CUSTOMER_ID INTEGER,
  OLD_CONTACT VARCHAR (20),
  OLD_NAME VARCHAR (150),
  OLD_EMAIL VARCHAR (150),
  CUSTOMER_TYPE VARCHAR (10),
  CHANGED_BY VARCHAR (10),
  UPDATED_AT TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`LOG_ID`)
);


SET SQL_SAFE_UPDATES = 0;
UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET TRUE_CALLER_ONBOARDING = "DEFAULT";
UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET TRUE_CALLER_ONBOARDING = "TRUE_CALLER_FIRST" WHERE UNIT_ID IN (12017);
UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET TRUE_CALLER_ONBOARDING = "OTP_FIRST" WHERE UNIT_ID IN (26044);

ALTER TABLE KETTLE_STAGE.TRUE_CALLER_REQUEST_DETAIL ADD COLUMN STATUS VARCHAR(10);
ALTER TABLE KETTLE_STAGE.TRUE_CALLER_REQUEST_DETAIL ADD COLUMN END_POINT VARCHAR(100);