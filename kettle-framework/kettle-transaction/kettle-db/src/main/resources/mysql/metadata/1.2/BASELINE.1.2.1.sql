INSERT INTO ADDRESS_INFO
(`ADDRESS_ID`,`ADDRESS_LINE_1`,`ADDRESS_LINE_2`,`ADDRESS_LINE_3`,`CITY`,`STATE`,`COUNTRY`,`ZIPCODE`,`CONTACT_NUM_1`,`CONTACT_NUM_2`)
VALUES
(109,'8,Pearl Heaven,Building 86','Chappel Road,Bandra',null,'Mumbai','Maharashtra','India','400050','+91-','+91-'),
(110,'5,Ratand<PERSON>,Juhu Tara Road','Santacruz (West)',null,'Mumbai','Maharashtra','India','400049','+91-','+91-'),
(10031,'Building No.9,Flat No.201','Kailash Nagar,Vadavali,Ambarnath',null,'Mumbai','Maharashtra','India','421501','+91-9893598230','+91-9893596077'),
(10032,'<PERSON><PERSON><PERSON>,<PERSON><PERSON>chervy',null,null,'<PERSON><PERSON>ka<PERSON>','Kerala','India','678532','+91-9893598230','+91-9893596077'),
(10033,'Room No-63,Rahima Bai Baug',' opp. to khoja khana mahim west',null,'Mumbai','Maharashtra','India','400016','+91-9769898134','+91-9773943376'),
(10034,'Bhagat Singh Nagar','No-1 Linking Road Goregaon(west)','Near Renuka Mata Mandir','Mumbai','Maharashtra','India','400104','+91-9833092216','+91-9704530725');

INSERT INTO UNIT_DETAIL
(`UNIT_ID`,`UNIT_NAME`,`UNIT_REGION`,`UNIT_EMAIL`,`UNIT_CATEGORY`,`START_DATE`,`UNIT_STATUS`,`TIN`,`UNIT_ADDR_ID`,`BUSINESS_DIV_ID`)
VALUES
(10009,'Bandra','MUMBAI','<EMAIL>','CAFE','2015-10-24','Active','0000000000',109,1000),
(10010,'Juhu','MUMBAI','******','CAFE','2015-11-01','Active','0000000000',110,1000);

INSERT INTO UNIT_TAX_MAPPING
(`TAX_PROFILE_ID`,`UNIT_ID`,`TAX_PERCENTAGE`,`PROFILE_STATUS`,`STATE`)
VALUES
(1,10009,12.50,'ACTIVE','MUMBAI'),
(2,10009,5.00,'ACTIVE','MUMBAI'),
(3,10009,5.00,'ACTIVE','MUMBAI'),
(4,10009,5.60,'ACTIVE','MUMBAI'),
(5,10009,0.00,'ACTIVE','MUMBAI'),
(1,10010,12.50,'ACTIVE','MUMBAI'),
(2,10010,5.00,'ACTIVE','MUMBAI'),
(3,10010,5.00,'ACTIVE','MUMBAI'),
(4,10010,5.60,'ACTIVE','MUMBAI'),
(5,10010,0.00,'ACTIVE','MUMBAI');
INSERT INTO `EMPLOYEE_DETAIL`
(`EMP_ID`,`EMP_NAME`,`EMP_GENDER`,`EMP_CURRENT_ADDR`,`EMP_PERMANENT_ADDR`,`EMP_CONTACT_NUM_1`,`EMP_CONTACT_NUM_2`,`DEPTARTMENT_ID`,
`DESIGNATION_ID`,`EMPLOYMENT_TYPE`,`EMPLOYMENT_STATUS`,`BIOMETRIC_IDENTIFIER`,`JOINING_DATE`,`TERMINATION_DATE`,`REPORTING_MANAGER_ID`)
VALUES
(100027,'Vineeth Nair','M',10031,10032,'+91-9893598230','+91-9893596077',101,1003,'FULL_TIME','ACTIVE',null,'2015-06-04','9999-12-01',100000),
(100028,'Nilesh','M',10033,10033,'+91-9769898134','+91-9773943376',101,1003,'FULL_TIME','ACTIVE',null,'2015-06-04','9999-12-01',100027),
(100029,'Jai Prakash','M',10034,10034,'+91-9833092216','+91-9704530725',101,1003,'FULL_TIME','ACTIVE',null,'2015-08-20','9999-12-01',100027);


INSERT INTO `EMPLOYEE_UNIT_MAPPING`
(`EMP_ID`,
`UNIT_ID`)
VALUES
(100027,10009),
(100027,10010),
(100028,10009),
(100029,10010);

INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
VALUES
(100027,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100028,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100029,'0UxQYmqlaaaY1jDVAZWNQQ==' );

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 10009,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 10006;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 10006
and upm1.UNIT_ID = 10009;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 10010,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 10006;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 10006
and upm1.UNIT_ID = 10010;
