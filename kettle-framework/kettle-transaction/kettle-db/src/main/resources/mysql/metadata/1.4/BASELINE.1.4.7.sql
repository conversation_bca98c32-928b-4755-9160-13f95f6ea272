# COUPONS ACTIVE FOR <PERSON><PERSON><PERSON>I FROM CHAA01 TO CHAA10


INSERT INTO OFFER_DETAIL_DATA (	OFFER_DETAIL_ID,
								OFFER_CATEGORY,
								OFFER_TYPE,
								OFFER_TEXT,
								OFFER_DESCRIPTION,
								START_DATE,
								END_DATE,
								OFFER_STATUS,
								MIN_VALUE,
								VALIDATE_CUSTOMER,
								INCLUDE_TAXES,
								PR<PERSON>RITY,
								OFFER_SCOPE,
								MIN_ITEM_COUNT,
								QUANTITY_LIMIT,
								LOYALTY_LIMIT,
								OFFER_VALUE)
								
VALUES(	'2',
		'BILL',
		'FLAT_BILL_STRATEGY',
		'Rs. Up to 250 OFF on a Bill Value',
		'Get Up to 250 OFF on a Bill Value',
		'2016-02-04',
		'2016-03-31',
		'ACTIVE',
		'0',
		'Y',
		'Y',
		'1',
		'CUSTOMER',
		'1',
		'1',
		'0',
		'250');
		
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('2',
		'CHAA01',
		'2016-02-04',
		'2016-03-31',
		'N',
		'N',
		'1',
		'ACTIVE',
		'0',
		'Y');
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('2',
		'CHAA02',
		'2016-02-04',
		'2016-03-31',
		'N',
		'N',
		'1',
		'ACTIVE',
		'0',
		'Y');
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('2',
		'CHAA03',
		'2016-02-04',
		'2016-03-31',
		'N',
		'N',
		'1',
		'ACTIVE',
		'0',
		'Y');
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('2',
		'CHAA04',
		'2016-02-04',
		'2016-03-31',
		'N',
		'N',
		'1',
		'ACTIVE',
		'0',
		'Y');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('2',
		'CHAA05',
		'2016-02-04',
		'2016-03-31',
		'N',
		'N',
		'1',
		'ACTIVE',
		'0',
		'Y');
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('2',
		'CHAA06',
		'2016-02-04',
		'2016-03-31',
		'N',
		'N',
		'1',
		'ACTIVE',
		'0',
		'Y');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('2',
		'CHAA07',
		'2016-02-04',
		'2016-03-31',
		'N',
		'N',
		'1',
		'ACTIVE',
		'0',
		'Y');

INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('2',
		'CHAA08',
		'2016-02-04',
		'2016-03-31',
		'N',
		'N',
		'1',
		'ACTIVE',
		'0',
		'Y');
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('2',
		'CHAA09',
		'2016-02-04',
		'2016-03-31',
		'N',
		'N',
		'1',
		'ACTIVE',
		'0',
		'Y');
		
INSERT INTO COUPON_DETAIL_DATA (OFFER_DETAIL_ID,
								COUPON_CODE,
								START_DATE,
								END_DATE,
								COUPON_REUSE,
								CUSTOMER_REUSE,
								MAX_USAGE,
								COUPON_STATUS,
								USAGE_COUNT,
								MANUAL_OVERRIDE) 
VALUES('2',
		'CHAA10',
		'2016-02-04',
		'2016-03-31',
		'N',
		'N',
		'1',
		'ACTIVE',
		'0',
		'Y');

		
INSERT INTO COUPON_DETAIL_MAPPING_DATA (COUPON_DETAIL_ID,
										MAPPING_TYPE,
										MAPPING_VALUE,
										MAPPING_DATA_TYPE,
										MIN_VALUE,
										MAPPING_GROUP)
SELECT COUPON_DETAIL_ID,'UNIT_REGION','MUMBAI','java.lang.String','1','1' FROM COUPON_DETAIL_DATA WHERE COUPON_CODE LIKE 'CHAA%';