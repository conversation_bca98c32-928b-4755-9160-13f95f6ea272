DROP PROCEDURE KETTLE.HOURLY_TAT_DUMP;
DELIMITER $$
CREATE PROCEDURE KETTLE.`HOURLY_TAT_DUMP`()
BEGIN

DECLARE CURRENT_BIZ_DATE DATE;
DECLARE LAST_BIZ_HOUR DATETIME;
DECLARE GENERATION_TIMESTAMP DATETIME;
SELECT 
    DATE_ADD((CASE
            WHEN
                HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) < 5
            THEN
                SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
                    1)
            ELSE CURRENT_DATE
        END),
        INTERVAL 5 HOUR)
INTO CURRENT_BIZ_DATE;

SELECT ADDTIME(UTC_TIMESTAMP, '04:30:00') INTO LAST_BIZ_HOUR;
SELECT ADDTIME(UTC_TIMESTAMP, '05:30:00') INTO GENERATION_TIMESTAMP;

set sql_safe_updates=0;
UPDATE KETTLE.ASSEMBLY_TAT_DATA 
SET 
    TAT_STATUS = 'IN_ACTIVE'
WHERE
    TAT_STATUS = 'ACTIVE'
        AND BUSINESS_DATE = CURRENT_BIZ_DATE;

INSERT INTO KETTLE.ASSEMBLY_TAT_DATA(UNIT_ID,BUSINESS_DATE,TAT_TYPE,TAT_STATUS,
HOT_PREP_TIME,COLD_PREP_TIME,FOOD_PREP_TIME,ORDER_PREP_TIME,DISPATCH_TIME,GENERATION_TIME)
SELECT 
    A.UNIT_ID,
    CURRENT_BIZ_DATE,
    'TODAY',
    'ACTIVE',
    A.HOT_PREP_TIME,
    A.COLD_PREP_TIME,
    A.FOOD_PREP_TIME,
    B.ORDER_PREP_TIME,
    B.ORDER_DISPATCH_TIME,
    GENERATION_TIMESTAMP
FROM
    (SELECT 
        X.UNIT_ID,
            MAX(CASE
                WHEN X.TYPE = 'Hot Beverages' THEN X.PROCESSING_TIME
                ELSE 0
            END) AS HOT_PREP_TIME,
            MAX(CASE
                WHEN X.TYPE = 'Cold Beverages' THEN X.PROCESSING_TIME
                ELSE 0
            END) AS COLD_PREP_TIME,
            MAX(CASE
                WHEN X.TYPE = 'Food' THEN X.PROCESSING_TIME
                ELSE 0
            END) AS FOOD_PREP_TIME
    FROM
        (SELECT 
        wl.UNIT_ID,
            wl.TYPE,
            TRUNCATE(AVG(((CASE
                WHEN wl.TIME_TO_PROCESS > 0 THEN wl.TIME_TO_PROCESS
                ELSE 0
            END) + (CASE
                WHEN wl.TIME_TO_START > 0 THEN wl.TIME_TO_START
                ELSE 0
            END)) / 1000), 0) AS PROCESSING_TIME
    FROM
        KETTLE.WORKSTATION_LOG wl
    JOIN KETTLE.ORDER_DETAIL od
    WHERE
        od.ORDER_ID = wl.ORDER_ID
            AND od.BUSINESS_DATE IS NULL
            AND wl.TYPE IN ('Hot Beverages' , 'Cold Beverages', 'Food')
    GROUP BY wl.UNIT_ID , wl.TYPE) AS X
    GROUP BY X.UNIT_ID) AS A
        INNER JOIN
    (SELECT 
        UNIT_ID,
            TRUNCATE(AVG(ORDER_PROCESSING_TIME), 0) AS ORDER_PREP_TIME,
            TRUNCATE(AVG(ORDER_DISPATCH_TIME), 0) AS ORDER_DISPATCH_TIME
    FROM
        (SELECT 
        ald.UNIT_ID,
            od.ORDER_ID,
            TRUNCATE(MAX(((CASE
                WHEN wl.TIME_TO_PROCESS > 0 THEN wl.TIME_TO_PROCESS
                ELSE 0
            END) + (CASE
                WHEN wl.TIME_TO_START > 0 THEN wl.TIME_TO_START
                ELSE 0
            END)) / 1000), 2) AS ORDER_PROCESSING_TIME,
            TRUNCATE((ald.TIME_TO_DISPATCH - ald.TIME_TO_PROCESS_BY_WORKSTATIONS) / 1000, 2) ORDER_DISPATCH_TIME
    FROM
        KETTLE.WORKSTATION_LOG wl, KETTLE.ASSEMBLY_LOG_DATA ald, KETTLE.ORDER_DETAIL od
    WHERE
        ald.ORDER_ID = wl.ORDER_ID
            AND od.ORDER_ID = ald.ORDER_ID
            AND od.BUSINESS_DATE IS NULL
    GROUP BY ald.ORDER_ID) AS X
    GROUP BY X.UNIT_ID) AS B ON A.UNIT_ID = B.UNIT_ID;


INSERT INTO KETTLE.ASSEMBLY_TAT_DATA(UNIT_ID,BUSINESS_DATE,TAT_TYPE,TAT_STATUS,
HOT_PREP_TIME,COLD_PREP_TIME,FOOD_PREP_TIME,ORDER_PREP_TIME,DISPATCH_TIME,GENERATION_TIME)
SELECT A.UNIT_ID,CURRENT_BIZ_DATE,"LAST_HOUR","ACTIVE",A.HOT_PREP_TIME,A.COLD_PREP_TIME,
A.FOOD_PREP_TIME,B.ORDER_PREP_TIME, B.ORDER_DISPATCH_TIME, GENERATION_TIMESTAMP
FROM
(

SELECT 
X.UNIT_ID,
MAX(CASE WHEN X.TYPE = "Hot Beverages" then X.PROCESSING_TIME else 0 end) as HOT_PREP_TIME,
MAX(CASE WHEN X.TYPE = "Cold Beverages" then X.PROCESSING_TIME else 0 end) as COLD_PREP_TIME,
MAX(CASE WHEN X.TYPE = "Food" then X.PROCESSING_TIME else 0 end) as FOOD_PREP_TIME
FROM 
(
	select wl.UNIT_ID,wl.TYPE,
    TRUNCATE(AVG(
		(
			(CASE WHEN wl.TIME_TO_PROCESS > 0 THEN wl.TIME_TO_PROCESS ELSE 0 END) 
			+ 
			(CASE WHEN wl.TIME_TO_START > 0 THEN wl.TIME_TO_START ELSE 0 END)
		)/1000), 0) as PROCESSING_TIME 

	from KETTLE.WORKSTATION_LOG wl
	INNER JOIN KETTLE.ORDER_DETAIL od on od.ORDER_ID = wl.ORDER_ID
	where od.BILLING_SERVER_TIME > LAST_BIZ_HOUR
	and wl.TYPE IN ("Hot Beverages","Cold Beverages","Food")
	group by wl.UNIT_ID,wl.TYPE
) as X group by X.UNIT_ID) as A

INNER JOIN

(
	SELECT UNIT_ID, 
    TRUNCATE(AVG(ORDER_PROCESSING_TIME),0) as ORDER_PREP_TIME, 
    TRUNCATE(AVG(ORDER_DISPATCH_TIME),0) as ORDER_DISPATCH_TIME FROM 
    (
		SELECT 
				ald.UNIT_ID,
				od.ORDER_ID,
				TRUNCATE(MAX(
				(			
					(CASE WHEN wl.TIME_TO_PROCESS > 0 THEN wl.TIME_TO_PROCESS ELSE 0 END) 
					+ 
					(CASE WHEN wl.TIME_TO_START > 0 THEN wl.TIME_TO_START ELSE 0 END)
				) / 1000),2) AS ORDER_PROCESSING_TIME,
                TRUNCATE((ald.TIME_TO_DISPATCH - ald.TIME_TO_PROCESS_BY_WORKSTATIONS) / 1000, 2) ORDER_DISPATCH_TIME
			FROM
				KETTLE.WORKSTATION_LOG wl
			INNER JOIN KETTLE.ASSEMBLY_LOG_DATA ald ON ald.ORDER_ID = wl.ORDER_ID
			INNER JOIN KETTLE.ORDER_DETAIL od on od.ORDER_ID = ald.ORDER_ID
			WHERE
				od.BILLING_SERVER_TIME > LAST_BIZ_HOUR
			GROUP BY ald.ORDER_ID
	) as X GROUP BY X.UNIT_ID
    
) as B on A.UNIT_ID = B.UNIT_ID;



END$$
DELIMITER ;
