INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`R<PERSON>_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(12,
'CATEGORY',
'Others',
'Others');

INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('119', '4', 'NoSugar', 'No Sugar', 'NS', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('120', '4', 'SugarFree', 'Sugar Free', 'SF', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `R<PERSON>_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('121', '4', 'ExtraSugar', 'Extra Sugar', 'ES', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2204', '22', 'ExtraSugar', 'Extra Sugar', 'ES', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2205', '22', 'SugarFree', 'Sugar Free', 'SF', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2309', '23', 'FocacciaBread', 'Focaccia Bread', 'FB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2310', '23', 'MultigrainBread', 'Multigrain Bread', 'MB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2311', '23', 'JumboBread', 'Jumbo Bread', 'JB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2312', '23', 'CroissantBread', 'Croissant Bread', 'CB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2504', '25', 'ExtraSugar', 'Extra Sugar', 'ES', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2505', '25', 'SugarFree', 'Sugar Free', 'SF', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2506', '25', 'ExtraCoffeePowder', 'Extra Coffee Powder', 'ECP', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2507', '25', 'ExtraChocolateSyrup', 'Extra Chocolate Syrup', 'ECS', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2508', '25', 'ExtraRoofAfzaSyrup', 'Extra RoofAfza Syrup', 'ERS', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2609', '26', 'FocacciaBread', 'Focaccia Bread', 'FB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2610', '26', 'MultigrainBread', 'Multigrain Bread', 'MB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2611', '26', 'JumboBread', 'Jumbo Bread', 'JB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2612', '26', 'CroissantBread', 'Croissant Bread', 'CB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2815', '28', 'FocacciaBread', 'Focaccia Bread', 'FB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2816', '28', 'MultigrainBread', 'Multigrain Bread', 'MB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2817', '28', 'JumboBread', 'Jumbo Bread', 'JB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2818', '28', 'CroissantBread', 'Croissant Bread', 'CB', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2819', '28', 'ExtraSugar', 'Extra Sugar', 'ES', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2820', '28', 'SugarFree', 'Sugar Free', 'SF', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2821', '28', 'NoSugar', 'No Sugar', 'NS', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2822', '28', 'DesiDoodhPatti', 'Desi Doodh Patti', 'DDP', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2823', '28', 'DesiDoodhKum', 'Desi Doodh Kum', 'DDK', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2824', '28', 'DesiPaaniKum', 'Desi Paani Kum', 'DPK', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2920', '29', 'ExtraSugar', 'Extra Sugar', 'ES', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2921', '29', 'SugarFree', 'Sugar Free', 'SF', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2922', '29', 'NoSugar', 'No Sugar', 'NS', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2923', '29', 'DesiDoodhPatti', 'Desi Doodh Patti', 'DDP', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2924', '29', 'DesiDoodhKum', 'Desi Doodh Kum', 'DDK', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('2925', '29', 'DesiPaaniKum', 'Desi Paani Kum', 'DPK', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('1201', '12', 'PaidAddons', 'Paid Addons', 'ACTIVE');
UPDATE REF_LOOKUP SET `RL_CODE`='Regular' WHERE `RL_ID`='21';
UPDATE REF_LOOKUP SET `RL_CODE`='Full' WHERE `RL_ID`='22';
UPDATE REF_LOOKUP SET `RL_CODE`='ChotiKetli' WHERE `RL_ID`='23';
UPDATE REF_LOOKUP SET `RL_CODE`='BadiKetli' WHERE `RL_ID`='24';
UPDATE REF_LOOKUP SET `RL_CODE`='Regular' WHERE `RL_ID`='30';
UPDATE REF_LOOKUP SET `RL_CODE`='Full', `RL_NAME`='Full', `RL_SHORT_CODE`='FUL' WHERE `RL_ID`='31';
UPDATE REF_LOOKUP SET `RL_STATUS`='INACTIVE' WHERE `RL_ID`='1003';
UPDATE REF_LOOKUP SET `RL_NAME`='Hari Chutni' WHERE `RL_ID`='2305';
UPDATE REF_LOOKUP SET `RL_CODE`='To-Go' WHERE `RL_ID`='2401';
UPDATE REF_LOOKUP SET `RL_CODE`='NoToast' WHERE `RL_ID`='2406';
UPDATE REF_LOOKUP SET `RL_CODE`='NoButter' WHERE `RL_ID`='2407';
UPDATE REF_LOOKUP SET `RL_CODE`='To-Go' WHERE `RL_ID`='2501';
UPDATE REF_LOOKUP SET `RL_SHORT_CODE`='NI' WHERE `RL_ID`='2502';
UPDATE REF_LOOKUP SET `RL_CODE`='HoneyGarlicMayo', `RL_NAME`='Honey Garlic Mayo' WHERE `RL_ID`='2302';
UPDATE REF_LOOKUP SET `RL_CODE`='GreenChilliMayo', `RL_NAME`='Green Chilli Mayo' WHERE `RL_ID`='2304';
UPDATE REF_LOOKUP SET `RL_CODE`='HoneyGarlicMayo', `RL_NAME`='Honey Garlic Mayo' WHERE `RL_ID`='2402';
UPDATE REF_LOOKUP SET `RL_CODE`='GreenChilliMayo', `RL_NAME`='Green Chilli Mayo' WHERE `RL_ID`='2404';
UPDATE REF_LOOKUP SET `RL_CODE`='HoneyGarlicMayo', `RL_NAME`='Honey Garlic Mayo' WHERE `RL_ID`='2603';
UPDATE REF_LOOKUP SET `RL_CODE`='GreenChilliMayo', `RL_NAME`='Green Chilli Mayo' WHERE `RL_ID`='2605';
UPDATE REF_LOOKUP SET `RL_CODE`='NoToast' WHERE `RL_ID`='2607';
UPDATE REF_LOOKUP SET `RL_CODE`='NoButter' WHERE `RL_ID`='2608';
UPDATE REF_LOOKUP SET `RL_CODE`='To-Go' WHERE `RL_ID`='2701';
UPDATE REF_LOOKUP SET `RL_CODE`='Butter-ChickenWrap' WHERE `RL_ID`='2802';
UPDATE REF_LOOKUP SET `RL_SHORT_CODE`='SIC' WHERE `RL_ID`='2804';
UPDATE REF_LOOKUP SET `RL_CODE`='HoneyGarlicMayo', `RL_NAME`='Honey Garlic Mayo' WHERE `RL_ID`='2806';
UPDATE REF_LOOKUP SET `RL_CODE`='GreenChilliMayo', `RL_NAME`='Green Chilli Mayo' WHERE `RL_ID`='2810';
UPDATE REF_LOOKUP SET `RL_CODE`='BombaySpecial', `RL_NAME`='Bombay Special' WHERE `RL_ID`='2904';
UPDATE REF_LOOKUP SET `RL_CODE`='To-Go' WHERE `RL_ID`='2911';
UPDATE REF_LOOKUP SET `RL_CODE`='HoneyGarlicMayo', `RL_NAME`='Honey Garlic Mayo' WHERE `RL_ID`='2914';
UPDATE REF_LOOKUP SET `RL_CODE`='GreenChilliMayo', `RL_NAME`='Green Chilli Mayo' WHERE `RL_ID`='2916';

INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`) VALUES ('862', 'Extra Coffee Powder', '-----', '100', '12', '1201', 'ACTIVE', '2013-01-01', '9999-12-01', 'CH1011099', '1', 'NET_PRICE', '27');
UPDATE PRODUCT_DETAIL SET `PRODUCT_TYPE`='12', `PRODUCT_SUB_TYPE`='1201' WHERE `PRODUCT_ID`='860';
UPDATE PRODUCT_DETAIL SET `PRODUCT_TYPE`='12', `PRODUCT_SUB_TYPE`='1201' WHERE `PRODUCT_ID`='861';
INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`) VALUES ('863', 'Extra Chocolate Syrup', '-----', '100', '12', '1201', 'ACTIVE', '2013-01-01', '9999-12-01', 'CH1011100', '1', 'NET_PRICE', '27');
INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`) VALUES ('864', 'Extra RoofAfza Syrup', '-----', '100', '12', '1201', 'ACTIVE', '2013-01-01', '9999-12-01', 'CH1011101', '1', 'NET_PRICE', '27');

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 862, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where PRODUCT_ID = 10;

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 863, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where PRODUCT_ID = 10;

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 864, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where PRODUCT_ID = 10;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upm2.DIMENSION_CODE, 20 from UNIT_PRODUCT_MAPPING upm1
LEFT OUTER JOIN (
select upm.UNIT_ID, upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm where upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID and upm.PRODUCT_ID  = 860
) upm2
ON  upm1.UNIT_ID = upm2.UNIT_ID
where upm1.PRODUCT_ID  = 862;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upm2.DIMENSION_CODE, 20 from UNIT_PRODUCT_MAPPING upm1
LEFT OUTER JOIN (
select upm.UNIT_ID, upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm where upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID and upm.PRODUCT_ID  = 860
) upm2
ON  upm1.UNIT_ID = upm2.UNIT_ID
where upm1.PRODUCT_ID  = 863;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upm2.DIMENSION_CODE, 20 from UNIT_PRODUCT_MAPPING upm1
LEFT OUTER JOIN (
select upm.UNIT_ID, upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm where upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID and upm.PRODUCT_ID  = 860
) upm2
ON  upm1.UNIT_ID = upm2.UNIT_ID
where upm1.PRODUCT_ID  = 864;

