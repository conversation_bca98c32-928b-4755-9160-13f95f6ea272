DROP TABLE IF EXISTS DENOMINATION;
CREATE TABLE DENOMINATION(
DENOMIN<PERSON>ION_ID INT PRIMARY KEY AUTO_INCREMENT,
PAYMENT_MODE INT NOT NULL,
<PERSON><PERSON><PERSON>INATION_TEXT VARCHAR(100) NOT NULL,
<PERSON>N<PERSON><PERSON><PERSON>ION_CODE VARCHAR(20) NOT NULL,
<PERSON>N<PERSON><PERSON><PERSON><PERSON>_VALUE INT NOT NULL,
D<PERSON><PERSON>AY_ORDER INT NOT NULL,
STATUS VARCHAR(20) NOT NULL DEFAULT 'IN_ACTIVE',
BUNDLE_SIZE INT NOT NULL DEFAULT 100,
FOREIGN KEY(PAYMENT_MODE) REFERENCES PAYMENT_MODE(PAYMENT_MODE_ID)
)ENGINE=INNODB;

DROP TABLE IF EXISTS UNIT_CLOSURE_ENTRY;
CREATE TABLE UNIT_CLOSURE_ENTRY(
UNIT_CLOSURE_ENTRY_ID INT PRIMARY KEY AUTO_INCREMENT,
<PERSON><PERSON><PERSON>URE_UNIT INT NOT NULL,
PAYMENT_MODE INT NOT NULL,
CL<PERSON>URE_AMOUNT FLOAT NOT NULL,
COMMENT VARCHAR(1000) DEFAULT NULL,
CLOSURE_STATUS VARCHAR(20) NOT NULL,
FOREIGN KEY(CLOSURE_UNIT) REFERENCES UNIT_DETAIL(UNIT_ID),
FOREIGN KEY(PAYMENT_MODE) REFERENCES PAYMENT_MODE(PAYMENT_MODE_ID)
)ENGINE=INNODB;

DROP TABLE IF EXISTS SETTLEMENT_DETAIL;
CREATE TABLE SETTLEMENT_DETAIL(
SETTLEMENT_DETAIL_ID INT PRIMARY KEY AUTO_INCREMENT,
SETTLEMENT_UNIT INT NOT NULL,
SETTLEMENT_TIME DATETIME NOT NULL,
SETTLEMENT_SERVICE_PROVIDER VARCHAR(100) NOT NULL,
SETTLEMENT_AMOUNT FLOAT NOT NULL,
PAYMENT_MODE INT NOT NULL,
SETTLEMENT_SERVICE_PROVIDER_RECEIPT VARCHAR(20) DEFAULT NULL,
UNSETTLED_AMOUNT FLOAT DEFAULT NULL,
TOTAL_AMOUNT FLOAT DEFAULT NULL,
CLOSING_AMOUNT FLOAT DEFAULT NULL,
SETTLEMENT_CLOSING_RECEIPT VARCHAR(100) DEFAULT NULL,
SETTLEMENT_RECEIPT_PATH VARCHAR(200) DEFAULT NULL,
FOREIGN KEY(SETTLEMENT_UNIT) REFERENCES UNIT_DETAIL(UNIT_ID),
FOREIGN KEY(PAYMENT_MODE) REFERENCES PAYMENT_MODE(PAYMENT_MODE_ID)
)ENGINE=INNODB;

DROP TABLE IF EXISTS PULL_DETAIL;
CREATE TABLE PULL_DETAIL(
PULL_ID INT PRIMARY KEY AUTO_INCREMENT,
CREATION_TIME DATETIME NOT NULL,
CREATED_BY INT NOT NULL,
WITNESSED_BY INT NOT NULL,
PAYMENT_MODE INT NOT NULL,
PULL_AMOUNT FLOAT NOT NULL,
COMMENT VARCHAR(1000) DEFAULT NULL,
STATUS VARCHAR(20) NOT NULL,
PULL_DATE DATETIME NOT NULL,
PENDING_REASON VARCHAR(1000) DEFAULT NULL,
SOURCE VARCHAR(20) NOT NULL,
PULL_UNIT INT NOT NULL,
CLOSURE_PAYMENT_DETAIL_ID INT NOT NULL,
SETTLEMENT_DETAIL INT DEFAULT NULL,
FOREIGN KEY(CREATED_BY) REFERENCES EMPLOYEE_DETAIL(EMP_ID),
FOREIGN KEY(WITNESSED_BY) REFERENCES EMPLOYEE_DETAIL(EMP_ID),
FOREIGN KEY(PAYMENT_MODE) REFERENCES PAYMENT_MODE(PAYMENT_MODE_ID),
FOREIGN KEY(PULL_UNIT) REFERENCES UNIT_DETAIL(UNIT_ID),
FOREIGN KEY(CLOSURE_PAYMENT_DETAIL_ID) REFERENCES CLOSURE_PAYMENT_DETAILS(PAYMENT_CLOSURE_ID),
FOREIGN KEY(SETTLEMENT_DETAIL) REFERENCES SETTLEMENT_DETAIL(SETTLEMENT_DETAIL_ID)
)ENGINE=INNODB;

DROP TABLE IF EXISTS PULL_DENOMINATION;
CREATE TABLE PULL_DENOMINATION(
PULL_DENOMINATION_ID INT PRIMARY KEY AUTO_INCREMENT,
PULL_DETAIL INT NOT NULL,
DENOMINATION INT NOT NULL,
PACKET_COUNT INT NOT NULL,
LOOSE_CURRENCY_COUNT INT NOT NULL,
TOTAL_AMOUNT FLOAT NOT NULL,
FOREIGN KEY(PULL_DETAIL) REFERENCES PULL_DETAIL(PULL_ID),
FOREIGN KEY(DENOMINATION) REFERENCES DENOMINATION(DENOMINATION_ID)
)ENGINE=INNODB;

DROP TABLE IF EXISTS SETTLEMENT_DENOMINATION;
CREATE TABLE SETTLEMENT_DENOMINATION(
SETTLEMENT_DENOMINATION_ID INT PRIMARY KEY AUTO_INCREMENT,
SETTLEMENT_DETAIL INT NOT NULL,
DENOMINATION INT NOT NULL,
PACKET_COUNT INT NOT NULL,
LOOSE_CURRENCY_COUNT INT NOT NULL,
TOTAL_AMOUNT FLOAT NOT NULL,
FOREIGN KEY(SETTLEMENT_DETAIL) REFERENCES SETTLEMENT_DETAIL(SETTLEMENT_DETAIL_ID),
FOREIGN KEY(DENOMINATION) REFERENCES DENOMINATION(DENOMINATION_ID)
)ENGINE=INNODB;

INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `BUNDLE_SIZE`) VALUES ('1', 'Thousand', 'THOUSAND', '1000', '1', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `BUNDLE_SIZE`) VALUES ('1', 'Five Hundred', 'FIVE_HUNDRED', '500', '2', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `BUNDLE_SIZE`) VALUES ('1', 'Hundred', 'HUNDRED', '100', '3', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `BUNDLE_SIZE`) VALUES ('1', 'Fifty', 'FIFTY', '50', '4', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `BUNDLE_SIZE`) VALUES ('1', 'Twenty', 'TWENTY', '20', '5', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `BUNDLE_SIZE`) VALUES ('1', 'Ten', 'Ten', '10', '6', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `BUNDLE_SIZE`) VALUES ('1', 'Five', 'FIVE', '5', '7', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `BUNDLE_SIZE`) VALUES ('1', 'Two', 'TWO', '2', '8', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `BUNDLE_SIZE`) VALUES ('1', 'One', 'ONE', '1', '9', '100');
