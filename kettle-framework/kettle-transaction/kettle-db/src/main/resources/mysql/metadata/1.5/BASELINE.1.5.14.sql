

CREATE TABLE ORDER_METADATA_DETAIL (
METADATA_ID INT PRIMARY KEY AUTO_INCREMENT,
ORDER_ID INT NOT NULL,
ATTRIBUTE_NAME VARCHAR(50) NOT NULL,
ATTRIBUTE_VALUE VARCHAR(30)
);


#ADDING NEW DEPARTMENT, DESIGNATION & DEPARTMENT DESIGNATION MAPPING
INSERT INTO DEPARTMENT (DEPT_ID, DEPT_NAME, DEPT_DESC, BUSINESS_DIV_ID) VALUES ('105', 'Finance', 'Finance', '1000');

INSERT INTO DESIGNATION (DE<PERSON>GNATION_ID, DESIG<PERSON>TION_NAME, DESIGNA<PERSON><PERSON>_DESC) VALUES ('1301', 'Manager', 'Manager in Finance');
INSERT INTO DESIGNATION (DESIGNATION_ID, DESIGNATION_NAME, DESIGNATION_DESC) VALUES ('1302', 'Associate', 'Associate in Finance');

INSERT INTO DEPARTMENT_DESIGNATION_MAPPING (DEPT_<PERSON>, DE<PERSON><PERSON><PERSON>TION_ID) VALUES ('105', '1301');
INSERT INTO DEPARTMENT_DESIGNATION_MAPPING (DEPT_ID, DESIGNATION_ID) VALUES ('105', '1302');
