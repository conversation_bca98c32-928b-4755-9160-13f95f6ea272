CREATE TABLE KETTLE_WAREHOUSE_DEV.RFM_SCORE_FOR_ACTIVE_CUSTOMERS
(
    `CUSTOMER_ID`            int(11) DEFAULT NULL,
    `PRODUCT_ID`             int(11) DEFAULT NULL,
    `REC<PERSON><PERSON>Y`                int(11) DEFAULT NULL,
    `FREQUENCY`              int(11) DEFAULT NULL,
    `R_QUARTILE`             int(11) DEFAULT NULL,
    `F_QUARTILE`             int(11) DEFAULT NULL,
    `RFM_SCORE`              int(11) DEFAULT NULL,
    `LAST_UPDATE_DATE`       date         DEFAULT NULL,
    `RECOMMENDATION_SEGMENT` varchar(200) DEFAULT NULL,
    KEY  `RFM_SCORE_FOR_ACTIVE_CUSTOMERS_CUSTOMER_ID` (`CUSTOMER_ID`) USING BTREE,
    KEY  `RFM_SCORE_FOR_ACTIVE_CUSTOMERS_PRODUCT_ID` (`PRODUCT_ID`) USING BTREE,
    KEY  `RFM_SCORE_FOR_ACTIVE_CUSTOMERS_LAST_UPDATE_DATE` (`LAST_UPDATE_DATE`) USING BTREE,
    KEY  `RFM_SCORE_FOR_ACTIVE_CUSTOMERS_RECOMMENDATION_SEGMENT` (`RECOMMENDATION_SEGMENT`) USING BTREE
);


CREATE TABLE KETTLE_WAREHOUSE_DEV.`RFM_SCORE_FOR_CUSTOMERS`
(
    `CUSTOMER_ID`            int(11) DEFAULT NULL,
    `PRODUCT_ID`             int(11) DEFAULT NULL,
    `RECENCY`                int(11) DEFAULT NULL,
    `FREQUENCY`              int(11) DEFAULT NULL,
    `R_QUARTILE`             int(11) DEFAULT NULL,
    `F_QUARTILE`             int(11) DEFAULT NULL,
    `RFM_SCORE`              int(11) DEFAULT NULL,
    `LAST_UPDATE_DATE`       date         DEFAULT NULL,
    `RECOMMENDATION_SEGMENT` varchar(200) DEFAULT NULL,
    KEY   `RFM_SCORE_FOR_CUSTOMERS_CUSTOMER_ID` (`CUSTOMER_ID`) USING BTREE,
    KEY   `RFM_SCORE_FOR_CUSTOMERS_PRODUCT_ID` (`PRODUCT_ID`) USING BTREE,
    KEY   `RFM_SCORE_FOR_CUSTOMERS_LAST_UPDATE_DATE` (`LAST_UPDATE_DATE`) USING BTREE,
    KEY    `RFM_SCORE_FOR_CUSTOMERS_RECOMMENDATION_SEGMENT` (`RECOMMENDATION_SEGMENT`) USING BTREE
) ;


ALTER TABLE KETTLE_DEV.ORDER_ITEM ADD ORIGINAL_PRICE DECIMAL(10,2) NOT NULL;


ALTER TABLE  KETTLE_DEV.UNIT_PRODUCT_STOCK_DATA
    ADD COLUMN `DIMENSION` varchar(20) DEFAULT NULL,
ADD COLUMN   `END_HOUR` int(11) DEFAULT NULL,
 ADD COLUMN  `START_HOUR` int(11) DEFAULT NULL,
 ADD COLUMN  `TOTAL_TIME` int(11) DEFAULT NULL,
 ADD COLUMN  `WEIGHT` decimal(16,6) DEFAULT NULL,
 ADD COLUMN  `BRAND_ID` int(11) DEFAULT NULL;

ALTER TABLE  KETTLE_DEV.UNIT_PRODUCT_STOCK_DATA
    MODIFY COLUMN `CAFE_OPENING` datetime  DEFAULT NULL,
    MODIFY COLUMN `CAFE_CLOSING` datetime  DEFAULT NULL,
    MODIFY COLUMN `START_TIME` datetime  DEFAULT NULL,
    MODIFY COLUMN `CLOSE_TIME` datetime  DEFAULT NULL;


CREATE TABLE KETTLE_DEV.UNIT_PRODUCTS_STOCK_EVENT_DATA (
   `KEY_ID` int(11) NOT NULL AUTO_INCREMENT,
   `UNIT_ID` int(11) DEFAULT NULL,
   `UNIT_NAME` varchar(40) DEFAULT NULL,
   `PRODUCT_ID` int(11) DEFAULT NULL,
   `DIMENSION` varchar(255) DEFAULT NULL,
   `BRAND_ID` int(11) DEFAULT NULL,
   `PRODUCT_NAME` varchar(255) DEFAULT NULL,
   `STATUS` varchar(255) DEFAULT NULL,
   `EVENT_TYPE` varchar(255) DEFAULT NULL,
   `EVENT_TIMESTAMP` datetime DEFAULT NULL,
   PRIMARY KEY (`KEY_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=latin1;