update DESI<PERSON><PERSON><PERSON>ON set DESIGNATION_NAME='Shift Supervisor',DESIGNATION_DESC='Shift Supervisor' where DESIGNATION_ID=1002;

INSERT INTO CHANNEL_PARTNER
(`PARTNER_ID`,
`PARTNER_CODE`,
`PARTNER_DISPLAY_NAME`)
VALUES
(8,
'G<PERSON><PERSON><PERSON>',
'Groupon');

INSERT INTO CHANNEL_PARTNER
(`PARTNER_ID`,
`PARTNER_CODE`,
`PARTNER_DISPLAY_NAME`)
VALUES
(9,
'CHAAYOS_TAKE_AWAY',
'<PERSON><PERSON><PERSON> (Take Away)');
 
INSERT INTO ADDRESS_INFO
(`ADDRESS_ID`,`ADDRESS_LINE_1`,`ADDRESS_LINE_2`,`ADDRESS_LINE_3`,`CITY`,`STATE`,`COUNTRY`,`ZIPCODE`,`CONTACT_NUM_1`,`CONTACT_NUM_2`)
VALUES
(10014,'904/24 Tuglka bad','Ext.near Govind P<PERSON>',null,'New Delhi','New Delhi','India','110019','+91-9654642395','+91-7838983998'),
(10015,'C43 Block-4','Street No.5 Frakha Barrage',null,'Kolkata','West Bengal','India','7440211','+91-9654642395','+91-7838983998'),
(10016,'Room no.-11','L-184 Mahipalpur Ext.',null,'New Delhi','New Delhi','India','190037','+91-8512024165','+91-8750070089'),
(10017,'H.N.-579 Avasvikas colony','kunraghat',null,'Gorakhpur','Uttar Pradesh','India','27308','+91-8512024165','+91-8750070089'),
(10018,'Moh. Pal nagar','Sugar mill',null,'Bijnor','Uttar Pradesh','India','246701','+91-9717066592','+91-8287277133'),
(10019,'B-34','SHIVA ENCLAVE VIKAS NAGAR',null,'New Delhi','New Delhi','India','110059','+91-8802052292','+91-8802052292'),
(10022,'465,Phase 5','Udyog Vihar',null,'Haryana','Gurgaon','India','122016','+91-9999999999','+91-9999999999'),
(10023,'3 Alipur road','Canal Rest house civil lines',null,'New Delhi','New Delhi','India','110054','+91-8802126201','+91-8802126201'),
(10024,'RZ-2812/32,2nd floor','Tugluqabad Extension','Guru Ravidas road','New Delhi','New Delhi','India','110019','+91-9910717936','+91-9910717936'),
(10025,'H.no.-60,GF','Near park-2',null,'Gurgaon','Haryana','India','122017','+91-8287511757','+91-8470860322'),
(10026,'Vill-Nauva',null,null,'Rohtas','Bihar','India','821112','+91-8287511757','+91-8470860322'),
(10027,'H.No-96,GF','Vill-Sukhrali',null,'Gurgaon','Haryana','India','122001','+91-8860981485','+91-9711461443'),
(10028,'Vill-Sidhpur','Po-Bergaon',null,'Almora','Utrakhand','India','263678','+91-8860981485','+91-9711461443'),
(10029,'E-83,Om Vihar Phase 5','Uttam Nagar landmark Chandra public school',null,'New Delhi','New Delhi','India','110059','+91-9210661973','+91-9210661973'),
(10030,'RG-A/82','Raghvir nagar',null,'New Delhi','New Delhi','India','110027','+91-9958135894','+91-9958135894');


INSERT INTO EMPLOYEE_DETAIL
(`EMP_ID`,`EMP_NAME`,`EMP_GENDER`,`EMP_CURRENT_ADDR`,`EMP_PERMANENT_ADDR`,`EMP_CONTACT_NUM_1`,`EMP_CONTACT_NUM_2`,`DEPTARTMENT_ID`,
`DESIGNATION_ID`,`EMPLOYMENT_TYPE`,`EMPLOYMENT_STATUS`,`BIOMETRIC_IDENTIFIER`,`JOINING_DATE`,`TERMINATION_DATE`,`REPORTING_MANAGER_ID`)
VALUES
(100014,'Kuldeep Kumar Singh','M',10014,10015,'+91-9654642395','+91-7838983998',101,1002,'FULL_TIME','ACTIVE',null,'2014-10-29','9999-12-01',100002),
(100015,'Nitesh Singh','M',10016,10017,'+91-8512024165','+91-8750070089',101,1002,'FULL_TIME','ACTIVE',null,'2014-10-05','9999-12-01',100001),
(100016,'Hemendra Kumar','M',10018,10018,'+91-9717066592','+91-8287277133',101,1002,'FULL_TIME','ACTIVE',null,'2014-12-03','9999-12-01',100008),
(100017,'Vinod Chauhan','M',10019,10019,'+91-8802052292','+91-8802052292',101,1002,'FULL_TIME','ACTIVE',null,'2013-10-18','9999-12-01',100006),
(100018,'Rahul','M',10023,10023,'+91-8802126201','+91-8802126201',101,1002,'FULL_TIME','ACTIVE',null,'2015-07-11','9999-12-01',100006),
(100019,'Arobindo Kumar Mullick','M',10024,10024,'+91-9910717936','+91-9910717936',101,1002,'FULL_TIME','ACTIVE',null,'2015-10-22','9999-12-01',100003),
(100020,'Prince Kumar','M',10025,10026,'+91-8287511757','+91-8470860322',101,1002,'FULL_TIME','ACTIVE',null,'2013-10-02','9999-12-01',100002),
(100021,'Deepak Kandpal','M',10027,10028,'+91-8860981485','+91-9711461443',101,1002,'FULL_TIME','ACTIVE',null,'2015-08-18','9999-12-01',100002),
(100022,'Ranvir Singh Gausain','M',10029,10029,'+91-9210661973','+91-9210661973',101,1002,'FULL_TIME','ACTIVE',null,'2013-12-26','9999-12-01',100007),
(100023,'Amit Kumar','M',10030,10030,'+91-9958135894','+91-9958135894',101,1002,'FULL_TIME','ACTIVE',null,'2015-06-30','9999-12-01',100007),
(100024,'Raghav Verma','M',10022,10022,'+91-9560898111','+91-9560898111',102,1003,'FULL_TIME','ACTIVE',null,'2013-01-01','9999-12-01',100000),
(100025,'Saurabh Singh Tanwar','M',10022,10022,'+91-9650404364','+91-9650404364',102,1003,'FULL_TIME','ACTIVE',null,'2013-01-01','9999-12-01',100000),
(100026,'Mohit Malik','M',10022,10022,'+91-9599597740','+91-9599597740',102,1003,'FULL_TIME','ACTIVE',null,'2013-01-01','9999-12-01',100000);

INSERT INTO `EMPLOYEE_UNIT_MAPPING`
(`EMP_ID`,
`UNIT_ID`)
VALUES
(100014,10001),
(100015,10001),
(100016,10007),
(100017,10006),
(100018,10006),
(100019,10003),
(100020,10000),
(100021,10000),
(100022,10008),
(100023,10008),
(100024,10000),
(100024,10001),
(100024,10002),
(100024,10003),
(100024,10004),
(100024,10005),
(100024,10006),
(100024,10007),
(100024,10008),
(100025,10000),
(100025,10001),
(100025,10002),
(100025,10003),
(100025,10004),
(100025,10005),
(100025,10006),
(100025,10007),
(100025,10008),
(100026,10000),
(100026,10001),
(100026,10002),
(100026,10003),
(100026,10004),
(100026,10005),
(100026,10006),
(100026,10007),
(100026,10008);

INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
VALUES
(100014,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100015,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100016,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100017,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100018,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100019,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100020,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100021,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100022,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100023,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100024,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100025,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100026,'0UxQYmqlaaaY1jDVAZWNQQ==' );


UPDATE REF_LOOKUP_TYPE 
SET 
    `RTL_CODE` = 'Bakery',
    `RTL_NAME` = 'Bakery'
WHERE
    `RTL_ID` = '10';

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(23,
'ADDONS',
'SANDWICH',
'Sandwich');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(24,
'ADDONS',
'NASHTA',
'Nashta');

UPDATE REF_LOOKUP_TYPE 
SET 
    `RTL_CODE` = 'WRAP',
    `RTL_NAME` = 'Wrap'
WHERE
    `RTL_ID` = '26';


UPDATE REF_LOOKUP_TYPE 
SET 
    `RTL_CODE` = 'RF',
    `RTL_NAME` = 'Regular,Full'
WHERE
    `RTL_ID` = '3';

 
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'FULL',
    `RL_NAME` = 'Full',
    `RL_SHORT_CODE` = 'FULL'
WHERE
    `RL_ID` = '31';
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'TO-GO',
    `RL_NAME` = 'To-Go',
    `RL_SHORT_CODE` = '*TG'
WHERE
    `RL_ID` = '100';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'N'
WHERE
    `RL_ID` = '1';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'REG'
WHERE
    `RL_ID` = '30';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'T'
WHERE
    `RL_ID` = '101';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'A'
WHERE
    `RL_ID` = '102';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'E'
WHERE
    `RL_ID` = '103';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'S'
WHERE
    `RL_ID` = '104';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'L'
WHERE
    `RL_ID` = '105';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'C'
WHERE
    `RL_ID` = '106';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'M'
WHERE
    `RL_ID` = '107';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'KM'
WHERE
    `RL_ID` = '108';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'MNT'
WHERE
    `RL_ID` = '109';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'AJ'
WHERE
    `RL_ID` = '110';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'ME'
WHERE
    `RL_ID` = '111';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'HM'
WHERE
    `RL_ID` = '112';
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'KADAK',
    `RL_NAME` = 'Kadak',
    `RL_SHORT_CODE` = 'KDK'
WHERE
    `RL_ID` = '113';
  
UPDATE REF_LOOKUP SET RL_STATUS='INACTIVE'
WHERE
    `RL_ID` = '114';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'HNY'
WHERE
    `RL_ID` = '115';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'ML'
WHERE
    `RL_ID` = '116';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'KES'
WHERE
    `RL_ID` = '117';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'W2S'
WHERE
    `RL_ID` = '2602';
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'TO-GO',
    `RL_NAME` = 'To-Go',
    `RL_SHORT_CODE` = '*TG'
WHERE
    `RL_ID` = '2501';
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'TO-GO',
    `RL_NAME` = 'To-Go',
    `RL_SHORT_CODE` = '*TG'
WHERE
    `RL_ID` = '2601';
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'HoneyGingerMayo',
    `RL_NAME` = 'Honey Ginger Mayo',
    `RL_SHORT_CODE` = 'HGM'
WHERE
    `RL_ID` = '2603';
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2604', '26', 'RedChilliMayo', 'Red Chilli Mayo', 'RCM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2605', '26', 'GingerChilliMayo', 'Ginger Chilli Mayo', 'GCM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2606', '26', 'HariChutni', 'Hari Chutni', 'HC');
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'TO-GO',
    `RL_NAME` = 'To-Go',
    `RL_SHORT_CODE` = '*TG'
WHERE
    `RL_ID` = '2911';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'BM'
WHERE
    `RL_ID` = '2910';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'BB'
WHERE
    `RL_ID` = '2909';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'VP'
WHERE
    `RL_ID` = '2908';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'POH'
WHERE
    `RL_ID` = '2907';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'EB'
WHERE
    `RL_ID` = '2906';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'KP'
WHERE
    `RL_ID` = '2905';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'BS'
WHERE
    `RL_ID` = '2904';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'ALO'
WHERE
    `RL_ID` = '2903';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = '2M'
WHERE
    `RL_ID` = '2902';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'CUT'
WHERE
    `RL_ID` = '2901';
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'TO-GO',
    `RL_NAME` = 'To-Go',
    `RL_SHORT_CODE` = '*TG'
WHERE
    `RL_ID` = '2808';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'W2S'
WHERE
    `RL_ID` = '2807';
UPDATE REF_LOOKUP 
SET 
    `RL_ID` = '2806',
    `RTL_ID` = '28',
    `RL_CODE` = 'HoneyGingerMayo',
    `RL_NAME` = 'Honey Ginger Mayo',
    `RL_SHORT_CODE` = 'HGM'
WHERE
    `RL_ID` = '2806';
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2809', '28', 'RedChilliMayo', 'Red Chilli Mayo', 'RCM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2810', '28', 'GingerChilliMayo', 'Ginger Chilli Mayo', 'GCM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2811', '28', 'HariChutni', 'Hari Chutni', 'HC');
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'SCC'
WHERE
    `RL_ID` = '2805';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'SC'
WHERE
    `RL_ID` = '2804';
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'KadhaiPaneerWrap',
    `RL_NAME` = 'Kadhai Paneer Wrap',
    `RL_SHORT_CODE` = 'WKP'
WHERE
    `RL_ID` = '2803';
UPDATE REF_LOOKUP 
SET 
    `RL_NAME` = 'Butter-Chicken Wrap',
    `RL_SHORT_CODE` = 'WBC'
WHERE
    `RL_ID` = '2802';
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'CUT'
WHERE
    `RL_ID` = '2801';
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'TO-GO',
    `RL_NAME` = 'To-Go',
    `RL_SHORT_CODE` = '*TG'
WHERE
    `RL_ID` = '2701';
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2607', '26', 'NOTOAST', 'No Toast', 'NT');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2608', '26', 'NOBUTTER', 'No Butter', 'NB');
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'BadiKetli',
    `RL_NAME` = 'Badi Ketli'
WHERE
    `RL_ID` = '902';
UPDATE REF_LOOKUP 
SET 
    `RL_CODE` = 'ChotiKetli',
    `RL_NAME` = 'Choti Ketli'
WHERE
    `RL_ID` = '903';
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2502', '25', 'WithoutIce', 'Without Ice', 'WOI');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2503', '25', 'NoSugar', 'No Sugar', 'NS');

INSERT INTO REF_LOOKUP_TYPE
(`RTL_ID`,
`RTL_GROUP`,
`RTL_CODE`,
`RTL_NAME`)
VALUES
(22,
'ADDONS',
'OTHER_TEAS',
'Other Teas');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2201', '22', 'TO-GO', 'To-Go', '*TG');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2202', '22', 'NoSugar', 'No Sugar', 'NS');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2203', '22', 'Honey', 'Honey', 'HNY');

INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2301', '23', 'TO-GO', 'To-Go', '*TG');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2302', '23', 'HoneyGingerMayo', 'Honey Ginger Mayo', 'HGM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2303', '23', 'RedChilliMayo', 'Red Chilli Mayo', 'RCM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2304', '23', 'GingerChilliMayo', 'Ginger Chilli Mayo', 'GCM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2305', '23', 'HariChutni', 'Har iChutni', 'HC');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2306', '23', 'NOTOAST', 'No Toast', 'NT');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2307', '23', 'NOBUTTER', 'No Butter', 'NB');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2308', '23', 'SandwichToWrap', 'Sandwich To Wrap', 'S2W');


INSERT INTO REF_LOOKUP_TYPE (`RTL_ID`, `RTL_GROUP`, `RTL_CODE`, `RTL_NAME`) VALUES ('2', 'DIMENSION', 'RLCKBK', 'Reg,Full,CK,BK');
INSERT INTO REF_LOOKUP_TYPE (`RTL_ID`, `RTL_GROUP`, `RTL_CODE`, `RTL_NAME`) VALUES ('11', 'DIMENSION', 'NoneCKBK', 'None,CK,BK');

INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2401', '24', 'TO-GO', 'To-Go', '*TG');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2402', '24', 'HoneyGingerMayo', 'Honey Ginger Mayo', 'HGM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2403', '24', 'RedChilliMayo', 'Red Chilli Mayo', 'RCM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2404', '24', 'GingerChilliMayo', 'Ginger Chilli Mayo', 'GCM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2405', '24', 'HariChutni', 'Hari Chutni', 'HC');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2406', '24', 'NOTOAST', 'No Toast', 'NT');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2407', '24', 'NOBUTTER', 'No Butter', 'NB');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2812', '28', 'DesiChai', 'Desi Chai', 'DC');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2912', '29', 'DesiChai', 'Desi Chai', 'DC');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2914', '29', 'HoneyGingerMayo', 'Honey Ginger Mayo', 'HGM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2915', '29', 'RedChilliMayo', 'Red Chilli Mayo', 'RCM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2916', '29', 'GingerChilliMayo', 'Ginger Chilli Mayo', 'GCM');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('2917', '29', 'HariChutni', 'Hari Chutni', 'HC');

INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('1101', '11', 'None', 'None', 'N');
UPDATE REF_LOOKUP 
SET 
    `RL_SHORT_CODE` = 'N'
WHERE
    `RL_ID` = '1';
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('1102', '11', 'ChotiKetli', 'Choti Ketli', 'CK');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('1103', '11', 'BadiKetli', 'Badi Ketli', 'BK');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('21', '2', 'REGULAR', 'Regular', 'REG');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('22', '2', 'FULL', 'Full', 'FUL');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('23', '2', 'CHOTIKETLI', 'Choti Ketli', 'CK');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`) VALUES ('24', '2', 'BADIKETLI', 'Badi Ketli', 'BK');

UPDATE PRODUCT_DETAIL 
SET 
    `PRODUCT_NAME` = 'Desi Chai',
    `PRODUCT_DESCRIPTION` = '-----',
    `DIMENSION_CODE` = '2'
WHERE
    `PRODUCT_ID` = '10';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '24'
WHERE
    `PRODUCT_ID` = '690';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '24'
WHERE
    `PRODUCT_ID` = '680';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '24'
WHERE
    `PRODUCT_ID` = '670';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '24'
WHERE
    `PRODUCT_ID` = '660';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '24'
WHERE
    `PRODUCT_ID` = '650';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '24'
WHERE
    `PRODUCT_ID` = '640';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '24'
WHERE
    `PRODUCT_ID` = '630';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '24'
WHERE
    `PRODUCT_ID` = '620';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '24'
WHERE
    `PRODUCT_ID` = '610';
UPDATE PRODUCT_DETAIL 
SET 
    `PRODUCT_NAME` = 'Kadhai Paneer Wrap'
WHERE
    `PRODUCT_ID` = '590';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '23'
WHERE
    `PRODUCT_ID` = '500';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '23'
WHERE
    `PRODUCT_ID` = '510';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '23'
WHERE
    `PRODUCT_ID` = '520';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '23'
WHERE
    `PRODUCT_ID` = '530';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '23'
WHERE
    `PRODUCT_ID` = '540';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '23'
WHERE
    `PRODUCT_ID` = '550';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '23'
WHERE
    `PRODUCT_ID` = '560';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '25'
WHERE
    `PRODUCT_ID` = '440';
UPDATE PRODUCT_DETAIL 
SET 
    `PRODUCT_NAME` = 'LoPCHU',
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '260';
UPDATE PRODUCT_DETAIL 
SET 
    `PRODUCT_NAME` = 'Darjeeling Muscatel',
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '250';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '290';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '280';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '270';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '240';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '230';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '220';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '210';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '200';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '190';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '180';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '170';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '160';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '150';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '140';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '130';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '120';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '110';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '100';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '90';
UPDATE PRODUCT_DETAIL 
SET 
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '80';
UPDATE PRODUCT_DETAIL 
SET 
    `PRODUCT_NAME` = 'God\'s Chai',
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '60';
UPDATE PRODUCT_DETAIL 
SET 
    `DIMENSION_CODE` = '11',
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '30';
UPDATE PRODUCT_DETAIL 
SET 
    `DIMENSION_CODE` = '11',
    `ADDITIONAL_ITEM_TYPES` = '22'
WHERE
    `PRODUCT_ID` = '20';
INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`) VALUES ('11', 'Desi Doodh Patti', '-----', '100', '5', '501', 'ACTIVE', '2013-01-01', '9999-12-01', 'CH1011097', '2', 'NET_PRICE', '4');
INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`) VALUES ('861', 'Kulhad', '-----', '100', '10', '1003', 'ACTIVE', '2013-01-01', '9999-12-01', 'CH1011098', '1', 'NET_PRICE', '27');
INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`) VALUES ('12', 'Desi Doodh Kum', 'Less Milk More Water', '100', '5', '501', 'ACTIVE', '2013-01-01', '9999-12-01', 'CH1011099', '2', 'NET_PRICE', '4');
UPDATE PRODUCT_DETAIL 
SET 
    `PRODUCT_NAME` = 'Badi Ketli',
    `ADDITIONAL_ITEM_TYPES` = '27'
WHERE
    `PRODUCT_ID` = '840';
UPDATE PRODUCT_DETAIL 
SET 
    `PRODUCT_NAME` = 'Choti Ketli',
    `ADDITIONAL_ITEM_TYPES` = '27'
WHERE
    `PRODUCT_ID` = '850';
UPDATE PRODUCT_DETAIL 
SET 
    `DIMENSION_CODE` = '11'
WHERE
    `PRODUCT_ID` = '40';
UPDATE PRODUCT_DETAIL 
SET 
    `PRODUCT_NAME` = 'Desi Paani Kum'
WHERE
    `PRODUCT_ID` = '50';
    
delete from UNIT_PRODUCT_PRICING where UNIT_PROD_REF_ID IN (
select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING
where PRODUCT_ID =  70)
;
delete from UNIT_PRODUCT_MAPPING
where PRODUCT_ID =  70;

DELETE from UNIT_PRODUCT_PRICING where UNIT_PROD_REF_ID IN (
select upm.UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING upm where upm.PRODUCT_ID = 20)
AND DIMENSION_CODE = 31;

update PRODUCT_DETAIL pd, UNIT_PRODUCT_MAPPING upm, UNIT_PRODUCT_PRICING upp
set upp.DIMENSION_CODE = 1
where pd.PRODUCT_ID = upm.PRODUCT_ID and upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and pd.PRODUCT_TYPE = 5 and upp.DIMENSION_CODE = 30 and pd.PRODUCT_ID = 20;

update PRODUCT_DETAIL
set DIMENSION_CODE = 2
where PRODUCT_ID NOT IN (20,
30,80) and PRODUCT_TYPE = 5
; 

update PRODUCT_DETAIL pd, UNIT_PRODUCT_MAPPING upm, UNIT_PRODUCT_PRICING upp
set upp.DIMENSION_CODE = 21
where pd.PRODUCT_ID = upm.PRODUCT_ID and upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and pd.PRODUCT_TYPE = 5 and upp.DIMENSION_CODE = 30;

update PRODUCT_DETAIL pd, UNIT_PRODUCT_MAPPING upm, UNIT_PRODUCT_PRICING upp
set upp.DIMENSION_CODE = 22
where pd.PRODUCT_ID = upm.PRODUCT_ID and upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and pd.PRODUCT_TYPE = 5 and upp.DIMENSION_CODE = 31;

update PRODUCT_DETAIL
set DIMENSION_CODE = 11
where PRODUCT_ID = 80;

update PRODUCT_DETAIL pd, UNIT_PRODUCT_MAPPING upm, UNIT_PRODUCT_PRICING upp
set upp.DIMENSION_CODE = 1101
where pd.PRODUCT_ID = upm.PRODUCT_ID and upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and pd.PRODUCT_TYPE = 5 and upp.DIMENSION_CODE = 1 and pd.PRODUCT_ID = 80;


INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 11, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where PRODUCT_ID = 10;

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 12, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where PRODUCT_ID = 10;

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 861, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where PRODUCT_ID = 10;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upm2.DIMENSION_CODE, upm2.PRICE from UNIT_PRODUCT_MAPPING upm1
LEFT OUTER JOIN (
select upm.UNIT_ID, upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm where upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID and upm.PRODUCT_ID  = 10
) upm2
ON  upm1.UNIT_ID = upm2.UNIT_ID
where upm1.PRODUCT_ID  = 11;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upm2.DIMENSION_CODE, upm2.PRICE from UNIT_PRODUCT_MAPPING upm1
LEFT OUTER JOIN (
select upm.UNIT_ID, upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm where upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID and upm.PRODUCT_ID  = 10
) upm2
ON  upm1.UNIT_ID = upm2.UNIT_ID
where upm1.PRODUCT_ID  = 12;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upm2.DIMENSION_CODE, 20 from UNIT_PRODUCT_MAPPING upm1
LEFT OUTER JOIN (
select upm.UNIT_ID, upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm where upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID and upm.PRODUCT_ID  = 860
) upm2
ON  upm1.UNIT_ID = upm2.UNIT_ID
where upm1.PRODUCT_ID  = 861;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, PRICE)
select upm.UNIT_PROD_REF_ID, 23, 2*upp.PRICE from PRODUCT_DETAIL pd,UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm 
where pd.PRODUCT_ID = upm.PRODUCT_ID and upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID 
and pd.DIMENSION_CODE =2 and upp.DIMENSION_CODE = 21 and pd.PRODUCT_TYPE = 5
;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, PRICE)
select upm.UNIT_PROD_REF_ID, 24, 5*upp.PRICE from PRODUCT_DETAIL pd,UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm 
where pd.PRODUCT_ID = upm.PRODUCT_ID and upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID 
and pd.DIMENSION_CODE =2 and upp.DIMENSION_CODE = 21 and pd.PRODUCT_TYPE = 5
;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, PRICE)
select upm.UNIT_PROD_REF_ID, 23, 2 * upp.PRICE from PRODUCT_DETAIL pd,UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm 
where pd.PRODUCT_ID = upm.PRODUCT_ID and upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID 
and pd.DIMENSION_CODE =11 and upp.DIMENSION_CODE = 1101 and pd.PRODUCT_TYPE = 5;

INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, PRICE)
select upm.UNIT_PROD_REF_ID, 24, 5*upp.PRICE from PRODUCT_DETAIL pd,UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm 
where pd.PRODUCT_ID = upm.PRODUCT_ID and upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID 
and pd.DIMENSION_CODE =11 and upp.DIMENSION_CODE = 1101 and pd.PRODUCT_TYPE = 5;











