
ALTER TABLE KETTLE_DEV.CASH_CARD_DETAIL 
ADD COLUMN CASH_CARD_OFFER_ID INTEGER NULL;

ALTER TABLE KETTLE_DEV.CASH_CARD_DETAIL 
ADD COLUMN CARD_INITIAL_OFFER DECIMAL(10,2) NULL;

CREATE TABLE KETTLE_DEV.CASH_CARD_OFFER(
CASH_CARD_OFFER_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
UNIT_ID INTEGER NOT NULL,
CARD_DESCRIPTION VARCHAR(200) NOT NULL,
CARD_DENOMINATION DECIMAL(10,2) NOT NULL,
OFFER_PERCENTAGE DECIMAL(10,2) NOT NULL,
START_DATE DATE NOT NULL,
END_DATE DATE NOT NULL,
OFFER_STATUS VARCHAR(15),
CREATED_BY VARCHAR(50) NOT NULL,
CREATION_TIME TIMESTAMP NOT NULL,
CANCELLED_BY VARCHAR(50) NULL,
CANCELLATION_TIME TIMESTAMP NULL
);

CREATE INDEX UNIT_ID_CASH_CARD_OFFER ON KETTLE_DEV.CASH_CARD_OFFER(UNIT_ID) USING BTREE;
CREATE INDEX CARD_DENOMINATION_CASH_CARD_OFFER ON KETTLE_DEV.CASH_CARD_OFFER(CARD_DENOMINATION) USING BTREE;
CREATE INDEX START_DATE_CASH_CARD_OFFER ON KETTLE_DEV.CASH_CARD_OFFER(START_DATE) USING BTREE;
CREATE INDEX END_DATE_CASH_CARD_OFFER ON KETTLE_DEV.CASH_CARD_OFFER(END_DATE) USING BTREE;
CREATE INDEX OFFER_STATUS_CASH_CARD_OFFER ON KETTLE_DEV.CASH_CARD_OFFER(OFFER_STATUS) USING BTREE;
