DROP TABLE IF EXISTS SALES_REPORT_DATA;
CREATE TABLE `SALES_REPORT_DATA` (
  `UNIT_ID` INTEGER NOT NULL DEFAULT '0',
  `UNIT_NAME` varchar(255) NOT NULL,
  `BUSINESS_DATE` DATE NOT NULL,
  `TOTAL_TICKETS` INTEGER NOT NULL DEFAULT '0',
  `GMV_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `NET_SALES_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `PROMOTIONAL_DISCOUNT` DECIMAL(10,2) DEFAULT NULL,
  `PROMOTIONAL_TICKETS` INTEGER DEFAULT NULL,
  `FLAT_DISCOUNT_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `DISCOUNT_TICKETS` INTEGER DEFAULT NULL,
  `COMPLIMENTARY_TICKETS` INTEGER DEFAULT NULL,
  `NET_TICKETS` INTEGER DEFAULT NULL,
  `CAFE_GMV_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `CAFE_NET_SALES_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `CAFE_PROMOTIONAL_DISCOUNT` DECIMAL(10,2) DEFAULT NULL,
  `CAFE_PROMOTIONAL_TICKETS` INTEGER DEFAULT NULL,
  `CAFE_DISCOUNT_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `CAFE_DISCOUNT_TICKETS` INTEGER DEFAULT NULL,
  `CAFE_COMPLIMENTARY_TICKETS` INTEGER DEFAULT NULL,
  `CAFE_NET_TICKETS` INTEGER DEFAULT NULL,
  `COD_GMV_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `COD_NET_SALES_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `COD_PROMOTIONAL_DISCOUNT` DECIMAL(10,2) DEFAULT NULL,
  `COD_PROMOTIONAL_TICKETS` INTEGER DEFAULT NULL,
  `COD_DISCOUNT_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `COD_DISCOUNT_TICKETS` INTEGER DEFAULT NULL,
  `COD_COMPLIMENTARY_TICKETS` INTEGER DEFAULT NULL,
  `COD_NET_TICKETS` INTEGER DEFAULT NULL,
  `TAKE_AWAY_GMV_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `TAKE_AWAY_NET_SALES_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `TAKE_AWAY_PROMOTIONAL_DISCOUNT` DECIMAL(10,2) DEFAULT NULL,
  `TAKE_AWAY_PROMOTIONAL_TICKETS` INTEGER DEFAULT NULL,
  `TAKE_AWAY_DISCOUNT_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `TAKE_AWAY_DISCOUNT_TICKETS` INTEGER DEFAULT NULL,
  `TAKE_AWAY_COMPLIMENTARY_TICKETS` INTEGER DEFAULT NULL,
  `TAKE_AWAY_NET_TICKETS` INTEGER DEFAULT NULL,
  `NEW_CUSTOMERS` INTEGER DEFAULT NULL,
  `OLD_CUSTOMERS` INTEGER DEFAULT NULL,
  `TICKETS_WITH_CUSTOMERS` INTEGER DEFAULT NULL,
  `TICKETS_WITH_NO_CUSTOMERS` INTEGER DEFAULT NULL,
  `AVG_BILLING_TIME` DECIMAL(10,2) DEFAULT NULL,
  `CAFE_NEW_CUSTOMERS` INTEGER DEFAULT NULL,
  `CAFE_OLD_CUSTOMERS` INTEGER DEFAULT NULL,
  `CAFE_TICKETS_WITH_CUSTOMERS` INTEGER DEFAULT NULL,
  `CAFE_TICKETS_WITH_NO_CUSTOMERS` INTEGER DEFAULT NULL,
  `COD_NEW_CUSTOMERS` INTEGER DEFAULT NULL,
  `COD_OLD_CUSTOMERS` INTEGER DEFAULT NULL,
  `COD_TICKETS_WITH_CUSTOMERS` INTEGER DEFAULT NULL,
  `COD_TICKETS_WITH_NO_CUSTOMERS` INTEGER DEFAULT NULL,
  `TAKE_AWAY_NEW_CUSTOMERS` INTEGER DEFAULT NULL,
  `TAKE_AWAY_OLD_CUSTOMERS` INTEGER DEFAULT NULL,
  `TAKE_AWAY_TICKETS_WITH_CUSTOMERS` INTEGER DEFAULT NULL,
  `TAKE_AWAY_TICKETS_WITH_NO_CUSTOMERS` INTEGER DEFAULT NULL,
  `COMPLIMENTARY_AMOUNT` DECIMAL(10,2) DEFAULT NULL,
  `BAKERY_QUANTITY` INTEGER DEFAULT NULL,
  `BAKERY_TOTAL_SALES` DECIMAL(10,2) DEFAULT NULL,
  `COLD_BEVERAGES_QUANTITY` INTEGER DEFAULT NULL,
  `COLD_BEVERAGES_TOTAL_SALES` DECIMAL(10,2) DEFAULT NULL,
  `COMBOS_QUANTITY` INTEGER DEFAULT NULL,
  `COMBOS_TOTAL_SALES` DECIMAL(10,2) DEFAULT NULL,
  `FOOD_QUANTITY` INTEGER DEFAULT NULL,
  `FOOD_TOTAL_SALES` DECIMAL(10,2) DEFAULT NULL,
  `HOT_BEVERAGES_QUANTITY` INTEGER DEFAULT NULL,
  `HOT_BEVERAGES_TOTAL_SALES` DECIMAL(10,2) DEFAULT NULL,
  `MERCHANDISE_QUANTITY` INTEGER DEFAULT NULL,
  `MERCHANDISE_TOTAL_SALES` DECIMAL(10,2) DEFAULT NULL,
  `OTHERS_QUANTITY` INTEGER DEFAULT NULL,
  `OTHERS_TOTAL_SALES` DECIMAL(10,2) DEFAULT NULL
) 
;

DROP PROCEDURE IF EXISTS SP_INSERT_SALES_DATA;
delimiter $$
CREATE PROCEDURE SP_INSERT_SALES_DATA
(IN TILL_DATE DATE)
proc_label : BEGIN

DECLARE LAST_DATE DATE;
select case when max(BUSINESS_DATE) is null then '2015-08-26' else max(BUSINESS_DATE) end into LAST_DATE from SALES_REPORT_DATA;

IF LAST_DATE IS NOT NULL AND LAST_DATE > TILL_DATE
THEN 
	LEAVE proc_label;
END IF;

label_loop : LOOP
if  TILL_DATE >= LAST_DATE then
delete from SALES_REPORT_DATA where BUSINESS_DATE = LAST_DATE;
INSERT INTO SALES_REPORT_DATA(UNIT_ID,UNIT_NAME,BUSINESS_DATE,TOTAL_TICKETS,GMV_AMOUNT,NET_SALES_AMOUNT,PROMOTIONAL_DISCOUNT,PROMOTIONAL_TICKETS,FLAT_DISCOUNT_AMOUNT,DISCOUNT_TICKETS,COMPLIMENTARY_TICKETS,NET_TICKETS,CAFE_GMV_AMOUNT,CAFE_NET_SALES_AMOUNT,CAFE_PROMOTIONAL_DISCOUNT,CAFE_PROMOTIONAL_TICKETS,CAFE_DISCOUNT_AMOUNT,CAFE_DISCOUNT_TICKETS,CAFE_COMPLIMENTARY_TICKETS,CAFE_NET_TICKETS,COD_GMV_AMOUNT,COD_NET_SALES_AMOUNT,COD_PROMOTIONAL_DISCOUNT,COD_PROMOTIONAL_TICKETS,COD_DISCOUNT_AMOUNT,COD_DISCOUNT_TICKETS,COD_COMPLIMENTARY_TICKETS,COD_NET_TICKETS,TAKE_AWAY_GMV_AMOUNT,TAKE_AWAY_NET_SALES_AMOUNT,TAKE_AWAY_PROMOTIONAL_DISCOUNT,TAKE_AWAY_PROMOTIONAL_TICKETS,TAKE_AWAY_DISCOUNT_AMOUNT,TAKE_AWAY_DISCOUNT_TICKETS,TAKE_AWAY_COMPLIMENTARY_TICKETS,TAKE_AWAY_NET_TICKETS,NEW_CUSTOMERS,OLD_CUSTOMERS,TICKETS_WITH_CUSTOMERS,TICKETS_WITH_NO_CUSTOMERS,AVG_BILLING_TIME,CAFE_NEW_CUSTOMERS,CAFE_OLD_CUSTOMERS,CAFE_TICKETS_WITH_CUSTOMERS,CAFE_TICKETS_WITH_NO_CUSTOMERS,COD_NEW_CUSTOMERS,COD_OLD_CUSTOMERS,COD_TICKETS_WITH_CUSTOMERS,COD_TICKETS_WITH_NO_CUSTOMERS,TAKE_AWAY_NEW_CUSTOMERS,TAKE_AWAY_OLD_CUSTOMERS,TAKE_AWAY_TICKETS_WITH_CUSTOMERS,TAKE_AWAY_TICKETS_WITH_NO_CUSTOMERS,COMPLIMENTARY_AMOUNT,BAKERY_QUANTITY,BAKERY_TOTAL_SALES,COLD_BEVERAGES_QUANTITY,COLD_BEVERAGES_TOTAL_SALES,COMBOS_QUANTITY,COMBOS_TOTAL_SALES,FOOD_QUANTITY,FOOD_TOTAL_SALES,HOT_BEVERAGES_QUANTITY,HOT_BEVERAGES_TOTAL_SALES,MERCHANDISE_QUANTITY,MERCHANDISE_TOTAL_SALES,OTHERS_QUANTITY,OTHERS_TOTAL_SALES
)
SELECT 
    summary.UNIT_ID,summary.UNIT_NAME,summary.BUSINESS_DATE,
    summary.TOTAL_TICKETS,summary.GMV_AMOUNT,summary.NET_SALES_AMOUNT,
    summary.PROMOTIONAL_DISCOUNT,summary.PROMOTIONAL_TICKETS,
    summary.FLAT_DISCOUNT_AMOUNT,summary.DISCOUNT_TICKETS,
    summary.COMPLIMENTARY_TICKETS,summary.NET_TICKETS,summary.CAFE_GMV_AMOUNT,
    summary.CAFE_NET_SALES_AMOUNT,summary.CAFE_PROMOTIONAL_DISCOUNT,
    summary.CAFE_PROMOTIONAL_TICKETS,summary.CAFE_DISCOUNT_AMOUNT,
    summary.CAFE_DISCOUNT_TICKETS,summary.CAFE_COMPLIMENTARY_TICKETS,
    summary.CAFE_NET_TICKETS,summary.COD_GMV_AMOUNT,summary.COD_NET_SALES_AMOUNT,
    summary.COD_PROMOTIONAL_DISCOUNT,summary.COD_PROMOTIONAL_TICKETS,
    summary.COD_DISCOUNT_AMOUNT,summary.COD_DISCOUNT_TICKETS,
    summary.COD_COMPLIMENTARY_TICKETS,summary.COD_NET_TICKETS,
    summary.TAKE_AWAY_GMV_AMOUNT,summary.TAKE_AWAY_NET_SALES_AMOUNT,
    summary.TAKE_AWAY_PROMOTIONAL_DISCOUNT,summary.TAKE_AWAY_PROMOTIONAL_TICKETS,
    summary.TAKE_AWAY_DISCOUNT_AMOUNT,summary.TAKE_AWAY_DISCOUNT_TICKETS,
    summary.TAKE_AWAY_COMPLIMENTARY_TICKETS,summary.TAKE_AWAY_NET_TICKETS,
    summary.NEW_CUSTOMERS,summary.OLD_CUSTOMERS,summary.TICKETS_WITH_CUSTOMERS,
    summary.TICKETS_WITH_NO_CUSTOMERS,summary.AVG_BILLING_TIME,summary.CAFE_NEW_CUSTOMERS,
    summary.CAFE_OLD_CUSTOMERS,summary.CAFE_TICKETS_WITH_CUSTOMERS,summary.CAFE_TICKETS_WITH_NO_CUSTOMERS,
    summary.COD_NEW_CUSTOMERS,summary.COD_OLD_CUSTOMERS,summary.COD_TICKETS_WITH_CUSTOMERS,
    summary.COD_TICKETS_WITH_NO_CUSTOMERS,summary.TAKE_AWAY_NEW_CUSTOMERS,
    summary.TAKE_AWAY_OLD_CUSTOMERS,summary.TAKE_AWAY_TICKETS_WITH_CUSTOMERS,
    summary.TAKE_AWAY_TICKETS_WITH_NO_CUSTOMERS,
    sales.COMPLIMENTARY_AMOUNT,
    sales.BAKERY_QUANTITY,
    sales.BAKERY_TOTAL_SALES,
    sales.COLD_BEVERAGES_QUANTITY,
    sales.COLD_BEVERAGES_TOTAL_SALES,
    sales.COMBOS_QUANTITY,
    sales.COMBOS_TOTAL_SALES,
    sales.FOOD_QUANTITY,
    sales.FOOD_TOTAL_SALES,
    sales.HOT_BEVERAGES_QUANTITY,
    sales.HOT_BEVERAGES_TOTAL_SALES,
    sales.MERCHANDISE_QUANTITY,
    sales.MERCHANDISE_TOTAL_SALES,
    sales.OTHERS_QUANTITY,
    sales.OTHERS_TOTAL_SALES
FROM
    (SELECT 
        ud.UNIT_ID,
            ud.UNIT_NAME,
            LAST_DATE BUSINESS_DATE,
            COUNT(*) TOTAL_TICKETS,
            SUM(od.TOTAL_AMOUNT) GMV_AMOUNT,
            SUM(od.TAXABLE_AMOUNT) NET_SALES_AMOUNT,
            SUM(od.PROMOTIONAL_DISCOUNT) PROMOTIONAL_DISCOUNT,
            SUM(CASE
                WHEN
                    od.PROMOTIONAL_DISCOUNT IS NOT NULL
                        AND od.PROMOTIONAL_DISCOUNT > '0.00'
                THEN
                    1
                ELSE 0
            END) PROMOTIONAL_TICKETS,
            SUM(od.DISCOUNT_AMOUNT) FLAT_DISCOUNT_AMOUNT,
            SUM(CASE
                WHEN
                    od.DISCOUNT_AMOUNT IS NOT NULL
                        AND od.DISCOUNT_AMOUNT > '0.00'
                THEN
                    1
                ELSE 0
            END) DISCOUNT_TICKETS,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT = '0.00' THEN 1
                ELSE 0
            END) COMPLIMENTARY_TICKETS,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) NET_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'CAFE' THEN od.TOTAL_AMOUNT
                ELSE 0.00
            END) CAFE_GMV_AMOUNT,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'CAFE' THEN od.TAXABLE_AMOUNT
                ELSE 0.00
            END) CAFE_NET_SALES_AMOUNT,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'CAFE' THEN od.PROMOTIONAL_DISCOUNT
                ELSE 0.00
            END) CAFE_PROMOTIONAL_DISCOUNT,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'CAFE'
                        AND od.PROMOTIONAL_DISCOUNT IS NOT NULL
                        AND od.PROMOTIONAL_DISCOUNT > '0.00'
                THEN
                    1
                ELSE 0
            END) CAFE_PROMOTIONAL_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'CAFE' THEN od.DISCOUNT_AMOUNT
                ELSE 0.00
            END) CAFE_DISCOUNT_AMOUNT,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'CAFE'
                        AND od.DISCOUNT_AMOUNT IS NOT NULL
                        AND od.DISCOUNT_AMOUNT > '0.00'
                THEN
                    1
                ELSE 0
            END) CAFE_DISCOUNT_TICKETS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'CAFE'
                        AND od.TOTAL_AMOUNT = '0.00'
                THEN
                    1
                ELSE 0
            END) CAFE_COMPLIMENTARY_TICKETS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'CAFE'
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END) CAFE_NET_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TOTAL_AMOUNT
                ELSE 0.00
            END) COD_GMV_AMOUNT,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0.00
            END) COD_NET_SALES_AMOUNT,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.PROMOTIONAL_DISCOUNT
                ELSE 0.00
            END) COD_PROMOTIONAL_DISCOUNT,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.PROMOTIONAL_DISCOUNT IS NOT NULL
                        AND od.PROMOTIONAL_DISCOUNT > '0.00'
                THEN
                    1
                ELSE 0
            END) COD_PROMOTIONAL_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.DISCOUNT_AMOUNT
                ELSE 0.00
            END) COD_DISCOUNT_AMOUNT,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.DISCOUNT_AMOUNT IS NOT NULL
                        AND od.DISCOUNT_AMOUNT > '0.00'
                THEN
                    1
                ELSE 0
            END) COD_DISCOUNT_TICKETS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.TOTAL_AMOUNT = '0.00'
                THEN
                    1
                ELSE 0
            END) COD_COMPLIMENTARY_TICKETS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END) COD_NET_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'TAKE_AWAY' THEN od.TOTAL_AMOUNT
                ELSE 0.00
            END) TAKE_AWAY_GMV_AMOUNT,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'TAKE_AWAY' THEN od.TAXABLE_AMOUNT
                ELSE 0.00
            END) TAKE_AWAY_NET_SALES_AMOUNT,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'TAKE_AWAY' THEN od.PROMOTIONAL_DISCOUNT
                ELSE 0.00
            END) TAKE_AWAY_PROMOTIONAL_DISCOUNT,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'TAKE_AWAY'
                        AND od.PROMOTIONAL_DISCOUNT IS NOT NULL
                        AND od.PROMOTIONAL_DISCOUNT > '0.00'
                THEN
                    1
                ELSE 0
            END) TAKE_AWAY_PROMOTIONAL_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'TAKE_AWAY' THEN od.DISCOUNT_AMOUNT
                ELSE 0.00
            END) TAKE_AWAY_DISCOUNT_AMOUNT,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'TAKE_AWAY'
                        AND od.DISCOUNT_AMOUNT IS NOT NULL
                        AND od.DISCOUNT_AMOUNT > '0.00'
                THEN
                    1
                ELSE 0
            END) TAKE_AWAY_DISCOUNT_TICKETS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'TAKE_AWAY'
                        AND od.TOTAL_AMOUNT = '0.00'
                THEN
                    1
                ELSE 0
            END) TAKE_AWAY_COMPLIMENTARY_TICKETS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'TAKE_AWAY'
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END) TAKE_AWAY_NET_TICKETS,
            SUM(CASE
                WHEN
                    ci.CUSTOMER_ID > 5
                        AND ci.ADD_TIME IS NOT NULL
                        AND ci.ADD_TIME >= DATE_ADD(LAST_DATE, INTERVAL 5 HOUR)
                THEN
                    1
                ELSE 0
            END) NEW_CUSTOMERS,
            SUM(CASE
                WHEN
                    ci.CUSTOMER_ID > 5
                        AND ci.ADD_TIME IS NOT NULL
                        AND ci.ADD_TIME < DATE_ADD(LAST_DATE, INTERVAL 5 HOUR)
                THEN
                    1
                ELSE 0
            END) OLD_CUSTOMERS,
            SUM(CASE
                WHEN ci.CUSTOMER_ID > 5 THEN 1
                ELSE 0
            END) TICKETS_WITH_CUSTOMERS,
            SUM(CASE
                WHEN ci.CUSTOMER_ID <= 5 THEN 1
                ELSE 0
            END) TICKETS_WITH_NO_CUSTOMERS,
            AVG(TIMESTAMPDIFF(SECOND, od.BILL_START_TIME, od.BILL_GENERATION_TIME)) AVG_BILLING_TIME,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'CAFE'
                        AND ci.CUSTOMER_ID > 5
                        AND ci.ADD_TIME IS NOT NULL
                        AND ci.ADD_TIME >= DATE_ADD(LAST_DATE, INTERVAL 5 HOUR)
                THEN
                    1
                ELSE 0
            END) CAFE_NEW_CUSTOMERS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'CAFE'
                        AND ci.CUSTOMER_ID > 5
                        AND ci.ADD_TIME IS NOT NULL
                        AND ci.ADD_TIME < DATE_ADD(LAST_DATE, INTERVAL 5 HOUR)
                THEN
                    1
                ELSE 0
            END) CAFE_OLD_CUSTOMERS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'CAFE'
                        AND ci.CUSTOMER_ID > 5
                THEN
                    1
                ELSE 0
            END) CAFE_TICKETS_WITH_CUSTOMERS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'CAFE'
                        AND ci.CUSTOMER_ID <= 5
                THEN
                    1
                ELSE 0
            END) CAFE_TICKETS_WITH_NO_CUSTOMERS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND ci.CUSTOMER_ID > 5
                        AND ci.ADD_TIME IS NOT NULL
                        AND ci.ADD_TIME >= DATE_ADD(LAST_DATE, INTERVAL 5 HOUR)
                THEN
                    1
                ELSE 0
            END) COD_NEW_CUSTOMERS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND ci.CUSTOMER_ID > 5
                        AND ci.ADD_TIME IS NOT NULL
                        AND ci.ADD_TIME < DATE_ADD(LAST_DATE, INTERVAL 5 HOUR)
                THEN
                    1
                ELSE 0
            END) COD_OLD_CUSTOMERS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND ci.CUSTOMER_ID > 5
                THEN
                    1
                ELSE 0
            END) COD_TICKETS_WITH_CUSTOMERS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND ci.CUSTOMER_ID <= 5
                THEN
                    1
                ELSE 0
            END) COD_TICKETS_WITH_NO_CUSTOMERS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'TAKE_AWAY'
                        AND ci.CUSTOMER_ID > 5
                        AND ci.ADD_TIME IS NOT NULL
                        AND ci.ADD_TIME >= DATE_ADD(LAST_DATE, INTERVAL 5 HOUR)
                THEN
                    1
                ELSE 0
            END) TAKE_AWAY_NEW_CUSTOMERS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'TAKE_AWAY'
                        AND ci.CUSTOMER_ID > 5
                        AND ci.ADD_TIME IS NOT NULL
                        AND ci.ADD_TIME < DATE_ADD(LAST_DATE, INTERVAL 5 HOUR)
                THEN
                    1
                ELSE 0
            END) TAKE_AWAY_OLD_CUSTOMERS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'TAKE_AWAY'
                        AND ci.CUSTOMER_ID > 5
                THEN
                    1
                ELSE 0
            END) TAKE_AWAY_TICKETS_WITH_CUSTOMERS,
            SUM(CASE
                WHEN
                    od.ORDER_SOURCE = 'TAKE_AWAY'
                        AND ci.CUSTOMER_ID <= 5
                THEN
                    1
                ELSE 0
            END) TAKE_AWAY_TICKETS_WITH_NO_CUSTOMERS
    FROM
        ORDER_DETAIL od
    INNER JOIN UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT OUTER JOIN CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    LEFT OUTER JOIN CUSTOMER_ADDRESS_INFO cai ON cai.ADDRESS_ID = od.DELIVERY_ADDRESS
    WHERE
        od.ORDER_STATUS = 'SETTLED'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(LAST_DATE, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = LAST_DATE)
    GROUP BY ud.UNIT_NAME , BUSINESS_DATE
    ORDER BY ud.UNIT_ID , ud.UNIT_NAME) summary
        LEFT OUTER JOIN
    (SELECT 
        sales.UNIT_ID,
            sales.UNIT_NAME,
            sales.BUSINESS_DATE,
            SUM(sales.QUANTITY) QUANTITY,
            SUM(sales.TOTAL_SALES) TOTAL_SALES,
            SUM(sales.COMPLIMENTARY_AMOUNT) COMPLIMENTARY_AMOUNT,
            SUM(CASE
                WHEN sales.RTL_CODE = 'Bakery' THEN sales.QUANTITY
                ELSE 0
            END) BAKERY_QUANTITY,
            SUM(CASE
                WHEN sales.RTL_CODE = 'Bakery' THEN sales.TOTAL_SALES
                ELSE 0
            END) BAKERY_TOTAL_SALES,
            SUM(CASE
                WHEN sales.RTL_CODE = 'ColdBeverages' THEN sales.QUANTITY
                ELSE 0
            END) COLD_BEVERAGES_QUANTITY,
            SUM(CASE
                WHEN sales.RTL_CODE = 'ColdBeverages' THEN sales.TOTAL_SALES
                ELSE 0
            END) COLD_BEVERAGES_TOTAL_SALES,
            SUM(CASE
                WHEN sales.RTL_CODE = 'Combos' THEN sales.QUANTITY
                ELSE 0
            END) COMBOS_QUANTITY,
            SUM(CASE
                WHEN sales.RTL_CODE = 'Combos' THEN sales.TOTAL_SALES
                ELSE 0
            END) COMBOS_TOTAL_SALES,
            SUM(CASE
                WHEN sales.RTL_CODE = 'Food' THEN sales.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN sales.RTL_CODE = 'Food' THEN sales.TOTAL_SALES
                ELSE 0
            END) FOOD_TOTAL_SALES,
            SUM(CASE
                WHEN sales.RTL_CODE = 'HotBeverages' THEN sales.QUANTITY
                ELSE 0
            END) HOT_BEVERAGES_QUANTITY,
            SUM(CASE
                WHEN sales.RTL_CODE = 'HotBeverages' THEN sales.TOTAL_SALES
                ELSE 0
            END) HOT_BEVERAGES_TOTAL_SALES,
            SUM(CASE
                WHEN sales.RTL_CODE = 'Merchandise' THEN sales.QUANTITY
                ELSE 0
            END) MERCHANDISE_QUANTITY,
            SUM(CASE
                WHEN sales.RTL_CODE = 'Merchandise' THEN sales.TOTAL_SALES
                ELSE 0
            END) MERCHANDISE_TOTAL_SALES,
            SUM(CASE
                WHEN sales.RTL_CODE = 'Others' THEN sales.QUANTITY
                ELSE 0
            END) OTHERS_QUANTITY,
            SUM(CASE
                WHEN sales.RTL_CODE = 'Others' THEN sales.TOTAL_SALES
                ELSE 0
            END) OTHERS_TOTAL_SALES
    FROM
        (SELECT 
        ud.UNIT_ID,
            ud.UNIT_NAME,
            LAST_DATE BUSINESS_DATE,
            rtl.RTL_CODE,
            SUM(CASE
                WHEN AMOUNT_PAID <> '0.00' THEN oi.QUANTITY
                ELSE 0
            END) QUANTITY,
            SUM(CASE
                WHEN AMOUNT_PAID = '0.00' THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) COMPLIMENTARY_AMOUNT,
            SUM(oi.AMOUNT_PAID) TOTAL_SALES
    FROM
        ORDER_DETAIL od
    INNER JOIN UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
    INNER JOIN REF_LOOKUP_TYPE rtl ON rtl.RTL_ID = pd.PRODUCT_TYPE
    WHERE
        od.ORDER_STATUS = 'SETTLED'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(LAST_DATE, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = LAST_DATE)
    GROUP BY ud.UNIT_ID , ud.UNIT_NAME , BUSINESS_DATE , rtl.RTL_CODE) sales
    GROUP BY sales.UNIT_ID , sales.UNIT_NAME , sales.BUSINESS_DATE) sales ON sales.UNIT_ID = summary.UNIT_ID
        AND sales.BUSINESS_DATE = summary.BUSINESS_DATE;
        
        ELSE
			LEAVE label_loop;
        END IF;
		
        SET LAST_DATE = DATE_ADD(LAST_DATE, INTERVAL 1 DAY);
 
        
        END LOOP label_loop;

END$$

delimiter ;