INSERT INTO `ACTION_DETAIL` (`ACTION_DETAIL_ID`,`ACTION_CODE`,`APPLICATION_ID`,`ACTION_TYPE`,`ACTION_CATEGORY`,`ACTION_DESCRIPTION`,`ACTION_STATUS`) VALUES (259,'FSBCM',11,'MENU','VIEW','Forms service -> Batch Code Management -> show','ACTIVE');
INSERT INTO `ACTION_DETAIL` (`ACTION_DETAIL_ID`,`ACTION_CODE`,`APPLICATION_ID`,`ACTION_TYPE`,`ACTION_CATEGORY`,`ACTION_DESCRIPTION`,`ACTION_STATUS`) VALUES (260,'FSCL',11,'MENU','VIEW','Forms service ->Check_List -> show','ACTIVE');
INSERT INTO `ACTION_DETAIL` (`ACTION_DETAIL_ID`,`ACTION_CODE`,`APPLICATION_ID`,`ACTION_TYPE`,`ACTION_CATEGORY`,`ACTION_DESC<PERSON><PERSON>TION`,`ACTION_STATUS`) VALUES (261,'FSC<PERSON>',11,'SUBMENU','VIEW','Forms service ->Cafe_Check_List -> show','ACTIVE');
INSERT INTO `USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES ('Cafe_Checklist', 'Cafe_Checklist', 'ACTIVE');
INSERT INTO `ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('50', '260', 'ACTIVE', '120063', '2020-07-16 00:00:00');
INSERT INTO `ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('50', '261', 'ACTIVE', '120063', '2020-07-16 00:00:00');