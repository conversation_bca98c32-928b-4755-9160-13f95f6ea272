DROP TABLE IF EXISTS UNIT_EXPENSE_DETAIL;
CREATE TABLE UNIT_EXPENSE_DETAIL
(
UNIT_EXPENSE_DETAIL_ID INT PRIMARY KEY AUTO_INCREMENT,
EXPENSE_UPDATE_EVENT_ID INT NOT NULL,
UNIT_ID INT NOT NULL,
UNIT_NAME VARCHAR(100),
ENTRY_TYPE VARCHAR(10) NOT NULL,
ENTRY_YEAR INT NOT NULL,
ITERATION_NUMBER INT NOT NULL,
EXPENSE_STATUS VARCHAR(10) NOT NULL DEFAULT 'ACTIVE',
UPDATED_BY VARCHAR(50) NOT NULL,
LAST_UPDATE_TIME TIMESTAMP NOT NULL,
MANPOWER DECIMAL(10,2) NULL,
GMV_AMOUNT DECIMAL(10,2) NULL,
NET_SALES_AMOUNT DECIMAL(10,2) NULL,
NET_TICKETS INT NOT NULL,
TOTAL_TICKETS INT NOT NULL,
NET_DISCOUNT DECIMAL(10,2) NULL,
COGS DECIMAL(10,2) NULL,
CONSUMABLES_AND_UTILITIES DECIMAL(10,2) NULL,
MARKETING_AND_SAMPLING DECIMAL(10,2) NULL,
UNSATISFIED_CUSTOMER_COST DECIMAL(10,2) NULL,
EMPLOYEE_MEAL DECIMAL(10,2) NULL,
WASTAGE_AND_EXPIRED DECIMAL(10,2) NULL,
DELIVERY_COST DECIMAL(10,2) NULL,
ELECTRICITY DECIMAL(10,2) NULL,
WATER DECIMAL(10,2) NULL,
DG_RENT DECIMAL(10,2) NULL,
DG_CHARGES DECIMAL(10,2) NULL,
CREDIT_CARD_CHARGES DECIMAL(10,2) NULL,
AMEX_CARD_CHARGES DECIMAL(10,2) NULL,
SODEXO_CHARGES DECIMAL(10,2) NULL,
TKT_RESTAURANT_CHARGES DECIMAL(10,2) NULL,
CHANNEL_PARTNER DECIMAL(10,2) NULL,
SCM_RENTEL DECIMAL(10,2) NULL,
EDC_MACHINE DECIMAL(10,2) NULL,
FREIGHT_OUTWARD DECIMAL(10,2) NULL,
CONVENYANCE DECIMAL(10,2) NULL,
STAFF_WELFARE DECIMAL(10,2) NULL,
CHANGE_COMMISSION DECIMAL(10,2) NULL,
COURIER DECIMAL(10,2) NULL,
PRINTING_AND_STATIONARY DECIMAL(10,2) NULL,
MISC_EXP DECIMAL(10,2) NULL,
PARKING_CHARGES DECIMAL(10,2) NULL,
CLEANING_CHARGES DECIMAL(10,2) NULL,
NEWSPAPER DECIMAL(10,2) NULL,
RENT DECIMAL(10,2) NULL,
FIXED_RENT DECIMAL(10,2) NULL,
RENT_PERCENTAGE DECIMAL(10,2) NULL,
CAM_CAHRGES DECIMAL(10,2) NULL,
INTERNET DECIMAL(10,2) NULL,
TELEPHONE DECIMAL(10,2) NULL,
OPS_COST_TOTAL DECIMAL(10,2) NULL,
OPS_COST_PERCENTAGE DECIMAL(10,2) NULL,
KITCHEN_COST_TOTAL DECIMAL(10,2) NULL,
KITCHEN_COST_PERCENTAGE DECIMAL(10,2) NULL,
REPAIR_AND_MAINTENANCE_MINOR DECIMAL(10,2) NULL,
REPAIR_AND_MAINTENANCE_MAJOR  DECIMAL(10,2) NULL,
MANUAL_ADJUSTMENT  DECIMAL(10,2) NULL,
TOTAL_COST  DECIMAL(10,2) NULL,
EBIDTA_PERCENTAGE  DECIMAL(10,2) NULL,
COMMENTS  VARCHAR(500) NULL
);

DROP TABLE IF EXISTS EXPENSE_UPDATE_EVENT_DATA;
CREATE TABLE EXPENSE_UPDATE_EVENT_DATA(
EVENT_ID INT PRIMARY KEY AUTO_INCREMENT,
ADDED_BY_USER_ID INT NOT NULL,
ADDED_BY_USER_NAME VARCHAR(100),
UPDATED_BY_USER_ID INT NOT NULL,
UPDATED_BY_USER_NAME VARCHAR(100),
EVENT_TIMESTAMP TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
EVENT_DESCRIPTION VARCHAR(10000),
ENTRY_TYPE VARCHAR(10) NOT NULL,
ENTRY_YEAR INT NOT NULL,
ITERATION_NUMBER INT NOT NULL,
EVENT_STATUS VARCHAR(10),
NO_OF_ROWS INT NOT NULL,
START_ORDER_ID INT NOT NULL,
END_ORDER_ID INT NOT NULL,
INPUT_FILE_NAME VARCHAR(250),
STORED_FILE_NAME VARCHAR(250),
ERROR_MESSAGE VARCHAR(10000)
);

DROP TABLE IF EXISTS UNIT_EXPENSE_DRILLDOWN;
CREATE TABLE UNIT_EXPENSE_DRILLDOWN
(
DRILLDOWN_ID INT PRIMARY KEY AUTO_INCREMENT,
UNIT_EXPENSE_DETAIL_ID INT NOT NULL,
EXPENSE_UPDATE_EVENT_ID INT NOT NULL,
REFERENCE_ID INT NOT NULL,
REFERENCE_NAME VARCHAR(100) NOT NULL,
EXPENSE_TYPE VARCHAR(50) NOT NULL,
EXPENSE_VALUE DECIMAL(10,2),
EXPENSE_RATE DECIMAL(10,2),
NO_OF_TICKETS INT,
ADJUSTMENTS DECIMAL(10,2)
)
;
alter table UNIT_PRODUCT_PRICING
add column COST DECIMAL(10,2) NULL
;

alter table UNIT_PRODUCT_PRICING
add column COD_COST DECIMAL(10,2) NULL
;

alter table PAYMENT_MODE
add column COMMISSION_RATE DECIMAL(10,2) NOT NULL DEFAULT 0.0;

UPDATE UNIT_PRODUCT_PRICING 
SET COST = 0.0;

ALTER TABLE DELIVERY_PARTNER
ADD COLUMN PER_DELIVERY_COST DECIMAL(10,2) NOT NULL DEFAULT 0.0;

ALTER TABLE CHANNEL_PARTNER
ADD COLUMN COMMISSION_RATE DECIMAL(10,2) NOT NULL DEFAULT 0.0;

UPDATE `PAYMENT_MODE` SET `COMMISSION_RATE`='0.00' WHERE `PAYMENT_MODE_ID`='1';
UPDATE `PAYMENT_MODE` SET `COMMISSION_RATE`='1.05' WHERE `PAYMENT_MODE_ID`='2';
UPDATE `PAYMENT_MODE` SET `COMMISSION_RATE`='2.65' WHERE `PAYMENT_MODE_ID`='3';
UPDATE `PAYMENT_MODE` SET `COMMISSION_RATE`='4.50' WHERE `PAYMENT_MODE_ID`='4';
UPDATE `PAYMENT_MODE` SET `COMMISSION_RATE`='3.80' WHERE `PAYMENT_MODE_ID`='5';
UPDATE `PAYMENT_MODE` SET `COMMISSION_RATE`='4.50' WHERE `PAYMENT_MODE_ID`='7';
UPDATE `PAYMENT_MODE` SET `COMMISSION_RATE`='3.80' WHERE `PAYMENT_MODE_ID`='8';
UPDATE `PAYMENT_MODE` SET `COMMISSION_RATE`='0.00' WHERE `PAYMENT_MODE_ID`='9';
UPDATE `PAYMENT_MODE` SET `COMMISSION_RATE`='0.00' WHERE `PAYMENT_MODE_ID`='6';

UPDATE `DELIVERY_PARTNER` SET `PER_DELIVERY_COST`='0.00' WHERE `PARTNER_ID`='1';
UPDATE `DELIVERY_PARTNER` SET `PER_DELIVERY_COST`='40.00' WHERE `PARTNER_ID`='2';
UPDATE `DELIVERY_PARTNER` SET `PER_DELIVERY_COST`='40.00' WHERE `PARTNER_ID`='3';
UPDATE `DELIVERY_PARTNER` SET `PER_DELIVERY_COST`='40.00' WHERE `PARTNER_ID`='4';
UPDATE `DELIVERY_PARTNER` SET `PER_DELIVERY_COST`='0.00' WHERE `PARTNER_ID`='5';
UPDATE `DELIVERY_PARTNER` SET `PER_DELIVERY_COST`='30.00' WHERE `PARTNER_ID`='6';
UPDATE `DELIVERY_PARTNER` SET `PER_DELIVERY_COST`='30.00' WHERE `PARTNER_ID`='7';

UPDATE `CHANNEL_PARTNER` SET `COMMISSION_RATE`='0.00' WHERE `PARTNER_ID`='1';
UPDATE `CHANNEL_PARTNER` SET `COMMISSION_RATE`='0.00' WHERE `PARTNER_ID`='2';
UPDATE `CHANNEL_PARTNER` SET `COMMISSION_RATE`='10.05' WHERE `PARTNER_ID`='3';
UPDATE `CHANNEL_PARTNER` SET `COMMISSION_RATE`='7.90' WHERE `PARTNER_ID`='4';
UPDATE `CHANNEL_PARTNER` SET `COMMISSION_RATE`='11.06' WHERE `PARTNER_ID`='5';
UPDATE `CHANNEL_PARTNER` SET `COMMISSION_RATE`='7.90' WHERE `PARTNER_ID`='6';
UPDATE `CHANNEL_PARTNER` SET `COMMISSION_RATE`='0.00' WHERE `PARTNER_ID`='7';
UPDATE `CHANNEL_PARTNER` SET `COMMISSION_RATE`='0.00' WHERE `PARTNER_ID`='8';
UPDATE `CHANNEL_PARTNER` SET `COMMISSION_RATE`='0.00' WHERE `PARTNER_ID`='9';
UPDATE `CHANNEL_PARTNER` SET `COMMISSION_RATE`='0.00' WHERE `PARTNER_ID`='10';
UPDATE `CHANNEL_PARTNER` SET `COMMISSION_RATE`='0.00' WHERE `PARTNER_ID`='11';


update UNIT_PRODUCT_PRICING set COST = null;

DROP TABLE IF EXISTS COST_DATA;

CREATE TABLE COST_DATA
select upm.UNIT_PROD_REF_ID, ud.UNIT_ID, pd.PRODUCT_ID, ud.UNIT_NAME, ud.UNIT_CATEGORY, pd.PRODUCT_NAME, rl.RL_ID, rl.RTL_ID, rl.RL_CODE, upp.UNIT_PROD_PRICE_ID,
upp.DIMENSION_CODE, upp.PRICE,upp.COST
from UNIT_PRODUCT_MAPPING upm, UNIT_DETAIL ud , PRODUCT_DETAIL pd, REF_LOOKUP rl , UNIT_PRODUCT_PRICING upp
WHERE ud.UNIT_ID = upm.UNIT_ID AND pd.PRODUCT_ID = upm.PRODUCT_ID AND pd.DIMENSION_CODE = rl.RTL_ID AND upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID AND upp.DIMENSION_CODE = rl.RL_ID;

update COST_DATA
set COST = null;

alter table COST_DATA 
add column PRODUCT_WITH_DIA VARCHAR(100);

update COST_DATA
set PRODUCT_WITH_DIA = case when RL_CODE = 'None' then PRODUCT_NAME else concat(PRODUCT_NAME,' ',RL_CODE) end
where RL_CODE <> '';

update COST_DATA
set PRODUCT_WITH_DIA = concat(PRODUCT_WITH_DIA, ' COD')
WHERE UNIT_CATEGORY IN ('COD','DELIVERY');
DROP TABLE IF EXISTS RECIPE_COST;
CREATE TABLE RECIPE_COST
(
PRODUCT VARCHAR(1000),
COST DECIMAL(10,2)
);


INSERT INTO RECIPE_COST
VALUES
("Hot Coffee Full","19.84"),
("Homestyle Aloo Filling","31.68"),
("Green Chicken Filling","131.61"),
("Kadhai Paneer Filling","105.66"),
("Keema Pav Filling","111.84"),
("2 Minutes Filling","77.83"),
("Butter Chicken Filling","141.53"),
("Sicilian Chicken Filling","184.29"),
("Spinach Corn cheese Filling","61.13"),
("Vada Pav Filling","4.77"),
("Hari Chutney","75.32"),
("Honey Garlic Mayo","134.08"),
("Lasoon Chutney","68.87"),
("Red Chilli Paste","78.84"),
("Kadhai Paneer Masala","851.07"),
("Sicilian Chicken Masala","721.38"),
("Napoli Filling","257.92"),
("Fried Mushroom","115.85"),
("Balsamico Filling","96.51"),
("Pepper Roasted Chicken","143.59"),
("Pepper Chicken Filling","163.7"),
("Bun Bhujia Filling","3.79"),
("Sicilian Roasted Chicken","922.79"),
("Chatpata Kebab Filling","12.19"),
("Green Chilli Mint Mayo","76.14"),
("Red Chilli Mayo","100.52"),
("Egg Bun Filling","4.18"),
("Poha Filling","8.45"),
("Mutton Lazeez Filling","36.15"),
("Sulemani Chai","1.35"),
("Cutting Chai","2.48"),
("Desi chai Regular","6.04"),
("Desi Chai Full","8.88"),
("Desi Chai Kadak Regular","6.49"),
("Desi Chai Kadak Full","11.18"),
("Desi Chai Pani Kum Regular","9.53"),
("Desi Chai Pani Kum Full","15.49"),
("Camomile - Regular","1.94"),
("Camomile - Full","2.56"),
("Darjeeling Muscatel Regular","4.72"),
("Darjeeling Muscatel Full","7.2"),
("Lopchu Regular","3.55"),
("Lopchu Full","4.98"),
("English Breakfast Regular","3.12"),
("English Breakfast Full","4.19"),
("Earl Grey Regular","3.13"),
("Earl Grey Full","4.02"),
("Green Tea Regular","3.1"),
("Green Tea Full","4.1"),
("Orange Pekoe Regular","3.32"),
("Darjeeling First Flush Regular","3.77"),
("Darjeeling First Flush Full","5.31"),
("Lemon Grass Regular","3"),
("Lemon Grass Full","4.51"),
("Jasmine Tea Regular","5.18"),
("Jasmine Tea Full","6.61"),
("Cinnamon Green Regular","3.1"),
("Cinnamon Green Full","4.1"),
("Lemon Green Regular","3.98"),
("Lemon Green Full","5.56"),
("Mint N Lemon Green Regular","4.1"),
("Mint N Lemon Green Full","6.15"),
("Moroccan Mint Regular","4.1"),
("Moroccan Mint Full","6.15"),
("Rose Cardamom Regular","3.88"),
("Rose Cardamom Full","5.51"),
("Honey Ginger Lemon Regular","11.41"),
("Honey Ginger Lemon Full","16.07"),
("Aam Papad Chai Regular","8.63"),
("God's Chai Regular","4.79"),
("God's Chai Full","6.48"),
("Pahadi Chai Regular","5.52"),
("Pahadi Chai Full","8.55"),
("Lemon Iced Tea Regular","5.84"),
("Lemon Iced Tea Full","7.28"),
("Peach Iced Tea Regular","19.82"),
("Peach Iced Tea Full","25.98"),
("Strawberry Iced Tea Regular","20.13"),
("Strawberry Iced Tea Full","26.61"),
("Passion Fruit Iced Tea Regular","19.82"),
("Passion Fruit Iced Tea Full","25.98"),
("Red Berry Iced Tea Regular","20.13"),
("Red Berry Iced Tea Full","26.61"),
("Kiwi Iced Tea Regular","20.13"),
("Kiwi Iced Tea Full","26.61"),
("Kiwi Shake","43.17"),
("Chikoo Shake","19.02"),
("Banana Shake","66.52"),
("Mango Shake Full","18.36"),
("Chocolate Shake Regular","18.34"),
("Chocolate Shake Full","25.24"),
("Rooh Afza Regular","14.63"),
("Rooh Afza Full","19.15"),
("Thandai Regular","20.38"),
("Thandai Full","27.2"),
("Strawberry Shake","33.8"),
("Black Grape Shake Full","43.33"),
("Mint Lemonade Regular","11.22"),
("Mint Lemonade Full","15.37"),
("Fresh Lime Regular","11.25"),
("Modinagar Shikanji Regular","12.01"),
("Modinagar Shikanji Full","16.94"),
("Home Made Chaach Regular","8.68"),
("Home Made Chaach Full","11.2"),
("Cold Coffee Regular","13.95"),
("Cold Coffee Full","18.06"),
("Hot Chocolate Regular","16.97"),
("Hot Chocolate Full","28.59"),
("Hot Coffee Regular","13.46"),
("Bournvita Regular","13.2"),
("Bournvita Full","19.19"),
("Square Coins Chai","5.17"),
("Pre paid Chai","5.17"),
("Kadhai Paneer wrap","47.36"),
("Spinach Corn Cheese","38.48"),
("2 Minutes Sandwich","22.59"),
("2 Minutes Sandwich COD","24.59"),
("Homestyle Aloo","31.64"),
("Bombay Special","28.54"),
("Napoli","48.97"),
("Balsamico","32.83"),
("Chocolate Bun","19.66"),
("Sicilian Chicken","57.03"),
("Green Chicken","45.5"),
("Butter-Chicken Wrap","54.07"),
("Pepper Chicken","58.99"),
("Egg Bun","9.79"),
("Vada Pav","12.97"),
("Bun Maska","9.45"),
("Keema Pav","21.11"),
("Brownie","51.47"),
("Blueberry Cake","44.73"),
("Carrot Cake","49.1"),
("Banana Cake","38.48"),
("Lemon Cake","33.98"),
("Chatpata Kebab Wrap","37.95"),
("Mutton Lazeez Wrap","62.37"),
("Bun Bhujia","13.45"),
("Kulhad Chai","10.2"),
("Chocolate Bun Filling","14.05"),
("Banana Filling","56.5"),
("Kiwi Filling","32.5"),
("Strawberry Filling","23.4"),
("Mango Filling","9"),
("Black Grape Filling","33"),
("Chikoo Filling","9"),
("Orange Pekoe Full","3.94"),
("Aam Papad Chai Full","13.89"),
("Fresh Lime Full","15.46"),
("POHA","14.37"),
("Makhni Masala","138.1"),
("Boiled Spinach","8"),
("Mutton Keema Masala","260.28"),
("Mutton Leg Masala","751.03"),
("Jeera Cookies","12.8"),
("Tulsi Adrak Chai Pack","57.43"),
("Desi Chai Pack","50.18"),
("Green Tea Pack","108.45"),
("Chai Masala Pack","99.74"),
("Tulsi Adrak Chai Pack COD","57.43"),
("Desi Chai Pack COD","50.18"),
("Green Tea Pack COD","108.45"),
("Chai Masala Pack COD","99.74"),
("Rusk","1.42"),
("Gur Wali Chai","7.47"),
("Roasted Chicken","215"),
("Oatmeal Cookies","20"),
("Badam Pista Cookies","16"),
("Cheese","0"),
("Thandi Chai","11.28"),
("Desi Chai 4 Cutting COD","30.21"),
("Desi Chai 10 Cutting COD","56.63"),
("Kulhad Chai 4 Cutting COD","44.16"),
("Kulhad Chai 10 Cutting COD","91.51"),
("Gods Chai 4 Cutting COD","26.63"),
("Gods Chai 10 Cutting COD","47.69"),
("Honey Ginger Lemon 4 Cutting COD","39.11"),
("Honey Ginger Lemon 10 Cutting COD","78.87"),
("Desi Chai 4 Cutting Pani Kum COD","36.12"),
("Desi Chai 10 Cutting Pani Kum COD","71.41"),
("Desi Chai 4 Cutting Kadak COD","30.04"),
("Desi Chai 10 Cutting Kadak Chai COD","56.21"),
("Peach Iced Tea Regular COD","39.97"),
("Passion Fruit Iced Tea Regular COD","39.97"),
("Lemon Iced Tea Regular COD","25.99"),
("Vada Pav COD","27.18"),
("Keema Pav COD","32.94"),
("Bun Bhujia COD","27.11"),
("Egg Bun COD","23.45"),
("Carrot Cake COD","60.01"),
("Lemon Cake COD","44.89"),
("Blueberry Cake COD","55.64"),
("Banana Cake COD","49.39"),
("Rusk COD","10.33"),
("Jeera Cookies COD","19.49"),
("Oatmeal Cookies COD","26.69"),
("Badam Pista Cookies COD","22.69"),
("Desi Chai ChotiKetli","30.21"),
("Desi Chai BadiKetli","56.63"),
("Desi Paani Kum ChotiKetli","36.12"),
("Desi Paani Kum BadiKetli","71.41"),
("God's Chai ChotiKetli","26.63"),
("God's Chai BadiKetli","47.69"),
("Kulhad Chai ChotiKetli","44.16"),
("Kulhad Chai BadiKetli","91.51"),
("Honey Ginger Lemon ChotiKetli","39.61"),
("Honey Ginger Lemon BadiKetli","78.87"),
("Additional Kulhad","2.5"),
("Thandi Elaichi Chai","11.58"),
("Thandi Saunf Chai","11.65"),
("Desi Doodh Kum Regular","4.52"),
("Desi Doodh Patti Full","8.06"),
("Full Doodh Full","8.06"),
("Desi Doodh Kum Full","5.88"),
("Desi Doodh Patti Regular","4.8"),
("Full Doodh Regular","4.8"),
("Thandi Cinnamon Chai","11.41"),
("Thandi Masala Chai","11.58"),
("Bun Maska Vada","14.63"),
("Bun Maska Vada COD","16.63"),
("Bun Maska Keema","22.35"),
("Bun Maska Keema COD","24.35"),
("Chaayos Special","28.54"),
("Achari butter","23.05"),
("Honey Lemon butter","30.67"),
("olive and sundried tomato butter","42.48"),
("Mint and jalapeno butter","25.56"),
("Bun Maska - Achari","4.91"),
("Bun Maska - Honey Lemon","5.68"),
("Bun Maska - Sundried Tomatoes & Olives","6.86"),
("Bun Maska - Mint Jalapeno","5.17"),
("Desi Chai ChotiKetli COD","30.21"),
("Desi Chai BadiKetli COD","56.63"),
("Desi Paani Kum ChotiKetli COD","36.12"),
("Desi Paani Kum BadiKetli COD","71.41"),
("God's Chai ChotiKetli COD","26.63"),
("God's Chai BadiKetli COD","47.69"),
("Kulhad Chai ChotiKetli COD","44.16"),
("Kulhad Chai BadiKetli COD","91.51"),
("Cinnamon Green ChotiKetli","21.15"),
("Lemon Green ChotiKetli","22.92"),
("Mint N Lemon Green ChotiKetli","23.17"),
("Rose Cardamom ChotiKetli","20.78"),
("Aam Papad Chai ChotiKetli","24.54"),
("Pahadi Chai ChotiKetli","22.42"),
("Pahadi Chai ChotiKetli COD","28.92"),
("Cinnamon Green ChotiKetli COD","27.65"),
("Lemon Green ChotiKetli COD","29.42"),
("Mint n Lemon Green ChotiKetli COD","29.67"),
("Aam Papad Chai ChotiKetli COD","31.04"),
("Rose Cardamom ChotiKetli COD","25.15"),
("Green Tea ChotiKetli COD","26.49"),
("Green Tea ChotiKetli","21.15"),
("English Breakfast ChotiKetli","21.21"),
("English Breakfast ChotiKetli COD","27.71"),
("Earl Grey ChotiKetli","21.22"),
("Earl Grey ChotiKetli COD","27.72"),
("Darjeeling First Flush ChotiKetli","7.59"),
("Darjeeling First Flush ChotiKetli COD","29"),
("Jasmine Tea ChotiKetli","10.41"),
("Jasmine Tea ChotiKetli COD","31.82"),
("Lemon Grass ChotiKetli","27.45"),
("Lemon Grass ChotiKetli COD","27.45"),
("Strawberry shake COD","54.11"),
("Banana shake COD","86.67"),
("Mango shake COD","38.51"),
("Black grape shake COD","63.48"),
("Chocolate Shake Regular COD","38.49"),
("Rooh Afza Regular COD","34.78"),
("Thandai Regular COD","40.53"),
("Fresh Lime Regular COD","31.4"),
("Modinagar Shikanji Regular COD","32.16"),
("Cold Coffee Regular COD","34.1"),
("Hot Chocolate chotiketli COD","55.41"),
("Hot Coffee ChotiKetli COD","47.62"),
("Hot Coffee ChotiKetli","41.88"),
("Bournvita chotiKetli COD","47.86"),
("Bournvita ChotiKetli","41.2"),
("Hot Chocolate ChotiKetli","41.31"),
("Poha COD","19.39"),
("Homestyle Aloo COD","34.86"),
("Spinach Corn Cheese COD","45.26"),
("Napoli COD","55.5"),
("Green Chicken COD","52.03"),
("Sicilian Chicken COD","63.56"),
("pepper chicken COD","65.52"),
("chocolate bun COD","26.19"),
("balsamico COD","39.36"),
("Bun Maska COD","15.98"),
("Bun Maska - Achari COD","9.6"),
("Bun Maska - Honey Lemon COD","9.75"),
("Bun Maska - Sundried Tomato & Olives COD","13.39"),
("Bun Maska - Sundried Tomatoes & Olives COD","13.39"),
("Bun Maska - Mint Jalapeno COD","11.7"),
("Honey Ginger Lemon ChotiKetli COD","40.37"),
("Lemongrass ChotiKetli COD","27.45"),
("Desi Doodh Kum ChotiKetli COD","30.35"),
("Desi Doodh Patti ChotiKetli COD","31.05"),
("Desi Doodh Kum BadiKetli COD","51.52"),
("Desi Doodh Patti BadiKetli COD","52.88"),
("Desi Doodh Patti ChotiKetli","31.05"),
("Desi Doodh Patti BadiKetli","52.88"),
("Desi Doodh Kum ChotiKetli","30.35"),
("Desi Doodh Kum BadiKetli","51.52"),
("Full Doodh ChotiKetli COD","31.05"),
("Full Doodh Regular COD","31.05"),
("Full Doodh BadiKetli COD","52.88"),
("Full Doodh ChotiKetli","31.05"),
("Full Doodh BadiKetli","52.88"),
("Cinnamon Green BadiKetli","44.38"),
("Lemon Green BadiKetli","48.81"),
("Mint N Lemon Green BadiKetli","285.06"),
("Moroccan Mint ChotiKetli","29.92"),
("Moroccan Mint BadiKetli","49.42"),
("Aam Papad Chai BadiKetli","71.53"),
("Pahadi Chai BadiKetli","56.51"),
("Rose Cardamom BadiKetli","48.32"),
("Green Tea BadiKetli","44.38"),
("English Breakfast BadiKetli","44.52"),
("Earl Grey BadiKetli","44.53"),
("Orange Pekoe ChotiKetli","30.54"),
("Orange Pekoe BadiKetli","45.5"),
("Darjeeling First Flush BadiKetli","47.75"),
("Lemon Grass BadiKetli","43.87"),
("Lemon Grass BadiKetli COD","43.87"),
("Jasmine Tea BadiKetli","54.81"),
("LOPCHU ChotiKetli","28.56"),
("LOPCHU BadiKetli","41.28"),
("Hot Chocolate BadiKetli","113.77"),
("Hot Coffee BadiKetli","272.81"),
("Bournvita BadiKetli","94.88"),
("Darjeeling Muscatel ChotiKetli","30.91"),
("Darjeeling Muscatel BadiKetli","52.52"),
("Desi Paani Kum Regular","9.27"),
("Additional honey","1.85"),
("Extra Coffee Powder","4.09"),
("Extra Chocolate Syrup","4.2"),
("Extra RoofAfza Syrup","3.67"),
("Desi Filter Kaafi Regular","15.51"),
("Desi Filter Kaafi ChotiKetli COD","47.62"),
("Drinking Chocolate ChotiKetli COD","50.8"),
("Drinking Chocolate Full","36.8"),
("Drinking Chocolate Regular","24.73"),
("Grofer chai pouch","15.35"),
("Sugar with masala chai","52.07"),
("POHA masala","44.7"),
("Paneer Sambossa Single","17.09"),
("Paneer Sambossa Single COD","19.00"),
("Paneer Sambossa Double COD","37.95"),
("Gujiya Double","37.95"),
("Gujiya Double COD","37.95"),
("Chicken Sambossa Single","17.09"),
("Chicken Sambossa Single COD","19.09"),
("Chicken Sambossa Double COD","38.2"),
("Auto expo Chai pouch","54.85"),
("Desi Paani Kum Full","8.22");


update COST_DATA cd set COST = (select COST from RECIPE_COST rc where 
rc.PRODUCT = cd.PRODUCT_WITH_DIA);

update UNIT_PRODUCT_PRICING upp, COST_DATA cd
set upp.COST = cd.COST
where upp.UNIT_PROD_PRICE_ID = cd.UNIT_PROD_PRICE_ID
;


update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set upp.COST =0.00 where upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and pd.PRODUCT_TYPE = 8
and upp.COST is null;

update UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm, PRODUCT_DETAIL pd
set upp.COST =0 where upp.UNIT_PROD_REF_ID = upm.UNIT_PROD_REF_ID
and upm.PRODUCT_ID = pd.PRODUCT_ID
and pd.PRODUCT_ID IN (400,410,691,692,840,850)
and upp.COST is null;


DROP PROCEDURE IF EXISTS `SP_UPDATE_EXPENSE_DETAIL_DATA`;
DELIMITER $$
CREATE PROCEDURE `SP_UPDATE_EXPENSE_DETAIL_DATA`(IN EXPENSE_EVENT_ID INTEGER)
proc_label : BEGIN
UPDATE UNIT_EXPENSE_DETAIL ued
LEFT OUTER JOIN 
    (SELECT 
        ud.UNIT_ID,
            ud.UNIT_NAME,
			EXPENSE_EVENT_ID AS EVENT_ID,
            COALESCE(q.CREDIT_CARD_CHARGES, 0) CREDIT_CARD_CHARGES,
            COALESCE(q.AMEX_CHARGES, 0) AMEX_CHARGES,
            COALESCE(q.SODEXO_CHARGES, 0) SODEXO_CHARGES,
            COALESCE(q.TICKET_RESTAURENT_CHARGES, 0) TICKET_RESTAURENT_CHARGES,
            COALESCE(w.CHANNEL_PARTNER_COST, 0) CHANNEL_PARTNER_COST,
            COALESCE(e.DELIVERY_PARTNER_COST, 0) DELIVERY_PARTNER_COST,
            COALESCE(r.TOTAL_TICKET, 0) TOTAL_TICKET,
            COALESCE(r.TKT, 0) TKT,
            COALESCE(r.SALES, 0) SALES,
            COALESCE(t.EMPLOYEE_MEAL, 0) EMPLOYEE_MEAL,
            COALESCE(t.UNSATISFIED_QUANTITY, 0) UNSATISFIED_QUANTITY,
            COALESCE(t.SAMPLING_MARKETING, 0) SAMPLING_MARKETING,
            COALESCE(t.GMV, 0) GMV,
            COALESCE(t.COGS_1, 0) + COALESCE(y.COGS_2, 0) AS TOTAL_COGS,
            COALESCE(r.DISCOUNT, 0) + COALESCE(r.PROMOTIONAL, 0) AS DISCOUNT
    FROM
        UNIT_DETAIL ud
    LEFT JOIN (SELECT
        od.UNIT_ID,
            eue.EVENT_ID,
            SUM(CASE
                WHEN ose.PAYMENT_MODE_ID = 2 THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100
                ELSE 0
            END) AS CREDIT_CARD_CHARGES,
            SUM(CASE
                WHEN ose.PAYMENT_MODE_ID = 3 THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100
                ELSE 0
            END) AS AMEX_CHARGES,
            SUM(CASE
                WHEN ose.PAYMENT_MODE_ID IN (4 , 7) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100
                ELSE 0
            END) AS SODEXO_CHARGES,
            SUM(CASE
                WHEN ose.PAYMENT_MODE_ID IN (5 , 8) THEN (COALESCE(ose.AMOUNT_PAID,0) + COALESCE(ose.EXTRA_VOUCHERS,0)) * pm.COMMISSION_RATE / 100
                ELSE 0
            END) AS TICKET_RESTAURENT_CHARGES
    FROM
        ORDER_DETAIL od
    LEFT JOIN ORDER_SETTLEMENT ose ON od.ORDER_ID = ose.ORDER_ID
    LEFT JOIN PAYMENT_MODE pm ON ose.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    INNER JOIN EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID
        AND od.ORDER_ID <= eue.END_ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND eue.EVENT_ID = EXPENSE_EVENT_ID
    GROUP BY od.UNIT_ID , eue.EVENT_ID) q ON ud.UNIT_ID = q.UNIT_ID
    LEFT JOIN (SELECT 
        a.UNIT_ID,
            a.EVENT_ID,
            SUM(a.CHANNEL_PARTNER_COST) AS CHANNEL_PARTNER_COST
    FROM
        (SELECT 
        od.UNIT_ID,
            eue.EVENT_ID,
            cp.PARTNER_DISPLAY_NAME,
            TRUNCATE(SUM(COALESCE(od.TAXABLE_AMOUNT,0)) * cp.COMMISSION_RATE / 100, 0) AS CHANNEL_PARTNER_COST
    FROM
        ORDER_DETAIL od
    LEFT JOIN CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    INNER JOIN EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID
        AND od.ORDER_ID <= eue.END_ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND eue.EVENT_ID = EXPENSE_EVENT_ID
    GROUP BY od.UNIT_ID , eue.EVENT_ID , cp.PARTNER_DISPLAY_NAME) AS a
    GROUP BY a.UNIT_ID , a.EVENT_ID) AS w ON ud.UNIT_ID = w.UNIT_ID
        AND q.EVENT_ID = w.EVENT_ID
    LEFT JOIN (SELECT 
        a.UNIT_ID,
            a.EVENT_ID,
            SUM(a.DELIVERY_PARTNER_COST) AS DELIVERY_PARTNER_COST
    FROM
        (SELECT 
        od.UNIT_ID,
            eue.EVENT_ID,
            cp.PARTNER_DISPLAY_NAME,
            COALESCE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) * cp.PER_DELIVERY_COST, 0) AS DELIVERY_PARTNER_COST
    FROM
        ORDER_DETAIL od
    LEFT JOIN DELIVERY_PARTNER cp ON od.DELIVERY_PARTNER_ID = cp.PARTNER_ID
    INNER JOIN EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID
        AND od.ORDER_ID <= eue.END_ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND eue.EVENT_ID = EXPENSE_EVENT_ID
    GROUP BY od.UNIT_ID , eue.EVENT_ID , cp.PARTNER_DISPLAY_NAME) AS a
    GROUP BY a.UNIT_ID , a.EVENT_ID) AS e ON ud.UNIT_ID = e.UNIT_ID
        AND q.EVENT_ID = e.EVENT_ID
    LEFT JOIN (SELECT 
        od.UNIT_ID,
            eue.EVENT_ID,
            COUNT(*) AS TOTAL_TICKET,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS TKT,
            SUM(COALESCE(od.TAXABLE_AMOUNT,0)) AS SALES,
            SUM(COALESCE(od.DISCOUNT_AMOUNT,0)) AS DISCOUNT,
            SUM(COALESCE(od.PROMOTIONAL_DISCOUNT,0)) AS PROMOTIONAL
    FROM
        ORDER_DETAIL od
    LEFT JOIN UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID
        AND od.ORDER_ID <= eue.END_ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND eue.EVENT_ID = EXPENSE_EVENT_ID
    GROUP BY od.UNIT_ID , eue.EVENT_ID) AS r ON ud.UNIT_ID = r.UNIT_ID
        AND q.EVENT_ID = r.EVENT_ID
    LEFT JOIN (SELECT 
        c.UNIT_ID,
            c.EVENT_ID,
            SUM(c.COGS) AS COGS_1,
            SUM(c.EMPLOYEE_MEAL) AS EMPLOYEE_MEAL,
            SUM(c.UNSATISFIED_QUANTITY) AS UNSATISFIED_QUANTITY,
            SUM(c.SAMPLING_MARKETING) AS SAMPLING_MARKETING,
            SUM(c.GMV) AS GMV,
            SUM(c.NEWGMV) AS NEWGMV
    FROM
        (SELECT 
        a.UNIT_ID,
            a.EVENT_ID,
            a.PRODUCT_ID,
            a.DIMENSION,
            COALESCE(a.SOLD_QUANTITY,0) * COALESCE(b.COST,0) AS COGS,
            COALESCE(a.EMPLOYEE_MEAL,0) * COALESCE(b.COST,0) AS EMPLOYEE_MEAL,
            COALESCE(a.UNSATISFIED_QUANTITY,0) * COALESCE(b.COST,0) AS UNSATISFIED_QUANTITY,
            COALESCE(a.SAMPLING_MARKETING,0) * COALESCE(b.COST,0) AS SAMPLING_MARKETING,
            COALESCE(a.GMV,0)GMV,
            COALESCE(a.SOLD_QUANTITY,0) * b.PRICE AS NEWGMV
    FROM
        (SELECT 
        od.UNIT_ID,
            eue.EVENT_ID,
            pd.PRODUCT_ID,
            rl2.RL_ID AS DIMENSION,
            oi.PRICE,
            SUM(CASE
                WHEN
					IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
                        OR (IS_COMPLIMENTARY = 'Y'
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106))
                THEN
                    oi.QUANTITY
                ELSE 0
            END) SOLD_QUANTITY,
            SUM(CASE
                WHEN
                    IS_COMPLIMENTARY = 'Y'
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2100
                THEN
                    oi.QUANTITY
                ELSE 0
            END) EMPLOYEE_MEAL,
            SUM(CASE
                WHEN
                    IS_COMPLIMENTARY = 'Y'
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
                THEN
                    oi.QUANTITY
                ELSE 0
            END) UNSATISFIED_QUANTITY,
            SUM(CASE
                WHEN
                    IS_COMPLIMENTARY = 'Y'
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) IN (2105 , 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) SAMPLING_MARKETING,
            SUM(CASE
                WHEN
                    IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
                        OR (IS_COMPLIMENTARY = 'Y'
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106))
                THEN
                    oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GMV
    FROM
        ORDER_DETAIL od
    LEFT JOIN ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    LEFT JOIN UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    LEFT JOIN PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
    LEFT JOIN REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID
    LEFT JOIN REF_LOOKUP rl2 ON oi.DIMENSION = rl2.RL_CODE
        AND rl2.RTL_ID = pd.DIMENSION_CODE
    INNER JOIN EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID
        AND od.ORDER_ID <= eue.END_ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND eue.EVENT_ID = EXPENSE_EVENT_ID
    GROUP BY od.UNIT_ID , eue.EVENT_ID , pd.PRODUCT_ID , rl2.RL_ID , oi.PRICE) AS a
    INNER JOIN (SELECT 
        upm.UNIT_ID,
            upm.PRODUCT_ID,
            upp.DIMENSION_CODE,
            upp.PRICE,
            upp.COST
    FROM
        UNIT_PRODUCT_MAPPING upm
    LEFT JOIN UNIT_PRODUCT_PRICING upp ON upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID) AS b ON a.UNIT_ID = b.UNIT_ID
        AND a.PRODUCT_ID = b.PRODUCT_ID
        AND a.DIMENSION = b.DIMENSION_CODE) AS c
    GROUP BY c.UNIT_ID , c.EVENT_ID) AS t ON ud.UNIT_ID = t.UNIT_ID
        AND q.EVENT_ID = t.EVENT_ID
    LEFT JOIN (SELECT 
        p.UNIT_ID, p.EVENT_ID, SUM(p.QUANTITY * b.COST) AS COGS_2
    FROM
        (SELECT 
        od.UNIT_ID,
            eue.EVENT_ID,
            pd1.PRODUCT_ID,
            (CASE
                WHEN pd1.DIMENSION_CODE = 1 THEN 1
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    21
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    24
                WHEN pd1.DIMENSION_CODE = 3 THEN 30
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    1101
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1102
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    3401
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    3402
                ELSE 1
            END) AS DIMENSIONS,
            SUM(oi.QUANTITY) AS QUANTITY
    FROM
        ORDER_DETAIL od
    INNER JOIN ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN ORDER_ITEM_ADDON oia ON oi.ORDER_ITEM_ID = oia.ORDER_ITEM_ID
    INNER JOIN ADDON_PRODUCT_DATA apd ON oia.ADDON_ID = apd.ADDON_ID
    INNER JOIN PRODUCT_DETAIL pd1 ON pd1.PRODUCT_ID = apd.PRODUCT_ID
    INNER JOIN EXPENSE_UPDATE_EVENT_DATA eue ON od.ORDER_ID > eue.START_ORDER_ID
        AND od.ORDER_ID <= eue.END_ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND eue.EVENT_ID = EXPENSE_EVENT_ID
            AND pd.PRODUCT_TYPE = 8
            AND apd.ADDON_ID IS NOT NULL
    GROUP BY od.UNIT_ID , eue.EVENT_ID , pd1.PRODUCT_ID , DIMENSIONS) AS p
    INNER JOIN (SELECT 
        upm.UNIT_ID,
            upm.PRODUCT_ID,
            upp.DIMENSION_CODE,
            upp.PRICE,
            upp.COST
    FROM
        UNIT_PRODUCT_MAPPING upm
    LEFT JOIN UNIT_PRODUCT_PRICING upp ON upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID) AS b ON p.UNIT_ID = b.UNIT_ID
        AND p.PRODUCT_ID = b.PRODUCT_ID
        AND p.DIMENSIONS = b.DIMENSION_CODE
    GROUP BY p.UNIT_ID , p.EVENT_ID) AS y ON ud.UNIT_ID = y.UNIT_ID
        AND q.EVENT_ID = y.EVENT_ID) AS z 
ON   ued.UNIT_ID = z.UNIT_ID
        AND ued.EXPENSE_UPDATE_EVENT_ID = z.EVENT_ID        
SET 
    ued.GMV_AMOUNT = COALESCE(z.GMV, 0),
    ued.NET_SALES_AMOUNT = COALESCE(z.SALES, 0),
    ued.NET_TICKETS = COALESCE(z.TKT, 0),
    ued.TOTAL_TICKETS = COALESCE(z.TOTAL_TICKET, 0),
    ued.COGS = COALESCE(z.TOTAL_COGS, 0),
    ued.MARKETING_AND_SAMPLING = COALESCE(z.SAMPLING_MARKETING, 0),
    ued.UNSATISFIED_CUSTOMER_COST = COALESCE(z.UNSATISFIED_QUANTITY, 0),
    ued.EMPLOYEE_MEAL = COALESCE(z.EMPLOYEE_MEAL, 0),
    ued.DELIVERY_COST = COALESCE(z.DELIVERY_PARTNER_COST, 0),
    ued.CREDIT_CARD_CHARGES = COALESCE(z.CREDIT_CARD_CHARGES, 0),
    ued.AMEX_CARD_CHARGES = COALESCE(z.AMEX_CHARGES, 0),
    ued.SODEXO_CHARGES = COALESCE(z.SODEXO_CHARGES, 0),
    ued.TKT_RESTAURANT_CHARGES = COALESCE(z.TICKET_RESTAURENT_CHARGES, 0),
    ued.CHANNEL_PARTNER = COALESCE(z.CHANNEL_PARTNER_COST, 0),
    ued.NET_DISCOUNT = COALESCE(z.DISCOUNT, 0),
    ued.KITCHEN_COST_TOTAL = COALESCE(z.SALES, 0) * ued.KITCHEN_COST_PERCENTAGE / 100,
    ued.OPS_COST_TOTAL = COALESCE(z.SALES, 0) * ued.OPS_COST_PERCENTAGE / 100,
    ued.RENT = GREATEST( COALESCE(ued.FIXED_RENT, 0) , ( COALESCE(ued.RENT_PERCENTAGE, 0) * COALESCE(z.SALES, 0) / 100))
    where ued.EXPENSE_UPDATE_EVENT_ID = EXPENSE_EVENT_ID
;

 
update UNIT_EXPENSE_DETAIL ued
SET ued.TOTAL_COST=(
COALESCE(ued.COGS, 0)
+ COALESCE(ued.MANPOWER, 0)
+ COALESCE(ued.CONSUMABLES_AND_UTILITIES, 0)
+ COALESCE(ued.MARKETING_AND_SAMPLING, 0)
+ COALESCE(ued.UNSATISFIED_CUSTOMER_COST, 0)
+ COALESCE(ued.EMPLOYEE_MEAL, 0)
+ COALESCE(ued.WASTAGE_AND_EXPIRED, 0)
+ COALESCE(ued.DELIVERY_COST, 0)
+ COALESCE(ued.ELECTRICITY, 0)
+ COALESCE(ued.WATER, 0)
+ COALESCE(ued.DG_RENT, 0)
+ COALESCE(ued.DG_CHARGES, 0)
+ COALESCE(ued.CREDIT_CARD_CHARGES, 0)
+ COALESCE(ued.AMEX_CARD_CHARGES, 0)
+ COALESCE(ued.SODEXO_CHARGES, 0)
+ COALESCE(ued.TKT_RESTAURANT_CHARGES, 0)
+ COALESCE(ued.CHANNEL_PARTNER, 0)
+ COALESCE(ued.SCM_RENTEL, 0)
+ COALESCE(ued.EDC_MACHINE, 0)
+ COALESCE(ued.FREIGHT_OUTWARD, 0)
+ COALESCE(ued.CONVENYANCE, 0)
+ COALESCE(ued.STAFF_WELFARE, 0)
+ COALESCE(ued.CHANGE_COMMISSION, 0)
+ COALESCE(ued.COURIER, 0)
+ COALESCE(ued.PRINTING_AND_STATIONARY, 0)
+ COALESCE(ued.MISC_EXP, 0)
+ COALESCE(ued.PARKING_CHARGES, 0)
+ COALESCE(ued.CLEANING_CHARGES, 0)
+ COALESCE(ued.NEWSPAPER, 0)
+ COALESCE(ued.RENT, 0)
+ COALESCE(ued.CAM_CAHRGES, 0)
+ COALESCE(ued.INTERNET, 0)
+ COALESCE(ued.TELEPHONE, 0)
+ COALESCE(ued.OPS_COST_TOTAL, 0)
+ COALESCE(ued.KITCHEN_COST_TOTAL, 0)
+ COALESCE(ued.REPAIR_AND_MAINTENANCE_MINOR, 0)
+ COALESCE(ued.REPAIR_AND_MAINTENANCE_MAJOR, 0)
+ COALESCE(ued.MANUAL_ADJUSTMENT, 0)
+ COALESCE(ued.CUSTOMER_CARE_COST, 0)
+ COALESCE(ued.MAINTENANCE_TEAM_COST, 0)
+ COALESCE(ued.TRAINING_TEAM_COST, 0)
+ COALESCE(ued.IT_TEAM_COST, 0)
) WHERE EXPENSE_UPDATE_EVENT_ID = EXPENSE_EVENT_ID;
 
update UNIT_EXPENSE_DETAIL ued
SET ued.EBIDTA_PERCENTAGE=TRUNCATE((COALESCE(ued.NET_SALES_AMOUNT,0) -  COALESCE(ued.TOTAL_COST,0) )*100 / (case when  COALESCE(ued.NET_SALES_AMOUNT,0) = 0 then 1 else ued.NET_SALES_AMOUNT end),2) 
WHERE EXPENSE_UPDATE_EVENT_ID = EXPENSE_EVENT_ID;
 
END$$
DELIMITER ;

#Customer Care Cost	Maintenance Team Cost	Training Team Cost	IT Team Cost
ALTER TABLE UNIT_EXPENSE_DETAIL
ADD COLUMN CUSTOMER_CARE_COST DECIMAL(10,2) NULL, 
ADD COLUMN MAINTENANCE_TEAM_COST DECIMAL(10,2) NULL,
ADD COLUMN TRAINING_TEAM_COST DECIMAL(10,2) NULL,
ADD COLUMN IT_TEAM_COST DECIMAL(10,2) NULL;

