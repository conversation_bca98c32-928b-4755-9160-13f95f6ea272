CREATE TABLE LOYALTY_SCORE(
LOYAL<PERSON>_POINTS_ID INT NOT NULL AUTO_INCREMENT,
CUSTOMER_ID INT NOT NULL,
ACQUIRED_POINTS INT NOT NULL,
PRIMARY KEY (<PERSON>O<PERSON><PERSON><PERSON>_POINTS_ID)
);

CREATE TABLE LOYALTY_EVENTS(
LOYALTY_EVENTS_ID INT NOT NULL AUTO_INCREMENT,
CUSTOMER_ID INT NOT NULL ,
TRANSACTION_TYPE VARCHAR(10) NOT NULL,
TRANSACTION_POINTS INT NOT NULL,
TRANSACTION_CODE_TYPE VARCHAR(20) NOT NULL,
TRANSACTION_CODE VARCHAR(500) NOT NULL,
TRANSACTION_STATUS VARCHAR(10) NOT NULL,
TRANSACTION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
PRIMARY KEY (LOYALTY_EVENTS_ID)
);

insert into LOYALTY_SCORE( CUS<PERSON>MER_ID, ACQUIRED_POINTS)
select CUS<PERSON>MER_ID , 0 from CUSTOMER_INFO;
