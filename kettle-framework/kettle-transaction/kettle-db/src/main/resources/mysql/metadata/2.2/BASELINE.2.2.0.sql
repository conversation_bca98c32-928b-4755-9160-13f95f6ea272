ALTER TABLE KETTLE_DEV.FEEDBACK_RESPONSE
ADD COLUMN FEEDBACK_INFO_ID INTEGER NULL;


ALTER TABLE FEEDBACK_RESPONSE
ADD COLUMN FEEDBACK_RATING INTEGER NULL;

ALTER TABLE FEEDBACK_RESPONSE_DATA
ADD COLUMN FEEDBACK_RATING INTEGER NULL;


DROP TABLE IF EXISTS KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE;
CREATE TABLE KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE (
    CSAT_SCORE_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    UNIT_ID INTEGER NOT NULL,
    CSAT_SCORE_TYPE VARCHAR(20) NOT NULL,
    ADD_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CSAT_YEAR INTEGER NOT NULL,
    CSAT_MONTH INTEGER NOT NULL,
    CSAT_DAY INTEGER NOT NULL,
    CSAT_WEEK INTEGER NOT NULL,
    CSAT_DATE DATE NOT NULL,
    REQUESTED_FEEDBACK INTEGER NULL,
    RECEIVED_FEEDBACK INTEGER NULL,
    REQUESTED_DINE_IN_FEEDBACK INTEGER NULL,
	REQUESTED_DELIVERY_FEEDBACK INTEGER NULL,
    RECEIVED_DINE_IN_FEEDBACK INTEGER NULL,   
    RECEIVED_DELIVERY_FEEDBACK INTEGER NULL,
    DINE_IN_SCORE DECIMAL(10 , 2 ) NULL,
    DELIVERY_SCORE DECIMAL(10 , 2 ) NULL,
    CUMULATIVE_SCORE DECIMAL(10 , 2 ) NULL,
    NO_OF_ORDER_DAYS INTEGER NULL,
    NO_OF_FEEDBACK_DAYS INTEGER NULL,
    START_FEEDBACK_ID INTEGER NULL,
    END_FEEDBACK_ID INTEGER NULL,
    IS_CURRENT_SCORE VARCHAR(1) NULL
);

DROP PROCEDURE IF EXISTS DAILY_CONSOLIDATED_CSAT_SCORE;
DELIMITER $$
CREATE PROCEDURE DAILY_CONSOLIDATED_CSAT_SCORE(
IN BIZ_DATE DATE,
IN ORDER_START_TIME TIMESTAMP, 
IN ORDER_END_TIME TIMESTAMP, 
IN FEEDBACK_START_TIME TIMESTAMP, 
IN FEEDBACK_END_TIME TIMESTAMP,
ORDER_DAYS INTEGER, 
FEEDBACK_DAYS INTEGER, 
IN CSAT_TYPE VARCHAR(20))
proc_label : BEGIN

update KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE
set IS_CURRENT_SCORE = 'N'
where IS_CURRENT_SCORE = 'Y'
and CSAT_SCORE_TYPE = CSAT_TYPE;

INSERT INTO KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE (UNIT_ID,CSAT_SCORE_TYPE, ADD_TIME,CSAT_YEAR,CSAT_MONTH,
CSAT_DAY,CSAT_WEEK,CSAT_DATE,NO_OF_ORDER_DAYS, NO_OF_FEEDBACK_DAYS, IS_CURRENT_SCORE)
SELECT 
    ud.UNIT_ID,
    CSAT_TYPE,
    CURRENT_TIMESTAMP,
    YEAR(BIZ_DATE),
    MONTH(BIZ_DATE),
    DAY(BIZ_DATE),
    WEEK(BIZ_DATE),
    BIZ_DATE,
	ORDER_DAYS,
    FEEDBACK_DAYS,
    'Y'
FROM KETTLE_MASTER_DUMP.UNIT_DETAIL ud
where ud.UNIT_STATUS = 'ACTIVE'
and ud.UNIT_CATEGORY IN ('CAFE', 'DELIVERY')
;
 
update KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE ofd, (SELECT 
    ofd.UNIT_ID,
    min(ofd.FEEDBACK_ID) START_FEEDBACK_ID,
    max(ofd.FEEDBACK_ID) END_FEEDBACK_ID,
    COUNT(*) TOTAL_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN ofd.FEEDBACK_STATUS = 'COMPLETED' THEN 1
        ELSE 0
    END) TOTAL_RECEIVED_FEEDBACK,
    AVG(ofd.FEEDBACK_RATING) CUMULATIVE_FEEDBACK_RATING,
    SUM(CASE
        WHEN ofd.ORDER_SOURCE <> 'COD' THEN 1
        ELSE 0
    END) DINE_IN_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN
            ofd.ORDER_SOURCE <> 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            1
        ELSE 0
    END) DINE_IN_RECEIVED_FEEDBACK,
    AVG(CASE
        WHEN
            ofd.ORDER_SOURCE <> 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            ofd.FEEDBACK_RATING
        ELSE NULL
    END) DINE_IN_FEEDBACK_RATING,
    SUM(CASE
        WHEN ofd.ORDER_SOURCE = 'COD' THEN 1
        ELSE 0
    END) DELIVERY_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN
            ofd.ORDER_SOURCE = 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            1
        ELSE 0
    END) DELIVERY_RECEIVED_FEEDBACK,
    AVG(CASE
        WHEN
            ofd.ORDER_SOURCE = 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            ofd.FEEDBACK_RATING
        ELSE NULL
    END) DELIVERY_FEEDBACK_RATING
FROM
    ORDER_DETAIL od
        INNER JOIN
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL ofd ON ofd.ORDER_ID = od.ORDER_ID
WHERE
    (ofd.FEEDBACK_TIME IS NULL
        && ofd.FEEDBACK_STATUS = 'CREATED')
        OR (ofd.FEEDBACK_TIME >= FEEDBACK_START_TIME
        AND ofd.FEEDBACK_TIME < FEEDBACK_END_TIME
        AND ofd.FEEDBACK_STATUS = 'COMPLETED')
        AND od.BILLING_SERVER_TIME >= ORDER_START_TIME
        AND od.BILLING_SERVER_TIME < ORDER_END_TIME
GROUP BY ofd.UNIT_ID ) a
set ofd.REQUESTED_FEEDBACK = a.TOTAL_REQUESTED_FEEDBACK,
    ofd.RECEIVED_FEEDBACK = a.TOTAL_RECEIVED_FEEDBACK,
    ofd.REQUESTED_DINE_IN_FEEDBACK = a.DINE_IN_REQUESTED_FEEDBACK,
	ofd.REQUESTED_DELIVERY_FEEDBACK = a.DELIVERY_REQUESTED_FEEDBACK,
    ofd.RECEIVED_DINE_IN_FEEDBACK = a.DINE_IN_RECEIVED_FEEDBACK,   
    ofd.RECEIVED_DELIVERY_FEEDBACK = a.DELIVERY_RECEIVED_FEEDBACK,
    ofd.DINE_IN_SCORE = a.DINE_IN_FEEDBACK_RATING,
    ofd.DELIVERY_SCORE = a.DELIVERY_FEEDBACK_RATING,
    ofd.CUMULATIVE_SCORE = a.CUMULATIVE_FEEDBACK_RATING,
    ofd.START_FEEDBACK_ID = a.START_FEEDBACK_ID,
    ofd.END_FEEDBACK_ID = a.END_FEEDBACK_ID
    where a.UNIT_ID = ofd.UNIT_ID
    and ofd.CSAT_SCORE_TYPE = CSAT_TYPE
    and ofd.IS_CURRENT_SCORE = 'Y';
END$$
DELIMITER ;

DROP PROCEDURE IF EXISTS OVERALL_CONSOLIDATED_CSAT_SCORE;
DELIMITER $$
CREATE PROCEDURE OVERALL_CONSOLIDATED_CSAT_SCORE(
IN BIZ_DATE DATE)
proc_label : BEGIN

INSERT INTO KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE (UNIT_ID,CSAT_SCORE_TYPE, ADD_TIME,CSAT_YEAR,CSAT_MONTH,
CSAT_DAY,CSAT_WEEK,CSAT_DATE,NO_OF_ORDER_DAYS, NO_OF_FEEDBACK_DAYS, IS_CURRENT_SCORE)
SELECT 
    ud.UNIT_ID,
    'CUMULATIVE',
    CURRENT_TIMESTAMP,
    YEAR(BIZ_DATE),
    MONTH(BIZ_DATE),
    DAY(BIZ_DATE),
    WEEK(BIZ_DATE),
    BIZ_DATE,
	-1,
    -1,
    'Y'
FROM KETTLE_MASTER_DUMP.UNIT_DETAIL ud left outer join KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE ccs
on ud.UNIT_ID = ccs.UNIT_ID and ccs.CSAT_SCORE_TYPE = 'CUMULATIVE'
where ud.UNIT_STATUS = 'ACTIVE'
and ud.UNIT_CATEGORY IN ('CAFE', 'DELIVERY')
and ccs.UNIT_ID is null
;
 
update KETTLE_DUMP.CONSOLIDATED_CSAT_SCORE ofd, (SELECT 
    ofd.UNIT_ID,
    min(ofd.FEEDBACK_ID) START_FEEDBACK_ID,
    max(ofd.FEEDBACK_ID) END_FEEDBACK_ID,
    COUNT(*) TOTAL_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN ofd.FEEDBACK_STATUS = 'COMPLETED' THEN 1
        ELSE 0
    END) TOTAL_RECEIVED_FEEDBACK,
    AVG(ofd.FEEDBACK_RATING) CUMULATIVE_FEEDBACK_RATING,
    SUM(CASE
        WHEN ofd.ORDER_SOURCE <> 'COD' THEN 1
        ELSE 0
    END) DINE_IN_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN
            ofd.ORDER_SOURCE <> 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            1
        ELSE 0
    END) DINE_IN_RECEIVED_FEEDBACK,
    AVG(CASE
        WHEN
            ofd.ORDER_SOURCE <> 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            ofd.FEEDBACK_RATING
        ELSE NULL
    END) DINE_IN_FEEDBACK_RATING,
    SUM(CASE
        WHEN ofd.ORDER_SOURCE = 'COD' THEN 1
        ELSE 0
    END) DELIVERY_REQUESTED_FEEDBACK,
    SUM(CASE
        WHEN
            ofd.ORDER_SOURCE = 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            1
        ELSE 0
    END) DELIVERY_RECEIVED_FEEDBACK,
    AVG(CASE
        WHEN
            ofd.ORDER_SOURCE = 'COD'
                && ofd.FEEDBACK_STATUS = 'COMPLETED'
        THEN
            ofd.FEEDBACK_RATING
        ELSE NULL
    END) DELIVERY_FEEDBACK_RATING
FROM
       KETTLE_DUMP.ORDER_FEEDBACK_DETAIL ofd 
WHERE
    ofd.FEEDBACK_STATUS IN('CREATED','COMPLETED')
       
GROUP BY ofd.UNIT_ID ) a
set ofd.REQUESTED_FEEDBACK = a.TOTAL_REQUESTED_FEEDBACK,
    ofd.RECEIVED_FEEDBACK = a.TOTAL_RECEIVED_FEEDBACK,
    ofd.REQUESTED_DINE_IN_FEEDBACK = a.DINE_IN_REQUESTED_FEEDBACK,
	ofd.REQUESTED_DELIVERY_FEEDBACK = a.DELIVERY_REQUESTED_FEEDBACK,
    ofd.RECEIVED_DINE_IN_FEEDBACK = a.DINE_IN_RECEIVED_FEEDBACK,   
    ofd.RECEIVED_DELIVERY_FEEDBACK = a.DELIVERY_RECEIVED_FEEDBACK,
    ofd.DINE_IN_SCORE = a.DINE_IN_FEEDBACK_RATING,
    ofd.DELIVERY_SCORE = a.DELIVERY_FEEDBACK_RATING,
    ofd.CUMULATIVE_SCORE = a.CUMULATIVE_FEEDBACK_RATING,
    ofd.START_FEEDBACK_ID = a.START_FEEDBACK_ID,
    ofd.END_FEEDBACK_ID = a.END_FEEDBACK_ID
    where a.UNIT_ID = ofd.UNIT_ID
    and ofd.CSAT_SCORE_TYPE = 'CUMULATIVE'
    and ofd.IS_CURRENT_SCORE = 'Y';
END$$
DELIMITER ;