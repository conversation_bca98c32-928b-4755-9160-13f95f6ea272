
#DEACTIVE DISCOUNT VALUES
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2008';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2009';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2014';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2000';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2016';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2013';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2007';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2003';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2004';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2011';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2006';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2001';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2015';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2010';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2002';
UPDATE REF_LOOKUP SET RL_STATUS='IN_ACTIVE' WHERE RL_ID='2012';


#CHANGED ODC to ODC/Bulk Order AS SUGGESTED BY NMS
UPDATE KETTLE.REF_LOOKUP SET RL_CODE='ODC/Bulk Order', RL_NAME='ODC/Bulk Order' WHERE RL_ID='2005';

#Added denomination of 5 for Ticket Restaurant coupons
INSERT INTO DENOMINATION 
(DENOMINATION_ID, PAYMENT_MODE, DENOMINATION_TEXT, DENOMINATION_CODE, DENOMINATION_VALUE, DISPLAY_ORDER, STATUS, BUNDLE_SIZE)
VALUES ('28', '5', 'Five', 'FIVE', '5', '10', 'ACTIVE', '100');

