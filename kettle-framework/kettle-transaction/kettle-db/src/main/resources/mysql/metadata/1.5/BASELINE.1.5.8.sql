ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN GMV FLOAT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN DISCOUNT FLOAT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN NET_SALES_AMOUNT FLOAT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN VAT FLOAT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN MRP_VAT FLOAT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN NET_PRICE_VAT FLOAT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN S_CHARGE FLOAT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN SERVICE_TAX FLOAT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN SB_CESS FLOAT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN ROUND_OFF_AMOUNT FLOAT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN TOTAL_AMOUNT FLOAT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS ADD COLUMN BILL_COUNT INT NOT NULL;
ALTER TABLE CLOSURE_PAYMENT_DETAILS MODIFY COLUMN RECONCILIATION_STATUS VARCHAR(20) DEFAULT NULL;

ALTER TABLE OFFER_METADATA ADD COLUMN STATUS VARCHAR(10) DEFAULT 'ACTIVE';
ALTER TABLE COUPON_DETAIL_MAPPING_DATA ADD COLUMN STATUS VARCHAR(10) DEFAULT 'ACTIVE';
ALTER TABLE OFFER_PARTNERS ADD COLUMN STATUS VARCHAR(10) DEFAULT 'ACTIVE';

ALTER TABLE SUBSCRIPTION_DETAIL 
ADD COLUMN AUTOMATED_DELIVERY VARCHAR(1) NOT NULL DEFAULT 'Y';
