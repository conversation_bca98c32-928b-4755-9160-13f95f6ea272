ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
    ADD COLUMN PHOTO_COPY_EXPENSES_CAFE DECIMAL(20, 6),
ADD COLUMN PRINTING_AND_STATIONARY_CAFE  DECIMAL(20,6),
ADD COLUMN STAFF_WELFARE_EXPENSES_CAFE  DECIMAL(20,6),
ADD COLUMN CLEANING_CHARGES_CAFE  DECIMAL(20,6),
ADD COLUMN BUSINESS_PROMOTION_CAFE  DECIMAL(20,6),
ADD COLUMN COURIER_CHARGES_CAFE  DECIMAL(20,6),
ADD COLUMN NEWSPAPER_CHARGES_CAFE  DECIMAL(20,6);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
    ADD COLUMN COMMISSION_CHANGE DECIMAL(20, 6),
ADD COLUMN PHOTO_COPY_EXPENSES_CAFE  DECIMAL(20,6),
ADD COLUMN PRINTING_AND_STATIONARY_CAFE  DECIMAL(20,6),
ADD COLUMN STAFF_WELFARE_EXPENSES_CAFE  DECIMAL(20,6),
ADD COLUMN CLEANING_CHARGES_CAFE  DECIMAL(20,6),
ADD COLUMN BUSINESS_PROMOTION_CAFE  DECIMAL(20,6),
ADD COLUMN COURIER_CHARGES_CAFE  DECIMAL(20,6),
ADD COLUMN NEWSPAPER_CHARGES_CAFE  DECIMAL(20,6);


ALTER TABLE `KETTLE_DEV`.`MENU_PRODUCT_COST_DETAIL`
    CHANGE COLUMN `TAXABLE_QUANTITY` `TAXABLE_QUANTITY` INT (11) NOT NULL;

CREATE TABLE `KETTLE_DEV`.`WEB_OFFER_COUPON_REDEMPTION_DETAIL`
(
    `KEY_ID`          INT(11) NOT NULL AUTO_INCREMENT,
    `PHONE_NUMBER`    VARCHAR(15) NOT NULL,
    `COUPON_CODE`     VARCHAR(100) NOT NULL,
    `GENERATION_TIME` TIMESTAMP    NOT NULL,
    PRIMARY KEY (`KEY_ID`)
) ENGINE = InnoDB
DEFAULT CHARACTER SET = latin1
COLLATE = latin1_bin;

