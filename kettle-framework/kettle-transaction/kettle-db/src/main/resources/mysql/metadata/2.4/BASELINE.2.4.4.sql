ALTER TABLE KETTLE_DEV.ORDER_PAYMENT_DETAIL
ADD COLUMN PARTNER_ORDER_ID VARCHAR(40) NULL;

CREATE INDEX PARTNER_ORDER_ID_ORDER_PAYMENT_DETAIL ON KETTLE_DEV.ORDER_PAYMENT_DETAIL(PARTNER_ORDER_ID) USING BTREE;

CREATE TABLE KETTLE_DEV.ORDER_PAYMENT_EVENT
(
ORDER_PAYMENT_EVENT_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
ORDER_PAYMENT_DETAIL_ID INTEGER NULL,
EVENT_TYPE VARCHAR(30) NULL,
PAYMENT_STATUS VARCHAR(30) NULL,
CREATE_TIME TIMESTAMP NULL,
ENTITY VARCHAR(30) NULL,
PAYMENT_ID VARCHAR(30) NULL,
CARD_ID VARCHAR(30) NULL,
BANK_CODE VARCHAR(30) NULL,
WALLET_CODE VARCHAR(30) NULL,
VPA_CODE VARCHAR(30) NULL,
SERVICE_FEE INTEGER NULL,
SERVICE_TAX INTEGER NULL,
ERROR_CODE VARCHAR(30) NULL,
ERROR_DESCRIPTION VARCHAR(2000) NULL,
RECEIPT VARCHAR(30) NULL,
ATTEMPTS INTEGER NULL
);
