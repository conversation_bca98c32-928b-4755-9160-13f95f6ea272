
INSERT INTO REF_LOOKUP_TYPE (`RTL_ID`, `R<PERSON>_GROUP`, `RTL_CODE`, `RTL_NAME`, `STATUS`) VALUES ('34', 'DIMENSION', 'SingleDouble', 'Single,Double', 'ACTIVE');
UPDATE REF_LOOKUP_TYPE
SET
    `RTL_CODE` = 'RFCKBK'
WHERE
    `RTL_ID` = '2';
 
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3401', '34', 'Single', 'Single', 'S', 'ACTIVE');
INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('3402', '34', 'Double', 'Double', 'D', 'ACTIVE');
 
 
INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`) VALUES ('651', 'Paneer Sambossa', '-----', '100', '7', '703', 'ACTIVE', 'VEG', '2013-01-01', '9999-12-01', 'CH1011102', '34', 'NET_PRICE', '24', '2015-08-12 11:54:05', '2030-12-01 00:00:00', 'Y');
INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`) VALUES ('652', 'Chicken Sambossa', '-----', '100', '7', '703', 'ACTIVE', 'NON_VEG', '2013-01-01', '9999-12-01', 'CH1011103', '34', 'NET_PRICE', '24', '2015-08-12 11:54:05', '2030-12-01 00:00:00', 'Y');
 

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 651, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where PRODUCT_ID = 10;
 
INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 652, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where PRODUCT_ID = 10;
 
INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,3401,current_timestamp, 39.00 from UNIT_PRODUCT_MAPPING upm,UNIT_DETAIL ud  where upm.UNIT_ID=ud.UNIT_ID and  upm.PRODUCT_ID = 651 and ud.UNIT_CATEGORY = 'CAFE';
 
INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,3401,current_timestamp, 49.00 from UNIT_PRODUCT_MAPPING upm,UNIT_DETAIL ud  where upm.UNIT_ID=ud.UNIT_ID and  upm.PRODUCT_ID = 652 and ud.UNIT_CATEGORY = 'CAFE';
 
INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,3402,current_timestamp, 78.00 from UNIT_PRODUCT_MAPPING upm,UNIT_DETAIL ud  where upm.UNIT_ID=ud.UNIT_ID and  upm.PRODUCT_ID = 651 and ud.UNIT_CATEGORY = 'COD';
 
INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,3402,current_timestamp, 98.00 from UNIT_PRODUCT_MAPPING upm,UNIT_DETAIL ud  where upm.UNIT_ID=ud.UNIT_ID and  upm.PRODUCT_ID = 652 and ud.UNIT_CATEGORY = 'COD';
 
INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,3402,current_timestamp, 78.00 from UNIT_PRODUCT_MAPPING upm,UNIT_DETAIL ud  where upm.UNIT_ID=ud.UNIT_ID and  upm.PRODUCT_ID = 651 and ud.UNIT_CATEGORY = 'DELIVERY';
 
INSERT INTO UNIT_PRODUCT_PRICING (UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select UNIT_PROD_REF_ID,3402,current_timestamp, 98.00 from UNIT_PRODUCT_MAPPING upm,UNIT_DETAIL ud  where upm.UNIT_ID=ud.UNIT_ID and  upm.PRODUCT_ID = 652 and ud.UNIT_CATEGORY = 'DELIVERY';

 
 
INSERT INTO UNIT_PRODUCT_INVENTORY(UNIT_ID, PRODUCT_ID, LAST_UPDATE_TMSTMP, NO_OF_UNIT, LAST_STOCK_OUT_TIME)
select UNIT_ID,PRODUCT_ID, CURRENT_TIMESTAMP,0, '2016-02-01 00:00:00' from UNIT_PRODUCT_MAPPING
where PRODUCT_ID = 651;
INSERT INTO UNIT_PRODUCT_INVENTORY(UNIT_ID, PRODUCT_ID, LAST_UPDATE_TMSTMP, NO_OF_UNIT, LAST_STOCK_OUT_TIME)
select UNIT_ID,PRODUCT_ID, CURRENT_TIMESTAMP,0, '2016-02-01 00:00:00' from UNIT_PRODUCT_MAPPING
where PRODUCT_ID = 652;
 