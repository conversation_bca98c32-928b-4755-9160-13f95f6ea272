CREATE TABLE KETTLE_DEV.CASHBACK_OFFER_DATA (
    ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    UNIT_ID INTEGER,
    LAG_DAYS INT,
    VALIDITY_IN_DAYS INT,
    OFFER_START_DATE TIMESTAMP NULL DEFAULT NULL,
    OFFER_END_DATE TIMESTAMP NULL DEFAULT NULL,
    MAX_NUMBER_OF_ORDER INTEGER,
    CASH<PERSON>CK_PERCENTAGE DECIMAL(10, 2),
    OFFER_STATUS VARCHAR(255)
);


CREATE TABLE KETTLE_DEV`PARTNER_ORDER_RIDER_STATES_DETAIL` (
  `PARTNER_ORDER_RIDER_DETAIL_ID` int(11) NOT NULL AUTO_INCREMENT,
  `BILLING_SERVER_TIME` datetime NOT NULL,
  `KETTLE_ORDER_ID` int(11) NOT NULL,
  `ORDER_DELIVERY_TIME` TIMES<PERSON><PERSON> DEFAULT NULL,
  `ORDER_PICKUP_TIME` TIMESTAMP DEFAULT NULL,
  `PARTNER_NAME` varchar(255) NOT NULL,
  `PARTNER_ORDER_ID` varchar(100) NOT NULL,
  `RIDER_ARRIVED_AT` datetime DEFAULT NULL,
  `RIDER_ASSIGNED_AT` datetime DEFAULT NULL,
  `RIDER_CONTACT` varchar(255) DEFAULT NULL,
  `RIDER_NAME` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
);

create index IDX_PARTNER_ORDER_RIDER_STATES_DETAIL_KETTLE_ORDER_ID ON KETTLE_STAGE.PARTNER_ORDER_RIDER_STATES_DETAIL(KETTLE_ORDER_ID);
create index IDX_PARTNER_ORDER_RIDER_STATES_DETAIL_PARTNER_NAME ON KETTLE_STAGE.PARTNER_ORDER_RIDER_STATES_DETAIL(PARTNER_NAME);
create index IDX_PARTNER_ORDER_RIDER_STATES_DETAIL_PARTNER_ORDER_ID ON KETTLE_STAGE.PARTNER_ORDER_RIDER_STATES_DETAIL(PARTNER_ORDER_ID);

USE `KETTLE_STAGE`;
DROP procedure IF EXISTS `CLEVERTAP_CHARGED_EVENT_ATTRIBUTES`;

USE `KETTLE_STAGE`;
DROP procedure IF EXISTS `KETTLE_STAGE`.`CLEVERTAP_CHARGED_EVENT_ATTRIBUTES`;
;

DELIMITER $$
USE `KETTLE_STAGE`$$
CREATE DEFINER=`root`@`%` PROCEDURE `CLEVERTAP_CHARGED_EVENT_ATTRIBUTES`(IN IN_OFFER_CODE varchar(255), IN IN_CUSTOMER_ID INTEGER, IN IN_ORDER_ID INTEGER, IN IN_ORDER_SOURCE VARCHAR(255),IN IN_BRAND_ID INTEGER)
BEGIN
SELECT
    (SELECT cou.OFFER_DETAIL_ID FROM KETTLE_MASTER_STAGE.COUPON_DETAIL_DATA cou WHERE cou.COUPON_CODE = IN_OFFER_CODE ) offerDetailId ,
    ( SELECT CASE WHEN COUNT(*) <=1 THEN 'Y' ELSE 'N' END
      FROM KETTLE_STAGE.ORDER_DETAIL  WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND BRAND_ID = IN_BRAND_ID ORDER BY BILLING_SERVER_TIME DESC ) isNewCustomer ,
    (SELECT ORDER_ID FROM KETTLE_STAGE.ORDER_DETAIL
     WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID <> IN_ORDER_ID AND BRAND_ID = IN_BRAND_ID ORDER BY BILLING_SERVER_TIME DESC LIMIT 1) previousOrderId ,
(SELECT ORDER_ID FROM KETTLE_STAGE.ORDER_DETAIL
   WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID <> IN_ORDER_ID AND ORDER_SOURCE = IN_ORDER_SOURCE AND BRAND_ID = IN_BRAND_ID ORDER BY BILLING_SERVER_TIME DESC LIMIT 1 ) previousSourceOrderId,
( SELECT BILLING_SERVER_TIME FROM KETTLE_STAGE.ORDER_DETAIL
   WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID <> IN_ORDER_ID AND BRAND_ID = IN_BRAND_ID ORDER BY BILLING_SERVER_TIME DESC LIMIT 1 ) previousBillingServerTime ,
(SELECT  TIMESTAMPDIFF(MINUTE,previousBillingServerTime,BILLING_SERVER_TIME) FROM KETTLE_STAGE.ORDER_DETAIL
   WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID = IN_ORDER_ID AND BRAND_ID = IN_BRAND_ID ORDER BY BILLING_SERVER_TIME DESC LIMIT 1 ) previousOrderTimeDiff ,
(SELECT BILLING_SERVER_TIME FROM KETTLE_STAGE.ORDER_DETAIL
   WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID <> IN_ORDER_ID AND ORDER_SOURCE = IN_ORDER_SOURCE AND BRAND_ID = IN_BRAND_ID ORDER BY BILLING_SERVER_TIME DESC LIMIT 1 ) previousSourceBillingServerTime ,
(SELECT TIMESTAMPDIFF(MINUTE,previousSourceBillingServerTime,BILLING_SERVER_TIME) FROM KETTLE_STAGE.ORDER_DETAIL
   WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID = IN_ORDER_ID AND ORDER_SOURCE = IN_ORDER_SOURCE AND BRAND_ID = IN_BRAND_ID ORDER BY BILLING_SERVER_TIME DESC LIMIT 1 ) previousSourceOrderTimeDiff ,
(SELECT COUNT(*)  FROM KETTLE_STAGE.ORDER_DETAIL WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND BRAND_ID = IN_BRAND_ID ) overallOrderCount ,
(SELECT  COUNT(*) FROM KETTLE_STAGE.ORDER_DETAIL WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_SOURCE = IN_ORDER_SOURCE AND BRAND_ID = IN_BRAND_ID ORDER BY BILLING_SERVER_TIME DESC ) orderSourceOrderCount ,(SELECT CASE
    WHEN cdd.CAMPAIGN_STRATEGY IN ('NBO') THEN 'NBO'
    WHEN cdd.CAMPAIGN_STRATEGY IN ('DELIVERY_NBO') THEN 'DELIVERY_NBO'
    WHEN cdd.CAMPAIGN_STRATEGY IN ('GENERAL') THEN 'GENERAL'
    ELSE 'OTHERS' END AS OFFER_CLASS FROM KETTLE_STAGE.ORDER_DETAIL od LEFT JOIN
     KETTLE_STAGE.CUSTOMER_CAMPIAGN_OFFER_DETAIL ccod ON ccod.COUPON_CODE = od.OFFER_CODE
     LEFT JOIN
     KETTLE_MASTER_STAGE.CAMPAIGN_DETAIL_DATA
     cdd ON cdd.CAMPAIGN_ID = ccod.CAMPAIGN_ID WHERE od.ORDER_ID = IN_ORDER_ID AND  od.BRAND_ID = IN_BRAND_ID  ) offerClass;
END$$

DELIMITER ;
;

CREATE TABLE `KETTLE_DEV`.`STOCK_REFRESH_AUDIT_DATA` (
  `AUDIT_ID` INT NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT(11) NULL,
  `CHANNEL_PARTNER_ID` INT(11) NULL,
  `BRAND_ID` INT(11) NULL,
  `LOG_TIME` DATETIME NULL,
  `TYPE` VARCHAR(45) NULL,
  `STATUS` VARCHAR(45) NULL,
  PRIMARY KEY (`AUDIT_ID`));

  CREATE INDEX  STOCK_REFRESH_AUDIT_DATA_UNIT_ID_IDX ON KETTLE_DEV.STOCK_REFRESH_AUDIT_DATA(UNIT_ID);
  CREATE INDEX  STOCK_REFRESH_AUDIT_DATA_CHANNEL_PARTNER_ID_IDX ON KETTLE_DEV.STOCK_REFRESH_AUDIT_DATA(CHANNEL_PARTNER_ID);
  CREATE INDEX  STOCK_REFRESH_AUDIT_DATA_BRAND_ID_IDX ON KETTLE_DEV.STOCK_REFRESH_AUDIT_DATA(BRAND_ID);
  CREATE INDEX  STOCK_REFRESH_AUDIT_DATA_STATUS_IDX ON KETTLE_DEV.STOCK_REFRESH_AUDIT_DATA(STATUS);


CREATE INDEX ORDER_ITEM_RECIPE_ID ON KETTLE_DEV.ORDER_ITEM(RECIPE_ID) USING BTREE;


ALTER TABLE KETTLE_DEV.PARTNER_ORDER_RIDER_STATES_DETAIL
ADD COLUMN REASONS_FOR_DELAY VARCHAR(100) NULL AFTER RIDER_NAME;

CREATE TABLE `KETTLE_DATA_LAKE`.`DASHBOARD_DATA_LAST_UPDATE` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `ZOMATO_FOOD_RATING` date DEFAULT NULL,
  `GRID_VISIBILITY` date DEFAULT NULL,
  `FOOD_RATING_ZOMATO_ORS` date DEFAULT NULL,
  PRIMARY KEY (`ID`)
);

CREATE TABLE KETTLE_DEV.WEIGHT_CALIBRATION_DETAIL (
    WT_CALIBRATION_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    UNIT_ID INTEGER,
    USER_ID INTEGER,
    USER_NAME VARCHAR(50),
    MONK_NUMBER INTEGER,
    KNOWN_WEIGHT  VARCHAR(25),
    MEASURED_WEIGHT VARCHAR(25),
    MEASUREMENT_TIME TIMESTAMP NULL DEFAULT NULL
);

ALTER TABLE `KETTLE_STAGE`.`CUSTOMER_INFO`
ADD COLUMN `APP_ACTION` VARCHAR(100) NULL AFTER `CARD_CODE`,
ADD COLUMN `APP_ACTION_TIME` TIMESTAMP NULL AFTER `APP_ACTION`;

CREATE TABLE KETTLE_MASTER_DEV.UNIT_CONTACT_DETAIL
(
    ID INTEGER  PRIMARY KEY AUTO_INCREMENT,
    UNIT_ID INTEGER NOT NULL,
    UNIT_NAME VARCHAR(100) NOT NULL,
    FIRST_CONTACT_NAME  VARCHAR(100),
    FIRST_CONTACT_NUMBER  VARCHAR(15),
    SECOND_CONTACT_NAME  VARCHAR(100),
    SECOND_CONTACT_NUMBER  VARCHAR(15),
    THIRD_CONTACT_NAME  VARCHAR(100),
    THIRD_CONTACT_NUMBER  VARCHAR(15),
    STATUS VARCHAR(15) DEFAULT "ACTIVE"
);
CREATE TABLE KETTLE_STAGE.ORDER_DELAY_REASONS (
  DELAY_REASON_ID INT AUTO_INCREMENT NOT NULL,
   KETTLE_ORDER_ID INT NOT NULL,
   PARTNER_ORDER_ID VARCHAR(255) NOT NULL,
   PARTNER_NAME VARCHAR(255) NOT NULL,
   BILLING_SERVER_TIME datetime NOT NULL,
   RIDER_NAME VARCHAR(255) NULL,
   RIDER_CONTACT VARCHAR(255) NULL,
   RIDER_DELAY_REASON VARCHAR(255) NULL,
   OPTIONAL_RIDER_DELAY VARCHAR(255) NULL,
   RIDER_DELAY_UPDATED_BY INT NULL,
   RIDER_DELAY_UPDATED_AT datetime NULL,
   UNIT_ID INT NULL,
   UNIT_NAME VARCHAR(255) NULL,
   CAFE_DELAY_REASON VARCHAR(255) NULL,
   OPTIONAL_CAFE_DELAY VARCHAR(255) NULL,
   CAFE_DELAY_UPDATED_BY INT NULL,
   CAFE_DELAY_UPDATED_AT datetime NULL,
   CONSTRAINT pk_order_delay_reasons PRIMARY KEY (delay_reason_id)
);


CREATE TABLE KETTLE.CHAAYOS_BRANCH_UNIT_MAPPING (
	ID int AUTO_INCREMENT PRIMARY KEY,
	BRANCH_ID int(11) DEFAULT NULL,
	ALIAS text,
	UNIT_ID int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

ALTER TABLE `KETTLE_MASTER_STAGE`.`UNIT_CLOSURE_FORM_DATA`
CHANGE COLUMN `ATTACHMENT` `ATTACHMENT` INT(11) NULL DEFAULT NULL ;

ALTER TABLE `KETTLE_MASTER_STAGE`.`UNIT_CLOSURE_FORM_DATA`
CHANGE COLUMN `ATTACHMENT` `ATTACHMENT_ID` INT(11) NULL DEFAULT NULL ;


ALTER TABLE `KETTLE_MASTER_STAGE`.`UNIT_CLOSURE_EVENT`
CHANGE COLUMN `UPDATED_BY` `UPDATED_BY` INT(11) NULL DEFAULT NULL ;


ALTER TABLE `KETTLE_MASTER_STAGE`.`UNIT_CLOSURE_EVENT`
CHANGE COLUMN `CREATED_BY` `CREATED_BY` INT(11) NOT NULL;


ALTER TABLE `KETTLE_MASTER_STAGE`.`UNIT_CLOSURE_STATE_EVENT`
CHANGE COLUMN `UPDATED_BY` `UPDATED_BY` INT(11) NULL DEFAULT NULL ;

CREATE TABLE KETTLE_MASTER_DEV.`UNIT_CLOSURE_FORM_DATA` (
  `UNIT_CLOSURE_FORM_DATA_ID` int(11) NOT NULL AUTO_INCREMENT,
  `ATTACHMENT_ID` int(11) DEFAULT NULL,
  `COMMENT` varchar(255) DEFAULT NULL,
  `DATE` datetime DEFAULT NULL,
  `UNIT_CLOSURE_EVENT_ID` bigint(20) DEFAULT NULL,
  `UNIT_CLOSURE_FORM_METADATA_ID` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`UNIT_CLOSURE_FORM_DATA_ID`),
  KEY `FKp4y3s4kw6a6yssh6c5fp49v7e` (`UNIT_CLOSURE_EVENT_ID`),
  KEY `FKevycrd6dxqd28bian3ldt6doc` (`UNIT_CLOSURE_FORM_METADATA_ID`)
) ENGINE=MyISAM AUTO_INCREMENT=62 DEFAULT CHARSET=latin1;

CREATE TABLE KETTLE_MASTER_DEV.`UNIT_CLOSURE_FORM_METADATA` (
  `UNIT_CLOSURE_FORM_METADATA_ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ATTACHMENT_REQUIRED` varchar(255) DEFAULT NULL,
  `COMMENT_REQUIRED` varchar(255) DEFAULT NULL,
  `DATE_REQUIRED` varchar(255) DEFAULT NULL,
  `DEPARTMENT` varchar(255) DEFAULT NULL,
  `TASK_DESCRIPTION` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`UNIT_CLOSURE_FORM_METADATA_ID`)
) ENGINE=MyISAM AUTO_INCREMENT=124 DEFAULT CHARSET=latin1;



CREATE TABLE KETTLE_STAGE.`ORDER_PREP_TIME_AGGREGATE` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `BUSINESS_DATE` date DEFAULT NULL,
  `LAST_UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `BRAND_ID` int(11) DEFAULT NULL,
  `UNIT_ID` int(11) DEFAULT NULL,
  `RECORD_TYPE` varchar(30) DEFAULT NULL,
  `DELAY_ORDERS` int(11) DEFAULT NULL,
  `TOTAL_ORDERS` int(11) DEFAULT NULL,
  `AVG_TIME_IN_MINUTES` decimal(10,6) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `ORDER_PREP_TIME_AGGREGATE_ID` (`ID`) USING BTREE,
  KEY `ORDER_PREP_TIME_AGGREGATE_BUSINESS_DATE` (`BUSINESS_DATE`) USING BTREE,
  KEY `ORDER_PREP_TIME_AGGREGATE_BRAND_ID` (`BRAND_ID`) USING BTREE,
  KEY `ORDER_PREP_TIME_AGGREGATE_UNIT_ID` (`UNIT_ID`) USING BTREE,
  KEY `ORDER_PREP_TIME_AGGREGATE_RECORD_TYPE` (`RECORD_TYPE`) USING BTREE,
  KEY `ORDER_PREP_TIME_AGGREGATE_LAST_UPDATE_TIME` (`LAST_UPDATE_TIME`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=473 DEFAULT CHARSET=latin1;


DELIMITER $$
CREATE DEFINER=`root`@`%` PROCEDURE KETTLE_STAGE.`CALCULATE_ORDER_PREP_TIME_AGGREGATE`()
BEGIN

DECLARE CURRENT_BIZ_DATE DATE;
SELECT
    CASE
        WHEN
            TIME(current_timestamp()) < '05:30:00'
        THEN
            DATE_ADD(CURRENT_DATE(),
                INTERVAL - 1 DAY)
        ELSE CURRENT_DATE()
    END
INTO CURRENT_BIZ_DATE;

DELETE FROM KETTLE_STAGE.ORDER_PREP_TIME_AGGREGATE WHERE BUSINESS_DATE = CURRENT_BIZ_DATE;

INSERT INTO KETTLE_STAGE.ORDER_PREP_TIME_AGGREGATE (BUSINESS_DATE, LAST_UPDATE_TIME, BRAND_ID, UNIT_ID, RECORD_TYPE, DELAY_ORDERS, TOTAL_ORDERS, AVG_TIME_IN_MINUTES)
SELECT

    CASE
        WHEN
            TIME(current_timestamp()) < '05:30:00'
        THEN
            DATE_ADD(CURRENT_DATE(),
                INTERVAL - 1 DAY)
        ELSE CURRENT_DATE()
    END BUSINESS_DATE,
    ADDTIME(UTC_TIMESTAMP, '05:30:00') LAST_UPDATE_TIME,
    a.BRAND_ID,
    a.UNIT_ID,
    a.RECORD_TYPE,
    SUM(CASE
        WHEN
            (a.BRAND_ID = 3
                AND a.TIME_IN_MINUTES > 15)
                OR (a.BRAND_ID <> 3
                AND a.TIME_IN_MINUTES > 10)
        THEN
            1
        ELSE 0
    END) DELAY_ORDERS,
    COUNT(*) TOTAL_ORDERS,
    AVG(a.TIME_IN_MINUTES) AVG_TIME_IN_MINUTES
FROM
    (SELECT
        od.UNIT_ID,
            od.BRAND_ID,
            od.ORDER_SOURCE,
            CASE
                WHEN
                    od.BRAND_ID <> 3
                        AND od.ORDER_SOURCE <> 'COD'
                THEN
                    'Dine In'
                WHEN
                    od.BRAND_ID <> 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    'COD'
                WHEN
                    od.BRAND_ID = 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    'GNT'
                WHEN
                    od.BRAND_ID = 3
                        AND od.ORDER_SOURCE <> 'COD'
                THEN
                    'GNT - Dine In'
            END RECORD_TYPE,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, ose.UPDATE_TIME) TIME_IN_MINUTES
    FROM
        KETTLE_STAGE.ORDER_DETAIL od, KETTLE_STAGE.ORDER_STATUS_EVENT ose
    WHERE
        od.ORDER_ID = ose.ORDER_ID
            AND ((od.ORDER_SOURCE = 'COD'
            AND ose.TO_STATUS = 'READY_TO_DISPATCH')
            OR (od.ORDER_SOURCE <> 'COD'
            AND ose.TO_STATUS = 'SETTLED'))
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5 THEN SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')
            AND ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND ose.TRANSITION_STATUS = 'SUCCESS') a
GROUP BY a.BRAND_ID,a.UNIT_ID , a.RECORD_TYPE;


END$$
DELIMITER ;

DELIMITER $$
CREATE DEFINER=`root`@`%` PROCEDURE KETTLE_STAGE.`CALCULATE_ORDER_PREP_TIME_AGGREGATE_FOR_DATE`(IN in_business_date DATE )
BEGIN


DELETE FROM KETTLE_STAGE.ORDER_PREP_TIME_AGGREGATE WHERE BUSINESS_DATE = in_business_date;

INSERT INTO KETTLE_STAGE.ORDER_PREP_TIME_AGGREGATE (BUSINESS_DATE, LAST_UPDATE_TIME, BRAND_ID, UNIT_ID, RECORD_TYPE, DELAY_ORDERS, TOTAL_ORDERS, AVG_TIME_IN_MINUTES)
SELECT

   in_business_date as BUSINESS_DATE,
    ADDTIME(UTC_TIMESTAMP, '05:30:00') LAST_UPDATE_TIME,
    a.BRAND_ID,
    a.UNIT_ID,
    a.RECORD_TYPE,
    SUM(CASE
        WHEN
            (a.BRAND_ID = 3
                AND a.TIME_IN_MINUTES > 15)
                OR (a.BRAND_ID <> 3
                AND a.TIME_IN_MINUTES > 10)
        THEN
            1
        ELSE 0
    END) DELAY_ORDERS,
    COUNT(*) TOTAL_ORDERS,
    AVG(a.TIME_IN_MINUTES) AVG_TIME_IN_MINUTES
FROM
    (SELECT
        od.UNIT_ID,
            od.BRAND_ID,
            od.ORDER_SOURCE,
            CASE
                WHEN
                    od.BRAND_ID <> 3
                        AND od.ORDER_SOURCE <> 'COD'
                THEN
                    'Dine In'
                WHEN
                    od.BRAND_ID <> 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    'COD'
                WHEN
                    od.BRAND_ID = 3
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    'GNT'
                WHEN
                    od.BRAND_ID = 3
                        AND od.ORDER_SOURCE <> 'COD'
                THEN
                    'GNT - Dine In'
            END RECORD_TYPE,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, ose.UPDATE_TIME) TIME_IN_MINUTES
    FROM
        KETTLE_STAGE.ORDER_DETAIL od, KETTLE_STAGE.ORDER_STATUS_EVENT ose
    WHERE
        od.ORDER_ID = ose.ORDER_ID
            AND ((od.ORDER_SOURCE = 'COD'
            AND ose.TO_STATUS = 'READY_TO_DISPATCH')
            OR (od.ORDER_SOURCE <> 'COD'
            AND ose.TO_STATUS = 'SETTLED'))
            AND DATE(od.BILLING_SERVER_TIME) = in_business_date
            AND ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND ose.TRANSITION_STATUS = 'SUCCESS') a
GROUP BY a.BRAND_ID,a.UNIT_ID , a.RECORD_TYPE;


END$$
DELIMITER ;

CREATE TABLE `KETTLE_STAGE`.`ORDER_COMPLAINT_DETAIL_DATA` (
  `ORDER_COMPLAINT_ID` INT AUTO_INCREMENT PRIMARY KEY,
  `ISSUE_TIME` datetime DEFAULT NULL,
  `BILLING_SERVER_TIME` datetime,
  `READY_TO_DISPATCH` datetime DEFAULT NULL,
  `RIDER_ASSIGNED_AT` datetime DEFAULT NULL,
  `RIDER_ARRIVED_AT` datetime DEFAULT NULL,
  `ORDER_PICKUP_TIME` datetime DEFAULT NULL,
  `ORDER_DELIVERY_TIME` datetime DEFAULT NULL,
  `PREPERATION_TIME` bigint(21) DEFAULT NULL,
  `RIDER_ASSIGNED_TIME` bigint(21) DEFAULT NULL,
  `RIDER_ARRIVED_TIME` bigint(21) DEFAULT NULL,
  `RIDER_PICKED_TIME` bigint(21) DEFAULT NULL,
  `RIDER_DELIVERY_TIME` bigint(21) DEFAULT NULL,
  `TOTAL_DELIVERY_TIME` bigint(21) DEFAULT NULL,
  `RIDER_LATE_ARRIVED` varchar(3) NOT NULL DEFAULT '',
  `ORDER_LATE_DELIVERED` varchar(3) NOT NULL DEFAULT '',
  `FOOD_PREPARED_LATE` varchar(3) NOT NULL DEFAULT '',
  `BRAND_NAME` varchar(50) NOT NULL,
  `UNIT_REGION` varchar(25) NOT NULL,
  `UNIT_ID` int(11) NOT NULL DEFAULT '0',
  `BRANCH_ID` int(11) DEFAULT NULL,
  `BUSINESS_UID` varchar(36) NOT NULL DEFAULT '',
  `UNIT_NAME` varchar(255) NOT NULL,
  `UNIT_EMAIL` varchar(50) NOT NULL,
  `CAFE_MANAGER` varchar(255),
  `UNIT_CAFE_MANAGER` varchar(255),
  `UNIT_MANAGER` varchar(255),
  `CAFE_MANAGER_ID` varchar(100) DEFAULT NULL,
  `UNIT_MANAGER_ID` varchar(100) DEFAULT NULL,
  `CAFE_MANAGER_CONTACT` varchar(32) DEFAULT NULL,
  `UNIT_MANAGER_CONTACT` varchar(32) DEFAULT NULL,
  `GENERATED_ORDER_ID` varchar(30) DEFAULT NULL,
  `CUSTOMER_NAME` varchar(50) DEFAULT NULL,
  `ORDER_SOURCE_ID` varchar(100) DEFAULT NULL,
  `PARTNER_CODE` varchar(50) DEFAULT NULL,
  `ITEMS` text,
  `ITEM_NAMES` text,
  `REVENUE` decimal(10,2) DEFAULT NULL,
  `TOTAL_AMOUNT` decimal(10,2) DEFAULT NULL,
  `DISCOUNT` decimal(11,2) DEFAULT NULL,
  `RIDER_NAME` varchar(100) DEFAULT NULL,
  `RIDER_CONTACT` varchar(255) DEFAULT NULL,
  `ORDER_ID` int(11) NOT NULL DEFAULT '0',
  `CUTSOMER_COMPLAINT` text,
  `ISSUE_TYPE` text,
  `COMPLAINT_ITEMS` text,
  `COMPLAINT_TIME` text,
  `PREVIOUS_ITMES` text,
  `MODIFIED_ITEMS` text,
  `COMPLAINT_TYPE` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

Alter TABLE KETTLE_STAGE.ORDER_DELAY_REASONS ADD COLUMN CAFE_DELAY_SOURCE VARCHAR(20) NOT NULL;
Alter TABLE KETTLE_STAGE.ORDER_DELAY_REASONS ADD COLUMN RIDER_DELAY_SOURCE VARCHAR(20) NOT NULL;
UPDATE KETTLE_STAGE.ORDER_DELAY_REASONS SET CAFE_DELAY_SOURCE="CALL CENTER"  ;
UPDATE KETTLE_STAGE.ORDER_DELAY_REASONS SET RIDER_DELAY_SOURCE="CALL CENTER";
ALTER TABLE KETTLE_STAGE.CUSTOMER_FAV_CHAI_MAPPING ADD COLUMN TOTAL_ORDER_COUNT INTEGER DEFAULT 0;

ALTER TABLE KETTLE_STAGE.PRODUCT_DETAIL ADD COLUMN UPDATED_BY INTEGER;
ALTER TABLE KETTLE_STAGE.PRODUCT_DETAIL ADD COLUMN LAST_UPDATE_TIME TIMESTAMP;




CREATE TABLE KETTLE_MASTER_STAGE.UNIT_TO_PARTNER_DQR_MAPPING (
                                                               MAPPING_ID INT(11) NOT NULL AUTO_INCREMENT,
                                                               UNIT_ID INT(11) NOT NULL,
                                                               STATUS VARCHAR(45) NULL,
                                                               PARTNER_NAME VARCHAR(100) NULL,
                                                               MERCHANT_ID VARCHAR(100) NULL,
                                                               MERCHANT_KEY VARCHAR(100) NULL,
                                                               PRIMARY KEY (MAPPING_ID));

ALTER TABLE KETTLE_MASTER_STAGE.OFFER_DETAIL_DATA ADD COLUMN LOYALTY_BURN_POINTS INTEGER;

ALTER TABLE KETTLE_MASTER_STAGE.OFFER_DETAIL_DATA ADD COLUMN SIGNUP_OFFER_APPLICABLE VARCHAR(1) default 'N';
ALTER TABLE KETTLE_DEV.PARTNER_ORDER_RIDER_STATES_DETAIL
ADD COLUMN RIDER_REACHED_CUSTOMER_AT VARCHAR(100) NULL AFTER RIDER_ARRIVED_AT;
