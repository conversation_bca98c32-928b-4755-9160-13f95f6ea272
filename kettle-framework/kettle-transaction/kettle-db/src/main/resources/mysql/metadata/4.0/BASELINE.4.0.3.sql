
CREATE INDEX CUSTOMER_ID_CASH_CARD_DETAIL ON KETTLE_DEV.CASH_CARD_DETAIL(CUSTOMER_ID) USING BTREE;

ALTER TABLE KETTLE_DEV.DELIVERY_DETAIL ADD COLUMN  ALLOTED_NO  VARCHAR(30);

DROP TABLE IF EXISTS KETTLE_DEV.EMPLOYEE_MEAL_ALLOWANCE_DATA;
CREATE TABLE KETTLE_DEV.EMPLOYEE_MEAL_ALLOWANCE_DATA (
MEAL_ID INTEGER AUTO_INCREMENT PRIMARY KEY, 
<PERSON>MPLOYEE_ID INTEGER NOT NULL,
ORDER_ID INTEGER NOT NULL,
AMOUNT_AVAILED DECIMAL(10,6) NOT NULL,
ORDER_TIME TIMESTAMP NOT NULL
);

CREATE INDEX ORDER_TIME_EMPLOYEE_MEAL_ALLOWANCE_DATA ON KETTLE_DEV.EMPLOYEE_MEAL_ALLOWANCE_DATA(ORDER_TIME) USING BTREE;
CREATE INDEX EMPLOYEE_ID_EMPLOYEE_MEAL_ALLOWANCE_DATA ON KETTLE_DEV.EMPLOYEE_MEAL_ALLOWANCE_DATA(EMPLOYEE_ID) USING BTREE;


DROP TABLE IF EXISTS KETTLE_DEV.MONK_LOG;
CREATE TABLE KETTLE_DEV.MONK_LOG(
MONK_LOG_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ORDER_ID INTEGER NOT NULL,
ORDER_ITEM_ID INTEGER NOT NULL,
TASK_CREATION_TIME TIMESTAMP NOT NULL,
MONK_NAME VARCHAR(20) NOT NULL,
MONK_EVENT VARCHAR(3) NOT NULL,
TIME_AT_EVENT INTEGER NOT NULL,
REASSIGNED VARCHAR(1) NOT NULL
);
