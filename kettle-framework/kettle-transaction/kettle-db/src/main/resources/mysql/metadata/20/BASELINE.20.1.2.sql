CREATE TABLE KETTLE_STAGE.STOCK_DATA_UNIT_WISE (
    STOCK_DATA_UNIT_WISE_ID INT PRIMARY KEY AUTO_INCREMENT,
    UNIT_ID INT(11),
    BRAND_ID INT(11),
    BUSINESS_DATE date DEFAULT NULL,
    CAFE_OPENING_TIME datetime DEFAULT NULL,
    CAFE_CLOSING_TIME datetime DEFAULT NULL,
    DELIVERY_OPENING_TIME datetime DEFAULT NULL,
    DELIVERY_CLOSING_TIME datetime DEFAULT NULL,
    NO_OF_SKU INT(11),
    TOTAL_DINEIN_OP_TIME INT(11),
    TOTAL_DELIVERY_OP_TIME INT(11),
    TOTAL_DINEIN_DOWN_TIME INT(11),
    IS_TWENTY_FOUR_CAFE VARCHAR(2)
);
ALTER TABLE KETTLE_STAGE.MONK_ORDER_DETAIL ADD COLUMN FORCEFULLY_REMOVED VARCHAR(4) DEFAULT 'N';

ALTER TABLE KETTLE_DEV.EVENT_PUSH_TRACK
ADD COLUMN PUBLISH_TIME TIMESTAMP NULL,
ADD COLUMN PROCESS_START_TIME TIMESTAMP NULL,
ADD COLUMN PROCESS_END_TIME TIMESTAMP NULL,
ADD COLUMN TOTAL_PROCESS_TIME BIGINT NULL,
ADD COLUMN CLEVERTAP_RESPONSE_TIME BIGINT NULL;

ALTER TABLE KETTLE_STAGE.CLEVERTAP_PROFILE_PUSH_TRACK
ADD COLUMN PUBLISH_TIME TIMESTAMP NULL,
ADD COLUMN PROCESS_START_TIME TIMESTAMP NULL,
ADD COLUMN PROCESS_END_TIME TIMESTAMP NULL,
ADD COLUMN TOTAL_PROCESS_TIME BIGINT NULL,
ADD COLUMN CLEVERTAP_RESPONSE_TIME BIGINT NULL;

ALTER TABLE `KETTLE_STAGE`.`VOUCHER_DATA`
ADD COLUMN `GR_NUMBER` INT(11) NULL AFTER `PNL_INCLUSION_DATE`,
ADD UNIQUE INDEX `GR_NUMBER_UNIQUE` (`GR_NUMBER` ASC);
;





CREATE TABLE KETTLE_STAGE.MONK_TASK_COMPLETION_STATS
(
    MONK_TASK_COMPLETION_STATS_ID   INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    ORDER_ID                        INT                NULL,
    TASK_ID                         INT                NULL,
    UNIT_ID                         INT                NULL,
    ACTUAL_BOIL_COUNT               INT                NULL,
    EXPECTED_BOIL_COUNT             INT                NULL,
    TOTAL_TIME_FOR_POURING          INT                NULL,
    TOTAL_TIME_FOR_ADD_PATTI        INT                NULL,
    TOTAL_TIME_FOR_BREWING          INT                NULL,
    TOTAL_TIME_FOR_TASK_COMPLETION  INT                NULL,
    ACTUAL_WATER_DISPENSED          INT                NULL,
    EXPECTED_WATER_TO_DISPENSE      INT                NULL,
    ACTUAL_MILK_DISPENSED           INT                NULL,
    EXPECTED_MILK_TO_DISPENSE       INT                NULL,
    FINAL_QUANTITY_WITH_INGREDIENTS INT                NULL,
    BOILS_DETECTED_BEFORE_ADD_PATTI INT                NULL,
    MINIMUM_TARGET_WEIGHT           INT                NULL,
    FIRMWARE_VERSION                DECIMAL(16,6)            NULL,
    IS_ACTUAL_COMPLETION_LOG        VARCHAR(1)       NULL,
    LOG_ADD_TIME                    TIMESTAMP           NULL
);

ALTER TABLE KETTLE_DEV.EVENT_PUSH_TRACK
ADD COLUMN PUBLISH_TIME TIMESTAMP NULL,
ADD COLUMN PROCESS_START_TIME TIMESTAMP NULL,
ADD COLUMN PROCESS_END_TIME TIMESTAMP NULL,
ADD COLUMN TOTAL_PROCESS_TIME BIGINT NULL,
ADD COLUMN CLEVERTAP_RESPONSE_TIME BIGINT NULL;

ALTER TABLE KETTLE_STAGE.CLEVERTAP_PROFILE_PUSH_TRACK
ADD COLUMN PUBLISH_TIME TIMESTAMP NULL,
ADD COLUMN PROCESS_START_TIME TIMESTAMP NULL,
ADD COLUMN PROCESS_END_TIME TIMESTAMP NULL,
ADD COLUMN TOTAL_PROCESS_TIME BIGINT NULL,
ADD COLUMN CLEVERTAP_RESPONSE_TIME BIGINT NULL;


CREATE TABLE KETTLE_STAGE.`ASSEMBLY_NOTIFICATION_DATA`
(
    `KEY_ID`                  int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `UNIT_ID`                 int(11) NOT NULL,
    `NOTIFICATION_TYPE`       varchar(30) NOT NULL,
    `NOTIFICATION_START_TIME` datetime     DEFAULT NULL,
    `NOTIFICATION_END_TIME`   datetime     DEFAULT NULL,
    `NOTIFICATION_STATUS`     varchar(100) DEFAULT NULL,
    `RESPONSE_MESSAGE`        varchar(300) DEFAULT NULL
);

ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN RECIPE_REGION_AT_MONK VARCHAR(20) DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN ACTUAL_RECIPE_REGION_OF_UNIT VARCHAR(20) DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN IS_SPLIT VARCHAR(1) DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN LINKED_TASK_ID INT DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN MONK_NAME VARCHAR(15) DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN HOT_STATION_VERSION VARCHAR(15) DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN ASSEMBLY_VERSION VARCHAR(15) DEFAULT NULL;

INSERT INTO `KETTLE_MASTER_DUMP`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Assembly Notifications', 'Access to see Assembly Notifications', 'ACTIVE', '5');

INSERT INTO `KETTLE_MASTER_DUMP`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('ADMN_ASMBLY_NTFCTN', '5', 'MENU', 'SHOW', 'Admin -> Assembly Notification', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DUMP`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DUMP.USER_ROLE_DATA WHERE ROLE_NAME = 'Assembly Notifications'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DUMP.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_ASMBLY_NTFCTN'), 'ACTIVE', '120063', '2024-07-18 12:00:00');

CREATE TABLE KETTLE_STAGE.MONK_BROKER_STATUS (
    MONK_BROKER_STATUS_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    UNIT_ID INTEGER,
    START_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    END_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CREATED_AT TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE KETTLE_STAGE.MONK_DIAGNOSIS_EVENT
(
    MONK_DIAGNOSIS_EVENT_ID INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    UNIT_ID                 INT                NOT NULL,
    USER_ID                 INT                NOT NULL,
    ENTERED_USER_NAME       VARCHAR(200)        NULL,
    CREATED_AT              TIMESTAMP           NULL,
    LOGGED_AT_SERVER        TIMESTAMP           NULL
);


CREATE TABLE KETTLE_STAGE.MONK_DIAGNOSIS_TROUBLE_SHOOT
(
    MONK_DIAGNOSIS_TROUBLE_SHOOT_ID INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    MONK_DIAGNOSIS_EVENT_ID         INT                NOT NULL,
    DIAGNOSIS_AT                    TIMESTAMP           NULL,
    CODE                            INT                NOT NULL,
    CODE_MEANING                    VARCHAR(255)       NOT NULL,
    ACKNOWLEDGED                    VARCHAR(1)       NOT NULL
);


CREATE TABLE `KETTLE_MASTER_STAGE`.`ORDER_ITEM_STATUS` (
  `ORDER_ITEM_STATUS_ID` INT NOT NULL AUTO_INCREMENT,
  `ORDER_ITEM_ID` INT(11) NOT NULL,
  `FROM_STATUS` VARCHAR(100) NULL,
  `TO_STATUS` VARCHAR(100) NULL,
  `UPDATED_BY` INT(11) NULL,
  `UPDATION_TIME` TIMESTAMP NULL,
  `TRANSITION_STATUS` VARCHAR(100) NULL,
  PRIMARY KEY (`ORDER_ITEM_STATUS_ID`));

  ALTER TABLE `KETTLE_MASTER_STAGE`.`ORDER_ITEM_STATUS`
  ADD COLUMN `ORDER_ID` INT(11) NULL AFTER `ORDER_ITEM_STATUS_ID`;

ALTER TABLE KETTLE.MONK_DIAGNOSIS_EVENT ADD COLUMN MONK_NAME VARCHAR(20) DEFAULT NULL;

ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN CLUBBED_WITH_TASK INT DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN WEIGHT_CALIBRATION_POINT DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN FORCEFULLY_REMOVED_REASON VARCAHR(50) DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN TOTAL_MONKS INT DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN ACTIVE_MONKS INT DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN DE_ACTIVATED_MONKS INT DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN NOT_CONNECTED_MONKS INT DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN ACTIVE_BUT_NOT_CONNECTED_MONKS INT DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN UNDER_CLEANING_MONKS INT DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN UNDER_RESET_MONKS INT DEFAULT NULL;

ALTER TABLE `KETTLE_STAGE`.`ORDER_ITEM_STATUS`
ADD COLUMN `TABLE_REQUEST_ID` INT(11) NULL AFTER `ORDER_ITEM_STATUS_ID`;

ALTER TABLE KETTLE.ASSEMBLY_NOTIFICATION_DATA DROP COLUMN UNIT_ID;
ALTER TABLE KETTLE.ASSEMBLY_NOTIFICATION_DATA DROP COLUMN NOTIFICATION_TYPE;
ALTER TABLE KETTLE.ASSEMBLY_NOTIFICATION_DATA ADD COLUMN ASSEMBLY_NOTIFICATION_UNIT_WISE_DATA_ID INT DEFAULT NULL ;
ALTER TABLE KETTLE.ASSEMBLY_NOTIFICATION_DATA ADD COLUMN NOTIFICATION_TRIGGERED_BY INT DEFAULT NULL ;




CREATE TABLE `KETTLE_MASTER_STAGE`.`ORDER_ITEM_STATUS` (
  `ORDER_ITEM_STATUS_ID` INT NOT NULL AUTO_INCREMENT,
  `ORDER_ITEM_ID` INT(11) NOT NULL,
  `FROM_STATUS` VARCHAR(100) NULL,
  `TO_STATUS` VARCHAR(100) NULL,
  `UPDATED_BY` INT(11) NULL,
  `UPDATION_TIME` TIMESTAMP NULL,
  `TRANSITION_STATUS` VARCHAR(100) NULL,
  PRIMARY KEY (`ORDER_ITEM_STATUS_ID`));

  ALTER TABLE `KETTLE_MASTER_STAGE`.`ORDER_ITEM_STATUS`
  ADD COLUMN `ORDER_ID` INT(11) NULL AFTER `ORDER_ITEM_STATUS_ID`;

ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN CLUBBED_WITH_TASK INT DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN WEIGHT_CALIBRATION_POINT DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE.MONK_TASK_COMPLETION_STATS ADD COLUMN FORCEFULLY_REMOVED_REASON VARCAHR(50) DEFAULT NULL;



CREATE TABLE APP_OFFER_APPLICABILITY_DATA (
  APP_OFFER_APPLICABILITY_ID INT AUTO_INCREMENT NOT NULL,
   APP_OFFER_APPLICABILITY_FLAG VARCHAR(255) NULL,
   IS_APPLICABLE VARCHAR(255) NULL,
   APP_OFFER_DETAIL_DATA_ID INT NULL,
   CONSTRAINT PK_APP_OFFER_APPLICABILITY_DATA PRIMARY KEY (APP_OFFER_APPLICABILITY_ID)
);

ALTER TABLE APP_OFFER_APPLICABILITY_DATA ADD CONSTRAINT FK_APP_OFFER_APPLICABILITY_DATA_ON_APP_OFFER_DETAIL_DATA FOREIGN KEY (APP_OFFER_DETAIL_DATA_ID) REFERENCES APP_OFFER_DETAIL_DATA (ID);

CREATE TABLE KETTLE_STAGE.SUPERU_ORDER_ANALYSIS_DATA (
    EVENT_ID INT AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID INT,
    UNIT_NAME VARCHAR(255),
    CUSTOMER_ID INT,
    EMP_ID INT,
    ORDER_ID INT,
    BILLING_SERVER_TIME VARCHAR(255),
    PRODUCTS_AND_QUANTITY VARCHAR(255),
    REVENUE FLOAT,
    CUSTOMER_TYPE VARCHAR(255),
    APPROPRIATE_UPSELL BOOLEAN,
    APPROPRIATE_UPSELL_ITEM VARCHAR(255),
    REASON VARCHAR(255),
    STATEMENT_SPEAKER VARCHAR(255),
    DRINKS_PRODUCTS_AND_QUANTITY VARCHAR(255),
    EATABLES_PRODUCTS_AND_QUANTITY VARCHAR(255),
    MERCHANDISE_PRODUCTS_AND_QUANTITY VARCHAR(255),
    BOT_PRODUCTS_AND_QUANTITY VARCHAR(255),
    EOT_PRODUCTS_AND_QUANTITY VARCHAR(255),
    DAYPART_CONVERSION INT,
    GREETING BOOLEAN,
    ASK_FOR_CONTACT_NUMBER BOOLEAN,
    MENTION_LOYALTY_PROGRAM_IF_DENIED_CONTACT BOOLEAN,
    THANKING BOOLEAN,
    SUGGEST_DESI_CHAI_AND_POPULAR_COMBOS_IF_NEW_CUSTOMER BOOLEAN,
    EXPLAIN_LOYALTY_PROGRAM_IF_NEW_CUSTOMER BOOLEAN,
    ATTEMPT_CROSS_SELL_RELEVANT_ITEM BOOLEAN,
    USE_CUSTOMER_NAME_THRICE BOOLEAN,
    PROVIDE_PRECISE_PRODUCT_DESCRIPTIONS_FOR_QUERIES BOOLEAN,
    ATTEMPT_UPSELL_WHEN_OPPORTUNITY BOOLEAN,
    SUGGEST_MERCHANDISE BOOLEAN,
    SUGGEST_TOP_UP_WALLET_IF_AVAILABLE BOOLEAN,
    PROVIDE_SPECIFIC_RELEVANT_SUGGESTIONS BOOLEAN,
    PROVIDE_PRECISE_DESCRIPTIONS_FOR_QUERIES_AND_UPSELL BOOLEAN
);

CREATE TABLE KETTLE_STAGE.SUPERU_ALERTS_DATA (
    ALERT_ID INT AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID INT,
    STORE_NAME VARCHAR(255),
    ERROR_TYPE VARCHAR(255),
    TIMESTAMP VARCHAR(255),
    DESCRIPTION VARCHAR(255)
);
ALTER TABLE `KETTLE_STAGE`.`UNIT_EXPENDITURE_DETAIL`
ADD COLUMN PPE_COST DECIMAL(10, 2),
ADD COLUMN PPE_COST_TAX DECIMAL(10, 2);

ALTER TABLE `KETTLE_STAGE`.`UNIT_BUDGETORY_DETAIL`
ADD COLUMN PPE_COST DECIMAL(10, 2),
ADD COLUMN PPE_COST_TAX DECIMAL(10, 2);

ALTER TABLE `KETTLE_STAGE`.`UNIT_EXPENSE_DETAIL`
ADD COLUMN PPE_COST DECIMAL(10, 2),
ADD COLUMN DEL_PPE_COST DECIMAL(10, 2);


ALTER TABLE KETTLE_STAGE.CAFE_RAISE_REQUEST_APPROVAL
ADD COLUMN REQUEST_TIMESTAMP TIMESTAMP NULL;

ALTER TABLE KETTLE_STAGE.CAFE_RAISE_REQUEST_APPROVAL
ADD COLUMN APPROVAL_TIMESTAMP TIMESTAMP NULL;

ALTER TABLE KETTLE_STAGE.CAFE_RAISE_REQUEST_APPROVAL
ADD COLUMN COMPLETION_TIMESTAMP TIMESTAMP NULL;

ALTER TABLE KETTLE_STAGE.CAFE_RAISE_REQUEST_APPROVAL
MODIFY COLUMN REQUEST_TIMESTAMP TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE KETTLE_STAGE.CAFE_RAISE_REQUEST_APPROVAL
MODIFY COLUMN APPROVAL_TIMESTAMP TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE KETTLE_STAGE.CAFE_RAISE_REQUEST_APPROVAL
MODIFY COLUMN COMPLETION_TIMESTAMP TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE KETTLE_MASTER_STAGE.MENU_SEQUENCE_DATA
ADD COLUMN MENU_STATUS VARCHAR(45) NOT NULL DEFAULT 'ACTIVE';

-- (FOR BRAND ID WISE REFACTOR)
ALTER TABLE KETTLE_MASTER_STAGE.OFFER_DETAIL_DATA ADD COLUMN BRAND_ID INTEGER;
UPDATE KETTLE_MASTER_STAGE.OFFER_DETAIL_DATA SET BRAND_ID = 1 WHERE BRAND_ID IS NULL;

ALTER TABLE KETTLE_MASTER_STAGE.APP_OFFER_DETAIL_DATA ADD COLUMN BRAND_ID INTEGER;
UPDATE KETTLE_MASTER_STAGE.APP_OFFER_DETAIL_DATA SET BRAND_ID = 1 WHERE BRAND_ID IS NULL;

-- (FOR CUSTOMER VISIBILITY)
ALTER TABLE KETTLE_MASTER_STAGE.COUPON_DETAIL_DATA ADD COLUMN CUSTOMER_VISIBILITY VARCHAR(1) DEFAULT 'Y' NOT NULL;

-- (FOR PRODUCT CHECKLIST EVENT)
CREATE TABLE KETTLE_MASTER_STAGE.PRODUCT_CHECKLIST_EVENT (
    EVENT_ID INT PRIMARY KEY AUTO_INCREMENT,
    USER_ID INT NOT NULL,
    GENERATION_TIMESTAMP TIMESTAMP,
    COMPLETION_TIMESTAMP TIMESTAMP,
    STATUS VARCHAR(20),
    FILE_LINK VARCHAR(1000)
);

-- (Following 4 queries used in Kettle Admin Brandwise Refactor)

CREATE TABLE KETTLE_MASTER_STAGE.COMPANY_BRAND_MAPPING (
	COMPANY_BRAND_MAPPING_ID INT PRIMARY KEY AUTO_INCREMENT,
    COMPANY_ID INT NOT NULL,
    BRAND_ID INT NOT NULL,
    MAPPING_STATUS VARCHAR(45) NOT NULL
);

CREATE TABLE KETTLE_MASTER_STAGE.UNIT_BRAND_MAPPING (
	UNIT_BRAND_MAPPING_ID INT PRIMARY KEY AUTO_INCREMENT,
    UNIT_ID INT NOT NULL,
    BRAND_ID INT NOT NULL,
    MAPPING_STATUS VARCHAR(45) NOT NULL,
    CREATION_TIMESTAMP TIMESTAMP NOT NULL,
    CREATED_BY INT NOT NULL,
    UPDATION_TIMESTAMP TIMESTAMP,
    UPDATED_BY INT
);

CREATE TABLE KETTLE_MASTER_STAGE.ROLE_BRAND_MAPPING (
	ROLE_BRAND_MAPPING_ID INT PRIMARY KEY AUTO_INCREMENT,
	ROLE_ID INT NOT NULL,
	BRAND_ID INT NOT NULL,
	STATUS VARCHAR(20) NOT NULL
);

ALTER TABLE KETTLE_MASTER_STAGE.EMPLOYEE_ROLE_MAPPING
ADD COLUMN BRAND_ID INT NOT NULL DEFAULT 1;

CREATE TABLE KETTLE_STAGE.MONK_X_TWO_LOG_DATA
(
    MONK_X_TWO_LOG_DATA_ID              INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    TASK_SENT_TO_MONK_TIME              TIMESTAMP           NULL,
    TASK_ACCEPTED_TIME                  TIMESTAMP           NULL,
    TASK_COMPLETED_TIME                 TIMESTAMP           NULL,
    TASK_STOPPED_TIME                   TIMESTAMP           NULL,
    TASK_STOP_ACCEPTED_TIME             TIMESTAMP           NULL,
    TASK_STOP_REJECTED_TIME             TIMESTAMP           NULL,
    TASK_COOKING_COMPLETED_TIME         TIMESTAMP           NULL,
    TIME_TO_DETECT_VESSEL               INT                NULL,
    VESSEL_OFF_POSITION_TIME            INT                NULL,
    WATER_QUANTITY                      INT                NULL,
    MILK_QUANTITY                       INT                NULL,
    WATER_POURING_TIME                  INT                NULL,
    MILK_POURING_TIME                   INT                NULL,
    TOTAL_TIME_FOR_POURING              INT                NULL,
    TOTAL_TIME_FOR_ADD_PATTI            INT                NULL,
    TOTAL_TIME_FOR_BREWING              INT                NULL,
    TOTAL_TIME_FOR_TASK_COMPLETION      INT                NULL,
    WEB_APP_VERSION                     VARCHAR(20)       NULL,
    TOTAL_TIME_TO_COMPLETE_COOKING      INT                NULL,
    TIME_TO_REMOVE_PAN_AFTER_COMPLETION INT                NULL
);

ALTER TABLE KETTLE_STAGE.MONK_TASK_COMPLETION_STATS ADD COLUMN MONK_X_TWO_LOG_DATA_ID INT DEFAULT NULL;

CREATE TABLE KETTLE.MONK_XTWO_FAILURE_LOGS (
    MONK_XTWO_FAILURE_LOGS_ID INT AUTO_INCREMENT PRIMARY KEY,
    MONK_NAME VARCHAR(255),
    MONK_FAILED_TIMESTAMP DATETIME,
    UNIT_ID INT
);
CREATE TABLE IF NOT EXISTS KETTLE_STAGE.NON_WASTAGE_ITEM (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    ORDER_ID INT NOT NULL,
    UNIT_ID INT NOT NULL,
    ITEM_ID INT NOT NULL,
    QUANTITY INT NOT NULL,
    CREATED_AT TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ORDER_ID) REFERENCES KETTLE_STAGE.ORDER_DETAIL(ORDER_ID),
    FOREIGN KEY (UNIT_ID) REFERENCES KETTLE_MASTER_STAGE.UNIT_DETAIL(UNIT_ID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE INDEX idx_non_wastage_item_order_id ON KETTLE_STAGE.NON_WASTAGE_ITEM(ORDER_ID);
CREATE INDEX idx_non_wastage_item_unit_id ON KETTLE_STAGE.NON_WASTAGE_ITEM(UNIT_ID);
CREATE INDEX idx_non_wastage_item_item_id ON KETTLE_STAGE.NON_WASTAGE_ITEM(ITEM_ID);

CREATE TABLE KETTLE_STAGE.MONK_MANUAL_TASK_ADDON (
    MONK_MANUAL_TASK_ADDON_ID INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    MONK_MANUAL_TASK_ID INT NOT NULL,
    ADDON_ID INT NOT NULL,
    PRODUCT_SOURCE_SYSTEM VARCHAR(15),
    DIMENSION VARCHAR(15),
    QUANTITY DECIMAL(16,6),
    ADDON_NAME VARCHAR(55),
    ADDON_TYPE VARCHAR(15),
    UNIT_OF_MEASURE VARCHAR(10),
    DEFAULT_SETTING VARCHAR(1) NOT NULL DEFAULT 'N',
    KEY idx_monk_manual_task_id (MONK_MANUAL_TASK_ID),
    KEY idx_addon_id (ADDON_ID)
);
CREATE TABLE KETTLE_STAGE.MONK_MANUAL_TASK (
    MONK_MANUAL_TASK_ID INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    ORDER_ID VARCHAR(100),
    UNIT_ID INT,
    EMPLOYEE_ID INT,
    PRODUCT_ID INT,
    DIMENSION VARCHAR(50),
    KEY idx_order_id (ORDER_ID),
    KEY idx_unit_id (UNIT_ID),
    KEY idx_employee_id (EMPLOYEE_ID),
    KEY idx_product_id (PRODUCT_ID),
    KEY idx_dimension (DIMENSION)
);
CREATE TABLE KETTLE_STAGE.UNIT_APP_VERSION_METADATA (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID VARCHAR(255) NOT NULL,
    VERSION_TIME DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ASSEMBLY_VERSION VARCHAR(50),
    WORKSTATION_VERSION VARCHAR(50),
    WORKSTATION_WITH_MONK_VERSION VARCHAR(50)
);
CREATE TABLE KETTLE_STAGE.UNIT_APPLICATION_VERSION (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID VARCHAR(255) NOT NULL,
    VERSION_TIME DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    APPLICATION_TYPE VARCHAR(50) NOT NULL,
    APPLICATION_VERSION VARCHAR(50)
);
CREATE TABLE KETTLE_STAGE.MONK_RECIPE_ADDONS_DATA (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    MONK_TASK_COMPLETION_STATS_ID BIGINT NOT NULL,
    ADDON_NAME VARCHAR(255) NOT NULL,
    QUANTITY INT NOT NULL
);
CREATE TABLE KETTLE_STAGE.UNIT_DEVICE_WHITELIST (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID VARCHAR(50) NOT NULL,
    MAC_ADDRESS VARCHAR(17) NOT NULL UNIQUE,
    STATUS VARCHAR(20) DEFAULT 'ACTIVE',
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    MONK_TASK_COMPLETION_STATS_ID BIGINT NOT NULL,
    ADDON_NAME VARCHAR(255) NOT NULL,
    QUANTITY INT NOT NULL
);

CREATE TABLE KETTLE_STAGE.MONK_TROUBLE_SHOOT_DATA (
    MONK_TROUBLE_SHOOT_DATA_ID INT AUTO_INCREMENT PRIMARY KEY,
    MONK_CALIBRATION_EVENT_ID INT NOT NULL,
    TROUBLE_SHOOT_CODE INT DEFAULT NULL,
    TROUBLE_SHOOT_CODE_MEANING VARCHAR(100) DEFAULT NULL
);