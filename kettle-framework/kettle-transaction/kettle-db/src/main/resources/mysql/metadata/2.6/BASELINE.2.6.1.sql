ALTER TABLE KETTLE_DEV.CASH_CARD_DETAIL ADD COLUMN BUYER_ID INT;
ALTER TABLE KETTLE_DEV.CASH_CARD_DETAIL ADD COLUMN PURCHASE_DATE DATETIME;
ALTER TABLE KETTLE_DEV.CASH_CARD_DETAIL ADD COLUMN ACTIVATION_DATE DATETIME;

ALTER TABLE KETTLE_DEV.CASH_CARD_DETAIL
CHANGE COLUMN END_DATE END_DATE DATE NULL ;

ALTER TABLE KETTLE_DEV.CASH_CARD_DETAIL
CHANGE COLUMN CARD_STATUS CARD_STATUS VARCHAR(30) NOT NULL ;

CREATE TABLE CASH_CARD_EVENT_LOG(
    CASH_CARD_EVENT_LOG_ID INT PRIMARY KEY AUTO_INCREMENT,
    CARD_NUMBER VARCHAR(20) NOT NULL,
    EVENT_NAME VARCHAR(100) NOT NULL,
    EVENT_DETAIL VARCHAR(200) DEFAULT NULL,
    EVENT_TIME DATETIME DEFAULT NULL
);

ALTER TABLE KETTLE_DEV.WORKSTATION_LOG
ADD COLUMN MONK_NAME VARCHAR(10);

ALTER TABLE KETTLE_DEV.CASH_CARD_DETAIL ADD COLUMN LAST_MODIFIED DATETIME DEFAULT NULL;

ALTER TABLE KETTLE_DEV.CASH_CARD_EVENT_LOG ADD COLUMN CARD_SERIAL VARCHAR(30) DEFAULT NULL;

ALTER TABLE KETTLE_DEV.CASH_CARD_EVENT_LOG MODIFY COLUMN CARD_NUMBER VARCHAR(20) DEFAULT NULL;