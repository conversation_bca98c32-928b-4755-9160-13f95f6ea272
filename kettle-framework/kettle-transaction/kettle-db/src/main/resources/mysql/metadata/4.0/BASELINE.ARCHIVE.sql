set foreign_key_checks = 0;

CREATE TABLE KETTLE_ARCHIVE_DUMP.ADDON_PRODUCT_DATA_BACKUP
AS SELECT * from KETTLE_DUMP.ADDON_PRODUCT_DATA;
drop table KETTLE_DUMP.ADDON_PRODUCT_DATA;

CREATE TABLE KETTLE_ARCHIVE_DUMP.COMPANY_DETAIL_BACKUP
AS SELECT * from KETTLE_DUMP.COMPANY_DETAIL;
drop table KETTLE_DUMP.COMPANY_DETAIL;

CREATE TABLE KETTLE_ARCHIVE_DUMP.BUSINESS_DIVISION_BACKUP
AS SELECT * from KETTLE_DUMP.BUSINESS_DIVISION;
drop table KETTLE_DUMP.BUSINESS_DIVISION;

CREATE TABLE KETTLE_ARCHIVE_DUMP.UNIT_DETAIL_BACKUP
AS SELECT * from KETTLE_DUMP.UNIT_DETAIL;
drop table KETTLE_DUMP.UNIT_DETAIL; 

CREATE TABLE KETTLE_ARCHIVE_DUMP.ADDRESS_INFO_BACKUP
AS SELECT * from KETTLE_DUMP.ADDRESS_INFO;
drop table KETTLE_DUMP.ADDRESS_INFO;

CREATE TABLE KETTLE_ARCHIVE_DUMP.AUTHORIZED_MAC_ADDRESSES_BACKUP
AS SELECT * from KETTLE_DUMP.AUTHORIZED_MAC_ADDRESSES;
drop table KETTLE_DUMP.AUTHORIZED_MAC_ADDRESSES;

CREATE TABLE KETTLE_ARCHIVE_DUMP.COUPON_CODE_USAGE_DATA_BACKUP
AS SELECT * from KETTLE_DUMP.COUPON_CODE_USAGE_DATA;
drop table KETTLE_DUMP.COUPON_CODE_USAGE_DATA;

CREATE TABLE KETTLE_ARCHIVE_DUMP.COUPON_DETAIL_DATA_BACKUP
AS SELECT * from KETTLE_DUMP.COUPON_DETAIL_DATA;
drop table KETTLE_DUMP.COUPON_DETAIL_DATA;

CREATE TABLE KETTLE_ARCHIVE_DUMP.COUPON_DETAIL_MAPPING_DATA_BACKUP
AS SELECT * from KETTLE_DUMP.COUPON_DETAIL_MAPPING_DATA;
drop table KETTLE_DUMP.COUPON_DETAIL_MAPPING_DATA;

CREATE TABLE KETTLE_ARCHIVE_DUMP.PAYMENT_MODE_BACKUP
AS SELECT * from KETTLE_DUMP.PAYMENT_MODE;
drop table KETTLE_DUMP.PAYMENT_MODE;

CREATE TABLE KETTLE_ARCHIVE_DUMP.DENOMINATION_BACKUP
AS SELECT * from KETTLE_DUMP.DENOMINATION;
drop table KETTLE_DUMP.DENOMINATION;

CREATE TABLE KETTLE_ARCHIVE_DUMP.DEPARTMENT_BACKUP
AS SELECT * from KETTLE_DUMP.DEPARTMENT;
drop table KETTLE_DUMP.DEPARTMENT;

CREATE TABLE KETTLE_ARCHIVE_DUMP.DEPARTMENT_DESIGNATION_MAPPING_BACKUP
AS SELECT * from KETTLE_DUMP.DEPARTMENT_DESIGNATION_MAPPING;
drop table KETTLE_DUMP.DEPARTMENT_DESIGNATION_MAPPING;

CREATE TABLE KETTLE_ARCHIVE_DUMP.DESIGNATION_BACKUP
AS SELECT * from KETTLE_DUMP.DESIGNATION;
drop table KETTLE_DUMP.DESIGNATION;

CREATE TABLE KETTLE_ARCHIVE_DUMP.EMPLOYEE_DETAIL_BACKUP
AS SELECT * from KETTLE_DUMP.EMPLOYEE_DETAIL;
drop table KETTLE_DUMP.EMPLOYEE_DETAIL;

CREATE TABLE KETTLE_ARCHIVE_DUMP.EMPLOYEE_PASS_CODE_BACKUP
AS SELECT * from KETTLE_DUMP.EMPLOYEE_PASS_CODE;
drop table KETTLE_DUMP.EMPLOYEE_PASS_CODE;

CREATE TABLE KETTLE_ARCHIVE_DUMP.EMPLOYEE_SESSION_DETAILS_BACKUP
AS SELECT * from KETTLE_DUMP.EMPLOYEE_SESSION_DETAILS;
drop table KETTLE_DUMP.EMPLOYEE_SESSION_DETAILS;

CREATE TABLE KETTLE_ARCHIVE_DUMP.EMPLOYEE_UNIT_MAPPING_BACKUP
AS SELECT * from KETTLE_DUMP.EMPLOYEE_UNIT_MAPPING;
drop table KETTLE_DUMP.EMPLOYEE_UNIT_MAPPING;

CREATE TABLE KETTLE_ARCHIVE_DUMP.MARKETING_PARTNER_BACKUP
AS SELECT * from KETTLE_DUMP.MARKETING_PARTNER;
drop table KETTLE_DUMP.MARKETING_PARTNER;

CREATE TABLE KETTLE_ARCHIVE_DUMP.OFFER_DETAIL_DATA_BACKUP
AS SELECT * from KETTLE_DUMP.OFFER_DETAIL_DATA;
drop table KETTLE_DUMP.OFFER_DETAIL_DATA;

CREATE TABLE KETTLE_ARCHIVE_DUMP.OFFER_METADATA_BACKUP
AS SELECT * from KETTLE_DUMP.OFFER_METADATA;
drop table KETTLE_DUMP.OFFER_METADATA;

CREATE TABLE KETTLE_ARCHIVE_DUMP.OFFER_PARTNERS_BACKUP
AS SELECT * from KETTLE_DUMP.OFFER_PARTNERS;
drop table KETTLE_DUMP.OFFER_PARTNERS;



CREATE TABLE KETTLE_ARCHIVE_DUMP.PRICE_DATA_BACKUP
AS SELECT * from KETTLE_DUMP.PRICE_DATA;
drop table KETTLE_DUMP.PRICE_DATA;

CREATE TABLE KETTLE_ARCHIVE_DUMP.PRODUCT_DETAIL_BACKUP
AS SELECT * from KETTLE_DUMP.PRODUCT_DETAIL;
drop table KETTLE_DUMP.PRODUCT_DETAIL;


CREATE TABLE KETTLE_ARCHIVE_DUMP.PRODUCT_RELATIONSHIP_BACKUP
AS SELECT * from KETTLE_DUMP.PRODUCT_RELATIONSHIP;
drop table KETTLE_DUMP.PRODUCT_RELATIONSHIP;

CREATE TABLE KETTLE_ARCHIVE_DUMP.REF_LOOKUP_BACKUP
AS SELECT * from KETTLE_DUMP.REF_LOOKUP;
drop table KETTLE_DUMP.REF_LOOKUP;

CREATE TABLE KETTLE_ARCHIVE_DUMP.REF_LOOKUP_TYPE_BACKUP
AS SELECT * from KETTLE_DUMP.REF_LOOKUP_TYPE;
drop table KETTLE_DUMP.REF_LOOKUP_TYPE;

CREATE TABLE KETTLE_ARCHIVE_DUMP.TAX_PROFILE_BACKUP
AS SELECT * from KETTLE_DUMP.TAX_PROFILE;
drop table KETTLE_DUMP.TAX_PROFILE;

CREATE TABLE KETTLE_ARCHIVE_DUMP.TEMP_LOYALTY_SCORE_BACKUP
AS SELECT * from KETTLE_DUMP.TEMP_LOYALTY_SCORE;
drop table KETTLE_DUMP.TEMP_LOYALTY_SCORE;



CREATE SCHEMA `KETTLE_ARCHIVE_DUMP`;
CREATE TABLE KETTLE_ARCHIVE_DUMP.NOTIFICATION_LOG_DETAIL_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_MASTER_DUMP.NOTIFICATION_LOG_DETAIL
WHERE NOTIFICATION_TIME <= '2017-07-31 00:00:00';
delete from KETTLE_MASTER_DUMP.NOTIFICATION_LOG_DETAIL
WHERE NOTIFICATION_TIME <= '2017-07-31 00:00:00';

CREATE TABLE KETTLE_ARCHIVE_DUMP.EMPLOYEE_SESSION_DETAILS_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_MASTER_DUMP.EMPLOYEE_SESSION_DETAILS
WHERE LOGIN_TIME <= '2017-07-31 00:00:00';
delete from KETTLE_MASTER_DUMP.EMPLOYEE_SESSION_DETAILS
WHERE LOGIN_TIME <= '2017-07-31 00:00:00';

CREATE TABLE KETTLE_ARCHIVE_DUMP.ASSEMBLY_LOG_DATA_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_DUMP.ASSEMBLY_LOG_DATA
WHERE BILLING_SERVER_TIME <= '2017-07-31 00:00:00';
delete from KETTLE_DUMP.ASSEMBLY_LOG_DATA
WHERE BILLING_SERVER_TIME <= '2017-07-31 00:00:00';

CREATE TABLE KETTLE_ARCHIVE_DUMP.CUSTOMER_NOTIFICATION_DETAIL_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_DUMP.CUSTOMER_NOTIFICATION_DETAIL
WHERE NOTIFICATION_TIME <= '2017-07-31 00:00:00';
delete from KETTLE_DUMP.CUSTOMER_NOTIFICATION_DETAIL
WHERE NOTIFICATION_TIME <= '2017-07-31 00:00:00';

CREATE TABLE KETTLE_ARCHIVE_DUMP.DELIVERY_STATUS_EVENT_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_DUMP.DELIVERY_STATUS_EVENT
WHERE STATUS_START_TMSTMP <= '2017-07-31 00:00:00';
delete from KETTLE_DUMP.DELIVERY_STATUS_EVENT
WHERE STATUS_START_TMSTMP <= '2017-07-31 00:00:00';

CREATE TABLE KETTLE_ARCHIVE_DUMP.EMPLOYEE_MEAL_DATA_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_DUMP.EMPLOYEE_MEAL_DATA
WHERE BUSINESS_DATE <= '2017-07-31 00:00:00';
delete from KETTLE_DUMP.EMPLOYEE_MEAL_DATA
WHERE BUSINESS_DATE <= '2017-07-31 00:00:00';

CREATE TABLE KETTLE_ARCHIVE_DUMP.INVENTORY_LOG_DATA_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_DUMP.INVENTORY_LOG_DATA
WHERE BUSINESS_DATE <= '2017-07-31 00:00:00';
delete from KETTLE_DUMP.INVENTORY_LOG_DATA
WHERE BUSINESS_DATE <= '2017-07-31 00:00:00';

CREATE TABLE KETTLE_ARCHIVE_DUMP.ORDER_EMAIL_NOTIFICATION_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_DUMP.ORDER_EMAIL_NOTIFICATION
WHERE REQUEST_TIME <= '2017-07-31 00:00:00';
delete from KETTLE_DUMP.ORDER_EMAIL_NOTIFICATION
WHERE REQUEST_TIME <= '2017-07-31 00:00:00';

CREATE TABLE KETTLE_ARCHIVE_DUMP.ORDER_ENQUIRY_ITEM_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_DUMP.ORDER_ENQUIRY_ITEM
WHERE ENQUIRY_TIME <= '2017-07-31 00:00:00';
delete from KETTLE_DUMP.ORDER_ENQUIRY_ITEM
WHERE ENQUIRY_TIME <= '2017-07-31 00:00:00';

CREATE TABLE KETTLE_ARCHIVE_DUMP.ORDER_METADATA_DETAIL_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_DUMP.ORDER_METADATA_DETAIL
WHERE ORDER_ID <= (select max(ORDER_ID) FROM KETTLE_DUMP.ORDER_DETAIL WHERE BUSINESS_DATE <= '2017-07-31 00:00:00');
delete from KETTLE_DUMP.ORDER_METADATA_DETAIL
WHERE ORDER_ID <= (select max(ORDER_ID) FROM KETTLE_DUMP.ORDER_DETAIL WHERE BUSINESS_DATE <= '2017-07-31 00:00:00');

CREATE TABLE KETTLE_ARCHIVE_DUMP.ORDER_RE_PRINT_DETAIL_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_DUMP.ORDER_RE_PRINT_DETAIL
WHERE ORDER_ID <= (select max(ORDER_ID) FROM KETTLE_DUMP.ORDER_DETAIL WHERE BUSINESS_DATE <= '2017-07-31 00:00:00');
delete from KETTLE_DUMP.ORDER_RE_PRINT_DETAIL
WHERE ORDER_ID <= (select max(ORDER_ID) FROM KETTLE_DUMP.ORDER_DETAIL WHERE BUSINESS_DATE <= '2017-07-31 00:00:00');

CREATE TABLE KETTLE_ARCHIVE_DUMP.ORDER_STATUS_EVENT_ARCHIVE_2017_07_31
AS SELECT * from KETTLE_DUMP.ORDER_STATUS_EVENT
WHERE ORDER_ID <= (select max(ORDER_ID) FROM KETTLE_DUMP.ORDER_DETAIL WHERE BUSINESS_DATE <= '2017-07-31 00:00:00');

select max(ORDER_ID) FROM KETTLE_DUMP.ORDER_DETAIL WHERE BUSINESS_DATE <= '2017-07-31 00:00:00';
## Run the deletes from this table in ultiples of lakhs upto the max order id got from the above SQL.
delete from KETTLE_DUMP.ORDER_STATUS_EVENT
WHERE ORDER_ID <= 100000;
delete from KETTLE_DUMP.ORDER_STATUS_EVENT
WHERE ORDER_ID <= 200000;
delete from KETTLE_DUMP.ORDER_STATUS_EVENT
WHERE ORDER_ID <= 300000;
delete from KETTLE_DUMP.ORDER_STATUS_EVENT
WHERE ORDER_ID <= 400000;
delete from KETTLE_DUMP.ORDER_STATUS_EVENT
WHERE ORDER_ID <= 500000;
delete from KETTLE_DUMP.ORDER_STATUS_EVENT
WHERE ORDER_ID <= 600000;
delete from KETTLE_DUMP.ORDER_STATUS_EVENT
WHERE ORDER_ID <= 700000;
delete from KETTLE_DUMP.ORDER_STATUS_EVENT
WHERE ORDER_ID <= 800000;
delete from KETTLE_DUMP.ORDER_STATUS_EVENT
WHERE ORDER_ID <= 900000;


set foreign_key_checks = 1;
