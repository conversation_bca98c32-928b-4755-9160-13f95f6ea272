UPDATE `KETTLE_DEV`.`DELIVERY_PARTNER` SET `AUTOMATED`='Y' WHERE `PARTNER_ID`='7';
INSERT INTO `KETTLE_DEV`.`DELIVERY_PARTNER`
(`PARTNER_ID`, `PARTNER_CODE`, `PARTNER_DISPLAY_NAME`, `PARTNER_STATUS`, `PARTNER_TYPE`, `AUTOMATED`, `PER_DELIVERY_COST`)
VALUES ('9', 'GRAB', 'Grab', 'ACTIVE', 'EXTERNAL', 'Y', '50.00');


#Grab related queries
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('9', 'EXECUTOR', 'com.stpl.tech.kettle.delivery.strategy.GrabExecutor', 'DELIVERY');
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('9', 'CREATE_ENDPOINT', 'pushorder/', 'DELIVERY');
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('9', 'CANCEL_ENDPOINT', 'cancelorder/', 'DELIVERY');
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('9', 'REGISTER_MERCHANT', 'createmerchant/', 'DELIVERY');
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('9', 'CALLBACK_TOKEN', 'WkgsmrYjFhIf0UedN6BgDA==', 'DELIVERY');
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('9', 'AUTHORIZATION_TOKEN', 'a4897dfb4ecd666e39ef41474b8421c944937142c3ffa7139ac31aa540ac7de2', 'DELIVERY');
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('9', 'AUTHORIZATION_TOKEN_SECRET', 'b179e43f0fb3b6204c7a299b193dd054f73f1ace4605061f4e7c4adfeb8c1023', 'DELIVERY');



#Delivery Track related queries
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', 'REQUEST_OBJECT_ADAPTER', 'com.stpl.tech.kettle.delivery.adapter.DTRequestAdapter', 'DELIVERY');
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', 'EXECUTOR', 'com.stpl.tech.kettle.delivery.strategy.DTExecutor', 'DELIVERY');
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', 'CREATE_ENDPOINT', 'Orders/OrderCreation.php', 'DELIVERY');
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', 'CANCEL_ENDPOINT', 'Orders/OrderCancelled.php', 'DELIVERY');
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', 'CALLBACK_TOKEN', 'QNNKHrCHT4kaJAakePFA6mo0qnQyf2Aa7ZGKBWg9eCs=', 'DELIVERY');

#For testing purposes only
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`,`MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12004','57ce389ae2243', 'DELIVERY');

#For production on boarding purposes only
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12015', 'IRbGHQJpSl', 'DELIVERY');
INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '10010', '576393bfc3414', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
	VALUES ('7', '10013', 'W644Wzie8d', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12002', '57dba7ef0d9fd', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12003', 'JujwtvQSNa', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12028', 'fhMGMs33p9', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '26005', '57de30368c4cf', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '10006', '57de2c0ad9223', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '10002', '57de2d15ecfbe', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12012', '57de373ee7252', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12020', '57de2d602caf4', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '10009', '581c28856ff1c', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12022', '581c2c084b22c', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12001', '5816e69d83781', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12014', '5811981c7807c', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12031', '581c2bbf0ecbe', 'DELIVERY');
INSERT INTO `KETTLE`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`)
    VALUES ('7', '12023', '581c29e9905b3', 'DELIVERY');







#Changes for Delivery Partner Credit and Cash Eligibility
ALTER TABLE KETTLE.DELIVERY_PARTNER ADD COLUMN CASH_ELIGIBILITY VARCHAR(1) NULL DEFAULT "Y";
UPDATE KETTLE.DELIVERY_PARTNER SET CASH_ELIGIBILITY='N' WHERE PARTNER_ID=7;
CREATE INDEX INDEX_GROUP_DELIVERY_TASK_ID ON DELIVERY_DETAIL (DELIVERY_TASK_ID) USING BTREE;
