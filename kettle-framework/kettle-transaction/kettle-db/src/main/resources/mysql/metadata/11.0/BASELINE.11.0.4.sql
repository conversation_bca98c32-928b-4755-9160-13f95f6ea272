ALTER TABLE KETTLE_JOBS_DEV.AGGR<PERSON>ATED_RESULT_DEFINITION
    ADD COLUMN BUSINESS_DATE VARCHAR(40) AFTER AGGREGATED_RESULT_DEFINITION_ID;

ALTER TABLE KETTLE_JOBS_DEV.AGGREGATED_RESULT_DEFINITION
    ADD COLUMN DEBIT_CREDIT VARCHAR(10) AFTER TOTAL_SALES;

ALTER TABLE KETTLE_JOBS_DEV.AGGREGATED_RESULT_DEFINITION
    ADD COLUMN KEY_TYPE VARCHAR(40) AFTER ACCOUNT_CODE;

ALTER TABLE KETTLE_JOBS_DEV.AGGREGATED_RESULT_DEFINITION
    ADD COLUMN KEY_ID VARCHAR(10) AFTER KEY_TYPE;