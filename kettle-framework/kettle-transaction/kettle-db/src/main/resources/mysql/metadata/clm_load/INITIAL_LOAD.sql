

DROP TABLE CLM_ANALYTICS.CUSTOMER_DATA_NEW;
CREATE TABLE CLM_ANALYTICS.CUSTOMER_DATA_NEW (
    CUSTOMER_ID INTEGER NOT NULL,
    TOTAL_UNITS_VISITED INTEGER NULL,
    FIRST_ORDER_ID INTEGER NULL,
    LAST_ORDER_ID INTEGER NULL,
    FIRST_ORDER_DATE DATE NULL,
    LAST_ORDER_DATE DATE NULL,
    TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DINE_IN_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DELIVERY_TICKET_COUNT INTEGER NULL,
    CANCELLED_TICKET_COUNT INTEGER NULL,
    TICKET_WITH_OFFER INTEGER NULL,
    TICKET_WITH_REDEMPTION INTEGER NULL,
    DINE_IN_TICKET INTEGER NULL,
    DELIVERY_TICKET INTEGER NULL,
    TAKE_AWAY_TICKET INTEGER NULL,
    ZOMATO_TICKET INTEGER NULL,
    SWIGGY_TICKET INTEGER NULL,
    FOOD_PANDA_TICKET INTEGER NULL,
    UBER_EATS_TICKET INTEGER NULL,
    OLD_APP_TICKET INTEGER NULL,
    WEB_APP_TICKET INTEGER NULL,
    CALL_CENTER_TICKET INTEGER NULL,
    OTHER_PARTNER_TICKET INTEGER NULL,
    TICKET_ON_MONDAY INTEGER NULL,
    TICKET_ON_TUESDAY INTEGER NULL,
    TICKET_ON_WEDNESDAY INTEGER NULL,
    TICKET_ON_THURSDAY INTEGER NULL,
    TICKET_ON_FRIDAY INTEGER NULL,
    TICKET_ON_SATURDAY INTEGER NULL,
    TICKET_ON_SUNDAY INTEGER NULL,
    TICKET_ON_WEEKDAY INTEGER NULL,
    TICKET_ON_WEEKEND INTEGER NULL,
    TICKET_IN_BREAKFAST INTEGER NULL,
    TICKET_IN_LUNCH INTEGER NULL,
    TICKET_IN_EVENING INTEGER NULL,
    TICKET_IN_DINNER INTEGER NULL,
    TICKET_IN_POST_DINNER INTEGER NULL,
    TICKET_IN_NIGHT INTEGER NULL,
    ONE_NPS_TICKET INTEGER NULL,
    TWO_NPS_TICKET INTEGER NULL,
    THREE_NPS_TICKET INTEGER NULL,
    FOUR_NPS_TICKET INTEGER NULL,
    FIVE_NPS_TICKET INTEGER NULL,
    SIX_NPS_TICKET INTEGER NULL,
    SEVEN_NPS_TICKET INTEGER NULL,
    EIGHT_NPS_TICKET INTEGER NULL,
    NINE_NPS_TICKET INTEGER NULL,
    TEN_NPS_TICKET INTEGER NULL,
    LAST_NPS_SCORE INTEGER NULL,
    NEGATIVE_NPS_TICKET INTEGER NULL,
    POSITIVE_NPS_TICKET INTEGER NULL,
    NEUTRAL_NPS_TICKET INTEGER NULL,
    TOTAL_SPEND DECIMAL(10 , 2 ) NULL,
    TOTAL_DISCOUNT DECIMAL(10 , 2 ) NULL,
    MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_SPEND DECIMAL(10 , 2 ) NULL,
    DELIVERY_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DELIVERY_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_SPEND DECIMAL(10 , 2 ) NULL,
    DINE_IN_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DINE_IN_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    CASH_TICKET INTEGER NULL,
    CARD_TICKET INTEGER NULL,
    AMEX_TICKET INTEGER NULL,
    PAYTM_TICKET INTEGER NULL,
    GIFT_CARD_TICKET INTEGER NULL,
    ONLY_GIFT_CARD_TICKET INTEGER NULL,
    MOBIKWIK_TICKET INTEGER NULL,
    ONLINE_PAYMENT_TICKET INTEGER NULL,
    OTHER_PAYMENT_TICKET INTEGER NULL,
    CASH_SPEND DECIMAL(10 , 2 ) NULL,
    CARD_SPEND DECIMAL(10 , 2 ) NULL,
    AMEX_SPEND DECIMAL(10 , 2 ) NULL,
    PAYTM_SPEND DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_SPEND DECIMAL(10 , 2 ) NULL,
    MOBIKWIK_SPEND DECIMAL(10 , 2 ) NULL,
    ONLINE_SPEND DECIMAL(10 , 2 ) NULL,
    OTHER_PAYMENT_SPEND DECIMAL(10 , 2 ) NULL,
    ONE_FEEDBACK_TICKET INTEGER NULL,
    TWO_FEEDBACK_TICKET INTEGER NULL,
    THREE_FEEDBACK_TICKET INTEGER NULL,
    FOUR_FEEDBACK_TICKET INTEGER NULL,
    FIVE_FEEDBACK_TICKET INTEGER NULL,
    TICKET_WITH_FOOD INTEGER NULL,
    TICKET_WITH_VEG INTEGER NULL,
    TICKET_WITH_NON_VEG INTEGER NULL,
    TICKET_WITH_HOT INTEGER NULL,
    TICKET_WITH_COLD INTEGER NULL,
    TICKET_WITH_BAKERY INTEGER NULL,
    TICKET_WITH_COMBO INTEGER NULL,
    TICKET_WITH_MERCHANDISE INTEGER NULL,
    TICKET_WITH_OTHER INTEGER NULL,
    TICKET_WITH_GIFT_CARD INTEGER NULL,
    PEOPLE_PER_TICKET INTEGER NULL,
    MINIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    MAXIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    SPLIT_PAYMENT_TICKET INTEGER NULL,
    LAST_PAYMENT_MODE INTEGER NULL,
    FIRST_UNIT_ID INTEGER NULL,
    FIRST_UNIT_NAME VARCHAR(100) NULL,
    LAST_UNIT_ID INTEGER NULL,
    LAST_UNIT_NAME VARCHAR(100) NULL,
    LAST_FEEDBACK_SCORE INTEGER NULL,
    DAY_GAP_SINCE_LAST_ORDER INTEGER NULL,
    TOTAL_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_APC DECIMAL(10 , 2 ) NULL
);

CREATE INDEX CUSTOMER_DATA_CUSTOMER_ID ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(CUSTOMER_ID) USING BTREE;


INSERT INTO CLM_ANALYTICS.CUSTOMER_DATA_NEW
(CUSTOMER_ID,
 FIRST_ORDER_ID,
 LAST_ORDER_ID,
 TOTAL_UNITS_VISITED,
 TICKET_COUNT,
 CANCELLED_TICKET_COUNT,
 FIRST_ORDER_DATE,
 LAST_ORDER_DATE,
 TICKET_WITH_OFFER,
 ZERO_AMOUNT_TICKET_COUNT,
 ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
 ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
 TICKET_WITH_REDEMPTION,
 DINE_IN_TICKET,
 DELIVERY_TICKET,
 TAKE_AWAY_TICKET,
 CALL_CENTER_TICKET,
 ZOMATO_TICKET,
 SWIGGY_TICKET,
 FOOD_PANDA_TICKET,
 UBER_EATS_TICKET,
 OLD_APP_TICKET,
 WEB_APP_TICKET,
 OTHER_PARTNER_TICKET,
 TICKET_ON_SUNDAY,
 TICKET_ON_MONDAY,
 TICKET_ON_TUESDAY,
 TICKET_ON_WEDNESDAY,
 TICKET_ON_THURSDAY,
 TICKET_ON_FRIDAY,
 TICKET_ON_SATURDAY,
 TICKET_ON_WEEKDAY,
 TICKET_ON_WEEKEND,
 TICKET_IN_BREAKFAST,
 TICKET_IN_LUNCH,
 TICKET_IN_EVENING,
 TICKET_IN_DINNER,
 TICKET_IN_POST_DINNER,
 TICKET_IN_NIGHT,
 TOTAL_SPEND,
 MINIMUM_APC,
 MAXIMUM_APC,
 TOTAL_DISCOUNT,
 DELIVERY_SPEND,
 DELIVERY_MINIMUM_APC,
 DELIVERY_MAXIMUM_APC,
 DELIVERY_DISCOUNT,
 DINE_IN_SPEND,
 DINE_IN_MINIMUM_APC,
 DINE_IN_MAXIMUM_APC,
 DINE_IN_DISCOUNT
)
  SELECT
    od.CUSTOMER_ID CUSTOMER_ID,
    MIN(od.ORDER_ID) FIRST_ORDER_ID,
    MAX(od.ORDER_ID) LAST_ORDER_ID,
    COUNT(DISTINCT od.UNIT_ID) TOTAL_UNITS_VISITED,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 1
      ELSE 0
    END) TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 0
      ELSE 1
    END) CANCELLED_TICKET_COUNT,
    MIN(od.BUSINESS_DATE) FIRST_ORDER_DATE,
    MAX(od.BUSINESS_DATE) LAST_ORDER_DATE,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TOTAL_AMOUNT <> od.TAXABLE_AMOUNT THEN 1
      ELSE 0
    END) TICKET_WITH_OFFER,
     SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.POINTS_REDEEMED < 0 THEN 1
      ELSE 0
    END) TICKET_WITH_REDEMPTION,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' THEN 1
      ELSE 0
    END) DINE_IN_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' THEN 1
      ELSE 0
    END) DELIVERY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'TAKE_AWAY' THEN 1
      ELSE 0
    END) TAKE_AWAY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 2 THEN 1
      ELSE 0
    END) CALL_CENTER_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 3 THEN 1
      ELSE 0
    END) ZOMATO_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 6 THEN 1
      ELSE 0
    END) SWIGGY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 5 THEN 1
      ELSE 0
    END) FOOD_PANDA_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 15 THEN 1
      ELSE 0
    END) UBER_EATS_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (10) THEN 1
      ELSE 0
    END) OLD_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (14) THEN 1
      ELSE 0
    END) WEB_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID NOT IN (2, 3, 5, 6, 10, 14, 15) THEN 1
      ELSE 0
    END) OTHER_PARTNER_TICKET,

    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1) THEN 1
      ELSE 0
    END) TICKET_ON_SUNDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (2) THEN 1
      ELSE 0
    END) TICKET_ON_MONDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (3) THEN 1
      ELSE 0
    END) TICKET_ON_TUESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (4) THEN 1
      ELSE 0
    END) TICKET_ON_WEDNESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (5) THEN 1
      ELSE 0
    END) TICKET_ON_THURSDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (6) THEN 1
      ELSE 0
    END) TICKET_ON_FRIDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (7) THEN 1
      ELSE 0
    END) TICKET_ON_SATURDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) NOT IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 6 AND
        HOUR(od.BILLING_SERVER_TIME) < 12 THEN 1
      ELSE 0
    END) TICKET_IN_BREAKFAST,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 12 AND
        HOUR(od.BILLING_SERVER_TIME) < 15 THEN 1
      ELSE 0
    END) TICKET_IN_LUNCH,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 15 AND
        HOUR(od.BILLING_SERVER_TIME) < 20 THEN 1
      ELSE 0
    END) TICKET_IN_EVENING,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 20 AND
        HOUR(od.BILLING_SERVER_TIME) < 22 THEN 1
      ELSE 0
    END) TICKET_IN_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 22 AND
        HOUR(od.BILLING_SERVER_TIME) <= 23 THEN 1
      ELSE 0
    END) TICKET_IN_POST_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 0 AND
        HOUR(od.BILLING_SERVER_TIME) < 6 THEN 1
      ELSE 0
    END) TICKET_IN_NIGHT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_DISCOUNT

  FROM KETTLE_DUMP.ORDER_DETAIL od
  WHERE od.CUSTOMER_ID > 5
  GROUP BY od.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.CUSTOMER_DATA_NEW c,
    (SELECT 
        CUSTOMER_ID,
            SUM(CASE
                WHEN NPS_SCORE = 1 THEN 1
                ELSE 0
            END) ONE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 2 THEN 1
                ELSE 0
            END) TWO_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 3 THEN 1
                ELSE 0
            END) THREE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 4 THEN 1
                ELSE 0
            END) FOUR_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 5 THEN 1
                ELSE 0
            END) FIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 6 THEN 1
                ELSE 0
            END) SIX_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 7 THEN 1
                ELSE 0
            END) SEVEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 8 THEN 1
                ELSE 0
            END) EIGHT_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 9 THEN 1
                ELSE 0
            END) NINE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 10 THEN 1
                ELSE 0
            END) TEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE < 7 THEN 1
                ELSE 0
            END) NEGATIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE >= 7 AND NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) NEUTRAL_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE > 8 THEN 1
                ELSE 0
            END) POSITIVE_NPS_TICKET
    FROM
        KETTLE_DUMP.ORDER_NPS_DETAIL
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_NPS_TICKET = d.ONE_NPS_TICKET,
    c.TWO_NPS_TICKET = d.TWO_NPS_TICKET,
    c.THREE_NPS_TICKET = d.THREE_NPS_TICKET,
    c.FOUR_NPS_TICKET = d.FOUR_NPS_TICKET,
    c.FIVE_NPS_TICKET = d.FIVE_NPS_TICKET,
    c.SIX_NPS_TICKET = d.SIX_NPS_TICKET,
    c.SEVEN_NPS_TICKET = d.SEVEN_NPS_TICKET,
    c.EIGHT_NPS_TICKET = d.EIGHT_NPS_TICKET,
    c.NINE_NPS_TICKET = d.NINE_NPS_TICKET,
    c.TEN_NPS_TICKET = d.TEN_NPS_TICKET,
    c.POSITIVE_NPS_TICKET = d.POSITIVE_NPS_TICKET,
    c.NEGATIVE_NPS_TICKET = d.NEGATIVE_NPS_TICKET,
    c.NEUTRAL_NPS_TICKET = d.NEUTRAL_NPS_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID

;

UPDATE CLM_ANALYTICS.CUSTOMER_DATA_NEW c,
    (SELECT 
        od.CUSTOMER_ID,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN os.AMOUNT_PAID
                ELSE 0
            END) CASH_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN 1
                ELSE 0
            END) CASH_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN os.AMOUNT_PAID
                ELSE 0
            END) CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN 1
                ELSE 0
            END) CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN os.AMOUNT_PAID
                ELSE 0
            END) AMEX_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN 1
                ELSE 0
            END) AMEX_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN os.AMOUNT_PAID
                ELSE 0
            END) GIFT_CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN 1
                ELSE 0
            END) GIFT_CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN os.AMOUNT_PAID
                ELSE 0
            END) PAYTM_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN 1
                ELSE 0
            END) PAYTM_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN os.AMOUNT_PAID
                ELSE 0
            END) ONLINE_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN 1
                ELSE 0
            END) ONLINE_PAYMENT_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) MOBIKWIK_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN 1
                ELSE 0
            END) MOBIKWIK_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID NOT IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) OTHER_PAYMENT_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN 1
                ELSE 0
            END) OTHER_PAYMENT_TICKET
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = os.ORDER_ID
            AND od.CUSTOMER_ID > 5
    GROUP BY od.CUSTOMER_ID) d 
SET 
    c.CASH_SPEND = d.CASH_SPEND,
    c.CASH_TICKET = d.CASH_TICKET,
    c.CARD_SPEND = d.CARD_SPEND,
    c.CARD_TICKET = d.CARD_TICKET,
    c.AMEX_SPEND = d.AMEX_SPEND,
    c.AMEX_TICKET = d.AMEX_TICKET,
    c.GIFT_CARD_SPEND = d.GIFT_CARD_SPEND,
    c.GIFT_CARD_TICKET = d.GIFT_CARD_TICKET,
    c.PAYTM_SPEND = d.PAYTM_SPEND,
    c.PAYTM_TICKET = d.PAYTM_TICKET,
    c.ONLINE_SPEND = d.ONLINE_SPEND,
    c.ONLINE_PAYMENT_TICKET = d.ONLINE_PAYMENT_TICKET,
    c.MOBIKWIK_SPEND = d.MOBIKWIK_SPEND,
    c.MOBIKWIK_TICKET = d.MOBIKWIK_TICKET,
    c.OTHER_PAYMENT_SPEND = d.OTHER_PAYMENT_SPEND,
    c.OTHER_PAYMENT_TICKET = d.OTHER_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.CUSTOMER_DATA_NEW c,
    (SELECT 
        CUSTOMER_ID,
            SUM(CASE
                WHEN FEEDBACK_RATING = 1 THEN 1
                ELSE 0
            END) ONE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 2 THEN 1
                ELSE 0
            END) TWO_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 3 THEN 1
                ELSE 0
            END) THREE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 4 THEN 1
                ELSE 0
            END) FOUR_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 5 THEN 1
                ELSE 0
            END) FIVE_FEEDBACK_TICKET
    FROM
        KETTLE_DUMP.ORDER_FEEDBACK_DETAIL
    WHERE
        FEEDBACK_STATUS = 'COMPLETED'
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_FEEDBACK_TICKET = d.ONE_FEEDBACK_TICKET,
    c.TWO_FEEDBACK_TICKET = d.TWO_FEEDBACK_TICKET,
    c.THREE_FEEDBACK_TICKET = d.THREE_FEEDBACK_TICKET,
    c.FOUR_FEEDBACK_TICKET = d.FOUR_FEEDBACK_TICKET,
    c.FIVE_FEEDBACK_TICKET = d.FIVE_FEEDBACK_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID

;
UPDATE CLM_ANALYTICS.CUSTOMER_DATA_NEW_NEW c,
    (SELECT 
        a.CUSTOMER_ID,
        SUM(CASE
                WHEN a.HOT_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_HOT,
            SUM(CASE
                WHEN a.VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_VEG,
            SUM(CASE
                WHEN a.NON_VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_NON_VEG,
            SUM(CASE
                WHEN a.FOOD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_FOOD,
            SUM(CASE
                WHEN a.COLD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COLD,
            SUM(CASE
                WHEN a.BAKERY_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_BAKERY,
            SUM(CASE
                WHEN a.COMBO_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COMBO,
            SUM(CASE
                WHEN a.MERCHANDISE_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_MERCHANDISE,
            SUM(CASE
                WHEN a.OTHERS_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_OTHER,
            SUM(CASE
                WHEN a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_GIFT_CARD,
            SUM(CASE
                WHEN a.HOT_QUANTITY = 0 AND 
                a.HOT_QUANTITY = 0 AND 
                a.FOOD_QUANTITY = 0 AND 
                a.COLD_QUANTITY = 0 AND 
                a.BAKERY_QUANTITY = 0 AND 
                a.COMBO_QUANTITY = 0 AND 
                a.MERCHANDISE_QUANTITY = 0 AND 
                a.OTHERS_QUANTITY = 0 AND a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_ONLY_GIFT_CARD
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 8 THEN oi.QUANTITY
                ELSE 0
            END) COMBO_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 9 THEN oi.QUANTITY
                ELSE 0
            END) MERCHANDISE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 10 THEN oi.QUANTITY
                ELSE 0
            END) BAKERY_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 12
                        AND pd.TAX_CODE <> 'GIFT_CARD'
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHERS_QUANTITY,
            SUM(CASE
                WHEN pd.TAX_CODE = 'GIFT_CARD' THEN oi.QUANTITY
                ELSE 0
            END) GIFT_CARD_QUANTITY,
			SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 7 AND (pd.ATTRIBUTE IS NULL
                        OR pd.ATTRIBUTE = 'VEG')
                THEN
                    oi.QUANTITY
                ELSE 0
            END) VEG_QUANTITY,
            SUM(CASE
                WHEN  pd.PRODUCT_TYPE = 7 AND pd.ATTRIBUTE = 'NON_VEG' THEN oi.QUANTITY
                ELSE 0
            END) NON_VEG_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID NOT IN(1,2,3,4,5,67456,142315)
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a
    GROUP BY a.CUSTOMER_ID) a 
SET 
    c.TICKET_WITH_HOT = a.TICKET_WITH_HOT,
    c.TICKET_WITH_VEG = a.TICKET_WITH_VEG,
    c.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG,
    c.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD,
    c.TICKET_WITH_COLD = a.TICKET_WITH_COLD,
    c.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY,
    c.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO,
    c.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE,
    c.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER,
    c.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD,
    c.ONLY_GIFT_CARD_TICKET =a.TICKET_WITH_ONLY_GIFT_CARD
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID;



UPDATE CLM_ANALYTICS.CUSTOMER_DATA_NEW c,
    (SELECT 
        m.CUSTOMER_ID,
            TRUNCATE(AVG(PEOPLE_PER_TICKET), 1) PEOPLE_PER_TICKET,
            MIN(PEOPLE_PER_TICKET) MINIMUM_PEOPLE_PER_ORDER,
            MAX(PEOPLE_PER_TICKET) MAXIMUM_PEOPLE_PER_ORDER
    FROM
        (SELECT 
        a.CUSTOMER_ID,
            a.ORDER_ID,
            CASE
                WHEN (a.HOT_QUANTITY + a.COLD_QUANTITY) > a.FOOD_QUANTITY THEN a.HOT_QUANTITY + a.COLD_QUANTITY
                ELSE a.FOOD_QUANTITY
            END PEOPLE_PER_TICKET
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID NOT IN(1,2,3,4,5,67456,142315)
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a) m
    GROUP BY m.CUSTOMER_ID) a 
SET 
    c.PEOPLE_PER_TICKET = a.PEOPLE_PER_TICKET,
    c.MINIMUM_PEOPLE_PER_ORDER = a.MINIMUM_PEOPLE_PER_ORDER,
    c.MAXIMUM_PEOPLE_PER_ORDER = a.MAXIMUM_PEOPLE_PER_ORDER
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID
;
UPDATE CLM_ANALYTICS.CUSTOMER_DATA_NEW c,
    (SELECT 
        CUSTOMER_ID, COUNT(*) SPLIT_PAYMENT_TICKET
    FROM
        (SELECT 
        od.ORDER_ID, od.CUSTOMER_ID, COUNT(*)
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_ID = os.ORDER_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.CUSTOMER_ID NOT IN(1,2,3,4,5,67456,142315)
    GROUP BY od.ORDER_ID , od.CUSTOMER_ID
    HAVING COUNT(*) > 1) a
    GROUP BY CUSTOMER_ID) a 
SET 
    c.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = a.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.CUSTOMER_DATA_NEW c,
    KETTLE_DUMP.ORDER_DETAIL o1,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u1,
    KETTLE_DUMP.ORDER_DETAIL o2,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u2 
SET 
    c.FIRST_UNIT_ID = u1.UNIT_ID,
    c.FIRST_UNIT_NAME = u1.UNIT_NAME,
    c.LAST_UNIT_ID = u2.UNIT_ID,
    c.LAST_UNIT_NAME = u2.UNIT_NAME
WHERE
    c.FIRST_ORDER_ID = o1.ORDER_ID
        AND c.LAST_ORDER_ID = o2.ORDER_ID
        AND o1.UNIT_ID = u1.UNIT_ID
        AND o2.UNIT_ID = u2.UNIT_ID;
        
        
UPDATE CLM_ANALYTICS.CUSTOMER_DATA_NEW c,
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o1 
SET 
    c.LAST_FEEDBACK_SCORE = o1.FEEDBACK_RATING
WHERE
    c.LAST_ORDER_ID = o1.ORDER_ID
        AND o1.FEEDBACK_STATUS = 'COMPLETED';

UPDATE CLM_ANALYTICS.CUSTOMER_DATA_NEW 
SET 
    TOTAL_APC = CASE
        WHEN
            COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(TOTAL_SPEND / (COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DINE_IN_APC = CASE
        WHEN
            COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DINE_IN_SPEND / (COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DELIVERY_APC = CASE
        WHEN
            COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DELIVERY_SPEND / (COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END;
update CLM_ANALYTICS.CUSTOMER_DATA_NEW c, (select
    c.CUSTOMER_ID, MAX(c.LAST_ORDER_DATE) LAST_ORDER_DATE, MAX(o.BUSINESS_DATE) SECOND_LAST_BUSINESS_DATE, DATEDIFF(MAX(c.LAST_ORDER_DATE),MAX(o.BUSINESS_DATE)) DAY_GAP_SINCE_LAST_ORDER
FROM
    CLM_ANALYTICS.CUSTOMER_DATA_NEW c1,
    CLM_ANALYTICS.CUSTOMER_DATA_NEW c,
    KETTLE_DUMP.ORDER_DETAIL o
WHERE
    c.CUSTOMER_ID = o.CUSTOMER_ID
        AND o.ORDER_ID < c.LAST_ORDER_ID
        AND c1.CUSTOMER_ID = c.CUSTOMER_ID
GROUP BY c.CUSTOMER_ID
) a
SET c.DAY_GAP_SINCE_LAST_ORDER =  a.DAY_GAP_SINCE_LAST_ORDER
where c.CUSTOMER_ID = a.CUSTOMER_ID ;

update CLM_ANALYTICS.CUSTOMER_DATA_NEW_NEW
SET
ONE_NPS_TICKET =  case when ONE_NPS_TICKET IS NULL THEN 0 ELSE ONE_NPS_TICKET END,
TWO_NPS_TICKET =  case when TWO_NPS_TICKET IS NULL THEN 0 ELSE TWO_NPS_TICKET END,
THREE_NPS_TICKET =  case when THREE_NPS_TICKET IS NULL THEN 0 ELSE THREE_NPS_TICKET END,
FOUR_NPS_TICKET =  case when FOUR_NPS_TICKET IS NULL THEN 0 ELSE FOUR_NPS_TICKET END,
FIVE_NPS_TICKET =  case when FIVE_NPS_TICKET IS NULL THEN 0 ELSE FIVE_NPS_TICKET END,
SIX_NPS_TICKET =  case when SIX_NPS_TICKET IS NULL THEN 0 ELSE SIX_NPS_TICKET END,
SEVEN_NPS_TICKET =  case when SEVEN_NPS_TICKET IS NULL THEN 0 ELSE SEVEN_NPS_TICKET END,
EIGHT_NPS_TICKET =  case when EIGHT_NPS_TICKET IS NULL THEN 0 ELSE EIGHT_NPS_TICKET END,
NINE_NPS_TICKET =  case when NINE_NPS_TICKET IS NULL THEN 0 ELSE NINE_NPS_TICKET END,
TEN_NPS_TICKET =  case when TEN_NPS_TICKET IS NULL THEN 0 ELSE TEN_NPS_TICKET END,
LAST_NPS_SCORE =  case when LAST_NPS_SCORE IS NULL THEN 0 ELSE LAST_NPS_SCORE END,
NEGATIVE_NPS_TICKET =  case when NEGATIVE_NPS_TICKET IS NULL THEN 0 ELSE NEGATIVE_NPS_TICKET END,
POSITIVE_NPS_TICKET =  case when POSITIVE_NPS_TICKET IS NULL THEN 0 ELSE POSITIVE_NPS_TICKET END,
NEUTRAL_NPS_TICKET =  case when NEUTRAL_NPS_TICKET IS NULL THEN 0 ELSE NEUTRAL_NPS_TICKET END,
SPLIT_PAYMENT_TICKET =  case when SPLIT_PAYMENT_TICKET IS NULL THEN 0 ELSE SPLIT_PAYMENT_TICKET END,
DINE_IN_MINIMUM_APC =  case when DINE_IN_MINIMUM_APC IS NULL THEN 0 ELSE DINE_IN_MINIMUM_APC END,
DINE_IN_MAXIMUM_APC =  case when DINE_IN_MAXIMUM_APC IS NULL THEN 0 ELSE DINE_IN_MAXIMUM_APC END,
CASH_TICKET =  case when CASH_TICKET IS NULL THEN 0 ELSE CASH_TICKET END,
CARD_TICKET =  case when CARD_TICKET IS NULL THEN 0 ELSE CARD_TICKET END,
AMEX_TICKET =  case when AMEX_TICKET IS NULL THEN 0 ELSE AMEX_TICKET END,
PAYTM_TICKET =  case when PAYTM_TICKET IS NULL THEN 0 ELSE PAYTM_TICKET END,
GIFT_CARD_TICKET =  case when GIFT_CARD_TICKET IS NULL THEN 0 ELSE GIFT_CARD_TICKET END,
ONLY_GIFT_CARD_TICKET =  case when ONLY_GIFT_CARD_TICKET IS NULL THEN 0 ELSE ONLY_GIFT_CARD_TICKET END,
MOBIKWIK_TICKET =  case when MOBIKWIK_TICKET IS NULL THEN 0 ELSE MOBIKWIK_TICKET END,
ONLINE_PAYMENT_TICKET =  case when ONLINE_PAYMENT_TICKET IS NULL THEN 0 ELSE ONLINE_PAYMENT_TICKET END,
OTHER_PAYMENT_TICKET =  case when OTHER_PAYMENT_TICKET IS NULL THEN 0 ELSE OTHER_PAYMENT_TICKET END,
CASH_SPEND =  case when CASH_SPEND IS NULL THEN 0 ELSE CASH_SPEND END,
CARD_SPEND =  case when CARD_SPEND IS NULL THEN 0 ELSE CARD_SPEND END,
AMEX_SPEND =  case when AMEX_SPEND IS NULL THEN 0 ELSE AMEX_SPEND END,
PAYTM_SPEND =  case when PAYTM_SPEND IS NULL THEN 0 ELSE PAYTM_SPEND END,
GIFT_CARD_SPEND =  case when GIFT_CARD_SPEND IS NULL THEN 0 ELSE GIFT_CARD_SPEND END,
MOBIKWIK_SPEND =  case when MOBIKWIK_SPEND IS NULL THEN 0 ELSE MOBIKWIK_SPEND END,
ONLINE_SPEND =  case when ONLINE_SPEND IS NULL THEN 0 ELSE ONLINE_SPEND END,
OTHER_PAYMENT_SPEND =  case when OTHER_PAYMENT_SPEND IS NULL THEN 0 ELSE OTHER_PAYMENT_SPEND END,
ONE_FEEDBACK_TICKET =  case when ONE_FEEDBACK_TICKET IS NULL THEN 0 ELSE ONE_FEEDBACK_TICKET END,
TWO_FEEDBACK_TICKET =  case when TWO_FEEDBACK_TICKET IS NULL THEN 0 ELSE TWO_FEEDBACK_TICKET END,
THREE_FEEDBACK_TICKET =  case when THREE_FEEDBACK_TICKET IS NULL THEN 0 ELSE THREE_FEEDBACK_TICKET END,
FOUR_FEEDBACK_TICKET =  case when FOUR_FEEDBACK_TICKET IS NULL THEN 0 ELSE FOUR_FEEDBACK_TICKET END,
FIVE_FEEDBACK_TICKET =  case when FIVE_FEEDBACK_TICKET IS NULL THEN 0 ELSE FIVE_FEEDBACK_TICKET END,
MINIMUM_APC =  case when MINIMUM_APC IS NULL THEN 0 ELSE MINIMUM_APC END,
MAXIMUM_APC =  case when MAXIMUM_APC IS NULL THEN 0 ELSE MAXIMUM_APC END,
DELIVERY_SPEND =  case when DELIVERY_SPEND IS NULL THEN 0 ELSE DELIVERY_SPEND END,
DELIVERY_DISCOUNT =  case when DELIVERY_DISCOUNT IS NULL THEN 0 ELSE DELIVERY_DISCOUNT END,
DELIVERY_MINIMUM_APC =  case when DELIVERY_MINIMUM_APC IS NULL THEN 0 ELSE DELIVERY_MINIMUM_APC END,
DELIVERY_MAXIMUM_APC =  case when DELIVERY_MAXIMUM_APC IS NULL THEN 0 ELSE DELIVERY_MAXIMUM_APC END;






DROP TABLE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS;
CREATE TABLE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS (
    CUSTOMER_ID INTEGER NOT NULL,
    TOTAL_UNITS_VISITED INTEGER NULL,
    FIRST_ORDER_ID INTEGER NULL,
    LAST_ORDER_ID INTEGER NULL,
    FIRST_ORDER_DATE DATE NULL,
    LAST_ORDER_DATE DATE NULL,
    TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DINE_IN_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DELIVERY_TICKET_COUNT INTEGER NULL,
    CANCELLED_TICKET_COUNT INTEGER NULL,
    TICKET_WITH_OFFER INTEGER NULL,
    TICKET_WITH_REDEMPTION INTEGER NULL,
    DINE_IN_TICKET INTEGER NULL,
    DELIVERY_TICKET INTEGER NULL,
    TAKE_AWAY_TICKET INTEGER NULL,
    ZOMATO_TICKET INTEGER NULL,
    SWIGGY_TICKET INTEGER NULL,
    FOOD_PANDA_TICKET INTEGER NULL,
    UBER_EATS_TICKET INTEGER NULL,
    OLD_APP_TICKET INTEGER NULL,
    WEB_APP_TICKET INTEGER NULL,
    CALL_CENTER_TICKET INTEGER NULL,
    OTHER_PARTNER_TICKET INTEGER NULL,
    TICKET_ON_MONDAY INTEGER NULL,
    TICKET_ON_TUESDAY INTEGER NULL,
    TICKET_ON_WEDNESDAY INTEGER NULL,
    TICKET_ON_THURSDAY INTEGER NULL,
    TICKET_ON_FRIDAY INTEGER NULL,
    TICKET_ON_SATURDAY INTEGER NULL,
    TICKET_ON_SUNDAY INTEGER NULL,
    TICKET_ON_WEEKDAY INTEGER NULL,
    TICKET_ON_WEEKEND INTEGER NULL,
    TICKET_IN_BREAKFAST INTEGER NULL,
    TICKET_IN_LUNCH INTEGER NULL,
    TICKET_IN_EVENING INTEGER NULL,
    TICKET_IN_DINNER INTEGER NULL,
    TICKET_IN_POST_DINNER INTEGER NULL,
    TICKET_IN_NIGHT INTEGER NULL,
    ONE_NPS_TICKET INTEGER NULL,
    TWO_NPS_TICKET INTEGER NULL,
    THREE_NPS_TICKET INTEGER NULL,
    FOUR_NPS_TICKET INTEGER NULL,
    FIVE_NPS_TICKET INTEGER NULL,
    SIX_NPS_TICKET INTEGER NULL,
    SEVEN_NPS_TICKET INTEGER NULL,
    EIGHT_NPS_TICKET INTEGER NULL,
    NINE_NPS_TICKET INTEGER NULL,
    TEN_NPS_TICKET INTEGER NULL,
    LAST_NPS_SCORE INTEGER NULL,
    NEGATIVE_NPS_TICKET INTEGER NULL,
    POSITIVE_NPS_TICKET INTEGER NULL,
    NEUTRAL_NPS_TICKET INTEGER NULL,
    TOTAL_SPEND DECIMAL(10 , 2 ) NULL,
    TOTAL_DISCOUNT DECIMAL(10 , 2 ) NULL,
    MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_SPEND DECIMAL(10 , 2 ) NULL,
    DELIVERY_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DELIVERY_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_SPEND DECIMAL(10 , 2 ) NULL,
    DINE_IN_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DINE_IN_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    CASH_TICKET INTEGER NULL,
    CARD_TICKET INTEGER NULL,
    AMEX_TICKET INTEGER NULL,
    PAYTM_TICKET INTEGER NULL,
    GIFT_CARD_TICKET INTEGER NULL,
    ONLY_GIFT_CARD_TICKET INTEGER NULL,
    MOBIKWIK_TICKET INTEGER NULL,
    ONLINE_PAYMENT_TICKET INTEGER NULL,
    OTHER_PAYMENT_TICKET INTEGER NULL,
    CASH_SPEND DECIMAL(10 , 2 ) NULL,
    CARD_SPEND DECIMAL(10 , 2 ) NULL,
    AMEX_SPEND DECIMAL(10 , 2 ) NULL,
    PAYTM_SPEND DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_SPEND DECIMAL(10 , 2 ) NULL,
    MOBIKWIK_SPEND DECIMAL(10 , 2 ) NULL,
    ONLINE_SPEND DECIMAL(10 , 2 ) NULL,
    OTHER_PAYMENT_SPEND DECIMAL(10 , 2 ) NULL,
    ONE_FEEDBACK_TICKET INTEGER NULL,
    TWO_FEEDBACK_TICKET INTEGER NULL,
    THREE_FEEDBACK_TICKET INTEGER NULL,
    FOUR_FEEDBACK_TICKET INTEGER NULL,
    FIVE_FEEDBACK_TICKET INTEGER NULL,
    TICKET_WITH_FOOD INTEGER NULL,
    TICKET_WITH_VEG INTEGER NULL,
    TICKET_WITH_NON_VEG INTEGER NULL,
    TICKET_WITH_HOT INTEGER NULL,
    TICKET_WITH_COLD INTEGER NULL,
    TICKET_WITH_BAKERY INTEGER NULL,
    TICKET_WITH_COMBO INTEGER NULL,
    TICKET_WITH_MERCHANDISE INTEGER NULL,
    TICKET_WITH_OTHER INTEGER NULL,
    TICKET_WITH_GIFT_CARD INTEGER NULL,
    PEOPLE_PER_TICKET INTEGER NULL,
    MINIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    MAXIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    SPLIT_PAYMENT_TICKET INTEGER NULL,
    LAST_PAYMENT_MODE INTEGER NULL,
    FIRST_UNIT_ID INTEGER NULL,
    FIRST_UNIT_NAME VARCHAR(100) NULL,
    LAST_UNIT_ID INTEGER NULL,
    LAST_UNIT_NAME VARCHAR(100) NULL,
    LAST_FEEDBACK_SCORE INTEGER NULL,
    DAY_GAP_SINCE_LAST_ORDER INTEGER NULL,
    TOTAL_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_APC DECIMAL(10 , 2 ) NULL
);

CREATE INDEX CUSTOMER_DATA_CUSTOMER_ID ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(CUSTOMER_ID) USING BTREE;


INSERT INTO CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS
(CUSTOMER_ID,
 FIRST_ORDER_ID,
 LAST_ORDER_ID,
 TOTAL_UNITS_VISITED,
 TICKET_COUNT,
 CANCELLED_TICKET_COUNT,
 FIRST_ORDER_DATE,
 LAST_ORDER_DATE,
 TICKET_WITH_OFFER,
 ZERO_AMOUNT_TICKET_COUNT,
 ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
 ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
 TICKET_WITH_REDEMPTION,
 DINE_IN_TICKET,
 DELIVERY_TICKET,
 TAKE_AWAY_TICKET,
 CALL_CENTER_TICKET,
 ZOMATO_TICKET,
 SWIGGY_TICKET,
 FOOD_PANDA_TICKET,
 UBER_EATS_TICKET,
 OLD_APP_TICKET,
 WEB_APP_TICKET,
 OTHER_PARTNER_TICKET,
 TICKET_ON_SUNDAY,
 TICKET_ON_MONDAY,
 TICKET_ON_TUESDAY,
 TICKET_ON_WEDNESDAY,
 TICKET_ON_THURSDAY,
 TICKET_ON_FRIDAY,
 TICKET_ON_SATURDAY,
 TICKET_ON_WEEKDAY,
 TICKET_ON_WEEKEND,
 TICKET_IN_BREAKFAST,
 TICKET_IN_LUNCH,
 TICKET_IN_EVENING,
 TICKET_IN_DINNER,
 TICKET_IN_POST_DINNER,
 TICKET_IN_NIGHT,
 TOTAL_SPEND,
 MINIMUM_APC,
 MAXIMUM_APC,
 TOTAL_DISCOUNT,
 DELIVERY_SPEND,
 DELIVERY_MINIMUM_APC,
 DELIVERY_MAXIMUM_APC,
 DELIVERY_DISCOUNT,
 DINE_IN_SPEND,
 DINE_IN_MINIMUM_APC,
 DINE_IN_MAXIMUM_APC,
 DINE_IN_DISCOUNT
)
  SELECT
    od.CUSTOMER_ID CUSTOMER_ID,
    MIN(od.ORDER_ID) FIRST_ORDER_ID,
    MAX(od.ORDER_ID) LAST_ORDER_ID,
    COUNT(DISTINCT od.UNIT_ID) TOTAL_UNITS_VISITED,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 1
      ELSE 0
    END) TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 0
      ELSE 1
    END) CANCELLED_TICKET_COUNT,
    MIN(od.BUSINESS_DATE) FIRST_ORDER_DATE,
    MAX(od.BUSINESS_DATE) LAST_ORDER_DATE,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TOTAL_AMOUNT <> od.TAXABLE_AMOUNT THEN 1
      ELSE 0
    END) TICKET_WITH_OFFER,
     SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.POINTS_REDEEMED < 0 THEN 1
      ELSE 0
    END) TICKET_WITH_REDEMPTION,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' THEN 1
      ELSE 0
    END) DINE_IN_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' THEN 1
      ELSE 0
    END) DELIVERY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'TAKE_AWAY' THEN 1
      ELSE 0
    END) TAKE_AWAY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 2 THEN 1
      ELSE 0
    END) CALL_CENTER_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 3 THEN 1
      ELSE 0
    END) ZOMATO_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 6 THEN 1
      ELSE 0
    END) SWIGGY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 5 THEN 1
      ELSE 0
    END) FOOD_PANDA_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 15 THEN 1
      ELSE 0
    END) UBER_EATS_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (10) THEN 1
      ELSE 0
    END) OLD_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (14) THEN 1
      ELSE 0
    END) WEB_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID NOT IN (2, 3, 5, 6, 10, 14, 15) THEN 1
      ELSE 0
    END) OTHER_PARTNER_TICKET,

    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1) THEN 1
      ELSE 0
    END) TICKET_ON_SUNDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (2) THEN 1
      ELSE 0
    END) TICKET_ON_MONDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (3) THEN 1
      ELSE 0
    END) TICKET_ON_TUESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (4) THEN 1
      ELSE 0
    END) TICKET_ON_WEDNESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (5) THEN 1
      ELSE 0
    END) TICKET_ON_THURSDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (6) THEN 1
      ELSE 0
    END) TICKET_ON_FRIDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (7) THEN 1
      ELSE 0
    END) TICKET_ON_SATURDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) NOT IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 6 AND
        HOUR(od.BILLING_SERVER_TIME) < 12 THEN 1
      ELSE 0
    END) TICKET_IN_BREAKFAST,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 12 AND
        HOUR(od.BILLING_SERVER_TIME) < 15 THEN 1
      ELSE 0
    END) TICKET_IN_LUNCH,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 15 AND
        HOUR(od.BILLING_SERVER_TIME) < 20 THEN 1
      ELSE 0
    END) TICKET_IN_EVENING,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 20 AND
        HOUR(od.BILLING_SERVER_TIME) < 22 THEN 1
      ELSE 0
    END) TICKET_IN_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 22 AND
        HOUR(od.BILLING_SERVER_TIME) <= 23 THEN 1
      ELSE 0
    END) TICKET_IN_POST_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 0 AND
        HOUR(od.BILLING_SERVER_TIME) < 6 THEN 1
      ELSE 0
    END) TICKET_IN_NIGHT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_DISCOUNT

  FROM KETTLE_DUMP.ORDER_DETAIL od
  WHERE od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -90 DAY)
  GROUP BY od.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c,
    (SELECT 
        CUSTOMER_ID,
            SUM(CASE
                WHEN NPS_SCORE = 1 THEN 1
                ELSE 0
            END) ONE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 2 THEN 1
                ELSE 0
            END) TWO_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 3 THEN 1
                ELSE 0
            END) THREE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 4 THEN 1
                ELSE 0
            END) FOUR_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 5 THEN 1
                ELSE 0
            END) FIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 6 THEN 1
                ELSE 0
            END) SIX_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 7 THEN 1
                ELSE 0
            END) SEVEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 8 THEN 1
                ELSE 0
            END) EIGHT_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 9 THEN 1
                ELSE 0
            END) NINE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 10 THEN 1
                ELSE 0
            END) TEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE < 7 THEN 1
                ELSE 0
            END) NEGATIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE >= 7 AND NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) NEUTRAL_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE > 8 THEN 1
                ELSE 0
            END) POSITIVE_NPS_TICKET
    FROM
        KETTLE_DUMP.ORDER_NPS_DETAIL
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_NPS_TICKET = d.ONE_NPS_TICKET,
    c.TWO_NPS_TICKET = d.TWO_NPS_TICKET,
    c.THREE_NPS_TICKET = d.THREE_NPS_TICKET,
    c.FOUR_NPS_TICKET = d.FOUR_NPS_TICKET,
    c.FIVE_NPS_TICKET = d.FIVE_NPS_TICKET,
    c.SIX_NPS_TICKET = d.SIX_NPS_TICKET,
    c.SEVEN_NPS_TICKET = d.SEVEN_NPS_TICKET,
    c.EIGHT_NPS_TICKET = d.EIGHT_NPS_TICKET,
    c.NINE_NPS_TICKET = d.NINE_NPS_TICKET,
    c.TEN_NPS_TICKET = d.TEN_NPS_TICKET,
    c.POSITIVE_NPS_TICKET = d.POSITIVE_NPS_TICKET,
    c.NEGATIVE_NPS_TICKET = d.NEGATIVE_NPS_TICKET,
    c.NEUTRAL_NPS_TICKET = d.NEUTRAL_NPS_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID

;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c,
    (SELECT 
        od.CUSTOMER_ID,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN os.AMOUNT_PAID
                ELSE 0
            END) CASH_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN 1
                ELSE 0
            END) CASH_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN os.AMOUNT_PAID
                ELSE 0
            END) CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN 1
                ELSE 0
            END) CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN os.AMOUNT_PAID
                ELSE 0
            END) AMEX_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN 1
                ELSE 0
            END) AMEX_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN os.AMOUNT_PAID
                ELSE 0
            END) GIFT_CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN 1
                ELSE 0
            END) GIFT_CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN os.AMOUNT_PAID
                ELSE 0
            END) PAYTM_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN 1
                ELSE 0
            END) PAYTM_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN os.AMOUNT_PAID
                ELSE 0
            END) ONLINE_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN 1
                ELSE 0
            END) ONLINE_PAYMENT_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) MOBIKWIK_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN 1
                ELSE 0
            END) MOBIKWIK_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID NOT IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) OTHER_PAYMENT_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN 1
                ELSE 0
            END) OTHER_PAYMENT_TICKET
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = os.ORDER_ID
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -90 DAY)
    GROUP BY od.CUSTOMER_ID) d 
SET 
    c.CASH_SPEND = d.CASH_SPEND,
    c.CASH_TICKET = d.CASH_TICKET,
    c.CARD_SPEND = d.CARD_SPEND,
    c.CARD_TICKET = d.CARD_TICKET,
    c.AMEX_SPEND = d.AMEX_SPEND,
    c.AMEX_TICKET = d.AMEX_TICKET,
    c.GIFT_CARD_SPEND = d.GIFT_CARD_SPEND,
    c.GIFT_CARD_TICKET = d.GIFT_CARD_TICKET,
    c.PAYTM_SPEND = d.PAYTM_SPEND,
    c.PAYTM_TICKET = d.PAYTM_TICKET,
    c.ONLINE_SPEND = d.ONLINE_SPEND,
    c.ONLINE_PAYMENT_TICKET = d.ONLINE_PAYMENT_TICKET,
    c.MOBIKWIK_SPEND = d.MOBIKWIK_SPEND,
    c.MOBIKWIK_TICKET = d.MOBIKWIK_TICKET,
    c.OTHER_PAYMENT_SPEND = d.OTHER_PAYMENT_SPEND,
    c.OTHER_PAYMENT_TICKET = d.OTHER_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c,
    (SELECT 
        CUSTOMER_ID,
            SUM(CASE
                WHEN FEEDBACK_RATING = 1 THEN 1
                ELSE 0
            END) ONE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 2 THEN 1
                ELSE 0
            END) TWO_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 3 THEN 1
                ELSE 0
            END) THREE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 4 THEN 1
                ELSE 0
            END) FOUR_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 5 THEN 1
                ELSE 0
            END) FIVE_FEEDBACK_TICKET
    FROM
        KETTLE_DUMP.ORDER_FEEDBACK_DETAIL
    WHERE
        FEEDBACK_STATUS = 'COMPLETED'
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_FEEDBACK_TICKET = d.ONE_FEEDBACK_TICKET,
    c.TWO_FEEDBACK_TICKET = d.TWO_FEEDBACK_TICKET,
    c.THREE_FEEDBACK_TICKET = d.THREE_FEEDBACK_TICKET,
    c.FOUR_FEEDBACK_TICKET = d.FOUR_FEEDBACK_TICKET,
    c.FIVE_FEEDBACK_TICKET = d.FIVE_FEEDBACK_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID

;
UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c,
    (SELECT 
        a.CUSTOMER_ID,
        SUM(CASE
                WHEN a.HOT_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_HOT,
            SUM(CASE
                WHEN a.VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_VEG,
            SUM(CASE
                WHEN a.NON_VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_NON_VEG,
            SUM(CASE
                WHEN a.FOOD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_FOOD,
            SUM(CASE
                WHEN a.COLD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COLD,
            SUM(CASE
                WHEN a.BAKERY_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_BAKERY,
            SUM(CASE
                WHEN a.COMBO_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COMBO,
            SUM(CASE
                WHEN a.MERCHANDISE_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_MERCHANDISE,
            SUM(CASE
                WHEN a.OTHERS_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_OTHER,
            SUM(CASE
                WHEN a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_GIFT_CARD,
            SUM(CASE
                WHEN a.HOT_QUANTITY = 0 AND 
                a.HOT_QUANTITY = 0 AND 
                a.FOOD_QUANTITY = 0 AND 
                a.COLD_QUANTITY = 0 AND 
                a.BAKERY_QUANTITY = 0 AND 
                a.COMBO_QUANTITY = 0 AND 
                a.MERCHANDISE_QUANTITY = 0 AND 
                a.OTHERS_QUANTITY = 0 AND a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_ONLY_GIFT_CARD
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 8 THEN oi.QUANTITY
                ELSE 0
            END) COMBO_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 9 THEN oi.QUANTITY
                ELSE 0
            END) MERCHANDISE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 10 THEN oi.QUANTITY
                ELSE 0
            END) BAKERY_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 12
                        AND pd.TAX_CODE <> 'GIFT_CARD'
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHERS_QUANTITY,
            SUM(CASE
                WHEN pd.TAX_CODE = 'GIFT_CARD' THEN oi.QUANTITY
                ELSE 0
            END) GIFT_CARD_QUANTITY,
			SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 7 AND (pd.ATTRIBUTE IS NULL
                        OR pd.ATTRIBUTE = 'VEG')
                THEN
                    oi.QUANTITY
                ELSE 0
            END) VEG_QUANTITY,
            SUM(CASE
                WHEN  pd.PRODUCT_TYPE = 7 AND pd.ATTRIBUTE = 'NON_VEG' THEN oi.QUANTITY
                ELSE 0
            END) NON_VEG_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -90 DAY)
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a
    GROUP BY a.CUSTOMER_ID) a 
SET 
    c.TICKET_WITH_HOT = a.TICKET_WITH_HOT,
    c.TICKET_WITH_VEG = a.TICKET_WITH_VEG,
    c.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG,
    c.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD,
    c.TICKET_WITH_COLD = a.TICKET_WITH_COLD,
    c.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY,
    c.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO,
    c.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE,
    c.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER,
    c.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD,
    c.ONLY_GIFT_CARD_TICKET =a.TICKET_WITH_ONLY_GIFT_CARD
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID;



UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c,
    (SELECT 
        m.CUSTOMER_ID,
            TRUNCATE(AVG(PEOPLE_PER_TICKET), 1) PEOPLE_PER_TICKET,
            MIN(PEOPLE_PER_TICKET) MINIMUM_PEOPLE_PER_ORDER,
            MAX(PEOPLE_PER_TICKET) MAXIMUM_PEOPLE_PER_ORDER
    FROM
        (SELECT 
        a.CUSTOMER_ID,
            a.ORDER_ID,
            CASE
                WHEN (a.HOT_QUANTITY + a.COLD_QUANTITY) > a.FOOD_QUANTITY THEN a.HOT_QUANTITY + a.COLD_QUANTITY
                ELSE a.FOOD_QUANTITY
            END PEOPLE_PER_TICKET
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -90 DAY)
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a) m
    GROUP BY m.CUSTOMER_ID) a 
SET 
    c.PEOPLE_PER_TICKET = a.PEOPLE_PER_TICKET,
    c.MINIMUM_PEOPLE_PER_ORDER = a.MINIMUM_PEOPLE_PER_ORDER,
    c.MAXIMUM_PEOPLE_PER_ORDER = a.MAXIMUM_PEOPLE_PER_ORDER
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID
;
UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c,
    (SELECT 
        CUSTOMER_ID, COUNT(*) SPLIT_PAYMENT_TICKET
    FROM
        (SELECT 
        od.ORDER_ID, od.CUSTOMER_ID, COUNT(*)
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_ID = os.ORDER_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -90 DAY)
    GROUP BY od.ORDER_ID , od.CUSTOMER_ID
    HAVING COUNT(*) > 1) a
    GROUP BY CUSTOMER_ID) a 
SET 
    c.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = a.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c,
    KETTLE_DUMP.ORDER_DETAIL o1,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u1,
    KETTLE_DUMP.ORDER_DETAIL o2,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u2 
SET 
    c.FIRST_UNIT_ID = u1.UNIT_ID,
    c.FIRST_UNIT_NAME = u1.UNIT_NAME,
    c.LAST_UNIT_ID = u2.UNIT_ID,
    c.LAST_UNIT_NAME = u2.UNIT_NAME
WHERE
    c.FIRST_ORDER_ID = o1.ORDER_ID
        AND c.LAST_ORDER_ID = o2.ORDER_ID
        AND o1.UNIT_ID = u1.UNIT_ID
        AND o2.UNIT_ID = u2.UNIT_ID;
        
        
UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c,
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o1 
SET 
    c.LAST_FEEDBACK_SCORE = o1.FEEDBACK_RATING
WHERE
    c.LAST_ORDER_ID = o1.ORDER_ID
        AND o1.FEEDBACK_STATUS = 'COMPLETED';

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS 
SET 
    TOTAL_APC = CASE
        WHEN
            COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(TOTAL_SPEND / (COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DINE_IN_APC = CASE
        WHEN
            COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DINE_IN_SPEND / (COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DELIVERY_APC = CASE
        WHEN
            COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DELIVERY_SPEND / (COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END;
update CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c, (select
    c.CUSTOMER_ID, MAX(c.LAST_ORDER_DATE) LAST_ORDER_DATE, MAX(o.BUSINESS_DATE) SECOND_LAST_BUSINESS_DATE, DATEDIFF(MAX(c.LAST_ORDER_DATE),MAX(o.BUSINESS_DATE)) DAY_GAP_SINCE_LAST_ORDER
FROM
    CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c1,
    CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c,
    KETTLE_DUMP.ORDER_DETAIL o
WHERE
    c.CUSTOMER_ID = o.CUSTOMER_ID
        AND o.ORDER_ID < c.LAST_ORDER_ID
        AND c1.CUSTOMER_ID = c.CUSTOMER_ID
GROUP BY c.CUSTOMER_ID
) a
SET c.DAY_GAP_SINCE_LAST_ORDER =  a.DAY_GAP_SINCE_LAST_ORDER
where c.CUSTOMER_ID = a.CUSTOMER_ID ;

update CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS
SET
ONE_NPS_TICKET =  case when ONE_NPS_TICKET IS NULL THEN 0 ELSE ONE_NPS_TICKET END,
TWO_NPS_TICKET =  case when TWO_NPS_TICKET IS NULL THEN 0 ELSE TWO_NPS_TICKET END,
THREE_NPS_TICKET =  case when THREE_NPS_TICKET IS NULL THEN 0 ELSE THREE_NPS_TICKET END,
FOUR_NPS_TICKET =  case when FOUR_NPS_TICKET IS NULL THEN 0 ELSE FOUR_NPS_TICKET END,
FIVE_NPS_TICKET =  case when FIVE_NPS_TICKET IS NULL THEN 0 ELSE FIVE_NPS_TICKET END,
SIX_NPS_TICKET =  case when SIX_NPS_TICKET IS NULL THEN 0 ELSE SIX_NPS_TICKET END,
SEVEN_NPS_TICKET =  case when SEVEN_NPS_TICKET IS NULL THEN 0 ELSE SEVEN_NPS_TICKET END,
EIGHT_NPS_TICKET =  case when EIGHT_NPS_TICKET IS NULL THEN 0 ELSE EIGHT_NPS_TICKET END,
NINE_NPS_TICKET =  case when NINE_NPS_TICKET IS NULL THEN 0 ELSE NINE_NPS_TICKET END,
TEN_NPS_TICKET =  case when TEN_NPS_TICKET IS NULL THEN 0 ELSE TEN_NPS_TICKET END,
LAST_NPS_SCORE =  case when LAST_NPS_SCORE IS NULL THEN 0 ELSE LAST_NPS_SCORE END,
NEGATIVE_NPS_TICKET =  case when NEGATIVE_NPS_TICKET IS NULL THEN 0 ELSE NEGATIVE_NPS_TICKET END,
POSITIVE_NPS_TICKET =  case when POSITIVE_NPS_TICKET IS NULL THEN 0 ELSE POSITIVE_NPS_TICKET END,
NEUTRAL_NPS_TICKET =  case when NEUTRAL_NPS_TICKET IS NULL THEN 0 ELSE NEUTRAL_NPS_TICKET END,
SPLIT_PAYMENT_TICKET =  case when SPLIT_PAYMENT_TICKET IS NULL THEN 0 ELSE SPLIT_PAYMENT_TICKET END,
DINE_IN_MINIMUM_APC =  case when DINE_IN_MINIMUM_APC IS NULL THEN 0 ELSE DINE_IN_MINIMUM_APC END,
DINE_IN_MAXIMUM_APC =  case when DINE_IN_MAXIMUM_APC IS NULL THEN 0 ELSE DINE_IN_MAXIMUM_APC END,
CASH_TICKET =  case when CASH_TICKET IS NULL THEN 0 ELSE CASH_TICKET END,
CARD_TICKET =  case when CARD_TICKET IS NULL THEN 0 ELSE CARD_TICKET END,
AMEX_TICKET =  case when AMEX_TICKET IS NULL THEN 0 ELSE AMEX_TICKET END,
PAYTM_TICKET =  case when PAYTM_TICKET IS NULL THEN 0 ELSE PAYTM_TICKET END,
GIFT_CARD_TICKET =  case when GIFT_CARD_TICKET IS NULL THEN 0 ELSE GIFT_CARD_TICKET END,
ONLY_GIFT_CARD_TICKET =  case when ONLY_GIFT_CARD_TICKET IS NULL THEN 0 ELSE ONLY_GIFT_CARD_TICKET END,
MOBIKWIK_TICKET =  case when MOBIKWIK_TICKET IS NULL THEN 0 ELSE MOBIKWIK_TICKET END,
ONLINE_PAYMENT_TICKET =  case when ONLINE_PAYMENT_TICKET IS NULL THEN 0 ELSE ONLINE_PAYMENT_TICKET END,
OTHER_PAYMENT_TICKET =  case when OTHER_PAYMENT_TICKET IS NULL THEN 0 ELSE OTHER_PAYMENT_TICKET END,
CASH_SPEND =  case when CASH_SPEND IS NULL THEN 0 ELSE CASH_SPEND END,
CARD_SPEND =  case when CARD_SPEND IS NULL THEN 0 ELSE CARD_SPEND END,
AMEX_SPEND =  case when AMEX_SPEND IS NULL THEN 0 ELSE AMEX_SPEND END,
PAYTM_SPEND =  case when PAYTM_SPEND IS NULL THEN 0 ELSE PAYTM_SPEND END,
GIFT_CARD_SPEND =  case when GIFT_CARD_SPEND IS NULL THEN 0 ELSE GIFT_CARD_SPEND END,
MOBIKWIK_SPEND =  case when MOBIKWIK_SPEND IS NULL THEN 0 ELSE MOBIKWIK_SPEND END,
ONLINE_SPEND =  case when ONLINE_SPEND IS NULL THEN 0 ELSE ONLINE_SPEND END,
OTHER_PAYMENT_SPEND =  case when OTHER_PAYMENT_SPEND IS NULL THEN 0 ELSE OTHER_PAYMENT_SPEND END,
ONE_FEEDBACK_TICKET =  case when ONE_FEEDBACK_TICKET IS NULL THEN 0 ELSE ONE_FEEDBACK_TICKET END,
TWO_FEEDBACK_TICKET =  case when TWO_FEEDBACK_TICKET IS NULL THEN 0 ELSE TWO_FEEDBACK_TICKET END,
THREE_FEEDBACK_TICKET =  case when THREE_FEEDBACK_TICKET IS NULL THEN 0 ELSE THREE_FEEDBACK_TICKET END,
FOUR_FEEDBACK_TICKET =  case when FOUR_FEEDBACK_TICKET IS NULL THEN 0 ELSE FOUR_FEEDBACK_TICKET END,
FIVE_FEEDBACK_TICKET =  case when FIVE_FEEDBACK_TICKET IS NULL THEN 0 ELSE FIVE_FEEDBACK_TICKET END,
MINIMUM_APC =  case when MINIMUM_APC IS NULL THEN 0 ELSE MINIMUM_APC END,
MAXIMUM_APC =  case when MAXIMUM_APC IS NULL THEN 0 ELSE MAXIMUM_APC END,
DELIVERY_SPEND =  case when DELIVERY_SPEND IS NULL THEN 0 ELSE DELIVERY_SPEND END,
DELIVERY_DISCOUNT =  case when DELIVERY_DISCOUNT IS NULL THEN 0 ELSE DELIVERY_DISCOUNT END,
DELIVERY_MINIMUM_APC =  case when DELIVERY_MINIMUM_APC IS NULL THEN 0 ELSE DELIVERY_MINIMUM_APC END,
DELIVERY_MAXIMUM_APC =  case when DELIVERY_MAXIMUM_APC IS NULL THEN 0 ELSE DELIVERY_MAXIMUM_APC END

;


DROP TABLE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS;
CREATE TABLE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS (
    CUSTOMER_ID INTEGER NOT NULL,
    TOTAL_UNITS_VISITED INTEGER NULL,
    FIRST_ORDER_ID INTEGER NULL,
    LAST_ORDER_ID INTEGER NULL,
    FIRST_ORDER_DATE DATE NULL,
    LAST_ORDER_DATE DATE NULL,
    TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DINE_IN_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DELIVERY_TICKET_COUNT INTEGER NULL,
    CANCELLED_TICKET_COUNT INTEGER NULL,
    TICKET_WITH_OFFER INTEGER NULL,
    TICKET_WITH_REDEMPTION INTEGER NULL,
    DINE_IN_TICKET INTEGER NULL,
    DELIVERY_TICKET INTEGER NULL,
    TAKE_AWAY_TICKET INTEGER NULL,
    ZOMATO_TICKET INTEGER NULL,
    SWIGGY_TICKET INTEGER NULL,
    FOOD_PANDA_TICKET INTEGER NULL,
    UBER_EATS_TICKET INTEGER NULL,
    OLD_APP_TICKET INTEGER NULL,
    WEB_APP_TICKET INTEGER NULL,
    CALL_CENTER_TICKET INTEGER NULL,
    OTHER_PARTNER_TICKET INTEGER NULL,
    TICKET_ON_MONDAY INTEGER NULL,
    TICKET_ON_TUESDAY INTEGER NULL,
    TICKET_ON_WEDNESDAY INTEGER NULL,
    TICKET_ON_THURSDAY INTEGER NULL,
    TICKET_ON_FRIDAY INTEGER NULL,
    TICKET_ON_SATURDAY INTEGER NULL,
    TICKET_ON_SUNDAY INTEGER NULL,
    TICKET_ON_WEEKDAY INTEGER NULL,
    TICKET_ON_WEEKEND INTEGER NULL,
    TICKET_IN_BREAKFAST INTEGER NULL,
    TICKET_IN_LUNCH INTEGER NULL,
    TICKET_IN_EVENING INTEGER NULL,
    TICKET_IN_DINNER INTEGER NULL,
    TICKET_IN_POST_DINNER INTEGER NULL,
    TICKET_IN_NIGHT INTEGER NULL,
    ONE_NPS_TICKET INTEGER NULL,
    TWO_NPS_TICKET INTEGER NULL,
    THREE_NPS_TICKET INTEGER NULL,
    FOUR_NPS_TICKET INTEGER NULL,
    FIVE_NPS_TICKET INTEGER NULL,
    SIX_NPS_TICKET INTEGER NULL,
    SEVEN_NPS_TICKET INTEGER NULL,
    EIGHT_NPS_TICKET INTEGER NULL,
    NINE_NPS_TICKET INTEGER NULL,
    TEN_NPS_TICKET INTEGER NULL,
    LAST_NPS_SCORE INTEGER NULL,
    NEGATIVE_NPS_TICKET INTEGER NULL,
    POSITIVE_NPS_TICKET INTEGER NULL,
    NEUTRAL_NPS_TICKET INTEGER NULL,
    TOTAL_SPEND DECIMAL(10 , 2 ) NULL,
    TOTAL_DISCOUNT DECIMAL(10 , 2 ) NULL,
    MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_SPEND DECIMAL(10 , 2 ) NULL,
    DELIVERY_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DELIVERY_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_SPEND DECIMAL(10 , 2 ) NULL,
    DINE_IN_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DINE_IN_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    CASH_TICKET INTEGER NULL,
    CARD_TICKET INTEGER NULL,
    AMEX_TICKET INTEGER NULL,
    PAYTM_TICKET INTEGER NULL,
    GIFT_CARD_TICKET INTEGER NULL,
    ONLY_GIFT_CARD_TICKET INTEGER NULL,
    MOBIKWIK_TICKET INTEGER NULL,
    ONLINE_PAYMENT_TICKET INTEGER NULL,
    OTHER_PAYMENT_TICKET INTEGER NULL,
    CASH_SPEND DECIMAL(10 , 2 ) NULL,
    CARD_SPEND DECIMAL(10 , 2 ) NULL,
    AMEX_SPEND DECIMAL(10 , 2 ) NULL,
    PAYTM_SPEND DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_SPEND DECIMAL(10 , 2 ) NULL,
    MOBIKWIK_SPEND DECIMAL(10 , 2 ) NULL,
    ONLINE_SPEND DECIMAL(10 , 2 ) NULL,
    OTHER_PAYMENT_SPEND DECIMAL(10 , 2 ) NULL,
    ONE_FEEDBACK_TICKET INTEGER NULL,
    TWO_FEEDBACK_TICKET INTEGER NULL,
    THREE_FEEDBACK_TICKET INTEGER NULL,
    FOUR_FEEDBACK_TICKET INTEGER NULL,
    FIVE_FEEDBACK_TICKET INTEGER NULL,
    TICKET_WITH_FOOD INTEGER NULL,
    TICKET_WITH_VEG INTEGER NULL,
    TICKET_WITH_NON_VEG INTEGER NULL,
    TICKET_WITH_HOT INTEGER NULL,
    TICKET_WITH_COLD INTEGER NULL,
    TICKET_WITH_BAKERY INTEGER NULL,
    TICKET_WITH_COMBO INTEGER NULL,
    TICKET_WITH_MERCHANDISE INTEGER NULL,
    TICKET_WITH_OTHER INTEGER NULL,
    TICKET_WITH_GIFT_CARD INTEGER NULL,
    PEOPLE_PER_TICKET INTEGER NULL,
    MINIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    MAXIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    SPLIT_PAYMENT_TICKET INTEGER NULL,
    LAST_PAYMENT_MODE INTEGER NULL,
    FIRST_UNIT_ID INTEGER NULL,
    FIRST_UNIT_NAME VARCHAR(100) NULL,
    LAST_UNIT_ID INTEGER NULL,
    LAST_UNIT_NAME VARCHAR(100) NULL,
    LAST_FEEDBACK_SCORE INTEGER NULL,
    DAY_GAP_SINCE_LAST_ORDER INTEGER NULL,
    TOTAL_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_APC DECIMAL(10 , 2 ) NULL
);

CREATE INDEX CUSTOMER_DATA_CUSTOMER_ID ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(CUSTOMER_ID) USING BTREE;


INSERT INTO CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS
(CUSTOMER_ID,
 FIRST_ORDER_ID,
 LAST_ORDER_ID,
 TOTAL_UNITS_VISITED,
 TICKET_COUNT,
 CANCELLED_TICKET_COUNT,
 FIRST_ORDER_DATE,
 LAST_ORDER_DATE,
 TICKET_WITH_OFFER,
 ZERO_AMOUNT_TICKET_COUNT,
 ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
 ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
 TICKET_WITH_REDEMPTION,
 DINE_IN_TICKET,
 DELIVERY_TICKET,
 TAKE_AWAY_TICKET,
 CALL_CENTER_TICKET,
 ZOMATO_TICKET,
 SWIGGY_TICKET,
 FOOD_PANDA_TICKET,
 UBER_EATS_TICKET,
 OLD_APP_TICKET,
 WEB_APP_TICKET,
 OTHER_PARTNER_TICKET,
 TICKET_ON_SUNDAY,
 TICKET_ON_MONDAY,
 TICKET_ON_TUESDAY,
 TICKET_ON_WEDNESDAY,
 TICKET_ON_THURSDAY,
 TICKET_ON_FRIDAY,
 TICKET_ON_SATURDAY,
 TICKET_ON_WEEKDAY,
 TICKET_ON_WEEKEND,
 TICKET_IN_BREAKFAST,
 TICKET_IN_LUNCH,
 TICKET_IN_EVENING,
 TICKET_IN_DINNER,
 TICKET_IN_POST_DINNER,
 TICKET_IN_NIGHT,
 TOTAL_SPEND,
 MINIMUM_APC,
 MAXIMUM_APC,
 TOTAL_DISCOUNT,
 DELIVERY_SPEND,
 DELIVERY_MINIMUM_APC,
 DELIVERY_MAXIMUM_APC,
 DELIVERY_DISCOUNT,
 DINE_IN_SPEND,
 DINE_IN_MINIMUM_APC,
 DINE_IN_MAXIMUM_APC,
 DINE_IN_DISCOUNT
)
  SELECT
    od.CUSTOMER_ID CUSTOMER_ID,
    MIN(od.ORDER_ID) FIRST_ORDER_ID,
    MAX(od.ORDER_ID) LAST_ORDER_ID,
    COUNT(DISTINCT od.UNIT_ID) TOTAL_UNITS_VISITED,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 1
      ELSE 0
    END) TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 0
      ELSE 1
    END) CANCELLED_TICKET_COUNT,
    MIN(od.BUSINESS_DATE) FIRST_ORDER_DATE,
    MAX(od.BUSINESS_DATE) LAST_ORDER_DATE,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TOTAL_AMOUNT <> od.TAXABLE_AMOUNT THEN 1
      ELSE 0
    END) TICKET_WITH_OFFER,
     SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.POINTS_REDEEMED < 0 THEN 1
      ELSE 0
    END) TICKET_WITH_REDEMPTION,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' THEN 1
      ELSE 0
    END) DINE_IN_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' THEN 1
      ELSE 0
    END) DELIVERY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'TAKE_AWAY' THEN 1
      ELSE 0
    END) TAKE_AWAY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 2 THEN 1
      ELSE 0
    END) CALL_CENTER_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 3 THEN 1
      ELSE 0
    END) ZOMATO_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 6 THEN 1
      ELSE 0
    END) SWIGGY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 5 THEN 1
      ELSE 0
    END) FOOD_PANDA_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 15 THEN 1
      ELSE 0
    END) UBER_EATS_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (10) THEN 1
      ELSE 0
    END) OLD_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (14) THEN 1
      ELSE 0
    END) WEB_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID NOT IN (2, 3, 5, 6, 10, 14, 15) THEN 1
      ELSE 0
    END) OTHER_PARTNER_TICKET,

    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1) THEN 1
      ELSE 0
    END) TICKET_ON_SUNDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (2) THEN 1
      ELSE 0
    END) TICKET_ON_MONDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (3) THEN 1
      ELSE 0
    END) TICKET_ON_TUESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (4) THEN 1
      ELSE 0
    END) TICKET_ON_WEDNESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (5) THEN 1
      ELSE 0
    END) TICKET_ON_THURSDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (6) THEN 1
      ELSE 0
    END) TICKET_ON_FRIDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (7) THEN 1
      ELSE 0
    END) TICKET_ON_SATURDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) NOT IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 6 AND
        HOUR(od.BILLING_SERVER_TIME) < 12 THEN 1
      ELSE 0
    END) TICKET_IN_BREAKFAST,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 12 AND
        HOUR(od.BILLING_SERVER_TIME) < 15 THEN 1
      ELSE 0
    END) TICKET_IN_LUNCH,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 15 AND
        HOUR(od.BILLING_SERVER_TIME) < 20 THEN 1
      ELSE 0
    END) TICKET_IN_EVENING,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 20 AND
        HOUR(od.BILLING_SERVER_TIME) < 22 THEN 1
      ELSE 0
    END) TICKET_IN_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 22 AND
        HOUR(od.BILLING_SERVER_TIME) <= 23 THEN 1
      ELSE 0
    END) TICKET_IN_POST_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 0 AND
        HOUR(od.BILLING_SERVER_TIME) < 6 THEN 1
      ELSE 0
    END) TICKET_IN_NIGHT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_DISCOUNT

  FROM KETTLE_DUMP.ORDER_DETAIL od
  WHERE od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -15 DAY)
  GROUP BY od.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c,
    (SELECT 
        CUSTOMER_ID,
            SUM(CASE
                WHEN NPS_SCORE = 1 THEN 1
                ELSE 0
            END) ONE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 2 THEN 1
                ELSE 0
            END) TWO_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 3 THEN 1
                ELSE 0
            END) THREE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 4 THEN 1
                ELSE 0
            END) FOUR_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 5 THEN 1
                ELSE 0
            END) FIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 6 THEN 1
                ELSE 0
            END) SIX_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 7 THEN 1
                ELSE 0
            END) SEVEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 8 THEN 1
                ELSE 0
            END) EIGHT_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 9 THEN 1
                ELSE 0
            END) NINE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 10 THEN 1
                ELSE 0
            END) TEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE < 7 THEN 1
                ELSE 0
            END) NEGATIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE >= 7 AND NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) NEUTRAL_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE > 8 THEN 1
                ELSE 0
            END) POSITIVE_NPS_TICKET
    FROM
        KETTLE_DUMP.ORDER_NPS_DETAIL
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_NPS_TICKET = d.ONE_NPS_TICKET,
    c.TWO_NPS_TICKET = d.TWO_NPS_TICKET,
    c.THREE_NPS_TICKET = d.THREE_NPS_TICKET,
    c.FOUR_NPS_TICKET = d.FOUR_NPS_TICKET,
    c.FIVE_NPS_TICKET = d.FIVE_NPS_TICKET,
    c.SIX_NPS_TICKET = d.SIX_NPS_TICKET,
    c.SEVEN_NPS_TICKET = d.SEVEN_NPS_TICKET,
    c.EIGHT_NPS_TICKET = d.EIGHT_NPS_TICKET,
    c.NINE_NPS_TICKET = d.NINE_NPS_TICKET,
    c.TEN_NPS_TICKET = d.TEN_NPS_TICKET,
    c.POSITIVE_NPS_TICKET = d.POSITIVE_NPS_TICKET,
    c.NEGATIVE_NPS_TICKET = d.NEGATIVE_NPS_TICKET,
    c.NEUTRAL_NPS_TICKET = d.NEUTRAL_NPS_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID

;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c,
    (SELECT 
        od.CUSTOMER_ID,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN os.AMOUNT_PAID
                ELSE 0
            END) CASH_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN 1
                ELSE 0
            END) CASH_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN os.AMOUNT_PAID
                ELSE 0
            END) CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN 1
                ELSE 0
            END) CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN os.AMOUNT_PAID
                ELSE 0
            END) AMEX_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN 1
                ELSE 0
            END) AMEX_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN os.AMOUNT_PAID
                ELSE 0
            END) GIFT_CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN 1
                ELSE 0
            END) GIFT_CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN os.AMOUNT_PAID
                ELSE 0
            END) PAYTM_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN 1
                ELSE 0
            END) PAYTM_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN os.AMOUNT_PAID
                ELSE 0
            END) ONLINE_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN 1
                ELSE 0
            END) ONLINE_PAYMENT_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) MOBIKWIK_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN 1
                ELSE 0
            END) MOBIKWIK_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID NOT IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) OTHER_PAYMENT_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN 1
                ELSE 0
            END) OTHER_PAYMENT_TICKET
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = os.ORDER_ID
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -15 DAY)
    GROUP BY od.CUSTOMER_ID) d 
SET 
    c.CASH_SPEND = d.CASH_SPEND,
    c.CASH_TICKET = d.CASH_TICKET,
    c.CARD_SPEND = d.CARD_SPEND,
    c.CARD_TICKET = d.CARD_TICKET,
    c.AMEX_SPEND = d.AMEX_SPEND,
    c.AMEX_TICKET = d.AMEX_TICKET,
    c.GIFT_CARD_SPEND = d.GIFT_CARD_SPEND,
    c.GIFT_CARD_TICKET = d.GIFT_CARD_TICKET,
    c.PAYTM_SPEND = d.PAYTM_SPEND,
    c.PAYTM_TICKET = d.PAYTM_TICKET,
    c.ONLINE_SPEND = d.ONLINE_SPEND,
    c.ONLINE_PAYMENT_TICKET = d.ONLINE_PAYMENT_TICKET,
    c.MOBIKWIK_SPEND = d.MOBIKWIK_SPEND,
    c.MOBIKWIK_TICKET = d.MOBIKWIK_TICKET,
    c.OTHER_PAYMENT_SPEND = d.OTHER_PAYMENT_SPEND,
    c.OTHER_PAYMENT_TICKET = d.OTHER_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c,
    (SELECT 
        CUSTOMER_ID,
            SUM(CASE
                WHEN FEEDBACK_RATING = 1 THEN 1
                ELSE 0
            END) ONE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 2 THEN 1
                ELSE 0
            END) TWO_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 3 THEN 1
                ELSE 0
            END) THREE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 4 THEN 1
                ELSE 0
            END) FOUR_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 5 THEN 1
                ELSE 0
            END) FIVE_FEEDBACK_TICKET
    FROM
        KETTLE_DUMP.ORDER_FEEDBACK_DETAIL
    WHERE
        FEEDBACK_STATUS = 'COMPLETED'
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_FEEDBACK_TICKET = d.ONE_FEEDBACK_TICKET,
    c.TWO_FEEDBACK_TICKET = d.TWO_FEEDBACK_TICKET,
    c.THREE_FEEDBACK_TICKET = d.THREE_FEEDBACK_TICKET,
    c.FOUR_FEEDBACK_TICKET = d.FOUR_FEEDBACK_TICKET,
    c.FIVE_FEEDBACK_TICKET = d.FIVE_FEEDBACK_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID

;
UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c,
    (SELECT 
        a.CUSTOMER_ID,
        SUM(CASE
                WHEN a.HOT_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_HOT,
            SUM(CASE
                WHEN a.VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_VEG,
            SUM(CASE
                WHEN a.NON_VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_NON_VEG,
            SUM(CASE
                WHEN a.FOOD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_FOOD,
            SUM(CASE
                WHEN a.COLD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COLD,
            SUM(CASE
                WHEN a.BAKERY_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_BAKERY,
            SUM(CASE
                WHEN a.COMBO_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COMBO,
            SUM(CASE
                WHEN a.MERCHANDISE_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_MERCHANDISE,
            SUM(CASE
                WHEN a.OTHERS_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_OTHER,
            SUM(CASE
                WHEN a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_GIFT_CARD,
            SUM(CASE
                WHEN a.HOT_QUANTITY = 0 AND 
                a.HOT_QUANTITY = 0 AND 
                a.FOOD_QUANTITY = 0 AND 
                a.COLD_QUANTITY = 0 AND 
                a.BAKERY_QUANTITY = 0 AND 
                a.COMBO_QUANTITY = 0 AND 
                a.MERCHANDISE_QUANTITY = 0 AND 
                a.OTHERS_QUANTITY = 0 AND a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_ONLY_GIFT_CARD
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 8 THEN oi.QUANTITY
                ELSE 0
            END) COMBO_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 9 THEN oi.QUANTITY
                ELSE 0
            END) MERCHANDISE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 10 THEN oi.QUANTITY
                ELSE 0
            END) BAKERY_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 12
                        AND pd.TAX_CODE <> 'GIFT_CARD'
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHERS_QUANTITY,
            SUM(CASE
                WHEN pd.TAX_CODE = 'GIFT_CARD' THEN oi.QUANTITY
                ELSE 0
            END) GIFT_CARD_QUANTITY,
			SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 7 AND (pd.ATTRIBUTE IS NULL
                        OR pd.ATTRIBUTE = 'VEG')
                THEN
                    oi.QUANTITY
                ELSE 0
            END) VEG_QUANTITY,
            SUM(CASE
                WHEN  pd.PRODUCT_TYPE = 7 AND pd.ATTRIBUTE = 'NON_VEG' THEN oi.QUANTITY
                ELSE 0
            END) NON_VEG_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -15 DAY)
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a
    GROUP BY a.CUSTOMER_ID) a 
SET 
    c.TICKET_WITH_HOT = a.TICKET_WITH_HOT,
    c.TICKET_WITH_VEG = a.TICKET_WITH_VEG,
    c.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG,
    c.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD,
    c.TICKET_WITH_COLD = a.TICKET_WITH_COLD,
    c.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY,
    c.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO,
    c.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE,
    c.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER,
    c.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD,
    c.ONLY_GIFT_CARD_TICKET =a.TICKET_WITH_ONLY_GIFT_CARD
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID;



UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c,
    (SELECT 
        m.CUSTOMER_ID,
            TRUNCATE(AVG(PEOPLE_PER_TICKET), 1) PEOPLE_PER_TICKET,
            MIN(PEOPLE_PER_TICKET) MINIMUM_PEOPLE_PER_ORDER,
            MAX(PEOPLE_PER_TICKET) MAXIMUM_PEOPLE_PER_ORDER
    FROM
        (SELECT 
        a.CUSTOMER_ID,
            a.ORDER_ID,
            CASE
                WHEN (a.HOT_QUANTITY + a.COLD_QUANTITY) > a.FOOD_QUANTITY THEN a.HOT_QUANTITY + a.COLD_QUANTITY
                ELSE a.FOOD_QUANTITY
            END PEOPLE_PER_TICKET
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -15 DAY)
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a) m
    GROUP BY m.CUSTOMER_ID) a 
SET 
    c.PEOPLE_PER_TICKET = a.PEOPLE_PER_TICKET,
    c.MINIMUM_PEOPLE_PER_ORDER = a.MINIMUM_PEOPLE_PER_ORDER,
    c.MAXIMUM_PEOPLE_PER_ORDER = a.MAXIMUM_PEOPLE_PER_ORDER
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID
;
UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c,
    (SELECT 
        CUSTOMER_ID, COUNT(*) SPLIT_PAYMENT_TICKET
    FROM
        (SELECT 
        od.ORDER_ID, od.CUSTOMER_ID, COUNT(*)
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_ID = os.ORDER_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -15 DAY)
    GROUP BY od.ORDER_ID , od.CUSTOMER_ID
    HAVING COUNT(*) > 1) a
    GROUP BY CUSTOMER_ID) a 
SET 
    c.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = a.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c,
    KETTLE_DUMP.ORDER_DETAIL o1,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u1,
    KETTLE_DUMP.ORDER_DETAIL o2,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u2 
SET 
    c.FIRST_UNIT_ID = u1.UNIT_ID,
    c.FIRST_UNIT_NAME = u1.UNIT_NAME,
    c.LAST_UNIT_ID = u2.UNIT_ID,
    c.LAST_UNIT_NAME = u2.UNIT_NAME
WHERE
    c.FIRST_ORDER_ID = o1.ORDER_ID
        AND c.LAST_ORDER_ID = o2.ORDER_ID
        AND o1.UNIT_ID = u1.UNIT_ID
        AND o2.UNIT_ID = u2.UNIT_ID;
        
        
UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c,
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o1 
SET 
    c.LAST_FEEDBACK_SCORE = o1.FEEDBACK_RATING
WHERE
    c.LAST_ORDER_ID = o1.ORDER_ID
        AND o1.FEEDBACK_STATUS = 'COMPLETED';

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS 
SET 
    TOTAL_APC = CASE
        WHEN
            COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(TOTAL_SPEND / (COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DINE_IN_APC = CASE
        WHEN
            COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DINE_IN_SPEND / (COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DELIVERY_APC = CASE
        WHEN
            COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DELIVERY_SPEND / (COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END;
update CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c, (select
    c.CUSTOMER_ID, MAX(c.LAST_ORDER_DATE) LAST_ORDER_DATE, MAX(o.BUSINESS_DATE) SECOND_LAST_BUSINESS_DATE, DATEDIFF(MAX(c.LAST_ORDER_DATE),MAX(o.BUSINESS_DATE)) DAY_GAP_SINCE_LAST_ORDER
FROM
    CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c1,
    CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c,
    KETTLE_DUMP.ORDER_DETAIL o
WHERE
    c.CUSTOMER_ID = o.CUSTOMER_ID
        AND o.ORDER_ID < c.LAST_ORDER_ID
        AND c1.CUSTOMER_ID = c.CUSTOMER_ID
GROUP BY c.CUSTOMER_ID
) a
SET c.DAY_GAP_SINCE_LAST_ORDER =  a.DAY_GAP_SINCE_LAST_ORDER
where c.CUSTOMER_ID = a.CUSTOMER_ID ;

update CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS
SET
ONE_NPS_TICKET =  case when ONE_NPS_TICKET IS NULL THEN 0 ELSE ONE_NPS_TICKET END,
TWO_NPS_TICKET =  case when TWO_NPS_TICKET IS NULL THEN 0 ELSE TWO_NPS_TICKET END,
THREE_NPS_TICKET =  case when THREE_NPS_TICKET IS NULL THEN 0 ELSE THREE_NPS_TICKET END,
FOUR_NPS_TICKET =  case when FOUR_NPS_TICKET IS NULL THEN 0 ELSE FOUR_NPS_TICKET END,
FIVE_NPS_TICKET =  case when FIVE_NPS_TICKET IS NULL THEN 0 ELSE FIVE_NPS_TICKET END,
SIX_NPS_TICKET =  case when SIX_NPS_TICKET IS NULL THEN 0 ELSE SIX_NPS_TICKET END,
SEVEN_NPS_TICKET =  case when SEVEN_NPS_TICKET IS NULL THEN 0 ELSE SEVEN_NPS_TICKET END,
EIGHT_NPS_TICKET =  case when EIGHT_NPS_TICKET IS NULL THEN 0 ELSE EIGHT_NPS_TICKET END,
NINE_NPS_TICKET =  case when NINE_NPS_TICKET IS NULL THEN 0 ELSE NINE_NPS_TICKET END,
TEN_NPS_TICKET =  case when TEN_NPS_TICKET IS NULL THEN 0 ELSE TEN_NPS_TICKET END,
LAST_NPS_SCORE =  case when LAST_NPS_SCORE IS NULL THEN 0 ELSE LAST_NPS_SCORE END,
NEGATIVE_NPS_TICKET =  case when NEGATIVE_NPS_TICKET IS NULL THEN 0 ELSE NEGATIVE_NPS_TICKET END,
POSITIVE_NPS_TICKET =  case when POSITIVE_NPS_TICKET IS NULL THEN 0 ELSE POSITIVE_NPS_TICKET END,
NEUTRAL_NPS_TICKET =  case when NEUTRAL_NPS_TICKET IS NULL THEN 0 ELSE NEUTRAL_NPS_TICKET END,
SPLIT_PAYMENT_TICKET =  case when SPLIT_PAYMENT_TICKET IS NULL THEN 0 ELSE SPLIT_PAYMENT_TICKET END,
DINE_IN_MINIMUM_APC =  case when DINE_IN_MINIMUM_APC IS NULL THEN 0 ELSE DINE_IN_MINIMUM_APC END,
DINE_IN_MAXIMUM_APC =  case when DINE_IN_MAXIMUM_APC IS NULL THEN 0 ELSE DINE_IN_MAXIMUM_APC END,
CASH_TICKET =  case when CASH_TICKET IS NULL THEN 0 ELSE CASH_TICKET END,
CARD_TICKET =  case when CARD_TICKET IS NULL THEN 0 ELSE CARD_TICKET END,
AMEX_TICKET =  case when AMEX_TICKET IS NULL THEN 0 ELSE AMEX_TICKET END,
PAYTM_TICKET =  case when PAYTM_TICKET IS NULL THEN 0 ELSE PAYTM_TICKET END,
GIFT_CARD_TICKET =  case when GIFT_CARD_TICKET IS NULL THEN 0 ELSE GIFT_CARD_TICKET END,
ONLY_GIFT_CARD_TICKET =  case when ONLY_GIFT_CARD_TICKET IS NULL THEN 0 ELSE ONLY_GIFT_CARD_TICKET END,
MOBIKWIK_TICKET =  case when MOBIKWIK_TICKET IS NULL THEN 0 ELSE MOBIKWIK_TICKET END,
ONLINE_PAYMENT_TICKET =  case when ONLINE_PAYMENT_TICKET IS NULL THEN 0 ELSE ONLINE_PAYMENT_TICKET END,
OTHER_PAYMENT_TICKET =  case when OTHER_PAYMENT_TICKET IS NULL THEN 0 ELSE OTHER_PAYMENT_TICKET END,
CASH_SPEND =  case when CASH_SPEND IS NULL THEN 0 ELSE CASH_SPEND END,
CARD_SPEND =  case when CARD_SPEND IS NULL THEN 0 ELSE CARD_SPEND END,
AMEX_SPEND =  case when AMEX_SPEND IS NULL THEN 0 ELSE AMEX_SPEND END,
PAYTM_SPEND =  case when PAYTM_SPEND IS NULL THEN 0 ELSE PAYTM_SPEND END,
GIFT_CARD_SPEND =  case when GIFT_CARD_SPEND IS NULL THEN 0 ELSE GIFT_CARD_SPEND END,
MOBIKWIK_SPEND =  case when MOBIKWIK_SPEND IS NULL THEN 0 ELSE MOBIKWIK_SPEND END,
ONLINE_SPEND =  case when ONLINE_SPEND IS NULL THEN 0 ELSE ONLINE_SPEND END,
OTHER_PAYMENT_SPEND =  case when OTHER_PAYMENT_SPEND IS NULL THEN 0 ELSE OTHER_PAYMENT_SPEND END,
ONE_FEEDBACK_TICKET =  case when ONE_FEEDBACK_TICKET IS NULL THEN 0 ELSE ONE_FEEDBACK_TICKET END,
TWO_FEEDBACK_TICKET =  case when TWO_FEEDBACK_TICKET IS NULL THEN 0 ELSE TWO_FEEDBACK_TICKET END,
THREE_FEEDBACK_TICKET =  case when THREE_FEEDBACK_TICKET IS NULL THEN 0 ELSE THREE_FEEDBACK_TICKET END,
FOUR_FEEDBACK_TICKET =  case when FOUR_FEEDBACK_TICKET IS NULL THEN 0 ELSE FOUR_FEEDBACK_TICKET END,
FIVE_FEEDBACK_TICKET =  case when FIVE_FEEDBACK_TICKET IS NULL THEN 0 ELSE FIVE_FEEDBACK_TICKET END,
MINIMUM_APC =  case when MINIMUM_APC IS NULL THEN 0 ELSE MINIMUM_APC END,
MAXIMUM_APC =  case when MAXIMUM_APC IS NULL THEN 0 ELSE MAXIMUM_APC END,
DELIVERY_SPEND =  case when DELIVERY_SPEND IS NULL THEN 0 ELSE DELIVERY_SPEND END,
DELIVERY_DISCOUNT =  case when DELIVERY_DISCOUNT IS NULL THEN 0 ELSE DELIVERY_DISCOUNT END,
DELIVERY_MINIMUM_APC =  case when DELIVERY_MINIMUM_APC IS NULL THEN 0 ELSE DELIVERY_MINIMUM_APC END,
DELIVERY_MAXIMUM_APC =  case when DELIVERY_MAXIMUM_APC IS NULL THEN 0 ELSE DELIVERY_MAXIMUM_APC END
;

DROP TABLE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS;
CREATE TABLE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS (
    CUSTOMER_ID INTEGER NOT NULL,
    TOTAL_UNITS_VISITED INTEGER NULL,
    FIRST_ORDER_ID INTEGER NULL,
    LAST_ORDER_ID INTEGER NULL,
    FIRST_ORDER_DATE DATE NULL,
    LAST_ORDER_DATE DATE NULL,
    TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DINE_IN_TICKET_COUNT INTEGER NULL,
    ZERO_AMOUNT_DELIVERY_TICKET_COUNT INTEGER NULL,
    CANCELLED_TICKET_COUNT INTEGER NULL,
    TICKET_WITH_OFFER INTEGER NULL,
    TICKET_WITH_REDEMPTION INTEGER NULL,
    DINE_IN_TICKET INTEGER NULL,
    DELIVERY_TICKET INTEGER NULL,
    TAKE_AWAY_TICKET INTEGER NULL,
    ZOMATO_TICKET INTEGER NULL,
    SWIGGY_TICKET INTEGER NULL,
    FOOD_PANDA_TICKET INTEGER NULL,
    UBER_EATS_TICKET INTEGER NULL,
    OLD_APP_TICKET INTEGER NULL,
    WEB_APP_TICKET INTEGER NULL,
    CALL_CENTER_TICKET INTEGER NULL,
    OTHER_PARTNER_TICKET INTEGER NULL,
    TICKET_ON_MONDAY INTEGER NULL,
    TICKET_ON_TUESDAY INTEGER NULL,
    TICKET_ON_WEDNESDAY INTEGER NULL,
    TICKET_ON_THURSDAY INTEGER NULL,
    TICKET_ON_FRIDAY INTEGER NULL,
    TICKET_ON_SATURDAY INTEGER NULL,
    TICKET_ON_SUNDAY INTEGER NULL,
    TICKET_ON_WEEKDAY INTEGER NULL,
    TICKET_ON_WEEKEND INTEGER NULL,
    TICKET_IN_BREAKFAST INTEGER NULL,
    TICKET_IN_LUNCH INTEGER NULL,
    TICKET_IN_EVENING INTEGER NULL,
    TICKET_IN_DINNER INTEGER NULL,
    TICKET_IN_POST_DINNER INTEGER NULL,
    TICKET_IN_NIGHT INTEGER NULL,
    ONE_NPS_TICKET INTEGER NULL,
    TWO_NPS_TICKET INTEGER NULL,
    THREE_NPS_TICKET INTEGER NULL,
    FOUR_NPS_TICKET INTEGER NULL,
    FIVE_NPS_TICKET INTEGER NULL,
    SIX_NPS_TICKET INTEGER NULL,
    SEVEN_NPS_TICKET INTEGER NULL,
    EIGHT_NPS_TICKET INTEGER NULL,
    NINE_NPS_TICKET INTEGER NULL,
    TEN_NPS_TICKET INTEGER NULL,
    LAST_NPS_SCORE INTEGER NULL,
    NEGATIVE_NPS_TICKET INTEGER NULL,
    POSITIVE_NPS_TICKET INTEGER NULL,
    NEUTRAL_NPS_TICKET INTEGER NULL,
    TOTAL_SPEND DECIMAL(10 , 2 ) NULL,
    TOTAL_DISCOUNT DECIMAL(10 , 2 ) NULL,
    MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_SPEND DECIMAL(10 , 2 ) NULL,
    DELIVERY_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DELIVERY_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_SPEND DECIMAL(10 , 2 ) NULL,
    DINE_IN_DISCOUNT DECIMAL(10 , 2 ) NULL,
    DINE_IN_MINIMUM_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_MAXIMUM_APC DECIMAL(10 , 2 ) NULL,
    CASH_TICKET INTEGER NULL,
    CARD_TICKET INTEGER NULL,
    AMEX_TICKET INTEGER NULL,
    PAYTM_TICKET INTEGER NULL,
    GIFT_CARD_TICKET INTEGER NULL,
    ONLY_GIFT_CARD_TICKET INTEGER NULL,
    MOBIKWIK_TICKET INTEGER NULL,
    ONLINE_PAYMENT_TICKET INTEGER NULL,
    OTHER_PAYMENT_TICKET INTEGER NULL,
    CASH_SPEND DECIMAL(10 , 2 ) NULL,
    CARD_SPEND DECIMAL(10 , 2 ) NULL,
    AMEX_SPEND DECIMAL(10 , 2 ) NULL,
    PAYTM_SPEND DECIMAL(10 , 2 ) NULL,
    GIFT_CARD_SPEND DECIMAL(10 , 2 ) NULL,
    MOBIKWIK_SPEND DECIMAL(10 , 2 ) NULL,
    ONLINE_SPEND DECIMAL(10 , 2 ) NULL,
    OTHER_PAYMENT_SPEND DECIMAL(10 , 2 ) NULL,
    ONE_FEEDBACK_TICKET INTEGER NULL,
    TWO_FEEDBACK_TICKET INTEGER NULL,
    THREE_FEEDBACK_TICKET INTEGER NULL,
    FOUR_FEEDBACK_TICKET INTEGER NULL,
    FIVE_FEEDBACK_TICKET INTEGER NULL,
    TICKET_WITH_FOOD INTEGER NULL,
    TICKET_WITH_VEG INTEGER NULL,
    TICKET_WITH_NON_VEG INTEGER NULL,
    TICKET_WITH_HOT INTEGER NULL,
    TICKET_WITH_COLD INTEGER NULL,
    TICKET_WITH_BAKERY INTEGER NULL,
    TICKET_WITH_COMBO INTEGER NULL,
    TICKET_WITH_MERCHANDISE INTEGER NULL,
    TICKET_WITH_OTHER INTEGER NULL,
    TICKET_WITH_GIFT_CARD INTEGER NULL,
    PEOPLE_PER_TICKET INTEGER NULL,
    MINIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    MAXIMUM_PEOPLE_PER_ORDER INTEGER NULL,
    SPLIT_PAYMENT_TICKET INTEGER NULL,
    LAST_PAYMENT_MODE INTEGER NULL,
    FIRST_UNIT_ID INTEGER NULL,
    FIRST_UNIT_NAME VARCHAR(100) NULL,
    LAST_UNIT_ID INTEGER NULL,
    LAST_UNIT_NAME VARCHAR(100) NULL,
    LAST_FEEDBACK_SCORE INTEGER NULL,
    DAY_GAP_SINCE_LAST_ORDER INTEGER NULL,
    TOTAL_APC DECIMAL(10 , 2 ) NULL,
    DELIVERY_APC DECIMAL(10 , 2 ) NULL,
    DINE_IN_APC DECIMAL(10 , 2 ) NULL
);

CREATE INDEX CUSTOMER_DATA_CUSTOMER_ID ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(CUSTOMER_ID) USING BTREE;


INSERT INTO CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS
(CUSTOMER_ID,
 FIRST_ORDER_ID,
 LAST_ORDER_ID,
 TOTAL_UNITS_VISITED,
 TICKET_COUNT,
 CANCELLED_TICKET_COUNT,
 FIRST_ORDER_DATE,
 LAST_ORDER_DATE,
 TICKET_WITH_OFFER,
 ZERO_AMOUNT_TICKET_COUNT,
 ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
 ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
 TICKET_WITH_REDEMPTION,
 DINE_IN_TICKET,
 DELIVERY_TICKET,
 TAKE_AWAY_TICKET,
 CALL_CENTER_TICKET,
 ZOMATO_TICKET,
 SWIGGY_TICKET,
 FOOD_PANDA_TICKET,
 UBER_EATS_TICKET,
 OLD_APP_TICKET,
 WEB_APP_TICKET,
 OTHER_PARTNER_TICKET,
 TICKET_ON_SUNDAY,
 TICKET_ON_MONDAY,
 TICKET_ON_TUESDAY,
 TICKET_ON_WEDNESDAY,
 TICKET_ON_THURSDAY,
 TICKET_ON_FRIDAY,
 TICKET_ON_SATURDAY,
 TICKET_ON_WEEKDAY,
 TICKET_ON_WEEKEND,
 TICKET_IN_BREAKFAST,
 TICKET_IN_LUNCH,
 TICKET_IN_EVENING,
 TICKET_IN_DINNER,
 TICKET_IN_POST_DINNER,
 TICKET_IN_NIGHT,
 TOTAL_SPEND,
 MINIMUM_APC,
 MAXIMUM_APC,
 TOTAL_DISCOUNT,
 DELIVERY_SPEND,
 DELIVERY_MINIMUM_APC,
 DELIVERY_MAXIMUM_APC,
 DELIVERY_DISCOUNT,
 DINE_IN_SPEND,
 DINE_IN_MINIMUM_APC,
 DINE_IN_MAXIMUM_APC,
 DINE_IN_DISCOUNT
)
  SELECT
    od.CUSTOMER_ID CUSTOMER_ID,
    MIN(od.ORDER_ID) FIRST_ORDER_ID,
    MAX(od.ORDER_ID) LAST_ORDER_ID,
    COUNT(DISTINCT od.UNIT_ID) TOTAL_UNITS_VISITED,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 1
      ELSE 0
    END) TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 0
      ELSE 1
    END) CANCELLED_TICKET_COUNT,
    MIN(od.BUSINESS_DATE) FIRST_ORDER_DATE,
    MAX(od.BUSINESS_DATE) LAST_ORDER_DATE,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TOTAL_AMOUNT <> od.TAXABLE_AMOUNT THEN 1
      ELSE 0
    END) TICKET_WITH_OFFER,
     SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DINE_IN_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT = 0.00 THEN 1
      ELSE 0
    END) ZERO_AMOUNT_DELIVERY_TICKET_COUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.POINTS_REDEEMED < 0 THEN 1
      ELSE 0
    END) TICKET_WITH_REDEMPTION,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'CAFE' THEN 1
      ELSE 0
    END) DINE_IN_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' THEN 1
      ELSE 0
    END) DELIVERY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'TAKE_AWAY' THEN 1
      ELSE 0
    END) TAKE_AWAY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 2 THEN 1
      ELSE 0
    END) CALL_CENTER_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 3 THEN 1
      ELSE 0
    END) ZOMATO_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 6 THEN 1
      ELSE 0
    END) SWIGGY_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 5 THEN 1
      ELSE 0
    END) FOOD_PANDA_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID = 15 THEN 1
      ELSE 0
    END) UBER_EATS_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (10) THEN 1
      ELSE 0
    END) OLD_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID IN (14) THEN 1
      ELSE 0
    END) WEB_APP_TICKET,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.CHANNEL_PARTNER_ID NOT IN (2, 3, 5, 6, 10, 14, 15) THEN 1
      ELSE 0
    END) OTHER_PARTNER_TICKET,

    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1) THEN 1
      ELSE 0
    END) TICKET_ON_SUNDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (2) THEN 1
      ELSE 0
    END) TICKET_ON_MONDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (3) THEN 1
      ELSE 0
    END) TICKET_ON_TUESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (4) THEN 1
      ELSE 0
    END) TICKET_ON_WEDNESDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (5) THEN 1
      ELSE 0
    END) TICKET_ON_THURSDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (6) THEN 1
      ELSE 0
    END) TICKET_ON_FRIDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (7) THEN 1
      ELSE 0
    END) TICKET_ON_SATURDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) NOT IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKDAY,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        DAYOFWEEK(od.BUSINESS_DATE) IN (1, 7) THEN 1
      ELSE 0
    END) TICKET_ON_WEEKEND,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 6 AND
        HOUR(od.BILLING_SERVER_TIME) < 12 THEN 1
      ELSE 0
    END) TICKET_IN_BREAKFAST,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 12 AND
        HOUR(od.BILLING_SERVER_TIME) < 15 THEN 1
      ELSE 0
    END) TICKET_IN_LUNCH,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 15 AND
        HOUR(od.BILLING_SERVER_TIME) < 20 THEN 1
      ELSE 0
    END) TICKET_IN_EVENING,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 20 AND
        HOUR(od.BILLING_SERVER_TIME) < 22 THEN 1
      ELSE 0
    END) TICKET_IN_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 22 AND
        HOUR(od.BILLING_SERVER_TIME) <= 23 THEN 1
      ELSE 0
    END) TICKET_IN_POST_DINNER,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        HOUR(od.BILLING_SERVER_TIME) >= 0 AND
        HOUR(od.BILLING_SERVER_TIME) < 6 THEN 1
      ELSE 0
    END) TICKET_IN_NIGHT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) TOTAL_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DELIVERY_MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE = 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DELIVERY_DISCOUNT,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_SPEND,
    MIN(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MINIMUM_APC,
    MAX(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TAXABLE_AMOUNT
      ELSE NULL
    END) DINE_IN_MAXIMUM_APC,
    SUM(CASE
      WHEN od.ORDER_STATUS <> 'CANCELLED' AND
        od.ORDER_SOURCE <> 'COD' AND
        od.TAXABLE_AMOUNT > 0 THEN od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT
      ELSE 0
    END) DINE_IN_DISCOUNT

  FROM KETTLE_DUMP.ORDER_DETAIL od
  WHERE od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -30 DAY)
  GROUP BY od.CUSTOMER_ID
;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c,
    (SELECT 
        CUSTOMER_ID,
            SUM(CASE
                WHEN NPS_SCORE = 1 THEN 1
                ELSE 0
            END) ONE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 2 THEN 1
                ELSE 0
            END) TWO_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 3 THEN 1
                ELSE 0
            END) THREE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 4 THEN 1
                ELSE 0
            END) FOUR_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 5 THEN 1
                ELSE 0
            END) FIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 6 THEN 1
                ELSE 0
            END) SIX_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 7 THEN 1
                ELSE 0
            END) SEVEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 8 THEN 1
                ELSE 0
            END) EIGHT_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 9 THEN 1
                ELSE 0
            END) NINE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE = 10 THEN 1
                ELSE 0
            END) TEN_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE < 7 THEN 1
                ELSE 0
            END) NEGATIVE_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE >= 7 AND NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) NEUTRAL_NPS_TICKET,
            SUM(CASE
                WHEN NPS_SCORE > 8 THEN 1
                ELSE 0
            END) POSITIVE_NPS_TICKET
    FROM
        KETTLE_DUMP.ORDER_NPS_DETAIL
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_NPS_TICKET = d.ONE_NPS_TICKET,
    c.TWO_NPS_TICKET = d.TWO_NPS_TICKET,
    c.THREE_NPS_TICKET = d.THREE_NPS_TICKET,
    c.FOUR_NPS_TICKET = d.FOUR_NPS_TICKET,
    c.FIVE_NPS_TICKET = d.FIVE_NPS_TICKET,
    c.SIX_NPS_TICKET = d.SIX_NPS_TICKET,
    c.SEVEN_NPS_TICKET = d.SEVEN_NPS_TICKET,
    c.EIGHT_NPS_TICKET = d.EIGHT_NPS_TICKET,
    c.NINE_NPS_TICKET = d.NINE_NPS_TICKET,
    c.TEN_NPS_TICKET = d.TEN_NPS_TICKET,
    c.POSITIVE_NPS_TICKET = d.POSITIVE_NPS_TICKET,
    c.NEGATIVE_NPS_TICKET = d.NEGATIVE_NPS_TICKET,
    c.NEUTRAL_NPS_TICKET = d.NEUTRAL_NPS_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID

;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c,
    (SELECT 
        od.CUSTOMER_ID,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN os.AMOUNT_PAID
                ELSE 0
            END) CASH_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1) THEN 1
                ELSE 0
            END) CASH_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN os.AMOUNT_PAID
                ELSE 0
            END) CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (2) THEN 1
                ELSE 0
            END) CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN os.AMOUNT_PAID
                ELSE 0
            END) AMEX_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (3) THEN 1
                ELSE 0
            END) AMEX_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN os.AMOUNT_PAID
                ELSE 0
            END) GIFT_CARD_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (10) THEN 1
                ELSE 0
            END) GIFT_CARD_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN os.AMOUNT_PAID
                ELSE 0
            END) PAYTM_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (11 , 13) THEN 1
                ELSE 0
            END) PAYTM_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN os.AMOUNT_PAID
                ELSE 0
            END) ONLINE_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (12) THEN 1
                ELSE 0
            END) ONLINE_PAYMENT_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) MOBIKWIK_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (14 , 15) THEN 1
                ELSE 0
            END) MOBIKWIK_TICKET,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID NOT IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN os.AMOUNT_PAID
                ELSE 0
            END) OTHER_PAYMENT_SPEND,
            SUM(CASE
                WHEN os.PAYMENT_MODE_ID IN (1 , 2, 3, 10, 11, 13, 12, 14, 15) THEN 1
                ELSE 0
            END) OTHER_PAYMENT_TICKET
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = os.ORDER_ID
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -30 DAY)
    GROUP BY od.CUSTOMER_ID) d 
SET 
    c.CASH_SPEND = d.CASH_SPEND,
    c.CASH_TICKET = d.CASH_TICKET,
    c.CARD_SPEND = d.CARD_SPEND,
    c.CARD_TICKET = d.CARD_TICKET,
    c.AMEX_SPEND = d.AMEX_SPEND,
    c.AMEX_TICKET = d.AMEX_TICKET,
    c.GIFT_CARD_SPEND = d.GIFT_CARD_SPEND,
    c.GIFT_CARD_TICKET = d.GIFT_CARD_TICKET,
    c.PAYTM_SPEND = d.PAYTM_SPEND,
    c.PAYTM_TICKET = d.PAYTM_TICKET,
    c.ONLINE_SPEND = d.ONLINE_SPEND,
    c.ONLINE_PAYMENT_TICKET = d.ONLINE_PAYMENT_TICKET,
    c.MOBIKWIK_SPEND = d.MOBIKWIK_SPEND,
    c.MOBIKWIK_TICKET = d.MOBIKWIK_TICKET,
    c.OTHER_PAYMENT_SPEND = d.OTHER_PAYMENT_SPEND,
    c.OTHER_PAYMENT_TICKET = d.OTHER_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c,
    (SELECT 
        CUSTOMER_ID,
            SUM(CASE
                WHEN FEEDBACK_RATING = 1 THEN 1
                ELSE 0
            END) ONE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 2 THEN 1
                ELSE 0
            END) TWO_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 3 THEN 1
                ELSE 0
            END) THREE_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 4 THEN 1
                ELSE 0
            END) FOUR_FEEDBACK_TICKET,
            SUM(CASE
                WHEN FEEDBACK_RATING = 5 THEN 1
                ELSE 0
            END) FIVE_FEEDBACK_TICKET
    FROM
        KETTLE_DUMP.ORDER_FEEDBACK_DETAIL
    WHERE
        FEEDBACK_STATUS = 'COMPLETED'
    GROUP BY CUSTOMER_ID) d 
SET 
    c.ONE_FEEDBACK_TICKET = d.ONE_FEEDBACK_TICKET,
    c.TWO_FEEDBACK_TICKET = d.TWO_FEEDBACK_TICKET,
    c.THREE_FEEDBACK_TICKET = d.THREE_FEEDBACK_TICKET,
    c.FOUR_FEEDBACK_TICKET = d.FOUR_FEEDBACK_TICKET,
    c.FIVE_FEEDBACK_TICKET = d.FIVE_FEEDBACK_TICKET
WHERE
    c.CUSTOMER_ID = d.CUSTOMER_ID

;
UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c,
    (SELECT 
        a.CUSTOMER_ID,
        SUM(CASE
                WHEN a.HOT_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_HOT,
            SUM(CASE
                WHEN a.VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_VEG,
            SUM(CASE
                WHEN a.NON_VEG_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_NON_VEG,
            SUM(CASE
                WHEN a.FOOD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_FOOD,
            SUM(CASE
                WHEN a.COLD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COLD,
            SUM(CASE
                WHEN a.BAKERY_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_BAKERY,
            SUM(CASE
                WHEN a.COMBO_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_COMBO,
            SUM(CASE
                WHEN a.MERCHANDISE_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_MERCHANDISE,
            SUM(CASE
                WHEN a.OTHERS_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_OTHER,
            SUM(CASE
                WHEN a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_GIFT_CARD,
            SUM(CASE
                WHEN a.HOT_QUANTITY = 0 AND 
                a.HOT_QUANTITY = 0 AND 
                a.FOOD_QUANTITY = 0 AND 
                a.COLD_QUANTITY = 0 AND 
                a.BAKERY_QUANTITY = 0 AND 
                a.COMBO_QUANTITY = 0 AND 
                a.MERCHANDISE_QUANTITY = 0 AND 
                a.OTHERS_QUANTITY = 0 AND a.GIFT_CARD_QUANTITY > 0 THEN 1
                ELSE 0
            END) TICKET_WITH_ONLY_GIFT_CARD
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 8 THEN oi.QUANTITY
                ELSE 0
            END) COMBO_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 9 THEN oi.QUANTITY
                ELSE 0
            END) MERCHANDISE_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 10 THEN oi.QUANTITY
                ELSE 0
            END) BAKERY_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 12
                        AND pd.TAX_CODE <> 'GIFT_CARD'
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHERS_QUANTITY,
            SUM(CASE
                WHEN pd.TAX_CODE = 'GIFT_CARD' THEN oi.QUANTITY
                ELSE 0
            END) GIFT_CARD_QUANTITY,
			SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 7 AND (pd.ATTRIBUTE IS NULL
                        OR pd.ATTRIBUTE = 'VEG')
                THEN
                    oi.QUANTITY
                ELSE 0
            END) VEG_QUANTITY,
            SUM(CASE
                WHEN  pd.PRODUCT_TYPE = 7 AND pd.ATTRIBUTE = 'NON_VEG' THEN oi.QUANTITY
                ELSE 0
            END) NON_VEG_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -30 DAY)
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a
    GROUP BY a.CUSTOMER_ID) a 
SET 
    c.TICKET_WITH_HOT = a.TICKET_WITH_HOT,
    c.TICKET_WITH_VEG = a.TICKET_WITH_VEG,
    c.TICKET_WITH_NON_VEG = a.TICKET_WITH_NON_VEG,
    c.TICKET_WITH_FOOD = a.TICKET_WITH_FOOD,
    c.TICKET_WITH_COLD = a.TICKET_WITH_COLD,
    c.TICKET_WITH_BAKERY = a.TICKET_WITH_BAKERY,
    c.TICKET_WITH_COMBO = a.TICKET_WITH_COMBO,
    c.TICKET_WITH_MERCHANDISE = a.TICKET_WITH_MERCHANDISE,
    c.TICKET_WITH_OTHER = a.TICKET_WITH_OTHER,
    c.TICKET_WITH_GIFT_CARD = a.TICKET_WITH_GIFT_CARD,
    c.ONLY_GIFT_CARD_TICKET =a.TICKET_WITH_ONLY_GIFT_CARD
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID;



UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c,
    (SELECT 
        m.CUSTOMER_ID,
            TRUNCATE(AVG(PEOPLE_PER_TICKET), 1) PEOPLE_PER_TICKET,
            MIN(PEOPLE_PER_TICKET) MINIMUM_PEOPLE_PER_ORDER,
            MAX(PEOPLE_PER_TICKET) MAXIMUM_PEOPLE_PER_ORDER
    FROM
        (SELECT 
        a.CUSTOMER_ID,
            a.ORDER_ID,
            CASE
                WHEN (a.HOT_QUANTITY + a.COLD_QUANTITY) > a.FOOD_QUANTITY THEN a.HOT_QUANTITY + a.COLD_QUANTITY
                ELSE a.FOOD_QUANTITY
            END PEOPLE_PER_TICKET
    FROM
        (SELECT 
        od.CUSTOMER_ID,
            od.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 5 THEN oi.QUANTITY
                ELSE 0
            END) HOT_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END) COLD_QUANTITY,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID = oi.ORDER_ID
            AND oi.PRODUCT_ID = pd.PRODUCT_ID
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -30 DAY)
    GROUP BY od.CUSTOMER_ID , od.ORDER_ID) a) m
    GROUP BY m.CUSTOMER_ID) a 
SET 
    c.PEOPLE_PER_TICKET = a.PEOPLE_PER_TICKET,
    c.MINIMUM_PEOPLE_PER_ORDER = a.MINIMUM_PEOPLE_PER_ORDER,
    c.MAXIMUM_PEOPLE_PER_ORDER = a.MAXIMUM_PEOPLE_PER_ORDER
WHERE
    a.CUSTOMER_ID = c.CUSTOMER_ID
;
UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c,
    (SELECT 
        CUSTOMER_ID, COUNT(*) SPLIT_PAYMENT_TICKET
    FROM
        (SELECT 
        od.ORDER_ID, od.CUSTOMER_ID, COUNT(*)
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_SETTLEMENT os
    WHERE
        od.ORDER_ID = os.ORDER_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.CUSTOMER_ID NOT IN (1,2,3,4,5,67456,142315)
  AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -30 DAY)
    GROUP BY od.ORDER_ID , od.CUSTOMER_ID
    HAVING COUNT(*) > 1) a
    GROUP BY CUSTOMER_ID) a 
SET 
    c.SPLIT_PAYMENT_TICKET = a.SPLIT_PAYMENT_TICKET
WHERE
    c.CUSTOMER_ID = a.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c,
    KETTLE_DUMP.ORDER_DETAIL o1,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u1,
    KETTLE_DUMP.ORDER_DETAIL o2,
    KETTLE_MASTER_DUMP.UNIT_DETAIL u2 
SET 
    c.FIRST_UNIT_ID = u1.UNIT_ID,
    c.FIRST_UNIT_NAME = u1.UNIT_NAME,
    c.LAST_UNIT_ID = u2.UNIT_ID,
    c.LAST_UNIT_NAME = u2.UNIT_NAME
WHERE
    c.FIRST_ORDER_ID = o1.ORDER_ID
        AND c.LAST_ORDER_ID = o2.ORDER_ID
        AND o1.UNIT_ID = u1.UNIT_ID
        AND o2.UNIT_ID = u2.UNIT_ID;
        
        
UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c,
    KETTLE_DUMP.ORDER_FEEDBACK_DETAIL o1 
SET 
    c.LAST_FEEDBACK_SCORE = o1.FEEDBACK_RATING
WHERE
    c.LAST_ORDER_ID = o1.ORDER_ID
        AND o1.FEEDBACK_STATUS = 'COMPLETED';

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS 
SET 
    TOTAL_APC = CASE
        WHEN
            COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(TOTAL_SPEND / (COALESCE(TICKET_COUNT, 0) - COALESCE(ZERO_AMOUNT_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DINE_IN_APC = CASE
        WHEN
            COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DINE_IN_SPEND / (COALESCE(DINE_IN_TICKET, 0) - COALESCE(ZERO_AMOUNT_DINE_IN_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END,
    DELIVERY_APC = CASE
        WHEN
            COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0) > 0
        THEN
            TRUNCATE(DELIVERY_SPEND / (COALESCE(DELIVERY_TICKET, 0) - COALESCE(ZERO_AMOUNT_DELIVERY_TICKET_COUNT, 0)),
                0)
        ELSE 0
    END;
update CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c, (select
    c.CUSTOMER_ID, MAX(c.LAST_ORDER_DATE) LAST_ORDER_DATE, MAX(o.BUSINESS_DATE) SECOND_LAST_BUSINESS_DATE, DATEDIFF(MAX(c.LAST_ORDER_DATE),MAX(o.BUSINESS_DATE)) DAY_GAP_SINCE_LAST_ORDER
FROM
    CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c1,
    CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c,
    KETTLE_DUMP.ORDER_DETAIL o
WHERE
    c.CUSTOMER_ID = o.CUSTOMER_ID
        AND o.ORDER_ID < c.LAST_ORDER_ID
        AND c1.CUSTOMER_ID = c.CUSTOMER_ID
GROUP BY c.CUSTOMER_ID
) a
SET c.DAY_GAP_SINCE_LAST_ORDER =  a.DAY_GAP_SINCE_LAST_ORDER
where c.CUSTOMER_ID = a.CUSTOMER_ID ;

update CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS
SET
ONE_NPS_TICKET =  case when ONE_NPS_TICKET IS NULL THEN 0 ELSE ONE_NPS_TICKET END,
TWO_NPS_TICKET =  case when TWO_NPS_TICKET IS NULL THEN 0 ELSE TWO_NPS_TICKET END,
THREE_NPS_TICKET =  case when THREE_NPS_TICKET IS NULL THEN 0 ELSE THREE_NPS_TICKET END,
FOUR_NPS_TICKET =  case when FOUR_NPS_TICKET IS NULL THEN 0 ELSE FOUR_NPS_TICKET END,
FIVE_NPS_TICKET =  case when FIVE_NPS_TICKET IS NULL THEN 0 ELSE FIVE_NPS_TICKET END,
SIX_NPS_TICKET =  case when SIX_NPS_TICKET IS NULL THEN 0 ELSE SIX_NPS_TICKET END,
SEVEN_NPS_TICKET =  case when SEVEN_NPS_TICKET IS NULL THEN 0 ELSE SEVEN_NPS_TICKET END,
EIGHT_NPS_TICKET =  case when EIGHT_NPS_TICKET IS NULL THEN 0 ELSE EIGHT_NPS_TICKET END,
NINE_NPS_TICKET =  case when NINE_NPS_TICKET IS NULL THEN 0 ELSE NINE_NPS_TICKET END,
TEN_NPS_TICKET =  case when TEN_NPS_TICKET IS NULL THEN 0 ELSE TEN_NPS_TICKET END,
LAST_NPS_SCORE =  case when LAST_NPS_SCORE IS NULL THEN 0 ELSE LAST_NPS_SCORE END,
NEGATIVE_NPS_TICKET =  case when NEGATIVE_NPS_TICKET IS NULL THEN 0 ELSE NEGATIVE_NPS_TICKET END,
POSITIVE_NPS_TICKET =  case when POSITIVE_NPS_TICKET IS NULL THEN 0 ELSE POSITIVE_NPS_TICKET END,
NEUTRAL_NPS_TICKET =  case when NEUTRAL_NPS_TICKET IS NULL THEN 0 ELSE NEUTRAL_NPS_TICKET END,
SPLIT_PAYMENT_TICKET =  case when SPLIT_PAYMENT_TICKET IS NULL THEN 0 ELSE SPLIT_PAYMENT_TICKET END,
DINE_IN_MINIMUM_APC =  case when DINE_IN_MINIMUM_APC IS NULL THEN 0 ELSE DINE_IN_MINIMUM_APC END,
DINE_IN_MAXIMUM_APC =  case when DINE_IN_MAXIMUM_APC IS NULL THEN 0 ELSE DINE_IN_MAXIMUM_APC END,
CASH_TICKET =  case when CASH_TICKET IS NULL THEN 0 ELSE CASH_TICKET END,
CARD_TICKET =  case when CARD_TICKET IS NULL THEN 0 ELSE CARD_TICKET END,
AMEX_TICKET =  case when AMEX_TICKET IS NULL THEN 0 ELSE AMEX_TICKET END,
PAYTM_TICKET =  case when PAYTM_TICKET IS NULL THEN 0 ELSE PAYTM_TICKET END,
GIFT_CARD_TICKET =  case when GIFT_CARD_TICKET IS NULL THEN 0 ELSE GIFT_CARD_TICKET END,
ONLY_GIFT_CARD_TICKET =  case when ONLY_GIFT_CARD_TICKET IS NULL THEN 0 ELSE ONLY_GIFT_CARD_TICKET END,
MOBIKWIK_TICKET =  case when MOBIKWIK_TICKET IS NULL THEN 0 ELSE MOBIKWIK_TICKET END,
ONLINE_PAYMENT_TICKET =  case when ONLINE_PAYMENT_TICKET IS NULL THEN 0 ELSE ONLINE_PAYMENT_TICKET END,
OTHER_PAYMENT_TICKET =  case when OTHER_PAYMENT_TICKET IS NULL THEN 0 ELSE OTHER_PAYMENT_TICKET END,
CASH_SPEND =  case when CASH_SPEND IS NULL THEN 0 ELSE CASH_SPEND END,
CARD_SPEND =  case when CARD_SPEND IS NULL THEN 0 ELSE CARD_SPEND END,
AMEX_SPEND =  case when AMEX_SPEND IS NULL THEN 0 ELSE AMEX_SPEND END,
PAYTM_SPEND =  case when PAYTM_SPEND IS NULL THEN 0 ELSE PAYTM_SPEND END,
GIFT_CARD_SPEND =  case when GIFT_CARD_SPEND IS NULL THEN 0 ELSE GIFT_CARD_SPEND END,
MOBIKWIK_SPEND =  case when MOBIKWIK_SPEND IS NULL THEN 0 ELSE MOBIKWIK_SPEND END,
ONLINE_SPEND =  case when ONLINE_SPEND IS NULL THEN 0 ELSE ONLINE_SPEND END,
OTHER_PAYMENT_SPEND =  case when OTHER_PAYMENT_SPEND IS NULL THEN 0 ELSE OTHER_PAYMENT_SPEND END,
ONE_FEEDBACK_TICKET =  case when ONE_FEEDBACK_TICKET IS NULL THEN 0 ELSE ONE_FEEDBACK_TICKET END,
TWO_FEEDBACK_TICKET =  case when TWO_FEEDBACK_TICKET IS NULL THEN 0 ELSE TWO_FEEDBACK_TICKET END,
THREE_FEEDBACK_TICKET =  case when THREE_FEEDBACK_TICKET IS NULL THEN 0 ELSE THREE_FEEDBACK_TICKET END,
FOUR_FEEDBACK_TICKET =  case when FOUR_FEEDBACK_TICKET IS NULL THEN 0 ELSE FOUR_FEEDBACK_TICKET END,
FIVE_FEEDBACK_TICKET =  case when FIVE_FEEDBACK_TICKET IS NULL THEN 0 ELSE FIVE_FEEDBACK_TICKET END,
MINIMUM_APC =  case when MINIMUM_APC IS NULL THEN 0 ELSE MINIMUM_APC END,
MAXIMUM_APC =  case when MAXIMUM_APC IS NULL THEN 0 ELSE MAXIMUM_APC END,
DELIVERY_SPEND =  case when DELIVERY_SPEND IS NULL THEN 0 ELSE DELIVERY_SPEND END,
DELIVERY_DISCOUNT =  case when DELIVERY_DISCOUNT IS NULL THEN 0 ELSE DELIVERY_DISCOUNT END,
DELIVERY_MINIMUM_APC =  case when DELIVERY_MINIMUM_APC IS NULL THEN 0 ELSE DELIVERY_MINIMUM_APC END,
DELIVERY_MAXIMUM_APC =  case when DELIVERY_MAXIMUM_APC IS NULL THEN 0 ELSE DELIVERY_MAXIMUM_APC END


;
ALTER TABLE CLM_ANALYTICS.CUSTOMER_DATA_NEW
ADD COLUMN UNIQUE_VISIT_DAYS INTEGER NULL;
ALTER TABLE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS
ADD COLUMN UNIQUE_VISIT_DAYS INTEGER NULL;
ALTER TABLE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS
ADD COLUMN UNIQUE_VISIT_DAYS INTEGER NULL;
ALTER TABLE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS
ADD COLUMN UNIQUE_VISIT_DAYS INTEGER NULL;
ALTER TABLE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS
ADD COLUMN UNIQUE_VISIT_DAYS INTEGER NULL;

UPDATE CLM_ANALYTICS.CUSTOMER_DATA_NEW c,
    (SELECT 
        od.CUSTOMER_ID,
            COUNT(DISTINCT od.BUSINESS_DATE) UNIQUE_VISIT_DAYS
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.CUSTOMER_ID > 5
            AND od.BUSINESS_DATE <= '2018-01-22'
    GROUP BY od.CUSTOMER_ID) a 
SET 
    c.UNIQUE_VISIT_DAYS = a.UNIQUE_VISIT_DAYS
    where c.CUSTOMER_ID = a.CUSTOMER_ID;

UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS c,
    (SELECT 
        od.CUSTOMER_ID,
            COUNT(DISTINCT od.BUSINESS_DATE) UNIQUE_VISIT_DAYS
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.CUSTOMER_ID > 5
             AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -90 DAY)
            AND od.BUSINESS_DATE <= '2018-01-22'
    GROUP BY od.CUSTOMER_ID) a 
SET 
    c.UNIQUE_VISIT_DAYS = a.UNIQUE_VISIT_DAYS
where c.CUSTOMER_ID = a.CUSTOMER_ID;

    
UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS c,
    (SELECT 
        od.CUSTOMER_ID,
            COUNT(DISTINCT od.BUSINESS_DATE) UNIQUE_VISIT_DAYS
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.CUSTOMER_ID > 5
             AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -30 DAY)
            AND od.BUSINESS_DATE <= '2018-01-22'
    GROUP BY od.CUSTOMER_ID) a 
SET 
    c.UNIQUE_VISIT_DAYS = a.UNIQUE_VISIT_DAYS
        where c.CUSTOMER_ID = a.CUSTOMER_ID;


UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS c,
    (SELECT 
        od.CUSTOMER_ID,
            COUNT(DISTINCT od.BUSINESS_DATE) UNIQUE_VISIT_DAYS
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.CUSTOMER_ID > 5
             AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -15 DAY)
            AND od.BUSINESS_DATE <= '2018-01-22'
    GROUP BY od.CUSTOMER_ID) a 
SET 
    c.UNIQUE_VISIT_DAYS = a.UNIQUE_VISIT_DAYS
    where c.CUSTOMER_ID = a.CUSTOMER_ID;



UPDATE CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS c,
    (SELECT 
        od.CUSTOMER_ID,
            COUNT(DISTINCT od.BUSINESS_DATE) UNIQUE_VISIT_DAYS
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.CUSTOMER_ID > 5
             AND od.BUSINESS_DATE >= DATE_ADD(CURRENT_DATE, INTERVAL -7 DAY)
            AND od.BUSINESS_DATE <= '2018-01-22'
    GROUP BY od.CUSTOMER_ID) a 
SET 
    c.UNIQUE_VISIT_DAYS = a.UNIQUE_VISIT_DAYS 
    where c.CUSTOMER_ID = a.CUSTOMER_ID;


