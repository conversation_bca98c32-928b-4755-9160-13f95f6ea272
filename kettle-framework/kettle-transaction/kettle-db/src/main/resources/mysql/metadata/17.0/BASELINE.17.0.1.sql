ALTER TABLE KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA
ADD COLUMN CALCULATED_ORDER_SOURCE VARCHAR(50) NULL;

ALTER TABLE `KETTLE_SCM_DEV`.`ESTIMATE_REQUEST_DATA`
CHANGE COLUMN `NET_SALES` `NET_SALES` DECIMAL(10,3) NOT NULL ,
CHANGE COLUMN `TOTAL_SALE` `TOTAL_SALE` DECIMAL(10,3) NOT NULL ;


ALTER TABLE KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST
ADD COLUMN DINE_IN_SALE DECIMAL(10,3) NULL,
ADD COLUMN DELIVERY_SALE DECIMAL(10,3) NULL;

ALTER TABLE `KETTLE_SCM_DEV`.`ESTIMATE_QUERY_REQUEST`
CHANGE COLUMN `TARGET_SALE` `TARGET_SALE` DECIMAL(10,3) NOT NULL ;



ALTER TABLE `KETTLE_DEV`.`DAY_CLOSE_ESTIMATE_DATA`
CHANGE COLUMN `NET_SALES` `NET_SALES` DECIMAL(10,3) NOT NULL ;

CREATE TABLE KETTLE_SCM_DEV.DAY_CLOSE_SALES_DATA(
ID INTEGER AUTO_INCREMENT PRIMARY KEY NOT NULL,
UNIT_ID INTEGER NOT NULL,
BRAND_ID INTEGER NOT NULL,
BUSINESS_DATE DATE NOT NULL,
NET_SALES DECIMAL(10,3)  NULL,
SALE_TYPE VARCHAR(50)  NULL,
SALES_PERCENTAGE DECIMAL(10,3) NULL
);




DROP  PROCEDURE  IF EXISTS KETTLE_DEV.`DAY_CLOSE_ESTIMATE_DATA_PROC`;

DELIMITER $$
CREATE  PROCEDURE KETTLE_DEV.`DAY_CLOSE_ESTIMATE_DATA_PROC`(IN IN_UNIT_ID INTEGER, IN IN_BIZ_DATE DATE, IN_MAX_TAXABLE_AMOUNT INTEGER)
BEGIN
DECLARE TOTAL_NET_SALES INT DEFAULT 0;
DELETE FROM KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA
WHERE
    UNIT_ID = IN_UNIT_ID
    AND BUSINESS_DATE = IN_BIZ_DATE;
# inserting
INSERT INTO KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA (
UNIT_ID ,
BRAND_ID ,
PRODUCT_ID ,
DIMENSION ,
QUANTITY ,
BUSINESS_DATE ,
DAY_OF_WEEK ,
UPT ,
NET_SALES ,
GMV ,
DATA_TYPE,
CALCULATED_ORDER_SOURCE
)

SELECT m.UNIT_ID,m.BRAND_ID,m.PRODUCT_ID,m.DIMENSION,SUM(m.QUANTITY),m.BUSINESS_DATE,DAYOFWEEK(m.BUSINESS_DATE),NULL,SUM(m.NET_SALE),SUM(m.GMV),m.DATA_TYPE,m.CALCULATED_ORDER_SOURCE
FROM(
SELECT
	od.BRAND_ID,
	od.BUSINESS_DATE,
    od.UNIT_ID,DAYOFWEEK(od.BUSINESS_DATE) AS DAYOFWEEK,
    oi.PRODUCT_ID,
    SUM(oi.QUANTITY) AS QUANTITY,
	oi.DIMENSION,
    CASE WHEN od.ORDER_SOURCE='COD' THEN 'DELIVERY' ELSE 'DINE_IN' END CALCULATED_ORDER_SOURCE,
    SUM(oi.AMOUNT_PAID) AS NET_SALE,
    SUM(oi.PRICE * oi.QUANTITY) AS GMV,
    'CALCULATED' DATA_TYPE
    FROM KETTLE_DEV.ORDER_DETAIL od
INNER JOIN KETTLE_DEV.ORDER_ITEM oi ON od.ORDER_ID=oi.ORDER_ID
WHERE od.BUSINESS_DATE = IN_BIZ_DATE
	AND od.UNIT_ID=IN_UNIT_ID
    -- AND od.BRAND_ID=1
    AND oi.COMBO_CONSTITUENT<> 'Y'
    AND oi.TAX_CODE NOT IN ( 'COMBO', 'GIFT_CARD')
    AND od.ORDER_STATUS <> 'CANCELLED'
	AND od.SETTLED_AMOUNT < IN_MAX_TAXABLE_AMOUNT
	AND oi.PARENT_ITEM_ID IS NULL
    AND od.IS_GIFT_CARD_ORDER <>"Y"
GROUP BY oi.PRODUCT_ID,oi.DIMENSION,od.BRAND_ID,od.ORDER_SOURCE

UNION ALL

SELECT
	od.BRAND_ID,
	od.BUSINESS_DATE,
    od.UNIT_ID,DAYOFWEEK(od.BUSINESS_DATE) AS DAYOFWEEK,
    b.PRODUCT_ID,
    SUM(b.QUANTITY) AS QUANTITY,
    b.DIMENSION,
    CASE WHEN od.ORDER_SOURCE='COD' THEN 'DELIVERY' ELSE 'DINE_IN' END CALCULATED_ORDER_SOURCE,
    SUM((b.PRICE * b.QUANTITY) / a.TOTAL_AMOUNT * a.AMOUNT_PAID) AS NET_SALE,
    SUM(b.PRICE * b.QUANTITY) GMV,
    'CALCULATED' DATA_TYPE


FROM
    (SELECT
        a.PARENT_ITEM_ID, a.TOTAL_AMOUNT, b.AMOUNT_PAID
    FROM
        (SELECT
        oi.PARENT_ITEM_ID PARENT_ITEM_ID,
            SUM(oi.QUANTITY * oi.PRICE) TOTAL_AMOUNT
    FROM
        KETTLE_DEV.ORDER_DETAIL od, KETTLE_DEV.ORDER_ITEM oi
    WHERE
        od.ORDER_ID = oi.ORDER_ID
            AND od.BUSINESS_DATE = IN_BIZ_DATE
            AND od.UNIT_ID = IN_UNIT_ID
            AND oi.PARENT_ITEM_ID IS NOT NULL
    GROUP BY oi.PARENT_ITEM_ID) a, KETTLE_DEV.ORDER_ITEM b
    WHERE
        a.PARENT_ITEM_ID = b.ORDER_ITEM_ID) a,
    KETTLE_DEV.ORDER_ITEM b,
    KETTLE_DEV.ORDER_DETAIL od
WHERE
    a.PARENT_ITEM_ID = b.PARENT_ITEM_ID
    and b.ORDER_ID = od.ORDER_ID
    GROUP BY b.PRODUCT_ID,b.DIMENSION,od.BRAND_ID,od.ORDER_SOURCE)m
GROUP BY m.PRODUCT_ID,m.DIMENSION,m.BRAND_ID,m.CALCULATED_ORDER_SOURCE;



SELECT
    SUM(NET_SALES)
INTO TOTAL_NET_SALES FROM
    KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA
WHERE
    UNIT_ID = IN_UNIT_ID
        AND BUSINESS_DATE = IN_BIZ_DATE;

UPDATE KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA
SET
    UPT = QUANTITY * 1000 / TOTAL_NET_SALES
WHERE
    UNIT_ID = IN_UNIT_ID
        AND BUSINESS_DATE = IN_BIZ_DATE;


      CALL DAY_CLOSE_NET_SALES(IN_UNIT_ID , IN_BIZ_DATE) ;

END$$
DELIMITER ;









USE `KETTLE_SCM_DEV`;
DROP PROCEDURE IF EXISTS `PROC_ESTIMATE_REQUEST_DATA`;

DELIMITER $$
USE `KETTLE_SCM_DEV`$$
CREATE PROCEDURE `PROC_ESTIMATE_REQUEST_DATA`(IN_BRAND_ID INTEGER,IN_UNIT_ID INTEGER,IN_TARGET_DATE DATE,IN_TARGET_SALE INTEGER,IN_DINEIN_SALE DECIMAL,IN_DELIVERY_SALE DECIMAL,IN_CALCULATION_DATES VARCHAR(500),IN_CATEGORY_BUFFER INTEGER, IN_BUFFER INTEGER,IN_PRODUCT_IDS varchar(500),IN IN_REQUEST_ID INTEGER)
BEGIN
DECLARE TOTAL_NET_SALES INT DEFAULT 0;
DECLARE EXTRA_TARGET_SALES INT DEFAULT 0;
DECLARE UPDATED_QUANTITY INT DEFAULT 0;
DECLARE UPDATED_SALES INT DEFAULT 0;
DECLARE PRICE INT DEFAULT 0;
DECLARE UNIQUE_IDENTIFIER INT DEFAULT 0;
DECLARE COUNTER INT DEFAULT 0;
DECLARE MAX_COUNTER INT DEFAULT 0;
DECLARE INCREMENT INT DEFAULT 0;




INSERT INTO KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(
REQUEST_ID,
BUSINESS_DATE,
DAY_OF_WEEK,
UNIT_ID,
BRAND_ID,
PRODUCT_ID,
DIMENSION,
AVG_UPT,
NET_SALES,
AVG_PRICE,
TOTAL_SALE,
SUGGESTED_QUANTITY,
CATEGORY_BUFFER_SUGGESTED_QUANTITY,
ROUND_SUGST_QUANTITY,
SUGGESTED_SALES,
DATA_TYPE,
QUANTITY_INCREMENT
)
SELECT
      IN_REQUEST_ID,
     IN_TARGET_DATE,DAYOFWEEK(IN_TARGET_DATE),
      m.UNIT_ID,m.BRAND_ID,m.PRODUCT_ID,m.DIMENSION, AVG(m.AVG_UPT),SUM(m.NET_SALES),AVG(m.AVG_PRICE),IN_TARGET_SALE ,SUM(m.SGST_QTY),NULL,NULL,NULL,'CALCULATED',0
FROM(
SELECT
	DC.BRAND_ID,
	DC.BUSINESS_DATE,
    DC.UNIT_ID,
    DC.PRODUCT_ID,
    SUM(DC.NET_SALES) AS NET_SALES,
    SUM(DC.QUANTITY) AS TOTAL_QUANTITY,
	DC.DIMENSION,
    SUM(DC.NET_SALES)/SUM(DC.QUANTITY) AS AVG_PRICE,
    IN_DELIVERY_SALE*AVG(DC.UPT)/1000 AS SGST_QTY,
    AVG(DC.UPT) AS AVG_UPT,
    'CALCULATED' DATA_TYPE
    FROM KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA DC, (SELECT
    upm.UNIT_ID, upm.PRODUCT_ID, rl.RL_CODE DIMENSION
FROM
    KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING upm,
    KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING upp,
    KETTLE_MASTER_DEV.REF_LOOKUP rl
WHERE
    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
    AND upp.DIMENSION_CODE = rl.RL_ID
        AND upm.UNIT_ID = IN_UNIT_ID
        AND upm.PRODUCT_STATUS = 'ACTIVE'
        AND upp.PRICING_STATUS = 'ACTIVE') AP
          WHERE FIND_IN_SET(DC.BUSINESS_DATE ,IN_CALCULATION_DATES)>0
               AND DC.UNIT_ID=IN_UNIT_ID
               AND DC.BRAND_ID=IN_BRAND_ID
               AND DC.UNIT_ID =IN_UNIT_ID
               AND AP.PRODUCT_ID = DC.PRODUCT_ID
               and AP.DIMENSION = DC.DIMENSION
               AND DC.CALCULATED_ORDER_SOURCE='DELIVERY'
    GROUP BY DC.PRODUCT_ID,DC.DIMENSION,DC.BRAND_ID

    UNION ALL

    SELECT
	DC.BRAND_ID,
	DC.BUSINESS_DATE,
    DC.UNIT_ID,
    DC.PRODUCT_ID,
    SUM(DC.NET_SALES) AS NET_SALES,
    SUM(DC.QUANTITY) AS TOTAL_QUANTITY,
	DC.DIMENSION,
    SUM(DC.NET_SALES)/SUM(DC.QUANTITY) AS AVG_PRICE,
    IN_DINEIN_SALE*AVG(DC.UPT)/1000 AS SGST_QTY,
    AVG(DC.UPT) AS AVG_UPT,
    'CALCULATED' DATA_TYPE
    FROM KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA DC, (SELECT
    upm.UNIT_ID, upm.PRODUCT_ID, rl.RL_CODE DIMENSION
FROM
    KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING upm,
    KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING upp,
    KETTLE_MASTER_DEV.REF_LOOKUP rl
WHERE
    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
    AND upp.DIMENSION_CODE = rl.RL_ID
        AND upm.UNIT_ID = IN_UNIT_ID
        AND upm.PRODUCT_STATUS = 'ACTIVE'
        AND upp.PRICING_STATUS = 'ACTIVE') AP
          WHERE FIND_IN_SET(DC.BUSINESS_DATE ,IN_CALCULATION_DATES)>0
               AND DC.UNIT_ID=IN_UNIT_ID
               AND DC.BRAND_ID=IN_BRAND_ID
               AND DC.UNIT_ID =IN_UNIT_ID
               AND AP.PRODUCT_ID = DC.PRODUCT_ID
               and AP.DIMENSION = DC.DIMENSION
               AND DC.CALCULATED_ORDER_SOURCE='DINE_IN'
    GROUP BY DC.PRODUCT_ID,DC.DIMENSION,DC.BRAND_ID
)m
    GROUP BY m.PRODUCT_ID,m.DIMENSION,m.BRAND_ID;




UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
SET
    ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY = ER.SUGGESTED_QUANTITY + (IN_CATEGORY_BUFFER * ER.SUGGESTED_QUANTITY) / 100,
    ER.CATEGORY_BUFFER_APPLIED = 'Y'
WHERE
    FIND_IN_SET(ER.PRODUCT_ID,
            IN_PRODUCT_IDS) > 0
        AND UNIT_ID = IN_UNIT_ID
        AND BUSINESS_DATE = IN_TARGET_DATE
        AND ER.REQUEST_ID = IN_REQUEST_ID
        AND ER.BRAND_ID = IN_BRAND_ID;

    UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
SET
    ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY = ER.SUGGESTED_QUANTITY,
    ER.CATEGORY_BUFFER_APPLIED = 'N'
WHERE
   ER.UNIT_ID = IN_UNIT_ID
        AND BUSINESS_DATE = IN_TARGET_DATE
        AND ER.REQUEST_ID = IN_REQUEST_ID
        AND ER.CATEGORY_BUFFER_APPLIED IS NULL
        AND ER.BRAND_ID = IN_BRAND_ID;

UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
SET
    ER.ROUND_SUGST_QUANTITY = CEILING(ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY),
    ER.SUGGESTED_SALES = ER.AVG_PRICE * CEILING(ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY)
WHERE
    ER.UNIT_ID = IN_UNIT_ID
        AND ER.BUSINESS_DATE = IN_TARGET_DATE
        AND ER.REQUEST_ID = IN_REQUEST_ID
        AND ER.BRAND_ID = IN_BRAND_ID;



SELECT SUM(SUGGESTED_SALES)
INTO TOTAL_NET_SALES
FROM KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA  ER
WHERE ER.UNIT_ID = IN_UNIT_ID AND ER.BUSINESS_DATE =IN_TARGET_DATE AND ER.REQUEST_ID=IN_REQUEST_ID AND ER.BRAND_ID = IN_BRAND_ID;

SET EXTRA_TARGET_SALES=IN_TARGET_SALE+ (IN_BUFFER* IN_TARGET_SALE)/100;

UPDATE KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST
SET SUGGESTED_SALE=EXTRA_TARGET_SALES
WHERE UNIT_ID = IN_UNIT_ID
  AND BRAND_ID = IN_BRAND_ID
  AND STATUS = 'ACTIVE'
  AND TARGET_DATE = IN_TARGET_DATE;

SELECT COUNT(*)
INTO MAX_COUNTER
FROM KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA  ER
WHERE ER.UNIT_ID = IN_UNIT_ID AND ER.BUSINESS_DATE =IN_TARGET_DATE AND ER.REQUEST_ID=IN_REQUEST_ID AND ER.BRAND_ID = IN_BRAND_ID;


        WHILE TOTAL_NET_SALES < EXTRA_TARGET_SALES DO

            SELECT ER.ROUND_SUGST_QUANTITY,ER.SUGGESTED_SALES,ER.AVG_PRICE,ER.ID,ER.QUANTITY_INCREMENT
            INTO UPDATED_QUANTITY,UPDATED_SALES,PRICE,UNIQUE_IDENTIFIER,INCREMENT
            FROM KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA  ER
            WHERE ER.UNIT_ID =IN_UNIT_ID AND ER.BUSINESS_DATE= IN_TARGET_DATE AND ER.REQUEST_ID=IN_REQUEST_ID AND ER.BRAND_ID = IN_BRAND_ID
            ORDER BY ER.ROUND_SUGST_QUANTITY DESC LIMIT COUNTER,1;
            SET COUNTER=COUNTER+1;
            SET UPDATED_QUANTITY= UPDATED_QUANTITY+1;
            SET UPDATED_SALES= UPDATED_SALES+PRICE;
            SET TOTAL_NET_SALES= TOTAL_NET_SALES+PRICE;

            UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA  ER
            SET ER.QUANTITY_INCREMENT= INCREMENT+1,
            ER.ROUND_SUGST_QUANTITY=UPDATED_QUANTITY,
            ER.SUGGESTED_SALES=UPDATED_SALES
            WHERE ER.ID=UNIQUE_IDENTIFIER;

            IF COUNTER=MAX_COUNTER THEN
              SET COUNTER=0;
            END IF;
            END WHILE;

SELECT
A.PRODUCT_ID,
A.DIMENSION,
A.CATEGORY_ID,
A.CATEGORY,
COALESCE (B.ROUND_SUGST_QUANTITY,0) QUANTITY,
A.UNIT_ID,
COALESCE (B.SUGGESTED_SALES,0) SUGGESTED_SALES
 FROM (
        SELECT
    UPM.UNIT_ID, UPM.PRODUCT_ID, RL.RL_CODE DIMENSION,RTL.RTL_NAME CATEGORY,RTL.RTL_ID CATEGORY_ID
FROM
    KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING UPM,
    KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING UPP,
    KETTLE_MASTER_DEV.REF_LOOKUP RL,
    KETTLE_MASTER_DEV.PRODUCT_DETAIL PD,
    KETTLE_MASTER_DEV.REF_LOOKUP_TYPE RTL

WHERE
    UPM.UNIT_PROD_REF_ID = UPP.UNIT_PROD_REF_ID
    AND UPP.DIMENSION_CODE = RL.RL_ID
    AND UPM.PRODUCT_ID = PD.PRODUCT_ID AND PD.PRODUCT_TYPE = RTL.RTL_ID
        AND UPM.UNIT_ID = IN_UNIT_ID
        AND UPM.PRODUCT_STATUS = 'ACTIVE'
        AND UPP.PRICING_STATUS = 'ACTIVE') A LEFT OUTER JOIN ( SELECT
    *
FROM
    KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
WHERE
        ER.UNIT_ID = IN_UNIT_ID
        AND ER.REQUEST_ID = IN_REQUEST_ID)B
        ON A.UNIT_ID = B.UNIT_ID
        AND A.PRODUCT_ID = B.PRODUCT_ID
        AND B.BRAND_ID=IN_BRAND_ID
        AND A.DIMENSION = B.DIMENSION
        ORDER BY A.CATEGORY,A.PRODUCT_ID;
        END$$

DELIMITER ;



DROP PROCEDURE IF EXISTS KETTLE_DEV.`DAY_CLOSE_NET_SALES`;
DELIMITER $$
CREATE  PROCEDURE KETTLE_DEV.`DAY_CLOSE_NET_SALES`(IN IN_UNIT_ID INTEGER, IN IN_BIZ_DATE DATE)
BEGIN

DELETE FROM KETTLE_SCM_DEV.DAY_CLOSE_SALES_DATA
WHERE
    UNIT_ID = IN_UNIT_ID
    AND BUSINESS_DATE = IN_BIZ_DATE;

-- SAVING ALL TOTAL SALE (INCLUDING BOTH DINE IN AND DELIVERY)
INSERT INTO KETTLE_SCM_DEV.DAY_CLOSE_SALES_DATA (
UNIT_ID ,
BRAND_ID,
BUSINESS_DATE,
NET_SALES,
SALE_TYPE,
SALES_PERCENTAGE
)
 SELECT DC.UNIT_ID,DC.BRAND_ID,DC.BUSINESS_DATE,SUM(DC.NET_SALES),'TOTAL_SALE',100
FROM KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA DC
WHERE  DC.UNIT_ID=IN_UNIT_ID
AND DC.BUSINESS_DATE=IN_BIZ_DATE
GROUP BY DC.BUSINESS_DATE,DC.UNIT_ID,DC.BRAND_ID;



-- SAVING DELIVERY SALE
  INSERT INTO KETTLE_SCM_DEV.DAY_CLOSE_SALES_DATA (
UNIT_ID ,
BRAND_ID,
BUSINESS_DATE,
NET_SALES,
SALE_TYPE
)
 SELECT DC.UNIT_ID,DC.BRAND_ID,DC.BUSINESS_DATE,SUM(DC.NET_SALES),DC.CALCULATED_ORDER_SOURCE
FROM KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA DC
WHERE  DC.UNIT_ID=IN_UNIT_ID
AND DC.BUSINESS_DATE=IN_BIZ_DATE
AND CALCULATED_ORDER_SOURCE='DELIVERY'
GROUP BY DC.BUSINESS_DATE,DC.UNIT_ID,DC.BRAND_ID;

-- SAVING DINE IN SALE
INSERT INTO KETTLE_SCM_DEV.DAY_CLOSE_SALES_DATA (
UNIT_ID ,
BRAND_ID,
BUSINESS_DATE,
NET_SALES,
SALE_TYPE
)
 SELECT DC.UNIT_ID,DC.BRAND_ID,DC.BUSINESS_DATE,SUM(DC.NET_SALES),DC.CALCULATED_ORDER_SOURCE
FROM KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA DC
WHERE  DC.UNIT_ID=IN_UNIT_ID
AND DC.BUSINESS_DATE=IN_BIZ_DATE
AND CALCULATED_ORDER_SOURCE='DINE_IN'
GROUP BY DC.BUSINESS_DATE,DC.UNIT_ID,DC.BRAND_ID;


 UPDATE KETTLE_SCM_DEV.DAY_CLOSE_SALES_DATA DA
INNER JOIN  KETTLE_SCM_DEV.DAY_CLOSE_SALES_DATA DC
ON DC.BRAND_ID=DA.BRAND_ID AND DC.UNIT_ID=IN_UNIT_ID AND DC.BUSINESS_DATE=IN_BIZ_DATE AND DC.SALE_TYPE ='TOTAL_SALE'
SET DA.SALES_PERCENTAGE=DA.NET_SALES/DC.NET_SALES*100
WHERE DA.UNIT_ID=IN_UNIT_ID AND DA.BUSINESS_DATE=IN_BIZ_DATE AND DA.SALE_TYPE <>'TOTAL_SALE';

END$$
DELIMITER ;



USE `KETTLE_SCM_DEV`;
DROP procedure IF EXISTS `ESTIMATE_QUERY_REQUEST_PROC`;

DELIMITER $$
USE `KETTLE_SCM_DEV`$$
CREATE PROCEDURE `ESTIMATE_QUERY_REQUEST_PROC`(IN_BRAND_ID INTEGER, IN_UNIT_ID INTEGER, IN_TARGET_DATE DATE,
                                               IN_TARGET_SALE INTEGER, IN_CALCULATION_DATES VARCHAR (500),
                                               IN_ORDERING_TYPE VARCHAR (500), IN_GENERATION_DATE DATE,IN_DINE_IN_SALE DECIMAL, IN_DELIVERY_SALE DECIMAL)
BEGIN
UPDATE KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST SET STATUS = 'IN_ACTIVE'
WHERE UNIT_ID = IN_UNIT_ID  AND BRAND_ID = IN_BRAND_ID
  AND TARGET_DATE = IN_TARGET_DATE;

INSERT INTO KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST (UNIT_ID,DAY_OF_WEEK,TARGET_DATE,BRAND_ID,
                                                   TARGET_SALE,CALCULATION_DATES,STATUS,ORDERING_TYPE,GENERATION_DATE,DINE_IN_SALE,
DELIVERY_SALE)
VALUES (IN_UNIT_ID, DAYOFWEEK(IN_TARGET_DATE), IN_TARGET_DATE, IN_BRAND_ID, IN_TARGET_SALE, IN_CALCULATION_DATES,
        'ACTIVE', IN_ORDERING_TYPE, IN_GENERATION_DATE,IN_DINE_IN_SALE,IN_DELIVERY_SALE);

SELECT
    ID
FROM
    KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST EQ
WHERE
    EQ.UNIT_ID = IN_UNIT_ID
        AND EQ.BRAND_ID = IN_BRAND_ID
        AND EQ.TARGET_DATE = IN_TARGET_DATE
        AND EQ.STATUS = 'ACTIVE';

END$$

DELIMITER ;




