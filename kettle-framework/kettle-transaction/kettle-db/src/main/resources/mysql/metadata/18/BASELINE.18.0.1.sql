CREATE TABLE `KETTLE_DEV`.`WEB_OFFER_DETAIL` (
`WEB_OFFER_DETAIL_ID` INT(11) NOT NULL AUTO_INCREMENT,
`OFFER_TYPE` VARCHAR(100) NOT NULL,
`CUSTOMER_ID` INT(11) NOT NULL,
`TIME_OF_DELIVERY` VARCHAR(40) NOT NULL,
`DATE_OF_DELIVERY` VARCHAR(40) NOT NULL,
`COMPLETE_ADDRESS` VARCHAR(255) NOT NULL,
`CITY` VARCHAR(40) NOT NULL,
`PIN_CODE` INT(11) NOT NULL,
`PRODUCT_ID` INT(11) NOT NULL,
`PRODUCT_NAME` VARCHAR(40) NOT NULL,
`QUANTITY` INT(11) NULL DEFAULT NULL,
`PRICE` DECIMAL(10,2) NULL DEFAULT NULL,
`DIMENSION` VARCHAR(10) NOT NULL,
`VARIANTS` VARCHAR(255) NULL DEFAULT NULL,
`ADDONS` VARCHAR(255) NULL DEFAULT NULL,
`RECIPE_ID` INT(11) NULL DEFAULT NULL,
`BRAND_ID` INT(11) NOT NULL,
`UNIT_ID` INT(11) NOT NULL,
PRIMARY KEY (`WEB_OFFER_DETAIL_ID`))
ENGINE = InnoDB
DEFAULT CHARACTER SET = latin1
COLLATE = latin1_bin;


ALTER TABLE `KETTLE_DEV`.`WEB_OFFER_DETAIL`
ADD COLUMN `CUSTOMER_NAME` VARCHAR(100) NULL DEFAULT NULL AFTER `CUSTOMER_ID`,
ADD COLUMN `CUSTOMER_CONTACT` VARCHAR(10) NOT NULL AFTER `CUSTOMER_NAME`;

ALTER TABLE `KETTLE_DEV`.`WEB_OFFER_DETAIL`
CHANGE COLUMN `` `CREATION_TIME` TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00' ;




CREATE TABLE KETTLE_DEV.CUSTOMER_BRAND_MAPPING (
    ID INTEGER AUTO_INCREMENT PRIMARY KEY NOT NULL,
    CUSTOMER_ID INTEGER NOT NULL,
    BRAND_ID INTEGER NOT NULL,
    TOTAL_ORDER INTEGER NOT NULL,
    LAST_ORDER_ID INTEGER NOT NULL,
    LAST_ORDER_TIME DATE NOT NULL
);

ALTER TABLE `KETTLE_DEV`.`CUSTOMER_BRAND_MAPPING`
CHANGE COLUMN `LAST_ORDER_TIME` `LAST_ORDER_TIME` DATETIME NOT NULL ;


CREATE INDEX CUSTOMER_ID_CUSTOMER_BRAND_INDEX ON KETTLE_DEV.CUSTOMER_BRAND_MAPPING (CUSTOMER_ID) USING BTREE;
CREATE INDEX BRAND_ID_CUSTOMER_BRAND_INDEX ON KETTLE_DEV.CUSTOMER_BRAND_MAPPING (BRAND_ID) USING BTREE;

INSERT INTO KETTLE_DEV.CUSTOMER_BRAND_MAPPING (CUSTOMER_ID,BRAND_ID,TOTAL_ORDER,LAST_ORDER_ID,LAST_ORDER_TIME)

SELECT
    CUSTOMER_ID,BRAND_ID, COUNT(*) AS TOTAL_ORDER,MAX(ORDER_ID),MAX(BILLING_SERVER_TIME)
FROM
    KETTLE_DEV.ORDER_DETAIL
WHERE
   ORDER_STATUS NOT IN ('CANCELLED','CANCELLED_REQUESTED') AND  CUSTOMER_ID IN (SELECT
            CUSTOMER_ID
        FROM
            KETTLE_DEV.CUSTOMER_INFO)
            GROUP BY BRAND_ID,CUSTOMER_ID;




