CREATE TABLE KETTLE_DEV.MENU_PRODUCT_COST_DETAIL(
COST_DETAIL_ID INTEGER UNIQUE NOT NULL PRIMARY KEY AUTO_INCREMENT,
CL<PERSON>URE_ID INTEGER NOT NULL,
MENU_PRODUCT_ID INTEGER NOT NULL,
DIMENSION VARCHAR(20) NOT NULL,
PRODUCT_NAME VARCHAR(100) NOT NULL,
RECIPE_ID INTEGER NOT NULL,
COST_SOURCE VARCHAR(20) NOT NULL,
INGREDIENT_COST DECIMAL(16,6),
ADDON_COST DECIMAL(16,6),
TOTAL_COST DECIMAL(16,6),
GENERATION_TIME DATETIME NOT NULL
);

CREATE TABLE KETTLE_DEV.MENU_PRODUCT_COGS_DRILLDOWN(
DRILLDOWN_ID INTEGER UNIQUE NOT NULL PRIMARY KEY AUTO_INCREMENT,
COGS_DETAIL_ID INTEGER NOT NULL,
SCM_PRODUCT_ID INTEGER NOT NULL,
COST_SOURCE VARCHAR(20) NOT NULL,
ADDON VARCHAR(1) NOT NULL,
QUANTITY DECIMAL(16,6) NOT NULL,
PRICE DECIMAL(16,6),
COST DECIMAL(16,6)
);