CREATE TABLE KETTLE_DEV.LOYALTY_TRANSFER(
EVENT_ID INT(15) AUTO_INCREMENT PRIMARY KEY,
SENDER_ID INT(15) NOT NULL,
RECEIVER_ID INT(15) DEFAULT NULL,
RECEIVER_NAME VARCHAR(50) DEFAULT NULL,
RECEIVER_CONTACT_NUMBER VARCHAR(15) NOT NULL,
SENDER_INITIAL_POINTS INT(10) DEFAULT NULL,
SENDER_TRANSFER_POINTS INT(10) DEFAULT NULL,
TRANSFER_INITIATED_TIME TIMESTAMP,
TRANSFER_REQUEST_TIME TIMESTAMP ,
TRA<PERSON>FER_STATUS VARCHAR(20) ,
EXPIRY_TIME TIMESTAMP,
TRANSFER_TYPE VARCHAR(20) ,
TRANSFER_MESSAGE VARCHAR(300) DEFAULT NULL,
BONUS_POINT INT(15) DEFAULT 0,
TOTAL_TRANSFERRED INT(15) DEFAULT NULL
);


CREATE TABLE KETTLE_DEV.LOYALTY_TRANSFER_STATUS_LOG(
LOYALTY_TRANSFER_STATUS_ID INT(15) PRIMARY KEY AUTO_INCREMENT,
EVENT_ID INT(15) NOT NULL,
FROM_STATUS VARCHAR(20),
TO_STATUS VARCHAR(20),
STATUS_UPDATE_TIME TIMESTAMP,
COMMENT VARCHAR(20)
);


ALTER TABLE `KETTLE_DEV`.`LOYALTY_TRANSFER`
CHANGE COLUMN `TRANSFER_INITIATED_TIME` `TRANSFER_INITIATED_TIME` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ,
CHANGE COLUMN `TRANSFER_REQUEST_TIME` `TRANSFER_REQUEST_TIME` TIMESTAMP NULL DEFAULT '0000-00-00 00:00:00' ,
CHANGE COLUMN `EXPIRY_TIME` `EXPIRY_TIME` TIMESTAMP NULL DEFAULT '0000-00-00 00:00:00' ;

ALTER TABLE KETTLE_DEV.LOYALTY_SCORE ADD COLUMN BLOCKED_POINTS INT(15) DEFAULT 0;
ALTER TABLE KETTLE_DEV.CUSTOMER_ADDRESS_INFO ADD COLUMN STATUS VARCHAR(20);

