CREATE DATABASE KETTLE_WAREHOUSE_DEV;

CREATE TABLE KETTLE_WAREHOUSE_DEV.`CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE`
(
    `BUSINESS_DATE` date DEFAULT NULL,
    `CUSTOMER_ID`   int(11) DEFAULT NULL,
    KEY   `CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE_BUSINESS_DATE` (`BUSINESS_DATE`) USING BTREE,
    KEY    `CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE_CUSTOMER_ID` (`CUSTOMER_ID`) USING BTREE
);



DELIMITER $$
CREATE PROCEDURE KETTLE_WAREHOUSE_DEV.`CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE`(IN FROM_DATE DATE, IN TILL_DATE DATE) proc_label :
BEGIN

DECLARE
LAST_DATE DATE;

SET
LAST_DATE = FROM_DATE;

IF
LAST_DATE IS NOT NULL AND LAST_DATE > TILL_DATE
THEN
	LEAVE proc_label;
END IF;

label_loop
: LOOP
if  TILL_DATE >= LAST_DATE then
delete
from KETTLE_WAREHOUSE_DEV.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE
where BUSINESS_DATE = LAST_DATE;

INSERT INTO KETTLE_WAREHOUSE_DEV.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE(CUSTOMER_ID, BUSINESS_DATE)
SELECT distinct od.CUSTOMER_ID, LAST_DATE BUSINESS_DATE
FROM KETTLE_DEV.ORDER_NPS_DETAIL od
WHERE od.CUSTOMER_ID NOT IN (1, 2, 3, 4, 5, 24035, 67456, 142315, 24035, 142315)
  AND od.SURVEY_CREATION_TIME BETWEEN DATE_ADD(LAST_DATE, INTERVAL 295 MINUTE) AND DATE_ADD(LAST_DATE, INTERVAL 1735 MINUTE);

ELSE
			LEAVE label_loop;
END IF;

        SET
LAST_DATE = DATE_ADD(LAST_DATE, INTERVAL 1 DAY);


END LOOP
label_loop;

END$$
DELIMITER ;



CREATE TABLE KETTLE_WAREHOUSE_DEV.CUSTOMER_ONE_VIEW_FEEDBACK_STATS
(
    BUSINESS_DATE                                     date       DEFAULT NULL,
    CUSTOMER_ID                                       INTEGER(11) DEFAULT NULL,
    IS_LATEST                                         varchar(1) DEFAULT NULL,

    IS_LAST_FEEDBACK_OF_LAST_ORDER                    VARCHAR(1),
    IS_LAST_CAFE_FEEDBACK_OF_LAST_CAFE_ORDER          VARCHAR(1),
    IS_LAST_DELIVERY_FEEDBACK_OF_LAST_DELIVERY_ORDER  VARCHAR(1),

    LAST_NPS_SURVEY_ID                       INTEGER NULL,
    SECOND_LAST_NPS_SURVEY_ID                   INTEGER NULL,
    THIRD_LAST_NPS_SURVEY_ID                       INTEGER NULL,

    LAST_NPS_SCORE                                    INTEGER NULL,
    SECOND_LAST_NPS_SCORE                             INTEGER NULL,
    THIRD_LAST_NPS_SCORE                              INTEGER NULL,

    LAST_NPS_SCORE_DATE                               DATE NULL,
    SECOND_LAST_NPS_SCORE_DATE                        DATE NULL,
    THIRD_LAST_NPS_SCORE_DATE                         DATE NULL,

    LAST_NPS_SCORE_ORDER_DATE                         DATE NULL,
    SECOND_LAST_NPS_SCORE_ORDER_DATE                        DATE NULL,
    THIRD_LAST_NPS_SCORE_ORDER_DATE                         DATE NULL,

    LAST_NPS_SCORE_TIME_GAP                           INTEGER NULL,
    SECOND_LAST_NPS_SCORE_TIME_GAP                          INTEGER NULL,
    THIRD_LAST_NPS_SCORE_TIME_GAP                           INTEGER NULL,

    LAST_NPS_SCORE_ORDER_ID                           INTEGER NULL,
    SECOND_LAST_NPS_SCORE_ORDER_ID                          INTEGER NULL,
    THIRD_LAST_NPS_SCORE_ORDER_ID                           INTEGER NULL,

    LAST_NPS_SCORE_ORDER_SOURCE                       VARCHAR(15) NULL,
    SECOND_LAST_NPS_SCORE_ORDER_SOURCE                VARCHAR(15) NULL,
    THIRD_LAST_NPS_SCORE_ORDER_SOURCE                 VARCHAR(15) NULL,

    LAST_NPS_SCORE_CALLBACK_REQUESTED                 VARCHAR(1) NULL,
    SECOND_LAST_NPS_SCORE_CALLBACK_REQUESTED          VARCHAR(1) NULL,
    THIRD_LAST_NPS_SCORE_CALLBACK_REQUESTED           VARCHAR(1) NULL,

    LAST_NPS_SCORE_CHANNEL_PARTNER                    INTEGER NULL,
    SECOND_LAST_NPS_SCORE_CHANNEL_PARTNER            INTEGER NULL,
    THIRD_LAST_NPS_SCORE_CHANNEL_PARTNER              INTEGER NULL,

    LAST_NPS_SCORE_UNIT_ID                            INTEGER NULL,
    SECOND_LAST_NPS_SCORE_UNIT_ID                     INTEGER NULL,
    THIRD_LAST_NPS_SCORE_UNIT_ID                      INTEGER NULL,

    CAFE_LAST_NPS_SCORE                               INTEGER NULL,
    CAFE_SECOND_LAST_NPS_SCORE                        INTEGER NULL,
    CAFE_THIRD_LAST_NPS_SCORE                         INTEGER NULL,

    CAFE_LAST_NPS_SCORE_DATE                          DATE NULL,
    CAFE_SECOND_LAST_NPS_SCORE_DATE                   DATE NULL,
    CAFE_THIRD_LAST_NPS_SCORE_DATE                    DATE NULL,

    CAFE_LAST_NPS_SCORE_ORDER_ID                      INTEGER NULL,
    CAFE_SECOND_LAST_NPS_SCORE_ORDER_ID                     INTEGER NULL,
    CAFE_THIRD_LAST_NPS_SCORE_ORDER_ID                      INTEGER NULL,

    CAFE_LAST_NPS_SCORE_ORDER_SOURCE                  VARCHAR(15) NULL,
    CAFE_SECOND_LAST_NPS_SCORE_ORDER_SOURCE           VARCHAR(15) NULL,
    CAFE_THIRD_LAST_NPS_SCORE_ORDER_SOURCE            VARCHAR(15) NULL,

    CAFE_LAST_NPS_SCORE_CALLBACK_REQUESTED            VARCHAR(1) NULL,
    CAFE_SECOND_LAST_NPS_SCORE_CALLBACK_REQUESTED     VARCHAR(1) NULL,
    CAFE_THIRD_LAST_NPS_SCORE_CALLBACK_REQUESTED      VARCHAR(1) NULL,

    CAFE_LAST_NPS_SCORE_CHANNEL_PARTNER               INTEGER NULL,
    CAFE_SECOND_LAST_NPS_SCORE_CHANNEL_PARTNER        INTEGER NULL,
    CAFE_THIRD_LAST_NPS_SCORE_CHANNEL_PARTNER         INTEGER NULL,

    CAFE_LAST_NPS_SCORE_UNIT_ID                       INTEGER NULL,
    CAFE_SECOND_LAST_NPS_SCORE_UNIT_ID                INTEGER NULL,
    CAFE_THIRD_LAST_NPS_SCORE_UNIT_ID                 INTEGER NULL,

    DELIVERY_LAST_NPS_SCORE                           INTEGER NULL,
    DELIVERY_SECOND_LAST_NPS_SCORE                    INTEGER NULL,
    DELIVERY_THIRD_LAST_NPS_SCORE                     INTEGER NULL,

    DELIVERY_LAST_NPS_SCORE_DATE                      DATE NULL,
    DELIVERY_SECOND_LAST_NPS_SCORE_DATE               DATE NULL,
    DELIVERY_THIRD_LAST_NPS_SCORE_DATE                DATE NULL,

    DELIVERY_LAST_NPS_SCORE_ORDER_ID                  INTEGER NULL,
    DELIVERY_SECOND_LAST_NPS_SCORE_ORDER_ID            INTEGER NULL,
    DELIVERY_THIRD_LAST_NPS_SCORE_ORDER_ID             INTEGER NULL,

    DELIVERY_LAST_NPS_SCORE_ORDER_SOURCE              VARCHAR(15) NULL,
    DELIVERY_SECOND_LAST_NPS_SCORE_ORDER_SOURCE       VARCHAR(15) NULL,
    DELIVERY_THIRD_LAST_NPS_SCORE_ORDER_SOURCE        VARCHAR(15) NULL,

    DELIVERY_LAST_NPS_SCORE_CALLBACK_REQUESTED        VARCHAR(1) NULL,
    DELIVERY_SECOND_LAST_NPS_SCORE_CALLBACK_REQUESTED VARCHAR(1) NULL,
    DELIVERY_THIRD_LAST_NPS_SCORE_CALLBACK_REQUESTED  VARCHAR(1) NULL,

    DELIVERY_LAST_NPS_SCORE_CHANNEL_PARTNER           INTEGER NULL,
    DELIVERY_SECOND_LAST_NPS_SCORE_CHANNEL_PARTNER    INTEGER NULL,
    DELIVERY_THIRD_LAST_NPS_SCORE_CHANNEL_PARTNER     INTEGER NULL,

    DELIVERY_LAST_NPS_SCORE_UNIT_ID                   INTEGER NULL,
    DELIVERY_SECOND_LAST_NPS_SCORE_UNIT_ID            INTEGER NULL,
    DELIVERY_THIRD_LAST_NPS_SCORE_UNIT_ID             INTEGER NULL,

    LAST_FEEDBACK_AMBIENCE_SCORE                      INTEGER NULL,
    LAST_FEEDBACK_FOOD_SCORE                          INTEGER NULL,
    LAST_FEEDBACK_BEVERAGE_SCORE                      INTEGER NULL,
    LAST_FEEDBACK_SERVICE_SCORE                       INTEGER NULL,
    LAST_FEEDBACK_DELIVERY_SCORE                      INTEGER NULL,

    SECOND_LAST_FEEDBACK_AMBIENCE_SCORE               INTEGER NULL,
    SECOND_LAST_FEEDBACK_FOOD_SCORE                   INTEGER NULL,
    SECOND_LAST_FEEDBACK_BEVERAGE_SCORE               INTEGER NULL,
    SECOND_LAST_FEEDBACK_SERVICE_SCORE                INTEGER NULL,
    SECOND_LAST_FEEDBACK_DELIVERY_SCORE               INTEGER NULL,

    THIRD_LAST_FEEDBACK_AMBIENCE_SCORE                INTEGER NULL,
    THIRD_LAST_FEEDBACK_FOOD_SCORE                    INTEGER NULL,
    THIRD_LAST_FEEDBACK_BEVERAGE_SCORE                INTEGER NULL,
    THIRD_LAST_FEEDBACK_SERVICE_SCORE                 INTEGER NULL,
    THIRD_LAST_FEEDBACK_DELIVERY_SCORE                INTEGER NULL,

    TOTAL_NPS                                         INTEGER NULL,
    TOTAL_CAFE_NPS                                    INTEGER NULL,
    TOTAL_DELIVERY_NPS                                INTEGER NULL,

    AVG_NPS                                           DECIMAL (10,2) NULL ,
    AVG_CAFE_NPS                                      DECIMAL (10,2) NULL,
    AVG_DELIVERY_NPS                                  DECIMAL (10,2) NULL,

    TOTAL_NPS_IN_LAST_180_DAYS                        INTEGER NULL,
    TOTAL_CAFE_NPS_IN_LAST_180_DAYS                   INTEGER NULL,
    TOTAL_DELIVERY_NPS_IN_LAST_180_DAYS               INTEGER NULL,

    AVG_NPS_IN_LAST_180_DAYS                          DECIMAL (10,2) NULL ,
    AVG_CAFE_NPS_IN_LAST_180_DAYS                      DECIMAL (10,2) NULL ,
    AVG_DELIVERY_NPS_IN_LAST_180_DAYS                 DECIMAL (10,2) NULL

);


CREATE INDEX BUSINESS_DATE_CUSTOMER_ONE_VIEW_INDEX ON KETTLE_WAREHOUSE_DEV.CUSTOMER_ONE_VIEW_FEEDBACK_STATS (BUSINESS_DATE) USING BTREE;
CREATE INDEX CUSTOMER_ID_CUSTOMER_ONE_VIEW_INDEX ON KETTLE_WAREHOUSE_DEV.CUSTOMER_ONE_VIEW_FEEDBACK_STATS (CUSTOMER_ID) USING BTREE;
CREATE INDEX IS_LATEST_CUSTOMER_ONE_VIEW_INDEX ON KETTLE_WAREHOUSE_DEV.CUSTOMER_ONE_VIEW_FEEDBACK_STATS (IS_LATEST) USING BTREE;
CREATE INDEX IS_LAST_FEEDBACK_OF_LAST_ORDER_CUSTOMER_ONE_VIEW_INDEX ON KETTLE_WAREHOUSE_DEV.CUSTOMER_ONE_VIEW_FEEDBACK_STATS (IS_LAST_FEEDBACK_OF_LAST_ORDER) USING BTREE;

CREATE INDEX LAST_NPS_SCORE_CUSTOMER_ONE_VIEW_INDEX ON KETTLE_WAREHOUSE_DEV.CUSTOMER_ONE_VIEW_FEEDBACK_STATS (LAST_NPS_SCORE) USING BTREE;
CREATE INDEX SECOND_LAST_NPS_SCORE_CUSTOMER_ONE_VIEW_INDEX ON KETTLE_WAREHOUSE_DEV.CUSTOMER_ONE_VIEW_FEEDBACK_STATS (SECOND_LAST_NPS_SCORE) USING BTREE;
CREATE INDEX THIRD_LAST_NPS_SCORE_CUSTOMER_ONE_VIEW_INDEX ON KETTLE_WAREHOUSE_DEV.CUSTOMER_ONE_VIEW_FEEDBACK_STATS (THIRD_LAST_NPS_SCORE) USING BTREE;


CREATE INDEX TOTAL_NPS_CUSTOMER_ONE_VIEW_INDEX ON KETTLE_WAREHOUSE_DEV.CUSTOMER_ONE_VIEW_FEEDBACK_STATS (TOTAL_NPS) USING BTREE;

CREATE INDEX AVG_NPS_CUSTOMER_ONE_VIEW_INDEX ON KETTLE_WAREHOUSE_DEV.CUSTOMER_ONE_VIEW_FEEDBACK_STATS (AVG_NPS) USING BTREE;


CREATE TABLE KETTLE_WAREHOUSE_DEV.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS
(  BUSINESS_DATE DATE DEFAULT NULL,
CUSTOMER_ID INTEGER NULL,
IS_LATEST  VARCHAR (1) DEFAULT NULL,
SURVEY_RESPONSE_ID INTEGER NULL,
INSTANCE INTEGER NULL,
ORDER_SOURCE VARCHAR (10) DEFAULT NULL
);

CREATE INDEX BUSINESS_DATE_TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS_INDEX ON KETTLE_WAREHOUSE_DEV.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(BUSINESS_DATE) USING BTREE;
CREATE INDEX CUSTOMER_ID_TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS_INDEX ON KETTLE_WAREHOUSE_DEV.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(CUSTOMER_ID) USING BTREE;
CREATE INDEX IS_LATEST_TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS_INDEX ON KETTLE_WAREHOUSE_DEV.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(IS_LATEST) USING BTREE;
CREATE INDEX SURVEY_RESPONSE_ID_TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS_INDEX ON KETTLE_WAREHOUSE_DEV.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(SURVEY_RESPONSE_ID) USING BTREE;
CREATE INDEX INSTANCE_TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS_INDEX ON KETTLE_WAREHOUSE_DEV.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(INSTANCE) USING BTREE;
CREATE INDEX ORDER_SOURCE_CUSTOMER_ONE_VIEW_FEEDBACK_STATS_INDEX ON KETTLE_WAREHOUSE_DEV.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(ORDER_SOURCE) USING BTREE;


DROP PROCEDURE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS;
DELIMITER $$
CREATE  PROCEDURE KETTLE_WAREHOUSE.`CUSTOMER_ONE_VIEW_FEEDBACK_STATS`(IN FROM_DATE DATE, IN TILL_DATE DATE)
    proc_label : BEGIN
DECLARE LAST_DATE DATE;
SET LAST_DATE = FROM_DATE;
IF LAST_DATE IS NOT NULL AND LAST_DATE > TILL_DATE
THEN
    LEAVE proc_label;
END IF;
label_loop : LOOP
if  TILL_DATE >= LAST_DATE then
delete from KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS where BUSINESS_DATE = LAST_DATE;
TRUNCATE KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS;

INSERT INTO KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(
    CUSTOMER_ID,
    IS_LATEST,
    BUSINESS_DATE,
    SURVEY_RESPONSE_ID,
    INSTANCE,
    ORDER_SOURCE
)
SELECT   od.CUSTOMER_ID, 'Y', LAST_DATE,MAX(od.SURVEY_RESPONSE_ID),1, 'ALL'
FROM KETTLE.ORDER_NPS_DETAIL od ,KETTLE_WAREHOUSE.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE ci
where
        od.CUSTOMER_ID = ci.CUSTOMER_ID
  AND ci.BUSINESS_DATE = LAST_DATE
  AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)

GROUP BY ci.CUSTOMER_ID
;
INSERT INTO KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(
    CUSTOMER_ID,
    IS_LATEST,
    BUSINESS_DATE,
    SURVEY_RESPONSE_ID,
    INSTANCE,
    ORDER_SOURCE
)
SELECT   od.CUSTOMER_ID, 'Y', LAST_DATE,MAX(od.SURVEY_RESPONSE_ID),1, 'CAFE'
FROM KETTLE.ORDER_NPS_DETAIL od , KETTLE.ORDER_DETAIL o ,KETTLE_WAREHOUSE.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE ci
where
        od.CUSTOMER_ID = ci.CUSTOMER_ID
  AND od.ORDER_ID = o.ORDER_ID
  AND o.ORDER_SOURCE <> 'COD'
  AND ci.BUSINESS_DATE = LAST_DATE
  AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
GROUP BY ci.CUSTOMER_ID
;
INSERT INTO KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(
    CUSTOMER_ID,
    IS_LATEST,
    BUSINESS_DATE,
    SURVEY_RESPONSE_ID,
    INSTANCE,
    ORDER_SOURCE
)
SELECT   od.CUSTOMER_ID, 'Y', LAST_DATE,MAX(od.SURVEY_RESPONSE_ID),1, 'COD'
FROM KETTLE.ORDER_NPS_DETAIL od , KETTLE.ORDER_DETAIL o ,KETTLE_WAREHOUSE.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE ci
where
        od.CUSTOMER_ID = ci.CUSTOMER_ID
  AND od.ORDER_ID = o.ORDER_ID
  AND o.ORDER_SOURCE = 'COD'
  AND ci.BUSINESS_DATE = LAST_DATE
  AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
GROUP BY ci.CUSTOMER_ID
;
INSERT INTO KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(
    CUSTOMER_ID,
    IS_LATEST,
    BUSINESS_DATE,
    SURVEY_RESPONSE_ID,
    INSTANCE,
    ORDER_SOURCE
)
SELECT   od.CUSTOMER_ID, 'Y', LAST_DATE,MAX(od.SURVEY_RESPONSE_ID),2, 'ALL'
FROM KETTLE.ORDER_NPS_DETAIL od ,KETTLE_WAREHOUSE.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE ci
where
        od.CUSTOMER_ID = ci.CUSTOMER_ID
  AND ci.BUSINESS_DATE = LAST_DATE
  AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
  AND od.SURVEY_RESPONSE_ID NOT IN (  select SURVEY_RESPONSE_ID from KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS WHERE BUSINESS_DATE = LAST_DATE AND IS_LATEST ='Y' AND ORDER_SOURCE = 'ALL')
GROUP BY ci.CUSTOMER_ID
;
INSERT INTO KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(
    CUSTOMER_ID,
    IS_LATEST,
    BUSINESS_DATE,
    SURVEY_RESPONSE_ID,
    INSTANCE,
    ORDER_SOURCE
)
SELECT   od.CUSTOMER_ID, 'Y', LAST_DATE,MAX(od.SURVEY_RESPONSE_ID),2,  'CAFE'
FROM KETTLE.ORDER_NPS_DETAIL od, KETTLE.ORDER_DETAIL o ,KETTLE_WAREHOUSE.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE ci
where
        od.CUSTOMER_ID = ci.CUSTOMER_ID
  AND od.ORDER_ID = o.ORDER_ID
  AND ci.BUSINESS_DATE = LAST_DATE
  AND o.ORDER_SOURCE <> 'COD'
  AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
  AND od.SURVEY_RESPONSE_ID NOT IN (select SURVEY_RESPONSE_ID from KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS WHERE BUSINESS_DATE = LAST_DATE AND IS_LATEST ='Y' AND ORDER_SOURCE  = 'CAFE')
GROUP BY ci.CUSTOMER_ID
;
INSERT INTO KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(
    CUSTOMER_ID,
    IS_LATEST,
    BUSINESS_DATE,
    SURVEY_RESPONSE_ID,
    INSTANCE,
    ORDER_SOURCE
)
SELECT   od.CUSTOMER_ID, 'Y', LAST_DATE,MAX(od.SURVEY_RESPONSE_ID),2,  'COD'
FROM KETTLE.ORDER_NPS_DETAIL od, KETTLE.ORDER_DETAIL o ,KETTLE_WAREHOUSE.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE ci
where
        od.CUSTOMER_ID = ci.CUSTOMER_ID
  AND od.ORDER_ID = o.ORDER_ID
  AND ci.BUSINESS_DATE = LAST_DATE
  AND o.ORDER_SOURCE = 'COD'
  AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
  AND od.SURVEY_RESPONSE_ID NOT IN (select SURVEY_RESPONSE_ID from KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS WHERE BUSINESS_DATE = LAST_DATE AND IS_LATEST ='Y' AND ORDER_SOURCE  = 'COD')
GROUP BY ci.CUSTOMER_ID
;
INSERT INTO KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(
    CUSTOMER_ID,
    IS_LATEST,
    BUSINESS_DATE,
    SURVEY_RESPONSE_ID,
    INSTANCE,
    ORDER_SOURCE
)
SELECT   od.CUSTOMER_ID, 'Y', LAST_DATE,MAX(od.SURVEY_RESPONSE_ID),3, 'ALL'
FROM KETTLE.ORDER_NPS_DETAIL od ,KETTLE_WAREHOUSE.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE ci
where
        od.CUSTOMER_ID = ci.CUSTOMER_ID
  AND ci.BUSINESS_DATE = LAST_DATE
  AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
  AND od.SURVEY_RESPONSE_ID NOT IN (  select SURVEY_RESPONSE_ID from KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS WHERE BUSINESS_DATE = LAST_DATE AND IS_LATEST ='Y' AND ORDER_SOURCE = 'ALL')
GROUP BY ci.CUSTOMER_ID
;
INSERT INTO KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(
    CUSTOMER_ID,
    IS_LATEST,
    BUSINESS_DATE,
    SURVEY_RESPONSE_ID,
    INSTANCE,
    ORDER_SOURCE
)
SELECT   od.CUSTOMER_ID, 'Y', LAST_DATE,MAX(od.SURVEY_RESPONSE_ID),3,  'CAFE'
FROM KETTLE.ORDER_NPS_DETAIL od, KETTLE.ORDER_DETAIL o ,KETTLE_WAREHOUSE.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE ci
where
        od.CUSTOMER_ID = ci.CUSTOMER_ID
  AND od.ORDER_ID = o.ORDER_ID
  AND ci.BUSINESS_DATE = LAST_DATE
  AND o.ORDER_SOURCE <> 'COD'
  AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
  AND od.SURVEY_RESPONSE_ID NOT IN (select SURVEY_RESPONSE_ID from KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS WHERE BUSINESS_DATE = LAST_DATE AND IS_LATEST ='Y' AND ORDER_SOURCE  = 'CAFE')
GROUP BY ci.CUSTOMER_ID
;
INSERT INTO KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS(
    CUSTOMER_ID,
    IS_LATEST,
    BUSINESS_DATE,
    SURVEY_RESPONSE_ID,
    INSTANCE,
    ORDER_SOURCE
)
SELECT   od.CUSTOMER_ID, 'Y', LAST_DATE,MAX(od.SURVEY_RESPONSE_ID),3,  'COD'
FROM KETTLE.ORDER_NPS_DETAIL od, KETTLE.ORDER_DETAIL o ,KETTLE_WAREHOUSE.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE ci
where
        od.CUSTOMER_ID = ci.CUSTOMER_ID
  AND od.ORDER_ID = o.ORDER_ID
  AND ci.BUSINESS_DATE = LAST_DATE
  AND o.ORDER_SOURCE = 'COD'
  AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
  AND od.SURVEY_RESPONSE_ID NOT IN (select SURVEY_RESPONSE_ID from KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS WHERE BUSINESS_DATE = LAST_DATE AND IS_LATEST ='Y' AND ORDER_SOURCE  = 'COD')
GROUP BY ci.CUSTOMER_ID
;
INSERT INTO KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS(
    BUSINESS_DATE,
    CUSTOMER_ID,
    IS_LATEST,
    LAST_NPS_SURVEY_ID,
    LAST_NPS_SCORE,
    LAST_NPS_SCORE_DATE,
    LAST_NPS_SCORE_ORDER_SOURCE,
    LAST_NPS_SCORE_TIME_GAP,
    LAST_NPS_SCORE_ORDER_DATE,
    LAST_NPS_SCORE_ORDER_ID,
    LAST_NPS_SCORE_UNIT_ID,
    IS_LAST_FEEDBACK_OF_LAST_ORDER,
    LAST_NPS_SCORE_CHANNEL_PARTNER
)
SELECT
    LAST_DATE,
    t.CUSTOMER_ID, 'Y',
    t.SURVEY_RESPONSE_ID,
    os.NPS_SCORE,
    DATE(os.SURVEY_CREATION_TIME),
    od.ORDER_SOURCE,
    ABS(DATEDIFF(DATE(od.BILL_START_TIME), DATE(os.SURVEY_CREATION_TIME))),
    DATE(od.BILL_START_TIME),
    od.ORDER_ID,
    od.UNIT_ID,
    CASE WHEN od.ORDER_ID = (SELECT MAX(ORDER_ID) FROM KETTLE.ORDER_DETAIL WHERE CUSTOMER_ID = t.CUSTOMER_ID) THEN 'Y'
    ELSE 'N' END,
    od.CHANNEL_PARTNER_ID
FROM
    KETTLE.ORDER_NPS_DETAIL os,
    KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS t,
    KETTLE.ORDER_DETAIL od
WHERE
    t.BUSINESS_DATE = LAST_DATE
    AND os.ORDER_ID = od.ORDER_ID
	AND t.IS_LATEST = 'Y'
	AND t.SURVEY_RESPONSE_ID = os.SURVEY_RESPONSE_ID
	AND t.INSTANCE = 1
	AND t.ORDER_SOURCE ='ALL'
;
UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    LAST_DATE BUSINESS_DATE,
    t.CUSTOMER_ID,
    'Y' IS_LATEST,
    t.SURVEY_RESPONSE_ID,
    os.NPS_SCORE,
    DATE(os.SURVEY_CREATION_TIME) NPS_SCORE_DATE,
    od.ORDER_SOURCE,
    ABS(DATEDIFF(DATE(od.BILL_START_TIME), DATE(os.SURVEY_CREATION_TIME))) NPS_SCORE_TIME_GAP,
    DATE(od.BILL_START_TIME) NPS_SCORE_ORDER_DATE,
    od.ORDER_ID,
    od.UNIT_ID,
    od.CHANNEL_PARTNER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL os, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS t, KETTLE.ORDER_DETAIL od
    WHERE
    t.BUSINESS_DATE = LAST_DATE
    AND os.ORDER_ID = od.ORDER_ID
    AND t.IS_LATEST = 'Y'
    AND t.SURVEY_RESPONSE_ID = os.SURVEY_RESPONSE_ID
    AND t.INSTANCE = 2
    AND t.ORDER_SOURCE = 'ALL') os
SET
    od.SECOND_LAST_NPS_SURVEY_ID = os.SURVEY_RESPONSE_ID,
    od.SECOND_LAST_NPS_SCORE = os.NPS_SCORE,
    od.SECOND_LAST_NPS_SCORE_DATE = os.NPS_SCORE_DATE,
    od.SECOND_LAST_NPS_SCORE_ORDER_SOURCE = os.ORDER_SOURCE,
    od.SECOND_LAST_NPS_SCORE_TIME_GAP = os.NPS_SCORE_TIME_GAP,
    od.SECOND_LAST_NPS_SCORE_ORDER_DATE = os.NPS_SCORE_ORDER_DATE,
    od.SECOND_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.SECOND_LAST_NPS_SCORE_UNIT_ID = os.UNIT_ID,
    od.SECOND_LAST_NPS_SCORE_CHANNEL_PARTNER = os.CHANNEL_PARTNER_ID
WHERE
    od.CUSTOMER_ID = os.CUSTOMER_ID
  AND od.BUSINESS_DATE = os.BUSINESS_DATE
  AND od.IS_LATEST = 'Y';

UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    LAST_DATE BUSINESS_DATE,
    t.CUSTOMER_ID,
    'Y' IS_LATEST,
    t.SURVEY_RESPONSE_ID,
    os.NPS_SCORE,
    DATE(os.SURVEY_CREATION_TIME) NPS_SCORE_DATE,
    od.ORDER_SOURCE,
    ABS(DATEDIFF(DATE(od.BILL_START_TIME), DATE(os.SURVEY_CREATION_TIME))) NPS_SCORE_TIME_GAP,
    DATE(od.BILL_START_TIME) NPS_SCORE_ORDER_DATE,
    od.ORDER_ID,
    od.UNIT_ID,
    od.CHANNEL_PARTNER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL os, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS t, KETTLE.ORDER_DETAIL od
    WHERE
    t.BUSINESS_DATE = LAST_DATE
    AND os.ORDER_ID = od.ORDER_ID
    AND t.IS_LATEST = 'Y'
    AND t.SURVEY_RESPONSE_ID = os.SURVEY_RESPONSE_ID
    AND t.INSTANCE = 3
    AND t.ORDER_SOURCE = 'ALL') os
SET
    od.THIRD_LAST_NPS_SURVEY_ID = os.SURVEY_RESPONSE_ID,
    od.THIRD_LAST_NPS_SCORE = os.NPS_SCORE,
    od.THIRD_LAST_NPS_SCORE_DATE = os.NPS_SCORE_DATE,
    od.THIRD_LAST_NPS_SCORE_ORDER_SOURCE = os.ORDER_SOURCE,
    od.THIRD_LAST_NPS_SCORE_TIME_GAP = os.NPS_SCORE_TIME_GAP,
    od.THIRD_LAST_NPS_SCORE_ORDER_DATE = os.NPS_SCORE_ORDER_DATE,
    od.THIRD_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.THIRD_LAST_NPS_SCORE_UNIT_ID = os.UNIT_ID,
    od.THIRD_LAST_NPS_SCORE_CHANNEL_PARTNER = os.CHANNEL_PARTNER_ID
WHERE
    od.CUSTOMER_ID = os.CUSTOMER_ID
  AND od.BUSINESS_DATE = os.BUSINESS_DATE
  AND od.IS_LATEST = 'Y';

UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    LAST_DATE BUSINESS_DATE,
    t.CUSTOMER_ID,
    'Y' IS_LATEST,
    t.SURVEY_RESPONSE_ID,
    os.NPS_SCORE,
    DATE(os.SURVEY_CREATION_TIME) NPS_SCORE_DATE,
    od.ORDER_SOURCE,
    ABS(DATEDIFF(DATE(od.BILL_START_TIME), DATE(os.SURVEY_CREATION_TIME))) NPS_SCORE_TIME_GAP,
    DATE(od.BILL_START_TIME) NPS_SCORE_ORDER_DATE,
    od.ORDER_ID,
    od.UNIT_ID,
    od.CHANNEL_PARTNER_ID,
    CASE WHEN od.ORDER_ID = (SELECT MAX(ORDER_ID) FROM KETTLE.ORDER_DETAIL WHERE CUSTOMER_ID = t.CUSTOMER_ID AND ORDER_SOURCE <> 'COD') THEN 'Y'
    ELSE 'N' END AS IS_LAST_CAFE_FEEDBACK_OF_LAST_CAFE_ORDER
    FROM
    KETTLE.ORDER_NPS_DETAIL os, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS t, KETTLE.ORDER_DETAIL od
    WHERE
    t.BUSINESS_DATE = LAST_DATE
    AND os.ORDER_ID = od.ORDER_ID
    AND t.IS_LATEST = 'Y'
    AND t.SURVEY_RESPONSE_ID = os.SURVEY_RESPONSE_ID
    AND t.INSTANCE = 1
    AND t.ORDER_SOURCE = 'CAFE') os
SET
    od.CAFE_LAST_NPS_SCORE = os.NPS_SCORE,
    od.CAFE_LAST_NPS_SCORE_DATE = os.NPS_SCORE_DATE,
    od.CAFE_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.CAFE_LAST_NPS_SCORE_ORDER_SOURCE = os.ORDER_SOURCE,
    od.CAFE_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.CAFE_LAST_NPS_SCORE_UNIT_ID = os.UNIT_ID,
    od.CAFE_LAST_NPS_SCORE_CHANNEL_PARTNER = os.CHANNEL_PARTNER_ID,
    od.IS_LAST_CAFE_FEEDBACK_OF_LAST_CAFE_ORDER = os.IS_LAST_CAFE_FEEDBACK_OF_LAST_CAFE_ORDER
WHERE
    od.CUSTOMER_ID = os.CUSTOMER_ID
  AND od.BUSINESS_DATE = os.BUSINESS_DATE
  AND od.IS_LATEST = 'Y';

UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    LAST_DATE BUSINESS_DATE,
    t.CUSTOMER_ID,
    'Y' IS_LATEST,
    t.SURVEY_RESPONSE_ID,
    os.NPS_SCORE,
    DATE(os.SURVEY_CREATION_TIME) NPS_SCORE_DATE,
    od.ORDER_SOURCE,
    ABS(DATEDIFF(DATE(od.BILL_START_TIME), DATE(os.SURVEY_CREATION_TIME))) NPS_SCORE_TIME_GAP,
    DATE(od.BILL_START_TIME) NPS_SCORE_ORDER_DATE,
    od.ORDER_ID,
    od.UNIT_ID,
    od.CHANNEL_PARTNER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL os, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS t, KETTLE.ORDER_DETAIL od
    WHERE
    t.BUSINESS_DATE = LAST_DATE
    AND os.ORDER_ID = od.ORDER_ID
    AND t.IS_LATEST = 'Y'
    AND t.SURVEY_RESPONSE_ID = os.SURVEY_RESPONSE_ID
    AND t.INSTANCE = 2
    AND t.ORDER_SOURCE = 'CAFE') os
SET
    od.CAFE_SECOND_LAST_NPS_SCORE = os.NPS_SCORE,
    od.CAFE_SECOND_LAST_NPS_SCORE_DATE = os.NPS_SCORE_DATE,
    od.CAFE_SECOND_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.CAFE_SECOND_LAST_NPS_SCORE_ORDER_SOURCE = os.ORDER_SOURCE,
    od.CAFE_SECOND_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.CAFE_SECOND_LAST_NPS_SCORE_UNIT_ID = os.UNIT_ID,
    od.CAFE_SECOND_LAST_NPS_SCORE_CHANNEL_PARTNER = os.CHANNEL_PARTNER_ID
WHERE
    od.CUSTOMER_ID = os.CUSTOMER_ID
  AND od.BUSINESS_DATE = os.BUSINESS_DATE
  AND od.IS_LATEST = 'Y';

UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    LAST_DATE BUSINESS_DATE,
    t.CUSTOMER_ID,
    'Y' IS_LATEST,
    t.SURVEY_RESPONSE_ID,
    os.NPS_SCORE,
    DATE(os.SURVEY_CREATION_TIME) NPS_SCORE_DATE,
    od.ORDER_SOURCE,
    ABS(DATEDIFF(DATE(od.BILL_START_TIME), DATE(os.SURVEY_CREATION_TIME))) NPS_SCORE_TIME_GAP,
    DATE(od.BILL_START_TIME) NPS_SCORE_ORDER_DATE,
    od.ORDER_ID,
    od.UNIT_ID,
    od.CHANNEL_PARTNER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL os, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS t, KETTLE.ORDER_DETAIL od
    WHERE
    t.BUSINESS_DATE = LAST_DATE
    AND os.ORDER_ID = od.ORDER_ID
    AND t.IS_LATEST = 'Y'
    AND t.SURVEY_RESPONSE_ID = os.SURVEY_RESPONSE_ID
    AND t.INSTANCE = 3
    AND t.ORDER_SOURCE = 'CAFE') os
SET
    od.CAFE_THIRD_LAST_NPS_SCORE = os.NPS_SCORE,
    od.CAFE_THIRD_LAST_NPS_SCORE_DATE = os.NPS_SCORE_DATE,
    od.CAFE_THIRD_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.CAFE_THIRD_LAST_NPS_SCORE_ORDER_SOURCE = os.ORDER_SOURCE,
    od.CAFE_THIRD_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.CAFE_THIRD_LAST_NPS_SCORE_UNIT_ID = os.UNIT_ID,
    od.CAFE_THIRD_LAST_NPS_SCORE_CHANNEL_PARTNER = os.CHANNEL_PARTNER_ID
WHERE
    od.CUSTOMER_ID = os.CUSTOMER_ID
  AND od.BUSINESS_DATE = os.BUSINESS_DATE
  AND od.IS_LATEST = 'Y';

UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    LAST_DATE BUSINESS_DATE,
    t.CUSTOMER_ID,
    'Y' IS_LATEST,
    t.SURVEY_RESPONSE_ID,
    os.NPS_SCORE,
    DATE(os.SURVEY_CREATION_TIME) NPS_SCORE_DATE,
    od.ORDER_SOURCE,
    ABS(DATEDIFF(DATE(od.BILL_START_TIME), DATE(os.SURVEY_CREATION_TIME))) NPS_SCORE_TIME_GAP,
    DATE(od.BILL_START_TIME) NPS_SCORE_ORDER_DATE,
    od.ORDER_ID,
    od.UNIT_ID,
    od.CHANNEL_PARTNER_ID,
    CASE WHEN od.ORDER_ID = (SELECT MAX(ORDER_ID) FROM KETTLE.ORDER_DETAIL WHERE CUSTOMER_ID = t.CUSTOMER_ID AND ORDER_SOURCE = 'COD') THEN 'Y'
    ELSE 'N' END AS IS_LAST_DELIVERY_FEEDBACK_OF_LAST_DELIVERY_ORDER
    FROM
    KETTLE.ORDER_NPS_DETAIL os, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS t, KETTLE.ORDER_DETAIL od
    WHERE
    t.BUSINESS_DATE = LAST_DATE
    AND os.ORDER_ID = od.ORDER_ID
    AND t.IS_LATEST = 'Y'
    AND t.SURVEY_RESPONSE_ID = os.SURVEY_RESPONSE_ID
    AND t.INSTANCE = 1
    AND t.ORDER_SOURCE = 'COD') os
SET
    od.DELIVERY_LAST_NPS_SCORE = os.NPS_SCORE,
    od.DELIVERY_LAST_NPS_SCORE_DATE = os.NPS_SCORE_DATE,
    od.DELIVERY_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.DELIVERY_LAST_NPS_SCORE_ORDER_SOURCE = os.ORDER_SOURCE,
    od.DELIVERY_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.DELIVERY_LAST_NPS_SCORE_UNIT_ID = os.UNIT_ID,
    od.DELIVERY_LAST_NPS_SCORE_CHANNEL_PARTNER = os.CHANNEL_PARTNER_ID,
    od.IS_LAST_DELIVERY_FEEDBACK_OF_LAST_DELIVERY_ORDER = os.IS_LAST_DELIVERY_FEEDBACK_OF_LAST_DELIVERY_ORDER
WHERE
    od.CUSTOMER_ID = os.CUSTOMER_ID
  AND od.BUSINESS_DATE = os.BUSINESS_DATE
  AND od.IS_LATEST = 'Y';

UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    LAST_DATE BUSINESS_DATE,
    t.CUSTOMER_ID,
    'Y' IS_LATEST,
    t.SURVEY_RESPONSE_ID,
    os.NPS_SCORE,
    DATE(os.SURVEY_CREATION_TIME) NPS_SCORE_DATE,
    od.ORDER_SOURCE,
    ABS(DATEDIFF(DATE(od.BILL_START_TIME), DATE(os.SURVEY_CREATION_TIME))) NPS_SCORE_TIME_GAP,
    DATE(od.BILL_START_TIME) NPS_SCORE_ORDER_DATE,
    od.ORDER_ID,
    od.UNIT_ID,
    od.CHANNEL_PARTNER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL os, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS t, KETTLE.ORDER_DETAIL od
    WHERE
    t.BUSINESS_DATE = LAST_DATE
    AND os.ORDER_ID = od.ORDER_ID
    AND t.IS_LATEST = 'Y'
    AND t.SURVEY_RESPONSE_ID = os.SURVEY_RESPONSE_ID
    AND t.INSTANCE = 2
    AND t.ORDER_SOURCE = 'COD') os
SET
    od.DELIVERY_SECOND_LAST_NPS_SCORE = os.NPS_SCORE,
    od.DELIVERY_SECOND_LAST_NPS_SCORE_DATE = os.NPS_SCORE_DATE,
    od.DELIVERY_SECOND_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.DELIVERY_SECOND_LAST_NPS_SCORE_ORDER_SOURCE = os.ORDER_SOURCE,
    od.DELIVERY_SECOND_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.DELIVERY_SECOND_LAST_NPS_SCORE_UNIT_ID = os.UNIT_ID,
    od.DELIVERY_SECOND_LAST_NPS_SCORE_CHANNEL_PARTNER = os.CHANNEL_PARTNER_ID
WHERE
    od.CUSTOMER_ID = os.CUSTOMER_ID
  AND od.BUSINESS_DATE = os.BUSINESS_DATE
  AND od.IS_LATEST = 'Y';

UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    LAST_DATE BUSINESS_DATE,
    t.CUSTOMER_ID,
    'Y' IS_LATEST,
    t.SURVEY_RESPONSE_ID,
    os.NPS_SCORE,
    DATE(os.SURVEY_CREATION_TIME) NPS_SCORE_DATE,
    od.ORDER_SOURCE,
    ABS(DATEDIFF(DATE(od.BILL_START_TIME), DATE(os.SURVEY_CREATION_TIME))) NPS_SCORE_TIME_GAP,
    DATE(od.BILL_START_TIME) NPS_SCORE_ORDER_DATE,
    od.ORDER_ID,
    od.UNIT_ID,
    od.CHANNEL_PARTNER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL os, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS t, KETTLE.ORDER_DETAIL od
    WHERE
    t.BUSINESS_DATE = LAST_DATE
    AND os.ORDER_ID = od.ORDER_ID
    AND t.IS_LATEST = 'Y'
    AND t.SURVEY_RESPONSE_ID = os.SURVEY_RESPONSE_ID
    AND t.INSTANCE = 3
    AND t.ORDER_SOURCE = 'COD') os
SET
    od.DELIVERY_THIRD_LAST_NPS_SCORE = os.NPS_SCORE,
    od.DELIVERY_THIRD_LAST_NPS_SCORE_DATE = os.NPS_SCORE_DATE,
    od.DELIVERY_THIRD_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.DELIVERY_THIRD_LAST_NPS_SCORE_ORDER_SOURCE = os.ORDER_SOURCE,
    od.DELIVERY_THIRD_LAST_NPS_SCORE_ORDER_ID = os.ORDER_ID,
    od.DELIVERY_THIRD_LAST_NPS_SCORE_UNIT_ID = os.UNIT_ID,
    od.DELIVERY_THIRD_LAST_NPS_SCORE_CHANNEL_PARTNER = os.CHANNEL_PARTNER_ID
WHERE
    od.CUSTOMER_ID = os.CUSTOMER_ID
  AND od.BUSINESS_DATE = os.BUSINESS_DATE
  AND od.IS_LATEST = 'Y';

UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    COUNT(od.NPS_SCORE) TOTAL_NPS,
    AVG(od.NPS_SCORE) AVG_NPS,
    c.CUSTOMER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL od, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS c
    WHERE
    od.CUSTOMER_ID = c.CUSTOMER_ID
    and c.BUSINESS_DATE = LAST_DATE
    and c.IS_LATEST = 'Y'
    AND c.ORDER_SOURCE = 'ALL'
    AND c.INSTANCE = 1
    AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
    GROUP BY c.CUSTOMER_ID) t
SET
    od.TOTAL_NPS = t.TOTAL_NPS,
    od.AVG_NPS = t.AVG_NPS
WHERE
    od.CUSTOMER_ID = t.CUSTOMER_ID
  AND od.BUSINESS_DATE = LAST_DATE
  AND od.IS_LATEST = 'Y';


UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    COUNT(od.NPS_SCORE) TOTAL_NPS_IN_LAST_180_DAYS,
    AVG(od.NPS_SCORE) AVG_NPS_IN_LAST_180_DAYS,
    c.CUSTOMER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL od, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS c
    WHERE
    od.CUSTOMER_ID = c.CUSTOMER_ID
    and c.BUSINESS_DATE = LAST_DATE
    and c.IS_LATEST = 'Y'
    AND c.ORDER_SOURCE = 'ALL'
    AND c.INSTANCE = 1
    AND DATEDIFF(CURRENT_DATE(), DATE(od.SURVEY_CREATION_TIME)) BETWEEN 0 AND 180
    AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
    GROUP BY c.CUSTOMER_ID) t
SET
    od.TOTAL_NPS_IN_LAST_180_DAYS = t.TOTAL_NPS_IN_LAST_180_DAYS,
    od.AVG_NPS_IN_LAST_180_DAYS = t.AVG_NPS_IN_LAST_180_DAYS
WHERE
    od.CUSTOMER_ID = t.CUSTOMER_ID
  AND od.BUSINESS_DATE = LAST_DATE
  AND od.IS_LATEST = 'Y';


UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    COUNT(od.NPS_SCORE) TOTAL_CAFE_NPS,
    AVG(od.NPS_SCORE) AVG_CAFE_NPS,
    c.CUSTOMER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL od, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS c, KETTLE.ORDER_DETAIL o
    WHERE
    od.CUSTOMER_ID = c.CUSTOMER_ID
    and c.BUSINESS_DATE = LAST_DATE
    and c.IS_LATEST = 'Y'
    AND o.CUSTOMER_ID = c.CUSTOMER_ID
    AND od.ORDER_ID = o.ORDER_ID
    AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
    AND o.ORDER_SOURCE = 'CAFE'
    AND c.ORDER_SOURCE = 'CAFE'
    AND c.INSTANCE = 1
    GROUP BY c.CUSTOMER_ID) t
SET
    od.TOTAL_CAFE_NPS = t.TOTAL_CAFE_NPS,
    od.AVG_CAFE_NPS = t.AVG_CAFE_NPS
WHERE
    od.CUSTOMER_ID = t.CUSTOMER_ID
  AND od.BUSINESS_DATE = LAST_DATE
  AND od.IS_LATEST = 'Y';


UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    COUNT(od.NPS_SCORE) TOTAL_CAFE_NPS_IN_LAST_180_DAYS,
    AVG(od.NPS_SCORE) AVG_CAFE_NPS_IN_LAST_180_DAYS,
    c.CUSTOMER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL od, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS c, KETTLE.ORDER_DETAIL o
    WHERE
    od.CUSTOMER_ID = c.CUSTOMER_ID
    and c.BUSINESS_DATE = LAST_DATE
    and c.IS_LATEST = 'Y'
    AND o.CUSTOMER_ID = c.CUSTOMER_ID
    AND od.ORDER_ID = o.ORDER_ID
    AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
    AND o.ORDER_SOURCE = 'CAFE'
    AND c.ORDER_SOURCE = 'CAFE'
    AND DATEDIFF(CURRENT_DATE(), DATE(od.SURVEY_CREATION_TIME)) BETWEEN 0 AND 180

    AND c.INSTANCE = 1
    GROUP BY c.CUSTOMER_ID) t
SET
    od.TOTAL_CAFE_NPS_IN_LAST_180_DAYS = t.TOTAL_CAFE_NPS_IN_LAST_180_DAYS,
    od.AVG_CAFE_NPS_IN_LAST_180_DAYS = t.AVG_CAFE_NPS_IN_LAST_180_DAYS
WHERE
    od.CUSTOMER_ID = t.CUSTOMER_ID
  AND od.BUSINESS_DATE = LAST_DATE
  AND od.IS_LATEST = 'Y';



UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    COUNT(od.NPS_SCORE) TOTAL_DELIVERY_NPS,
    AVG(od.NPS_SCORE) AVG_DELIVERY_NPS,
    c.CUSTOMER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL od, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS c, KETTLE.ORDER_DETAIL o
    WHERE
    od.CUSTOMER_ID = c.CUSTOMER_ID
    and c.BUSINESS_DATE = LAST_DATE
    and c.IS_LATEST = 'Y'
    AND o.CUSTOMER_ID = c.CUSTOMER_ID
    AND od.ORDER_ID = o.ORDER_ID
    AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
    AND o.ORDER_SOURCE = 'COD'
    AND c.ORDER_SOURCE = 'COD'
    AND c.INSTANCE = 1
    GROUP BY c.CUSTOMER_ID) t
SET
    od.TOTAL_DELIVERY_NPS = t.TOTAL_DELIVERY_NPS,
    od.AVG_DELIVERY_NPS = t.AVG_DELIVERY_NPS
WHERE
    od.CUSTOMER_ID = t.CUSTOMER_ID
  AND od.BUSINESS_DATE = LAST_DATE
  AND od.IS_LATEST = 'Y';




UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS od,
    (SELECT
    COUNT(od.NPS_SCORE) TOTAL_DELIVERY_NPS_IN_LAST_180_DAYS,
    AVG(od.NPS_SCORE) AVG_DELIVERY_NPS_IN_LAST_180_DAYS,
    c.CUSTOMER_ID
    FROM
    KETTLE.ORDER_NPS_DETAIL od, KETTLE_WAREHOUSE.TEMP_CUSTOMER_ONE_VIEW_FEEDBACK_STATS c, KETTLE.ORDER_DETAIL o
    WHERE
    od.CUSTOMER_ID = c.CUSTOMER_ID
    and c.BUSINESS_DATE = LAST_DATE
    and c.IS_LATEST = 'Y'
    AND o.CUSTOMER_ID = c.CUSTOMER_ID
    AND od.ORDER_ID = o.ORDER_ID
    AND o.ORDER_SOURCE = 'COD'
    AND c.ORDER_SOURCE = 'COD'
    AND DATEDIFF(CURRENT_DATE(), DATE(od.SURVEY_CREATION_TIME)) BETWEEN 0 AND 180
    AND od.SURVEY_CREATION_TIME <= DATE_ADD(LAST_DATE, INTERVAL 1770 MINUTE)
    AND c.INSTANCE = 1
    GROUP BY c.CUSTOMER_ID) t
SET
    od.TOTAL_DELIVERY_NPS_IN_LAST_180_DAYS = t.TOTAL_DELIVERY_NPS_IN_LAST_180_DAYS,
    od.AVG_DELIVERY_NPS_IN_LAST_180_DAYS = t.AVG_DELIVERY_NPS_IN_LAST_180_DAYS
WHERE
    od.CUSTOMER_ID = t.CUSTOMER_ID
  AND od.BUSINESS_DATE = LAST_DATE
  AND od.IS_LATEST = 'Y';

UPDATE KETTLE_WAREHOUSE.CUSTOMER_ONE_VIEW_FEEDBACK_STATS cs,
    KETTLE_WAREHOUSE.CUSTOMER_IDS_FOR_NPS_FOR_A_BUSINESS_DATE ci
SET
    cs.IS_LATEST = 'N'
WHERE
    cs.CUSTOMER_ID = ci.CUSTOMER_ID
  AND cs.BUSINESS_DATE < ci.BUSINESS_DATE
  AND ci.BUSINESS_DATE = LAST_DATE;



ELSE
            LEAVE label_loop;
END IF;

        SET LAST_DATE = DATE_ADD(LAST_DATE, INTERVAL 1 DAY);


END LOOP label_loop;
END$$
DELIMITER ;