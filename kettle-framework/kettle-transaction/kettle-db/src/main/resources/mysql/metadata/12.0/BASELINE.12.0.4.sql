CREATE TABLE `KETTLE_DEV`.`PARTNER_ORDER_DISCOUNT_MAPPING` (
  `ORDER_DISCOUNT_MAPPING_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `ORDER_ID` INT(11) NOT NULL,
  `BRAND_ID` INT(11) NULL DEFAULT NULL,
  `PARTNER_NAME` VARCHAR(100) NULL DEFAULT NULL,
  `DISCOUNT_NAME` VARCHAR(250) NULL DEFAULT NULL,
  `DISCOUNT_TYPE` VARCHAR(100) NULL DEFAULT NULL,
  `DISCOUNT_CATEGORY` VARCHAR(100) NULL DEFAULT NULL,
  `DISCOUNT_VALUE` DECIMAL(10,2) NULL DEFAULT NULL,
  `DISCOUNT_AMOUNT` DECIMAL(10,2) NULL DEFAULT NULL,
  `DISCOUNT_TAXED` VARCHAR(45) NULL DEFAULT NULL,
  `DISCOUNT_APPLIEDON` DECIMAL(10,2) NULL DEFAULT NULL,
  `VOUCHER_CODE` VARCHAR(200) NULL DEFAULT NULL,
  PRIMARY KEY (`ORDER_DISCOUNT_MAPPING_ID`),
  INDEX `ORDER_DISCOUNT_MAP_idx` (`ORDER_ID` ASC),
  CONSTRAINT `ORDER_DISCOUNT_MAP`
    FOREIGN KEY (`ORDER_ID`)
    REFERENCES `KETTLE_DEV`.`ORDER_DETAIL` (`ORDER_ID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);
