CREATE TABLE `KETTLE_DEV`.`INVOICE_SEQUENCE_ID` (
  `SEQUENCE_ID` int(11) NOT NULL AUTO_INCREMENT,
  `FINANCIAL_YEAR` varchar(255) NOT NULL,
  `NEXT_VALUE` int(11) NOT NULL,
  `STATE_CODE` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`SEQUENCE_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;



ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN INVOICE_ID varchar(100);
ALTER TABLE KETTLE_DEV.ORDER_DETAIL
ADD COLUMN IS_INVOICE varchar(3);


DROP TABLE IF EXISTS `KETTLE_DEV`.`ORDER_INVOICE_DETAIL`;

CREATE TABLE `KETTLE_DEV`.`ORDER_INVOICE_DETAIL` (
	ORDER_INVOICE_DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `COMPANY_ADDRESS` varchar(255) NOT NULL,
  `COMPANY_NAME` varchar(255) NOT NULL,
  `GST_IN` varchar(50) NOT NULL,
  `ORDER_ID` varchar(50) NOT NULL,
	GENERATION_TIME TIMESTAMP NOT NULL,
  `GENERATED_BY` varchar(50) NULL
);

CREATE INDEX ORDER_INVOICE_DETAIL_ORDER_ID ON `KETTLE_DEV`.`ORDER_INVOICE_DETAIL`(ORDER_ID) USING BTREE;
CREATE INDEX ORDER_INVOICE_DETAIL_GST_IN ON `KETTLE_DEV`.`ORDER_INVOICE_DETAIL`(GST_IN) USING BTREE;
CREATE INDEX ORDER_INVOICE_DETAIL_COMPANY_NAME ON `KETTLE_DEV`.`ORDER_INVOICE_DETAIL`(COMPANY_NAME) USING BTREE;










CREATE TABLE KETTLE_DEV.CUSTOMER_TRANSACTION_DATA(
     CUSTOMER_TRANSACTION_ID PRIMARY KEY AUTO_INCREMENT,
     CUSTOMER_ID INTEGER NOT NULL,
     BRAND_ID INTEGER NOT NULL,
     FIRST_ORDER_ID INTEGER NULL,
     FIRST_ORDER_SOURCE VARCHAR(30) NULL,
     FIRST_CHANNEL_PARTNER_ID INTEGER NULL,
     FIRST_BUSINESS_DATE DATE NULL,
     FIRST_UNIT_ID INTEGER NULL,
     LAST_ORDER_ID INTEGER NULL,
     LAST_ORDER_SOURCE VARCHAR(30) NULL,
     LAST_CHANNEL_PARTNER_ID INTEGER NULL,
     LAST_BUSINESS_DATE DATE NULL,
     LAST_UNIT_ID INTEGER NULL,
     TOTAL_DINE_IN_ORDER INTEGER NULL,
     TOTAL_DELIVERY_ORDER INTEGER NULL,
     TOTAL_ORDER INTEGER NULL
);

DROP TABLE IF EXISTS KETTLE_DEV.CAFE_TIMING_CHANGE_REQUEST;
CREATE TABLE KETTLE_DEV.CAFE_TIMING_CHANGE_REQUEST (
                                                       `CAFE_TIMING_CHANGE_REQUEST_ID` INT NOT NULL AUTO_INCREMENT,
                                                       `UNIT_ID` INT(11) NULL,
                                                       `BRAND_ID` INT NULL ,
                                                       `UNIT_NAME` VARCHAR(45) NULL,
                                                       `DINE_IN_OPEN_TIME` VARCHAR(45) NULL DEFAULT NULL,
                                                       `DINE_IN_CLOSE_TIME` VARCHAR(45) NULL DEFAULT NULL,
                                                       `DELIVERY_OPEN_TIME` VARCHAR(45) NULL DEFAULT NULL,
                                                       `DELIVERY_CLOSE_TIME` VARCHAR(45) NULL DEFAULT NULL,
                                                       `DAY_OF_WEEK_TEXT` VARCHAR(255) NULL,
                                                       `EMPLOYEE_ID` INT(11) NULL,
                                                       PRIMARY KEY (`CAFE_TIMING_CHANGE_REQUEST_ID`));

DROP TABLE IF EXISTS KETTLE_DEV.CAFE_RAISE_REQUEST_APPROVAL;
CREATE TABLE KETTLE_DEV.CAFE_RAISE_REQUEST_APPROVAL (
                                                        `CAFE_RAISE_REQUEST_APPROVAL_ID` INT(11) NOT NULL AUTO_INCREMENT,
                                                        `UNIT_ID` INT NULL,
                                                        `CAFE_NAME` VARCHAR(255) NULL,
                                                        `PARTNER_NAME` VARCHAR(255) NULL,
                                                        `REASON` VARCHAR(255) NULL,
                                                        `PRIORITY` VARCHAR(255) NULL,
                                                        `START_TIME` VARCHAR(255) NULL,
                                                        `END_TIME` VARCHAR(255) NULL,
                                                        `BUISNESS_DATE` DATE NULL,
                                                        `ACTlON_TAKEN` VARCHAR(255) NULL,
                                                        `ACTION_TIMESTAMP` TIMESTAMP NULL,
                                                        `BRAND_ID` INT NULL,
                                                        `COMMENT_REASON` VARCHAR(255) NULL,
                                                        `AUTO_SWITCH` VARCHAR(1) NULL DEFAULT NULL,
                                                        `REQUESTED_BY_EMP_ID` INT(11) NULL DEFAULT NULL,
                                                        `ACCEPTED_BY_EMP_ID` INT(11) NULL DEFAULT NULL,
                                                        PRIMARY KEY (`CAFE_RAISE_REQUEST_APPROVAL_ID`));


-- Renewed customer after deleting account
ALTER TABLE KETTLE_DEV.CUSTOMER_INFO ADD COLUMN IS_RENEWED VARCHAR(1) DEFAULT "N";
CREATE INDEX ID ON KETTLE_DEV.CUSTOMER_CONTACT_INFO_MAPPING(ID) USING BTREE;
CREATE INDEX CUSTOMER_ID ON KETTLE_DEV.CUSTOMER_CONTACT_INFO_MAPPING(CUSTOMER_ID) USING BTREE;


ALTER TABLE KETTLE_DEV.CAFE_TIMING_CHANGE_REQUEST
    ADD COLUMN `ACCEPTED_BY_EMP_ID` INT(11) NULL DEFAULT NULL AFTER `EMPLOYEE_ID`,
ADD COLUMN `ACTION_TIMESTAMP` TIMESTAMP NULL DEFAULT NULL AFTER `ACCEPTED_BY_EMP_ID`,
ADD COLUMN `ACTION_TAKEN` VARCHAR(45) NULL DEFAULT NULL AFTER `ACTION_TIMESTAMP`;

CREATE TABLE KETTLE_DEV.CUSTOMER_FAV_CHAI_MAPPING (
                                                      `CUSTOMIZATION_ID` int(11) NOT NULL AUTO_INCREMENT,
                                                      `CUSTOMER_ID` int(11) NOT NULL,
                                                      `PRODUCT_ID` int(11) NOT NULL,
                                                      `STATUS` varchar(45) NOT NULL,
                                                      `DIMENSION` varchar(45) DEFAULT NULL,
                                                      `CREATION_TIME` datetime NOT NULL,
                                                      `LAST_UPDATED_TIME` datetime DEFAULT NULL,
                                                      `CREATED_AT` date NOT NULL,
                                                      `CONSUME_TYPE` varchar(45) DEFAULT NULL,
                                                      `TAG_TYPE` varchar(45) DEFAULT NULL,
                                                      `IS_UPDATED` varchar(45) NOT NULL DEFAULT 'NO',
                                                      `SOURCE_ID` int(11) DEFAULT NULL,
                                                      `SOURCE_NAME` varchar(45) DEFAULT NULL,
                                                      `RECIPE_ID` int(11) DEFAULT NULL,
                                                      `RECIPE_PROFILE` varchar(45) DEFAULT NULL,
                                                      `QUANTITY` int(11) NOT NULL,
                                                      PRIMARY KEY (`CUSTOMIZATION_ID`)
);
ALTER TABLE KETTLE_DEV.CUSTOMER_FAV_CHAI_MAPPING
    ADD COLUMN PRODUCT_NAME VARCHAR(45) NOT NULL AFTER PRODUCT_ID;

ALTER TABLE KETTLE_DEV.CUSTOMER_FAV_CHAI_MAPPING  ADD COLUMN PRODUCT_SHORT_CODE VARCHAR(25);

CREATE TABLE KETTLE_DEV.FAV_CHAI_CUSTOMIZATION_DETAIL(
                                                         FAV_CHAI_CUSTOMIZATION_DETAIL_ID INT NOT NULL AUTO_INCREMENT,
                                                         CUSTOMIZATION_ID INT NOT NULL,
                                                         ADDON_ID INT NOT NULL,
                                                         PRIMARY KEY (FAV_CHAI_CUSTOMIZATION_DETAIL_ID),
                                                         FOREIGN KEY FK_FAV_CHAI_CUSTOMIZATION_DETAIL(CUSTOMIZATION_ID)
                                                             REFERENCES CUSTOMER_FAV_CHAI_MAPPING(CUSTOMIZATION_ID)
                                                             ON DELETE RESTRICT
                                                             ON UPDATE CASCADE
);
ALTER TABLE KETTLE_DEV.FAV_CHAI_CUSTOMIZATION_DETAIL ADD COLUMN PRODUCT_SOURCE_SYSTEM VARCHAR(15) NULL;
ALTER TABLE KETTLE_DEV.FAV_CHAI_CUSTOMIZATION_DETAIL ADD COLUMN DIMENSION VARCHAR(15) NULL;
ALTER TABLE KETTLE_DEV.FAV_CHAI_CUSTOMIZATION_DETAIL ADD COLUMN QUANTITY DECIMAL(10, 2) NULL;
ALTER TABLE KETTLE_DEV.FAV_CHAI_CUSTOMIZATION_DETAIL ADD COLUMN ADDON_NAME VARCHAR(55) NULL;
ALTER TABLE KETTLE_DEV.FAV_CHAI_CUSTOMIZATION_DETAIL ADD COLUMN ADDON_TYPE VARCHAR(15) NULL;
ALTER TABLE KETTLE_DEV.FAV_CHAI_CUSTOMIZATION_DETAIL ADD COLUMN DEFAULT_SETTING VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_DEV.FAV_CHAI_CUSTOMIZATION_DETAIL ADD COLUMN SHORT_CODE VARCHAR(25);


ALTER TABLE KETTLE_DEV.CUSTOMER_FAV_CHAI_MAPPING ADD COLUMN  PRICE DECIMAL(10,2) NOT NULL AFTER PRODUCT_NAME;
ALTER TABLE KETTLE_DEV.FAV_CHAI_CUSTOMIZATION_DETAIL ADD COLUMN  UNIT_OF_MEASURE varchar(10) ;

