
INSERT INTO KETTLE_MASTER_DEV.DESIGNATION
VALUES(1009,'Admin','General <PERSON><PERSON> Ad<PERSON>','Y','Y','Y','Y','Y','Y');

INSERT INTO KETTLE_MASTER_DEV.DEPARTMENT_DESIGNATION_MAPPING
VALUES(102,1009);

# Reference Name Update
UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL
SET UNIT_REFERENCE_NAME = UNIT_NAME;


#-----------------------------------------

DROP PROCEDURE IF EXISTS EXPIRE_CASH_CARD;

DELIMITER $$
CREATE PROCEDURE EXPIRE_CASH_CARD()
proc_label : BEGIN

UPDATE KETTLE.CASH_CARD_DETAIL
SET CARD_STATUS = 'EXPIRED'
WHERE END_DATE < DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
AND CARD_STATUS <> 'EXPIRED';

<PERSON><PERSON>$$
DELIMITER ;


#------------------------------------------

ALTER TABLE ORDER_SETTLEMENT
ADD COLUMN EXTERNAL_TRANSACTION_ID VARCHAR(30);
