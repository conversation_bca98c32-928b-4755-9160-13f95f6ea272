ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_DETAIL
    CHANGE COLUMN EVENT_TYPE EVENT_TYPE VARCHAR(15) NULL DEFAULT NULL ;


CREATE TABLE `KETTLE_DEV`.`ORDER_FEEDBACK_RESPONSE` (
`ORDER_FEEDBACK_RESPONSE_ID` INT(11) NOT NULL AUTO_INCREMENT,
`FEEDBACK_ID` INT(11) NOT NULL,
`ORDER_ID` INT(11) NOT NULL,
`CUSTOMER_ID` INT(11) DEFAULT NULL,
`CUSTOMER_NAME` VARCHAR(50) DEFAULT NULL,
`FEED<PERSON>CK_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
`ORDER_RATING` INT(11) NOT NULL,
`ORDER_COMMENT` VARCHAR(200) DEFAULT NULL,
`USER_AGENT` VARCHAR(20) DEFAULT NULL,
`REDIRECT_URL` VARCHAR(200) NOT NULL,
`CHANNEL_PARTNER_CODE` VARCHAR(100) NOT NULL,
`CHANNEL_PARTNER_NAME` VARCHAR(100) NOT NULL,
`ORDER_SOURCE` VARCHAR(50) NOT NULL,
`CUSTOMER_CALLBACK` VARCHAR(1) NOT NULL DEFAULT 'N',
PRIMARY KEY (`ORDER_FEEDBACK_RESPONSE_ID`)
);

CREATE TABLE `KETTLE_DEV`.`ORDER_ITEM_FEEDBACK_RESPONSE` (
`ORDER_ITEM_FEEDBACK_RESPONSE_ID` INT(11) NOT NULL AUTO_INCREMENT,
`ORDER_FEEDBACK_RESPONSE_ID` INT(11) NOT NULL,
`ORDER_ITEM_ID` INT(11) NOT NULL,
`PRODUCT_ID` INT(11) NOT NULL,
`PRODUCT_NAME` VARCHAR(100) NOT NULL,
`QUANTITY` INT(11) NOT NULL,
`ISSUE_TAGS` VARCHAR(200) DEFAULT NULL,
`DIMENSION` VARCHAR(100) DEFAULT NULL,
`CUSTOMISATION` VARCHAR(500) DEFAULT NULL,
`QUESTION` VARCHAR(200) NOT NULL,
`ITEM_RATING` INT(11) DEFAULT NULL,
`ITEM_COMMENT` VARCHAR(500) DEFAULT NULL,
`IMAGE_URL` VARCHAR(500) DEFAULT NULL,
`VIDEO_URL` VARCHAR(500) DEFAULT NULL,
PRIMARY KEY (`ORDER_ITEM_FEEDBACK_RESPONSE_ID`)
);

ALTER TABLE `KETTLE_DEV`.`ORDER_FEEDBACK_RESPONSE`
ADD COLUMN `MAX_RATING` INT(11) NULL AFTER `CUSTOMER_CALLBACK`,
ADD COLUMN `FEEDBACK_TYPE` VARCHAR(45) NULL AFTER `MAX_RATING`;

ALTER TABLE `KETTLE_DEV`.`ORDER_ITEM_FEEDBACK_RESPONSE`
ADD COLUMN `MAX_RATING` INT(11) NULL AFTER `VIDEO_URL`,
ADD COLUMN `FEEDBACK_TYPE` VARCHAR(45) NULL AFTER `MAX_RATING`;

ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_RESPONSE ADD ORDER_NPS_RATING INT(11);
ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_RESPONSE ADD MAX_NPS_RATING INT(11);
ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_RESPONSE ADD HAS_ORDER_RATING VARCHAR(1);
ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_RESPONSE ADD HAS_ORDER_NPS_RATING VARCHAR(1);

ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_DETAIL ADD MAX_RATING INT(11);
ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_DETAIL ADD RATING_TYPE VARCHAR(20);
ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_EVENT ADD MAX_RATING INT(11);
ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_EVENT ADD RATING_TYPE VARCHAR(20);


