ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL 
ADD COLUMN BRAND_ID INTEGER NULL,
ADD COLUMN UNIT_ID INTEGER NULL,
ADD COLUMN FIRST_NAME VARCHAR(100) NULL;

CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_BRAND_ID ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(BRAND_ID) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_UNIT_ID ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(UNIT_ID) USING BTREE;

UPDATE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL  c, KETTLE_DEV.CUSTOMER_INFO ci
SET c.FIRST_NAME = ci.FIRST_NAME
where c.CUSTOMER_ID = ci.CUSTOMER_ID;

UPDATE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL  c, KET<PERSON><PERSON>_DEV.ORDER_DETAIL od
SET c.UNIT_ID = od.UNIT_ID,
c.BRAND_ID = od.BRAND_ID
where c.OFFER_CREATE_ORDER_ID = od.ORDER_ID;
