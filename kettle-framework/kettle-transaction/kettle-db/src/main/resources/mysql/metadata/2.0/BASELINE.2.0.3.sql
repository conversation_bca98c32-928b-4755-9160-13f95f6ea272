DROP TABLE IF EXISTS ITEM_CONSUMPTION_ESTIMATE;
CREATE TABLE ITEM_CONSUMPTION_ESTIMATE(
ITEM_CONSUMPTION_ESTIMATE_ID INTEGER  PRIMARY KEY NOT NULL AUTO_INCREMENT,
<PERSON>IT_ID INTEGER NOT NULL,
UNIT_NAME VARCHAR(100) NOT NULL,
PRODUCT_ID  INTEGER NOT NULL,
PRODUCT_NAME VARCHAR(100) NOT NULL,
DIMENSION VARCHAR(15) NOT NULL,
DAY_OF_WEEK INTEGER NOT NULL,
TOTAL_QUANTITY DECIMAL(10,2) NULL,
QUANTITY_DINE_IN DECIMAL(10,2) NULL,
QUANTITY_DELIVERY DECIMAL(10,2) NULL,
QUANTITY_TAKEAWAY DECIMAL(10,2) NULL,
ITEM_STATUS VARCHAR(15) NOT NULL,
CREATION_TIME TIMESTAMP NOT NULL
);

DROP PROCEDURE IF EXISTS ITEM_CONSUMPTION_ESTIMATE_CALCULATE;
DELIM<PERSON>ER $$
CREATE DEFINER=`root`@`%` PROCEDURE `ITEM_CONSUMPTION_ESTIMATE_CALCULATE`(IN BIZ_DATE DATE)
proc_label : BEGIN

update ITEM_CONSUMPTION_ESTIMATE
set ITEM_STATUS = 'IN_ACTIVE'
where ITEM_STATUS = 'ACTIVE';

INSERT INTO ITEM_CONSUMPTION_ESTIMATE (
UNIT_ID,
UNIT_NAME, 
PRODUCT_ID,
PRODUCT_NAME,
DIMENSION,
DAY_OF_WEEK,
TOTAL_QUANTITY,
QUANTITY_DINE_IN,
QUANTITY_DELIVERY,
QUANTITY_TAKEAWAY,
ITEM_STATUS,
CREATION_TIME)
SELECT 
    b.UNIT_ID,
    ud.UNIT_NAME,
    b.PRODUCT_ID,
    pd.PRODUCT_NAME,
    b.DIMENSION,
    b.DAY_OF_WEEK,
	(b.CAFE_QUANTITY + b.COD_QUANTITY + b.TAKE_AWAY_QUANTITY)/3, 
	b.CAFE_QUANTITY /3, 
    b.COD_QUANTITY /3, 
    b.TAKE_AWAY_QUANTITY/3, 
    'ACTIVE',
    CURRENT_TIMESTAMP
FROM
    (SELECT 
        m.UNIT_ID,
            MAX(CASE
                WHEN m.ORDER_SOURCE = 'CAFE' THEN m.SUM_QUANTITY
                ELSE 0
            END) CAFE_QUANTITY,
            MAX(CASE
                WHEN m.ORDER_SOURCE = 'COD' THEN m.SUM_QUANTITY
                ELSE 0
            END) COD_QUANTITY,
            MAX(CASE
                WHEN m.ORDER_SOURCE = 'TAKE_AWAY' THEN m.SUM_QUANTITY
                ELSE 0
            END) TAKE_AWAY_QUANTITY,
            m.PRODUCT_ID,
            m.DIMENSION,
            m.DAY_OF_WEEK
    FROM
        (SELECT 
        a.UNIT_ID,
            a.ORDER_SOURCE,
            a.PRODUCT_ID,
            a.DIMENSION,
            a.DAY_OF_WEEK,
            sum(a.TOTAL_QUANTITY) SUM_QUANTITY
            FROM
    
    (SELECT 
        od.UNIT_ID UNIT_ID,
            od.ORDER_SOURCE ORDER_SOURCE,
            pd.PRODUCT_ID PRODUCT_ID,
            oi.DIMENSION DIMENSION,
            DAYOFWEEK(CASE
                WHEN HOUR(od.BILLING_SERVER_TIME) < 5 THEN SUBDATE(DATE(od.BILLING_SERVER_TIME), 1)
                ELSE DATE(od.BILLING_SERVER_TIME)
            END) DAY_OF_WEEK,
            SUM(oi.QUANTITY) TOTAL_QUANTITY
    FROM
        KETTLE_DEV.ORDER_DETAIL od, KETTLE_DEV.ORDER_ITEM oi, KETTLE_MASTER_DEV.PRODUCT_DETAIL pd
    WHERE
        od.ORDER_ID = oi.ORDER_ID
            AND pd.PRODUCT_ID = oi.PRODUCT_ID
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(BIZ_DATE, 21))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = BIZ_DATE)
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND pd.PRODUCT_TYPE != 8
            AND od.TAXABLE_AMOUNT < 3000.00
    GROUP BY od.UNIT_ID , od.ORDER_SOURCE , pd.PRODUCT_ID , oi.DIMENSION , DAY_OF_WEEK
    union ALL
    SELECT 
			od.UNIT_ID UNIT_ID,
            od.ORDER_SOURCE ORDER_SOURCE,
            pd1.PRODUCT_ID PRODUCT_ID,
            CASE
				WHEN
                   pd1.PRODUCT_ID = 651
                THEN  
                'Double'
                WHEN
                    od.ORDER_SOURCE <> 'COD'
                        AND pd1.PRODUCT_TYPE NOT IN (7 , 8, 10)
                THEN
                    'Regular'
                WHEN
                    od.ORDER_SOURCE = 'COD'
                        AND pd1.PRODUCT_TYPE NOT IN (7 , 8, 10)
                THEN
                    'ChotiKetli'
                WHEN pd1.PRODUCT_TYPE IN (7 , 8, 10) THEN 'None'
            END DIMENSION,
            DAYOFWEEK(CASE
                WHEN HOUR(od.BILLING_SERVER_TIME) < 5 THEN SUBDATE(DATE(od.BILLING_SERVER_TIME), 1)
                ELSE DATE(od.BILLING_SERVER_TIME)
            END) DAY_OF_WEEK,
            SUM(oi.QUANTITY) TOTAL_QUANTITY
    FROM
        KETTLE_DEV.ORDER_DETAIL od
    INNER JOIN KETTLE_DEV.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_DEV.ORDER_ITEM_ADDON oia ON oi.ORDER_ITEM_ID = oia.ORDER_ITEM_ID
    INNER JOIN KETTLE_MASTER_DEV.REF_LOOKUP rl ON rl.RL_ID = oia.ADDON_ID
    INNER JOIN KETTLE_MASTER_DEV.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
    INNER JOIN KETTLE_MASTER_DEV.ADDON_PRODUCT_DATA apd ON oia.ADDON_ID = apd.ADDON_ID
    INNER JOIN KETTLE_MASTER_DEV.PRODUCT_DETAIL pd1 ON pd1.PRODUCT_ID = apd.PRODUCT_ID
    WHERE
        od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(BIZ_DATE, 21))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = BIZ_DATE)
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND pd.PRODUCT_TYPE = 8
            AND od.TAXABLE_AMOUNT < 3000.00
    GROUP BY od.UNIT_ID , od.ORDER_SOURCE , pd1.PRODUCT_ID , DIMENSION , DAY_OF_WEEK)a
    group by a.UNIT_ID,
            a.ORDER_SOURCE,
            a.PRODUCT_ID,
            a.DIMENSION,
            a.DAY_OF_WEEK
	) m
    GROUP BY m.UNIT_ID , m.PRODUCT_ID , m.DIMENSION , m.DAY_OF_WEEK) b,
    KETTLE_MASTER_DEV.PRODUCT_DETAIL pd, KETTLE_MASTER_DEV.UNIT_DETAIL ud where b.PRODUCT_ID = pd.PRODUCT_ID
    and b.UNIT_ID = ud.UNIT_ID;
 
END$$
DELIMITER ;



#Recipe saved to Order Item
alter table KETTLE_DEV.ORDER_ITEM add column RECIPE_ID INTEGER NULL ;


#Increased_Decimal_Values
ALTER TABLE KETTLE_DEV.ORDER_ITEM_ADDON MODIFY COLUMN QUANTITY DECIMAL(16,6) ;
