CREATE INDEX IS_EMAIL_DELIVERED_KEY ON KETTLE_DEV.ORDER_EMAIL_NOTIFICATION (IS_EMAIL_DELIVERED) USING BTREE;
CREATE INDEX ORDER_EMAIL_RETRY_COUNT_KEY ON KETTLE_DEV.ORDER_EMAIL_NOTIFICATION (RETRY_COUNT) USING BTREE;
CREATE INDEX ORDER_EMAIL_EXECUTION_TIME_KEY ON KETTLE_DEV.ORDER_EMAIL_NOTIFICATION (EXECUTION_TIME) USING BTREE;

ALTER TABLE KETTLE_DEV.ORDER_EMAIL_NOTIFICATION add column CONTACT VARCHAR(15);
ALTER TABLE KETTLE_DEV.ORDER_EMAIL_NOTIFICATION add column CUSTOMER_NAME VARCHAR(45);
ALTER TABLE KETTLE_DEV.UNIT_CLOSURE_DETAILS add column REPORT_STATUS VARCHAR(45);

ALTER TABLE KETTLE_DEV.CASH_CARD_DETAIL
ADD COLUMN PURCHASE_ORDER_ID INTEGER NULL;

CREATE INDEX PURCHASE_ORDER_ID_CASH_CARD_DETAIL ON KETTLE_DEV.CASH_CARD_DETAIL(PURCHASE_ORDER_ID) USING BTREE;

ALTER TABLE KETTLE_DEV.ORDER_NPS_DETAIL
ADD COLUMN NPS_QUESTION VARCHAR(500) NULL,
ADD COLUMN NPS_RESPONSE VARCHAR(500) NULL;

ALTER TABLE `KETTLE_DEV`.`ORDER_DETAIL`  ADD INDEX `ORDER_SOURCE_ID_ORDER_DETAIL` USING BTREE (`ORDER_SOURCE_ID` ASC);

ALTER TABLE KETTLE_DEV.UNIT_CLOSURE_DETAILS
ADD COLUMN REPORT_STATUS VARCHAR(20) NULL;

CREATE INDEX REPORT_STATUS_UNIT_CLOSURE_DETAILS ON KETTLE_DEV.UNIT_CLOSURE_DETAILS(REPORT_STATUS) USING BTREE;


ALTER TABLE KETTLE_DEV.ORDER_ITEM
ADD COLUMN TAKE_AWAY VARCHAR(1) NULL;


ALTER TABLE KETTLE_DEV.SUBSCRIPTION_ITEM
ADD COLUMN TAKE_AWAY VARCHAR(1) NULL;

