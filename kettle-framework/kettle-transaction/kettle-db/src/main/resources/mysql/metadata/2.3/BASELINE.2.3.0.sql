ALTER TABLE KETTLE_DEV.UNIT_EXPENSE_DETAIL
ADD COLUMN PAYTM_CHARGES DECIMAL(10,2) NULL,
ADD COLUMN MOBIKWIK_CHARGES DECIMAL(10,2) NULL,
ADD COLUMN FREE_CHARGE_CHARGES DECIMAL(10,2) NULL,
ADD COLUMN DEL_TOTAL_TICKETS DECIMAL(10,2) NULL,
ADD COLUMN DEL_NET_TICKETS DECIMAL(10,2) NULL,
ADD COLUMN DEL_GMV DECIMAL(10,2) NULL,
ADD COLUMN DEL_SALES DECIMAL(10,2) NULL,
ADD COLUMN DEL_COGS DECIMAL(10,2) NULL,
ADD COLUMN DEL_UNSATISFIED_CUSTOMER_COST DECIMAL(10,2) NULL,
ADD COLUMN DEL_MARKETING_AND_SAMPLING DECIMAL(10,2) NULL,
ADD COLUMN DEL_PAYTM_CHARGES DECIMAL(10,2) NULL;


ALTER TABLE KETTLE_DEV.ASSEMBLY_LOG_DATA
ADD COLUMN TIME_TO_DELIVER INTEGER DEFAULT 0;



INSERT INTO KETTLE_MASTER_DEV.PAYMENT_MODE (PAYMENT_MODE_ID, MODE_NAME, MODE_TYPE, MODE_DESCRIPTION, SETTLEMENT_TYPE, GENERATE_PULL, COMMISSION_RATE, MODE_STATUS, MODE_CATEGORY) 
VALUES ('15', 'MobikwikOnline', 'CARD', 'MobikwikOnline', 'DEBIT', '1', '1.60', 'ACTIVE', 'ONLINE');
INSERT INTO KETTLE_MASTER_DEV.PAYMENT_MODE (PAYMENT_MODE_ID, MODE_NAME, MODE_TYPE, MODE_DESCRIPTION, SETTLEMENT_TYPE, GENERATE_PULL, COMMISSION_RATE, MODE_STATUS, MODE_CATEGORY) 
VALUES ('16', 'FreeCharge', 'CARD', 'FreeCharge', 'DEBIT', '1', '1.35', 'ACTIVE', 'OFFLINE');


ALTER TABLE KETTLE_DEV.UNIT_EXPENSE_DETAIL
ADD COLUMN PAYTM_ONLINE_CHARGES DECIMAL(10,2) NULL,
ADD COLUMN MOBIKWIK_ONLINE_CHARGES DECIMAL(10,2) NULL,
ADD COLUMN RAZORPAY_CHARGES DECIMAL(10,2) NULL;


ALTER TABLE KETTLE_DEV.UNIT_EXPENSE_DETAIL
ADD COLUMN DEL_PAYTM_ONLINE_CHARGES  DECIMAL(10,2) NULL,
ADD COLUMN DEL_MOBIKWIK_CHARGES  DECIMAL(10,2) NULL,
ADD COLUMN DEL_MOBIKWIK_ONLINE_CHARGES  DECIMAL(10,2) NULL,
ADD COLUMN DEL_RAZORPAY_CHARGES DECIMAL(10,2) NULL,
ADD COLUMN DEL_FREE_CHARGE_CHARGES DECIMAL(10,2) NULL;
