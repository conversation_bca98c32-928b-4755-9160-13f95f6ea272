DROP TABLE IF EXISTS SUBSCRIPTION_STATUS_EVENT;
DROP TABLE IF EXISTS SUBSCRIPTION_EVENT_DETAIL;
DROP TABLE IF EXISTS SUBSCRIPTION_SETTLEMENT;
DROP TABLE IF EXISTS SUBSCRIPTION_ITEM_ADDON;
DROP TABLE IF EXISTS SUBSCRIPTION_EVENT_ITEM;
DROP TABLE IF EXISTS SUBSCRIPTION_ITEM;
DROP TABLE IF EXISTS SUBSCRIPTION_DETAIL;

CREATE TABLE SUBSCRIPTION_DETAIL (
SUBSCRIPTION_ID INT NOT NULL AUTO_INCREMENT,
GENERATED_SUBSCRIPTION_ID VARCHAR(30) NOT NULL,
CUSTOMER_ID INT NOT NULL,
EMP_ID INT NOT NULL,
ORDER_SOURCE_ID VARCHAR(80) NULL,
SUBSCRIPTION_STATUS VARCHAR(15) NOT NULL DEFAULT 'CREATED',
CANC<PERSON>LATION_REASON VARCHAR(100) NULL,
CA<PERSON><PERSON>LED_BY INTEGER NULL,
CA<PERSON><PERSON><PERSON>TION_TIME TIMESTAMP NULL,
SETTLEMENT_TYPE VARCHAR(10) NULL,
UNIT_ID INT NOT NULL,
SUBSCRIPTION_CREATION_TIME TIMESTAMP NULL,
CHANNEL_PARTNER_ID INT NOT NULL DEFAULT 1,
TOTAL_AMOUNT DECIMAL(10,2) NULL,
TAXABLE_AMOUNT DECIMAL(10,2) NULL,
DISCOUNT_PERCENT DECIMAL(10,2) NULL,
DISCOUNT_AMOUNT DECIMAL(10,2) NULL,
DISCOUNT_REASON_ID INT NULL,
DISCOUNT_REASON VARCHAR(255) NULL,
NET_PRICE_VAT_PERCENT DECIMAL(10,2) NULL,
NET_PRICE_VAT_AMOUNT DECIMAL(10,2) NULL,
MRP_VAT_PERCENT DECIMAL(10,2) NULL,
MRP_VAT_AMOUNT DECIMAL(10,2) NULL,
SERVICE_TAX_PERCENT DECIMAL(10,2) NULL,
SERVICE_TAX_AMOUNT DECIMAL(10,2) NULL,
SURCHARGE_TAX_PERCENT DECIMAL(10,2) NULL,
SURCHARGE_TAX_AMOUNT DECIMAL(10,2) NULL,
GST_PERCENT DECIMAL(10,2) NULL,
GST_AMOUNT DECIMAL(10,2) NULL,
SERVICE_CHARGE_PERCENT DECIMAL(10,2) NULL,
SERVICE_CHARGE_AMOUNT DECIMAL(10,2) NULL,
SB_CESS_PERCENT DECIMAL(10,2) NULL,
SB_CESS_AMOUNT DECIMAL(10,2) NULL,
ROUND_OFF_AMOUNT DECIMAL(10,2) NULL,
SETTLED_AMOUNT DECIMAL(10,2) NULL,
PROMOTIONAL_DISCOUNT DECIMAL(10,2) NULL,
TOTAL_DISCOUNT DECIMAL(10,2) NULL,
SALE_AMOUNT DECIMAL(10,2) NULL,
DELIVERY_ADDRESS INTEGER NOT NULL,
ORDER_REMARK VARCHAR(1000) NULL,
OFFER_CODE VARCHAR(15) NULL,
START_DATE TIMESTAMP NOT NULL,
END_DATE TIMESTAMP NOT NULL,
SUBSCRIPTION_TYPE VARCHAR(10) NOT NULL,
LAST_UPDATE_TIME TIMESTAMP NULL,
PRIMARY KEY(SUBSCRIPTION_ID),
FOREIGN KEY FK_SUBSCRIPTION_UNIT(UNIT_ID)
	REFERENCES UNIT_DETAIL(UNIT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_SUBSCRIPTION_EMPLOYEE(EMP_ID)
	REFERENCES EMPLOYEE_DETAIL(EMP_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_SUBSCRIPTION_CHANNEL_PARTNER(CHANNEL_PARTNER_ID)
	REFERENCES CHANNEL_PARTNER(PARTNER_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_SUBSCRIPTION_DISCOUNT_CODE(DISCOUNT_REASON_ID)
	REFERENCES REF_LOOKUP(RL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE SUBSCRIPTION_EVENT_ITEM(
SUBSCRIPTION_EVENT_ITEM_ID INT NOT NULL AUTO_INCREMENT,
SUBSCRIPTION_ID INT NOT NULL,
EVENT_ITEM_TYPE VARCHAR(30) NOT NULL,
EVENT_ITEM_STATUS VARCHAR(15) NOT NULL,
EVENT_ITEM_VALUE INTEGER NOT NULL,
PRIMARY KEY (SUBSCRIPTION_EVENT_ITEM_ID ),
FOREIGN KEY FK_SUBSCRIPTION_EVENT_ITEM_SUBSCRIPTION(SUBSCRIPTION_ID)
	REFERENCES SUBSCRIPTION_DETAIL(SUBSCRIPTION_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE SUBSCRIPTION_ITEM(
SUBSCRIPTION_ITEM_ID INT NOT NULL AUTO_INCREMENT,
SUBSCRIPTION_ID INT NOT NULL,
PRODUCT_ID INT NOT NULL,
PRODUCT_NAME VARCHAR(255) NOT NULL,
QUANTITY INT NOT NULL,
PRICE DECIMAL(10,2) NOT NULL,
HAS_ADDON VARCHAR(1) NOT NULL DEFAULT 'N',
TOTAL_AMOUNT DECIMAL(10,2) NOT NULL,
DISCOUNT_PERCENT DECIMAL(10,2) NULL,
DISCOUNT_AMOUNT DECIMAL(10,2) NULL,
DISCOUNT_REASON_ID INT NULL,
DISCOUNT_REASON VARCHAR(255) NULL,
DIMENSION VARCHAR(10) NULL,
BILL_TYPE VARCHAR(10) NOT NULL DEFAULT 'NET_PRICE',
AMOUNT_PAID DECIMAL(10,2) NOT NULL,
PRIMARY KEY (SUBSCRIPTION_ITEM_ID ),
FOREIGN KEY FK_SUBSCRIPTION_ITEM_SUBSCRIPTION(SUBSCRIPTION_ID)
	REFERENCES SUBSCRIPTION_DETAIL(SUBSCRIPTION_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_SUBSCRIPTION_ITEM_PRODUCT(PRODUCT_ID)
	REFERENCES PRODUCT_DETAIL(PRODUCT_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_SUBSCRIPTION_ITEM_DISCOUNT_CODE(DISCOUNT_REASON_ID)
	REFERENCES REF_LOOKUP(RL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);
CREATE TABLE SUBSCRIPTION_SETTLEMENT(
SUBSCRIPTION_SETTLEMENT_ID INT NOT NULL AUTO_INCREMENT,
SUBSCRIPTION_ID INT NOT NULL,
PAYMENT_MODE_ID INT NOT NULL,
AMOUNT_PAID DECIMAL(10,2) NULL,
ROUND_OFF_AMOUNT DECIMAL(10,2) NULL,
PRIMARY KEY(SUBSCRIPTION_SETTLEMENT_ID),
FOREIGN KEY FK_SETTLEMENT_SUBSCRIPTION(SUBSCRIPTION_ID)
	REFERENCES SUBSCRIPTION_DETAIL(SUBSCRIPTION_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_SETTLEMENT_PAYMENT_MODE(PAYMENT_MODE_ID)
	REFERENCES PAYMENT_MODE(PAYMENT_MODE_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE

);

CREATE TABLE SUBSCRIPTION_ITEM_ADDON(
SUBSCRIPTION_ITEM_ADDON_ID INT NOT NULL AUTO_INCREMENT,
SUBSCRIPTION_ITEM_ID INT NOT NULL,
ADDON_ID INT NOT NULL,
PRIMARY KEY (SUBSCRIPTION_ITEM_ADDON_ID),
FOREIGN KEY FK_SUBSCRIPTION_ITEM_ADDON(SUBSCRIPTION_ITEM_ID)
	REFERENCES SUBSCRIPTION_ITEM(SUBSCRIPTION_ITEM_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE,
FOREIGN KEY FK_ADDON_ID(ADDON_ID)
	REFERENCES REF_LOOKUP(RL_ID)
    ON DELETE RESTRICT
    ON UPDATE CASCADE

);



CREATE TABLE SUBSCRIPTION_STATUS_EVENT (
  SUBSCRIPTION_STATUS_EVENT_ID INTEGER NOT NULL AUTO_INCREMENT,
  SUBSCRIPTION_ID INTEGER NOT NULL,
  EVENT_TYPE VARCHAR(30) DEFAULT NULL,
  EVENT_STATUS VARCHAR(15) DEFAULT NULL,
  GENERATED_BY INTEGER NOT NULL,
  REASON_TEXT VARCHAR(100) NOT NULL,
  ADD_TIME TIMESTAMP NOT NULL,
  EVENT_START_DATE TIMESTAMP NOT NULL,
  EVENT_END_DATE TIMESTAMP NOT NULL,
  LAST_UPDATE_TIME TIMESTAMP NOT NULL,
  PRIMARY KEY (SUBSCRIPTION_STATUS_EVENT_ID)
)
;
CREATE TABLE SUBSCRIPTION_EVENT_DETAIL (
  SUBSCRIPTION_EVENT_DETAIL_ID INTEGER NOT NULL AUTO_INCREMENT,
  SUBSCRIPTION_ID INTEGER NOT NULL,
  EVENT_ID INTEGER NOT NULL,
  RETRY_COUNT INTEGER NOT NULL,
  ORDER_ID INTEGER NULL,
  EVENT_STATUS VARCHAR(30) DEFAULT NULL,
  EVENT_SOURCE VARCHAR(30) DEFAULT NULL,
  REASON_TEXT VARCHAR(100) NULL,
  ADD_TIME TIMESTAMP NOT NULL,
  EVENT_TIME TIMESTAMP NOT NULL,
  LAST_UPDATE_TIME TIMESTAMP NULL,
  REGULAR_SCHEDULE_CHANGED VARCHAR(1) NOT NULL DEFAULT 'N',
  REMARK VARCHAR(10000) DEFAULT NULL,
  EVENT_DATE DATE NOT NULL,
  PRIMARY KEY (SUBSCRIPTION_EVENT_DETAIL_ID)
);

ALTER TABLE ORDER_DETAIL
ADD COLUMN SUBSCRIPTION_ID INTEGER NULL;

ALTER TABLE ORDER_EMAIL_NOTIFICATION
ADD COLUMN ENTRY_TYPE VARCHAR(15) NOT NULL DEFAULT 'ORDER';
