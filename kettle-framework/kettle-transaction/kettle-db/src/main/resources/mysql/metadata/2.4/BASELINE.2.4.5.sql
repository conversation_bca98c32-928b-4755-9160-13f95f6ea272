ALTER TABLE KETTLE_DEV.ORDER_PAYMENT_DETAIL 
ADD COLUMN CANCELLED_BY VARCHAR(15) NULL,
ADD COLUMN CANCELLATION_REASON VARCHAR(300) NULL,
ADD COLUMN CANCELLATION_TIME TIMESTAMP NULL;


ALTER TABLE KETTLE_DEV.ORDER_PAYMENT_DETAIL 
ADD COLUMN FAILURE_REASON VARCHAR(300) NULL,
ADD COLUMN FAILURE_TIME TIMESTAMP NULL;

ALTER TABLE KETTLE_DEV.ORDER_PAYMENT_DETAIL 
CHANGE COLUMN REDIRECT_URL REDIRECT_URL VARCHAR(1000) NULL DEFAULT NULL;

CREATE INDEX EXTERNAL_ORDER_ID_ORDER_PAYMENT_DETAIL ON KETTLE_DEV.ORDER_PAYMENT_DETAIL(EXTERNAL_ORDER_ID) USING BTREE;
CREATE INDEX REQUEST_STATUS_ORDER_PAYMENT_DETAIL ON KETTLE_DEV.ORDER_PAYMENT_DETAIL(REQUEST_STATUS) USING BTREE;
CREATE INDEX PARTNER_ORDER_ID_PAYMENT_DETAIL ON KETTLE_DEV.ORDER_PAYMENT_DETAIL(PARTNER_ORDER_ID) USING BTREE;
CREATE INDEX PARTNER_TRANSACTION_ID_PAYMENT_DETAIL ON KETTLE_DEV.ORDER_PAYMENT_DETAIL(PARTNER_TRANSACTION_ID) USING BTREE;
CREATE INDEX PAYMENT_STATUS_PAYMENT_DETAIL ON KETTLE_DEV.ORDER_PAYMENT_DETAIL(PAYMENT_STATUS) USING BTREE;

