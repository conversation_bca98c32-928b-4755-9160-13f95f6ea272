ALTER TABLE CLOSURE_PAYMENT_DETAILS DROP COLUMN UNIT_ID;
ALTER TABLE SETTLEMENT_DETAIL MODIFY COLUMN SETTLEMENT_AMOUNT FLOAT DEFAULT NULL;
ALTER TABLE SETTLEMENT_DETAIL MODIFY COLUMN TOTAL_AMOUNT FLOAT NOT NULL;
ALTER TABLE PULL_DENOMINATION MODIFY COLUMN PACKET_COUNT INT DEFAULT NULL;
ALTER TABLE PULL_DENOMINATION MODIFY COLUMN LOOSE_CURRENCY_COUNT INT DEFAULT NULL;
ALTER TABLE SETTLEMENT_DENOMINATION MODIFY COLUMN PACKET_COUNT INT DEFAULT NULL;
ALTER TABLE SETTLEMENT_DENOMINATION MODIFY COLUMN LOOSE_CURRENCY_COUNT INT DEFAULT NULL;

UPDATE DENOMINATION SET STATUS = 'ACTIVE' WHERE STATUS = 'IN_ACTIVE';

INSERT INTO `DENOMINATION` (`<PERSON>YMENT_MODE`, `DENOMINATION_TEXT`, `<PERSON><PERSON><PERSON><PERSON><PERSON>ION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('4', 'Fifty', 'FIFTY', '50', '1', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('4', 'Fouty Five', 'FOURTY_FIVE', '45', '2', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('4', 'Fourty', 'FOURTY', '40', '3', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('4', 'Thirty Five', 'THIRTY_FIVE', '35', '4', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('4', 'Thirty', 'THIRTY', '30', '5', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('4', 'Twenty Five', 'TWENTY_FIVE', '25', '6', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('4', 'Twenty', 'TWENTY', '20', '7', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('4', 'Fifteeen', 'FIFTEEN', '15', '8', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('4', 'Ten', 'TEN', '10', '9', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('5', 'Fifty', 'FIFTY', '50', '1', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('5', 'Fouty Five', 'FOURTY_FIVE', '45', '2', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('5', 'Fourty', 'FOURTY', '40', '3', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('5', 'Thirty Five', 'THIRTY_FIVE', '35', '4', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('5', 'Thirty', 'THIRTY', '30', '5', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('5', 'Twenty Five', 'TWENTY_FIVE', '25', '6', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('5', 'Twenty', 'TWENTY', '20', '7', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('5', 'Fifteeen', 'FIFTEEN', '15', '8', 'ACTIVE', '100');
INSERT INTO `DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('5', 'Ten', 'TEN', '10', '9', 'ACTIVE', '100');


DROP TABLE ORDER_PAYMENT_DENOMINATION;
CREATE TABLE ORDER_PAYMENT_DENOMINATION (
ID INT PRIMARY KEY AUTO_INCREMENT,
ORDER_ID INT NOT NULL,
SETTLEMENT_ID INT NOT NULL,
DENOMINATION_ID INT NOT NULL,
DENOMINATION_COUNT INT NOT NULL,
TOTAL_AMOUNT INT NOT NULL,
FOREIGN KEY (ORDER_ID) REFERENCES ORDER_DETAIL(ORDER_ID),
FOREIGN KEY (SETTLEMENT_ID) REFERENCES ORDER_SETTLEMENT(SETTLEMENT_ID),
FOREIGN KEY (DENOMINATION_ID) REFERENCES DENOMINATION(DENOMINATION_ID)
)


