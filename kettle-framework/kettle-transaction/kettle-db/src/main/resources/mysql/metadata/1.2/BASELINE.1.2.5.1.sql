UPDATE EMPLOYEE_DETAIL SET `EMPLOYMENT_STATUS`='INACTIVE' WHERE `EMP_ID`='100005';
UPDATE EMPLOYEE_DETAIL SET `EMPLOYMENT_STATUS`='INACTIVE' WHERE `EMP_ID`='100006';
UPDATE EMPLOYEE_DETAIL SET `EMPLOYMENT_STATUS`='INACTIVE' WHERE `EMP_ID`='100007';
UPDATE EMPLOYEE_DETAIL SET `EMPLOYMENT_STATUS`='INACTIVE' WHERE `EMP_ID`='100021';
UPDATE EMPLOYEE_DETAIL SET `EMPLOYMENT_STATUS`='INACTIVE' WHERE `EMP_ID`='100017';
UPDATE EMPLOYEE_DETAIL SET `DESIGNATION_ID`='1003' WHERE `EMP_ID`='100037';

INSERT INTO ADDRESS_INFO
(`ADDRESS_ID`,`ADDRESS_LINE_1`,`ADDRESS_LINE_2`,`ADDRESS_LINE_3`,`CITY`,`STATE`,`COUNTRY`,`ZIPCODE`,`CONTACT_NUM_1`,`CONTACT_NUM_2`)
VALUES
(112,'Shop No-13,Ground floor','New Friends Colony,Community Centre',null,'New Delhi','New Delhi','India','110025','+91-','+91-'),
(113,'Shop No-G 22','PP Towers,Netaji Subhash Place','Pitampura','New Delhi','New Delhi','India','110034','+91-','+91-'),
(10047,'A-2/42 Mohan Garden','Uttam Nagar',null,'New Delhi','New Delhi','India','110059','+91-9555862103','+91-9555862103'),
(10048,'26-A,ZamrudPur','GK-1 Near LSR College',null,'New Delhi','New Delhi','India','110059','+91-8130784789','+91-8130784789'),
(10049,'RZ-24, B BLOCK','GOPAL NAGAR, NAJAFGARH',null,'New Delhi','New Delhi','India','110043','+91-9999499770','+91-9990924836'),
(10050,'Sehatpur Krishna Colony H-No 2947','Gali No.1 Palla No.1',null,'Faridabad','Haryana','India','121003','+91-9582384181','+91-9990722498'),
(10051,'H-No: KC 684','Krishna colony',null,'Palwal','Haryana','India','121003','+91-9899357356','+91-9899357356'),
(10052,'B-200 GALI NO.2','PREM VIHAR KARAWAL NAGAR',null,'New Delhi','New Delhi','India','110094','+91-9818929698','+91-9818929698'),
(10053,'Block- T, house no-894','Mangolpuri',null,'New Delhi','New Delhi','India','110083','+91-9910525038','+91-9910525038');

INSERT INTO UNIT_DETAIL
(`UNIT_ID`,`UNIT_NAME`,`UNIT_REGION`,`UNIT_EMAIL`,`UNIT_CATEGORY`,`START_DATE`,`UNIT_STATUS`,`TIN`,`UNIT_ADDR_ID`,`BUSINESS_DIV_ID`)
VALUES
(10012,'New Friends Colony','NCR','<EMAIL>','CAFE','2015-11-15','Active','7426936970',112,1000),
(10013,'Netaji Subhash Place','NCR','<EMAIL>','CAFE','2015-11-15','Active','7426936970',113,1000);

INSERT INTO UNIT_TAX_MAPPING
(`TAX_PROFILE_ID`,`UNIT_ID`,`TAX_PERCENTAGE`,`PROFILE_STATUS`,`STATE`)
VALUES
(1,10012,12.50,'ACTIVE','NEW DELHI'),
(2,10012,5.00,'ACTIVE','NEW DELHI'),
(3,10012,0.00,'ACTIVE','NEW DELHI'),
(4,10012,5.60,'ACTIVE','NEW DELHI'),
(5,10012,0.00,'ACTIVE','NEW DELHI'),
(6,10012,0.20,'ACTIVE','NEW DELHI'),
(1,10013,12.50,'ACTIVE','NEW DELHI'),
(2,10013,5.00,'ACTIVE','NEW DELHI'),
(3,10013,0.00,'ACTIVE','NEW DELHI'),
(4,10013,5.60,'ACTIVE','NEW DELHI'),
(5,10013,0.00,'ACTIVE','NEW DELHI'),
(6,10013,0.20,'ACTIVE','NEW DELHI');

INSERT INTO EMPLOYEE_DETAIL
(`EMP_ID`,`EMP_NAME`,`EMP_GENDER`,`EMP_CURRENT_ADDR`,`EMP_PERMANENT_ADDR`,`EMP_CONTACT_NUM_1`,`EMP_CONTACT_NUM_2`,`DEPTARTMENT_ID`,
`DESIGNATION_ID`,`EMPLOYMENT_TYPE`,`EMPLOYMENT_STATUS`,`BIOMETRIC_IDENTIFIER`,`JOINING_DATE`,`TERMINATION_DATE`,`REPORTING_MANAGER_ID`)
VALUES
(100042,'Mukesh Kumar','M',10048,10048,'+91-8130784789','+91-8130784789',101,1003,'FULL_TIME','ACTIVE',null,'2015-10-05','9999-12-01',100011),
(100043,'Sunil Singh Pathania','M',10047,10047,'+91-9555862103','+91-9555862103',101,1002,'FULL_TIME','ACTIVE',null,'2015-06-04','9999-12-01',100031),
(100044,'Ajay Kumar','M',10049,10049,'+91-9999499770','+91-9990924836',101,1003,'FULL_TIME','ACTIVE',null,'2015-10-01','9999-12-01',100011),
(100045,'Harish Singh Bisht','M',10050,10050,'+91-9582384181','+91-9990722498',101,1002,'FULL_TIME','ACTIVE',null,'2015-10-19','9999-12-01',100042),
(100046,'Ashok Tanwar','M',10051,10051,'+91-9899357356','+91-9899357356',101,1002,'FULL_TIME','ACTIVE',null,'2015-10-05','9999-12-01',100042),
(100047,'Rahul Yadav','M',10052,10052,'+91-9818929698','+91-9818929698',101,1002,'FULL_TIME','ACTIVE',null,'2015-10-09','9999-12-01',100012),
(100048,'Arvind Kumar','M',10053,10053,'+91-9910525038','+91-9910525038',101,1002,'FULL_TIME','ACTIVE',null,'2015-10-09','9999-12-01',100031);

INSERT INTO `EMPLOYEE_UNIT_MAPPING`
(`EMP_ID`,
`UNIT_ID`)
VALUES
(100042,10012),
(100043,10013),
(100044,10006),
(100045,10013),
(100046,10012),
(100047,10004),
(100048,10013);
INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
VALUES
(100042,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100043,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100044,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100045,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100046,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100047,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(100048,'0UxQYmqlaaaY1jDVAZWNQQ==' );


INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 10012,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 10006;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 10006
and upm1.UNIT_ID = 10012;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 10013,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 10006;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 10006
and upm1.UNIT_ID = 10013;


INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) 
VALUES ('704', '7', 'BunMaska\'s', 'Bun Maska\'s', 'ACTIVE');
UPDATE PRODUCT_DETAIL SET `PRODUCT_SUB_TYPE`='704' WHERE `PRODUCT_ID`='671';
UPDATE PRODUCT_DETAIL SET `PRODUCT_SUB_TYPE`='704' WHERE `PRODUCT_ID`='641';
UPDATE PRODUCT_DETAIL SET `PRODUCT_SUB_TYPE`='704' WHERE `PRODUCT_ID`='680';
UPDATE PRODUCT_DETAIL SET `PRODUCT_SUB_TYPE`='704' WHERE `PRODUCT_ID`='690';
INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`) VALUES ('681', 'Bun Maska - Achari', '-----', '100', '7', '704', 'ACTIVE', 'VEG', '2015-11-21', '9999-12-01', 'CH1011102', '1', 'NET_PRICE', '24', '2015-08-12 11:54:05', '2030-12-01 00:00:00');
INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`) VALUES ('682', 'Bun Maska - Honey Lemon', '-----', '100', '7', '704', 'ACTIVE', 'VEG', '2015-11-21', '9999-12-01', 'CH1011103', '1', 'NET_PRICE', '24', '2015-08-12 11:54:05', '2030-12-01 00:00:00');
INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`) VALUES ('683', 'Bun Maska - Sundried Tomatoes & Olives', '-----', '100', '7', '704', 'ACTIVE', 'VEG', '2015-11-21', '9999-12-01', 'CH1011104', '1', 'NET_PRICE', '24', '2015-08-12 11:54:05', '2030-12-01 00:00:00');
INSERT INTO PRODUCT_DETAIL (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `VENDOR_ID`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `ATTRIBUTE`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`) VALUES ('684', 'Bun Maska - Mint Jalapeño', '-----', '100', '7', '704', 'ACTIVE', 'VEG', '2015-11-21', '9999-12-01', 'CH1011105', '1', 'NET_PRICE', '24', '2015-08-12 11:54:05', '2030-12-01 00:00:00');

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 681, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING 
where PRODUCT_ID = 10
and UNIT_ID NOT IN (10009,10010);

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 682, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING 
where PRODUCT_ID = 10
and UNIT_ID NOT IN (10009,10010);

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 683, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING 
where PRODUCT_ID = 10
and UNIT_ID NOT IN (10009,10010);

INSERT INTO UNIT_PRODUCT_MAPPING (UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select UNIT_ID, 684, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING 
where PRODUCT_ID = 10
and UNIT_ID NOT IN (10009,10010);

INSERT INTO `UNIT_PRODUCT_PRICING`
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
SELECT UNIT_PROD_REF_ID, 1, CURRENT_TIMESTAMP,79.00 FROM UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID = 681;
INSERT INTO `UNIT_PRODUCT_PRICING`
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
SELECT UNIT_PROD_REF_ID, 1, CURRENT_TIMESTAMP,79.00 FROM UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID = 682;
INSERT INTO `UNIT_PRODUCT_PRICING`
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
SELECT UNIT_PROD_REF_ID, 1, CURRENT_TIMESTAMP,99.00 FROM UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID = 683;
INSERT INTO `UNIT_PRODUCT_PRICING`
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
SELECT UNIT_PROD_REF_ID, 1, CURRENT_TIMESTAMP,99.00 FROM UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID = 684;

update UNIT_DETAIL
set NO_OF_TERMINALS =2
where UNIT_ID IN (10012,
10013);
INSERT INTO `EMPLOYEE_UNIT_MAPPING`
(`EMP_ID`,
`UNIT_ID`)
VALUES
(100031,10013);


update ADDRESS_INFO
SET CONTACT_NUM_1 = '+91-9599598314'
where ADDRESS_ID  = 113;

update ADDRESS_INFO
SET CONTACT_NUM_1 = '+91-9599598313'
where ADDRESS_ID  = 112;

UPDATE `KETTLE`.`UNIT_DETAIL` SET `TIN`='Applied For', `NO_OF_TERMINALS`='2' WHERE `UNIT_ID`='10010';

INSERT INTO REF_LOOKUP (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ('118', '4', 'SabKuch', 'Sab Kuch', 'SK', 'ACTIVE');
