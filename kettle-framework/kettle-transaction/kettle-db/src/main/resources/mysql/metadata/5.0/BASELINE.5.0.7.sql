
CREATE TABLE KETTLE_DEV.EXTERNAL_PARTNER_CARD_DETAIL(
CARD_DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
EXTERNAL_ORDER_ID VARCHAR(20), 
PARTNER_CARD_NUMBER VARCHAR(20), 
PARTNER_CODE VARCHAR(20) NULL,
REQUEST_SOURCE VARCHAR(20) NULL,
REQUEST_STATUS VARCHAR(30) NULL,
REQUEST_TIME TIMESTAMP NULL,
RESPONSE_TIME TIMESTAMP NULL,
PARTNER_TRANSACTION_ID VARCHAR(100) NULL,
REDIRECT_URL VARCHAR(100) NULL,
CANCELLED_BY VARCHAR(15) NULL,
CANCELLATION_REASON VARCHAR(300) NULL,
CANCELLATION_TIME TIMESTAMP NULL,
CUSTOMER_NAME VARCHAR(100) NULL,
CUSTOMER_ID INTEGER NULL,
TRANSACTION_AMOUNT DECIMAL (10,2) NULL,
CARD_NUMBER VARCHAR(20) NULL,
`RESPONSE_RESULT` BLOB NULL
);


CREATE TABLE KETTLE_MASTER_DEV.EXTERNAL_PARTNER_DETAIL(
DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
PARTNER_NAME VARCHAR(50), 
PARTNER_CODE VARCHAR(20), 
END_POINT VARCHAR(200),
LINKED_PRODUCT_ID INTEGER ,
LINKED_PAYMENT_MODE_ID INTEGER,
LINKED_CREDIT_ACCOUNT_ID INTEGER NULL,
USERNAME VARCHAR(100) NULL,
PASS_CODE VARCHAR(100) NULL,
CREATED_AT TIMESTAMP NULL,
STATUS VARCHAR(15) NULL
);

INSERT INTO `KETTLE_DEV`.`CREDIT_ACCOUNT_DETAIL` (`LEGAL_NAME`, `DISPLAY_NAME`, `ADDRESS`, `TAN_NUMBER`, `PAN_NUMBER`, `CERTIFICATE_OF_INCORPORATION`, `BANK_DETAIL`, `CONTACT_PERSON`, `CONTACT_PERSON_NUMBER`, `CONTACT_PERSON_EMAIL`, `ACCOUNT_CONTACT_PERSON`, `ACCOUNT_CONTACT_PERSON_NUMBER`, `CREDIT_DAYS`, `COMPANY_CONTACT`, `ACCOUNT_STATUS`) VALUES ('GYFTR', 'GYFTR', 'GYFTR', 'GYFTR', 'GYFTR', 'GYFTR', 'GYFTR', 'GYFTR', '**********', '<EMAIL>', 'GYFTR', '**********', '30', 'Lalit Kumar', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`PAYMENT_MODE` (`MODE_NAME`, `MODE_TYPE`, `MODE_DESCRIPTION`, `SETTLEMENT_TYPE`, `GENERATE_PULL`, `COMMISSION_RATE`, `MODE_STATUS`, `MODE_CATEGORY`, `AUTOMATIC_PULL_VALIDATE`, `AUTOMATIC_TRANSFER`, `AUTOMATIC_CLOSE_TRANSFER`, `IS_EDITABLE`, `NEEDS_SETTLEMENT_SLIP_NUMBER`, `VALIDATION_SOURCE`) VALUES ('GYFTR', 'GYFTR', 'GYFTR', 'CREDIT', '1', '0', 'ACTIVE', 'ONLINE', 'N', 'N', 'N', 'N', 'N', 'Settlement Report');

INSERT INTO `KETTLE_MASTER_DEV`.`EXTERNAL_PARTNER_DETAIL` (`PARTNER_NAME`, `PARTNER_CODE`, `END_POINT`, `LINKED_PRODUCT_ID`, `LINKED_PAYMENT_MODE_ID`, LINKED_CREDIT_ACCOUNT_ID, `USERNAME`, `PASS_CODE`,`CREATED_AT`, `STATUS`) 
VALUES ('GYFTR', 'GYFTR', 'https://pos-staging.vouchagram.net/Service/RestServiceImpl.svc', (SELECT PRODUCT_ID FROM KETTLE_MASTER_DEV.PRODUCT_DETAIL WHERE PRODUCT_NAME = 'GYFTR'), 
(SELECT PAYMENT_MODE_ID FROM KETTLE_MASTER_DEV.PAYMENT_MODE WHERE MODE_NAME = 'GYFTR'), (SELECT CREDIT_ACCOUNT_DETAIL_ID FROM  `KETTLE_DEV`.`CREDIT_ACCOUNT_DETAIL` WHERE  LEGAL_NAME = 'GYFTR'), 
'49F79EB8-061D-4A6F-B7FA-E5DC1088DBBE', 'DpWB+dGrxKnrWEEXPUZC/A==',now(), 'ACTIVE');


INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`, `DEFAULT_VALUE`) VALUES ('boolean', 'GYFTRACTIVE', 'gyftr.active', 'false');

INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_VALUE` (`ATTRIBUTE_DEF_ID`, `ATTRIBUTE_VALUE`, `APPLICATION_NAME`) VALUES ((SELECT ATTRIBUTE_DEF_ID from  `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` WHERE ATTRIBUTE_NAME = 'gyftr.active'), 'true', 'KETTLE_SERVICE');





