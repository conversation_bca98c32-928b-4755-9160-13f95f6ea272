CREATE INDEX INDEX_FEEDBACK_ID_EVENT ON ORDER_FEEDBACK_EVENT (FEEDBACK_ID) USING BTREE;

ALTER TABLE KETTLE_DUMP.ORDER_DETAIL
        MODIFY COLUMN CANCELATION_REASON VARCHAR(250) NULL;
        
        ALTER TABLE KETTLE_DUMP.ORDER_FEEDBACK_DETAIL
        MODIFY COLUMN FEEDBACK_CREATION_TIME TIMESTAMP NULL;
        
         ALTER TABLE KETTLE_DUMP.ORDER_FEEDBACK_EVENT
        MODIFY COLUMN EVENT_GENERATION_TIME TIMESTAMP NULL;
	 
        ALTER TABLE KETTLE_DUMP.ORDER_FEEDBACK_EVENT
        MODIFY COLUMN EVENT_TRIGGER_TIME TIMESTAMP NULL;
        
         ALTER TABLE KETTLE_DUMP.ORDER_FEEDBACK_EVENT
        MODIFY COLUMN EVENT_NOTIFICATION_TIME TIMESTAMP NULL;
        
          ALTER TABLE KETTLE_DUMP.ORDER_FEEDBACK_EVENT
        MODIFY COLUMN EVENT_COMPLETION_TIME TIMESTAMP NULL;
	



update ORDER_FEEDBACK_EVENT ofe, ORDER_FEEDBACK_EVENT ofp 
set ofe.FEEDBACK_RATING = ofp.FEEDBACK_RATING
where ofe.FEEDBACK_ID = ofp.FEEDBACK_ID and ofe.EVENT_TYPE = 'ELABORATED' and ofp.EVENT_TYPE = 'REGULAR'
and ofe.EVENT_SOURCE = 'SMS' and ofp.EVENT_SOURCE = 'POS'
and ofp.EVENT_STATUS = 'SUCCESSFUL'
and ofe.FEEDBACK_RATING is null;

update ORDER_FEEDBACK_EVENT ofe, ORDER_FEEDBACK_EVENT ofp 
set ofe.FEEDBACK_RATING = ofp.FEEDBACK_RATING
where ofe.FEEDBACK_ID = ofp.FEEDBACK_ID and ofe.EVENT_TYPE = 'ELABORATED' and ofp.EVENT_TYPE = 'REGULAR'
and ofe.EVENT_SOURCE = 'SMS' and ofp.EVENT_SOURCE = 'POS'
and ofp.EVENT_STATUS = 'CANCELLED'
and ofe.FEEDBACK_RATING is null;

update ORDER_FEEDBACK_DETAIL ofd, ORDER_FEEDBACK_EVENT ofe
set ofd.FEEDBACK_RATING = ofe.FEEDBACK_RATING
 where ofd.LATEST_FEEDBACK_INFO_ID is not null and ofd.FEEDBACK_RATING is null
 and ofd.LATEST_FEEDBACK_INFO_ID = ofe.LATEST_FEEDBACK_INFO_ID

 update FEEDBACK_INFO fi, ORDER_FEEDBACK_EVENT ofe
set fi.FEEDBACK_RATING = ofe.FEEDBACK_RATING
where fi.FEEDBACK_INFO_ID = ofe.LATEST_FEEDBACK_INFO_ID
and fi.FEEDBACK_RATING is null;

 update FEEDBACK_RESPONSE fi, FEEDBACK_INFO ofe
 set fi.FEEDBACK_RATING = ofe.FEEDBACK_RATING
 where fi.FEEDBACK_INFO_ID = ofe.FEEDBACK_INFO_ID
 and fi.FEEDBACK_RATING is null;

 
  update FEEDBACK_RESPONSE_DATA fi, FEEDBACK_RESPONSE ofe
 set fi.FEEDBACK_RATING = ofe.FEEDBACK_RATING
 where fi.FEEDBACK_RESPONSE_ID = ofe.FEEDBACK_RESPONSE_ID
 and fi.FEEDBACK_RATING is null;


update FEEDBACK_RESPONSE_DATA
set FEEDBACK_RATING = 5 
where RESPONSE_DATA = '5'
and FEEDBACK_RATING is null;

update FEEDBACK_RESPONSE_DATA
set FEEDBACK_RATING = 4 
where RESPONSE_DATA = '4'
and FEEDBACK_RATING is null;

update FEEDBACK_RESPONSE_DATA
set FEEDBACK_RATING = 3 
where RESPONSE_DATA = '3'
and FEEDBACK_RATING is null;

update FEEDBACK_RESPONSE_DATA
set FEEDBACK_RATING = 2 
where RESPONSE_DATA = '2'
and FEEDBACK_RATING is null;

update FEEDBACK_RESPONSE_DATA
set FEEDBACK_RATING = 1 
where RESPONSE_DATA = '1'
and FEEDBACK_RATING is null;


## Run multiple times till the result comes out to be zero rows.


UPDATE FEEDBACK_RESPONSE_DATA frd,
    (SELECT 
        (FEEDBACK_RESPONSE_DATA_ID + 1) TEMP_FEEDBACK_RESPONSE_DATA,
            FEEDBACK_RATING
    FROM
        FEEDBACK_RESPONSE_DATA frd, (SELECT 
        FEEDBACK_RESPONSE_DATA_ID - 1 FEEDBACK_RESPONSE_DATA_TEMP
    FROM
        FEEDBACK_RESPONSE_DATA
    WHERE
        FEEDBACK_RATING IS NULL) a
    WHERE
        frd.FEEDBACK_RESPONSE_DATA_ID = a.FEEDBACK_RESPONSE_DATA_TEMP) b 
SET 
    frd.FEEDBACK_RATING = b.FEEDBACK_RATING
WHERE
    frd.FEEDBACK_RESPONSE_DATA_ID = b.TEMP_FEEDBACK_RESPONSE_DATA;


     update FEEDBACK_RESPONSE fr, ( 
    select FEEDBACK_RESPONSE_ID, max(FEEDBACK_RATING) FEEDBACK_RATING from FEEDBACK_RESPONSE_DATA group by FEEDBACK_RESPONSE_ID) a
    set fr.FEEDBACK_RATING =  a.FEEDBACK_RATING
    where fr.FEEDBACK_RATING is null
    and a.FEEDBACK_RESPONSE_ID = fr.FEEDBACK_RESPONSE_ID

    UPDATE FEEDBACK_RESPONSE 
SET 
    FEEDBACK_FIELD_ID = 18
WHERE
    FEEDBACK_FIELD_ID = 2
        AND FEEDBACK_RATING = 2;
         UPDATE FEEDBACK_RESPONSE 
SET 
    FEEDBACK_FIELD_ID = 19
WHERE
    FEEDBACK_FIELD_ID = 2
        AND FEEDBACK_RATING = 3;
         UPDATE FEEDBACK_RESPONSE 
SET 
    FEEDBACK_FIELD_ID = 20
WHERE
    FEEDBACK_FIELD_ID = 2
        AND FEEDBACK_RATING = 4;
         UPDATE FEEDBACK_RESPONSE 
SET 
    FEEDBACK_FIELD_ID = 21
WHERE
    FEEDBACK_FIELD_ID = 2
        AND FEEDBACK_RATING = 5;

UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='205';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='229';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='257';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='320';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='342';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='356';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='371';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='414';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='491';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='513';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='568';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='585';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='630';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='647';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='7' WHERE `FEEDBACK_RESPONSE_ID`='692';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='709';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='769';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='788';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='822';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='843';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='875';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='892';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='912';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='937';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='959';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='978';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='2' WHERE `FEEDBACK_RESPONSE_ID`='1062';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='1133';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='1276';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='1287';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='1320';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='1322';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='1323';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='1324';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='1431';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='1485';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='1487';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='2' WHERE `FEEDBACK_RESPONSE_ID`='1918';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='2343';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='2379';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='2' WHERE `FEEDBACK_RESPONSE_ID`='2424';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='2' WHERE `FEEDBACK_RESPONSE_ID`='2425';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='2631';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='3114';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='7060';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='6978';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='6976';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='6961';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='6954';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='2' WHERE `FEEDBACK_RESPONSE_ID`='6910';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='2' WHERE `FEEDBACK_RESPONSE_ID`='6908';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='6850';

UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='3328';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='3494';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='3593';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='3842';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='3843';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='3844';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='3845';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='3870';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='3921';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='4009';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='4010';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='4082';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='4085';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='4105';

UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='4133';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='4134';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='4135';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='4189';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='4198';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='4236';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='4237';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='4318';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='4489';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='4709';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='4845';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='4863';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='4865';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='4986';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='4996';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='2' WHERE `FEEDBACK_RESPONSE_ID`='5021';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='5160';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='5201';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='5376';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='5379';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='5469';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='5470';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='5513';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='5729';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='4' WHERE `FEEDBACK_RESPONSE_ID`='5740';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='6638';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='6639';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='6701';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='2' WHERE `FEEDBACK_RESPONSE_ID`='6703';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='6812';

UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='3' WHERE `FEEDBACK_RESPONSE_ID`='443';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='3058';

UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='5' WHERE `FEEDBACK_RESPONSE_ID`='1835';
UPDATE `KETTLE_DUMP`.`FEEDBACK_RESPONSE` SET `FEEDBACK_RATING`='1' WHERE `FEEDBACK_RESPONSE_ID`='2123';
delete from FEEDBACK_RESPONSE_DATA where RESPONSE_DATA = '0';