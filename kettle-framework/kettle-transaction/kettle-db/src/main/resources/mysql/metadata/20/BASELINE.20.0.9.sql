CREATE TABLE KETTLE_DEV.SPECIAL_OFFER_DETAIL (
SPECIAL_OFFER_ID int(11) NOT NULL AUTO_INCREMENT,
CUSTOMER_ID int(11) DEFAULT NULL,
CONTACT_NUMBER varchar(12) DEFAULT NULL,
OFFER_TYPE varchar(30) DEFAULT NULL,
CAMPAIGN_ID int(11) DEFAULT NULL,
CAMPAIGN_STRATEGY varchar(30) DEFAULT NULL,
RECORD_TIME timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
UTM_SOURCE varchar(50) DEFAULT NULL,
UTM_MEDIUM varchar(50) DEFAULT NULL,
RECORD_STATUS varchar(50) DEFAULT NULL,
CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID int(11) DEFAULT NULL,
SUBSCRIPTION_PLAN_ID int(11) DEFAULT NULL,
CASH_PACKET_ID int(11) DEFAULT NULL,
MAX_USAGE int(11) DEFAULT NULL,
START_DATE varchar(15) DEFAULT NULL,
END_DATE varchar(15) DEFAULT NULL,
COUPON_CODE varchar(30) DEFAULT NULL,
OFFER_TEXT varchar(65) DEFAULT NULL,
CASH_AMOUNT decimal(10,2) DEFAULT NULL,
PRIMARY KEY (SPECIAL_OFFER_ID),
KEY SPECIAL_OFFER_DETAIL_CASH_AMOUNT (CASH_AMOUNT) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=latin1;

CREATE INDEX SPECIAL_OFFER_DETAIL_SPECIAL_OFFER_ID ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(SPECIAL_OFFER_ID) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_CUSTOMER_ID ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(CUSTOMER_ID) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_CONTACT_NUMBER ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(CONTACT_NUMBER) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_OFFER_TYPE ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(OFFER_TYPE) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_RECORD_TIME ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(RECORD_TIME) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_UTM_SOURCE ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(UTM_SOURCE) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_UTM_MEDIUM ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(UTM_MEDIUM) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_RECORD_STATUS ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(RECORD_STATUS) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_SUBSCRIPTION_PLAN_ID ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(SUBSCRIPTION_PLAN_ID) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_MAX_USAGE ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(MAX_USAGE) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_START_DATE ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(START_DATE) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_COUPON_CODE ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(COUPON_CODE) USING BTREE;
CREATE INDEX SPECIAL_OFFER_DETAIL_OFFER_TEXT ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(OFFER_TEXT) USING BTREE;

ALTER TABLE KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT ADD COLUMN CAMPAIGN_ID INT(11);
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_CASH_AMOUNT ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(CAMPAIGN_ID) USING BTREE;

ALTER TABLE KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT ADD COLUMN SUBSCRIPTION_SOURCE VARCHAR(30);
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_CASH_AMOUNT ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(SUBSCRIPTION_SOURCE) USING BTREE;

ALTER TABLE KETTLE_DEV.LOYALTY_SCORECHANGE COLUMN SIGNUP_OFFER_STATUS SIGNUP_OFFER_STATUS VARCHAR(50) NULL DEFAULT NULL ;

ALTER TABLE KETTLE_DEV.SPECIAL_OFFER_DETAIL ADD COLUMN TNC VARCHAR(500);
CREATE INDEX SPECIAL_OFFER_DETAIL_TNC ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(TNC) USING BTREE;

ALTER TABLE KETTLE_DEV.SPECIAL_OFFER_DETAIL ADD COLUMN FLOW INT(11);
CREATE INDEX SPECIAL_OFFER_DETAIL_FLOW ON KETTLE_DEV.SPECIAL_OFFER_DETAIL(FLOW) USING BTREE;

ALTER TABLE KETTLE_DEV.SETTLEMENT_DETAIL ADD COLUMN SETTLEMENT_DATE DATE;

ALTER TABLE KETTLE_DEV.SETTLEMENT_DETAIL ADD COLUMN SERIAL_NUMBER VARCHAR(255);

ALTER TABLE KETTLE_DEV.SETTLEMENT_DETAIL ADD COLUMN TICKET_NUMBER VARCHAR(255);

ALTER TABLE KETTLE_DEV.SETTLEMENT_DETAIL ADD COLUMN SLIP_NUMBER VARCHAR(255);

CREATE TABLE KETTLE_DEV.UNIT_PULL_REASON_METADATA (
                                                      `UNIT_PULL_REASON_METADATA_ID` INT NOT NULL AUTO_INCREMENT,
                                                      `CATEGORY` VARCHAR(255) NULL DEFAULT NULL ,
                                                      `REASON` VARCHAR(255) NULL DEFAULT NULL,
                                                      PRIMARY KEY (`UNIT_PULL_REASON_METADATA_ID`));

INSERT INTO KETTLE_DEV.UNIT_PULL_REASON_METADATA (`UNIT_PULL_REASON_METADATA_ID`, `CATEGORY`, `REASON`) VALUES ('1', 'CASH_PICKUP', 'Bank not come');
INSERT INTO KETTLE_DEV.UNIT_PULL_REASON_METADATA (`UNIT_PULL_REASON_METADATA_ID`, `CATEGORY`, `REASON`) VALUES ('2', 'CASH_PICKUP', 'Bank refused to wait due to long waiting period');
INSERT INTO KETTLE_DEV.UNIT_PULL_REASON_METADATA (`UNIT_PULL_REASON_METADATA_ID`, `CATEGORY`, `REASON`) VALUES ('3', 'CASH_PICKUP', 'Cash Bundle not ready at the cafe');
INSERT INTO KETTLE_DEV.UNIT_PULL_REASON_METADATA (`UNIT_PULL_REASON_METADATA_ID`, `CATEGORY`, `REASON`) VALUES ('4', 'CASH_PICKUP', 'Banking Not Started');
INSERT INTO KETTLE_DEV.UNIT_PULL_REASON_METADATA (`UNIT_PULL_REASON_METADATA_ID`, `CATEGORY`, `REASON`) VALUES ('5', 'OTHER', 'Money used for Petty Cash');
INSERT INTO KETTLE_DEV.UNIT_PULL_REASON_METADATA (`UNIT_PULL_REASON_METADATA_ID`, `CATEGORY`, `REASON`) VALUES ('6', 'OTHER', 'Bank Holiday');
INSERT INTO KETTLE_DEV.UNIT_PULL_REASON_METADATA (`UNIT_PULL_REASON_METADATA_ID`, `CATEGORY`, `REASON`) VALUES ('7', 'OTHER', 'Weekly Collection');


CREATE TABLE KETTLE_DEV.UNIT_PULL_TRANSFER_SETTLEMENT_REASON (
                                                                 `UNIT_PULL_TRANSFER_SETTLEMENT_REASON_ID` INT NULL AUTO_INCREMENT,
                                                                 `UNIT_ID` INT NULL DEFAULT NULL,
                                                                 `DAY_CLOSURE_ID` INT NULL DEFAULT NULL,
                                                                 `PULL_ID` INT NULL DEFAULT NULL,
                                                                 `REASON` VARCHAR(255) NULL DEFAULT NULL,
                                                                 `DAY_CLOSE_STATUS` VARCHAR(45) NULL DEFAULT NULL,
                                                                 `BUISSNES_DATE` DATE NULL DEFAULT NULL,
                                                                 `CREATED_BY` VARCHAR(45) NULL DEFAULT NULL,
                                                                 `WITNESSED_BY` INT NULL DEFAULT NULL,
                                                                 `PAYMENT_MODE` INT NULL DEFAULT NULL,
                                                                 UNIQUE INDEX `UNIT_PULL_TRANSFER_SETTLEMENT_REASON_ID_UNIQUE` (`UNIT_PULL_TRANSFER_SETTLEMENT_REASON_ID` ASC));
ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL
ADD COLUMN UTM_MEDIUM VARCHAR(100),
ADD COLUMN UTM_SOURCE VARCHAR(100);

CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_UTM_SOURCE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(UTM_SOURCE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_UTM_MEDIUM ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(UTM_MEDIUM) USING BTREE;

ALTER TABLE `KETTLE_DEV`.`SUBSCRIPTION_PLAN` ADD COLUMN CARD_CODE varchar(20) UNIQUE NOT NULL;
ALTER TABLE `KETTLE_DEV`.`SUBSCRIPTION_PLAN` ADD COLUMN OFFER_DESCRIPTION varchar(200) NOT NULL DEFAULT NULL;

ALTER TABLE `KETTLE_DEV`.`SUBSCRIPTION_PLAN_EVENT` ADD COLUMN SUBSCRIPTION_SAVINGS DECIMAL(16,2) DEFAULT "NULL";ALTER TABLE `KETTLE_DEV`.`SUBSCRIPTION_PLAN_EVENT` ADD COLUMN SUBSCRIPTION_SAVINGS DECIMAL(16,2) DEFAULT NULL;

ALTER TABLE `KETTLE_DEV`.`SUBSCRIPTION_PLAN` DROP COLUMN CARD_CODE;
ALTER TABLE `KETTLE_DEV`.`CUSTOMER_INFO` ADD COLUMN CARD_CODE varchar(20) UNIQUE;

ALTER TABLE `KETTLE_DEV`.`SUBSCRIPTION_PLAN_EVENT` ADD COLUMN SUBSCRIPTION_SAVINGS DECIMAL(16,2) DEFAULT NULL;
CREATE TABLE KETTLE_DEV.ORDER_NOTIFICATION_DATA(
    ORDER_NOTIFICATION_ID     INT          NOT NULL,
    ORDER_FEEDBACK_URL        VARCHAR(500) NULL,
    ORDER_RECIEPT_URL         VARCHAR(500) NOT NULL,
    LOYAL_TEA_TOTAL_COUNT     INT NULL,
    TOTAL_LOYAL_TEA_POINTS    INT NULL,
    LOYAL_TEA_POINTS          INT NULL,
    LOYAL_TEA_COUNT           INT NULL,
    UNIT_NAME                 VARCHAR(150) NULL,
    ORDER_AMT                 DECIMAL(10, 2) NULL,
    EARNED_LOYAL_TEA_POINT    INT NULL,
    SAVING_TEXT               VARCHAR(450) NULL,
    SAVING_AMT                DECIMAL(10, 2) NULL,
    IS_SUBSCRIPTION_PURCHASED VARCHAR(2) NULL,
    IS_SUBSCRIPTION_USED      VARCHAR(2) NULL,
    IS_SMS_SUBSCRIBER         VARCHAR(2) NULL,
    IS_WHATSAPP_OPT_IN        VARCHAR(2) NULL,
    CUSTOMER_FNAME            VARCHAR(45)  NOT NULL,
    CUSTOMER_CONTACT          VARCHAR(45)  NOT NULL,
    GENERATED_ORDER_ID        VARCHAR(45) NULL,
    WALLET_PENDING_AMT        INT NULL,
    WALLET_EXTRA_AMT          DECIMAL(10, 2) NULL,
    WALLET_SAVING_AMT         VARCHAR(45) NULL,
    WALLET_PURCHASE_AMT       INT NULL,
    ITEM_CODE                 VARCHAR(45) NULL,
    CASH_PENDING_AMT          VARCHAR(45) NULL,
    VOUCHER_CODE              VARCHAR(150) NULL,
    CASHBACK_AMT              DECIMAL(10, 2) NULL,
    CASHBACK_START_DATE       VARCHAR(150) NULL,
    REFUND_AMT                INT NULL,
    USED_AMT                  INT NULL,
    SUBSCRIPTION_NAME         VARCHAR(45) NULL,
    SUBSCR_OFFER_DESCRIPTION  VARCHAR(550) NULL,
    VALID_DAYS                INT NULL,
    PLAN_END_DATE             VARCHAR(45) NULL,
    SELECT_OVERALL_SAVING     INT NULL,
    SELECT_SAVING_AMOUNT      DECIMAL(10, 2) NULL,
    NEXT_OFFER_TEXT           VARCHAR(450) NULL,
    OFFER_CODE                VARCHAR(450) NULL,
    VALIDITY_TILL             VARCHAR(45) NULL,
    DAYS_LEFT                 INT NULL,
    CHANNEL_PARTNER           VARCHAR(45) NULL,
    SUBSCR_VALIDITY_IN_DAYS   INT NULL,
    IS_LOYALTY_UNLOCKED       VARCHAR(45) NULL,
    ORDER_ID                  INT NULL,
    PRIMARY KEY (ORDER_NOTIFICATION_ID)
);
ALTER TABLE KETTLE_DEV.ORDER_NOTIFICATION_DATA
    CHANGE COLUMN `ORDER_NOTIFICATION_ID` `ORDER_NOTIFICATION_ID` INT(11) NOT NULL AUTO_INCREMENT ;
ALTER TABLE kettle_dev.ORDER_NOTIFICATION_DATA
    CHANGE COLUMN `UNIT_NAME` `UNIT_NAME` VARCHAR(150) NOT NULL ,
    CHANGE COLUMN `GENERATED_ORDER_ID` `GENERATED_ORDER_ID` VARCHAR(45) NOT NULL ,
    CHANGE COLUMN `ORDER_ID` `ORDER_ID` INT(11) NOT NULL ,
    ADD UNIQUE INDEX `ORDER_ID_UNIQUE` (`ORDER_ID` ASC),
    ADD UNIQUE INDEX `GENERATED_ORDER_ID_UNIQUE` (`GENERATED_ORDER_ID` ASC);
;



ALTER TABLE KETTLE_DEV.SUBSCRIPTION_PLAN
    ADD COLUMN FREQUENCY_STRATEGY VARCHAR(20),
ADD COLUMN OVERALL_FREQUENCY DECIMAL(15,2),
ADD COLUMN FREQUENCY_LIMIT DECIMAL(15,2);

ALTER TABLE KETTLE_DEV.ORDER_DETAIL
    CHANGE COLUMN `ORDER_SOURCE_ID` `ORDER_SOURCE_ID` VARCHAR(100) NULL DEFAULT NULL ;



ALTER TABLE KETTLE_DEV.SUBSCRIPTION_PLAN
    ADD COLUMN OFFER_STRATEGY VARCHAR(20),
ADD COLUMN OVERALL_FREQUENCY DECIMAL(15,2),
ADD COLUMN FREQUENCY_LIMIT DECIMAL(15,2);


ALTER TABLE KETTLE_DEV.ORDER_DETAIL ADD COLUMN COLLECTION_AMOUNT DECIMAL(10,2);
CREATE INDEX COLLECTION_AMOUNT ON KETTLE_DEV.ORDER_DETAIL(COLLECTION_AMOUNT) USING BTREE;

CREATE TABLE KETTLE_DEV.WORKSTATION_MANUAL_TASK_DETAIL (
	MANUAL_TASK_ID INT(11) NOT NULL AUTO_INCREMENT,
	TASK_TYPE VARCHAR(50),
	ORDER_TIME TIMESTAMP NULL DEFAULT NULL,
	UNIT_ID INT(11),
	EMPLOYEE_ID INT(11),
	GENERATED_ORDER_ID VARCHAR(100),
	PRODUCT_ITEM_ID INT(11),
	DIMENSION VARCHAR(50),
	QUANTITY INT(11),
	MONK_NUMBER INT(11),
	ERROR_TYPE VARCHAR(50),
	PRIMARY KEY(MANUAL_TASK_ID)
);


ALTER TABLE KETTLE_DEV.CASH_CARD_OFFER
    CHANGE COLUMN `OFFER_PERCENTAGE` `OFFER_PERCENTAGE` DECIMAL(10,2) NULL DEFAULT NULL ;

ALTER TABLE KETTLE_DEV.CASH_CARD_OFFER
    ADD COLUMN `SUGGEST_WALLET_OFFER_PERCENTAGE` DECIMAL(10,2) NULL DEFAULT NULL AFTER `OFFER_PERCENTAGE`;


ALTER TABLE KETTLE_DEV.CASH_CARD_OFFER
    ADD COLUMN `SUGGEST_WALLET_CARD_DESCRIPTION` VARCHAR(200) NULL DEFAULT NULL AFTER `CARD_DESCRIPTION`;

ALTER TABLE KETTLE_DEV.CASH_CARD_OFFER
    CHANGE COLUMN `CARD_DESCRIPTION` `CARD_DESCRIPTION` VARCHAR(200) NULL DEFAULT NULL ;


ALTER TABLE KETTLE_DEV.ORDER_ITEM ADD COLUMN SOURCE_CATEGORY VARCHAR(100) NULL;
ALTER TABLE KETTLE_DEV.ORDER_ITEM ADD COLUMN SOURCE_SUB_CATEGORY VARCHAR(100) NULL;

CREATE TABLE KETTLE_DEV.MONTHLY_AOV_METADATA (
                                                 AOV_MAPPING_ID bigint(20) NOT NULL AUTO_INCREMENT,
                                                 AOV decimal(19,2) DEFAULT NULL,
                                                 BRAND_ID int(11) DEFAULT NULL,
                                                 CALCULATED_AT datetime DEFAULT NULL,
                                                 CALCULATION_START_TIME datetime DEFAULT NULL,
                                                 MONTH int(11) DEFAULT NULL,
                                                 YEAR int(11) DEFAULT NULL,
                                                 PRIMARY KEY (AOV_MAPPING_ID)
);

