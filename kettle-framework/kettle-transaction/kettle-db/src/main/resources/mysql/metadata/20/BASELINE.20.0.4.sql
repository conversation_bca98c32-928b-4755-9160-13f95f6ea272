DROP TABLE IF EXISTS KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL;
CREATE TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(
CUSTOMER_CAMPIAGN_OFFER_DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
CUSTOMER_ID INTEGER NOT NULL,
CONTACT_NUMBER VARCHAR(15) NULL,
COUNTRY_CODE VARCHAR(15) NULL,
CAMPAIGN_TYPE VARCHAR(100) NOT NULL,
CAMPAIGN_CLONE_CODE VARCHAR(15) NULL,
CAMPAIGN_SOURCE VARCHAR(50) NULL,
COUPON_CODE VARCHAR(20)  NULL,
COUPON_DETAIL_ID INTEGER  NULL,
COUPON_START_DATE DATE NULL,
COUPON_END_DATE DATE NULL,
OFFER_DETAIL_ID INTEGER  NULL,
OFFER_TEXT VARCHAR(1500) NULL,
COUPON_GENERATION_TIME TIMESTAMP NULL,
IS_NOTIFICATION_REQUIRED VARCHAR(1),
OFFER_CREATE_ORDER_ID INTEGER NULL,
OFFER_APPLIED VARCHAR(1) NULL,
OFFER_APPLY_LAST_TIME TIMESTAMP NULL,
OFFER_APPLY_LAST_ORDER_ID INTEGER NULL,
OVERALL_SAVINGS DECIMAL(16,6) NULL,
OFFER_APPLY_COUNT INTEGER NULL,
OFFER_APPLY_TYPE VARCHAR(30) NULL,
GAP_IN_DAYS INTEGER NULL,
OFFER_STATUS VARCHAR(15) NUll
);
## OFFER_APPLY_TYPE - INITIAL, POST_REMINDER_1, POST_REMINDER_2, POST_REMINDER_3
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_CUSTOMER_ID ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(CUSTOMER_ID) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_COUNTRY_CODE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(COUNTRY_CODE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_CONTACT_NUMBER ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(CONTACT_NUMBER) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_CAMPAIGN_TYPE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(CAMPAIGN_TYPE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_CAMPAIGN_CLONE_CODE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(CAMPAIGN_CLONE_CODE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_CAMPAIGN_SOURCE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(CAMPAIGN_SOURCE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_COUPON_CODE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(COUPON_CODE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_COUPON_DETAIL_ID ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(COUPON_DETAIL_ID) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_COUPON_START_DATE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(COUPON_START_DATE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_COUPON_END_DATE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(COUPON_END_DATE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_OFFER_DETAIL_ID ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(OFFER_DETAIL_ID) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_COUPON_GENERATION_TIME ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(COUPON_GENERATION_TIME) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_IS_NOTIFICATION_REQUIRED ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(IS_NOTIFICATION_REQUIRED) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_OFFER_CREATE_ORDER_ID ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(OFFER_CREATE_ORDER_ID) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_OFFER_APPLIED ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(OFFER_APPLIED) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_OFFER_APPLY_LAST_TIME ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(OFFER_APPLY_LAST_TIME) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_OFFER_APPLY_LAST_ORDER_ID ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(OFFER_APPLY_LAST_ORDER_ID) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_OFFER_APPLY_COUNT ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(OFFER_APPLY_COUNT) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_OFFER_APPLY_TYPE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(OFFER_APPLY_TYPE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_GAP_IN_DAYS ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(GAP_IN_DAYS) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_OFFER_STATUS ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(OFFER_STATUS) USING BTREE;

DROP TABLE IF EXISTS KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION;

CREATE TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION(
CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
CUSTOMER_CAMPIAGN_OFFER_DETAIL_ID INTEGER NOT NULL,
CONTACT_NUMBER VARCHAR(15) NULL,
COUNTRY_CODE VARCHAR(15) NULL,
CUSTOMER_ID INTEGER NULL,
IS_NOTIFIED VARCHAR(1) NULL,
NOTIFICATION_REQUIRED VARCHAR(1) NULL,
NOTIFICATION_MEDIUM VARCHAR(100) NULL,
NOTIFICATION_MODE VARCHAR(30) NULL,
NOTIFICATION_TEXT VARCHAR(1500) NULL,
NOTIFICATION_TIME TIMESTAMP NULL,
NOTIFICATION_TYPE VARCHAR(20) NULL
);

CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION_CUSTOMER_OFFER_DETAIL_ID ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION(CUSTOMER_CAMPIAGN_OFFER_DETAIL_ID) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION_CONTACT_NUMBER ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION(CONTACT_NUMBER) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION_CUSTOMER_ID ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION(CUSTOMER_ID) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION_IS_NOTIFIED ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION(IS_NOTIFIED) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION_NOTIFICATION_REQUIRED ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION(NOTIFICATION_REQUIRED) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION_NOTIFICATION_MEDIUM ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION(NOTIFICATION_MEDIUM) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION_NOTIFICATION_MODE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION(NOTIFICATION_MODE) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION_NOTIFICATION_TIME ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION(NOTIFICATION_TIME) USING BTREE;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION_NOTIFICATION_TYPE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION(NOTIFICATION_TYPE) USING BTREE;

## NOTIFICATION_TYPE - INITIAL, REMINDER_1, REMINDER_2, REMINDER_3, ADHOC
## NOTIFICATION_MODE  - SMS, EMAIL, WHATSAPP, PUSH_NOTIFICATION
## NOTIFICATION_MEDIUM - SOLS_INFINI, SINCH_WHATSAPP

;

SELECT 
    ci.CUSTOMER_ID,
    :brandId BRAND_ID,
    ci.FIRST_NAME,
    ci.CONTACT_NUMBER,
    ci.COUNTRY_CODE,
    ci.IS_NUMBER_VERIFIED,
    ci.IS_EMAIL_VERIFIED,
    ci.IS_BLACKLISTED,
    ci.EMAIL_ID,
    ci.IS_DND,
    ci.REF_CODE,
    SUM(CASE
        WHEN
            od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),
                INTERVAL - 90 DAY)
                AND od.ORDER_SOURCE = 'COD'
        THEN
            1
        ELSE 0
    END) ACTIVE_DELIVERY_ORDERS,
    SUM(CASE
        WHEN
            od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),
                INTERVAL - 90 DAY)
                AND od.ORDER_SOURCE <> 'COD'
        THEN
            1
        ELSE 0
    END) ACTIVE_CAFE_ORDERS,
    SUM(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),
                INTERVAL - 90 DAY)
        THEN
            1
        ELSE 0
    END) ACTIVE_OVERALL_ORDERS,
    SUM(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER = 'Y')
                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),
                INTERVAL - 90 DAY)
        THEN
            1
        ELSE 0
    END) ACTIVE_WALLET_ORDERS,
    SUM(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),
                INTERVAL - 90 DAY)
                AND od.ORDER_SOURCE = 'COD'
        THEN
            od.TAXABLE_AMOUNT
        ELSE 0
    END) ACTIVE_DELIVERY_SALES,
    SUM(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),
                INTERVAL - 90 DAY)
                AND od.ORDER_SOURCE <> 'COD'
        THEN
            od.TAXABLE_AMOUNT
        ELSE 0
    END) ACTIVE_CAFE_SALES,
    SUM(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),
                INTERVAL - 90 DAY)
        THEN
            od.TAXABLE_AMOUNT
        ELSE 0
    END) ACTIVE_OVERALL_SALES,
    SUM(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER = 'Y')
                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),
                INTERVAL - 90 DAY)
        THEN
            od.TAXABLE_AMOUNT
        ELSE 0
    END) ACTIVE_WALLET_SALES,
    SUM(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),
                INTERVAL - 90 DAY)
                AND od.ORDER_SOURCE = 'COD'
        THEN
            od.TOTAL_AMOUNT
        ELSE 0
    END) ACTIVE_DELIVERY_GMV,
    SUM(CASE
        WHEN
            od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),
                INTERVAL - 90 DAY)
                AND od.ORDER_SOURCE <> 'COD'
        THEN
            od.TOTAL_AMOUNT
        ELSE 0
    END) ACTIVE_CAFE_GMV,
    SUM(CASE
        WHEN
            od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),
                INTERVAL - 90 DAY)
        THEN
            od.TOTAL_AMOUNT
        ELSE 0
    END) ACTIVE_OVERALL_GMV,
    SUM(CASE
        WHEN
            od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND od.ORDER_SOURCE = 'COD'
        THEN
            1
        ELSE 0
    END) DELIVERY_ORDERS,
    SUM(CASE
        WHEN
            od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND od.ORDER_SOURCE <> 'COD'
        THEN
            1
        ELSE 0
    END) CAFE_ORDERS,
    SUM(CASE
        WHEN
            od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
        THEN
            1
        ELSE 0
    END) OVERALL_ORDERS,
    SUM(CASE
        WHEN od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER = 'Y') THEN 1
        ELSE 0
    END) WALLET_ORDERS,
    SUM(CASE
        WHEN
            od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND od.ORDER_SOURCE = 'COD'
        THEN
            od.TAXABLE_AMOUNT
        ELSE 0
    END) DELIVERY_SALES,
    SUM(CASE
        WHEN
            od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND od.ORDER_SOURCE <> 'COD'
        THEN
            od.TAXABLE_AMOUNT
        ELSE 0
    END) CAFE_SALES,
    SUM(CASE
        WHEN
            od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
        THEN
            od.TAXABLE_AMOUNT
        ELSE 0
    END) OVERALL_SALES,
    SUM(CASE
        WHEN od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER = 'Y') THEN od.TAXABLE_AMOUNT
        ELSE 0
    END) WALLET_SALES,
    SUM(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND od.ORDER_SOURCE = 'COD'
        THEN
            od.TOTAL_AMOUNT
        ELSE 0
    END) DELIVERY_GMV,
    SUM(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND od.ORDER_SOURCE <> 'COD'
        THEN
            od.TOTAL_AMOUNT
        ELSE 0
    END) CAFE_GMV,
    SUM(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
        THEN
            od.TOTAL_AMOUNT
        ELSE 0
    END) OVERALL_GMV,
    MAX(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
        THEN
            od.ORDER_ID
        ELSE 0
    END) LAST_ORDER_ID,
    MAX(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND od.ORDER_SOURCE = 'COD'
        THEN
            od.ORDER_ID
        ELSE 0
    END) LAST_DELIVERY_ORDER_ID,
    MAX(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND od.ORDER_SOURCE <> 'COD'
        THEN
            od.ORDER_ID
        ELSE 0
    END) LAST_DINEIN_ORDER_ID,
    MAX(CASE
        WHEN (IS_GIFT_CARD_ORDER = 'Y') THEN od.ORDER_ID
        ELSE 0
    END) LAST_WALLET_ORDER_ID,
    MAX(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
        THEN
            od.BILLING_SERVER_TIME
        ELSE 0
    END) LAST_ORDER_TIME,
    MAX(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND od.ORDER_SOURCE = 'COD'
        THEN
            od.BILLING_SERVER_TIME
        ELSE 0
    END) LAST_DELIVERY_ORDER_TIME,
    MAX(CASE
        WHEN
           od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL
                OR IS_GIFT_CARD_ORDER = 'N')
                AND od.ORDER_SOURCE <> 'COD'
        THEN
            od.BILLING_SERVER_TIME
        ELSE 0
    END) LAST_DINEIN_ORDER_TIME,
    MAX(CASE
        WHEN od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER = 'Y') THEN od.BILLING_SERVER_TIME
        ELSE 0
    END) LAST_WALLET_ORDER_TIME,
    MAX(ls.AVAILED_SIGNUP_OFFER) AVAILED_SIGNUP_OFFER,
    MAX(ls.ACQUIRED_POINTS) ACQUIRED_POINTS,
    MAX(ls.CUMULATIVE_POINTS) CUMULATIVE_POINTS
    
FROM
    CUSTOMER_INFO ci
        INNER JOIN
    LOYALTY_SCORE ls ON ls.CUSTOMER_ID = ci.CUSTOMER_ID
        LEFT OUTER JOIN
    ORDER_DETAIL od ON od.CUSTOMER_ID = ci.CUSTOMER_ID
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.BRAND_ID = :brandId
        AND od.ORDER_TYPE = 'order'
        AND od.ORDER_ID NOT IN (:excludeOrderId)
WHERE
    ci.CUSTOMER_ID = :customerId
    group by ci.CUSTOMER_ID
    ;


ALTER TABLE KETTLE_DEV.CUSTOMER_INFO ADD COLUMN OPT_IN_WHATSAPP VARCHAR(10);
