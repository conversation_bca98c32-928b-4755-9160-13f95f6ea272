
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) 
VALUES ('114', 'Tower-B, Part- <PERSON>lokhera', 'DLF Phase 2, Sector 15', 'Gurgaon', 'Haryana', 'India', '122001', '+91-1132551234', '+91-1132551234', 'RESIDENTIAL');

INSERT INTO UNIT_DETAIL (`UNIT_ID`, `UNIT_NAME`, `UNIT_REGION`, `UNIT_CATEGORY`, `UNIT_EMAIL`, `START_DATE`, `UNIT_STATUS`, `TIN`, `UNIT_ADDR_ID`, `BUSINESS_DIV_ID`, `NO_OF_TERMINALS`) 
VALUES ('10014', 'Google Office', 'NCR', 'CAFE', '<EMAIL>', '2015-12-21 20:55:23', 'ACTIVE', '***********', '114', '1000', '1');

INSERT INTO UNIT_TAX_MAPPING
(`TAX_PROFILE_ID`,`UNIT_ID`,`TAX_PERCENTAGE`,`PROFILE_STATUS`,`STATE`)
VALUES
('1', '10014', '12.50', 'ACTIVE', 'HARYANA'),
('2', '10014', '5.00', 'ACTIVE', 'HARYANA'),
('3', '10014', '5.00', 'ACTIVE', 'HARYANA'),
('4', '10014', '5.60', 'ACTIVE', 'HARYANA'),
('5', '10014', '0.00', 'ACTIVE', 'HARYANA'),
('6', '10014', '0.20', 'ACTIVE', 'HARYANA');

UPDATE EMPLOYEE_UNIT_MAPPING SET `UNIT_ID`='10014' WHERE `EMP_ID`='100030';

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 10014,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 10000 and upm.PRODUCT_ID IN(
10,11,12,20,40,50,60,80,90,100,110,120,130,140,150,160,170,210,220,240,270,280,300,310,320,380,390,420,430,440,450,460,640,650,660,670,680,690,700,710,720,730,780,800,810,840,850,860,861,862,863,864
);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 10),21,21);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 11),21,21);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 12),21,21);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 20),1,21);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 40),1,48);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 50),21,21);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 60),21,40);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 80),1101,32);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 90),21,36);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 100),21,36);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 110),21,40);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 120),21,40);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 130),21,52);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 140),21,52);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 150),21,52);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 160),21,40);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 170),21,32);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 210),21,47);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 220),21,46);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 240),21,46);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 270),21,44);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 280),21,31);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 300),30,54);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 310),30,54);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 320),30,54);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 380),1,59);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 390),1,60);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 420),30,57);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 430),30,44);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 440),30,49);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 450),30,51);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 460),30,51);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 640),1,55);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 650),1,55);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 660),1,56);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 670),1,43);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 680),1,43);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 690),1,35);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 700),1,188.5);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 710),1,188.5);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 720),1,283.3);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 730),1,283.3);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 780),1,13);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 800),1,36);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 810),1,45);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 840),1,0);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 850),1,0);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 860),1,16.82);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 861),1,20);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 862),1,20);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 863),1,20);
INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)VALUES((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 864),1,20);

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 10014,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE 
from UNIT_PRODUCT_MAPPING upm 
where upm.UNIT_ID = 10000
and PRODUCT_ID  in (681,682,683,684);

INSERT INTO UNIT_PRODUCT_PRICING
(`UNIT_PROD_REF_ID`,`DIMENSION_CODE`,`LAST_UPDATE_TMSTMP`,`PRICE`)
VALUES
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 681),1,'2015-01-01 00:00:00',56.00),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 682),1,'2015-01-01 00:00:00',56.00),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 683),1,'2015-01-01 00:00:00',71.00),
((SELECT UNIT_PROD_REF_ID FROM UNIT_PRODUCT_MAPPING WHERE UNIT_ID = 10014 AND PRODUCT_ID = 684),1,'2015-01-01 00:00:00',71.00);

INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('2011', '20', 'MylesOffer', 'Myles Offer', 'ACTIVE');
