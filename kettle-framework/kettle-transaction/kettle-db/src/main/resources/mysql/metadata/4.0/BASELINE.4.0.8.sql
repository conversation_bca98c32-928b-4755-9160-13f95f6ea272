CREATE TABLE KETTLE_DEV.ORDER_NPS_DETAIL (
    SURVEY_RESPONSE_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    ORDER_ID INTEGER NULL,
    CUSTOMER_ID INTEGER NULL,
    CONTACT_NUMBER VARCHAR(15) NULL,
    GENERATED_ORDER_ID VARCHAR(20) NULL,
    UNIT_NAME VARCHAR(50) NULL,
    UNIT_ID INTEGER NULL,
    NPS_SCORE INTEGER NULL,
    SURVEY_CREATION_TIME TIMESTAMP NULL
);

CREATE INDEX ORDER_ID_ORDER_NPS_DETAIL ON KETTLE_DEV.ORDER_NPS_DETAIL(ORDER_ID);
CREATE INDEX CUSTOMER_ID_ORDER_NPS_DETAIL ON KETTLE_DEV.ORDER_NPS_DETAIL(CUSTOMER_ID);
CREATE INDEX CONTACT_NUMBER_ORDER_NPS_DETAIL ON KETTLE_DEV.ORDER_NPS_DETAIL(CONTACT_NUMBER);
CREATE INDEX GENERATED_ORDER_ID_ORDER_NPS_DETAIL ON KETTLE_DEV.ORDER_NPS_DETAIL(GENERATED_ORDER_ID);
CREATE INDEX UNIT_ID_ORDER_NPS_DETAIL ON KETTLE_DEV.ORDER_NPS_DETAIL(UNIT_ID);


ALTER TABLE `KETTLE_DEV`.`CASH_CARD_DETAIL` 
ADD COLUMN `CARD_TYPE` VARCHAR(30) NULL DEFAULT NULL AFTER `CARD_INITIAL_OFFER`;

ALTER TABLE KETTLE_DEV.CASH_CARD_EVENT_LOG ADD COLUMN UNIT_ID INT(11) NULL;

ALTER TABLE `KETTLE_DEV`.`CASH_CARD_DETAIL`  ADD COLUMN `SERIAL_NUMBER` VARCHAR(20) NULL DEFAULT NULL AFTER `CARD_NUMBER`;

INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`) VALUES ('6', 'DEFAULT DELIVERY', '5', 'DELIVERY');

INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`) VALUES ('3', 'DEFAULT DELIVERY', '8', 'DELIVERY');

INSERT INTO `KETTLE_DEV`.`PARTNER_ATTRIBUTES` (`PARTNER_ID`, `MAPPING_TYPE`, `MAPPING_VALUE`, `PARTNER_TYPE`) VALUES ('15', 'DEFAULT DELIVERY', '5', 'DELIVERY');


ALTER TABLE `KETTLE_DEV`.`ORDER_DETAIL`  ADD INDEX `FK_ORDER_DETAIL_CUSTOMER_ID` USING BTREE (`CUSTOMER_ID` ASC);
