DROP TABLE IF EXISTS KETTLE_DEV.UNIT_CHANNEL_PARTNER_CHARGES_BUDGET_DETAIL ;
CREATE TABLE KETTLE_DEV.UNIT_CHANNEL_PARTNER_CHARGES_BUDGET_DETAIL (
    UNIT_CHANNEL_PARTNER_CHARGES_BUDGET_DETAIL_ID INT(11) NOT NULL AUTO_INCREMENT,
    UNIT_ID INT(11) DEFAULT NULL,
    CALCULATION_MONTH INT(11) DEFAULT NULL,
    CALCULATION_YEAR INT(11) DEFAULT NULL,
    UNIT_NAME VARCHAR(100) DEFAULT NULL,
    COMMISSION_CHANNEL_PARTNERS DECIMAL(10 , 2 ) DEFAULT NULL,
    CANCELLATION_CHARGES DECIMAL(10 , 2 ) DEFAULT NULL,
    OLD_COMMISSION_CHANNEL_PARTNERS DECIMAL(10 , 2 ) DEFAULT NULL,
    OLD_CANCELLATION_CHARGES DECIMAL(10 , 2 ) DEFAULT NULL,
    BUDGET_STATUS VARCHAR(15) DEFAULT NULL,
    UPDATED_BY VARCHAR(100) DEFAULT NULL,
    UPDATE_TIME TIMESTAMP NULL,
    PRIMARY KEY (UNIT_CHANNEL_PARTNER_CHARGES_BUDGET_DETAIL_ID),
    KEY UNIT_CHANNEL_PARTNER_CHARGES_BUDGET_DETAIL_UNIT_ID (UNIT_ID) USING BTREE,
    KEY UNIT_CHANNEL_PARTNER_CHARGES_BUDGET_DETAIL_CALCULATION_MONTH (CALCULATION_MONTH) USING BTREE,
    KEY UNIT_CHANNEL_PARTNER_CHARGES_BUDGET_DETAIL_CALCULATION_YEAR (CALCULATION_YEAR) USING BTREE,
    KEY UNIT_CHANNEL_PARTNER_CHARGES_BUDGET_DETAIL_UNIT_NAME (UNIT_NAME) USING BTREE,
    KEY UNIT_CHANNEL_PARTNER_CHARGES_BUDGET_DETAIL_BUDGET_STATUS (BUDGET_STATUS) USING BTREE
);

 ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
    ADD COLUMN   CANCELLATION_CHARGES DECIMAL(10 , 2 ) DEFAULT NULL;
    
 ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
    ADD COLUMN   CANCELLATION_CHARGES DECIMAL(10 , 2 ) DEFAULT NULL;
       

DROP TABLE IF EXISTS KETTLE_DEV.UNIT_BANK_CHARGES_BUDGET_DETAIL ;
CREATE TABLE KETTLE_DEV.UNIT_BANK_CHARGES_BUDGET_DETAIL (
    UNIT_BANK_CHARGES_BUDGET_DETAIL_ID INT(11) NOT NULL AUTO_INCREMENT,
    UNIT_ID INT(11) DEFAULT NULL,
    CALCULATION_MONTH INT(11) DEFAULT NULL,
    CALCULATION_YEAR INT(11) DEFAULT NULL,
    UNIT_NAME VARCHAR(100) DEFAULT NULL,
    BANK_CHARGES DECIMAL(10 , 2 ) DEFAULT NULL,
    OLD_BANK_CHARGES DECIMAL(10 , 2 ) DEFAULT NULL,
    BUDGET_STATUS VARCHAR(15) DEFAULT NULL,
    UPDATED_BY VARCHAR(100) DEFAULT NULL,
    UPDATE_TIME TIMESTAMP NULL,
    PRIMARY KEY (UNIT_BANK_CHARGES_BUDGET_DETAIL_ID),
    KEY UNIT_BANK_CHARGES_BUDGET_DETAIL_UNIT_ID (UNIT_ID) USING BTREE,
    KEY UNIT_BANK_CHARGES_BUDGET_DETAIL_CALCULATION_MONTH (CALCULATION_MONTH) USING BTREE,
    KEY UNIT_BANK_CHARGES_BUDGET_DETAIL_CALCULATION_YEAR (CALCULATION_YEAR) USING BTREE,
    KEY UNIT_BANK_CHARGES_BUDGET_DETAIL_UNIT_NAME (UNIT_NAME) USING BTREE,
    KEY UNIT_BANK_CHARGES_BUDGET_DETAIL_BUDGET_STATUS (BUDGET_STATUS) USING BTREE
);

ALTER TABLE KETTLE_DEV.UNIT_BANK_CHARGES_BUDGET_DETAIL MODIFY UPDATED_BY INT(11);

DROP TABLE IF EXISTS KETTLE_DEV.UNIT_FACILITY_CHARGES_BUDGET_DETAIL ;
CREATE TABLE KETTLE_DEV.UNIT_FACILITY_CHARGES_BUDGET_DETAIL (
    UNIT_FACILITY_CHARGES_BUDGET_DETAIL_ID INT(11) NOT NULL AUTO_INCREMENT,
    UNIT_ID INT(11) DEFAULT NULL,
    CALCULATION_MONTH INT(11) DEFAULT NULL,
    CALCULATION_YEAR INT(11) DEFAULT NULL,
    UNIT_NAME VARCHAR(100) DEFAULT NULL,
    ENERGY_ELECTRICITY DECIMAL(10 , 2 ) DEFAULT NULL,
    WATER_CHARGES DECIMAL(10 , 2 ) DEFAULT NULL,
    OLD_ENERGY_ELECTRICITY DECIMAL(10 , 2 ) DEFAULT NULL,
    OLD_WATER_CHARGES DECIMAL(10 , 2 ) DEFAULT NULL,
    BUDGET_STATUS VARCHAR(15) DEFAULT NULL,
    UPDATED_BY VARCHAR(100) DEFAULT NULL,
    UPDATE_TIME TIMESTAMP NULL,
    PRIMARY KEY (UNIT_FACILITY_CHARGES_BUDGET_DETAIL_ID),
    KEY UNIT_FACILITY_CHARGES_BUDGET_DETAIL_UNIT_ID (UNIT_ID) USING BTREE,
    KEY UNIT_FACILITY_CHARGES_BUDGET_DETAIL_CALCULATION_MONTH (CALCULATION_MONTH) USING BTREE,
    KEY UNIT_FACILITY_CHARGES_BUDGET_DETAIL_CALCULATION_YEAR (CALCULATION_YEAR) USING BTREE,
    KEY UNIT_FACILITY_CHARGES_BUDGET_DETAIL_UNIT_NAME (UNIT_NAME) USING BTREE,
    KEY UNIT_FACILITY_CHARGES_BUDGET_DETAIL_BUDGET_STATUS (BUDGET_STATUS) USING BTREE
);

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN TOTAL_DISCOUNT_LOYAL_TEA DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN TOTAL_DISCOUNT_MARKETING DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN TOTAL_DISCOUNT_OPS DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN TOTAL_DISCOUNT_BD DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN TOTAL_DISCOUNT_EMPLOYEE_FICO DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DINE_IN_DISCOUNT_LOYAL_TEA DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DINE_IN_DISCOUNT_MARKETING DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DINE_IN_DISCOUNT_OPS DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DINE_IN_DISCOUNT_BD DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DINE_IN_DISCOUNT_EMPLOYEE_FICO DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DELIVERY_DISCOUNT_LOYAL_TEA DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DELIVERY_DISCOUNT_MARKETING DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DELIVERY_DISCOUNT_OPS DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DELIVERY_DISCOUNT_BD DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DELIVERY_DISCOUNT_EMPLOYEE_FICO DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN EMP_DISCOUNT_LOYAL_TEA DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN EMP_DISCOUNT_MARKETING DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN EMP_DISCOUNT_OPS DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN EMP_DISCOUNT_BD DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN EMP_DISCOUNT_EMPLOYEE_FICO DECIMAL(10 , 2 ) DEFAULT NULL;


ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN TOTAL_DISCOUNT_LOYAL_TEA DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN TOTAL_DISCOUNT_MARKETING DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN TOTAL_DISCOUNT_OPS DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN TOTAL_DISCOUNT_BD DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN TOTAL_DISCOUNT_EMPLOYEE_FICO DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DINE_IN_DISCOUNT_LOYAL_TEA DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DINE_IN_DISCOUNT_MARKETING DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DINE_IN_DISCOUNT_OPS DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DINE_IN_DISCOUNT_BD DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DINE_IN_DISCOUNT_EMPLOYEE_FICO DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DELIVERY_DISCOUNT_LOYAL_TEA DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DELIVERY_DISCOUNT_MARKETING DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DELIVERY_DISCOUNT_OPS DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DELIVERY_DISCOUNT_BD DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN DELIVERY_DISCOUNT_EMPLOYEE_FICO DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN EMP_DISCOUNT_LOYAL_TEA DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN EMP_DISCOUNT_MARKETING DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN EMP_DISCOUNT_OPS DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN EMP_DISCOUNT_BD DECIMAL(10 , 2 ) DEFAULT NULL,
ADD COLUMN EMP_DISCOUNT_EMPLOYEE_FICO DECIMAL(10 , 2 ) DEFAULT NULL;

ALTER TABLE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY
ADD COLUMN BUDGET_CATEGORY VARCHAR(50) NULL;

ALTER TABLE KETTLE_DEV.UNIT_BANK_CHARGES_BUDGET_DETAIL MODIFY UPDATED_BY INT(11);
ALTER TABLE KETTLE_DEV.UNIT_CHANNEL_PARTNER_CHARGES_BUDGET_DETAIL MODIFY UPDATED_BY INT(11);
ALTER TABLE KETTLE_DEV.UNIT_FACILITY_CHARGES_BUDGET_DETAIL MODIFY UPDATED_BY INT(11);

CREATE INDEX OFFER_CODE_ORDER_DETAIL ON KETTLE_DEV.ORDER_DETAIL (OFFER_CODE) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN IS_HOTSPOT_LIVE VARCHAR(10) DEFAULT 'N';

ALTER TABLE `KETTLE_DATA_LAKE`.`DASHBOARD_DATA_LAST_UPDATE`
ADD COLUMN `ZOMATO_COUPON_REDEMPTION` DATE NULL AFTER `SWIGGY_FOOD_RATING`;

ALTER TABLE KETTLE_DATA_LAKE.DASHBOARD_DATA_LAST_UPDATE ADD COLUMN ZOMATO_PROMO_REDEMPTION DATE NULL;

CREATE TABLE KETTLE_ANALYTICS.ZOMD_PROMO_REDEMPTION (
  BUSINESS_DATE DATE,
  CHANNEL_RESTAURANT_ID INTEGER NOT NULL,
  BRAND_ID INTEGER NOT NULL,
  PROMO_CODE VARCHAR(20),
  PROMO_ORDERS INTEGER,
  PROMO_ORDERS_SUBTOTAL DECIMAL(16,2),
  ZVD DECIMAL(16,2),
  MVD DECIMAL(16,2)
);