CREATE TABLE KETTLE_DEV.`UNIT_MANPOWER_BUDGET_DETAIL` (
  `UNIT_MANPOWER_BUDGET_DETAIL_ID` int(11) NOT NULL AUTO_INCREMENT,
  `UNIT_ID` int(11) DEFAULT NULL,
  `CALCULATION_MONTH` int(11) DEFAULT NULL,
  `CALCULATION_YEAR` int(11) DEFAULT NULL,
  `UNIT_NAME` varchar(100) DEFAULT NULL,
  `SALARY` decimal(10,2) DEFAULT NULL,
  `SALARY_INCENTIVE` decimal(10,2) DEFAULT NULL,
  `SALES_INCENTIVE` decimal(10,2) DEFAULT NULL,
  `OLD_SALARY` decimal(10,2) DEFAULT NULL,
  `OLD_SALARY_INCENTIVE` decimal(10,2) DEFAULT NULL,
  `OLD_SALES_INCENTIVE` decimal(10,2) DEFAULT NULL,
  `BUDGET_STATUS` varchar(15) DEFAULT NULL,
  `UPDATED_BY` varchar(100) DEFAULT NULL,
  `UPDATE_TIME` TIMESTAMP NULL,
  PRIMARY KEY (`UNIT_MANPOWER_BUDGET_DETAIL_ID`),
  KEY `UNIT_BUDGETORY_DETAIL_UNIT_ID` (`UNIT_ID`) USING BTREE,
  KEY `UNIT_BUDGETORY_DETAIL_CALCULATION_MONTH` (`CALCULATION_MONTH`) USING BTREE,
  KEY `UNIT_BUDGETORY_DETAIL_CALCULATION_YEAR` (`CALCULATION_YEAR`) USING BTREE,
  KEY `UNIT_BUDGETORY_DETAIL_UNIT_NAME` (`UNIT_NAME`) USING BTREE,
  KEY `UNIT_BUDGETORY_DETAIL_BUDGET_STATUS` (`BUDGET_STATUS`) USING BTREE
);



ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL ADD COLUMN  `UPDATED_BY` varchar(100) NULL,
 ADD COLUMN   `UPDATE_TIME` TIMESTAMP NULL;

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL MODIFY UPDATED_BY INT(11);

ALTER TABLE KETTLE_DEV.UNIT_MANPOWER_BUDGET_DETAIL MODIFY UPDATED_BY INT(11);

ALTER TABLE KETTLE_MASTER_DEV.COUPON_DETAIL_DATA ADD COLUMN COUPON_APPLICABILITY INTEGER;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN IS_HOTSPOT_LIVE VARCHAR(1) NOT NULL DEFAULT 'N';
