
ALTER TABLE KETTLE_DEV.LOYALTY_SCORE 
ADD COLUMN SIGNUP_OFFER_EXPIRED VARCHAR(1) NULL,
ADD COLUMN SIGNUP_OFFER_EXPIRY_TIME TIMESTAMP NULL;
CREATE INDEX LOYALTY_SCORE_SIGNUP_OFFER_EXPIRED ON KETTLE_DEV.LOYALTY_SCORE(SIGNUP_OFFER_EXPIRED) USING BTREE;

update KETTLE_DEV.LOYALTY_SCORE ls, KETTLE_DEV.CUSTOMER_INFO ci
set AVAILED_SIGNUP_OFFER = 'Y',
SIGNUP_OFFER_EXPIRED  = 'Y',
SIGNUP_OFFER_EXPIRY_TIME = '2020-01-08 06:00:00'
where 
ci.CUSTOMER_ID = ls.CUSTOMER_ID
and ls.SIGNUP_OFFER_EXPIRED IS NULL
and ci.IS_NUMBER_VERIFIED = 'Y'
and (ls.AVAILED_SIGNUP_OFFER = 'N' OR ls.AVAILED_SIGNUP_OFFER IS NULL)
AND DATEDIFF(current_date(), DATE(LAST_ORDER_TIME)) > 31;