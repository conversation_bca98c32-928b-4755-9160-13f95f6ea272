DELETE FROM UNIT_PRODUCT_PRICING
WHERE UNIT_PROD_REF_ID IN (select UNIT_PROD_REF_ID from UNIT_PRODUCT_MAPPING where <PERSON>IT_ID IN (10005));

DELETE FROM UNIT_PRODUCT_MAPPING where <PERSON><PERSON>_ID IN (10005);


INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 10005,upm.PRODUCT_ID,upm.PRODUCT_STATUS,upm.PRODUCT_START_DATE,upm.PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING upm where upm.UNIT_ID = 10006;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE, PRICE)
select upm1.UNIT_PROD_REF_ID,upp.DIMENSION_CODE, upp.PRICE from UNIT_PRODUCT_MAPPING upm,UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_PRICING upp
where upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
and upm.PRODUCT_ID =upm1.PRODUCT_ID
and upm.UNIT_ID = 10006
and upm1.UNIT_ID = 10005;


INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('2008', '20', 'AMEXCard', 'Amex Card Offer', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('2009', '20', 'AXISCard', 'Axis Card Offer', 'ACTIVE');
INSERT INTO `REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('2010', '20', 'TrulyMadlyOffer', 'Truly Madly Offer', 'ACTIVE');
