CREATE TABLE `KETTLE_DEV`.`PAYTM_PAYMENT_DETAILS` (
        `PAYTM_PAYMENT_DETAIL_ID` INT(11) NOT NULL AUTO_INCREMENT,
        `TRANSACTION_ID` VARCHAR(50) NOT NULL,
        `BANK_TRANSACTION_ID` VARCHAR(45) NULL DEFAULT NULL,
        `ORDER_ID` VARCHAR(45) NOT NULL,
        `TRANSACTION_AMOUNT` DECIMAL(10,2) NOT NULL,
        `STATUS` VARCHAR(45) NOT NULL,
        `TRANSACTION_TYPE` VARCHAR(45) NOT NULL,
        `RESPONSE_CODE` VARCHAR(45) NOT NULL,
        `RESPONSE_MESSAGE` VARCHAR(300) NULL DEFAULT NULL,
        `MID` VARCHAR(50) NOT NULL,
        `REFUND_AMOUNT` DECIMAL(10,2) NOT NULL,
        `REFUND_ID` VARCHAR(100) NOT NULL,
        `IS_AMOUNT_REFUNDED` VARCHAR(5) NOT NULL DEFAULT 'N',
        `TRANSACTION_DATE` DATE NOT NULL DEFAULT '0000-00-00',
        `UPDATED_TIME` DATE NOT NULL DEFAULT '0000-00-00',
        PRIMARY KEY (`PAYTM_PAYMENT_DETAIL_ID`));

ALTER TABLE `kettle`.`paytm_payment_details`
ADD COLUMN `GATEWAY_NAME` VARCHAR(45) NULL AFTER `UPDATED_TIME`,
ADD COLUMN `CARD_ISSUER` VARCHAR(45) NULL AFTER `GATEWAY_NAME`,
ADD COLUMN `PAYMENT_MODE` VARCHAR(45) NULL AFTER `CARD_ISSUER`,
ADD COLUMN `REFUND_DATE` VARCHAR(45) NULL AFTER `PAYMENT_MODE`,
ADD COLUMN `REFUND_TYPE` VARCHAR(45) NULL AFTER `REFUND_DATE`,
ADD COLUMN `REFUND_ID_BY_PAYTM` VARCHAR(45) NULL AFTER `REFUND_TYPE`,
ADD COLUMN `TOTAL_REFUNDED_AMOUNT` VARCHAR(45) NULL AFTER `REFUND_ID_BY_PAYTM`;

ALTER TABLE `kettle`.`paytm_payment_details`
CHANGE COLUMN `TOTAL_REFUNDED_AMOUNT` `TOTAL_REFUNDED_AMOUNT` DECIMAL(10,2) NULL DEFAULT NULL ;

UPDATE `KETTLE_MASTER_DEV`.`REF_LOOKUP` SET `RL_CODE`='MilkChai', `RL_NAME`='Milk Chai' WHERE `RL_ID`='3623';
UPDATE `KETTLE_MASTER_DEV`.`REF_LOOKUP` SET `RL_CODE`='Non-MilkChai', `RL_NAME`='Non-Milk Chai' WHERE `RL_ID`='3624';
UPDATE `KETTLE_MASTER_DEV`.`REF_LOOKUP` SET `RL_CODE`='Shakes', `RL_NAME`='Shakes' WHERE `RL_ID`='3625';
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('37', 'IcedTeas', 'Iced Teas', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('37', 'Lemonades', 'Lemonades', 'ACTIVE');
UPDATE `KETTLE_MASTER_DEV`.`REF_LOOKUP` SET `RL_CODE`='Meals', `RL_NAME`='Meals' WHERE `RL_ID`='3626';
UPDATE `KETTLE_MASTER_DEV`.`REF_LOOKUP` SET `RL_CODE`='QuickBites', `RL_NAME`='Quick Bites' WHERE `RL_ID`='3627';
UPDATE `KETTLE_MASTER_DEV`.`REF_LOOKUP` SET `RL_CODE`='Chaats', `RL_NAME`='Chaats' WHERE `RL_ID`='3628';
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_STATUS`) VALUES ('37', 'Sandwiches', 'Sandwiches', 'ACTIVE');
UPDATE `KETTLE_MASTER_DEV`.`REF_LOOKUP` SET `RL_CODE`='PackagedProducts', `RL_NAME`='Packaged Products' WHERE `RL_ID`='3631';