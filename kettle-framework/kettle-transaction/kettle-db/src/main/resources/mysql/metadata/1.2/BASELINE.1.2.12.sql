INSERT INTO BUSINESS_DIVISION (`BUSINESS_DIV_ID`, `BUSINESS_DIV_NAME`, `BUSIENSS_DIV_DESC`, `BUSIENSS_DIV_CATEGORY`, `COMPANY_ID`) 
VALUES ('1100', 'Chaayos Delivery', 'Chaayos Delivery', 'Delivery', '1000');
INSERT INTO DEPARTMENT (`DEPT_ID`, `DEPT_NAME`, `DEPT_DESC`, `BUSINESS_DIV_ID`) 
VALUES ('103', 'COD Call Center', 'COD Call Center', '1100');
INSERT INTO DESIGNATION (`DESIGNATION_ID`, `DESIGNATION_NAME`, `DESIGNATION_DESC`) 
VALUES ('1101', 'Manager', 'Manager');
INSERT INTO DESIGNATION (`DESIGNATION_ID`, `DESIGNATION_NAME`, `DESIGNATION_DESC`) 
VALUES ('1102', 'Team Lead', 'Team Lead');
INSERT INTO DESIGNATION (`DESIG<PERSON><PERSON>ON_ID`, `DE<PERSON>GNATION_NAME`, `DESIGNATION_DESC`) 
VALUES ('1103', 'Customer Care Executive', 'Customer Care Executive');

INSERT INTO DEPARTMENT_DESIGNATION_MAPPING (`DEPT_ID`, `DESIGNATION_ID`) 
VALUES ('103', '1101');
INSERT INTO DEPARTMENT_DESIGNATION_MAPPING (`DEPT_ID`, `DESIGNATION_ID`) 
VALUES ('103', '1102');
INSERT INTO DEPARTMENT_DESIGNATION_MAPPING (`DEPT_ID`, `DESIGNATION_ID`) 
VALUES ('103', '1103');

INSERT INTO ADDRESS_INFO
(`ADDRESS_ID`,`ADDRESS_LINE_1`,`ADDRESS_LINE_2`,`ADDRESS_LINE_3`,`CITY`,`STATE`,`COUNTRY`,`ZIPCODE`,`CONTACT_NUM_1`,`CONTACT_NUM_2`) VALUES (1101,'B-21','Udyog Vihar Phase 5',null,'Gurgaon','Haryana','India','122016','+91-1132551234','+91-1132551234');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11000', 'address', 'not known', 'city', 'state', 'India', 'pin', '+91-', '+91-', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11001', 'B-81, First Floor ', ' New Palam Vihar Ph-I', 'Gurgaon', 'Haryana', 'India', '122017', '+91-9873089095', '+91-9873089095', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11002', 'A7/2A Phase 5 ', ' Aaya Nagar', 'New Delhi ', 'New Delhi ', 'India', '110008', '+91-7053202967', '+91-7053202967', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11003', 'A-113 Jagdamba colony ', 'aali Village Sarita Vihar', 'New Delhi ', 'New Delhi ', 'India', '110076', '+91-7503494890', '+91-7503494890', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11004', 'A7/2A Phase 5 ', ' Aaya Nagar', 'New Delhi ', 'New Delhi ', 'India', '110008', '+91-8375089007', '+91-8375089007', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11005', 'H.No. 283 3rd Floor Gali No.3', 'Dabari Palam Rd', 'New Delhi', 'New Delhi', 'India', '110001', '+91-8934867004', '+91-8934867004', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11006', 'H.No.559/22  ', 'Gali No.6F Gandhi Nagar', 'Gurgaon', 'Haryana', 'India', '122001', '+91-8802304993', '+91-8802304993', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11007', 'H.No.283 ', 'Tarla Mohalla Ghitorni', 'New Delhi', 'New Delhi', 'India', '110001', '+91-9560153915', '+91-9560153915', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11008', 'Flat No.442,3Rd Floor ', 'Rao Phalad Appt. Near Sikanderpur', 'Gurgaon', 'Haryana', 'India', '122001', '+91-9717919217', '+91-9717919217', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11009', 'Rzp 79 Part 2 Gali No.1 Raj Nagar Dwarka', ' Sector 8 Palam Colony', 'New Delhi', 'New Delhi', 'India', '110001', '+91-8587805830', '+91-8587805830', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11010', 'Plot No. 37 ,Near Sheetal Hotel ', ' Noble Enclave  Sector 22', 'Gurgaon', 'Haryana', 'India', '122001', '+91-9643952200', '+91-9643952200', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11011', 'H.No C-1/29', 'Madhu vihar', 'New Delhi', 'New Delhi', 'India', '110059', '+91-9716046566', '+91-9716046566', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11012', 'I 2Nd 222', 'Madangiri', 'New Delhi', 'New Delhi', 'India', '110062', '+91-9818341016', '+91-9818341016', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11013', 'H.No.21 Sec-3', 'Pushpa Vihar Saket', 'New Delhi', 'New Delhi', 'India', '110062', '+91-9873138845', '+91-9873138845', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11014', 'C/O Dharam Pal Singh Yadav', ' Vill. Samaspur Sec-51', 'Gurgaon', 'Haryana', 'India', '122001', '+91-9560825498', '+91-9560825498', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11015', 'B 80 , New Palam  ', 'Vihar Ph-1', 'Gurgaon', 'Haryana', 'India', '122017', '+91-7065731535', '+91-7065731535', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11016', 'H-50 Ground Floor', ' Rajokari', 'New Delhi', 'New Delhi', 'India', '110038', '+91-9716416210', '+91-9716416210', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11017', ' H.No-97 Shyam Vihar Ph-1', ' Gali No-7 Dinpur,Najafgarh', 'New Delhi', 'New Delhi', 'India', '110038', '+91-8742971846', '+91-8742971846', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11018', 'Vill. Paprawat', ' Najafgarh', 'New Delhi', 'New Delhi', 'India', '110043', '+91-8882655128', '+91-8882655128', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11019', 'Hno- 1643 Sec-3', ' Vill. Samaspur Sec-51', ' Faridabad', 'Haryana', 'India', '121004', '+91-8587091669', '+91-8587091669', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11020', 'H No.-281, Vishwakarma Colony', 'Rajokri', 'New Delhi', 'New Delhi', 'India', '110038', '+91-8800875996', '+91-8800875996', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11021', 'Hno-2342, Sec-16  ', '', ' Faridabad', 'Haryana', 'India', '121002', '+91-9899796311', '+91-9899796311', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11022', 'H.No. 283 3rd Floor Gali No. 3', ' Dashrath Puri Dabari Palam Rd', 'New Delhi', 'New Delhi', 'India', '110038', '+91-7668281055', '+91-7668281055', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11023', '257A/4, Sheetla Colony', 'Near Sheetla Mata Mandir, Sec-5', 'Gurgaon', 'Haryana', 'India', '122001', '+91-9718757936', '+91-9718757936', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('11025','B-21','Udyog Vihar Phase 5','Gurgaon','Haryana','India','122016','+91-1132551234','+91-1132551234', 'RESIDENTIAL');

INSERT INTO UNIT_DETAIL
(`UNIT_ID`,`UNIT_NAME`,`UNIT_REGION`,`UNIT_EMAIL`,`UNIT_CATEGORY`,`START_DATE`,`UNIT_STATUS`,`TIN`,`UNIT_ADDR_ID`,`BUSINESS_DIV_ID`,`NO_OF_TERMINALS`)
VALUES
(11001,'Chai On Demand','NCR','<EMAIL>','COD','2015-11-15','Active','***********',1101,1100,15);

INSERT INTO UNIT_TAX_MAPPING
(`TAX_PROFILE_ID`,`UNIT_ID`,`TAX_PERCENTAGE`,`PROFILE_STATUS`,`STATE`)
VALUES
(1,11001,12.50,'ACTIVE','HARYANA'),
(2,11001,5.00,'ACTIVE','HARYANA'),
(3,11001,5.00,'ACTIVE','HARYANA'),
(4,11001,0.00,'ACTIVE','HARYANA'),
(5,11001,0.00,'ACTIVE','HARYANA'),
(6,11001,0.00,'ACTIVE','HARYANA');


INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`) VALUES ('110000', 'Nadeem Yamin', 'M', '11000', '11000', '+91-', '+91-', '103', '1101', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110001', 'Himanshu Shukla', 'M', '11001', '11001', '+91-9873089095', '+91-9873089095', '103', '1102', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110000');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110002', 'Sheela Devi Chettiry', 'F', '11002', '11002', '+91-7053202967', '+91-7053202967', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110003', 'Manveer', 'M', '11003', '11003', '+91-7503494890', '+91-7503494890', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110004', 'Nitya Thapa', 'F', '11004', '11004', '+91-8375089007', '+91-8375089007', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110005', 'Deepak Gautam', 'M', '11005', '11005', '+91-8934867004', '+91-8934867004', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110006', 'Dinesh Kumar', 'M', '11006', '11006', '+91-8802304993', '+91-8802304993', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110007', 'Rekha', 'F', '11007', '11007', '+91-9560153915', '+91-9560153915', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110008', 'Joshin Galosh', 'M', '11008', '11008', '+91-9717919217', '+91-9717919217', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110009', 'Rahul Bhardwaj', 'M', '11009', '11009', '+91-8587805830', '+91-8587805830', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110010', 'Devvert Singh', 'M', '11010', '11010', '+91-9643952200', '+91-9643952200', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110011', 'Dheeraj Kumar', 'M', '11011', '11011', '+91-9716046566', '+91-9716046566', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110012', 'Shivam Pal Singh', 'M', '11012', '11012', '+91-9818341016', '+91-9818341016', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110013', 'Virendra Singh Rawat', 'M', '11013', '11013', '+91-9873138845', '+91-9873138845', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110014', 'Mayank Saxena', 'M', '11014', '11014', '+91-9560825498', '+91-9560825498', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110015', 'Akash Dhasmana', 'M', '11015', '11015', '+91-7065731535', '+91-7065731535', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110016', 'Dharmendar', 'M', '11016', '11016', '+91-9716416210', '+91-9716416210', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110017', 'Rahul Kumar', 'M', '11017', '11017', '+91-8742971846', '+91-8742971846', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110018', 'Jitender Kumar', 'M', '11018', '11018', '+91-8882655128', '+91-8882655128', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110019', 'Dolly Mittal', 'F', '11019', '11019', '+91-8587091669', '+91-8587091669', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110020', 'Suraj Singh', 'M', '11020', '11020', '+91-8800875996', '+91-8800875996', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110021', 'Akanksha Chawla', 'F', '11021', '11021', '+91-9899796311', '+91-9899796311', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110022', 'Amit Kumar Tripathi', 'M', '11022', '11022', '+91-7668281055', '+91-7668281055', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110023', 'Rajat Kumar Mishra', 'M', '11023', '11023', '+91-9718757936', '+91-9718757936', '103', '1103', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');
INSERT INTO EMPLOYEE_DETAIL (`EMP_ID`, `EMP_NAME`, `EMP_GENDER`, `EMP_CURRENT_ADDR`, `EMP_PERMANENT_ADDR`, `EMP_CONTACT_NUM_1`, `EMP_CONTACT_NUM_2`, `DEPTARTMENT_ID`, `DESIGNATION_ID`, `EMPLOYMENT_TYPE`, `EMPLOYMENT_STATUS`, `JOINING_DATE`, `TERMINATION_DATE`, `REPORTING_MANAGER_ID`) VALUES ('110025', 'Training Call Center', 'M', '11025', '11025', '+91-1132551234', '+91-1132551234', '103', '1101', 'FULL_TIME', 'ACTIVE', '2015-01-01', '9999-12-01', '110001');

INSERT INTO `EMPLOYEE_UNIT_MAPPING`
(`EMP_ID`,
`UNIT_ID`)
VALUES
(110000,11001),
(110001,11001),
(110002,11001),
(110003,11001),
(110004,11001),
(110005,11001),
(110006,11001),
(110007,11001),
(110008,11001),
(110009,11001),
(110010,11001),
(110011,11001),
(110012,11001),
(110013,11001),
(110014,11001),
(110015,11001),
(110016,11001),
(110017,11001),
(110018,11001),
(110019,11001),
(110020,11001),
(110021,11001),
(110022,11001),
(110023,11001),
(110025,11001);


INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
VALUES
(110000,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110001,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110002,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110003,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110004,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110005,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110006,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110007,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110008,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110009,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110010,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110011,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110012,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110013,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110014,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110015,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110016,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110017,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110018,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110019,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110020,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110021,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110022,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110023,'0UxQYmqlaaaY1jDVAZWNQQ=='),
(110023,'0UxQYmqlaaaY1jDVAZWNQQ==');


INSERT INTO ADDRESS_INFO
(`ADDRESS_ID`,`ADDRESS_LINE_1`,`ADDRESS_LINE_2`,`ADDRESS_LINE_3`,`CITY`,`STATE`,`COUNTRY`,`ZIPCODE`,`CONTACT_NUM_1`,`CONTACT_NUM_2`)
VALUES
(1201,'U-1/6 ground floor','DLF phase 3',null,'Gurgaon','Haryana','India','122010','+91-1132551234','+91-1132551234'),
(1202,'A-77','Udyog Vihar Phase 5',null,'Gurgaon','Haryana','India','122016','+91-1132551234','+91-1132551234'),
(1203,'Shop No-4,Plot Number 668','Vil Kanhai,Sec 45',null,'Gurgaon','Haryana','India','122016','+91-1132551234','+91-1132551234'),
(1204,'First Floor,SCO-20','Sarasvati Vihar Complex,Chakkarpur',null,'Gurgaon','Haryana','India','122002','+91-1132551234','+91-1132551234'),
(1205,'A-8','Sector 9',null,'Noida','Uttar Pradesh','India','276001','+91-1132551234','+91-1132551234'),
(1206,'First Floor,Shed number-9','Phase 4,Okhla',null,'New Delhi','New Delhi','India','110020','+91-1132551234','+91-1132551234'),
(1207,'Shop 29,TDI','Jasola',null,'New Delhi','New Delhi','India','110025','+91-1132551234','+91-1132551234'),
(1208,'1E/19 B','Jhandewalan',null,'New Delhi','New Delhi','India','110051','+91-1132551234','+91-1132551234'),
(1209,'A1/116','Lajpat Nagar part-1',null,'New Delhi','New Delhi','India','110024','+91-1132551234','+91-1132551234');

INSERT INTO UNIT_DETAIL
(`UNIT_ID`,`UNIT_NAME`,`UNIT_REGION`,`UNIT_EMAIL`,`UNIT_CATEGORY`,`START_DATE`,`UNIT_STATUS`,`TIN`,`UNIT_ADDR_ID`,`BUSINESS_DIV_ID`,`NO_OF_TERMINALS`)
VALUES
(12001,'DLF Phase 3','NCR','<EMAIL>','DELIVERY','2015-11-15','Active','***********',1201,1100,1),
(12002,'Udyog Vihar 5','NCR','<EMAIL>','DELIVERY','2015-11-15','Active','***********',1202,1100,1),
(12003,'Sector 45','NCR','<EMAIL>','DELIVERY','2015-11-15','Active','***********',1203,1100,1),
(12004,'Mg Road','NCR','<EMAIL>','DELIVERY','2015-11-15','Active','***********',1204,1100,1),
(12005,'Noida Sector 9','NCR','<EMAIL>','DELIVERY','2015-11-15','Active','***********',1205,1100,1),
(12006,'Okhla','NCR','<EMAIL>','DELIVERY','2015-11-15','Active','***********',1206,1100,1),
(12007,'Jasola','NCR','<EMAIL>','DELIVERY','2015-11-15','Active','***********',1207,1100,1),
(12008,'Jhandewalan','NCR','<EMAIL>','DELIVERY','2015-11-15','Active','***********',1208,1100,1),
(12009,'Lajpat Nagar','NCR','<EMAIL>','DELIVERY','2015-11-15','Active','***********',1209,1100,1);

INSERT INTO UNIT_TAX_MAPPING
(`TAX_PROFILE_ID`,`UNIT_ID`,`TAX_PERCENTAGE`,`PROFILE_STATUS`,`STATE`)
VALUES
(1,12001,12.50,'ACTIVE','HARYANA'),
(2,12001,5.00,'ACTIVE','HARYANA'),
(3,12001,5.00,'ACTIVE','HARYANA'),
(4,12001,0.00,'ACTIVE','HARYANA'),
(5,12001,0.00,'ACTIVE','HARYANA'),
(6,12001,0.00,'ACTIVE','HARYANA'),
(1,12002,12.50,'ACTIVE','HARYANA'),
(2,12002,5.00,'ACTIVE','HARYANA'),
(3,12002,5.00,'ACTIVE','HARYANA'),
(4,12002,0.00,'ACTIVE','HARYANA'),
(5,12002,0.00,'ACTIVE','HARYANA'),
(6,12002,0.00,'ACTIVE','HARYANA'),
(1,12003,12.50,'ACTIVE','HARYANA'),
(2,12003,5.00,'ACTIVE','HARYANA'),
(3,12003,5.00,'ACTIVE','HARYANA'),
(4,12003,0.00,'ACTIVE','HARYANA'),
(5,12003,0.00,'ACTIVE','HARYANA'),
(6,12003,0.00,'ACTIVE','HARYANA'),
(1,12004,12.50,'ACTIVE','HARYANA'),
(2,12004,5.00,'ACTIVE','HARYANA'),
(3,12004,5.00,'ACTIVE','HARYANA'),
(4,12004,0.00,'ACTIVE','HARYANA'),
(5,12004,0.00,'ACTIVE','HARYANA'),
(6,12004,0.00,'ACTIVE','HARYANA'),
(1,12005,14.50,'ACTIVE','UTTAR PRADESH'),
(2,12005,5.00,'ACTIVE','UTTAR PRADESH'),
(3,12005,0.00,'ACTIVE','UTTAR PRADESH'),
(4,12005,0.00,'ACTIVE','UTTAR PRADESH'),
(5,12005,0.00,'ACTIVE','UTTAR PRADESH'),
(6,12005,0.00,'ACTIVE','UTTAR PRADESH'),
(1,12006,12.50,'ACTIVE','NEW DELHI'),
(2,12006,5.00,'ACTIVE','NEW DELHI'),
(3,12006,0.00,'ACTIVE','NEW DELHI'),
(4,12006,0.00,'ACTIVE','NEW DELHI'),
(5,12006,0.00,'ACTIVE','NEW DELHI'),
(6,12006,0.00,'ACTIVE','NEW DELHI'),
(1,12007,12.50,'ACTIVE','NEW DELHI'),
(2,12007,5.00,'ACTIVE','NEW DELHI'),
(3,12007,0.00,'ACTIVE','NEW DELHI'),
(4,12007,0.00,'ACTIVE','NEW DELHI'),
(5,12007,0.00,'ACTIVE','NEW DELHI'),
(6,12007,0.00,'ACTIVE','NEW DELHI'),
(1,12008,12.50,'ACTIVE','NEW DELHI'),
(2,12008,5.00,'ACTIVE','NEW DELHI'),
(3,12008,0.00,'ACTIVE','NEW DELHI'),
(4,12008,0.00,'ACTIVE','NEW DELHI'),
(5,12008,0.00,'ACTIVE','NEW DELHI'),
(6,12008,0.00,'ACTIVE','NEW DELHI'),
(1,12009,12.50,'ACTIVE','NEW DELHI'),
(2,12009,5.00,'ACTIVE','NEW DELHI'),
(3,12009,0.00,'ACTIVE','NEW DELHI'),
(4,12009,0.00,'ACTIVE','NEW DELHI'),
(5,12009,0.00,'ACTIVE','NEW DELHI'),
(6,12009,0.00,'ACTIVE','NEW DELHI');


INSERT INTO DEPARTMENT
(`DEPT_ID`,
`DEPT_NAME`,
`DEPT_DESC`,
`BUSINESS_DIV_ID`)
VALUES
(104,
'COD Operations',
'COD Operations',
1100);

INSERT INTO DESIGNATION (`DESIGNATION_ID`, `DESIGNATION_NAME`, `DESIGNATION_DESC`) 
VALUES ('1201', 'Manager', 'Manager');
INSERT INTO DESIGNATION (`DESIGNATION_ID`, `DESIGNATION_NAME`, `DESIGNATION_DESC`) 
VALUES ('1202', 'Shift Manager', 'Shift Manager');

INSERT INTO DEPARTMENT_DESIGNATION_MAPPING (`DEPT_ID`,`DESIGNATION_ID`)
VALUES
(104,1201);

INSERT INTO DEPARTMENT_DESIGNATION_MAPPING
(`DEPT_ID`,`DESIGNATION_ID`)
VALUES
(104,1202);

INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12001', 'D-2, 2 floor', 'Greenpark extension', 'New Delhi', 'New Delhi', 'India', '110016', '+91-9717449667', '+91-9717449667', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12002', '387 Rajokari', 'New Delhi', 'New Delhi', 'India', '110016', '+91-9599597732', '+91-9599597732', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12003', 'Suraskha Vihar', 'Vikas Nagar ,Uttam Nagar', 'New Delhi', 'New Delhi', 'India', '110016', '+91-9968562513', '+91-9968562513', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12004', 'House No136 ,ground floor', 'Aliganj , kotla Mubarakpur', 'New Delhi', 'New Delhi', 'India', '110003', '+91-9911271663', '+91-9711613256', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12005', '387 Rajokari', NULL, 'New Delhi', 'New Delhi', 'India', '110016', '+91-9719514151', '+91-9719514151', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12006', 'village -dubri', 'PO kumanda', 'tehri garhwal', 'Uttarakhand', 'India', '110016', '+91-8860586815', '+91-8860586815', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12007', 'H.No.-266, ward no :02', 'Mehrauli', 'New Delhi', 'New Delhi', 'India', '110016', '+91-9910735453', '+91-9910735453', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12008', 'Gali no 6, pilor no 706', 'Uttam nagar', 'New Delhi', 'New Delhi', 'India', '110016', '+91-7838876293', '+91-7838876293', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12009', 'House No: 46, Room D3', 'Sikkandarpur', 'Gurgaon', 'Haryana', 'India', '122016', '+91-9654176353', '+91-9654176353', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12010', 'House no :17, Gali No 420', 'Ashok Vihar , Phase3', 'Gurgaon', 'Haryana', 'India', '122016', '+91-9971172851', '+91-9971172851', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12011', 'House No:308 , Krisna Apartment', 'Room No18 , Rangpuri , Mahipalpur', 'New Delhi', 'New Delhi', 'India', '110037', '+91-9599992998', '+91-9599992998', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12012', 'House No :188', 'vill& Po Mohan bari', ' Jhajjar', 'Haryana', 'India', '124146', '+91-9672843761', '+91-9672843761', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12013', 'H.NO. 131 Mumrejpur', 'Post.Jahangirpur', 'Bulandsahar', 'Uttar Pradesh', 'India', '203141', '+91-9871450093', '+91-9871450093', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12014', 'Village Sainikhera', 'Near Signature Tower', 'Gurgaon', 'Haryana', 'India', '122016', '+91-8802316157', '+91-8802316157', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12015', 'E770 , Jagmal Enclave', 'Roshan Nagar', 'Faridabad', 'Haryana', 'India', '122016', '+91-', '+91-', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12016', '1786 , Phase 3 , JJ colony', 'Madanpur Khadar , Sarita Vihar', 'New Delhi', 'New Delhi', 'India', '110037', '+91-', '+91-', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12017', 'House No:15A , Street no :10 A', 'Molarband extension,Badarpur', 'New Delhi', 'New Delhi', 'India', '110044', '+91-9871375885', '+91-9910783637', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12018', 'I76 , Janak Vihar', 'IARI Pusa Campus ', 'New Delhi', 'New Delhi', 'India', '110044', '+91-9582499781', '+91-9582499781', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12019', 'Nagloi , Pratap Vihar', 'Phase 3', 'New Delhi', 'New Delhi', 'India', '110044', '+91-9899019009', '+91-9899019009', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12020', 'E Block ,283 , Street No:8', 'East Vinod Nagar', 'New Delhi', 'New Delhi', 'India', '110044', '+91-9999539104', '+91-9999539104', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12021', '587/6,Band gali', 'Sri lal chowk , Kalka ji , Govind Puri', 'New Delhi', 'New Delhi', 'India', '110044', '+91-9582551062', '+91-9800694845', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12022', '49, Sanjay colony', 'Arthala, Mohan nagar', 'Ghaziabad', 'Uttar Pradesh', 'India', '203141', '+91-9953982322', '+91-9953982322', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12023', 'Laximi Nagar', 'New Delhi', 'New Delhi', 'India', '110044', '+91-9560064102', '+91-9560064102', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12024', '5A/10915 , Karol Bagh', 'West extension Area', 'New Delhi', 'New Delhi', 'India', '110044', '+91-9599432845', '+91-9599432845', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12025', 'House No- 317,Pocket- 2', 'Paschim Puri', 'New Delhi', 'New Delhi', 'India', '110044', '+91-9999040657', '+91-9999040657', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12026', 'House no 312, k-block', 'street no 4, mahipalpur extension', 'New Delhi', 'New Delhi', 'India', '110037', '+91-9873637678', '+91-9873637678', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12027', 'G-256 wazirpur', 'J.J Colony', 'New Delhi', 'New Delhi', 'India', '110052', '+91-7503467678', '+91-9953337372', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12028', 'House no:109, Sec:45', 'mohyal colony', 'Gurgaon', 'Haryana', 'India', '122016', '+91-9599046586', '+91-9872002307', 'RESIDENTIAL');
INSERT INTO ADDRESS_INFO (`ADDRESS_ID`, `ADDRESS_LINE_1`, `ADDRESS_LINE_2`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `CONTACT_NUM_2`, `ADDRESS_TYPE`) VALUES ('12029', 'A-49/I8', 'Welcome,Shaharda', 'New Delhi', 'New Delhi', 'India', '110037', '+91-9013385382', '+91-9313288599', 'RESIDENTIAL');


INSERT INTO `EMPLOYEE_DETAIL`
(`EMP_ID`,`EMP_NAME`,`EMP_GENDER`,`EMP_CURRENT_ADDR`,`EMP_PERMANENT_ADDR`,`EMP_CONTACT_NUM_1`,`EMP_CONTACT_NUM_2`,`DEPTARTMENT_ID`,
`DESIGNATION_ID`,`EMPLOYMENT_TYPE`,`EMPLOYMENT_STATUS`,`BIOMETRIC_IDENTIFIER`,`JOINING_DATE`,`TERMINATION_DATE`,`REPORTING_MANAGER_ID`)
VALUES
(120001,'Nadeem Yamin','M',12001,12001,'+91-9717449667','+91-9717449667',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',null),
(120002,'Krishna Kumar','M',12002,12002,'+91-9599597732','+91-9599597732',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120003,'Raj Singh','M',12003,12003,'+91-9968562513','+91-9968562513',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120004,'Ashwini','M',12004,12004,'+91-9911271663','+91-9711613256',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120005,'Shailendra kumar','M',12005,12005,'+91-9719514151','+91-9719514151',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120006,'Sandeep pawar','M',12006,12006,'+91-8860586815','+91-8860586815',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120007,'Satyabhan','M',12007,12007,'+91-9910735453','+91-9910735453',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120008,'Sweety Kumari','M',12008,12008,'+91-7838876293','+91-7838876293',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120009,'Rahul Singh','M',12009,12009,'+91-9654176353','+91-9654176353',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120010,'Rajendra Prajapati','M',12010,12010,'+91-9971172851','+91-9971172851',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120011,'Sonu Khatri','M',12011,12011,'+91-9599992998','+91-9599992998',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120012,'Manoj kumar','M',12012,12012,'+91-9672843761','+91-9672843761',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120013,'Harendra Raghav','M',12013,12013,'+91-9871450093','+91-9871450093',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120014,'Binayakar Kumar','M',12014,12014,'+91-8802316157','+91-8802316157',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120015,'Pawan Gosain','M',12015,12015,'+91-','+91-',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120016,'Ajeet','M',12016,12016,'+91-','+91-',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120017,'Rahul Tiwari','M',12017,12017,'+91-9871375885','+91-9910783637',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120018,'Nanadkishore','M',12018,12018,'+91-9582499781','+91-9582499781',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120019,'Purushottom','M',12019,12019,'+91-9899019009','+91-9899019009',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120020,'Ashish hindman','M',12020,12020,'+91-9999539104','+91-9999539104',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120021,'Tazuddin Ajmeri','M',12021,12021,'+91-9582551062','+91-9800694845',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120022,'Junaid saif','M',12022,12022,'+91-9953982322','+91-9953982322',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120023,'Anthony','M',12023,12023,'+91-9560064102','+91-9560064102',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120024,'Soumen','M',12024,12024,'+91-9599432845','+91-9599432845',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120025,'Ravinder Bajaj','M',12025,12025,'+91-9999040657','+91-9999040657',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120026,'Dharmendra Singh','M',12026,12026,'+91-9873637678','+91-9873637678',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120027,'Virendra','M',12027,12027,'+91-7503467678','+91-9953337372',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120028,'Ashutosh Vaid','M',12028,12028,'+91-9599046586','+91-9872002307',104,1202,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001),
(120029,'Shahrukh','M',12029,12029,'+91-9013385382','+91-9313288599',104,1201,'FULL_TIME','ACTIVE',null,'2015-01-01','9999-12-01',120001);

INSERT INTO `EMPLOYEE_UNIT_MAPPING`
(`EMP_ID`,
`UNIT_ID`)
VALUES
(120001,12001),
(120001,12002),
(120001,12003),
(120001,12004),
(120001,12005),
(120001,12006),
(120001,12007),
(120001,12008),
(120001,12009),
(120002,12002),
(120003,12002),
(120004,12002),
(120005,12001),
(120006,12001),
(120007,12001),
(120008,12001),
(120009,12004),
(120010,12004),
(120011,12004),
(120012,12003),
(120013,12003),
(120014,12003),
(120015,12006),
(120016,12006),
(120017,12006),
(120018,12006),
(120019,12007),
(120020,12007),
(120021,12007),
(120022,12005),
(120023,12005),
(120024,12005),
(120025,12008),
(120026,12008),
(120027,12008),
(120028,12009),
(120029,12009);


INSERT INTO EMPLOYEE_PASS_CODE
(`EMP_ID`,`EMP_PASS_CODE`)
VALUES
(120001,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120002,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120003,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120004,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120005,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120006,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120007,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120008,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120009,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120010,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120011,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120012,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120013,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120014,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120015,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120016,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120017,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120018,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120019,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120020,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120021,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120022,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120023,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120024,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120025,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120026,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120027,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120028,'0UxQYmqlaaaY1jDVAZWNQQ==' ),
(120029,'0UxQYmqlaaaY1jDVAZWNQQ==' );


