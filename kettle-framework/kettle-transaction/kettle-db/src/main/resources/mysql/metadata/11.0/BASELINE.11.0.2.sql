INSERT INTO `KETTLE_JOBS_DEV`.`JO<PERSON>_DEFINITION` (`JOB_<PERSON>`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JO<PERSON>_STATUS`)
VALUES ('1', 'sales', 'sales data', 'ADHOC', 'sales_data_job.kjb', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('2', 'tax', 'tax data job', 'ADHOC', 'tax_data_job.kjb', 'ACTIVE');

ALTER TABLE KETTLE_JOBS_DEV.JOB_EXECUTION_DEFINITION ADD COLUMN BUSINESS_DATE TIMESTAMP ;

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION` (`PARAM_NAME`, `PARAM_CODE`, `PARAM_DESCRIPTION`, `PARAM_TYPE`, `PARAM_CATEGORY`)
VALUES ('BUSINESS_DATE', 'BUSINESS_DATE', 'Date for which calculations are performed', 'java.util.Date', 'EXECUTION');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION` (`PARAM_NAME`, `PARAM_CODE`, `PARAM_DESCRIPTION`, `PARAM_TYPE`, `PARAM_CATEGORY`)
VALUES ('JOB_EXECUTION_TIME','JOB_EXECUTION_TIME','It represent time at which job is executed','java.util.Date','EXECUTION');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION` (`PARAM_NAME`, `PARAM_CODE`, `PARAM_DESCRIPTION`, `PARAM_TYPE`, `PARAM_CATEGORY`)
VALUES ('JOB_EXECUTION_ID','JOB_EXECUTION_ID','It represent job execution Id','java.lang.Integer','EXECUTION');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION` (`PARAM_NAME`, `PARAM_CODE`, `PARAM_DESCRIPTION`, `PARAM_TYPE`, `PARAM_CATEGORY`)
VALUES ('JOB_ID','JOB_ID','It represent Job Id ','java.lang.Integer','JOB');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION` (`PARAM_NAME`, `PARAM_CODE`, `PARAM_DESCRIPTION`, `PARAM_TYPE`, `PARAM_CATEGORY`)
VALUES ('JOB_STEP_ID','JOB_STEP_ID','It represent the step Id','java.lang.Integer','JOB');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION` (`PARAM_NAME`, `PARAM_CODE`, `PARAM_DESCRIPTION`, `PARAM_TYPE`, `PARAM_CATEGORY`)
VALUES ('SCHEMA_SUFFIX','SCHEMA_SUFFIX','It represent schema suffix of database','java.lang.String','SYSTEM');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION` (`PARAM_NAME`, `PARAM_CODE`, `PARAM_DESCRIPTION`, `PARAM_TYPE`, `PARAM_CATEGORY`)
VALUES ('HOST_NAME','HOST_NAME','It represent host name of database','java.lang.String','SYSTEM');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION` (`PARAM_NAME`, `PARAM_CODE`, `PARAM_DESCRIPTION`, `PARAM_TYPE`, `PARAM_CATEGORY`)
VALUES ('USERNAME','USER_NAME','It represent user name of database','java.lang.String','SYSTEM');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION` (`PARAM_NAME`, `PARAM_CODE`, `PARAM_DESCRIPTION`, `PARAM_TYPE`, `PARAM_CATEGORY`)
VALUES ('TYPE_OF_DATA','TYPE_OF_DATA','It represent type of data for which calculation is being performed','java.lang.String','JOB');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('1', '9', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '1', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '2', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '3', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '4', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '5', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '6', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '7', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '8', 'ACTIVE');
INSERT INTO `KETTLE_JOBS_DEV`.`JOBS_PARAM_DEFINITION_MAPPING` (`JOB_ID`, `PARAM_ID`, `STATUS`) VALUES ('2', '9', 'ACTIVE');

ALTER TABLE `KETTLE_JOBS_DEV`.`AGGREGATED_RESULT_DEFINITION`
CHANGE COLUMN `EXECUTION_TIME` `EXECUTION_TIME` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('3', 'channel_partner', 'channel partner collection data job', 'ADHOC', 'channel_partner_collection_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('4', 'credit_payment', 'credit payment job', 'ADHOC', 'credit_payment.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('5', 'gift_card_discount', 'gift card discount job', 'ADHOC', 'gift_card_discount_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('6', 'gift_card_payment', 'gift card payment job', 'ADHOC', 'gift_card_payments_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('7', 'gift_card_sales', 'gift card sales job', 'ADHOC', 'gift_card_sales_job.kjb', 'ACTIVE');

INSERT INTO `KETTLE_JOBS_DEV`.`JOB_DEFINITION` (`JOB_ID`, `JOB_NAME`, `JOB_DESCRIPTION`, `JOB_TYPE`, `JOB_FILE_NAME`, `JOB_STATUS`)
VALUES ('8', 'round_off', 'round off job', 'ADHOC', 'round_off_job.kjb', 'ACTIVE');

ALTER TABLE `KETTLE_JOBS_DEV`.`JOB_EXECUTION_DEFINITION`
CHANGE COLUMN `BUSINESS_DATE` `BUSINESS_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;
