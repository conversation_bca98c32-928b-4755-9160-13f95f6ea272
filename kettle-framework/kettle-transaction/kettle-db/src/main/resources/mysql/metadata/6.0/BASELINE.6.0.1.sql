ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN VARIANCE_PCC  DECIMAL(20,2),
ADD COLUMN VARIANCE_PCC_TAX DECIMAL(20,2),
ADD COLUMN 	VARIANCE_YC DECIMAL(20,2),
ADD COLUMN 	VARIANCE_YC_TAX DECIMAL(20,2),
ADD COLUMN 	CONVEYANCE_MARKETING DECIMAL(20,2),
ADD COLUMN 	CONVEYANCE_OPERATION DECIMAL(20,2),
ADD COLUMN 	CONVEYANCE_OTHERS DECIMAL(20,2),
ADD COLUMN 	AUDIT_FEE DECIMAL(20,2),
ADD COLUMN 	AUDIT_FEE_OUT_OF_POCKET DECIMAL(20,2),
ADD COLUMN 	BAD_DEBTS_WRITTEN_OFF DECIMAL(20,2),
ADD COLUMN 	BROKERAGE DECIMAL(20,2),
ADD COLUMN 	CHARITY_AND_DONATIONS DECIMAL(20,2),
ADD COLUMN 	DOMESTIC_TICKETS_AND_HOTELS DECIMAL(20,2),
ADD COLUMN 	INTERNATIONAL_TICKETS_AND_HOTELS DECIMAL(20,2),
ADD COLUMN 	HOUSEKEEPING_CHARGES DECIMAL(20,2),
ADD COLUMN 	LATE_FEE_CHARGES DECIMAL(20,2),
ADD COLUMN 	MARKETING_DATA_ANALYSIS DECIMAL(20,2),
ADD COLUMN 	MISCELLANEOUS_EXPENSES DECIMAL(20,2),
ADD COLUMN 	PENALTY DECIMAL(20,2),
ADD COLUMN 	PHOTO_COPY_EXPENSES DECIMAL(20,2),
ADD COLUMN 	QCR_EXPENSE DECIMAL(20,2),
ADD COLUMN 	RECRUITMENT_CONSULTANTS DECIMAL(20,2),
ADD COLUMN 	ROC_FEES DECIMAL(20,2),
ADD COLUMN 	TRAVELLING_EXPENSE_ODC DECIMAL(20,2),
ADD COLUMN 	TRAVELLING_EXPENSE DECIMAL(20,2),
ADD COLUMN 	DEBIT_CREDIT_WRITTEN_OFF DECIMAL(20,2),
ADD COLUMN 	DIFFENECE_IN_EXCHANGE DECIMAL(20,2),
ADD COLUMN 	SERVICE_CHARGE_PAID DECIMAL(20,2),
ADD COLUMN 	INSURANCE_VEHICLE DECIMAL(20,2),
ADD COLUMN 	OTHERS_AMC DECIMAL(20,2),
ADD COLUMN 	OTHERS_MAINTENANCE DECIMAL(20,2),
ADD COLUMN 	RND_ENGINEERING_EXPENSE DECIMAL(20,2),
ADD COLUMN 	BYOD_CHARGES DECIMAL(20,2),
ADD COLUMN 	CAR_LEASE DECIMAL(20,2),
ADD COLUMN 	DRIVER_SALARY DECIMAL(20,2),
ADD COLUMN 	GRATUITY DECIMAL(20,2),
ADD COLUMN 	INSUARANCE_ACCIDENTAL DECIMAL(20,2),
ADD COLUMN 	INSUARANCE_MEDICAL DECIMAL(20,2),
ADD COLUMN 	SUPPORT_OPS_TURNOVER DECIMAL(20,2),
ADD COLUMN 	EMPLOYEE_FACILITATION_EXPENSES DECIMAL(20,2),
ADD COLUMN 	TELEPHONE_SR DECIMAL(20,2),
ADD COLUMN 	VEHICLE_RUNNING_AND_MAINT_SR DECIMAL(20,2),
ADD COLUMN 	EMPLOYEE_STOCK_OPTION_EXPENSE DECIMAL(20,2),
ADD COLUMN 	EMPLOYER_CONTRIBUTION_LWF DECIMAL(20,2),
ADD COLUMN 	ESIC_EMPLOYER_CONT DECIMAL(20,2),
ADD COLUMN 	LEAVE_TRAVEL_REIMBURSEMENT DECIMAL(20,2),
ADD COLUMN 	PF_ADMINISTRATION_CHARGES DECIMAL(20,2),
ADD COLUMN 	PF_EMPLOYER_CONT DECIMAL(20,2),
ADD COLUMN 	QUATERLY_INCENTIVE DECIMAL(20,2),
ADD COLUMN 	SUPPORT_AUDIT DECIMAL(20,2),
ADD COLUMN 	SUPPORT_CCC DECIMAL(20,2),
ADD COLUMN 	SUPPORT_IT DECIMAL(20,2),
ADD COLUMN 	SUPPORT_MAINTENANCE DECIMAL(20,2),
ADD COLUMN 	SUPPORT_COMM_WH DECIMAL(20,2),
ADD COLUMN 	CAPITAL_IMPORVEMENT_EXPENSES DECIMAL(20,2),
ADD COLUMN 	LEASE_HOLD_IMPROVEMENTS DECIMAL(20,2),
ADD COLUMN 	FIXED_ASSETS_EQUIPMENT DECIMAL(20,2),
ADD COLUMN 	FIXED_ASSET_FURNITURE DECIMAL(20,2),
ADD COLUMN 	FIXED_ASSETS_IT DECIMAL(20,2),
ADD COLUMN 	FIXED_ASSETS_KITCHEN_EQUIPMENT DECIMAL(20,2),
ADD COLUMN 	FIXED_ASSETS_OFFICE_EQUIPMENT DECIMAL(20,2),
ADD COLUMN 	MARKETINFG_LAUNCH DECIMAL(20,2),
ADD COLUMN 	PRE_OPENING_CONSUMABLE DECIMAL(20,2),
ADD COLUMN 	PRE_OPENING_OTHERS DECIMAL(20,2),
ADD COLUMN 	PRE_OPENING_RENT DECIMAL(20,2),
ADD COLUMN 	PRE_OPEINING_SALARY DECIMAL(20,2),
ADD COLUMN 	BANK_CHARGES DECIMAL(20,2),
ADD COLUMN 	INTREST_ON_LOAN DECIMAL(20,2),
ADD COLUMN 	INTREST_ON_TDS_OR_GST DECIMAL(20,2),
ADD COLUMN 	INTREST_ON_FDR DECIMAL(20,2),
ADD COLUMN 	PROFIT_SALE_MUTUTAL_FUND DECIMAL(20,2),
ADD COLUMN 	INTREST_INCOME_TAX_REFUND DECIMAL(20,2),
ADD COLUMN 	MISC_INCOME DECIMAL(20,2),
ADD COLUMN 	DISCOUNT_RECEIVED DECIMAL(20,2),
ADD COLUMN 	INTERIOR_DESIGNING_CHARGE DECIMAL(20,2),
ADD COLUMN 	SCRAPE DECIMAL(20,2),
ADD COLUMN 	SERVICE_CHARGE DECIMAL(20,2),
ADD COLUMN 	SERVICE_CHARGE_FICO DECIMAL(20,2),
ADD COLUMN CORPORATE_MARKETING_PHOTO DECIMAL(20,2),
ADD COLUMN MARKETING_NPI DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN VARIANCE_PCC  DECIMAL(20,2),
ADD COLUMN 	VARIANCE_YC DECIMAL(20,2),
ADD COLUMN 	CONVEYANCE_MARKETING DECIMAL(20,2),
ADD COLUMN 	CONVEYANCE_OPERATION DECIMAL(20,2),
ADD COLUMN 	CONVEYANCE_OTHERS DECIMAL(20,2),
ADD COLUMN 	AUDIT_FEE DECIMAL(20,2),
ADD COLUMN 	AUDIT_FEE_OUT_OF_POCKET DECIMAL(20,2),
ADD COLUMN 	BAD_DEBTS_WRITTEN_OFF DECIMAL(20,2),
ADD COLUMN 	BROKERAGE DECIMAL(20,2),
ADD COLUMN 	CHARITY_AND_DONATIONS DECIMAL(20,2),
ADD COLUMN 	DOMESTIC_TICKETS_AND_HOTELS DECIMAL(20,2),
ADD COLUMN 	INTERNATIONAL_TICKETS_AND_HOTELS DECIMAL(20,2),
ADD COLUMN 	HOUSEKEEPING_CHARGES DECIMAL(20,2),
ADD COLUMN 	LATE_FEE_CHARGES DECIMAL(20,2),
ADD COLUMN 	MARKETING_DATA_ANALYSIS DECIMAL(20,2),
ADD COLUMN 	MISCELLANEOUS_EXPENSES DECIMAL(20,2),
ADD COLUMN 	PENALTY DECIMAL(20,2),
ADD COLUMN 	PHOTO_COPY_EXPENSES DECIMAL(20,2),
ADD COLUMN 	QCR_EXPENSE DECIMAL(20,2),
ADD COLUMN 	RECRUITMENT_CONSULTANTS DECIMAL(20,2),
ADD COLUMN 	ROC_FEES DECIMAL(20,2),
ADD COLUMN 	TRAVELLING_EXPENSE_ODC DECIMAL(20,2),
ADD COLUMN 	TRAVELLING_EXPENSE DECIMAL(20,2),
ADD COLUMN 	DEBIT_CREDIT_WRITTEN_OFF DECIMAL(20,2),
ADD COLUMN 	DIFFENECE_IN_EXCHANGE DECIMAL(20,2),
ADD COLUMN 	SERVICE_CHARGE_PAID DECIMAL(20,2),
ADD COLUMN 	INSURANCE_VEHICLE DECIMAL(20,2),
ADD COLUMN 	OTHERS_AMC DECIMAL(20,2),
ADD COLUMN 	OTHERS_MAINTENANCE DECIMAL(20,2),
ADD COLUMN 	RND_ENGINEERING_EXPENSE DECIMAL(20,2),
ADD COLUMN 	BYOD_CHARGES DECIMAL(20,2),
ADD COLUMN 	CAR_LEASE DECIMAL(20,2),
ADD COLUMN 	DRIVER_SALARY DECIMAL(20,2),
ADD COLUMN 	GRATUITY DECIMAL(20,2),
ADD COLUMN 	INSUARANCE_ACCIDENTAL DECIMAL(20,2),
ADD COLUMN 	INSUARANCE_MEDICAL DECIMAL(20,2),
ADD COLUMN 	SUPPORT_OPS_TURNOVER DECIMAL(20,2),
ADD COLUMN 	EMPLOYEE_FACILITATION_EXPENSES DECIMAL(20,2),
ADD COLUMN 	TELEPHONE_SR DECIMAL(20,2),
ADD COLUMN 	VEHICLE_RUNNING_AND_MAINT_SR DECIMAL(20,2),
ADD COLUMN 	EMPLOYEE_STOCK_OPTION_EXPENSE DECIMAL(20,2),
ADD COLUMN 	EMPLOYER_CONTRIBUTION_LWF DECIMAL(20,2),
ADD COLUMN 	ESIC_EMPLOYER_CONT DECIMAL(20,2),
ADD COLUMN 	LEAVE_TRAVEL_REIMBURSEMENT DECIMAL(20,2),
ADD COLUMN 	PF_ADMINISTRATION_CHARGES DECIMAL(20,2),
ADD COLUMN 	PF_EMPLOYER_CONT DECIMAL(20,2),
ADD COLUMN 	QUATERLY_INCENTIVE DECIMAL(20,2),
ADD COLUMN 	SUPPORT_AUDIT DECIMAL(20,2),
ADD COLUMN 	SUPPORT_CCC DECIMAL(20,2),
ADD COLUMN 	SUPPORT_IT DECIMAL(20,2),
ADD COLUMN 	SUPPORT_MAINTENANCE DECIMAL(20,2),
ADD COLUMN 	SUPPORT_COMM_WH DECIMAL(20,2),
ADD COLUMN 	CAPITAL_IMPORVEMENT_EXPENSES DECIMAL(20,2),
ADD COLUMN 	LEASE_HOLD_IMPROVEMENTS DECIMAL(20,2),
ADD COLUMN 	FIXED_ASSETS_EQUIPMENT DECIMAL(20,2),
ADD COLUMN 	FIXED_ASSET_FURNITURE DECIMAL(20,2),
ADD COLUMN 	FIXED_ASSETS_IT DECIMAL(20,2),
ADD COLUMN 	FIXED_ASSETS_KITCHEN_EQUIPMENT DECIMAL(20,2),
ADD COLUMN 	FIXED_ASSETS_OFFICE_EQUIPMENT DECIMAL(20,2),
ADD COLUMN 	MARKETINFG_LAUNCH DECIMAL(20,2),
ADD COLUMN 	PRE_OPENING_CONSUMABLE DECIMAL(20,2),
ADD COLUMN 	PRE_OPENING_OTHERS DECIMAL(20,2),
ADD COLUMN 	PRE_OPENING_RENT DECIMAL(20,2),
ADD COLUMN 	PRE_OPEINING_SALARY DECIMAL(20,2),
ADD COLUMN 	BANK_CHARGES DECIMAL(20,2),
ADD COLUMN 	INTREST_ON_LOAN DECIMAL(20,2),
ADD COLUMN 	INTREST_ON_TDS_OR_GST DECIMAL(20,2),
ADD COLUMN 	INTREST_ON_FDR DECIMAL(20,2),
ADD COLUMN 	PROFIT_SALE_MUTUTAL_FUND DECIMAL(20,2),
ADD COLUMN 	INTREST_INCOME_TAX_REFUND DECIMAL(20,2),
ADD COLUMN 	MISC_INCOME DECIMAL(20,2),
ADD COLUMN 	DISCOUNT_RECEIVED DECIMAL(20,2),
ADD COLUMN 	INTERIOR_DESIGNING_CHARGE DECIMAL(20,2),
ADD COLUMN 	SCRAPE DECIMAL(20,2),
ADD COLUMN 	SERVICE_CHARGE DECIMAL(20,2),
ADD COLUMN 	SERVICE_CHARGE_FICO DECIMAL(20,2),
ADD COLUMN CORPORATE_MARKETING_PHOTO DECIMAL(20,2),
ADD COLUMN MARKETING_NPI DECIMAL(20,2);


ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL ADD COLUMN ROUNDED_OFF DECIMAL(10,2) ,
ADD COLUMN SHORT_AND_EXCESS DECIMAL(10,2),
ADD COLUMN BUSINESS_PROMOTION_SR DECIMAL(10,2),
ADD COLUMN COGS_OTHERS DECIMAL(10,2),
ADD COLUMN INSURANCE_ASSETS DECIMAL(10,2),
ADD COLUMN INSURANCE_CGL DECIMAL(10,2),
ADD COLUMN INSURANCE_D_AND_O DECIMAL(10,2),
ADD COLUMN ODC_RENTAL DECIMAL(10,2),
ADD COLUMN MARKETING_LAUNCH DECIMAL(10,2),
ADD COLUMN RO_AMC DECIMAL(10,2),
ADD COLUMN SUPPORT_OPS_MANAGEMENT DECIMAL(10,2),
ADD COLUMN VARIANCE_ZERO DECIMAL(10,2);



ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN BUSINESS_PROMOTION_SR  DECIMAL(10,2),
ADD COLUMN SHORT_AND_EXCESS  DECIMAL(10,2),
ADD COLUMN ROUNDED_OFF  DECIMAL(10,2),
ADD COLUMN INSURANCE_CGL  DECIMAL(10,2),
ADD COLUMN INSURANCE_D_AND_O  DECIMAL(10,2),
ADD COLUMN ODC_RENTAL  DECIMAL(10,2),
ADD COLUMN FIXED_ASSETS_OTHERS_SUB_CATEGORY  DECIMAL(10,2),
ADD COLUMN INSURANCE_ASSETS  DECIMAL(10,2),
ADD COLUMN RO_AMC  DECIMAL(10,2),
ADD COLUMN SUPPORT_OPS_MANAGEMENT  DECIMAL(10,2),
ADD COLUMN VARIANCE_ZERO  DECIMAL(10,2),
ADD COLUMN VARIANCE_ZERO_TAX  DECIMAL(10,2);



ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
DROP COLUMN MEDICAL_INSURANCE, 
DROP COLUMN VEHICLE_MONTHLY_MAINTENANCE,
DROP COLUMN ADVERTISEMENT_BTL,
DROP COLUMN ENERGY_DG,
DROP COLUMN LOCAL_CONVEYENCE,
DROP COLUMN LEGAL_CHARGES_CAFE,
DROP COLUMN CENVAT_REVERSAL,
DROP COLUMN MAINTENANCE_SALARY,
DROP COLUMN INSURANCE_CHARGES,
DROP COLUMN SUPPORT_OPERATIONS,	
DROP COLUMN SUPPORT_HEAD_OFFICE,
DROP COLUMN TECHNOLOGY_MAIL,
DROP COLUMN CORPORATE_MARKETING_PNS,
DROP COLUMN MARKETING_LOCAL_STORE_VARIABLE,	
DROP COLUMN SUPPORT_VARIABLE,
DROP COLUMN MAINTENANCE_VARIABLE,	
DROP COLUMN CORPORATE_MARKETING_VARIABLE,	
DROP COLUMN FIXED_COST_VARIABLE,
DROP COLUMN	MANPOWER_VARIABLE,	
DROP COLUMN SUPPLY_CHAIN_VARIABLE,
DROP COLUMN ANY_OTHER_VARIABLES_1,	
DROP COLUMN ANY_OTHER_VARIABLES_2,	
DROP COLUMN ANY_OTHER_VARIABLES_3,
DROP COLUMN COMMISSION_OTHER;


ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN VEHICLE_MONTHLY_MAINTENANCE DECIMAL(20,6);
ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
DROP COLUMN WASTAGE_OTHER,
DROP COLUMN STOCK_VARIANCE;
ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
DROP COLUMN CONSUMABLE_OTHER,
DROP COLUMN EXPENSE_OTHER;
ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
DROP COLUMN TRAINING_COGS;
ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
DROP COLUMN MARKETINFG_LAUNCH;

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
DROP COLUMN CENVAT_REVERSAL_PERCENTAGE;

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
CHANGE COLUMN MARKETINFG_LAUNCH MARKETING_LAUNCH DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN FUEL_CHARGES_CAFE DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN FUEL_CHARGES_CAFE DECIMAL(20,2);


UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET ACCOUNTABLE_IN_PNL = 'N' WHERE (EXPENSE_METADATA_ID = '26');
UPDATE KETTLE_MASTER_DEV.EXPENSE_METADATA SET ACCOUNTABLE_IN_PNL = 'N', EXPENSE_STATUS = 'IN_ACTIVE' WHERE (EXPENSE_METADATA_ID = '10');

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN NET_REVENUE DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN COGS_OTHERS  DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN ENERGY_DG_RUNNING_CAFE  DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN ENERGY_DG_RUNNING_CAFE  DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_EXPENDITURE_DETAIL
ADD COLUMN WATER_CHARGES_CAFE  DECIMAL(20,2);

ALTER TABLE KETTLE_DEV.UNIT_BUDGETORY_DETAIL
ADD COLUMN WATER_CHARGES_CAFE  DECIMAL(20,2);
