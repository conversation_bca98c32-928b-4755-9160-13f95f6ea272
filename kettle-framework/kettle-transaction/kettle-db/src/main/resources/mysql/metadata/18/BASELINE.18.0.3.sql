UPDATE  KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM SET
BUDGET_CATEGORY='CORPORATE_MARKETING_AD_ONLINE'
WHERE BUDGET_CATEGORY='CORPORATE_MARKETING_ADV_ONLINE';

UPDATE  KETTLE_SCM_DEV.LIST_TYPE SET
BUDGET_CATEGORY='CORPORATE_MARKETING_AD_ONLINE'
WHERE BUDGET_CATEGORY='CORPORATE_MARKETING_ADV_ONLINE';



ALTER TABLE `KETTLE_DEV`.`UNIT_EXPENDITURE_DETAIL`
CHANGE COLUMN `CORPORATE_MARKETING_CHANNEL` `CORPORATE_MARKETING_CHANNEL_PARTNER` DECIMAL(20,6) ,
CHANGE COLUMN `EMPLOYEE_FACILITATION_EXPENSES` `EMPLOYEE_FACILITATION_CHARGES` DECIMAL(20,6) ,
<PERSON>AN<PERSON> COLUMN `CAPITAL_IMPORVEMENT_EXPENSES` `CAPITAL_IMPROVEMENT_EXPENSES` DECIMAL(20,6) ;


ALTER TABLE `KETTLE_DEV`.`UNIT_BUDGETORY_DETAIL`
CHANGE COLUMN `CORPORATE_MARKETING_CHANNEL` `CORPORATE_MARKETING_CHANNEL_PARTNER` DECIMAL(20,6) ,
CHANGE COLUMN `EMPLOYEE_FACILITATION_EXPENSES` `EMPLOYEE_FACILITATION_CHARGES` DECIMAL(20,6) ,
CHANGE COLUMN `CAPITAL_IMPORVEMENT_EXPENSES` `CAPITAL_IMPROVEMENT_EXPENSES` DECIMAL(20,6) ;


ALTER TABLE KETTLE_DEV.CRM_APP_SCREEN_DETAIL ADD COLUMN UNIT_ID INT(10) NOT NULL;
ALTER TABLE KETTLE_DEV.CRM_APP_SCREEN_DETAIL ADD COLUMN UNIT_NAME VARCHAR(255) NOT NULL;