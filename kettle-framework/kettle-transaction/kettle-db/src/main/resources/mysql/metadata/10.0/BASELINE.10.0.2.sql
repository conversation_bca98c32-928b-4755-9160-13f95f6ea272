
CREATE TABLE KETTLE_DEV.STANDALONE_TRANSACTION_DETAIL(
TRANSACTION_DETAIL_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
ACCOUNT_ID VARCHAR(200) NULL,
EVENT_TYPE VARCHAR(200) NULL,
PAYMENT_ID VARCHAR(200) NULL,
PAYMENT_AMOUNT DECIMAL(16,6) NULL,
PAYMENT_CURRENCY VARCHAR(20) NULL,
PAYMENT_STATUS VARCHAR(50) NULL,
PAYMENT_INVOICE_ID VARCHAR(200) NULL,
PAYMENT_INTERNATIONAL VARCHAR(1) NULL,
PAYMENT_METHOD VARCHAR(200) NULL,
PAYMENT_REFUND_AMOUNT DECIMAL(16,6) NULL,
PAYMENT_REFUND_STATUS VARCHAR(50) NULL,
PAYMENT_CAPTURED VARCHAR(1) NULL,
PAYMENT_DESCRIPTION VARCHAR(1000) NULL,
PAYMENT_CARD_ID VARCHAR(200) NULL,
PAYMENT_BANK_NAME VARCHAR(200) NULL,
PAYMENT_WALLET VARCHAR(200) NULL,
PAYMENT_VPA_HANDLE VARCHAR(200) NULL,
PAYMENT_EMAIL_ID VARCHAR(200) NULL,
PAYMENT_CONTACT_NUMBER VARCHAR(200) NULL,
PAYMENT_CUSTOMER_NAME VARCHAR(200) NULL,
PAYMENT_NOTES_EMAIL_ID VARCHAR(200) NULL,
PAYMENT_NOTES_CONTACT_NUMBER VARCHAR(200) NULL,
PAYMENT_TRANSACTION_FEES DECIMAL(16,6) NULL,
PAYMENT_TRANSACTION_FEES_TAX DECIMAL(16,6) NULL,
PAYMENT_ERROR_CODE VARCHAR(200) NULL,
PAYMENT_ERROR_DESCRIPTION VARCHAR(1000) NULL,
PAYMENT_TIME TIMESTAMP NULL,
ORDER_ID VARCHAR(200) NULL,
ORDER_AMOUNT DECIMAL(16,6) NULL,
ORDER_AMOUNT_PAID DECIMAL(16,6) NULL,
ORDER_AMOUNT_DUE DECIMAL(16,6) NULL,
ORDER_CURRENCY VARCHAR(200) NULL,
ORDER_RECEIPT VARCHAR(200) NULL,
ORDER_OFFER_ID VARCHAR(200) NULL,
ORDER_STATUS VARCHAR(200) NULL,
ORDER_ATTEMPTS INTEGER NULL,
ORDER_CREATION_TIME TIMESTAMP NULL,
REQUEST_TIME TIMESTAMP NULL,
LAST_UPDATE_TIME TIMESTAMP NULL,
CAMPAIGN_ID VARCHAR(200) NULL,
CAMPAIGN_DESCRIPTION VARCHAR(1000) NULL,
CURRENT_STATUS VARCHAR(50) NULL,
SMS_TYPE VARCHAR(200) NULL,
EMAIL_TYPE VARCHAR(200) NULL,
IS_NOTIFIED VARCHAR(1) NULL);


CREATE INDEX STANDALONE_TRANSACTION_DETAIL_PAYMENT_ID ON KETTLE_DEV.STANDALONE_TRANSACTION_DETAIL(PAYMENT_ID) USING BTREE;