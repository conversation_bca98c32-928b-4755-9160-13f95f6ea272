CREATE TABLE KETTLE_DEV.SUBSCRIPTION_PLAN
(
SUBSCRIPTION_PLAN_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
SUBSCRIPTION_PLAN_CODE VARCHAR(200) NOT NULL,
CUSTOMER_ID INTEGER NOT NULL,
PLAN_STATUS VARCHAR(15) NOT NULL,
PLAN_START_DATE DATE NOT NULL,
PLAN_END_DATE DATE NOT NULL,
RENEWAL_TIME TIMESTAMP NOT NULL,
EVENT_TYPE VARCHAR(50) NOT NULL,
LAST_RENEWAL_EVENT_ID INTEGER NOT NULL
);

CREATE INDEX SUBSCRIPTION_PLAN_CODE ON KETTLE_DEV.SUBSCRIPTION_PLAN(SUBSCRIPTION_PLAN_CODE) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_CUSTOMER_ID ON KETTLE_DEV.SUBSCRIPTION_PLAN(CUSTOMER_ID) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_PLAN_STATUS ON KETTLE_DEV.SUBSCRIPTION_PLAN(PLAN_STATUS) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_PLAN_START_DATE ON KETTLE_DEV.SUBSCRIPTION_PLAN(PLAN_START_DATE) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_PLAN_END_DATE ON KETTLE_DEV.SUBSCRIPTION_PLAN(PLAN_END_DATE) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_RENEWAL_TIME ON KETTLE_DEV.SUBSCRIPTION_PLAN(RENEWAL_TIME) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_TYPE ON KETTLE_DEV.SUBSCRIPTION_PLAN(EVENT_TYPE) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_LAST_RENEWAL_EVENT_ID ON KETTLE_DEV.SUBSCRIPTION_PLAN(LAST_RENEWAL_EVENT_ID) USING BTREE;


CREATE TABLE KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT
(
SUBSCRIPTION_PLAN_EVENT_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
SUBSCRIPTION_PLAN_ID INTEGER NOT NULL,
SUBSCRIPTION_PLAN_CODE VARCHAR(200) NOT NULL,
CUSTOMER_ID INTEGER NOT NULL,
PLAN_STATUS VARCHAR(15) NOT NULL,
PLAN_START_DATE DATE NOT NULL,
PLAN_END_DATE DATE NOT NULL,
RENEWAL_TIME TIMESTAMP NOT NULL,
EVENT_TYPE VARCHAR(50) NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
DIMENSION_CODE VARCHAR(100) NOT NULL,
ORDER_ID INTEGER NOT NULL,
ORDER_ITEM_ID INTEGER NOT NULL,
PRICE DECIMAL(16,6) NOT NULL,
VALIDITY_IN_DAYS INTEGER NOT NULL
);


CREATE INDEX SUBSCRIPTION_PLAN_EVENT_CODE ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(SUBSCRIPTION_PLAN_CODE) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_CUSTOMER_ID ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(CUSTOMER_ID) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_PLAN_STATUS ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(PLAN_STATUS) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_PLAN_START_DATE ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(PLAN_START_DATE) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_PLAN_END_DATE ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(PLAN_END_DATE) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_RENEWAL_TIME ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(RENEWAL_TIME) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_EVENT_TYPE ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(EVENT_TYPE) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_PRODUCT_ID ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(PRODUCT_ID) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_DIMENSION_CODE ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(DIMENSION_CODE) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_ORDER_ID ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(ORDER_ID) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_ORDER_ITEM_ID ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(ORDER_ITEM_ID) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_PRICE ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(PRICE) USING BTREE;
CREATE INDEX SUBSCRIPTION_PLAN_EVENT_VALIDITY_IN_DAYS ON KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT(VALIDITY_IN_DAYS) USING BTREE;

ALTER TABLE KETTLE_DEV.SUBSCRIPTION_PLAN ADD COLUMN OVERALL_SAVING DECIMAL(16, 2);
