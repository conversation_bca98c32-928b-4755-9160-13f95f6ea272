CREATE TABLE COMPLIMENTARY_CODE (
    COMP_ID INT NOT NULL AUTO_INCREMENT,
    COMP_CODE VARCHAR(50) NOT NULL,
    NAME VARCHAR(100) NOT NULL,
    DESCRIPTION VARCHAR(100) NULL,
    STATUS VARCHAR(10) NOT NULL,
    IS_ACCOUNTABLE VARCHAR(1) NOT NULL,
    PRIMARY KEY (COMP_ID)
);

INSERT INTO COMPLIMENTARY_CODE
(select RL_ID,RL_CODE,RL_NAME, 'Complimentary Codes' ,'ACTIVE','Y' FROM REF_LOOKUP WHERE RTL_ID = 21);