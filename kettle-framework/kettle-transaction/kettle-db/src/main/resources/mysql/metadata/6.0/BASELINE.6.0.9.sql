ALTER TABLE KETTLE_DEV.MONTHLY_TARGETS_DATA
ADD COLUMN TARGET_TYPE VARCHAR(20) NULL,
ADD COLUMN BUSINESS_DATE DATE NULL;

CREATE INDEX  MONTHLY_TARGETS_DATA_TARGET_TYPE ON KETTLE_DEV.MONTHLY_TARGETS_DATA(TARGET_TYPE) USING BTREE;
CREATE INDEX  MONTHLY_TARGETS_DATA_BUSINESS_DATE ON KETTLE_DEV.MONTHLY_TARGETS_DATA(BUSINESS_DATE) USING BTREE;

UPDATE KETTLE_DEV.MONTHLY_TARGETS_DATA SET TARGET_TYPE = 'MONTHLY';
UPDATE KETTLE_DEV.MONTHLY_TARGETS_DATA SET BUSINESS_DATE = STR_TO_DATE(CONCAT(TARGET_YEAR,'-',LPAD(TARGET_MONTH,2,'00'),'-',LPAD(1,2,'00')), '%Y-%m-%d');
