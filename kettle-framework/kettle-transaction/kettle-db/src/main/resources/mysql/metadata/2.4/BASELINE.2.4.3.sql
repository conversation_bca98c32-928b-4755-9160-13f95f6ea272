CREATE TABLE ORDER_EXTERNAL_SETTLEMENT_DATA (
    ORDER_EXTERNAL_SETTLEMENT_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    SETTLEMENT_ID INTEGER NOT NULL,
    AMOUNT_PAID DECIMAL(10 , 2 ) NOT NULL,
    <PERSON><PERSON><PERSON><PERSON>L_TRANSACTION_ID VARCHAR(25) NOT NULL
);

INSERT INTO KETTLE_DEV.ORDER_EXTERNAL_SETTLEMENT_DATA(SETTLEMENT_ID,AMOUNT_PAID,EXTERNAL_TRANSACTION_ID)
select SETTLEMENT_ID, AMOUNT_PAID, EXTERNAL_TRANSACTION_ID from KETTLE_DEV.ORDER_SETTLEMENT where EXTERNAL_TRANSACTION_ID is not null;

ALTER TABLE KETTLE_DEV.ORDER_SETTLEMENT
DROP COLUMN EXTERNAL_TRANSACTION_ID;
