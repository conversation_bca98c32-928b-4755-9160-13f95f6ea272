
ALTER TABLE PULL_DETAIL MODIFY COLUMN COMMENT VARCHAR(1000) DEFAULT NULL;
ALTER TABLE PULL_DETAIL DROP FOREIGN KEY PULL_DETAIL_ibfk_2;
ALTER TABLE PULL_DETAIL MODIFY COLUMN WITNESSED_BY VARCHAR(100) DEFAULT NULL;
UPDATE `PAYMENT_MODE` SET `GENERATE_PULL`='0' WHERE `MODE_TYPE`='CREDIT';
ALTER TABLE SETTLEMENT_DETAIL MODIFY COLUMN SETTLEMENT_SERVICE_PROVIDER VARCHAR(100) DEFAULT NULL;

ALTER TABLE MARKETING_PARTNER ADD COLUMN AUTHORIZATION_KEY VARCHAR(100) UNIQUE;


ALTER TABLE UNIT_DETAIL ADD COLUMN WORKSTATION_ENABLED VARCHAR(1) DEFAULT 'N';


CREATE INDEX index_contact_number ON CUSTOMER_INFO (CONTACT_NUMBER(10)) USING BTREE;

CREATE INDEX index_coupon_code ON COUPON_DETAIL_DATA (COUPON_CODE(10)) USING BTREE;
