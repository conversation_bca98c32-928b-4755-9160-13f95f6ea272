UPDATE ADDRESS_INFO SET `LATITUDE`='28.4594965', `LONGITUDE`='77.0266383' WHERE `ADDRESS_ID`='100';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.4983558', `LONGITUDE`='77.0901711' WHERE `ADDRESS_ID`='101';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.6197948', `LONGITUDE`='77.3611549' WHERE `ADDRESS_ID`='102';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.5093337', `LONGITUDE`='77.0720061' WHERE `ADDRESS_ID`='103';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.4679758', `LONGITUDE`='77.0841503' WHERE `ADDRESS_ID`='105';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.5464886', `LONGITUDE`='77.1963412' WHERE `ADDRESS_ID`='106';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.673751', `LONGITUDE`='77.1273376' WHERE `ADDRESS_ID`='107';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.5539547', `LONGITUDE`='77.1943451' WHERE `ADDRESS_ID`='108';
UPDATE ADDRESS_INFO SET `LATITUDE`='19.0544', `LONGITUDE`='72.8406' WHERE `ADDRESS_ID`='109';
UPDATE ADDRESS_INFO SET `LATITUDE`='19.1', `LONGITUDE`='72.83' WHERE `ADDRESS_ID`='110';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.4472372', `LONGITUDE`='77.0406147' WHERE `ADDRESS_ID`='111';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.5667', `LONGITUDE`='77.2667' WHERE `ADDRESS_ID`='112';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.6959', `LONGITUDE`='77.1524' WHERE `ADDRESS_ID`='113';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.4621359', `LONGITUDE`='77.0463719' WHERE `ADDRESS_ID`='114';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.4961927', `LONGITUDE`='77.10817' WHERE `ADDRESS_ID`='1201';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.4339533', `LONGITUDE`='76.9995868' WHERE `ADDRESS_ID`='1202';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.4452895', `LONGITUDE`='77.0650809' WHERE `ADDRESS_ID`='1203';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.479568', `LONGITUDE`='77.080062' WHERE `ADDRESS_ID`='1204';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.585727', `LONGITUDE`='77.330292' WHERE `ADDRESS_ID`='1205';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.5667', `LONGITUDE`='77.2833' WHERE `ADDRESS_ID`='1206';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.5398632', `LONGITUDE`='77.2822537' WHERE `ADDRESS_ID`='1207';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.6444', `LONGITUDE`='77.1998' WHERE `ADDRESS_ID`='1208';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.5789', `LONGITUDE`='77.2439' WHERE `ADDRESS_ID`='1209';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.493644', `LONGITUDE`='77.141147' WHERE `ADDRESS_ID`='12030';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.657081', `LONGITUDE`='77.146775' WHERE `ADDRESS_ID`='12031';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.56753', `LONGITUDE`='77.326026' WHERE `ADDRESS_ID`='12032';
UPDATE ADDRESS_INFO SET `LATITUDE`='28.629726', `LONGITUDE`='77.220136' WHERE `ADDRESS_ID`='12037';


INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 10009, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where  PRODUCT_ID IN (
681,
682,
683,
684
) and UNIT_ID = 10000 ;

INSERT INTO UNIT_PRODUCT_MAPPING(UNIT_ID, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE)
select 10010, PRODUCT_ID, PRODUCT_STATUS, LAST_UPDATE_TMSTMP, PRODUCT_START_DATE, PRODUCT_END_DATE from UNIT_PRODUCT_MAPPING where  PRODUCT_ID IN (
681,
682,
683,
684
) and UNIT_ID = 10000 ;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select upm2.UNIT_PROD_REF_ID, upp.DIMENSION_CODE, upp.LAST_UPDATE_TMSTMP,upp.PRICE from UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_MAPPING upm2
where upp.UNIT_PROD_REF_ID = upm1.UNIT_PROD_REF_ID
and upm2.UNIT_ID = 10009 and 
upm1.PRODUCT_ID = upm2.PRODUCT_ID and upm1.PRODUCT_ID IN (
681,
682,
683,
684
) and upm1.UNIT_ID = 10000

;

INSERT INTO UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID, DIMENSION_CODE, LAST_UPDATE_TMSTMP, PRICE)
select upm2.UNIT_PROD_REF_ID, upp.DIMENSION_CODE, upp.LAST_UPDATE_TMSTMP,upp.PRICE from UNIT_PRODUCT_PRICING upp, UNIT_PRODUCT_MAPPING upm1, UNIT_PRODUCT_MAPPING upm2
where upp.UNIT_PROD_REF_ID = upm1.UNIT_PROD_REF_ID
and upm2.UNIT_ID = 10010 and 
upm1.PRODUCT_ID = upm2.PRODUCT_ID and upm1.PRODUCT_ID IN (
681,
682,
683,
684
) and upm1.UNIT_ID = 10000;

