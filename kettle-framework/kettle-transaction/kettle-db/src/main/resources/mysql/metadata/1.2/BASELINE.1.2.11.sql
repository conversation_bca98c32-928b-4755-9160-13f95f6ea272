ALTER TABLE PRODUCT_DETAIL
ADD COLUMN IS_INVENTORY_TRACKED VARCHAR(1) NOT NULL DEFAULT 'N';

DROP TABLE IF EXISTS UNIT_PRODUCT_INVENTORY;

CREATE TABLE UNIT_PRODUCT_INVENTORY (
    UNIT_PRODUCT_INVENTORY_ID INTEGER NOT NULL AUTO_INCREMENT,
    UNIT_ID INTEGER NOT NULL,
    PRODUCT_ID INTEGER NOT NULL,
    LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL,
    NO_OF_UNIT INTEGER NOT NULL,
    LAST_STOCK_OUT_TIME TIMESTAMP NULL,
    PRIMARY KEY(UNIT_PRODUCT_INVENTORY_ID)
);

ALTER TABLE ORDER_DETAIL
ADD COLUMN ORDER_SOURCE_ID VARCHAR(80) NULL;


ALTER TABLE LOYALTY_EVENTS
ADD COLUMN ORDER_ID INTEGER NULL;

ALTER TABLE ORDER_DETAIL MODIFY COLUMN ORDER_STATUS VARCHAR(30);

ALTER TABLE CUSTOMER_ADDRESS_INFO
MODIFY COLUMN ZIPCODE VARCHAR(40) NULL;

DROP TABLE IF EXISTS ADDON_PRODUCT_DATA;

CREATE TABLE ADDON_PRODUCT_DATA(
ADDON_PRODUCT_DATA_ID INTEGER NOT NULL AUTO_INCREMENT,
ADDON_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
PRIMARY KEY(ADDON_PRODUCT_DATA_ID)
);
alter table ADDON_PRODUCT_DATA ADD UNIQUE(ADDON_ID, PRODUCT_ID);

