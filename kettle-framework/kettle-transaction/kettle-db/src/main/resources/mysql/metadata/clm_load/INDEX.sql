CREATE INDEX CUSTOMER_DATA_NEW_TOTAL_UNITS_VISITED ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TOTAL_UNITS_VISITED) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_FIRST_ORDER_DATE ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(FIRST_ORDER_DATE) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_LAST_ORDER_DATE ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(LAST_ORDER_DATE) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_COUNT ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_COUNT) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_CANCELLED_TICKET_COUNT ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(CANCELLED_TICKET_COUNT) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_WITH_OFFER ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_WITH_OFFER) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_WITH_REDEMPTION ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_WITH_REDEMPTION) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DINE_IN_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DINE_IN_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DELIVERY_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DELIVERY_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TAKE_AWAY_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TAKE_AWAY_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_ZOMATO_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(ZOMATO_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_SWIGGY_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(SWIGGY_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_FOOD_PANDA_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(FOOD_PANDA_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_UBER_EATS_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(UBER_EATS_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_OLD_APP_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(OLD_APP_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_WEB_APP_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(WEB_APP_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_CALL_CENTER_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(CALL_CENTER_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_OTHER_PARTNER_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(OTHER_PARTNER_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_ON_WEEKDAY ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_ON_WEEKDAY) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_ON_WEEKEND ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_ON_WEEKEND) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_IN_BREAKFAST ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_IN_BREAKFAST) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_IN_LUNCH ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_IN_LUNCH) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_IN_EVENING ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_IN_EVENING) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_IN_DINNER ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_IN_DINNER) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_IN_POST_DINNER ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_IN_POST_DINNER) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_IN_NIGHT ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_IN_NIGHT) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_LAST_NPS_SCORE ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(LAST_NPS_SCORE) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_NEGATIVE_NPS_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(NEGATIVE_NPS_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_POSITIVE_NPS_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(POSITIVE_NPS_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_NEUTRAL_NPS_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(NEUTRAL_NPS_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TOTAL_SPEND ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TOTAL_SPEND) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TOTAL_DISCOUNT ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TOTAL_DISCOUNT) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_MINIMUM_APC ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(MINIMUM_APC) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_MAXIMUM_APC ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(MAXIMUM_APC) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DELIVERY_SPEND ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DELIVERY_SPEND) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DELIVERY_DISCOUNT ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DELIVERY_DISCOUNT) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DELIVERY_MINIMUM_APC ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DELIVERY_MINIMUM_APC) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DELIVERY_MAXIMUM_APC ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DELIVERY_MAXIMUM_APC) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DINE_IN_SPEND ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DINE_IN_SPEND) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DINE_IN_DISCOUNT ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DINE_IN_DISCOUNT) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DINE_IN_MINIMUM_APC ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DINE_IN_MINIMUM_APC) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DINE_IN_MAXIMUM_APC ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DINE_IN_MAXIMUM_APC) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_ONLY_GIFT_CARD_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(ONLY_GIFT_CARD_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_GIFT_CARD_SPEND ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(GIFT_CARD_SPEND) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TICKET_WITH_GIFT_CARD ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TICKET_WITH_GIFT_CARD) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_PEOPLE_PER_TICKET ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(PEOPLE_PER_TICKET) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_MINIMUM_PEOPLE_PER_ORDER ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(MINIMUM_PEOPLE_PER_ORDER) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_MAXIMUM_PEOPLE_PER_ORDER ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(MAXIMUM_PEOPLE_PER_ORDER) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_LAST_FEEDBACK_SCORE ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(LAST_FEEDBACK_SCORE) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DAY_GAP_SINCE_LAST_ORDER ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DAY_GAP_SINCE_LAST_ORDER) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_TOTAL_APC ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(TOTAL_APC) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DELIVERY_APC ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DELIVERY_APC) USING BTREE;
CREATE INDEX CUSTOMER_DATA_NEW_DINE_IN_APC ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(DINE_IN_APC) USING BTREE;


CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TOTAL_UNITS_VISITED ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TOTAL_UNITS_VISITED) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_FIRST_ORDER_DATE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(FIRST_ORDER_DATE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_LAST_ORDER_DATE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(LAST_ORDER_DATE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_COUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_COUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_CANCELLED_TICKET_COUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(CANCELLED_TICKET_COUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_WITH_OFFER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_WITH_OFFER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_WITH_REDEMPTION ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_WITH_REDEMPTION) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DINE_IN_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DINE_IN_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DELIVERY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DELIVERY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TAKE_AWAY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TAKE_AWAY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_ZOMATO_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(ZOMATO_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_SWIGGY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(SWIGGY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_FOOD_PANDA_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(FOOD_PANDA_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_UBER_EATS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(UBER_EATS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_OLD_APP_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(OLD_APP_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_WEB_APP_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(WEB_APP_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_CALL_CENTER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(CALL_CENTER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_OTHER_PARTNER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(OTHER_PARTNER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_ON_WEEKDAY ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_ON_WEEKDAY) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_ON_WEEKEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_ON_WEEKEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_IN_BREAKFAST ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_IN_BREAKFAST) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_IN_LUNCH ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_IN_LUNCH) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_IN_EVENING ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_IN_EVENING) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_IN_DINNER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_IN_DINNER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_IN_POST_DINNER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_IN_POST_DINNER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_IN_NIGHT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_IN_NIGHT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_LAST_NPS_SCORE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(LAST_NPS_SCORE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_NEGATIVE_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(NEGATIVE_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_POSITIVE_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(POSITIVE_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_NEUTRAL_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(NEUTRAL_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TOTAL_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TOTAL_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TOTAL_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TOTAL_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DELIVERY_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DELIVERY_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DELIVERY_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DELIVERY_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DELIVERY_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DELIVERY_MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DELIVERY_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DELIVERY_MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DINE_IN_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DINE_IN_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DINE_IN_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DINE_IN_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DINE_IN_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DINE_IN_MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DINE_IN_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DINE_IN_MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_ONLY_GIFT_CARD_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(ONLY_GIFT_CARD_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_GIFT_CARD_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(GIFT_CARD_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TICKET_WITH_GIFT_CARD ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TICKET_WITH_GIFT_CARD) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_PEOPLE_PER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(PEOPLE_PER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_MINIMUM_PEOPLE_PER_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(MINIMUM_PEOPLE_PER_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_MAXIMUM_PEOPLE_PER_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(MAXIMUM_PEOPLE_PER_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_LAST_FEEDBACK_SCORE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(LAST_FEEDBACK_SCORE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DAY_GAP_SINCE_LAST_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DAY_GAP_SINCE_LAST_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_TOTAL_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(TOTAL_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DELIVERY_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DELIVERY_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_DINE_IN_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(DINE_IN_APC) USING BTREE;


CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TOTAL_UNITS_VISITED ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TOTAL_UNITS_VISITED) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_FIRST_ORDER_DATE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(FIRST_ORDER_DATE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_LAST_ORDER_DATE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(LAST_ORDER_DATE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_COUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_COUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_CANCELLED_TICKET_COUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(CANCELLED_TICKET_COUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_WITH_OFFER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_WITH_OFFER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_WITH_REDEMPTION ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_WITH_REDEMPTION) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DINE_IN_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DINE_IN_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DELIVERY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DELIVERY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TAKE_AWAY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TAKE_AWAY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_ZOMATO_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(ZOMATO_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_SWIGGY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(SWIGGY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_FOOD_PANDA_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(FOOD_PANDA_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_UBER_EATS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(UBER_EATS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_OLD_APP_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(OLD_APP_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_WEB_APP_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(WEB_APP_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_CALL_CENTER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(CALL_CENTER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_OTHER_PARTNER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(OTHER_PARTNER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_ON_WEEKDAY ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_ON_WEEKDAY) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_ON_WEEKEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_ON_WEEKEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_IN_BREAKFAST ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_IN_BREAKFAST) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_IN_LUNCH ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_IN_LUNCH) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_IN_EVENING ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_IN_EVENING) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_IN_DINNER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_IN_DINNER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_IN_POST_DINNER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_IN_POST_DINNER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_IN_NIGHT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_IN_NIGHT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_LAST_NPS_SCORE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(LAST_NPS_SCORE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_NEGATIVE_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(NEGATIVE_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_POSITIVE_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(POSITIVE_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_NEUTRAL_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(NEUTRAL_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TOTAL_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TOTAL_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TOTAL_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TOTAL_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DELIVERY_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DELIVERY_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DELIVERY_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DELIVERY_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DELIVERY_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DELIVERY_MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DELIVERY_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DELIVERY_MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DINE_IN_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DINE_IN_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DINE_IN_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DINE_IN_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DINE_IN_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DINE_IN_MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DINE_IN_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DINE_IN_MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_ONLY_GIFT_CARD_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(ONLY_GIFT_CARD_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_GIFT_CARD_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(GIFT_CARD_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TICKET_WITH_GIFT_CARD ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TICKET_WITH_GIFT_CARD) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_PEOPLE_PER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(PEOPLE_PER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_MINIMUM_PEOPLE_PER_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(MINIMUM_PEOPLE_PER_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_MAXIMUM_PEOPLE_PER_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(MAXIMUM_PEOPLE_PER_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_LAST_FEEDBACK_SCORE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(LAST_FEEDBACK_SCORE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DAY_GAP_SINCE_LAST_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DAY_GAP_SINCE_LAST_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_TOTAL_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(TOTAL_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DELIVERY_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DELIVERY_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_DINE_IN_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(DINE_IN_APC) USING BTREE;


CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TOTAL_UNITS_VISITED ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TOTAL_UNITS_VISITED) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_FIRST_ORDER_DATE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(FIRST_ORDER_DATE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_LAST_ORDER_DATE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(LAST_ORDER_DATE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_COUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_COUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_CANCELLED_TICKET_COUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(CANCELLED_TICKET_COUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_WITH_OFFER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_WITH_OFFER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_WITH_REDEMPTION ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_WITH_REDEMPTION) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DINE_IN_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DINE_IN_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DELIVERY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DELIVERY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TAKE_AWAY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TAKE_AWAY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_ZOMATO_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(ZOMATO_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_SWIGGY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(SWIGGY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_FOOD_PANDA_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(FOOD_PANDA_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_UBER_EATS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(UBER_EATS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_OLD_APP_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(OLD_APP_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_WEB_APP_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(WEB_APP_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_CALL_CENTER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(CALL_CENTER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_OTHER_PARTNER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(OTHER_PARTNER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_ON_WEEKDAY ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_ON_WEEKDAY) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_ON_WEEKEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_ON_WEEKEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_IN_BREAKFAST ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_IN_BREAKFAST) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_IN_LUNCH ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_IN_LUNCH) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_IN_EVENING ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_IN_EVENING) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_IN_DINNER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_IN_DINNER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_IN_POST_DINNER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_IN_POST_DINNER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_IN_NIGHT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_IN_NIGHT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_LAST_NPS_SCORE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(LAST_NPS_SCORE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_NEGATIVE_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(NEGATIVE_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_POSITIVE_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(POSITIVE_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_NEUTRAL_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(NEUTRAL_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TOTAL_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TOTAL_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TOTAL_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TOTAL_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DELIVERY_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DELIVERY_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DELIVERY_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DELIVERY_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DELIVERY_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DELIVERY_MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DELIVERY_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DELIVERY_MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DINE_IN_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DINE_IN_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DINE_IN_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DINE_IN_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DINE_IN_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DINE_IN_MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DINE_IN_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DINE_IN_MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_ONLY_GIFT_CARD_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(ONLY_GIFT_CARD_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_GIFT_CARD_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(GIFT_CARD_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TICKET_WITH_GIFT_CARD ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TICKET_WITH_GIFT_CARD) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_PEOPLE_PER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(PEOPLE_PER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_MINIMUM_PEOPLE_PER_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(MINIMUM_PEOPLE_PER_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_MAXIMUM_PEOPLE_PER_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(MAXIMUM_PEOPLE_PER_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_LAST_FEEDBACK_SCORE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(LAST_FEEDBACK_SCORE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DAY_GAP_SINCE_LAST_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DAY_GAP_SINCE_LAST_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_TOTAL_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(TOTAL_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DELIVERY_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DELIVERY_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_DINE_IN_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(DINE_IN_APC) USING BTREE;

CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TOTAL_UNITS_VISITED ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TOTAL_UNITS_VISITED) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_FIRST_ORDER_DATE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(FIRST_ORDER_DATE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_LAST_ORDER_DATE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(LAST_ORDER_DATE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_COUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_COUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_CANCELLED_TICKET_COUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(CANCELLED_TICKET_COUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_WITH_OFFER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_WITH_OFFER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_WITH_REDEMPTION ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_WITH_REDEMPTION) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DINE_IN_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DINE_IN_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DELIVERY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DELIVERY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TAKE_AWAY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TAKE_AWAY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_ZOMATO_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(ZOMATO_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_SWIGGY_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(SWIGGY_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_FOOD_PANDA_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(FOOD_PANDA_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_UBER_EATS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(UBER_EATS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_OLD_APP_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(OLD_APP_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_WEB_APP_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(WEB_APP_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_CALL_CENTER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(CALL_CENTER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_OTHER_PARTNER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(OTHER_PARTNER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_ON_WEEKDAY ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_ON_WEEKDAY) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_ON_WEEKEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_ON_WEEKEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_IN_BREAKFAST ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_IN_BREAKFAST) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_IN_LUNCH ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_IN_LUNCH) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_IN_EVENING ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_IN_EVENING) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_IN_DINNER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_IN_DINNER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_IN_POST_DINNER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_IN_POST_DINNER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_IN_NIGHT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_IN_NIGHT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_LAST_NPS_SCORE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(LAST_NPS_SCORE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_NEGATIVE_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(NEGATIVE_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_POSITIVE_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(POSITIVE_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_NEUTRAL_NPS_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(NEUTRAL_NPS_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TOTAL_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TOTAL_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TOTAL_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TOTAL_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DELIVERY_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DELIVERY_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DELIVERY_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DELIVERY_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DELIVERY_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DELIVERY_MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DELIVERY_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DELIVERY_MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DINE_IN_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DINE_IN_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DINE_IN_DISCOUNT ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DINE_IN_DISCOUNT) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DINE_IN_MINIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DINE_IN_MINIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DINE_IN_MAXIMUM_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DINE_IN_MAXIMUM_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_ONLY_GIFT_CARD_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(ONLY_GIFT_CARD_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_GIFT_CARD_SPEND ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(GIFT_CARD_SPEND) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TICKET_WITH_GIFT_CARD ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TICKET_WITH_GIFT_CARD) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_PEOPLE_PER_TICKET ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(PEOPLE_PER_TICKET) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_MINIMUM_PEOPLE_PER_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(MINIMUM_PEOPLE_PER_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_MAXIMUM_PEOPLE_PER_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(MAXIMUM_PEOPLE_PER_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_LAST_FEEDBACK_SCORE ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(LAST_FEEDBACK_SCORE) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DAY_GAP_SINCE_LAST_ORDER ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DAY_GAP_SINCE_LAST_ORDER) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_TOTAL_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(TOTAL_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DELIVERY_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DELIVERY_APC) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_DINE_IN_APC ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(DINE_IN_APC) USING BTREE;


CREATE INDEX CUSTOMER_DATA_NEW_DAYS_UNIQUE_VISIT_DAYS ON CLM_ANALYTICS.CUSTOMER_DATA_NEW(UNIQUE_VISIT_DAYS) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_90_DAYS_UNIQUE_VISIT_DAYS ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_90_DAYS(UNIQUE_VISIT_DAYS) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_30_DAYS_UNIQUE_VISIT_DAYS ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_30_DAYS(UNIQUE_VISIT_DAYS) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_15_DAYS_UNIQUE_VISIT_DAYS ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_15_DAYS(UNIQUE_VISIT_DAYS) USING BTREE;
CREATE INDEX ACTIVE_CUSTOMER_DATA_7_DAYS_UNIQUE_VISIT_DAYS ON CLM_ANALYTICS.ACTIVE_CUSTOMER_DATA_7_DAYS(UNIQUE_VISIT_DAYS) USING BTREE;
