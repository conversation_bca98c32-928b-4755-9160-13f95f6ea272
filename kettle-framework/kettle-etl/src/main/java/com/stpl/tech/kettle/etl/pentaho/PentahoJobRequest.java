package com.stpl.tech.kettle.etl.pentaho;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Map;

import org.apache.commons.lang.RandomStringUtils;
import org.pentaho.di.core.Result;
import org.pentaho.di.core.logging.LogLevel;
import org.pentaho.di.job.Job;
import org.pentaho.di.job.JobMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.etl.core.JobRequest;

public class PentahoJobRequest implements JobRequest<Job> {

	private static final Logger LOG = LoggerFactory.getLogger(PentahoJobRequest.class);
	private Job job;
	private String jobName;

	public PentahoJobRequest(String jobName, String resourceFileName, Map<String, String> params) {
		this.jobName = jobName;
		this.job = loadJob(jobName, resourceFileName, params);
	}

	public PentahoJobRequest(String jobName, FileInputStream fileStream, Map<String, String> params) {
		this.jobName = jobName;
		this.job = loadJob(jobName, fileStream, params);
	}

	@Override
	public Job getJob() {
		return job;
	}

	@Override
	public void setJob(Job job) {
		this.job = job;
	}

	public Job loadJob(String jobName, String fileName, Map<String, String> params) {
		LOG.info("***************************************************************************************");
		LOG.info("Attempting to run job " + jobName + " from file system with file name " + fileName);
		LOG.info("***************************************************************************************\n");
		return loadJob(jobName, getClass().getClassLoader().getResourceAsStream(fileName), params);
	}

	public Job loadJob(String jobName, InputStream inputStream, Map<String, String> params) {

		try {

			// Loading the job file from file system into the JobMeta object.
			// The JobMeta object is the programmatic representation of a job
			// definition.
			JobMeta jobMeta = new JobMeta(inputStream, null, null);

			// The next section reports on the declared parameters and sets them
			// to arbitrary values
			// for demonstration purposes
			LOG.info("Attempting to read and set named parameters");
			String[] declaredParameters = jobMeta.listParameters();
			for (int i = 0; i < declaredParameters.length; i++) {
				LOG.info(declaredParameters[i]);
				String parameterName = declaredParameters[i];

				// determine the parameter description and default values for
				// display purposes
				String description = jobMeta.getParameterDescription(parameterName);
				String defaultValue = jobMeta.getParameterDefault(parameterName);
				// set the parameter value to an arbitrary string
				String parameterValue = RandomStringUtils.randomAlphanumeric(10);

				String output = String.format("Setting parameter %s to \"%s\" [description: \"%s\", default: \"%s\"]",
						parameterName, parameterValue, description, defaultValue);
				System.out.println(output);

				// assign the value to the parameter on the job
				jobMeta.setParameterValue(parameterName, parameterValue);
			}

			// Creating a Job object which is the programmatic representation of
			// a job
			// A Job object can be executed, report success, etc.
			Job job = new Job(null, jobMeta);

			// adjust the log level
			job.setLogLevel(LogLevel.MINIMAL);

			return job;
		} catch (Exception e) {
			// something went wrong, just log and return
			e.printStackTrace();
			return null;
		}
	}

	@Override
	public String getJobName() {
		return jobName;
	}

}
