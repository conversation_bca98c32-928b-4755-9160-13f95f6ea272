package com.stpl.tech.kettle.etl.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.jfree.util.Log;
import org.pentaho.di.core.KettleEnvironment;
import org.pentaho.di.core.Result;
import org.pentaho.di.core.exception.KettleXMLException;
import org.pentaho.di.core.parameters.UnknownParamException;
import org.pentaho.di.job.Job;
import org.pentaho.di.job.JobMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.etl.core.JobRequest;
import com.stpl.tech.kettle.etl.core.JobResponse;
import com.stpl.tech.kettle.etl.core.JobStatus;
import com.stpl.tech.kettle.etl.pentaho.PentahoJobResponse;
import com.stpl.tech.kettle.etl.service.JobExecutor;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Service
public class JobExecutorImpl implements JobExecutor<Job, Result> {

	private static final Logger LOG = LoggerFactory.getLogger(JobExecutorImpl.class);

	private static final String SEPARATOR = "/";

	@Override
	public JobResponse<Result> executeJobRequest(JobRequest<Job> request) {
		Job job = request.getJob();

		LOG.info("\nStarting job : " + job.getName());

		// starting the job thread, which will execute asynchronously
		job.start();

		// waiting for the job to finish
		job.waitUntilFinished();

		// retrieve the result object, which captures the success of the job
		Result result = job.getResult();

		JobResponse<Result> response = new PentahoJobResponse(result);
		// report on the outcome of the job
		LOG.info(result + "");
		String outcome = String.format("\nJob %s executed with result: %s and %d errors\n", job.getName(),
				result.getResult(), result.getNrErrors());
		LOG.info(outcome);
		if (result.getResult()) {
			response.setStatus(JobStatus.SUCCESS);
		} else {
			response.setStatus(JobStatus.FAILED);
		}
		return response;
	}
	@Override
	public JobResponse<Result> executeJob(String basePath, String fileName, Map<String, String> params)
			throws UnknownParamException, KettleXMLException {
//		try {
//			KettleEnvironment.init();
			LOG.info("Params" + params);
			//InputStream inputStream = getClass().getClassLoader().getResourceAsStream(fileName);
			String file = basePath + SEPARATOR + fileName;
			JobMeta jobmeta=new JobMeta(file, null);
			
			setParameter(jobmeta, params);

			Job job=new Job(null, jobmeta);
			job.start();
			job.waitUntilFinished();
            Result result = job.getResult();
            JobResponse<Result> response = new PentahoJobResponse(result);
            if (result.getResult()) {
                response.setStatus(JobStatus.SUCCESS);
            } else {
                response.setStatus(JobStatus.FAILED);
            }

			if(job.getErrors()>0){
				Log.info("Error in running Job");
			} else {
				Log.info(job.getResult() + " " + job.getName() + " " + result.getResult() + " " + result.getNrErrors() ) ;
			}

            return response;

//        } catch (KettleException e) {
//            LOG.info("Error in running Job", e.getMessage(), e.getSuperMessage());
//			e.printStackTrace();
//		}
//        return null;
	}

	public static void main(String[] args) throws Exception{
//		jobMethod();
//		JobExecutor<Job, Result> pentahoExecutor = new JobExecutorImpl();
//		JobRequest<Job> request = new PentahoJobRequest("Test 1", "jobs/tally/empty_job.kjb", new HashMap<String, String>());
//		JobResponse<Result> result = pentahoExecutor.executeJobRequest(request);
//		System.out.println("Results "+result.getResult().getResult());
		KettleEnvironment.init();
        JobExecutor<Job, Result> pentahoExecutor = new JobExecutorImpl();
        Map<String, String> params = new HashMap<>();
        params.put("BUSINESS_DATE","2020-10-01");
        params.put("HOST_NAME","dump.kettle.chaayos.com");
        params.put("JOB_EXECUTION_ID","101");
        params.put("JOB_EXECUTION_TIME","2019-03-30 15:00:01");
        params.put("JOB_ID","1");
        params.put("JOB_STEP_ID","4");
        params.put("PASSWORD","321In#@!");
        params.put("SCHEMA_SUFFIX","_DUMP");
        params.put("TYPE_OF_DATA","TAX_DATA");
        params.put("USERNAME","rptusr");
		params.put("LOCAL_DIRECTORY", "D:\\");
        pentahoExecutor
                .executeJob("",
                        "D:\\office\\chaayos\\kettle-framework\\kettle-etl\\src\\main\\resources\\jobs\\tally\\sales_data_job.kjb", params);
	}

	public  void setParameter(JobMeta jobMeta, Map<String, String> params) throws UnknownParamException {
		Log.info("Attempting to read and set named parameters");
		String[] declaredParameters = jobMeta.listParameters();
		for (int i = 0; i < declaredParameters.length; i++) {
			System.out.println(declaredParameters[i]);
			String parameterName = declaredParameters[i];

			// determine the parameter description and default values for
			// display purposes
			String description = jobMeta.getParameterDescription(parameterName);
			String defaultValue = jobMeta.getParameterDefault(parameterName);
			// set the parameter value to an arbitrary string
			String parameterValue = params.get(parameterName);

			String output = String.format("Setting parameter %s to \"%s\" [description: \"%s\", default: \"%s\"]",
					parameterName, parameterValue, description, defaultValue);
			Log.info(output);
			System.out.println(output);
			// assign the value to the parameter on the job

			jobMeta.setParameterValue(parameterName, parameterValue);
		}
	}

}
