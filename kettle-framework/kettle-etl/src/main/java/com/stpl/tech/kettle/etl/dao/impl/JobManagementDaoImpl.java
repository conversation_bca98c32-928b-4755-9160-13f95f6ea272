package com.stpl.tech.kettle.etl.dao.impl;

import com.stpl.tech.kettle.etl.core.AggregatedResultStatus;
import com.stpl.tech.kettle.etl.core.JobExecutionDefinitionStatus;
import com.stpl.tech.kettle.etl.core.data.AggregatedResultDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobParamsDefinitionMappingData;
import com.stpl.tech.kettle.etl.dao.JobManagementDao;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.Date;
import java.util.List;

@Repository
public class JobManagementDaoImpl extends AbstractDaoImpl implements JobManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(JobManagementDaoImpl.class);

    @Override
    public List<JobParamsDefinitionMappingData> getJobParamsDefinitionMapping(int jobId, String status) {
        Query query = manager.createQuery("FROM JobParamsDefinitionMappingData j WHERE j.jobDefinitionData.jobId = :jobId and j.status = :status");
        query.setParameter("jobId", jobId);
        query.setParameter("status", status);
        return query.getResultList();
    }

    @Override
    public List<JobExecutionDefinitionData> getJobExecutionDefinitionDataListForJobIdAndBusinessDate(int jobId, Date businessDate) {
        Query query = manager.createQuery("FROM JobExecutionDefinitionData j " +
                "WHERE j.jobDefinitionData.jobId = :jobId and DATE(j.businessDate) = DATE(:businessDate)");
        query.setParameter("jobId", jobId);
        query.setParameter("businessDate", businessDate);
        return query.getResultList();
    }

    @Override
    public boolean updateRecordStatusToInactiveForJobExecutionId(int executionId) {
        String status = AggregatedResultStatus.IN_ACTIVE.name();
        Query query = manager.createQuery("UPDATE AggregatedResultDefinitionData a SET a.status = :status " +
                "WHERE a.executionId = :executionId");
        query.setParameter("status", status);
        query.setParameter("executionId", executionId);
        int count = query.executeUpdate();
        return count > 0;
    }

    @Override
    public List<AggregatedResultDefinitionData> getTallyReport(String businessDate, String dataType) {
        Query query=manager.createQuery("FROM AggregatedResultDefinitionData WHERE businessDate= :businessDate AND keyType= :keyType");
        query.setParameter("businessDate",businessDate);
        query.setParameter("keyType",dataType);
        List<AggregatedResultDefinitionData> result=query.getResultList();
        return result;
    }

}
