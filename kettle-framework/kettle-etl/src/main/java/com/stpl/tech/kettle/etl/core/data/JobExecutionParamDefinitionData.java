package com.stpl.tech.kettle.etl.core.data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "JOB_EXECUTION_PARAM_MAPPING")
public class JobExecutionParamDefinitionData {

    private Integer jobExecutionParamId;

    private Integer jobExecutionId;

    private Integer paramId;

    private String paramName;

    private String paramValue;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name="JOB_EXECUTION_PARAM_MAPPING_ID",unique = true, nullable = false)
    public Integer getJobExecutionParamId() {
        return jobExecutionParamId;
    }

    public void setJobExecutionParamId(Integer jobExecutionParamId) {
        this.jobExecutionParamId = jobExecutionParamId;
    }

    @Column(name = "JOB_EXECUTION_DEFINITION_ID", nullable = false)
    public Integer getJobExecutionId() {
        return jobExecutionId;
    }

    public void setJobExecutionId(Integer jobExecutionId) {
        this.jobExecutionId = jobExecutionId;
    }

    @Column(name = "PARAM_ID", nullable = false)
    public Integer getParamId() {
        return paramId;
    }

    public void setParamId(Integer paramId) {
        this.paramId = paramId;
    }

    @Column(name = "PARAM_NAME", nullable = false)
    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    @Column(name = "PARAM_VALUE", nullable = false)
    public String getParamValue() {
        return paramValue;
    }

    public void setParamValue(String paramValue) {
        this.paramValue = paramValue;
    }
}
