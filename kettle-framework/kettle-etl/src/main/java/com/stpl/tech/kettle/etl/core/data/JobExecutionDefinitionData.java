package com.stpl.tech.kettle.etl.core.data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "JOB_EXECUTION_DEFINITION")
public class JobExecutionDefinitionData {

    private Integer executionId;

    private JobDefinitionData jobDefinitionData;

    private Date executionTime;

    private Integer executedBy;

    private String jobExecutionStatus;

    private Date businessDate;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name="JOB_EXECUTION_DEFINITION_ID",unique = true, nullable = false)
    public Integer getExecutionId() {
        return executionId;
    }

    public void setExecutionId(Integer executionId) {
        this.executionId = executionId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "JOB_ID", nullable = true)
    public JobDefinitionData getJobDefinitionData() {
        return jobDefinitionData;
    }

    public void setJobDefinitionData(JobDefinitionData jobDefinitionData) {
        this.jobDefinitionData = jobDefinitionData;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "EXECUTION_TIME", length = 19)
    public Date getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Date executionTime) {
        this.executionTime = executionTime;
    }

    @Column(name="EXECUTED_BY", nullable = false)
    public Integer getExecutedBy() {
        return executedBy;
    }

    public void setExecutedBy(Integer executedBy) {
        this.executedBy = executedBy;
    }

    @Column(name="JOB_EXECUTION_STATUS", nullable = false)
    public String getJobExecutionStatus() {
        return jobExecutionStatus;
    }

    public void setJobExecutionStatus(String jobExecutionStatus) {
        this.jobExecutionStatus = jobExecutionStatus;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "BUSINESS_DATE", length = 19)
    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }
}
