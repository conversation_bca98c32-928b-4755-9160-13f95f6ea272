package com.stpl.tech.kettle.etl.dao;

import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.etl.core.data.AggregatedResultDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobParamsDefinitionMappingData;

public interface JobManagementDao extends AbstractDao {

    List<JobParamsDefinitionMappingData> getJobParamsDefinitionMapping(int jobId, String status);

    List<JobExecutionDefinitionData> getJobExecutionDefinitionDataListForJobIdAndBusinessDate(int jobId, Date businessDate);

    boolean updateRecordStatusToInactiveForJobExecutionId(int executionId);

    List<AggregatedResultDefinitionData> getTallyReport(String businessDate, String dataType);
}
