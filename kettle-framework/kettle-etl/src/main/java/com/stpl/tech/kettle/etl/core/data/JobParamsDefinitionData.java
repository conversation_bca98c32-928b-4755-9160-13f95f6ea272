package com.stpl.tech.kettle.etl.core.data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "JOBS_PARAM_DEFINITION")
public class JobParamsDefinitionData {

    private Integer paramDefinitionId;

    private String paramName;

    private String paramCode;

    private String paramDescription;

    private String paramType;

    private String paramCategory;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PARAM_ID", unique = true, nullable = false)
    public Integer getParamDefinitionId() {
        return paramDefinitionId;
    }

    public void setParamDefinitionId(Integer paramDefinitionId) {
        this.paramDefinitionId = paramDefinitionId;
    }

    @Column(name = "PARAM_NAME", nullable = false)
    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    @Column(name = "PARAM_CODE", nullable = false)
    public String getParamCode() {
        return paramCode;
    }

    public void setParamCode(String paramCode) {
        this.paramCode = paramCode;
    }

    @Column(name = "PARAM_DESCRIPTION", nullable = false)
    public String getParamDescription() {
        return paramDescription;
    }

    public void setParamDescription(String paramDescription) {
        this.paramDescription = paramDescription;
    }

    @Column(name = "PARAM_TYPE", nullable = false)
    public String getParamType() {
        return paramType;
    }

    public void setParamType(String paramType) {
        this.paramType = paramType;
    }

    @Column(name = "PARAM_CATEGORY", nullable = false)
    public String getParamCategory() {
        return paramCategory;
    }

    public void setParamCategory(String paramCategory) {
        this.paramCategory = paramCategory;
    }
}
