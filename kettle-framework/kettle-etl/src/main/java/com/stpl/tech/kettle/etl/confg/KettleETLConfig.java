/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.etl.confg;

import java.util.Properties;
import java.util.TimeZone;

import javax.sql.DataSource;

import org.pentaho.di.core.KettleEnvironment;
import org.pentaho.di.core.exception.KettleException;
import org.pentaho.di.core.plugins.PluginFolder;
import org.pentaho.di.core.plugins.StepPluginType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@Configuration
@EnableTransactionManagement
@ComponentScan(basePackages = {
        "com.stpl.tech.kettle.etl"})
@EnableJpaRepositories(basePackages = {
		"com.stpl.tech.kettle.etl.dao" }, entityManagerFactoryRef = "JobsDataSourceEMFactory", transactionManagerRef = "JobsDataSourceTM")
public class KettleETLConfig {

	@Autowired
	private Environment env;

	private static final Logger LOG = LoggerFactory.getLogger(KettleETLConfig.class);
	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
		// Kettle Environment must always be initialized first when using PDI
		// It bootstraps the PDI engine by loading settings, appropriate plugins
		// etc.
		try {
			//TODO : This needs to be fixed
			StepPluginType.getInstance().getPluginFolders().add(new PluginFolder("/usr/share/tomcat8/webapps/kettle-jobs/WEB-INF/classes/plugins", false, true));
			KettleEnvironment.init(false);
		} catch (KettleException e) {
			LOG.error("Error while starting up kettle environment", e);
		}

	}

	@Bean(name = "JobsDataSourceEMFactory")
	public LocalContainerEntityManagerFactoryBean JobsDataSourceEMFactory() {
		final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(etlDataSource());
		em.setPackagesToScan("com.stpl.tech.kettle.etl.core.data");
		final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		em.setJpaProperties(masterAdditionalProperties());
		em.setPersistenceUnitName("JobsDataSourcePUName");
		return em;
	}

	@Bean(name = "etlDataSource")
	public DataSource etlDataSource() {
		final DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName(env.getProperty("jobs.jdbc.driverClassName"));
		dataSource.setUrl(env.getProperty("jobs.jdbc.url"));
		dataSource.setUsername(env.getProperty("jobs.jdbc.user"));
		dataSource.setPassword(env.getProperty("jobs.jdbc.pass"));
		return dataSource;
	}

    @Bean(name = "JobsDataSourceTM")
    public PlatformTransactionManager etlTransactionManager() {
        final JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(JobsDataSourceEMFactory().getObject());
        return transactionManager;
    }

	final Properties masterAdditionalProperties() {
		final Properties hibernateProperties = new Properties();
		hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getProperty("hibernate.hbm2ddl.auto"));
		hibernateProperties.setProperty("hibernate.dialect", env.getProperty("hibernate.dialect"));
		hibernateProperties.setProperty("hibernate.show_sql", env.getProperty("hibernate.show_sql"));
		return hibernateProperties;
	}

    @Bean(name = "ETLDataSourceET")
    public PersistenceExceptionTranslationPostProcessor clmExceptionTranslation() {
        return new PersistenceExceptionTranslationPostProcessor();
    }

}
//
//"com.stpl.tech.kettle.etl.core",
//		"com.stpl.tech.kettle.etl.service.impl",
//		"com.stpl.tech.kettle.etl.service",
//		"com.stpl.tech.kettle.etl.pentaho"