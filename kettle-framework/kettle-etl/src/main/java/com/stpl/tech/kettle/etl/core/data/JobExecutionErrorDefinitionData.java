package com.stpl.tech.kettle.etl.core.data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "JOB_EXECUTION_ERROR_DEFINITION")
public class JobExecutionErrorDefinitionData {


    private Integer jobExecutionErrorId;

    private JobExecutionDefinitionData jobExecutionDefinitionData;

    private String errorType;

    private String errorValue;


    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name="JOB_EXECUTION_ERROR_DEFINITION_ID",unique = true, nullable = false)
    public Integer getJobExecutionErrorId() {
        return jobExecutionErrorId;
    }

    public void setJobExecutionErrorId(Integer jobExecutionErrorId) {
        this.jobExecutionErrorId = jobExecutionErrorId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name ="JOB_EXECUTION_DEFINITION_ID", nullable = false)
    public JobExecutionDefinitionData getJobExecutionDefinitionData() {
        return jobExecutionDefinitionData;
    }

    public void setJobExecutionDefinitionData(JobExecutionDefinitionData jobExecutionDefinitionData) {
        this.jobExecutionDefinitionData = jobExecutionDefinitionData;
    }

    @Column(name = "ERROR_TYPE")
    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    @Column(name = "ERROR_VALUE")
    public String getErrorValue() {
        return errorValue;
    }

    public void setErrorValue(String errorValue) {
        this.errorValue = errorValue;
    }

}
