package com.stpl.tech.kettle.etl.service.impl;

import com.stpl.tech.kettle.etl.Converter.ETLDataConverter;
import com.stpl.tech.kettle.etl.core.JobExecutionDefinitionStatus;
import com.stpl.tech.kettle.etl.core.JobParamMappingStatus;
import com.stpl.tech.kettle.etl.core.JobResponse;
import com.stpl.tech.kettle.etl.core.data.AggregatedResultDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionErrorDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionParamDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobParamsDefinitionMappingData;
import com.stpl.tech.kettle.etl.dao.JobManagementDao;
import com.stpl.tech.kettle.etl.service.JobExecutor;
import com.stpl.tech.kettle.etl.service.JobManagementService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.util.EnvType;
import org.jfree.util.Log;
import org.pentaho.di.core.Result;
import org.pentaho.di.core.exception.KettleXMLException;
import org.pentaho.di.core.parameters.UnknownParamException;
import org.pentaho.di.job.Job;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class JobManagementServiceImpl implements JobManagementService {

    @Autowired
    private JobManagementDao jobManagementDao;

    private static final Logger LOG = LoggerFactory.getLogger(JobManagementServiceImpl.class);

    @Autowired
    private JobExecutor<Job, Result> jobExecutor;

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    private Environment env;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "JobsDataSourceTM", propagation = Propagation.REQUIRED)
    public JobExecutionDefinitionData createExecutionDefinition(int jobId, int executedBy, Date businessDate) {
        LOG.info("creating Execution Definition");
        JobDefinitionData jobDefinitionData = getJobDefinitionData(jobId);
        JobExecutionDefinitionData jobExecutionDefinitionData = ETLDataConverter.convert(jobDefinitionData, businessDate, executedBy);
        jobExecutionDefinitionData = (JobExecutionDefinitionData) jobManagementDao.add(jobExecutionDefinitionData);
        return jobExecutionDefinitionData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "JobsDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public JobDefinitionData getJobDefinitionData(int jobId) {
        Log.info("Getting job with jobId : " + jobId);
        JobDefinitionData jobDefinitionData = jobManagementDao.find(JobDefinitionData.class, jobId);
        if (jobDefinitionData == null) {
            Log.info("Could not get Job with jobId : " + jobId);
        }
        return jobDefinitionData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "JobsDataSourceTM", propagation = Propagation.REQUIRED)
    public JobResponse<Result> executeJob(JobExecutionDefinitionData jobExecutionDefinitionData, Map<String, String> params)
        throws UnknownParamException, KettleXMLException, URISyntaxException, IOException {

        JobDefinitionData jobDefinitionData = jobExecutionDefinitionData.getJobDefinitionData();
        Log.info("Start executing Job with JobId : " + jobDefinitionData.getJobId());

        List<JobParamsDefinitionMappingData> jobParamsMappingList =
            getJobParamsDefinitionMapping(jobDefinitionData.getJobId());
        for (JobParamsDefinitionMappingData data : jobParamsMappingList) {
            JobExecutionParamDefinitionData jobExecutionParamDefinitionData = ETLDataConverter.convert(data,
                params.get(data.getJobParamsDefinitionData().getParamName()),
                jobExecutionDefinitionData.getExecutionId());
            jobManagementDao.add(jobExecutionParamDefinitionData);
        }
        JobResponse<Result> response = jobExecutor.executeJob(env.getProperty("server.base.dir"), jobDefinitionData.getJobFileName(), params);
        LOG.info("Response : " + response);
        return response;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "JobsDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<JobParamsDefinitionMappingData> getJobParamsDefinitionMapping(int jobId) {
        return jobManagementDao.getJobParamsDefinitionMapping(jobId, JobParamMappingStatus.ACTIVE.name());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "JobsDataSourceTM", propagation = Propagation.REQUIRED)
    public JobExecutionParamDefinitionData addJobExecutionParamDefinitionData(JobExecutionParamDefinitionData data) {
        return (JobExecutionParamDefinitionData) jobManagementDao.add(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "JobsDataSourceTM", propagation = Propagation.REQUIRED)
    public JobExecutionErrorDefinitionData addJobExecutionErrorDefinitionData(JobExecutionErrorDefinitionData jobExecutionErrorDefinitionData) {
        return (JobExecutionErrorDefinitionData) jobManagementDao.add(jobExecutionErrorDefinitionData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "JobsDataSourceTM", propagation = Propagation.REQUIRED)
    public List<JobExecutionDefinitionData> getJobExecutionDefinitionDataListForJobIdAndBusinessDate(int jobId, Date businessDate) {
        return jobManagementDao.getJobExecutionDefinitionDataListForJobIdAndBusinessDate(jobId, businessDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "JobsDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean setStatusForRecordsAsInactiveForJobIdAndBusinessDate(int jobId, Date businessDate) {
        LOG.info("into setStatusForRecordsAsInactiveForJobIdAndBusinessDate");
        List<JobExecutionDefinitionData> list = getJobExecutionDefinitionDataListForJobIdAndBusinessDate(jobId, businessDate);
        for (JobExecutionDefinitionData definitionData : list) {
            jobManagementDao.updateRecordStatusToInactiveForJobExecutionId(definitionData.getExecutionId());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "JobsDataSourceTM", propagation = Propagation.REQUIRED)
    public JobExecutionDefinitionData updateJobExecutionStatus(JobExecutionDefinitionData jobExecutionDefinitionData, JobExecutionDefinitionStatus status, EnvType props) {
        jobExecutionDefinitionData.setJobExecutionStatus(status.name());
        jobExecutionDefinitionData = (JobExecutionDefinitionData) jobManagementDao.update(jobExecutionDefinitionData);

        if (status == JobExecutionDefinitionStatus.FAILED) {
            String message = jobExecutionDefinitionData.getJobDefinitionData().getJobName() + " status ::" + JobExecutionDefinitionStatus.FAILED;
            SlackNotificationService.getInstance().sendNotification(props, "Kettle",
                SlackNotification.TALLY_JOBS, message);
        }
        return jobExecutionDefinitionData;
    }

    @Override
    public List<AggregatedResultDefinitionData> getTallyReport(String businessDate, String dataType) {
        return jobManagementDao.getTallyReport(businessDate, dataType);
    }

    public static void main(String[] args) throws Exception {
    }
}
