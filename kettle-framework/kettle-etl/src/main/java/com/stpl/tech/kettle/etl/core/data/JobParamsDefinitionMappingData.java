package com.stpl.tech.kettle.etl.core.data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "JOBS_PARAM_DEFINITION_MAPPING")
public class JobParamsDefinitionMappingData {

    private Integer jobParamDefinitionMappingId;

    private JobDefinitionData jobDefinitionData;

    private JobParamsDefinitionData jobParamsDefinitionData;

    private String status;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "JOBS_PARAM_MAPPING_ID", unique = true, nullable = false)
    public Integer getJobParamDefinitionMappingId() {
        return jobParamDefinitionMappingId;
    }

    public void setJobParamDefinitionMappingId(Integer jobParamDefinitionMappingId) {
        this.jobParamDefinitionMappingId = jobParamDefinitionMappingId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "JOB_ID", nullable = true)
    public JobDefinitionData getJobDefinitionData() {
        return jobDefinitionData;
    }

    public void setJobDefinitionData(JobDefinitionData jobDefinitionData) {
        this.jobDefinitionData = jobDefinitionData;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PARAM_ID", nullable = true)
    public JobParamsDefinitionData getJobParamsDefinitionData() {
        return jobParamsDefinitionData;
    }

    public void setJobParamsDefinitionData(JobParamsDefinitionData jobParamsDefinitionData) {
        this.jobParamsDefinitionData = jobParamsDefinitionData;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

}
