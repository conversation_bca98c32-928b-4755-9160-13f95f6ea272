package com.stpl.tech.kettle.etl.service;

import com.stpl.tech.kettle.etl.core.JobExecutionDefinitionStatus;
import com.stpl.tech.kettle.etl.core.JobResponse;
import com.stpl.tech.kettle.etl.core.data.AggregatedResultDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionErrorDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionParamDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobParamsDefinitionMappingData;
import com.stpl.tech.util.EnvType;
import org.pentaho.di.core.Result;
import org.pentaho.di.core.exception.KettleXMLException;
import org.pentaho.di.core.parameters.UnknownParamException;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface JobManagementService {

    JobExecutionDefinitionData createExecutionDefinition(int jobId, int executedBy, Date businessDate);

    JobDefinitionData getJobDefinitionData(int jobId);

    JobResponse<Result> executeJob(JobExecutionDefinitionData jobExecutionDefinitionData, Map<String, String> params)
        throws UnknownParamException, KettleXMLException, URISyntaxException, IOException;

    List<JobParamsDefinitionMappingData> getJobParamsDefinitionMapping(int jobId);

    JobExecutionParamDefinitionData addJobExecutionParamDefinitionData(JobExecutionParamDefinitionData data);

    JobExecutionErrorDefinitionData addJobExecutionErrorDefinitionData(JobExecutionErrorDefinitionData jobExecutionErrorDefinitionData);

    List<JobExecutionDefinitionData> getJobExecutionDefinitionDataListForJobIdAndBusinessDate(int jobId, Date businessDate);

    boolean setStatusForRecordsAsInactiveForJobIdAndBusinessDate(int jobId, Date businessDate);

    JobExecutionDefinitionData updateJobExecutionStatus(
        JobExecutionDefinitionData jobExecutionDefinitionData, JobExecutionDefinitionStatus status, EnvType props);

    List<AggregatedResultDefinitionData> getTallyReport(String businessDate, String dataType);
}
