package com.stpl.tech.kettle.etl.service;

import com.stpl.tech.kettle.etl.core.JobRequest;
import com.stpl.tech.kettle.etl.core.JobResponse;
import org.pentaho.di.core.exception.KettleXMLException;
import org.pentaho.di.core.parameters.UnknownParamException;

import java.util.Map;

public interface JobExecutor<T, R> {

	public JobResponse<R> executeJobRequest(JobRequest<T> request);

	public JobResponse<R> executeJob(String basePath, String fileName, Map<String, String> params)
			throws UnknownParamException, KettleXMLException;
}
