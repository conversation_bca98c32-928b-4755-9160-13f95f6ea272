package com.stpl.tech.kettle.etl.core.data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "JOB_DEFINITION")
public class JobDefinitionData {

    private Integer jobId;

    private String jobName;

    private String jobDescription;

    private String jobType;

    private String jobFileName;

    private String jobStatus;



    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "JOB_ID", unique = true, nullable = false)
    public Integer getJobId() {
        return jobId;
    }

    public void setJobId(Integer jobId) {
        this.jobId = jobId;
    }

    @Column(name = "JOB_NAME",unique = true, nullable = false)
    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    @Column(name = "JOB_DESCRIPTION", nullable = false)
    public String getJobDescription() {
        return jobDescription;
    }

    public void setJobDescription(String jobDescription) {
        this.jobDescription = jobDescription;
    }

    @Column(name = "JOB_TYPE", nullable = false)
    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    @Column(name = "JOB_FILE_NAME", nullable = false)
    public String getJobFileName() {
        return jobFileName;
    }

    public void setJobFileName(String jobFileName) {
        this.jobFileName = jobFileName;
    }

    @Column(name = "JOB_STATUS", nullable = false)
    public String getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
    }
}
