package com.stpl.tech.kettle.etl.pentaho;

import org.pentaho.di.core.Result;

import com.stpl.tech.kettle.etl.core.JobResponse;
import com.stpl.tech.kettle.etl.core.JobStatus;

public class PentahoJobResponse implements JobResponse<Result> {

	private Result result;
	private JobStatus status;

	public PentahoJobResponse(Result result){
		this.result = result;
	}

	@Override
	public JobStatus getStatus() {
		return status;
	}
	
	@Override
	public void setStatus(JobStatus status) {
		this.status = status;
	}

	@Override
	public Result getResult() {
		return result;
	}

}
