package com.stpl.tech.kettle.etl.Converter;

import com.stpl.tech.kettle.etl.core.JobExecutionDefinitionStatus;
import com.stpl.tech.kettle.etl.core.JobResponse;
import com.stpl.tech.kettle.etl.core.data.JobDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionErrorDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobExecutionParamDefinitionData;
import com.stpl.tech.kettle.etl.core.data.JobParamsDefinitionMappingData;
import com.stpl.tech.util.AppUtils;
import org.jfree.util.Log;
import org.pentaho.di.core.Result;

import java.util.Date;

public class ETLDataConverter {

    public static JobExecutionDefinitionData convert(JobDefinitionData jobDefinitionData,
                                                     Date businessDate, int executedBy) {
        JobExecutionDefinitionData jobExecutionDefinitionData = new JobExecutionDefinitionData();
        jobExecutionDefinitionData.setExecutedBy(executedBy);
        jobExecutionDefinitionData.setBusinessDate(businessDate);
        jobExecutionDefinitionData.setExecutionTime(AppUtils.getCurrentTimestamp());
        jobExecutionDefinitionData.setJobDefinitionData(jobDefinitionData);
        jobExecutionDefinitionData.setJobExecutionStatus(JobExecutionDefinitionStatus.INITIATED.name());
        return jobExecutionDefinitionData;
    }

    public static JobExecutionParamDefinitionData convert(JobParamsDefinitionMappingData
                                                                  jobParamsDefinitionMappingData,
                                                          String paramValue, int jobExecutionId) {
        JobExecutionParamDefinitionData jobExecutionParamDefinitionData = new JobExecutionParamDefinitionData();
        jobExecutionParamDefinitionData.setParamValue(paramValue);
        jobExecutionParamDefinitionData.setParamName(jobParamsDefinitionMappingData
                .getJobParamsDefinitionData()
                .getParamName());
        jobExecutionParamDefinitionData.setParamId(jobParamsDefinitionMappingData
                .getJobParamsDefinitionData().getParamDefinitionId());
        jobExecutionParamDefinitionData.setJobExecutionId(jobExecutionId);
        return jobExecutionParamDefinitionData;
    }

    public static JobExecutionErrorDefinitionData convert(
            JobExecutionDefinitionData jobExecutionDefinitionData,
            Exception ex) {
        JobExecutionErrorDefinitionData data = new JobExecutionErrorDefinitionData();
        data.setErrorType(ex.getClass().getName());
        int maxLength = (ex.getMessage().length()
                < 500)?ex.getMessage().length():500;
        String errorLog = ex.getMessage().substring(0, maxLength);
        data.setErrorValue(errorLog);
        data.setJobExecutionDefinitionData(jobExecutionDefinitionData);
        return data;
    }

    public static JobExecutionErrorDefinitionData convert(
            JobExecutionDefinitionData jobExecutionDefinitionData,
            JobResponse<Result> jobResponse) {
        JobExecutionErrorDefinitionData data = new JobExecutionErrorDefinitionData();
        data.setErrorType("Pentaho Error");
        int maxLength = (jobResponse.getResult().getLogText().length()
                < 500)?jobResponse.getResult().getLogText().length():500;
        String errorLog = jobResponse.getResult().getLogText().substring(0, maxLength);
        data.setErrorValue(errorLog);
        data.setJobExecutionDefinitionData(jobExecutionDefinitionData);
        return data;
    }
}
