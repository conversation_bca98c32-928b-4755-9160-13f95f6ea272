package com.stpl.tech.kettle.etl.service.impl;

import com.stpl.tech.kettle.etl.core.JobRequest;
import com.stpl.tech.kettle.etl.core.JobResponse;
import com.stpl.tech.kettle.etl.pentaho.PentahoJobRequest;
import com.stpl.tech.kettle.etl.service.JobExecutor;
import org.junit.Ignore;
import org.junit.Test;
import org.pentaho.di.core.Result;
import org.pentaho.di.job.Job;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;

@Ignore
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(classes = KettleETLConfig.class, loader = AnnotationConfigContextLoader.class)
public class JobExecutorImplTest {

	@Autowired
	JobExecutor<Job, Result> pentahoExecutor;

	@Test
	public void testJob1() {
		JobRequest<Job> request = new PentahoJobRequest("Test 1", "jobs/tally/parameterized_job.kjb", new HashMap<String, String>());
		JobResponse<Result> result = pentahoExecutor.executeJobRequest(request);
		System.out.println("Results "+result.getResult().getResult());
	}

	public static void main(String[] args){
		JobExecutor<Job, Result> pentahoExecutor = new JobExecutorImpl();
		JobRequest<Job> request = new PentahoJobRequest("Test 1", "jobs/tally/parameterized_job.kjb", new HashMap<String, String>());
		JobResponse<Result> result = pentahoExecutor.executeJobRequest(request);
		System.out.println("Results "+result.getResult().getResult());
	}
}
