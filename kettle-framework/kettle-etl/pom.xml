<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.stpl.tech.kettle</groupId>
		<artifactId>kettle-framework</artifactId>
		<version>4.1.0-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<groupId>com.stpl.tech.kettle.etl</groupId>
	<artifactId>kettle-etl</artifactId>
	<name>kettle-etl</name>
	<url>http://maven.apache.org</url>
	<packaging>jar</packaging>
	<properties>
		<spring-boot.repackage.skip>true</spring-boot.repackage.skip>
<!--		<jersey.version>1.19.1</jersey.version>-->
<!--		<jsr311-api.version>1.1.1</jsr311-api.version>-->
<!--		<eula-wrap_create-dist-phase/>-->
<!--		<eula-wrap_assign-deps-to-properties-phase/>-->
<!--		<mockito.version>1.10.19</mockito.version>-->
<!--		<eula-wrap_create-izpack-installer-jar-phase/>-->
		<pdi.version>*******-682</pdi.version>
<!--		<eula-wrap_attach-dist-phase/>-->
<!--		<junit.version>4.12</junit.version>-->
	</properties>
	<repositories>
		<repository>
			<id>pentaho-public</id>
			<name>Pentaho Public</name>
			<url>https://nexus.pentaho.org/content/groups/omni/</url>
			<releases>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</snapshots>
		</repository>
	</repositories>

	<dependencies>
		<dependency>
			<groupId>pentaho-kettle</groupId>
			<artifactId>kettle-core</artifactId>
			<version>${pdi.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>xercesImpl</artifactId>
					<groupId>xerces</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-httpclient</artifactId>
					<groupId>commons-httpclient</groupId>
				</exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-c3p0</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-ehcache</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>
						hibernate-commons-annotations
					</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.security</groupId>
					<artifactId>spring-security-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-beans</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.validation</groupId>
					<artifactId>validation-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>pentaho-kettle</groupId>
			<artifactId>kettle-engine</artifactId>
			<version>${pdi.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-cli</artifactId>
					<groupId>commons-cli</groupId>
				</exclusion>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-c3p0</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-ehcache</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>
						hibernate-commons-annotations
					</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.security</groupId>
					<artifactId>spring-security-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-beans</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.validation</groupId>
					<artifactId>validation-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.pentaho.di.plugins</groupId>
			<artifactId>pdi-core-plugins-impl</artifactId>
			<version>${pdi.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>junit</groupId>-->
<!--			<artifactId>junit</artifactId>-->
<!--			<version>${junit.version}</version>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.mockito</groupId>-->
<!--			<artifactId>mockito-all</artifactId>-->
<!--			<version>${mockito.version}</version>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>xerces</groupId>-->
<!--			<artifactId>xercesImpl</artifactId>-->
<!--			<version>2.9.1</version>-->
<!--			<scope>test</scope>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<artifactId>*</artifactId>-->
<!--					<groupId>*</groupId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>commons-cli</groupId>-->
<!--			<artifactId>commons-cli</artifactId>-->
<!--			<version>1.3.1</version>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>com.sun.jersey</groupId>-->
<!--			<artifactId>jersey-core</artifactId>-->
<!--			<version>${jersey.version}</version>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>com.sun.jersey</groupId>-->
<!--			<artifactId>jersey-client</artifactId>-->
<!--			<version>${jersey.version}</version>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>com.sun.jersey</groupId>-->
<!--			<artifactId>jersey-bundle</artifactId>-->
<!--			<version>${jersey.version}</version>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>javax.ws.rs</groupId>-->
<!--			<artifactId>jsr311-api</artifactId>-->
<!--			<version>${jsr311-api.version}</version>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>javax.servlet</groupId>-->
<!--			<artifactId>javax.servlet-api</artifactId>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
		<!--<dependency>-->
			<!--<groupId>com.stpl.tech.kettle</groupId>-->
			<!--<artifactId>kettle-reports</artifactId>-->
			<!--<version>3.1.0-SNAPSHOT</version>-->
		<!--</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.springframework</groupId>-->
<!--			<artifactId>spring-context</artifactId>-->
<!--			<version>4.2.5.RELEASE</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.springframework</groupId>-->
<!--			<artifactId>spring-test</artifactId>-->
<!--			<version>4.2.5.RELEASE</version>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
		<!-- https://mvnrepository.com/artifact/mysql/mysql-connector-java -->
<!--		<dependency>-->
<!--			<groupId>mysql</groupId>-->
<!--			<artifactId>mysql-connector-java</artifactId>-->
<!--			<version>5.1.48</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.stpl.tech.kettle.master</groupId>
			<artifactId>master-core</artifactId>
			<version>4.1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

	</dependencies>
</project>
