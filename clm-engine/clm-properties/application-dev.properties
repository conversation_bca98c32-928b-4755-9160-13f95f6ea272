# All Spring Configurations

spring.profiles.active=dev

#All Common Properties Goes here

# Common System configurations

swagger.user-name=SwaggerDev
swagger.user-password=SwaggerDev@12345

# Common Brand configurations

brand.secret-key=Ch@@y05#D3S1C$@iD3v
brand.referral-domain-uri-prefix=https://devchaayos.page.link

#All Environment Properties Goes here
env.type=DEV
env.dine-in-token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6IkRJTkVfSU4iLCJlbnZUeXBlIjoiREVWIiwicGFydG5lcklkIjoxNSwicGFzc0NvZGUiOiJSQzdKODIySDdGREdORTYiLCJpYXQiOjE1ODczODI3NjF9.S5xx5c4chqH36auT8QmOlTwlVJwx-JlXDjCNBs6eR8Q
env.neo-base-url=http://************/neo-service/
env.truecaller-base-url=http://************/truecaller-service/
env.master-base-url=http://dev.kettle.chaayos.com:9595/master-service/
env.kettle-base-url=http://dev.kettle.chaayos.com:9595/kettle-service/
env.offer-base-url=http://dev.kettle.chaayos.com:9595/offer-service/
env.kettle-crm-base-url=http://dev.kettle.chaayos.com:9595/kettle-crm/
env.send-otp=true
env.skip-neo-traffic-control=true
env.product-image-base-url=https://d1nqp92n3q8zl7.cloudfront.net/product_image/
env.offer-image-base-url=https://d3rcyooxiaudps.cloudfront.net/
env.referral-base-url=http://************/signup
env.unit-non-filtered-list=26164,26135

env.razor-pay-payment-mode-id=12
env.wallet-payment-mode-id=10


env.razor-pay-payment-secret-key=rzp_test_5IXQfPSH0NO8Bk
env.razor-pay-redirect-url=http://************/payProcess

env.firebase-dynamic-links-base-url=https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=AIzaSyB9penIUCmyeSD5_jBAHTqhhayXRCErzDY

env.paytm-client-id=merchant-chaayos-prod
env.paytm-enabled=true
env.razor-pay-enabled=true
env.use-menu-recommendations=true
env.paytm-mid=Chaayo85776476098235
env.bazaar-url=https://bazaar.chaayos.com


#All DB Connections

master.jdbc.url=*****************************************************************************
master.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc.user=root
master.jdbc.pass=Chaayos@12345

kettle.jdbc.url=**********************************************************************
kettle.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
kettle.jdbc.user=root
kettle.jdbc.pass=Chaayos@12345


spring.data.mongodb.database=dine_in_app_dev
spring.data.mongodb.uri=************************************************************************************************

# Redis Config
spring.cache.type=redis
spring.redis.host=localhost
spring.redis.port=6379


cloud.aws.credentials.accessKey=********************
cloud.aws.credentials.secretKey=VKJkknoSkiQ7CF8IKJ/zrZszmWnIsSnhYOxfCtcJ
cloud.aws.credentials.instanceProfile=true
cloud.aws.region.static=ap-south-1
cloud.aws.stack.auto=false


