# All Spring Configurations

spring.profiles.active=sprod

#All Common Properties Goes here

# Common System configurations

swagger.user-name=SwaggerProd
swagger.user-password=S4@bb3RB40D@De5I

# Common Brand configurations

brand.secret-key=C4@@y0S#De55i$A1B7s
brand.referral-domain-uri-prefix=https://stpl.page.link

#All Environment Properties Goes here
env.type=SPROD

env.dine-in-token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6IkRJTkVfSU4iLCJlbnZUeXBlIjoiREVWIiwicGFydG5lcklkIjoxNSwicGFzc0NvZGUiOiJSQzdKODIySDdGREdORTYiLCJpYXQiOjE1ODczODI3NjF9.S5xx5c4chqH36auT8QmOlTwlVJwx-JlXDjCNBs6eR8Q
env.neo-base-url=https://internal.chaayos.com/neo-service/
env.truecaller-base-url=https://internal.chaayos.com/truecaller-service/
env.referral-base-url=https://cafes.chaayos.com/signup
env.master-base-url=https://internal.chaayos.com/master-service/
env.kettle-base-url=https://internal.chaayos.com/kettle-service/
env.offer-base-url=https://internal.chaayos.com/offer-service/
env.kettle-crm-base-url=https://internal.chaayos.com/kettle-crm/
env.razor-pay-payment-secret-key=rzp_live_tyTbBDh9aQPqiV
env.razor-pay-redirect-url=https://cafes.chaayos.com/payProcess
env.product-image-base-url=https://d3pjt1af33nqn0.cloudfront.net/product_image/
env.offer-image-base-url=https://d1o715igja4la8.cloudfront.net/
env.use-menu-recommendations=true
master.jdbc.url=******************************************************************************************************
master.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc.user=chaayosprodusr
master.jdbc.pass=ch44Y05Pr0dU53r

kettle.jdbc.url=***********************************************************************************************
kettle.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
kettle.jdbc.user=chaayosprodusr
kettle.jdbc.pass=ch44Y05Pr0dU53r

env.send-otp=true
env.skip-neo-traffic-control=true

env.razor-pay-payment-mode-id=12
env.wallet-payment-mode-id=10
env.unit-non-filtered-list=26164

env.firebase-dynamic-links-base-url=https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=AIzaSyA1kiWcPJAXbvKxs0Djdgx5lIRAGK-ADVE

env.paytm-client-id=merchant-chaayos-prod
env.paytm-enabled=true
env.razor-pay-enabled=true
env.paytm-mid=Chaayo85776476098235
env.bazaar-url=https://bazaar.chaayos.com

#All DB Connections
spring.data.mongodb.database=dine_in_app
spring.data.mongodb.uri=mongodb+srv://dineinappprod:<EMAIL>/test?retryWrites=true&w=majority

# Redis Config
spring.cache.type=redis
spring.redis.host=localhost
spring.redis.port=6379


cloud.aws.credentials.accessKey=********************
cloud.aws.credentials.secretKey=uT2AzZLe46KKP33o7xwa2m+F9Wip8CZSRNF45M6Q
cloud.aws.credentials.instanceProfile=true
cloud.aws.region.static=ap-south-1
cloud.aws.stack.auto=false

