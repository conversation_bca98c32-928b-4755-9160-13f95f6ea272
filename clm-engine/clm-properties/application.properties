# All Spring Configurations
management.endpoints.web.exposure.include=*
spring.profiles.active=default

#All Common Properties Goes here

# Common System configurations
system.deployment-environment=AWS
system.employee-id=120056

# hibernate.X
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show_sql=false
hibernate.hbm2ddl.auto=validate

spring.jpa.hibernate.use-new-id-generator-mappings=false


# Common Brand configurations

brand.name=Chaayos
brand.folder-name=chaayos
brand.base-app-dir=/data/app/${brand.folder-name}
brand.company-id=1000
brand.brand-id=1
brand.app-partner-id=21
brand.default-menu-sequence-id=5
brand.default-product-recommendation-id=1
brand.trending-today=Trending Now
brand.special-today=Chaayos Special
brand.brand-cash-offer-code=CHAAYOS_CASH
brand.brand-cash-offer-amount=100
brand.channel-partner=21
brand.source-suffix=APP
brand.discount-reason=2004
brand.loyalty-code=2101
brand.loyalty-reason=Loyalty
brand.signup-offer-code=2000
brand.signup-offer-reason=SIGNUP_OFFER
brand.customer-name=Chai Lover
brand.instruction-max-length=999
brand.referral-domain-uri-prefix=https://stpl.page.link
brand.referral-message-description=Signup with your friend's referral code and earn 300 Chaayos Cash
brand.referral-message-title=Chaayos Referral Program Unlocked
brand.referral-image-path=https://d1o715igja4la8.cloudfront.net/chaayos-referral.png
brand.referral-message-text=Hi Friend! I'm delighted to use one of my invites to refer you to Chaayos. My invite code is <REFERRAL_CODE> . Use this link to download the app and sign up to get 300 Chaayos Cash which can be used to pay at all Chaayos outlets. Hurry though, this link expires in 48 hours.
brand.ios-bundle-id=com.chaayos.chaayos
brand.android-package-name=com.chaayos
brand.source-string=CAFE,TAKE_AWAY
brand.super-addon=Super Addon

#All Environment Properties Goes here
env.base-dir=${brand.base-app-dir}/${env.type}
env.time-zone=Asia/Kolkata
env.app-access-user=${brand.name}-${env.type}
env.app-access-password=${brand.name}-${env.type}-${brand.secret-key}
env.send-otp=true
env.skip-neo-traffic-control=true
env.max-order-sync-old-data-size=20
env.max-order-sync-old-data-days=365
env.razor-pay-payment-mode-id=12
env.wallet-payment-mode-id=10
env.loyalty-products-list=1375,170,1205
env.loyalty-products-dimension=Regular
env.loyalty-products-points=60
env.public-links=lkp,lgn,v,rd,sd,sdn,sun,wo,u,up,a,r,banner,con,tc-lkp,tc-lg
env.run-order-update-listener=true
env.cash-payment-mode-id=1
env.optional-links=
env.payment-time-limit=20
env.login-exception-number=9599597740
env.login-exception-otp=7740
env.time-in-milli-sec-to-get-previous-payment=1800000
env.payment-object-life-in-minute=25
env.async-payment-retry-delay-in-milli=30000
env.async-payment-retry-max-attempt=10
env.async-payment-initial-delay-in-milli=30000
env.recommendation-product-threshold=5
env.recommendation-product-max-products=5
env.async-enabled-on-paytm=true
env.async-enabled-on-razorPay=true
env.app-landing-screen=GRID_VIEW
env.use-menu-recommendations=false
env.payment-api-version=rest/v2/
env.previously-ordered-product-threshold=5
env.last-ordered-product-threshold=3
env.in-app-update-enabled=true
env.paytm-enabled-on-android=true
env.paytm-enabled-on-ios=false
env.razor-pay-enabled-on-android=true
env.razor-pay-enabled-on-ios=true
env.wallet-suggestions-limit=3



#Test Values

env.test-value=Inside application.properties


