<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.kettle.tech.clm</groupId>
		<artifactId>clm-engine</artifactId>
		<version>3.1.0-SNAPSHOT</version>
	</parent>
	<groupId>com.kettle.tech</groupId>
	<artifactId>clm-server</artifactId>
	<packaging>pom</packaging>
	<name>clm-server</name>
	<url>http://maven.apache.org</url>

	<modules>
		<module>clm-config-server</module>
		<module>clm-config</module>
		<module>clm-domain</module>
		<module>clm-core</module>
		<module>clm-service</module>
	</modules>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>