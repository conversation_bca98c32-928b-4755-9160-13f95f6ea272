<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <property resource="application.properties" />

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{0}: %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <Append>true</Append>
        <File>${log.location}/${spring.application.name}.log</File>
        <encoder>
            <pattern>%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %-5p %10c: %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.location}/archive/${spring.application.name}/${spring.application.name}-%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>

    </appender>
    <springProfile name="git">
        <root level="INFO">
            <!--<appender-ref ref="CONSOLE" />-->
            <appender-ref ref="FILE" />
        </root>
    </springProfile>
      <springProfile name="native">
        <root level="INFO">
            <!--<appender-ref ref="CONSOLE" />-->
            <appender-ref ref="FILE" />
        </root>
    </springProfile>

</configuration>