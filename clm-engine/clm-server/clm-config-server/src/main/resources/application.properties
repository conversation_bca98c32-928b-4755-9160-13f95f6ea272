# All Spring Configurations

system.installation-dir=D:/clm-engine
spring.application.name=clm-config-server
log.location=D:/clm-engine/logs
management.endpoints.web.exposure.include=*



spring.profiles.active=native
spring.cloud.config.server.git.uri=https://github.com/sunshineTeahouse/dine-in-properties.git
spring.cloud.config.server.git.clone-on-start=true
spring.cloud.config.server.git.username=malimo
spring.cloud.config.server.git.password=777in&&&
#spring.cloud.config.label=dev
logging.level.org.springframework.cloud.config=DEBUG

spring.cloud.config.server.native.searchLocations=file:///${system.installation-dir}/dine-in-properties

spring.cloud.config.server.vault.host=localhost
spring.cloud.config.server.vault.port=8200

server.port=8888
