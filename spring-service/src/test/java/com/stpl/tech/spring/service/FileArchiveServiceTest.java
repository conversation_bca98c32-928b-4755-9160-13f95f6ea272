/**
 * 
 */
package com.stpl.tech.spring.service;

import java.io.File;

import com.stpl.tech.spring.config.SpringUtilityServiceConfig;
import com.stpl.tech.spring.model.FileDetail;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 *
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = { SpringUtilityServiceConfig.class })
public class FileArchiveServiceTest {

	@Autowired
	private FileArchiveService service;

	@Test
	public void uploadFileInBucket() {
		FileDetail detail = service.saveFileToS3("chaayosdevtest", "test1", new File("E://report.pdf"), false);
		System.out.println("Saved File " + detail);
		File file = service.getFileFromS3("E:\\outputs3", detail);
		System.out.println("Retrieved file " + file.getName());
	}

	@Test
	public void downloadFile() {
		File detail = service.getFileFromS3("I:\\downloads", new FileDetail("chaayos","vendors/1496913763_VendorUpload-PAN-47.pdf","1496913763_VendorUpload-PAN-47.pdf",null));
		System.out.println("Retrieved file " + detail.getName());
	}
}
