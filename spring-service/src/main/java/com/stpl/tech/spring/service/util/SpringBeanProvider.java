package com.stpl.tech.spring.service.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class SpringBeanProvider implements ApplicationContextAware {

	private static ApplicationContext context;

	public static boolean containsBean(String alias) {
		return context.containsBean(alias);
	}

	public static Object getBean(String alias) {
		return context.getBean(alias);
	}

	public static <T extends Object> T getBean(String alias, Class<T> beanClass) {
		return context.getBean(alias, beanClass);
	}

	public static <T extends Object> T getBean(Class<T> beanClass) {
		return context.getBean(beanClass);
	}

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		setContext(applicationContext);
	}

	private static synchronized void setContext(ApplicationContext context) {
		SpringBeanProvider.context = context;
	}

	public static void setAppContext(ApplicationContext context) {
		SpringBeanProvider.context = context;
	}

}
