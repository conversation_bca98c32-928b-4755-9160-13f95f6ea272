package com.stpl.tech.spring.crypto.impl;

import java.util.Base64;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.stpl.tech.spring.crypto.CryptoService;
import com.stpl.tech.spring.crypto.DataEncrypter;
import com.stpl.tech.spring.crypto.DataEncrypterFailureHandler;

import lombok.extern.slf4j.Slf4j;

@Service("dataEncrypter")
@Slf4j
public class DataEncrypterImpl implements DataEncrypter {

	@Autowired
	private CryptoService cryptoService;

	@Autowired
	@Qualifier("encryptionCipher")
	private Cipher encryptionCipher;

	@Autowired
	@Qualifier("decryptionCipher")
	private Cipher decryptionCipher;

	@Override
	public String encryptData(String data) {
		try {
			return cryptoService.encrypt(data, encryptionCipher);
		} catch (IllegalBlockSizeException | BadPaddingException e) {
			log.info("unable to encrypt data ");
			return data;
		}
	}

	@Override
	public String decryptData(String data, DataEncrypterFailureHandler dataEncrypterFailureHandler, String filter,
			String value, String attribute, String type) {
		try {
			return cryptoService.decrypt(data, decryptionCipher);
		} catch (IllegalBlockSizeException | BadPaddingException | IllegalArgumentException e) {
			log.info("unable to decrypt data , executing lookup for {} ", value);
			return dataEncrypterFailureHandler.fetchData(filter, value, attribute, type);
		}
	}

	@Override
	public String encryptData(String data, int customerId, Cipher cipher)
			throws IllegalBlockSizeException, BadPaddingException {
		return Base64.getEncoder().encodeToString(cipher.doFinal(data.getBytes()));
	}

}
