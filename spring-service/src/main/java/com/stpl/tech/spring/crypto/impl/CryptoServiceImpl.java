
package com.stpl.tech.spring.crypto.impl;

import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.util.Base64;
import java.util.Objects;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import com.stpl.tech.spring.crypto.CryptoService;

import lombok.extern.slf4j.Slf4j;

@Service("cryptoService")
@Slf4j
public class CryptoServiceImpl implements CryptoService {

	@Override
	public String decrypt(String message, Cipher cipher) throws IllegalBlockSizeException, BadPaddingException {
		if (Objects.isNull(message)) {
			return null;
		}
		return new String(cipher.doFinal(Base64.getDecoder().decode(message)));
	}

	@Override
	public String encrypt(String message, Cipher cipher) throws IllegalBlockSizeException, BadPaddingException {
		if (Objects.isNull(message)) {
			return null;
		}
		return Base64.getEncoder().encodeToString(cipher.doFinal(message.getBytes()));
	}

	@Override
	public KeySpec generatePasswordBasedKeySpec(String password, String salt, int iterations, int keyLength) {
		return new PBEKeySpec(password.toCharArray(), salt.getBytes(), iterations, keyLength);
	}

	@Override
	public SecureRandom getsecureRandom(byte[] IV) {
		SecureRandom secRandom = new SecureRandom();
		secRandom.nextBytes(IV);
		return secRandom;
	}

	@Override
	public SecretKey generateAESSecretKey(KeySpec spec) throws NoSuchAlgorithmException, InvalidKeySpecException {
		return new SecretKeySpec(SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256").generateSecret(spec).getEncoded(),
				"AES");
	}

	@Override
	public Cipher getAESECBCipher(String mode, SecretKey secretKey, boolean refresh, Cipher cipher)
			throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException,
			InvalidAlgorithmParameterException {
		if (Objects.isNull(cipher) || refresh) {
			cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
		}
		if (mode.equalsIgnoreCase("ENCRYPT")) {
			cipher.init(Cipher.ENCRYPT_MODE, secretKey);
		} else {
			cipher.init(Cipher.DECRYPT_MODE, secretKey);
		}
		return cipher;
	}

	@Bean(name = "encryptionCipher")
	private Cipher encryptionCipher(@Value("${data.encryption.passcode:ChaayosSTPL}") String password,
			@Value("${data.encryption.salt:RandomSalt}") String salt) {
		try {
			Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
			cipher.init(Cipher.ENCRYPT_MODE, genertaeSecretKey(password, salt));
			return cipher;
		} catch (Exception e) {
			log.info("Unable to initialize encryption cipher", e);
			return null;
		}
	}

	@Bean(name = "decryptionCipher")
	private Cipher decryptionCipher(@Value("${data.encryption.passcode:ChaayosSTPL}") String password,
			@Value("${data.encryption.salt:RandomSalt}") String salt) {
		try {
			Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
			cipher.init(Cipher.DECRYPT_MODE, genertaeSecretKey(password, salt));
			return cipher;
		} catch (Exception e) {
			log.info("Unable to initialize decryption cipher", e);
			return null;
		}
	}

	@Bean(name = "secretKey")
	private SecretKey genertaeSecretKey(@Value("${data.encryption.passcode:ChaayosSTPL}") String password,
			@Value("${data.encryption.salt:RandomSalt}") String salt) {
		try {
			SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");

			KeySpec spec = new PBEKeySpec(password.toCharArray(), salt.getBytes(), 65536, 256);
			SecretKey secret = new SecretKeySpec(factory.generateSecret(spec).getEncoded(), "AES");
			return secret;
		} catch (Exception e) {
			log.info("Unable to initialize secrert key ", e);
			return null;
		}
	}

	@Override
	public Cipher getRSACipher(String mode, Key key, boolean refresh, Cipher cipher)
			throws InvalidKeyException, NoSuchAlgorithmException, NoSuchPaddingException {
		if (Objects.isNull(cipher) || !refresh) {
			cipher = Cipher.getInstance("RSA");
		}
		if (mode.equalsIgnoreCase("ENCRYPT")) {
			cipher.init(Cipher.ENCRYPT_MODE, key);
		} else {
			cipher.init(Cipher.DECRYPT_MODE, key);
		}

		return cipher;
	}

	@Override
	public KeyPair getRSAKeyPair(int bits) throws NoSuchAlgorithmException {
		KeyPairGenerator generator = KeyPairGenerator.getInstance("RSA");
		generator.initialize(bits);
		return generator.generateKeyPair();
	}

}
