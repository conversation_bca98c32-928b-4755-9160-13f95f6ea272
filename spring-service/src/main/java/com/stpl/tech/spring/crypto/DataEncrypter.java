package com.stpl.tech.spring.crypto;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;

public interface DataEncrypter {

	String encryptData(String data);

	String decryptData(String data, DataEncrypterFailureHandler dataEncrypterFailureHandler, String filter,
			String value, String attribute, String type);

	String encryptData(String data, int customerId, Cipher cipher)
			throws IllegalBlockSizeException, BadPaddingException;

}
