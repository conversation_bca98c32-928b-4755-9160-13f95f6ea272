package com.stpl.tech.spring.service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.Instant;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.ObjectMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.stpl.tech.spring.exception.FileArchiveServiceException;
import com.stpl.tech.spring.model.FileDetail;

@Service
public class FileArchiveService {
	private static final int DEFAULT_EXIPRY_DAYS_FOR_S3_LINK = 5;
	private static final Logger LOG = LoggerFactory.getLogger(FileArchiveService.class);
	private static final String TEMP_UPLOAD_PATH = "/data/app/s3/tempFiles/";

	@Autowired
	private AmazonS3Client s3Client;

	/**
	 * Save image to S3 and return FileDetail containing key and public URL
	 * 
	 * @param multipartFile
	 * @return
	 * @throws IOException
	 */
	public FileDetail saveFileToS3(String bucket, String baseDir, String fileName, MultipartFile multipartFile,
			boolean sameName) throws FileArchiveServiceException {
		LOG.info("Received Request to save a file {} in bucket {} ", multipartFile.getName(), bucket);
		File fileToUpload = null;
		try {

			fileToUpload = convertFromMultiPart(fileName, multipartFile);
		} catch (Exception ex) {
			LOG.error("An error while creating temporary file from multipart file", ex);
			throw new FileArchiveServiceException("An error while creating temporary file from multipart file", ex);
		}
		FileDetail fileDetail = saveFileToS3(bucket, baseDir, fileToUpload, sameName, DEFAULT_EXIPRY_DAYS_FOR_S3_LINK);
		fileToUpload.delete(); // delete temporary file irrespective of file upload successful or not
		return fileDetail;
	}

	public byte[] downloadFile(String s3Key , String bucketName) throws IOException {
		S3Object s3Object = s3Client.getObject(new GetObjectRequest(bucketName, s3Key));
		try (InputStream inputStream = s3Object.getObjectContent();
			 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

			byte[] buffer = new byte[4096];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			return outputStream.toByteArray();
		}
	}

	/**
	 * Save image to S3 and return FileDetail containing key and public URL
	 *
	 * @param file
	 * @return
	 * @throws IOException
	 */
	public FileDetail saveFileToS3(String bucket, String baseDir, String fileName, File file,
								   boolean sameName) throws FileArchiveServiceException {
		LOG.info("Received Request to save a file {} in bucket {} ", file.getName(), bucket);
		FileDetail fileDetail = saveFileToS3(bucket, baseDir, file, sameName, DEFAULT_EXIPRY_DAYS_FOR_S3_LINK);
		file.delete(); // delete temporary file irrespective of file upload successful or not
		return fileDetail;
	}

	public FileDetail saveFileToS3(String bucket, String baseDir, String fileName, MultipartFile multipartFile)
			throws FileArchiveServiceException {
		return saveFileToS3(bucket, baseDir, fileName, multipartFile, false);
	}

	/**
	 * Getting file from S3 into a basedir
	 * 
	 * @param baseDir
	 * @param detail
	 * @return
	 * @throws FileArchiveServiceException
	 */
	public File getFileFromS3(String baseDir, FileDetail detail) throws FileArchiveServiceException {
		LOG.info("Received Request to get a file {} in bucket {} ", detail.getKey(), detail.getBucket());
		OutputStream out = null;
		try {
			File file = new File(baseDir + File.separator + detail.getKey());
			if (!file.getParentFile().exists()) {
				file.getParentFile().mkdirs();
			}
			S3Object s3Object = s3Client.getObject(detail.getBucket(), detail.getKey());
			InputStream in = s3Object.getObjectContent();
			byte[] buf = new byte[1024];
			out = new FileOutputStream(file);
			int count = 0;
			while ((count = in.read(buf)) != -1) {
				if (Thread.interrupted()) {
					throw new InterruptedException();
				}
				out.write(buf, 0, count);
			}
			out.close();
			in.close();
			return file;
		} catch (Exception ex) {
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
					LOG.error("An error occurred in closing resources : ", e);
				}
			}
			LOG.error("An error occurred getting file from S3 : " + detail, ex);
			throw new FileArchiveServiceException("An error occurred fetching file from S3", ex);
		}
	}

	/**
	 * Save image to S3 and return FileDetail containing key and public URL
	 * 
	 * @param bucket
	 * @param baseDir
	 * @param fileToUpload
	 * @return
	 * @throws IOException
	 */
	public FileDetail saveFileToS3(String bucket, String baseDir, File fileToUpload, boolean sameName,int expiryDays)
			throws FileArchiveServiceException {

		LOG.info("Received Request to save a file {} in bucket {} ", fileToUpload.getName(), bucket);
		try {
			String key = baseDir == null ?(sameName ? "" : Instant.now().getEpochSecond() + "_")
					+ fileToUpload.getName():  baseDir + "/" + (sameName ? "" : Instant.now().getEpochSecond() + "_")
					+ fileToUpload.getName();
			String fileName = fileToUpload.getName();

			/* save file */
			s3Client.putObject(new PutObjectRequest(bucket, key, fileToUpload));

			/* get signed URL (valid for one year) */
			URL signedUrl = getSignedUrl(bucket, key, expiryDays);
			LOG.info("Uploaded file is :::: {}", signedUrl.toString());
			return new FileDetail(bucket, key, fileName, signedUrl.toString());
		} catch (Exception ex) {
			LOG.error("An error occurred saving file to S3", ex);
			throw new FileArchiveServiceException("An error occurred saving file to S3", ex);
		}
	}

	public FileDetail saveFileToS3WithSameBaseDir(String bucket, @NonNull String baseDir, File fileToUpload)
			throws FileArchiveServiceException {

		LOG.info("Received Request to save a file {} in bucket {} ", fileToUpload.getName(), bucket);
		try {
			String fileName = fileToUpload.getName();
			String key = baseDir + fileName;
			/* save file */
			s3Client.putObject(new PutObjectRequest(bucket, key, fileToUpload));

			/* get signed URL (valid for one year) */
			URL signedUrl = getSignedUrl(bucket, key, DEFAULT_EXIPRY_DAYS_FOR_S3_LINK);
			LOG.info("Uploaded file is :::: {}", signedUrl.toString());
			return new FileDetail(bucket, key, fileName, signedUrl.toString());
		} catch (Exception ex) {
			LOG.error("An error occurred saving file to S3", ex);
			throw new FileArchiveServiceException("An error occurred saving file to S3", ex);
		}
	}


	public FileDetail saveFileToS3(String bucket, String baseDir, File fileToUpload, boolean sameName)
			throws FileArchiveServiceException {
		return saveFileToS3(bucket, baseDir, fileToUpload, sameName, DEFAULT_EXIPRY_DAYS_FOR_S3_LINK);
	}
	
	public URL getSignedUrl(String bucket, String key, int expiryDays) {
		GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucket, key);
		generatePresignedUrlRequest.setMethod(HttpMethod.GET);
		generatePresignedUrlRequest.setExpiration(getExpirationDate(expiryDays));
		return s3Client.generatePresignedUrl(generatePresignedUrlRequest);
	}

	public URL getSignedUrl(String bucket, String key) {
		GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucket, key);
		generatePresignedUrlRequest.setMethod(HttpMethod.GET);
		generatePresignedUrlRequest.setExpiration(getExpirationDate(DEFAULT_EXIPRY_DAYS_FOR_S3_LINK));
		return s3Client.generatePresignedUrl(generatePresignedUrlRequest);
	}

	public FileDetail saveFileToS3(String bucket, String baseDir, File fileToUpload)
			throws FileArchiveServiceException {

		return saveFileToS3(bucket, baseDir, fileToUpload, false, DEFAULT_EXIPRY_DAYS_FOR_S3_LINK);
	}

	public FileDetail uploadFile(String bucketName, String key, MultipartFile file) throws IOException {
		ObjectMetadata metadata = new ObjectMetadata();
		metadata.setContentLength(file.getSize());
		metadata.setContentType(file.getContentType());

		try (InputStream inputStream = file.getInputStream()) {
			s3Client.putObject(bucketName, key, inputStream, metadata);
		}
		URL signedUrl = getSignedUrl(bucketName, key,  DEFAULT_EXIPRY_DAYS_FOR_S3_LINK);
		LOG.info("Uploaded file is :::: {}", signedUrl.toString());
		return new FileDetail(bucketName, key, file.getName(), signedUrl.toString());
	}


	private Date getExpirationDate(int daysToExpire) {
		Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Kolkata"));
		calendar.add(Calendar.DATE, daysToExpire);
		return calendar.getTime();
	}

	/**
	 * Delete image from S3 using specified key
	 * 
	 * @param fileDetail
	 */
	public void deleteImageFromS3(String bucket, FileDetail fileDetail) {
		s3Client.deleteObject(new DeleteObjectRequest(bucket, fileDetail.getKey()));
	}

	/**
	 * Convert MultiPartFile to ordinary File
	 * 
	 * @param multipartFile
	 * @return
	 * @throws IOException
	 */
	public File convertFromMultiPart(String fileName, MultipartFile multipartFile) throws IOException {
		File file = new File(TEMP_UPLOAD_PATH + fileName);
		if (!file.exists()) {
			file.getParentFile().mkdirs();
		}
		file.createNewFile();
		FileOutputStream fos = new FileOutputStream(file);
		try {
			fos.write(multipartFile.getBytes());
		} catch (Exception e) {
			LOG.error("Error writing file:::", e);
		} finally {
			fos.close();
		}
		return file;
	}

	/**
	 *
	 * @param resourceFileName
	 * @param destinationDirectory
	 * @return
	 *
	 * This Function copy the give file to destination path and return TRUE
	 * if file copied successfully otherwise return false if some error or exception
	 * occered in copying or saveing the file.
	 */
	public boolean saveFileToDestinationPath(String resourceFileName,String destinationDirectory,String fileName){
		try {
			ClassPathResource resource = new ClassPathResource(resourceFileName);
			File destDir = new File(destinationDirectory);
			if (!destDir.exists()) {
				destDir.mkdirs();
			}
			Path destinationPath = destDir.toPath().resolve(fileName+".xls");
			FileCopyUtils.copy(resource.getInputStream(), Files.newOutputStream(destinationPath));
			LOG.info("File copied successfully to: {} " ,destinationPath.toAbsolutePath());
			return true;
		} catch (IOException e) {
			LOG.info("Error in saving file : {}",e);
			return false;
		}
	}


}
