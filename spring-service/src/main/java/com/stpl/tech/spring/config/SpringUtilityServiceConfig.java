/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.spring.config;

import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3Client;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan({ "com.stpl.tech.spring.service","com.stpl.tech.spring.config" })
public class SpringUtilityServiceConfig {

	public SpringUtilityServiceConfig() {
		super();
	}

	@Bean
	public AmazonS3Client amazonS3Client() {
		/*AmazonS3Client client =  new AmazonS3Client(
				new BasicAWSCredentials("********************", "muUI7le4c2BY7uby0YIVfX3tGffobVAe+5jPrvmS"));*/
		AmazonS3Client client =  new AmazonS3Client(
				new BasicAWSCredentials("********************", "waqQ/XK541NHcKM7xIWi5WflDunCSqfmTAA9XoSY"));

		//client.setRegion(Region.getRegion(Regions.EU_WEST_1));
		return client;
	}
}