import React, {Component} from 'react';
import {<PERSON>ert, NetInfo, SafeAreaView} from 'react-native';
import {connect} from 'react-redux';
import AppContainer from "./navigation/Navigator";
import NavigationService from "./store/services/NavigationService";
import * as LoginActions from "./store/actions/LoginActions";


type Props = {};

class Boot extends Component<Props> {

    async componentDidMount() {
        NetInfo.isConnected.addEventListener('connectionChange', this.handleConnectionChange);
    }

    componentWillUnmount() {
        NetInfo.isConnected.removeEventListener('connectionChange', this.handleConnectionChange);
    }


    handleConnectionChange = (isConnected) => {
        this.props.setIsConnected(isConnected, this.props);
    };

    getActiveRouteName(navigationState) {
        if (!navigationState) {
            return null;
        }
        const route = navigationState.routes[navigationState.index];
        if (route.routes) {
            return this.getActiveRouteName(route);
        }
        return route.routeName;
    }

    handleNavigationStateChange(prevState, currentState) {
        const currentScreen = this.getActiveRouteName(currentState);
        const prevScreen = this.getActiveRouteName(prevState);
        if (currentScreen != prevScreen) {
            this.props.setPrevScreen(prevScreen);
            this.props.setCurrScreen(currentScreen);
        }
    }

    render() {
        return (
            <SafeAreaView style={{flex: 1, backgroundColor: '#d4272a'}}>
                <AppContainer
                    onNavigationStateChange={(prevState, currentState) => this.handleNavigationStateChange(prevState, currentState)}
                    ref={navigatorRef => {
                        NavigationService.setTopLevelNavigator(navigatorRef);
                    }}/>
            </SafeAreaView>
        );
    }
}


const mapStateToProps = state => {
    return {
        internetConnected: state.loginReducer.internetConnected,
        fcmToken:state.loginReducer.fcmToken
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setPrevScreen: (prevScreen) => dispatch({type: "SET_PREV_ROUTE", payload: prevScreen}),
        setCurrScreen: (currScreen) => dispatch({type: "SET_CURR_ROUTE", payload: currScreen}),
        setIsConnected: (connected) => dispatch(LoginActions.setIsConnected(connected)),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(Boot);