import React from 'react';
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import {createAppContainer, createDrawerNavigator, createStackNavigator, createSwitchNavigator} from "react-navigation";
import DrawerSidebar from "./../screens/DrawerSidebar";
import LoginScreen from "./../screens/LoginScreen";
import SupplierHomeScreen from "./../screens/SupplierHomeScreen";
import SettingsScreen from "./../screens/SettingsScreen";
import MainRoutingScreen from "./../screens/MainRoutingScreen";
import RetailerManagementScreen from "./../screens/RetailerManagementScreen";
import HandlerListScreen from "./../screens/HandlerListScreen";
import ConnectionErrorScreen from "./../screens/ConnectionErrorScreen";
import ChangePasswordScreen from "./../screens/ChangePasswordScreen";

const SalesHandlerNavigator = createStackNavigator({
    Home: {screen: SupplierHomeScreen, navigationOptions: {header: null}},
    //HandlerList: {screen: HandlerListScreen, navigationOptions: {header: null}},
}, {
    navigationOptions: {
        header: null
    },
    initialRoute: "Home",
});

const RetailerManagementNavigator = createStackNavigator({
    ManageRetailers: {screen: RetailerManagementScreen, navigationOptions: {header: null}},
}, {
    navigationOptions: {
        header: null
    },
    initialRoute: "ManageRetailers",
});


const AppDrawerNavigator = createDrawerNavigator({
    Home: {screen: SalesHandlerNavigator},
    //RetailerManagement: {screen: RetailerManagementNavigator},
    //ChangePasswordScreen: {screen: ChangePasswordScreen},
    Settings: {screen: SettingsScreen}
}, {
    navigationOptions: {
        header: null
    },
    contentComponent: props => <DrawerSidebar {...props} />,
    initialRoute: "Home",
    contentOptions: {
        activeTintColor: '#f0af3b',
        inactiveTintColor: 'grey'
    }
});

const AppMainNavigator = createSwitchNavigator({
    MainRouting: {
        screen: MainRoutingScreen,
        navigationOptions: {header: null}
    },
    Login: {
        screen: LoginScreen,
        navigationOptions: {
            header: null,
            drawerLabel: 'Login',
            drawerIcon: ({tintColor}) => (
                <MaterialCommunityIcons name="home" style={{color: tintColor, fontSize: 20}} />
            ),
        }
    },
    ConnectionError: {
        screen: ConnectionErrorScreen,
        navigationOptions: {
            header: null,
            drawerLabel: 'Connection Error',
            drawerIcon: ({tintColor}) => (
                <MaterialCommunityIcons name="home" style={{color: tintColor, fontSize: 20}} />
            ),
        }
    },
    Drawer: {
        screen: AppDrawerNavigator,
        navigationOptions: {}
    }
}, {
    initialRoute: "MainRouting",
    contentOptions: {
        activeTintColor: '#f0af3b',
        inactiveTintColor: 'grey'
    }
});

export default createAppContainer(AppMainNavigator);