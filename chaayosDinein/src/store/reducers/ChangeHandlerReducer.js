export default function reducer(state = {
    showCHLoader: false,
    salesHandler: null,
    newSalesHandler: null,
    salesHandlerName: null,
    salesHandlers: null,
    retailers: null,
    selectedRetailers: null,
    cHFormViewEnabled: false,
    noRetailersFound: false,
    selectAllRetailers: false,
    errorChangeHandler: false,
    successChangeHandler: false
}, action) {
    switch (action.type) {
        case "CH_LOADING":
        {
            return {...state, showCHLoader:action.payload};
            break;
        }
        case "SET_CH_SALES_HANDLER":
        {
            return {...state, salesHandler:action.payload};
            break;
        }
        case "SET_NEW_SALES_HANDLER":
        {
            return {...state, newSalesHandler:action.payload};
            break;
        }
        case "SET_CH_SALES_HANDLER_NAME":
        {
            return {...state, salesHandlerName:action.payload};
            break;
        }
        case "SET_CH_SALES_HANDLERS":
        {
            return {...state, salesHandlers:action.payload};
            break;
        }
        case "SET_CH_RETAILERS":
        {
            return {...state, retailers:action.payload};
            break;
        }
        case "SET_CH_SELECTED_RETAILERS":
        {
            return {...state, selectedRetailers:action.payload};
            break;
        }
        case "SET_CH_SELECT_ALL_RETAILERS":
        {
            return {...state, selectAllRetailers:action.payload};
            break;
        }
        case "SET_CH_FORM_VIEW_ENABLED":
        {
            return {...state, cHFormViewEnabled:action.payload};
            break;
        }
        case "SET_NO_RETAILERS_FOUND":
        {
            return {...state, noRetailersFound:action.payload};
            break;
        }
        case "SET_ERROR_CHANGE_HANDLER":
        {
            return {...state, errorChangeHandler:action.payload};
            break;
        }
        case "SET_SUCCESS_CHANGE_HANDLER":
        {
            return {...state, successChangeHandler:action.payload};
            break;
        }
        default:
            return state;
    }
    return state;
}
