export default function reducer(state = {
    userId: null,
    password:null,
    showLoader: false,
    errorSalesLogin: false,
    errorSalesLoginMessage: null,
    salespersonDetail: null,
    locale: null,
    internetConnected: true,
    prevRoute: null,
    currRoute: null,
    fcmToken: null
}, action) {

    switch (action.type) {
        case "SET_USER_ID":
        {
            return {...state, userId:action.payload};
            break;
        }
        case "SET_PASSWORD":
        {
            return {...state, password:action.payload};
            break;
        }
        case "SET_SHOW_LOADER": {
            return {...state, showLoader: action.payload.showLoader, loadingMessage: action.payload.loadingMessage};
            break;
        }
        case "SET_ERROR_SALES_LOGIN": {
            return {...state, errorSalesLogin: action.payload.errorSalesLogin, errorSalesLoginMessage: action.payload.errorSalesLoginMessage};
            break;
        }
        case "SET_SALESPERSON_DETAIL": {
            return {...state, salespersonDetail: action.payload};
            break;
        }
        case "SET_AUTH_DETAIL": {
            return {...state, authDetail: action.payload};
            break;
        }
        case "SET_LOCALE": {
            return {...state, locale: action.payload};
            break;
        }
        case "SET_INTERNET_CONNECTED": {
            return {...state, internetConnected: action.payload};
            break;
        }
        case "SET_PREV_ROUTE": {
            return {...state, prevRoute: action.payload};
            break;
        }
        case "SET_CURR_ROUTE": {
            return {...state, currRoute: action.payload};
            break;
        }
        case "SET_FCM_TOKEN": {
            return {...state, fcmToken: action.payload};
            break;
        }
        default:
          return state;
    }

    return state;

}
