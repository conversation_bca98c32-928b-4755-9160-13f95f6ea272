export default function reducer(state = {
    employeeId: null,
    firstName: null,
    lastName: null,
    email: null,
    contact: null,
    addressLineOne: null,
    addressLineTwo: null,
    city: null,
    zipCode: null,
    stateName: null,
    country: null,
    avatarSource: null,
    manager:null,
    managers:null,
    showRegistrationScreen: false,
    showLoader: false,
    errorSHRegistration: false,
    successSHRegistration: false,
    employeeIdAlreadyExists: false,
    isValidEmployeeId: true,
    isManagerMissing: false,
    isFirstNameMissing: false,
    isContactMissing: false,
    isAddressMissing: false,
    isCityMissing: false,
    isZipCodeMissing: false,
    isStateNameMissing: false,
    isFirstNameInvalid: false,
    isContactInvalid: false,
    isCityInvalid: false,
    isEmailInvalid: false,
    isZipCodeInvalid: false,
    isInputIDViewEnabled: false,
    isSHFormViewEnabled:false,
    isDuplicateNameError:false,
    isDuplicateContactError:false
}, action) {
    switch (action.type) {
        case "ADD_SALES_HANDLER_LOADING":
        {
            return {...state, showLoader:action.payload};
            break;
        }
        case "SET_EMPLOYEE_ID":
        {
            return {...state, employeeId:action.payload};
            break;
        }
        case "SET_FIRST_NAME":
        {
            return {...state, firstName:action.payload};
            break;
        }
        case "SET_LAST_NAME":
        {
            return {...state, lastName:action.payload};
            break;
        }
        case "SET_EMAIL":
        {
            return {...state, email:action.payload};
            break;
        }
        case "SET_CONTACT":
        {
            return {...state, contact:action.payload};
            break;
        }
        case "SET_ADDRESS_LINE_ONE":
        {
            return {...state, addressLineOne:action.payload};
            break;
        }
        case "SET_ADDRESS_LINE_TWO":
        {
            return {...state, addressLineTwo:action.payload};
            break;
        }
        case "SET_CITY":
        {
            return {...state, city:action.payload};
            break;
        }
        case "SET_ZIP_CODE":
        {
            return {...state, zipCode:action.payload};
            break;
        }
        case "SET_STATE_NAME":
        {
            return {...state, stateName:action.payload};
            break;
        }
        case "SET_COUNTRY":
        {
            return {...state, country:action.payload};
            break;
        }
        case "SET_AVATAR_SOURCE":
        {
            return {...state, avatarSource:action.payload};
            break;
        }
        case "SET_MANAGER":
        {
            return {...state, manager:action.payload};
            break;
        }
        case "SET_MANAGERS":
        {
            return {...state, managers:action.payload};
            break;
        }
        case "SET_ERROR_SH_REGISTRATION": {
            return {...state, errorSHRegistration:action.payload};
            break;
        }
        case "SET_SUCCESS_SH_REGISTRATION": {
            return {...state, successSHRegistration:action.payload};
            break;
        }
        case "SET_EMPLOYEE_ID_ALREADY_EXISTS": {
            return {...state, employeeIdAlreadyExists:action.payload};
            break;
        }
        case "SET_IS_VALID_EMPLOYEE_ID": {
            return {...state, isValidEmployeeId:action.payload};
            break;
        }
        case "SET_ERROR_FIRST_NAME_MISSING": {
            return {...state, isFirstNameMissing:action.payload};
            break;
        }
        case "SET_ERROR_CONTACT_MISSING": {
            return {...state, isContactMissing:action.payload};
            break;
        }
        case "SET_ERROR_ADDRESS_MISSING": {
            return {...state, isAddressMissing:action.payload};
            break;
        }
        case "SET_ERROR_CITY_MISSING": {
            return {...state, isCityMissing:action.payload};
            break;
        }
        case "SET_ERROR_ZIP_CODE_MISSING": {
            return {...state, isZipCodeMissing:action.payload};
            break;
        }
        case "SET_ERROR_ZIP_CODE_INVALID": {
            return {...state, isZipCodeInvalid:action.payload};
            break;
        }
        case "SET_ERROR_STATE_NAME_MISSING": {
            return {...state, isStateNameMissing:action.payload};
            break;
        }
        case "SET_ERROR_MANAGER_MISSING": {
            return {...state, isManagerMissing:action.payload};
            break;
        }
        case "SET_ERROR_FIRST_NAME_INVALID": {
            return {...state, isFirstNameInvalid:action.payload};
            break;
        }
        case "SET_ERROR_CONTACT_INVALID": {
            return {...state, isContactInvalid:action.payload};
            break;
        }
        case "SET_ERROR_CITY_INVALID": {
            return {...state, isCityInvalid:action.payload};
            break;
        }
        case "SET_ERROR_EMAIL_INVALID": {
            return {...state, isEmailInvalid:action.payload};
            break;
        }
        case "SET_IS_INPUT_ID_VIEW_ENABLED": {
            return {...state, isInputIDViewEnabled:action.payload};
            break;
        }
        case "SET_IS_SH_FORM_VIEW_ENABLED": {
            return {...state, isSHFormViewEnabled:action.payload};
            break;
        }
        case "SET_IS_DUPLICATE_NAME_ERROR": {
            return {...state, isDuplicateNameError:action.payload};
            break;
        }
        case "SET_IS_DUPLICATE_CONTACT_ERROR": {
            return {...state, isDuplicateContactError:action.payload};
            break;
        }
        default:
            return state;
    }

    return state;

}
