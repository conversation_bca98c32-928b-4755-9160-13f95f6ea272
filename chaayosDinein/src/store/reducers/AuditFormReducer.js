export default function reducer(state = {
    showAuditLoader: false,
    auditors: null,
    salesHandler: null,
    salesHandlerName: null,
    salesHandlers: null,
    retailer: null,
    retailerName: null,
    retailers: null,
    activityTypes: null,
    activityType: null,
    isNewRetailer: false,
    isPromoter: false,
    noRetailersFound: false,
    noSalesHandlersFound: false,
    isAuditorMissing: false,
    isSalesHandlerMissing: false,
    isRetailerMissing: false,
    isAuditDateMissing: false,
    isAuditTimeMissing: false,
    isActivityTypeMissing: false,
    errorActivityAudit: false,
    successActivityAudit: false,
    auditFormViewEnabled: true,
    auditDate: null,
    auditTime: null,
    isDatePickerVisible: false,
    isTimePickerVisible: false,
    retailerSalesDiaryId: null,
    retailerAgreedForSS: "YES",
    retailerSSOrderQty: "1_PKT",
    retailerSSPaymentStatus: "NO",
    amountFromRetailer: null,
    orderPlacedViaIVR: "NO",
    kilogramsCommitted: null,
    retailerGIFTAIM: null,
    signUPFormSigned: "NO"
}, action) {
    switch (action.type) {
        case "AUDIT_LOADING":
        {
            return {...state, showAuditLoader:action.payload};
            break;
        }
        case "SET_AUDITORS":
        {
            return {...state, auditors:action.payload};
            break;
        }
        case "SET_AUDIT_SALES_HANDLER":
        {
            return {...state, salesHandler:action.payload};
            break;
        }
        case "SET_AUDIT_SALES_HANDLER_NAME":
        {
            return {...state, salesHandlerName:action.payload};
            break;
        }
        case "SET_AUDIT_SALES_HANDLERS":
        {
            return {...state, salesHandlers:action.payload};
            break;
        }
        case "SET_AUDIT_RETAILER":
        {
            return {...state, retailer:action.payload};
            break;
        }
        case "SET_AUDIT_RETAILER_NAME":
        {
            return {...state, retailerName:action.payload};
            break;
        }
        case "SET_AUDIT_RETAILERS":
        {
            return {...state, retailers:action.payload};
            break;
        }
        case "SET_ACTIVITY_TYPES":
        {
            return {...state, activityTypes:action.payload};
            break;
        }
        case "SET_ACTIVITY_TYPE":
        {
            return {...state, activityType:action.payload};
            break;
        }
        case "SET_IS_NEW_RETAILER":
        {
            return {...state, isNewRetailer:action.payload};
            break;
        }
        case "SET_IS_PROMOTER":
        {
            return {...state, isPromoter:action.payload};
            break;
        }
        case "SET_NO_RETAILERS_FOUND":
        {
            return {...state, noRetailersFound:action.payload};
            break;
        }
        case "SET_NO_SALES_HANDERS_FOUND":
        {
            return {...state, noSalesHandlersFound:action.payload};
            break;
        }
        case "SET_ERROR_AUDITOR_MISSING":
        {
            return {...state, isAuditorMissing:action.payload};
            break;
        }
        case "SET_ERROR_SALES_HANDLER_MISSING":
        {
            return {...state, isSalesHandlerMissing:action.payload};
            break;
        }
        case "SET_ERROR_RETAILER_MISSING":
        {
            return {...state, isRetailerMissing:action.payload};
            break;
        }
        case "SET_ERROR_AUDIT_DATE_MISSING":
        {
            return {...state, isAuditDateMissing:action.payload};
            break;
        }
        case "SET_ERROR_AUDIT_TIME_MISSING":
        {
            return {...state, isAuditTimeMissing:action.payload};
            break;
        }
        case "SET_ACTIVITY_AUDIT_SUCCESS":
        {
            return {...state, successActivityAudit:action.payload};
            break;
        }
        case "SET_ACTIVITY_AUDIT_ERROR":
        {
            return {...state, errorActivityAudit:action.payload};
            break;
        }
        case "SET_AUDIT_FORM_VIEW_ENABLED":
        {
            return {...state, auditFormViewEnabled:action.payload};
            break;
        }
        case "SET_AUDIT_DATE":
        {
            return {...state, auditDate:action.payload};
            break;
        }
        case "SET_AUDIT_TIME":
        {
            return {...state, auditTime:action.payload};
            break;
        }
        case "SET_IS_DATE_PICKER_VISIBLE":
        {
            return {...state, isDatePickerVisible:action.payload};
            break;
        }
        case "SET_IS_TIME_PICKER_VISIBLE":
        {
            return {...state, isTimePickerVisible:action.payload};
            break;
        }
        case "SET_RETAILER_SALES_DIARY_ID":
        {
            return {...state, retailerSalesDiaryId:action.payload};
            break;
        }
        case "SET_RETAILERS_SALES_DIARY_IDS":
        {
            return {...state, retailersSalesDiaryIds:action.payload};
            break;
        }
        case "SET_ERROR_SALES_DIARY_ID_MISSING":
        {
            return {...state, isSalesDiaryIdMissing:action.payload};
            break;
        }
        case "SET_ERROR_ACTIVITY_TYPE_MISSING":
        {
            return {...state, isActivityTypeMissing:action.payload};
            break;
        }
        case "SET_RETAILER_AGREED_FOR_SS":
        {
            return {...state, retailerAgreedForSS:action.payload};
            break;
        }
        case "SET_RETAILER_SS_ORDER_QTY":
        {
            return {...state, retailerSSOrderQty:action.payload};
            break;
        }
        case "SET_RETAILER_SS_PAYMENT_STATUS":
        {
            return {...state, retailerSSPaymentStatus:action.payload};
            break;
        }
        case "SET_AMOUNT_FROM_RETAILER":
        {
            return {...state, amountFromRetailer:action.payload};
            break;
        }
        case "SET_ORDER_PLACED_VIA_IVR":
        {
            return {...state, orderPlacedViaIVR:action.payload};
            break;
        }
        case "SET_KILOGRAMS_COMMITTED":
        {
            return {...state, kilogramsCommitted:action.payload};
            break;
        }
        case "SET_RETAILER_GIFT_AIM":
        {
            return {...state, retailerGIFTAIM:action.payload};
            break;
        }
        case "SET_SIGN_UP_FORM_SIGNED":
        {
            return {...state, signUPFormSigned:action.payload};
            break;
        }
        case "SET_AMOUNT_OF_PAYMENT_SS_MISSING":
        {
            return {...state, missingAmountOfPayment:action.payload};
            break;
        }
        case "SET_KG_COMMITTED_SS_MISSING":
        {
            return {...state, missingKGCommitted:action.payload};
            break;
        }
        case "SET_AIM_TO_WIN_MISSING":
        {
            return {...state, missingAIMToWIN:action.payload};
            break;
        }
        default:
            return state;
    }

    return state;

}
