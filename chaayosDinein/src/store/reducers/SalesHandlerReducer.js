export default function reducer(state = {
    noOfDays: 0,
    salesHandlerSummaryLoading: false,
    salesHandlerSummary: null,
    salesHandlerSummaries: [],
    handlersMap: null,
    retailersMap: null,
    ordersList: null,
    pendingOrders: [],
    orderCountDiff: 0,
    metadata: null,
    salesTargetsLoading: false
}, action) {
    switch (action.type) {
        case "SALES_TARGETS_LOADING": {
            return {...state, salesTargetsLoading: action.payload};
            break;
        }
        case "SET_NO_OF_DAYS": {
            return {...state, noOfDays: action.payload};
            break;
        }
        case "SALES_HANDLER_SUMMARY_LOADING": {
            return {...state, salesHandlerSummaryLoading: action.payload};
            break;
        }
        case "SET_SALES_HANDLER_SUMMARY": {
            return {...state, salesHandlerSummary: action.payload};
            break;
        }
        case "SET_SALES_HANDLER_SUMMARIES": {
            return {...state, salesHandlerSummaries: action.payload};
            break;
        }
        case "SET_HANDLERS_MAP": {
            return {...state, handlersMap: action.payload};
            break;
        }
        case "SET_RETAILERS_MAP": {
            return {...state, retailersMap: action.payload};
            break;
        }
        case "SET_ORDER_LIST": {
            return {...state, ordersList: action.payload};
            break;
        }
        case "SET_PENDING_ORDERS": {
            return {...state, pendingOrders: action.payload};
            break;
        }
        case "SET_ORDER_COUNT_DIFF": {
            return {...state, orderCountDiff: action.payload};
            break;
        }
        case "SET_METADATA": {
            return {...state, metadata: action.payload};
            break;
        }
        default:
            return state;
    }
    return state;
}
