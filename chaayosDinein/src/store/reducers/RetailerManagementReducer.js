export default function reducer(state = {
    showLoader: false,
    totalRetailers: 0,
    totalActiveRetailers: 0,
    retailers: null,
    showUpdateLoader: false,
    productPrice: null,
    errorUpdatingPrice: false,
    successUpdatingPrice:false,
    retailer: null,
    products: null,
    updatedProducts: null,
    isPricesChanged: false,
    isValidPrice: false,
    filter: {"status" : "All"},
    beforeFilterRetailers: null,
    targets: null,
    updatedTarget: null,
    orderFrequencies: null,
    updatedOrderFrequency: null,
    updatedCustomTarget: null,
    isUpdatedTargetMissing: false,
    isUpdatedOrderFrequencyMissing: false,
    isUpdatedTargetInvalid: false,
    isRUValidContactNo: true,
    contactRUAlreadyExists: false,
    newContact: null,
    newContactType: {"type" : "OTHER"},
    updatedSalesDiaryId: null,
    isErrorSalesDiaryIdUpdate: false,
    isSalesDiaryIdMissing: false,
    isSalesDiaryIdInvalid: false,
    isDuplicateSalesDiaryId: false
}, action) {
    switch (action.type) {
        case "MANAGE_RETAILERS_LOADING":
        {
            return {...state, showLoader:action.payload};
            break;
        }
        case "SET_TOTAL_RETAILERS":
        {
            return {...state, totalRetailers:action.payload};
            break;
        }
        case "SET_TOTAL_ACTIVE_RETAILERS":
        {
            return {...state, totalActiveRetailers:action.payload};
            break;
        }
        case "SET_RETAILERS":
        {
            return {...state, retailers:action.payload};
            break;
        }
        case "SET_RETAILER":
        {
            return {...state, retailer:action.payload};
            break;
        }
        case "UPDATE_RETAILER_LOADER":
        {
            return {...state, showUpdateLoader:action.payload};
            break;
        }
        case "SET_PRODUCT_PRICE":
        {
            return {...state, productPrice:action.payload};
            break;
        }
        case "SET_ERROR_UPDATING_PRICE":
        {
            return {...state, errorUpdatingPrice:action.payload};
            break;
        }
        case "SET_SUCCESS_UPDATING_PRICE":
        {
            return {...state, successUpdatingPrice:action.payload};
            break;
        }
        case "SET_PRODUCTS":
        {
            return {...state, products:action.payload};
            break;
        }
        case "SET_UPDATED_PRODUCTS":
        {
            return {...state, updatedProducts:action.payload};
            break;
        }
        case "SET_IS_PRICES_CHANGED":
        {
            return {...state, isPricesChanged:action.payload};
            break;
        }
        case "SET_IS_VALID_PRICE":
        {
            return {...state, isValidPrice:action.payload};
            break;
        }
        case "SET_FILTER":
        {
            return {...state, filter:action.payload};
            break;
        }
        case "SET_BEFORE_FILTER_RETAILERS":
        {
            return {...state, beforeFilterRetailers:action.payload};
            break;
        }
        case "SET_TARGETS": {
            return {...state, targets:action.payload};
            break;
        }
        case "SET_UPDATED_TARGET": {
            return {...state, updatedTarget:action.payload};
            break;
        }
        case "SET_UPDATED_CUSTOM_TARGET": {
            return {...state, updatedCustomTarget:action.payload};
            break;
        }
        case "SET_ORDER_FREQUENCIES": {
            return {...state, orderFrequencies:action.payload};
            break;
        }
        case "SET_UPDATED_ORDER_FREQUENCY": {
            return {...state, updatedOrderFrequency:action.payload};
            break;
        }
        case "SET_ERROR_UPDATED_TARGET_MISSING": {
            return {...state, isUpdatedTargetMissing:action.payload};
            break;
        }
        case "SET_ERROR_UPDATED_TARGET_INVALID": {
            return {...state, isUpdatedTargetInvalid:action.payload};
            break;
        }
        case "SET_ERROR_UPDATED_ORDER_FREQUENCY_MISSING": {
            return {...state, isUpdatedOrderFrequencyMissing:action.payload};
            break;
        }
        case "SET_NEW_CONTACT":
        {
            return {...state, newContact:action.payload};
            break;
        }
        case "SET_NEW_CONTACT_TYPE":
        {
            return {...state, newContactType:action.payload};
            break;
        }
        case "SET_IS_RU_VALID_CONTACT_NO": {
            return {...state, isRUValidContactNo:action.payload};
            break;
        }
        case "SET_RU_CONTACT_ALREADY_EXISTS": {
            return {...state, contactRUAlreadyExists:action.payload};
            break;
        }
        case "SET_UPDATED_SALES_DIARY_ID": {
            return {...state, updatedSalesDiaryId:action.payload};
            break;
        }
        case "SET_IS_ERROR_SALES_DIARY_ID_UPDATE": {
            return {...state, isErrorSalesDiaryIdUpdate:action.payload};
            break;
        }
        case "SET_ERROR_SALES_DIARY_ID_MISSING": {
            return {...state, isSalesDiaryIdMissing:action.payload};
            break;
        }
        case "SET_ERROR_SALES_DIARY_ID_INVALID": {
            return {...state, isSalesDiaryIdInvalid:action.payload};
            break;
        }
        case "SET_IS_DUPLICATE_SALES_DIARY_ID": {
            return {...state, isDuplicateSalesDiaryId:action.payload};
            break;
        }
    }
    return state;
}
