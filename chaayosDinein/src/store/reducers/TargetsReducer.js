export default function reducer(state = {
    salesTargetsLoading: false,
    targets: null,
    achievedTargets: null,
    isTargetsMissing: false,
    selectedHandlerId: null,
    selectedTargetType: "DAILY"
}, action) {
    switch (action.type) {
        case "SALES_TARGETS_LOADING": {
            return {...state, salesTargetsLoading: action.payload};
            break;
        }
        case "SET_TARGETS":
        {
            return {...state, targets:action.payload};
            break;
        }
        case "SET_ACHIEVED_TARGETS":
        {
            return {...state, achievedTargets:action.payload};
            break;
        }
        case "SET_ERROR_TARGETS_MISSING": {
            return {...state, isTargetsMissing:action.payload};
            break;
        }
        case "SET_SELECTED_TARGET_TYPE": {
            return {...state, selectedTargetType:action.payload};
            break;
        }
        case "SET_SELECTED_HANDLER_ID": {
            return {...state, selectedHandlerId:action.payload};
            break;
        }
        default:
            return state;
    }

    return state;

}
