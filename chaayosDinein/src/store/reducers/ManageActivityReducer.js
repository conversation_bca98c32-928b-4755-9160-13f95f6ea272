export default function reducer(state = {
    showLoader: false,
    handlers: null,
    currentSalesHandler: null,
    salesHandlerActivities: null,
    retailerActivities: null,
    currentRetailer:null,
    fromDate:null,
    toDate:null,
    isFromDatePickerVisible: false,
    isToDatePickerVisible: false
}, action) {
    switch (action.type) {
        case "MANAGE_ACTIVITIES_LOADING":
        {
            return {...state, showLoader:action.payload};
            break;
        }
        case "SET_HANDLERS":
        {
            return {...state, handlers:action.payload};
            break;
        }
        case "SET_CURRENT_SALES_HANDLER":
        {
            return {...state, currentSalesHandler:action.payload};
            break;
        }
        case "SET_SALES_HANDLER_ACTIVITIES":
        {
            return {...state, salesHandlerActivities:action.payload};
            break;
        }
        case "SET_RETAILER_ACTIVITIES":
        {
            return {...state, retailerActivities:action.payload};
            break;
        }
        case "SET_CURRENT_RETAILER":
        {
            return {...state, currentRetailer:action.payload};
            break;
        }
        case "SET_FROM_DATE":
        {
            return {...state, fromDate:action.payload};
            break;
        }
        case "SET_TO_DATE":
        {
            return {...state, toDate:action.payload};
            break;
        }
        case "SET_IS_FROM_DATE_PICKER_VISIBLE":
        {
            return {...state, isFromDatePickerVisible:action.payload};
            break;
        }
        case "SET_IS_TO_DATE_PICKER_VISIBLE":
        {
            return {...state, isToDatePickerVisible:action.payload};
            break;
        }
    }
    return state;
}
