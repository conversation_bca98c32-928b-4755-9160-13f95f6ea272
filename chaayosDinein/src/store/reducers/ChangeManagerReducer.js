export default function reducer(state = {
    showCMLoader: false,
    manager: null,
    newManager: null,
    managerName: null,
    managers: null,
    salesHandlers: null,
    selectedSalesHandlers: null,
    cMFormViewEnabled: false,
    noSalesHandlersFound: false,
    selectAllSalesHandlers: false,
    errorChangeManager: false,
    successChangeManager: false
}, action) {
    switch (action.type) {
        case "CM_LOADING":
        {
            return {...state, showCMLoader:action.payload};
            break;
        }
        case "SET_CM_MANAGER":
        {
            return {...state, manager:action.payload};
            break;
        }
        case "SET_NEW_MANAGER":
        {
            return {...state, newManager:action.payload};
            break;
        }
        case "SET_CM_MANAGER_NAME":
        {
            return {...state, managerName:action.payload};
            break;
        }
        case "SET_CM_MANAGERS":
        {
            return {...state, managers:action.payload};
            break;
        }
        case "SET_CM_SALES_HANDLERS":
        {
            return {...state, salesHandlers:action.payload};
            break;
        }
        case "SET_CM_SELECTED_SALES_HANDLERS":
        {
            return {...state, selectedSalesHandlers:action.payload};
            break;
        }
        case "SET_CM_SELECT_ALL_SALES_HANDLERS":
        {
            return {...state, selectAllSalesHandlers:action.payload};
            break;
        }
        case "SET_CM_FORM_VIEW_ENABLED":
        {
            return {...state, cMFormViewEnabled:action.payload};
            break;
        }
        case "SET_NO_SALES_HANDLERS_FOUND":
        {
            return {...state, noSalesHandlersFound:action.payload};
            break;
        }
        case "SET_ERROR_CHANGE_MANAGER":
        {
            return {...state, errorChangeManager:action.payload};
            break;
        }
        case "SET_SUCCESS_CHANGE_MANAGER":
        {
            return {...state, successChangeManager:action.payload};
            break;
        }
        default:
            return state;
    }
    return state;
}
