export default function reducer(state = {
    orderReceivedData: null,
    notifiedOrderID: null,
    latestAppUpdates: null
}, action) {
    switch (action.type) {

        case "SET_ORDER_RECEIVED_DATA": {
            return {...state, orderReceivedData: action.payload};
            break;
        }
        case "SET_NOTIFIED_ORDER_ID": {
            return {...state, notifiedOrderID: action.payload};
            break;
        }
        case "SET_LATEST_APP_UPDATES": {
            return {...state, latestAppUpdates: action.payload};
            break;
        }
        default:
            return state;
    }
    return state;
}
