export default function reducer(state = {
    firstName: null,
    lastName: null,
    email: null,
    contact: null,
    salesDiaryId: null,
    addressLineOne: null,
    addressLineTwo: null,
    city: null,
    zipCode: null,
    stateName: null,
    country: null,
    avatarSource: null,
    showRegistrationScreen: false,
    showLoader: false,
    errorRegistration: false,
    successRegistration: false,
    contactAlreadyExists: false,
    isValidContactNo: true,
    isFirstNameMissing: false,
    isContactMissing: false,
    isAddressMissing: false,
    isCityMissing: false,
    isZipCodeMissing: false,
    isStateNameMissing: false,
    isFirstNameInvalid: false,
    isCityInvalid: false,
    isEmailInvalid: false,
    isZipCodeInvalid: false,
    isContactViewEnabled: false,
    isFormViewEnabled:false,
    isSalesDiaryIdMissing: false,
    isSalesDiaryIdInvalid: false,
    isDuplicateSalesDiaryId: false,
    targets: null,
    target: null,
    orderFrequencies: null,
    orderFrequency: null,
    customTarget: null,
    isTargetMissing: false,
    isOrderFrequencyMissing: false
}, action) {
    switch (action.type) {
        case "ADD_RETAILERS_LOADING":
        {
            return {...state, showLoader:action.payload};
            break;
        }
        case "SET_FIRST_NAME":
        {
            return {...state, firstName:action.payload};
            break;
        }
        case "SET_LAST_NAME":
        {
            return {...state, lastName:action.payload};
            break;
        }
        case "SET_EMAIL":
        {
            return {...state, email:action.payload};
            break;
        }
        case "SET_CONTACT":
        {
            return {...state, contact:action.payload};
            break;
        }
        case "SET_SALES_DIARY_ID":
        {
            return {...state, salesDiaryId:action.payload};
            break;
        }
        case "SET_ADDRESS_LINE_ONE":
        {
            return {...state, addressLineOne:action.payload};
            break;
        }
        case "SET_ADDRESS_LINE_TWO":
        {
            return {...state, addressLineTwo:action.payload};
            break;
        }
        case "SET_CITY":
        {
            return {...state, city:action.payload};
            break;
        }
        case "SET_ZIP_CODE":
        {
            return {...state, zipCode:action.payload};
            break;
        }
        case "SET_STATE_NAME":
        {
            return {...state, stateName:action.payload};
            break;
        }
        case "SET_COUNTRY":
        {
            return {...state, country:action.payload};
            break;
        }
        case "SET_AVATAR_SOURCE":
        {
            return {...state, avatarSource:action.payload};
            break;
        }
        case "SET_ERROR_REGISTRATION": {
            return {...state, errorRegistration:action.payload};
            break;
        }
        case "SET_SUCCESS_REGISTRATION": {
            return {...state, successRegistration:action.payload};
            break;
        }
        case "SET_CONTACT_ALREADY_EXISTS": {
            return {...state, contactAlreadyExists:action.payload};
            break;
        }
        case "SET_IS_VALID_CONTACT_NO": {
            return {...state, isValidContactNo:action.payload};
            break;
        }
        case "SET_ERROR_FIRST_NAME_MISSING": {
            return {...state, isFirstNameMissing:action.payload};
            break;
        }
        case "SET_ERROR_CONTACT_MISSING": {
            return {...state, isContactMissing:action.payload};
            break;
        }
        case "SET_ERROR_ADDRESS_MISSING": {
            return {...state, isAddressMissing:action.payload};
            break;
        }
        case "SET_ERROR_CITY_MISSING": {
            return {...state, isCityMissing:action.payload};
            break;
        }
        case "SET_ERROR_ZIP_CODE_MISSING": {
            return {...state, isZipCodeMissing:action.payload};
            break;
        }
        case "SET_ERROR_ZIP_CODE_INVALID": {
            return {...state, isZipCodeInvalid:action.payload};
            break;
        }
        case "SET_ERROR_STATE_NAME_MISSING": {
            return {...state, isStateNameMissing:action.payload};
            break;
        }
        case "SET_ERROR_FIRST_NAME_INVALID": {
            return {...state, isFirstNameInvalid:action.payload};
            break;
        }
        case "SET_ERROR_CITY_INVALID": {
            return {...state, isCityInvalid:action.payload};
            break;
        }
        case "SET_ERROR_EMAIL_INVALID": {
            return {...state, isEmailInvalid:action.payload};
            break;
        }
        case "SET_IS_CONTACT_VIEW_ENABLED": {
            return {...state, isContactViewEnabled:action.payload};
            break;
        }
        case "SET_IS_FORM_VIEW_ENABLED": {
            return {...state, isFormViewEnabled:action.payload};
            break;
        }
        case "SET_ERROR_SALES_DIARY_ID_MISSING": {
            return {...state, isSalesDiaryIdMissing:action.payload};
            break;
        }
        case "SET_ERROR_SALES_DIARY_ID_INVALID": {
            return {...state, isSalesDiaryIdInvalid:action.payload};
            break;
        }
        case "SET_IS_DUPLICATE_SALES_DIARY_ID": {
            return {...state, isDuplicateSalesDiaryId:action.payload};
            break;
        }
        case "SET_TARGETS": {
            return {...state, targets:action.payload};
            break;
        }
        case "SET_TARGET": {
            return {...state, target:action.payload};
            break;
        }
        case "SET_CUSTOM_TARGET": {
            return {...state, customTarget:action.payload};
            break;
        }
        case "SET_ORDER_FREQUENCIES": {
            return {...state, orderFrequencies:action.payload};
            break;
        }
        case "SET_ORDER_FREQUENCY": {
            return {...state, orderFrequency:action.payload};
            break;
        }
        case "SET_ERROR_TARGET_MISSING": {
            return {...state, isTargetMissing:action.payload};
            break;
        }
        case "SET_ERROR_ORDER_FREQUENCY_MISSING": {
            return {...state, isOrderFrequencyMissing:action.payload};
            break;
        }
        default:
            return state;
    }

    return state;

}
