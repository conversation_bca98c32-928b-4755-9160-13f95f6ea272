import { combineReducers } from "redux";
import loginReducer from "./LoginReducer";
import retailerManagementReducer from "./RetailerManagementReducer";
import retailerRegistrationReducer from "./RetailerRegistrationReducer";
import salesHandlerReducer from "./SalesHandlerReducer";
import salesHandlerRegistrationReducer from "./SalesHandlerRegistrationReducer";
import firebaseReducer from "./FirebaseReducer";
import auditFormReducer from "./AuditFormReducer";
import targetsReducer from "./TargetsReducer";
import changePasswordReducer from "./ChangePasswordReducer";
import manageActivityReducer from "./ManageActivityReducer";
import changeHandlerReducer from "./ChangeHandlerReducer";
import changeManagerReducer from "./ChangeManagerReducer";

export default combineReducers({
    loginReducer,
    retailerManagementReducer,
    retailerRegistrationReducer,
    salesHandlerRegistrationReducer,
    salesHandlerReducer,
    firebaseReducer,
    auditFormReducer,
    targetsReducer,
    changePasswordReducer,
    manageActivityReducer,
    changeHandlerReducer,
    changeManagerReducer
})
