import {AsyncStorage} from "react-native";

class StorageService {

    checkEmpty(obj) {
        if (obj === undefined || obj === null || obj === {}) {
            return true;
        }
        if (typeof obj === "string" || typeof obj === "number") {
            return obj.toString().trim().length === 0;
        }
        for (var key in obj) {
            if (hasOwnProperty.call(obj, key)) return false;
        }
        return true;
    }

    getAsyncStoreItem(key) {
        try {
            return AsyncStorage.getItem(key);
        } catch (error) {
            return null;
        }
    }

    setAsyncStoreItem(key, value) {
        try {
            if (value != null) {
                AsyncStorage.setItem(key, JSON.stringify(value));
            }
        } catch (error) {
            // Error saving data
        }
    }

    removeAsyncStoreItem(key) {
        try {
            AsyncStorage.removeItem(key);
        } catch (error) {
            // Error removing data
        }
    }

    setAsyncStoreItemString(key, value) {
        try {
            if (value != null) {
                AsyncStorage.setItem(key, value);
            }
        } catch (error) {
            // Error saving data
        }
    }

    getAuthDetail() {
        return this.getAsyncStoreItem("cad");
    }

    setAuthDetail(data) {
        this.setAsyncStoreItem("cad", data);
    }

    removeAuthDetail() {
        this.removeAsyncStoreItem("cad");
    }

    getSalespersonDetail() {
        return this.getAsyncStoreItem("spd");
    }

    setSalespersonDetail(data) {
        this.setAsyncStoreItem("spd", data);
    }

    removeSalespersonDetail() {
        this.removeAsyncStoreItem("spd");
    }

    setLocale(data) {
        this.setAsyncStoreItemString("al", data);
    }

    getLocale() {
        return this.getAsyncStoreItem("al");
    }

    getFCMToken() {
        return this.getAsyncStoreItem("fcmToken");
    }

    setFCMToken(data) {
        this.setAsyncStoreItem("fcmToken", data);
    }

}

const storageService = new StorageService();
export default storageService;
