import UtilityService from "./UtilityService";
import fetchIntercept from 'fetch-intercept';
import NavigationService from "./NavigationService";
import { Alert } from "react-native";

class RestService {

    constructor() {
        this.launchInterceptor();
    }

    launchInterceptor() {
        fetchIntercept.register({
            request: function (url, config) {
                if(UtilityService.getConnected() == false){
                    NavigationService.navigate('ConnectionError');
                } else {
                    if (UtilityService.getAuthDetail() != null) {
                        config.headers.auth = UtilityService.getAuthDetail().authKey;
                    }
                    return [url, config];
                }
            },

            requestError: function (error) {
                // Called when an error occured during another 'request' interceptor call
                return Promise.reject(error);
            },

            response: function (response) {
                // Modify the response object
                if (response.status == 401) {
                    Alert.alert(
                        'Session expired',
                        'You session has expired. Please login again.',
                        [
                            {text: 'OK', onPress: () => {NavigationService.navigate('Login');}},
                        ],
                        { cancelable: false }
                    );

                }
                return response;
            },

            responseError: function (error) {
                // Handle an fetch error
                if (error.toString().indexOf("Network request failed") >= 0) {
                    NavigationService.navigate('ConnectionError');
                    return {status: 0};
                } else {
                    return Promise.reject(error);
                }
            }
        });
    }

    postJSON(url, data = {}, params = []) {
        let paramString = "";
        params.map((param) => {
            let key = Object.keys(param)[0];
            let value = param[key];
            paramString = paramString + key + "=" + value + "&";
        });
        paramString = paramString.substring(0, paramString.length - 1);
        url = url + "?" + paramString;
        return fetch(url, {
            method: "POST",
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        }).then(response => {
            if (response && response.status && response.status != 401) {
                var contentType = response.headers.get("content-type");
                if(contentType && contentType.includes("text/plain")) {
                    return response.text();
                } else {
                    return response.json();
                }
            } else {
                return null;
            }
        }).catch(error => {
            throw error
        });
    }


    getJSON(url, params = []) {
        let paramString = "";
        params.map((param) => {
            let key = Object.keys(param)[0];
            let value = param[key];
            paramString = paramString + key + "=" + value + "&";
        });
        paramString = paramString.substring(0, paramString.length - 1);
        url = url + "?" + paramString;
        return fetch(url, {
            method: "GET",
            headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json'
            },
        }).then(response => {
            if (response && response.status && response.status != 401) {
                var contentType = response.headers.get("content-type");
                if(contentType && contentType.includes("text/plain")) {
                    return response.text();
                } else {
                    return response.json();
                }
            } else{
                return null;
            }
        }).catch(error => {
            throw error
        });
    }

}

const restService = new RestService();
export default restService;
