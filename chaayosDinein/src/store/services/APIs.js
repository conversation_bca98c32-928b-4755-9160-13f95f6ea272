class APIs {


    constructor() {
        this.neoBaseUrl = "http://dev.dkc.chaayos.com:9595/neo-service/rest/v1/";
        this.masterBaseUrl = "http://dev.dkc.chaayos.com:9595/master-service/rest/v1/";
        this.kettleBaseUrl = "http://dev.dkc.chaayos.com:9595/kettle-service/rest/v1/";
        this.formsBaseUrl = "http://dev.dkc.chaayos.com:9595/forms-service/rest/v1/";
        this.kettleAnalyticsUrl = "http://dev.dkc.chaayos.com:9595/kettle-analytics/rest/v1/";
        /*this.neoBaseUrl = "http://172.16.16.140:8080/neo-service/rest/v1/";
        this.masterBaseUrl = "http://172.16.16.140:8080/master-service/rest/v1/";
        this.kettleBaseUrl = "http://172.16.16.140:8080/kettle-service/rest/v1/";
        this.formsBaseUrl = "http://172.16.16.140:8080/forms-service/rest/v1/";
        this.kettleAnalyticsUrl = "http://172.16.16.140:8080/kettle-analytics/rest/v1/";*/

        //Neo Service
        this.NEO_CACHE_ROOT_CONTEXT = this.neoBaseUrl + "nc/";
        this.STAMPING_ROOT_CONTEXT = this.neoBaseUrl + "st/";
        this.UNIT_CACHE_ROOT_CONTEXT = this.neoBaseUrl + "uc/";
        this.WEB_CART_ROOT_CONTEXT = this.neoBaseUrl + "wcrt/";
        this.WEB_ORDER_ROOT_CONTEXT = this.neoBaseUrl + "word/";
        this.WEB_CUSTOMER_ROOT_CONTEXT = this.neoBaseUrl + "c/";
        this.WEB_RETAILER_ROOT_CONTEXT = this.neoBaseUrl + "r/";
        this.WEB_PAYMENT_ROOT_CONTEXT = this.neoBaseUrl + "wp/";
        this.WEB_PAYMENT_EXTERNAL_ROOT_CONTEXT = this.neoBaseUrl + "wpe/";
        this.WEB_OFFER_ROOT_CONTEXT = this.neoBaseUrl + "woff/";
        this.WEB_INVENTORY_ROOT_CONTEXT = this.neoBaseUrl + "win/";
        this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT = this.kettleBaseUrl + "retailer-management/";
        this.WEB_USER_MANAGEMENT_ROOT_CONTEXT = this.masterBaseUrl + "user-management/";

        //Master Service
        this.WEB_USER_RESPONSES_ROOT_CONTEXT = this.masterBaseUrl + "users/";

        //Kettle Analytics
        this.WEB_ANALYTICS_ROOT_CONTEXT = this.kettleAnalyticsUrl + "analytics/";

        this.urls = {
            salePersonLogin: {
                login: this.WEB_USER_RESPONSES_ROOT_CONTEXT + "login/trim",
                verifyUser: this.WEB_USER_RESPONSES_ROOT_CONTEXT + "verifyUser",
                changePassword: this.WEB_USER_RESPONSES_ROOT_CONTEXT + "changePasscode"
            },
            retailer:{
                lookupContact: this.WEB_RETAILER_ROOT_CONTEXT + "lkpo",
                changeNegotiatedPrice: this.WEB_RETAILER_ROOT_CONTEXT + "cnp",
                getTargetAttributes: this.WEB_RETAILER_ROOT_CONTEXT + "ta"
            },
            salesHandler: {
                fetchRetailers: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "sales-handler/retailers",
                fetchRetailer: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "sales-handler/retailer",
                updateRetailerStatus: this.WEB_RETAILER_ROOT_CONTEXT + "urs",
                updateRetailerTarget: this.WEB_RETAILER_ROOT_CONTEXT + "urt",
                updateRetailerOrderFrequency: this.WEB_RETAILER_ROOT_CONTEXT + "urf",
                addRetailer: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "sales-handler/retailer/add",
                summary: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "sales-handler/summary",
                summaryCount: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "sales-handler/summary/count",
                lookupSalesHandler: this.WEB_USER_MANAGEMENT_ROOT_CONTEXT + "sales-handler/lookup",
                addSalesHandler: this.WEB_USER_MANAGEMENT_ROOT_CONTEXT + "sales-handler/add",
                getManagers: this.WEB_USER_MANAGEMENT_ROOT_CONTEXT + "sales-handler/managers",
                getSalesHandlers: this.WEB_USER_MANAGEMENT_ROOT_CONTEXT + "auditor/sales-handlers",
                addActivityAudit: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "audit/activity/add",
                getActivityTypes: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "audit/activity/types",
                getActivities: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "audit/activities",
                updateActivityStatus: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "audit/activity/update/status",
                getSalesHandlersForActivityAudit: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "audit/activity/sales-handlers",
                changeHandler: this.WEB_RETAILER_ROOT_CONTEXT + "csh",
                changeManager: this.WEB_RETAILER_ROOT_CONTEXT + "cm",
                addRetailerNewContact: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "sales-handler/retailer/add/contact",
                changeRetailerContactType: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "sales-handler/retailer/change/contact/type",
                updateSalesDiaryId: this.WEB_RETAILER_MANAGEMENT_ROOT_CONTEXT + "sales-handler/retailer/update/salesdiaryid"
            },
            targets: {
                fetchTargets: this.WEB_ANALYTICS_ROOT_CONTEXT + "salesHandler/targets",
            },
            neoCache: {
                getLocalities: this.NEO_CACHE_ROOT_CONTEXT + "lcts",
                getCities: this.NEO_CACHE_ROOT_CONTEXT + "cts",
                getDeliveryUnit: this.NEO_CACHE_ROOT_CONTEXT + "lct/d/u2",
                getLocalityData: this.NEO_CACHE_ROOT_CONTEXT + "ct/d/u1",
                getTakeawayUnits: this.NEO_CACHE_ROOT_CONTEXT + "lct/t/us",
                productRecipes: this.NEO_CACHE_ROOT_CONTEXT + "p/r",
                tags: this.NEO_CACHE_ROOT_CONTEXT + "ts",
                tag: this.NEO_CACHE_ROOT_CONTEXT + "t",
                specialMenu: this.NEO_CACHE_ROOT_CONTEXT + "mts",
                productTags: this.NEO_CACHE_ROOT_CONTEXT + "pts",
                getUnitProductPrice: this.NEO_CACHE_ROOT_CONTEXT + "upp"
            },
            unitCache: {
                getUnit: this.UNIT_CACHE_ROOT_CONTEXT + "u" //using
            },
            stamping: {
                registerDevice: this.STAMPING_ROOT_CONTEXT + "rd", //using
                stampDevice: this.STAMPING_ROOT_CONTEXT + "sd" //using
            },
            cart: {
                createCart: this.WEB_CART_ROOT_CONTEXT + "c", //using
                createCartFromOrderId: this.WEB_CART_ROOT_CONTEXT + "c2",
                addItem: this.WEB_CART_ROOT_CONTEXT + "i/a",
                updateItem: this.WEB_CART_ROOT_CONTEXT + "i/u",
                removeItem: this.WEB_CART_ROOT_CONTEXT + "i/r",
                clearCart: this.WEB_CART_ROOT_CONTEXT + "clr",
                checkout: this.WEB_CART_ROOT_CONTEXT + "ckt", //using
                checkoutId: this.WEB_CART_ROOT_CONTEXT + "ckt/id",
                checkoutWOId: this.WEB_CART_ROOT_CONTEXT + "ckt/wo",
                sync: this.WEB_CART_ROOT_CONTEXT + "sync"
            },
            customer: {
                lookup: this.WEB_CUSTOMER_ROOT_CONTEXT + "lkp", //using
                login: this.WEB_CUSTOMER_ROOT_CONTEXT + "lgn",  //using
                signUp: this.WEB_CUSTOMER_ROOT_CONTEXT + "su",
                addresses: this.WEB_CUSTOMER_ROOT_CONTEXT + "as",
                addAddress: this.WEB_CUSTOMER_ROOT_CONTEXT + "add/as",
                addAddressToCart: this.WEB_CUSTOMER_ROOT_CONTEXT + "up/a/c",
                logout: this.WEB_CUSTOMER_ROOT_CONTEXT + "lgt",
                resendVerification: this.WEB_CUSTOMER_ROOT_CONTEXT + "v/r2",
                loyaltea: this.WEB_CUSTOMER_ROOT_CONTEXT + "lyt",
                boughtByYou: this.WEB_CUSTOMER_ROOT_CONTEXT + "bbu"
            },
            payment: {
                create: this.WEB_PAYMENT_ROOT_CONTEXT + "c",
                createRazorPay: this.WEB_PAYMENT_ROOT_CONTEXT + "rc",
                createPaytm: this.WEB_PAYMENT_ROOT_CONTEXT + "pc",
                paymentKey: this.WEB_PAYMENT_ROOT_CONTEXT + "pk",
                validate: this.WEB_PAYMENT_ROOT_CONTEXT + "u",
                cancel: this.WEB_PAYMENT_ROOT_CONTEXT + "cc",
                failure: this.WEB_PAYMENT_ROOT_CONTEXT + "fc"
            },
            paymentExternal: {
                razorPayCheckoutAPI: this.WEB_PAYMENT_EXTERNAL_ROOT_CONTEXT + "rco"
            },
            order: {
                searchByWebId: this.WEB_ORDER_ROOT_CONTEXT + "s/w",
                searchByOrderId: this.WEB_ORDER_ROOT_CONTEXT + "s/id", //using
                searchStatusByOrderId: this.WEB_ORDER_ROOT_CONTEXT + "s/id/s",
                searchCustomerOrders: this.WEB_ORDER_ROOT_CONTEXT + "s/c", //using
                slackCartId: this.WEB_ORDER_ROOT_CONTEXT + "slk/cid",
                slackWOId: this.WEB_ORDER_ROOT_CONTEXT + "slk/oid",
                isFirstOrder: this.WEB_ORDER_ROOT_CONTEXT + "ifo",
                updateStatus: this.WEB_ORDER_ROOT_CONTEXT + "uos",
            },
            offer: {
                apply: this.WEB_OFFER_ROOT_CONTEXT + "a",
            },
            webInventory: {
                unit: this.WEB_INVENTORY_ROOT_CONTEXT + "u",
                unitLive: this.WEB_INVENTORY_ROOT_CONTEXT + "u/l",
                unitProducts: this.WEB_INVENTORY_ROOT_CONTEXT + "u/p",
            }
        };
        this.getUrls = this.getUrls.bind(this);
    }

    getUrls() {
        return this.urls;
    }
}

const apis = new APIs();
export default apis;
