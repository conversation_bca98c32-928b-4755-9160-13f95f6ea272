import StorageService from "./StorageService";

class UtilityService {

    authDetail: null;
    connected: true;

    getAuthDetail() {
        return this.authDetail;
    }

    setAuthDetail(data) {
        this.authDetail = data;
    }

    getConnected() {
        return this.connected;
    }

    setConnected(connected) {
        this.connected = connected;
    }

    checkEmpty(obj) {
        return StorageService.checkEmpty(obj);
    }

    formatDate(date, format) {
        let time = new Date(date);
        let yyyy = time.getFullYear();
        let M = time.getMonth() + 1;
        let d = time.getDate();
        let MM = M;
        const monthList = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        let dd = d;
        let hh = time.getHours();
        let mm = time.getMinutes();
        let ss = time.getSeconds();
        const A = hh < 12 ? "AM" : "PM";
        if (format.indexOf("A") > -1) {
            hh = (hh > 12) ? hh - 12 : hh;
        }
        MM = (M < 10) ? "0" + M : M;
        dd = (d < 10) ? "0" + d : d;
        hh = (hh < 10) ? "0" + hh : hh;
        mm = (mm < 10) ? "0" + mm : mm;
        format = format.replace("yyyy", yyyy);
        format = format.replace("MMM", monthList[M - 1]);
        format = format.replace("MM", MM);
        format = format.replace("dd", dd);
        format = format.replace("hh", hh);
        format = format.replace("mm", mm);
        format = format.replace("ss", ss);
        return format;
    }

    addDate(days) {
        let time = new Date();
        let d = time.getDate();
        d += days;
        time.setDate(d);
        return new Date(time);
    }

    addDateToDate(date, days) {
        let time = (date != null) ? new Date(date) : new Date(null);
        let d = time.getDate();
        d += days;
        time.setDate(d);
        return new Date(time);
    }

    timeDiff(date, date2) {
        return date.getTime() - new Date(date2).getTime();
    }

    getHandlerSummaryObj() {
        return {
            name: null,
            contact: null,
            retailerCount: 0,
            orderCount: 0,
            target: 0,
            deliveredOrderCount: 0,
            newReceivedOrderCount: 0,
            pendingOrderCount: 0,
            cancelledOrderCount: 0,
            cancelRequestOrderCount: 0,
            quantity: 0
        }
    }

    getRetailerSummaryObj() {
        return {
            name: null,
            contact: null,
            orderCount: 0,
            target: 0,
            deliveredOrderCount: 0,
            newReceivedOrderCount: 0,
            pendingOrderCount: 0,
            cancelledOrderCount: 0,
            cancelRequestOrderCount: 0,
            quantity: 0
        }
    }

    getTargetsResponseObj() {
        return {
            primaryHandler: null,
            retailerCount50kg: null,
            retailerSale50kg: null,
            retailerCount30kg: null,
            retailerSale30kg: null,
            retailerCount4kg: null,
            retailerSale4kg: null,
            retailers50Cup: null,
            distinctHandlers: null,
            totalSales: null,
            totalRetailers: null,
            totalKilograms: null,
            totalPacks: null,
            targetType: null
        }
    }

}

const utilityService = new UtilityService();

export default utilityService;
