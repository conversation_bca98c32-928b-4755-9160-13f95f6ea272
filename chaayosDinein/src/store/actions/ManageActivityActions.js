import RestService from "../services/RestService";
import apis from "../services/APIs";
import UtilityService from "../services/UtilityService";

export function updateActivityStatus(activityId, handlerId, status, props) {
    return dispatch => {
        dispatch({type: "MANAGE_ACTIVITIES_LOADING", payload: true});
        RestService.getJSON(apis.getUrls().salesHandler.updateActivityStatus,
            [{handlerId: handlerId}, {activityId: activityId}, {status: status}])
            .then((response) => {
                if (response != null && response === true) {
                    dispatch(fetchActivities(handlerId, props));
                }else{
                    console.log("Failed to update the status")
                }
                dispatch({type: "MANAGE_ACTIVITIES_LOADING", payload: false});
            }).catch((error) => {
            console.error(error);
        });
    }
}


export function fetchActivities(salesHandlerId, props) {
    return dispatch => {
        let fromDate = UtilityService.formatDate(props.fromDate, 'dd-MM-yyyy');
        let toDate = UtilityService.formatDate(props.toDate, 'dd-MM-yyyy');
        if(fromDate === undefined){
            fromDate = UtilityService.formatDate(new Date(), 'dd-MM-yyyy');
        }
        if(toDate === undefined){
            toDate = UtilityService.formatDate(new Date(), 'dd-MM-yyyy');
        }
        let data = {
            "salesHandlerId":parseInt(salesHandlerId, 10),
            "fromDate": fromDate,
            "toDate": toDate
        };
        console.log("data", data);
        dispatch({type: "MANAGE_ACTIVITIES_LOADING", payload: true});
        RestService.postJSON(apis.getUrls().salesHandler.getActivities, data)
            .then((response) => {
                console.log("activities are :: ", response);
                if (response != null) {
                    dispatch({type: "SET_SALES_HANDLER_ACTIVITIES", payload: response});
                    dispatch({type: "SET_CURRENT_SALES_HANDLER", payload: salesHandlerId});
                }else{
                    dispatch({type: "SET_SALES_HANDLER_ACTIVITIES", payload: null});
                }
                dispatch({type: "MANAGE_ACTIVITIES_LOADING", payload: false});
            }).catch((error) => {
            console.error(error);
        });
    }
}

export function fetchSalesHandlersForActivityAudit(salesHandlerId, fromDate, toDate){
    return dispatch => {
        let data = {
            "auditorId":parseInt(salesHandlerId, 10),
            "fromDate": UtilityService.formatDate(fromDate, 'dd-MM-yyyy'),
            "toDate": UtilityService.formatDate(toDate, 'dd-MM-yyyy')
        };
        console.log("get sales handler data", data);
        dispatch({type: "MANAGE_ACTIVITIES_LOADING", payload: true});
        RestService.postJSON(apis.getUrls().salesHandler.getSalesHandlersForActivityAudit,
            data).then((response) => {
            if (response != null && response.length > 0) {
                if (Array.isArray(response)) {
                    dispatch({type: "SET_HANDLERS", payload: [...response]});
                }
            }else{
                dispatch({type: "SET_HANDLERS", payload: null});
            }
            dispatch({type: "MANAGE_ACTIVITIES_LOADING", payload: false});
        }).catch((error) => {
            console.error(error);
            dispatch({type: "MANAGE_ACTIVITIES_LOADING", payload: false});
        });
    }
}
