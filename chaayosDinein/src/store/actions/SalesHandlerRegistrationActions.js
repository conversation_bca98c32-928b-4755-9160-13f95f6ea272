import RestService from "../services/RestService";
import apis from "../services/APIs";
import {ToastAndroid} from "react-native";

export function lookupSalesHandler(employeeId) {
        return dispatch => {
        dispatch({type: "ADD_SALES_HANDLER_LOADING", payload: true});
        dispatch({type: "SET_EMPLOYEE_ID", payload: employeeId});
        if (employeeId.length == 7) {
            RestService.postJSON(apis.getUrls().salesHandler.lookupSalesHandler, employeeId).then((response) => {
                dispatch({type: "SET_EMPLOYEE_ID_ALREADY_EXISTS", payload: response});
                if (response == true) {
                    dispatch({type: "SET_EMPLOYEE_ID_ALREADY_EXISTS", payload: true});
                    dispatch({type: "SET_IS_INPUT_ID_VIEW_ENABLED", payload: true});
                    dispatch({type: "SET_IS_SH_FORM_VIEW_ENABLED", payload: false});
                } else {
                    getManagersList(dispatch);
                }
                dispatch({type: "ADD_SALES_HANDLER_LOADING", payload: false});
            }).catch((error) => {
                console.error(error);
            });
        }
    }
}

function getManagersList(dispatch) {
    RestService.getJSON(apis.getUrls().salesHandler.getManagers).then((response) => {
        if (response != null && response.length > 0) {
            if (response.hasOwnProperty('errorMessage')) {
                dispatch({type: "SET_ERROR_SH_REGISTRATION", payload: true});
                dispatch({type: "SET_IS_SH_FORM_VIEW_ENABLED", payload: false});
                dispatch({type: "SET_IS_INPUT_ID_VIEW_ENABLED", payload: false});
            }
            if (response.hasOwnProperty('id')) {
                dispatch({type: "SET_SUCCESS_SH_REGISTRATION", payload: true});
                dispatch({type: "SET_ERROR_SH_REGISTRATION", payload: false});
                dispatch({type: "SET_IS_SH_FORM_VIEW_ENABLED", payload: false});
                dispatch({type: "SET_IS_INPUT_ID_VIEW_ENABLED", payload: false});
            }
            var managers = {
                "0":"-- None --"
            };
            if (Array.isArray(response)) {
                for(var i = 0; i < response.length; i++) {
                    var obj = response[i];
                    managers[obj.id] = obj.name;
                }
                dispatch({type: "SET_MANAGERS", payload: managers});
                dispatch({type: "SET_EMPLOYEE_ID_ALREADY_EXISTS", payload: false});
                dispatch({type: "SET_IS_INPUT_ID_VIEW_ENABLED", payload: false});
                dispatch({type: "SET_IS_SH_FORM_VIEW_ENABLED", payload: true});
            }
        }
    }).catch((error) => {
        console.error(error);
    });
}

function resetFormFields() {
    return dispatch => {
        dispatch({type: "SET_EMPLOYEE_ID", payload: null});
        dispatch({type: "SET_MANAGER", payload: null});
        dispatch({type: "SET_FIRST_NAME", payload: null});
        dispatch({type: "SET_LAST_NAME", payload: null});
        dispatch({type: "SET_EMAIL", payload: null});
        dispatch({type: "SET_CONTACT", payload: null});
        dispatch({type: "SET_ADDRESS_LINE_ONE", payload: null});
        dispatch({type: "SET_ADDRESS_LINE_TWO", payload: null});
        dispatch({type: "SET_CITY", payload: null});
        dispatch({type: "SET_ZIP_CODE", payload: null});
        dispatch({type: "SET_STATE_NAME", payload: null});
        dispatch({type: "SET_COUNTRY", payload: null});
        dispatch({type: "SET_AVATAR_SOURCE", payload: null});
        dispatch({type: "SET_IS_DUPLICATE_NAME_ERROR", payload: false});
        dispatch({type: "SET_IS_DUPLICATE_CONTACT_ERROR", payload: false});
    }
}

export function validateAndRegister(employeeId, manager, firstName, lastName, email,
                                    contact, addressLineOne, addressLineTwo, city, zipCode, stateName, country, props) {
    return dispatch => {
        dispatch({type: "ADD_SALES_HANDLER_LOADING", payload: true});
        var isValid = true;
        if(manager==null || manager=='0' || manager == ""){
            dispatch({type: "SET_ERROR_MANAGER_MISSING", payload: true});
            isValid = false;
        }else {
            dispatch({type: "SET_ERROR_MANAGER_MISSING", payload: false});
        }
        if (firstName == null || firstName == 'undefined' || firstName == "") {
            dispatch({type: "SET_ERROR_FIRST_NAME_MISSING", payload: true});
            isValid = false;
        } else {
            dispatch({type: "SET_ERROR_FIRST_NAME_MISSING", payload: false});
            dispatch({type: "SET_IS_DUPLICATE_NAME_ERROR", payload: false});
            if (!/^[a-zA-Z ]+$/.test(firstName)) {
                dispatch({type: "SET_ERROR_FIRST_NAME_INVALID", payload: true});
                isValid = false;
            } else {
                dispatch({type: "SET_ERROR_FIRST_NAME_INVALID", payload: false});
            }
        }
        if (contact == null || contact == 'undefined') {
            dispatch({type: "SET_ERROR_CONTACT_MISSING", payload: true});
            isValid = false;
        } else {
            dispatch({type: "SET_ERROR_CONTACT_MISSING", payload: false});
            dispatch({type: "SET_IS_DUPLICATE_CONTACT_ERROR", payload: false});
        }
        if (addressLineOne == null || addressLineOne == 'undefined') {
            dispatch({type: "SET_ERROR_ADDRESS_MISSING", payload: true});
            isValid = false;
        } else {
            dispatch({type: "SET_ERROR_ADDRESS_MISSING", payload: false});
        }
        if (city == null || city == 'undefined') {
            dispatch({type: "SET_ERROR_CITY_MISSING", payload: true});
            isValid = false;
        } else {
            dispatch({type: "SET_ERROR_CITY_MISSING", payload: false});
            if (!/^[a-zA-Z ]+$/.test(city)) {
                dispatch({type: "SET_ERROR_CITY_INVALID", payload: true});
                isValid = false;
            } else {
                dispatch({type: "SET_ERROR_CITY_INVALID", payload: false});
            }
        }
        if (zipCode == null || zipCode == 'undefined') {
            /*dispatch({type: "SET_ERROR_ZIP_CODE_MISSING", payload: true});
            isValid = false;*/
        } else {
            dispatch({type: "SET_ERROR_ZIP_CODE_MISSING", payload: false});
            dispatch({type: "SET_ERROR_ZIP_CODE_INVALID", payload: false});
            if (!/^[0-9]+$/.test(zipCode)) {
                dispatch({type: "SET_ERROR_ZIP_CODE_INVALID", payload: true});
                isValid = false;
            } else {
                dispatch({type: "SET_ERROR_ZIP_CODE_INVALID", payload: false});
            }
        }
        if (stateName == null || stateName == 'undefined') {
            dispatch({type: "SET_ERROR_STATE_NAME_MISSING", payload: true});
            isValid = false;
        } else {
            dispatch({type: "SET_ERROR_STATE_NAME_MISSING", payload: false});
        }
        if (email != null && email != 'undefined' && email!='') {
            var reg = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;
            if (reg.test(email) == false) {
                dispatch({type: "SET_ERROR_EMAIL_INVALID", payload: true});
                isValid = false;
            } else {
                dispatch({type: "SET_ERROR_EMAIL_INVALID", payload: false});
            }
        }else{
            dispatch({type: "SET_ERROR_EMAIL_INVALID", payload: false});
        }
        if (isValid) {
            let data = {
                "salesHandler": {
                    "id": employeeId,
                    "name": firstName,
                    "gender": "M",
                    "primaryContact": contact,
                    "secondaryContact": contact,
                    "biometricId": employeeId,
                    "department": {
                        "id": 2,
                        "name": "Sales",
                        "description": "Sales",
                        "division": {
                            "id": 2,
                            "name": "Express Chai",
                            "description": "Express Chai"
                        },
                        "designations": []
                    },
                    "designation": {
                        "id": 2001,
                        "name": "Territory Sales Executive"
                    },
                    "currentAddress": {
                        "city": city,
                        "state": stateName,
                        "country": "India",
                        "addressType": "OFFICIAL",
                        "line1": addressLineOne,
                        "line2": addressLineTwo,
                        "line3": "",
                        "zipCode": zipCode,
                        "contact1": contact,
                        "contact2": contact
                    },
                    "permanentAddress": {
                        "city": city,
                        "state": stateName,
                        "country": "India",
                        "addressType": "OFFICIAL",
                        "line1": addressLineOne,
                        "line2": addressLineTwo,
                        "line3": "",
                        "zipCode": zipCode,
                        "contact1": contact,
                        "contact2": contact
                    },
                    "employmentType": "FULL_TIME",
                    "employmentStatus": "IN_ACTIVE",
                    "employeeEmail": email,
                    "joiningDate": new Date(),
                    "reportingManager": {
                        "id": manager
                    },
                    "employeeCode": employeeId,
                    "communicationChannel": null,
                    "employeeMealEligible": false
                },
                "onboardedBy": null
            };
            RestService.postJSON(apis.getUrls().salesHandler.addSalesHandler, data).then((response) => {
                if(response!=null){
                    if(response.hasOwnProperty('errorTitle')){
                        if(response.errorTitle == "Duplicate Name"){
                            dispatch({type: "SET_IS_DUPLICATE_NAME_ERROR", payload: true});
                        }
                        if(response.errorTitle == "Duplicate Contact"){
                            dispatch({type: "SET_IS_DUPLICATE_CONTACT_ERROR", payload: true});
                        }
                    }else{
                        dispatch({type: "SET_ERROR_SH_REGISTRATION", payload: true});
                        dispatch({type: "SET_IS_SH_FORM_VIEW_ENABLED", payload: false});
                        dispatch({type: "SET_IS_INPUT_ID_VIEW_ENABLED", payload: false});
                        dispatch(resetFormFields());
                    }
                    if(response == true){
                        dispatch({type: "SET_SUCCESS_SH_REGISTRATION", payload: true});
                        dispatch({type: "SET_ERROR_SH_REGISTRATION", payload: false});
                        dispatch({type: "SET_IS_SH_FORM_VIEW_ENABLED", payload: false});
                        dispatch({type: "SET_IS_INPUT_ID_VIEW_ENABLED", payload: false});
                        dispatch(resetFormFields());
                    }
                    dispatch({type: "ADD_SALES_HANDLER_LOADING", payload: false});
                }
            }).catch((error) => {
                console.error(error);
            });
        } else {
            dispatch({type: "ADD_SALES_HANDLER_LOADING", payload: false});
        }
    }
}