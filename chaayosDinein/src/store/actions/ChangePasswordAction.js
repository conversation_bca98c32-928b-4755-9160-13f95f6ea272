import RestService from "../services/RestService";
import apis from "../services/APIs";


export function checkPassword(userId, password) {
    return dispatch => {
        dispatch({type: "CHANGE_PASSWORD_LOADING", payload: true});
        if (userId !== "" && password !== "" ) {
            let data = {
                "userId": userId,
                "unitId": 1,
                "password": password,
                "terminalId": 1,
                "application": "SALES_APP",
                "screenType": "POS"
            };
            RestService.postJSON(apis.getUrls().salePersonLogin.verifyUser, data).then((response) => {
                if (response != null && response === true) {
                    console.log('Entered old password is correct!');
                    dispatch({type: "SET_IS_OP_FORM_VIEW_ENABLED", payload: false});
                    dispatch({type: "SET_IS_CP_FORM_VIEW_ENABLED", payload: true});
                    dispatch({type: "SET_IS_OLD_PASSWORD_INCORRECT", payload: false});
                } else {
                    dispatch({type: "SET_IS_OLD_PASSWORD_INCORRECT", payload: true});
                }
                dispatch({type: "CHANGE_PASSWORD_LOADING", payload: false});
            }).catch((error) => {
                dispatch({type: "SET_OLD_PASSWORD", payload: null});
                dispatch({type: "CHANGE_PASSWORD_LOADING", payload: false});
            });
        }
    }
}

export function changePassword(userId, oldPassword, newPassword) {
    return dispatch => {
        dispatch({type: "CHANGE_PASSWORD_LOADING", payload: true});
        if (userId !== "" && oldPassword !== "" && newPassword !== "") {
            let data = {
                "userId": userId,
                "unitId": 1,
                "password": oldPassword,
                "newPassword": newPassword,
                "terminalId": 1,
                "application": "SALES_APP",
                "screenType": "POS"
            };
            RestService.postJSON(apis.getUrls().salePersonLogin.changePassword, data).then((response) => {
                if(response != null){
                    if (response === true) {
                        console.log('Entered old password is correct!');
                        dispatch({type: "SET_IS_OP_FORM_VIEW_ENABLED", payload: false});
                        dispatch({type: "SET_IS_CP_FORM_VIEW_ENABLED", payload: false});
                        dispatch({type: "SET_SUCCESS_CHANGE_PASSWORD", payload: true});
                    } else {
                        dispatch({type: "SET_IS_OP_FORM_VIEW_ENABLED", payload: false});
                        dispatch({type: "SET_IS_CP_FORM_VIEW_ENABLED", payload: false});
                        dispatch({type: "SET_ERROR_CHANGE_PASSWORD", payload: true});
                    }
                }else{
                    dispatch({type: "SET_IS_OP_FORM_VIEW_ENABLED", payload: false});
                    dispatch({type: "SET_IS_CP_FORM_VIEW_ENABLED", payload: false});
                    dispatch({type: "SET_ERROR_CHANGE_PASSWORD", payload: true});
                }
                dispatch({type: "CHANGE_PASSWORD_LOADING", payload: false});
            }).catch((error) => {
                dispatch({type: "SET_ERROR_CHANGE_PASSWORD", payload: true});
                dispatch({type: "CHANGE_PASSWORD_LOADING", payload: false});
            });
        }
    }
}

export function reset() {
    return dispatch => {
        dispatch({type: "SET_IS_OP_FORM_VIEW_ENABLED", payload: true});
        dispatch({type: "SET_IS_CP_FORM_VIEW_ENABLED", payload: false});
        dispatch({type: "SET_OLD_PASSWORD", payload: null});
        dispatch({type: "SET_NEW_PASSWORD", payload: null});
        dispatch({type: "SET_CONFIRM_NEW_PASSWORD", payload: null});
        dispatch({type: "SET_IS_OLD_PASSWORD_VALID", payload: false});
        dispatch({type: "SET_IS_OLD_PASSWORD_INCORRECT", payload: false});
        dispatch({type: "SET_IS_NEW_PASSWORD_INCORRECT", payload: false});
        dispatch({type: "SET_IS_PASSWORD_MISMATCH", payload: false});
        dispatch({type: "SET_ERROR_CHANGE_PASSWORD", payload: false});
        dispatch({type: "SET_SUCCESS_CHANGE_PASSWORD", payload: false});
        dispatch({type: "SET_IS_NEW_PASSWORD_VALID", payload: false});
        dispatch({type: "SET_IS_CONFIRM_NEW_PASSWORD_VALID", payload: false});
        dispatch({type: "SET_HIDE_OLD_PASSWORD", payload: true});
        dispatch({type: "SET_HIDE_NEW_PASSWORD", payload: true});
        dispatch({type: "SET_HIDE_CONFIRM_NEW_PASSWORD", payload: true});
    }
}