import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>} from "react-native";
import DeviceInfo from "react-native-device-info";
import NavigationService from "../services/NavigationService";
import StorageService from "../services/StorageService";

export function parseAndSaveNotificationData(notificationData) {
    return dispatch => {
        if(notificationData !=null && notificationData.actionType!=null){
            if(notificationData.actionType === 'order_received'){
                if(notificationData.orderId !=null &&
                    notificationData.retailerId !=null &&
                        notificationData.salesHandlerId !=null){
                    let orderReceivedData = {
                        "orderId" : notificationData.orderId,
                        "retailerId" : notificationData.retailerId,
                        "salesHandlerId" : notificationData.salesHandlerId
                    };
                    dispatch({type: "SET_ORDER_RECEIVED_DATA", payload: orderReceivedData});
                }else{
                    dispatch({type: "SET_ORDER_RECEIVED_DATA", payload: null});
                }
            }
            if(notificationData.actionType === 'app_updated'){
                let latestAppData = {
                    "latest_app_version" : notificationData.latest_app_version,
                    "latest_app_url" : notificationData.latest_app_url,
                    "latest_app_features" : notificationData.latest_app_features
                };
                checkAppUpdates()
            }
        }
    }
}

function showAppOpenedAlert(){
    Alert.alert(
        'App Opened',
        'The notification of action type open_app_only received',
        [
            { text: 'OK', onPress: () => console.log('OK Pressed') },
        ],
        { cancelable: false },
    );
}

export function checkAppUpdates(latestAppData) {
    return dispatch => {
        if(latestAppData != null && latestAppData !== undefined){
            if ('latest_app_version' in latestAppData){
                let latestAppVersion = latestAppData["latest_app_version"];
                let latestAppURL = latestAppData["latest_app_url"];
                let latestAppFeatures = latestAppData["latest_app_features"];
                if(latestAppVersion!=null && latestAppVersion > DeviceInfo.getVersion()){
                    dispatch({type: "SET_LATEST_APP_UPDATES", payload: latestAppData});
                    showAppUpdatePopUp(latestAppData);
                }else{
                    dispatch({type: "SET_LATEST_APP_UPDATES", payload: null});
                }
            }
        }
    }
}


export function showAppUpdatePopUp(latestAppData) {
    let latestAppVersion = latestAppData["latest_app_version"];
    let latestAppURL = latestAppData["latest_app_url"];
    let latestAppFeatures = latestAppData["latest_app_features"];
        Alert.alert(
            "Update Required",
            "New Features: " + latestAppFeatures,
            [
                { text: 'Update', onPress: () =>
                    {
                        Linking.openURL(latestAppURL);
                        BackHandler.exitApp();
                    }
                },
            ],
            { cancelable: false },
        );
}