import RestService from "../services/RestService";
import apis from "../services/APIs";
import UtilityService from "../services/UtilityService";

export function lookupRetailer(contact) {
    return dispatch => {
        dispatch({type: "ADD_RETAILERS_LOADING", payload: true});
        dispatch({type: "SET_CONTACT", payload: contact});
        if (contact.length == 10) {
            RestService.postJSON(apis.getUrls().retailer.lookupContact, contact).then((response) => {
                dispatch({type: "SET_CONTACT_ALREADY_EXISTS", payload: response});
                if (response == true) {
                    dispatch({type: "SET_CONTACT_ALREADY_EXISTS", payload: true});
                    dispatch({type: "SET_IS_CONTACT_VIEW_ENABLED", payload: true});
                    dispatch({type: "SET_IS_FORM_VIEW_ENABLED", payload: false});
                } else {
                    dispatch({type: "SET_CONTACT_ALREADY_EXISTS", payload: false});
                    dispatch({type: "SET_IS_CONTACT_VIEW_ENABLED", payload: false});
                    dispatch({type: "SET_IS_FORM_VIEW_ENABLED", payload: true});
                    getTargetAttributes(dispatch);
                }
            }).catch((error) => {
                console.error(error);
            });
        }
        dispatch({type: "ADD_RETAILERS_LOADING", payload: false});
    }
}

export function getTargetAttributes(dispatch) {
    let data = {
        "retailer": null,
        "salesHandler": {
            "id": 123
        }
    };
    dispatch({type: "ADD_RETAILERS_LOADING", payload: true});
    RestService.postJSON(apis.getUrls().retailer.getTargetAttributes, data).then((response) => {
        if (response != null) {
            response.map(function (targetAttribute) {
                if (targetAttribute.optionId!=null && targetAttribute.optionId === "targetOptions") {
                    dispatch({type: "SET_TARGETS", payload: targetAttribute.options});
                }

                if (targetAttribute.optionId!=null && targetAttribute.optionId === "orderFrequencies") {
                    dispatch({type: "SET_ORDER_FREQUENCIES", payload: targetAttribute.options});
                }
            });

            dispatch({type: "ADD_RETAILERS_LOADING", payload: false});
        }
        dispatch({type: "ADD_RETAILERS_LOADING", payload: false});
    }).catch((error) => {
    });
}

export function validateAndRegister(retailer, props) {
    return dispatch => {
        dispatch({type: "ADD_RETAILERS_LOADING", payload: true});
        var isValid = true;
        if (retailer.firstName == null || retailer.firstName == 'undefined' || retailer.firstName == "") {
            dispatch({type: "SET_ERROR_FIRST_NAME_MISSING", payload: true});
            isValid = false;
        } else {
            dispatch({type: "SET_ERROR_FIRST_NAME_MISSING", payload: false});
            if (!/^[a-zA-Z ]+$/.test(retailer.firstName)) {
                dispatch({type: "SET_ERROR_FIRST_NAME_INVALID", payload: true});
                isValid = false;
            } else {
                dispatch({type: "SET_ERROR_FIRST_NAME_INVALID", payload: false});
            }
        }
        if (retailer.salesDiaryId == null || retailer.salesDiaryId == 'undefined' || retailer.salesDiaryId == "") {
            dispatch({type: "SET_ERROR_SALES_DIARY_ID_MISSING", payload: true});
            isValid = false;
        } else {
            dispatch({type: "SET_ERROR_SALES_DIARY_ID_MISSING", payload: false});
            dispatch({type: "SET_ERROR_SALES_DIARY_ID_INVALID", payload: false});
            if (!/^[0-9]+$/.test(retailer.salesDiaryId)) {
                dispatch({type: "SET_ERROR_SALES_DIARY_ID_INVALID", payload: true});
                isValid = false;
            } else {
                dispatch({type: "SET_ERROR_SALES_DIARY_ID_INVALID", payload: false});
            }
        }
        if (retailer.targetType == null || retailer.targetType  === '' || retailer.targetType === 'undefined') {
            dispatch({type: "SET_ERROR_TARGET_MISSING", payload: true});
            isValid = false;
        } else {
            dispatch({type: "SET_ERROR_TARGET_MISSING", payload: false});
        }
        if (retailer.orderFrequency  == null || retailer.orderFrequency  === 'undefined' || retailer.orderFrequency   === "0") {
            dispatch({type: "SET_ERROR_ORDER_FREQUENCY_MISSING", payload: true});
            isValid = false;
        } else {
            dispatch({type: "SET_ERROR_ORDER_FREQUENCY_MISSING", payload: false});
        }

        if(props.salespersonDetail!=null && props.salespersonDetail.designation !== "Territory Sales Executive"){

            if (retailer.contact == null || retailer.contact == 'undefined') {
                dispatch({type: "SET_ERROR_CONTACT_MISSING", payload: true});
                isValid = false;
            } else {
                dispatch({type: "SET_ERROR_CONTACT_MISSING", payload: false});
            }
            if (retailer.addressLineOne == null || retailer.addressLineOne == 'undefined') {
                dispatch({type: "SET_ERROR_ADDRESS_MISSING", payload: true});
                isValid = false;
            } else {
                dispatch({type: "SET_ERROR_ADDRESS_MISSING", payload: false});
            }
            if (retailer.city == null || retailer.city == 'undefined') {
                dispatch({type: "SET_ERROR_CITY_MISSING", payload: true});
                isValid = false;
            } else {
                dispatch({type: "SET_ERROR_CITY_MISSING", payload: false});
                if (!/^[a-zA-Z ]+$/.test(retailer.city)) {
                    dispatch({type: "SET_ERROR_CITY_INVALID", payload: true});
                    isValid = false;
                } else {
                    dispatch({type: "SET_ERROR_CITY_INVALID", payload: false});
                }
            }
            if (retailer.zipCode == null || retailer.zipCode == 'undefined') {
                /*dispatch({type: "SET_ERROR_ZIP_CODE_MISSING", payload: true});
                isValid = false;*/
            } else {
                dispatch({type: "SET_ERROR_ZIP_CODE_MISSING", payload: false});
                dispatch({type: "SET_ERROR_ZIP_CODE_INVALID", payload: false});
                if (!/^[0-9]+$/.test(retailer.zipCode)) {
                    dispatch({type: "SET_ERROR_ZIP_CODE_INVALID", payload: true});
                    isValid = false;
                } else {
                    dispatch({type: "SET_ERROR_ZIP_CODE_INVALID", payload: false});
                }
            }
            if (retailer.stateName == null || retailer.stateName == 'undefined') {
                dispatch({type: "SET_ERROR_STATE_NAME_MISSING", payload: true});
                isValid = false;
            } else {
                dispatch({type: "SET_ERROR_STATE_NAME_MISSING", payload: false});
            }
            if (retailer.email != null && retailer.email != 'undefined' && retailer.email!='') {
                var reg = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;
                if (reg.test(retailer.email) == false) {
                    dispatch({type: "SET_ERROR_EMAIL_INVALID", payload: true});
                    isValid = false;
                } else {
                    dispatch({type: "SET_ERROR_EMAIL_INVALID", payload: false});
                }
            }else{
                dispatch({type: "SET_ERROR_EMAIL_INVALID", payload: false});
            }
        }

        if (isValid) {
            let retailerObject = {
                "firstName": retailer.firstName,
                "middleName": "",
                "lastName": retailer.lastName,
                "countryCode": "+91",
                "contactNumber": retailer.contact,
                "emailId": (retailer.email !=null?retailer.email:"<EMAIL>"),
                "name": "",
                "landmark": "",
                "line1": (retailer.addressLineOne !=null?retailer.addressLineOne:"dummyAddressone"),
                "line2": (retailer.addressLineTwo !=null?retailer.addressLineTwo:"dummyAddressone"),
                "line3": "",
                "subLocality": "",
                "locality": "",
                "city": (retailer.city !=null?retailer.city:"dummyCity"),
                "state": (retailer.stateName !=null?retailer.stateName:"dummyState"),
                "country": "India",
                "zipCode": retailer.zipCode,
                "contact1": "",
                "addressType": "",
                "company": "",
                "latitude": "",
                "longitude": "",
                "salesHandlerId": props.salespersonDetail.id,
                "salesHandler": props.salespersonDetail.name,
                "onboardingDate": new Date(),
                "appOrdering": "Y",
                "type": "RETAILER",
                "salesDiaryId": retailer.salesDiaryId,
                "targetType": retailer.targetType,
                "targetValue": retailer.targetValue,
                "orderFrequency": retailer.orderFrequency
            };
            let salesHandler = {
                "id": props.salespersonDetail.id,
                "name": props.salespersonDetail.name
            };
            let data = {
                "retailer": retailerObject,
                "salesHandler": salesHandler
            };
            RestService.postJSON(apis.getUrls().salesHandler.addRetailer, data).then((response) => {
                if (response != null) {
                    if(response.hasOwnProperty('errorTitle') && response.errorTitle == "Duplicate sales diary id") {
                        dispatch({type: "SET_IS_DUPLICATE_SALES_DIARY_ID", payload: true});
                    }else{
                        dispatch({type: "SET_IS_DUPLICATE_SALES_DIARY_ID", payload: false});
                    }
                    if (response.hasOwnProperty('errorMessage')) {
                        dispatch({type: "SET_ERROR_REGISTRATION", payload: true});
                        dispatch({type: "SET_IS_FORM_VIEW_ENABLED", payload: false});
                        dispatch({type: "SET_IS_CONTACT_VIEW_ENABLED", payload: false});
                        dispatch(resetFormFields());
                        dispatch(resetErrorMessages());
                    }
                    if (response.hasOwnProperty('id')) {
                        dispatch({type: "SET_SUCCESS_REGISTRATION", payload: true});
                        dispatch({type: "SET_ERROR_REGISTRATION", payload: false});
                        dispatch({type: "SET_IS_FORM_VIEW_ENABLED", payload: false});
                        dispatch({type: "SET_IS_CONTACT_VIEW_ENABLED", payload: false});
                        dispatch(resetFormFields());
                        dispatch(resetErrorMessages());
                    }
                    dispatch({type: "ADD_RETAILERS_LOADING", payload: false});
                }
            }).catch((error) => {
                console.error(error);
            });
        } else {
            dispatch({type: "ADD_RETAILERS_LOADING", payload: false});
        }
    }
}

export function resetFormFields() {
    return dispatch => {
        dispatch({type: "SET_FIRST_NAME", payload: null});
        dispatch({type: "SET_LAST_NAME", payload: null});
        dispatch({type: "SET_SALES_DIARY_ID", payload: null});
        dispatch({type: "SET_EMAIL", payload: null});
        dispatch({type: "SET_SALES_DIARY_ID", payload: null});
        dispatch({type: "SET_CONTACT", payload: null});
        dispatch({type: "SET_ADDRESS_LINE_ONE", payload: null});
        dispatch({type: "SET_ADDRESS_LINE_TWO", payload: null});
        dispatch({type: "SET_CITY", payload: null});
        dispatch({type: "SET_ZIP_CODE", payload: null});
        dispatch({type: "SET_STATE_NAME", payload: null});
        dispatch({type: "SET_COUNTRY", payload: null});
        dispatch({type: "SET_AVATAR_SOURCE", payload: null});
        dispatch({type: "SET_TARGET", payload: null});
        dispatch({type: "SET_CUSTOM_TARGET", payload: null});
        dispatch({type: "SET_ORDER_FREQUENCY", payload: null})
    }
}

export function resetErrorMessages() {
    return dispatch => {
        dispatch({type: "SET_ERROR_FIRST_NAME_MISSING", payload: false});
        dispatch({type: "SET_ERROR_CONTACT_MISSING", payload: false});
        dispatch({type: "SET_ERROR_ADDRESS_MISSING", payload: false});
        dispatch({type: "SET_ERROR_CITY_MISSING", payload: false});
        dispatch({type: "SET_ERROR_ZIP_CODE_MISSING", payload: false});
        dispatch({type: "SET_ERROR_ZIP_CODE_INVALID", payload: false});
        dispatch({type: "SET_ERROR_STATE_NAME_MISSING", payload: false});
        dispatch({type: "SET_ERROR_FIRST_NAME_INVALID", payload: false});
        dispatch({type: "SET_ERROR_CITY_INVALID", payload: false});
        dispatch({type: "SET_ERROR_EMAIL_INVALID", payload: false});
        dispatch({type: "SET_ERROR_SALES_DIARY_ID_MISSING", payload: false});
        dispatch({type: "SET_ERROR_SALES_DIARY_ID_INVALID", payload: false});
        dispatch({type: "SET_IS_DUPLICATE_SALES_DIARY_ID", payload: false});
        dispatch({type: "SET_ERROR_TARGET_MISSING", payload: false});
        dispatch({type: "SET_ERROR_ORDER_FREQUENCY_MISSING", payload: false});
    }
}