import RestService from "../services/RestService";
import apis from "../services/APIs";

export function getAuditors() {
    return dispatch => {
        dispatch({type: "AUDIT_LOADING", payload: true});
        RestService.getJSON(apis.getUrls().salesHandler.getManagers).then((response) => {
            if (response != null && response.length > 0) {
                if (response.hasOwnProperty('errorMessage')) {
                    //TODO
                }
                if (response.hasOwnProperty('id')) {
                    //TODO
                }
                var auditors = {
                    "0":" Select auditor name. "
                };
                if (Array.isArray(response)) {
                    for(var i = 0; i < response.length; i++) {
                        var obj = response[i];
                        auditors[obj.id] = obj.name;
                    }
                    dispatch({type: "SET_AUDITORS", payload: auditors});
                    dispatch({type: "AUDIT_LOADING", payload: false});
                }
            }
        }).catch((error) => {console.error(error);});
        dispatch({type: "AUDIT_LOADING", payload: false});
    }
}

export function getSalesHandlerList(auditorId) {
    return dispatch => {
        dispatch({type: "AUDIT_LOADING", payload: true});
        RestService.getJSON(apis.getUrls().salesHandler.getSalesHandlers, [{auditorId : auditorId}]).then((response) => {
            if (response != null && response.length > 0) {
                let salesHandlers = {
                    "0": " Select sales handler name. "
                };
                if (Array.isArray(response)) {
                    for(let i = 0; i < response.length; i++) {
                        let obj = response[i];
                        salesHandlers[obj.id] = obj.name;
                    }
                    dispatch({type: "SET_AUDIT_SALES_HANDLERS", payload: salesHandlers});
                    dispatch({type: "AUDIT_LOADING", payload: false});
                }
            }
            dispatch({type: "AUDIT_LOADING", payload: false});
        }).catch((error) => {
            console.error(error);
        });
    }
}

export function getRetailers(salesHandlerId) {
    return dispatch => {
        dispatch({type: "AUDIT_LOADING", payload: true});
        RestService.getJSON(apis.getUrls().salesHandler.fetchRetailers,
            [{salesHandlerId: salesHandlerId}, {statusFilter: 'ACTIVE'}])
            .then((response) => {
                if (response != null && response.length > 0) {
                    //response.sort((a, b) => a.customerName.toLowerCase().localeCompare(b.customerName.toLowerCase()));
                    let retailers = {
                        "0": " Select the retailer name."
                    };
                    let salesDiaryIds = {
                        "0": ""
                    };

                    if (Array.isArray(response)) {

                        for (let i = 0; i < response.length; i++) {
                            let obj = response[i];
                            retailers[obj.customerId] = obj.customerName;
                            salesDiaryIds[obj.customerId] = obj.salesDiaryId;
                        }
                        dispatch({type: "SET_AUDIT_RETAILERS", payload: {...retailers}});
                        dispatch({type: "SET_RETAILERS_SALES_DIARY_IDS", payload: salesDiaryIds});
                        dispatch({type: "SET_NO_RETAILERS_FOUND", payload: false});
                    }
                }else{
                    dispatch({type: "SET_AUDIT_RETAILERS", payload: null});
                    dispatch({type: "SET_NO_RETAILERS_FOUND", payload: true});
                }
                dispatch({type: "AUDIT_LOADING", payload: false});
            }).catch((error) => {
            console.error(error);
        });
    }
}

export function getActivityTypes() {
    return dispatch => {
        dispatch({type: "AUDIT_LOADING", payload: true});
        RestService.getJSON(apis.getUrls().salesHandler.getActivityTypes)
            .then((response) => {
                if (response != null && response.length > 0) {
                    let activityTypes = {
                        "0": " Select Activity Type."
                    };
                    if (Array.isArray(response)) {
                        for (let i = 0; i < response.length; i++) {
                            activityTypes[response[i].code]  = response[i].name;
                        }
                        dispatch({type: "SET_ACTIVITY_TYPES", payload: {...activityTypes}});
                    }
                }else{
                    dispatch({type: "SET_ACTIVITY_TYPES", payload: null});
                }
                dispatch({type: "AUDIT_LOADING", payload: false});
            }).catch((error) => {
            console.error(error);
        });
    }
}

export function validateAndSaveAudit(auditData) {
    return dispatch => {
        dispatch({type: "AUDIT_LOADING", payload: true});
        var isValid = true;
        if(auditData.auditor==null || auditData.auditor==='0' || auditData.auditor === ""){
            dispatch({type: "SET_ERROR_AUDITOR_MISSING", payload: true});
            isValid = false;
        }else {
            dispatch({type: "SET_ERROR_AUDITOR_MISSING", payload: false});
        }
        if(auditData.salesHandler==null || auditData.salesHandler==='0' || auditData.salesHandler === ""){
            dispatch({type: "SET_ERROR_SALES_HANDLER_MISSING", payload: true});
            isValid = false;
        }else {
            dispatch({type: "SET_ERROR_SALES_HANDLER_MISSING", payload: false});
        }
        if(auditData.retailer==null || auditData.retailer==='0' || auditData.retailer === "" || auditData.retailerName==null || auditData.retailerName===""){
            dispatch({type: "SET_ERROR_RETAILER_MISSING", payload: true});
            isValid = false;
        }else {
            dispatch({type: "SET_ERROR_RETAILER_MISSING", payload: false});
        }
        if(auditData.salesDiaryId==null || auditData.salesDiaryId === "" || auditData.salesDiaryId === 0){
            dispatch({type: "SET_ERROR_SALES_DIARY_ID_MISSING", payload: true});
            isValid = false;
        }else {
            dispatch({type: "SET_ERROR_SALES_DIARY_ID_MISSING", payload: false});
        }
        if(auditData.activityType==null || auditData.activityType === "" || auditData.activityType === '0'){
            dispatch({type: "SET_ERROR_ACTIVITY_TYPE_MISSING", payload: true});
            isValid = false;
        }else {
            dispatch({type: "SET_ERROR_ACTIVITY_TYPE_MISSING", payload: false});
        }
        if(auditData.activityType!=null && (auditData.auditDate==null || auditData.auditDate === "")){
            dispatch({type: "SET_ERROR_AUDIT_DATE_MISSING", payload: true});
            isValid = false;
        }else {
            dispatch({type: "SET_ERROR_AUDIT_DATE_MISSING", payload: false});
        }
        /*if(auditData.activityType!=null && (auditData.auditTime==null || auditData.auditTime === "")){
            dispatch({type: "SET_ERROR_AUDIT_TIME_MISSING", payload: true});
            isValid = false;
        }else {
            dispatch({type: "SET_ERROR_AUDIT_TIME_MISSING", payload: false});
        }*/
        console.log('Activity Type::', auditData.activityType);
        console.log('auditData::', auditData);
        if(auditData.activityType!=null && auditData.activityType == 'SUPERSTAR_ENROLLMENT'){
            if(auditData.amountFromRetailer==null || auditData.amountFromRetailer === ""){
                dispatch({type: "SET_AMOUNT_OF_PAYMENT_SS_MISSING", payload: true});
                isValid = false;
            }else {
                dispatch({type: "SET_AMOUNT_OF_PAYMENT_SS_MISSING", payload: false});
            }
            if(auditData.kilogramsCommitted==null || auditData.kilogramsCommitted === ""){
                dispatch({type: "SET_KG_COMMITTED_SS_MISSING", payload: true});
                isValid = false;
            }else {
                dispatch({type: "SET_KG_COMMITTED_SS_MISSING", payload: false});
            }
            if(auditData.retailerGIFTAIM==null || auditData.retailerGIFTAIM === ""){
                dispatch({type: "SET_AIM_TO_WIN_MISSING", payload: true});
                isValid = false;
            }else {
                dispatch({type: "SET_AIM_TO_WIN_MISSING", payload: false});
            }
        }
        if (isValid) {
            let data = {
                "auditorId":parseInt(auditData.auditor, 10),
                "salesHandlerId":parseInt(auditData.salesHandler, 10),
                "salesHandlerName":auditData.salesHandlerName,
                "retailerId":parseInt(auditData.retailer, 10),
                "retailerName": auditData.retailerName,
                "salesDiaryId" : auditData.salesDiaryId,
                "activityType" : auditData.activityType,
                "promoter":auditData.isPromoter,
                "newRetailer":auditData.isNewRetailer,
                "activityCreatedDate": auditData.auditDate,
                "retailerAgreedForSS": auditData.retailerAgreedForSS,
                "retailerSSOrderQty": auditData.retailerSSOrderQty,
                "retailerSSPaymentStatus": auditData.retailerSSPaymentStatus,
                "amountFromRetailer": auditData.amountFromRetailer,
                "orderPlacedViaIVR": auditData.orderPlacedViaIVR,
                "kilogramsCommitted": auditData.kilogramsCommitted,
                "retailerGIFTAIM": auditData.retailerGIFTAIM,
                "signUPFormSigned": auditData.signUPFormSigned
            };
            RestService.postJSON(apis.getUrls().salesHandler.addActivityAudit, data).then((response) => {
                if(response != null){
                    if(response === true){
                        dispatch({type: "SET_ACTIVITY_AUDIT_SUCCESS", payload: true});
                        dispatch({type: "SET_ACTIVITY_AUDIT_ERROR", payload: false});
                        dispatch({type: "SET_AUDIT_FORM_VIEW_ENABLED", payload: false});
                    }else{
                        dispatch({type: "SET_ACTIVITY_AUDIT_SUCCESS", payload: false});
                        dispatch({type: "SET_ACTIVITY_AUDIT_ERROR", payload: true});
                        dispatch({type: "SET_AUDIT_FORM_VIEW_ENABLED", payload: false});
                    }
                }
                dispatch(resetAuditFormFields());
                dispatch({type: "AUDIT_LOADING", payload: false});
            }).catch((error) => {
                console.error(error);
            });
        } else {
            dispatch({type: "AUDIT_LOADING", payload: false});
        }
    }
}

export function setSalesHandlerList(salesHandlerId, props) {
    return dispatch => {
        if (salesHandlerId !== undefined && salesHandlerId !== 0) {
            props.setSalesHandler(salesHandlerId);
            if(props.salespersonDetail.designation === "Territory Sales Executive") {
                props.setSalesHandlerName(props.salespersonDetail.name);
            }else{
                props.setSalesHandlerName(props.salesHandlers[salesHandlerId]);
            }
            dispatch(getRetailers(salesHandlerId));
            props.setRetailer(null);
            props.setRetailerName(null);
        }
    }
}

export function resetAuditFormFields() {
    return dispatch => {
        dispatch({type: "SET_AUDIT_SALES_HANDLER", payload: null});
        dispatch({type: "SET_AUDIT_SALES_HANDLER_NAME", payload: null});
        dispatch({type: "SET_AUDIT_RETAILER", payload: null});
        dispatch({type: "SET_AUDIT_RETAILER_NAME", payload: null});
        dispatch({type: "SET_RETAILER_SALES_DIARY_ID", payload: null});
        dispatch({type: "SET_AUDIT_DATE", payload: null});
        dispatch({type: "SET_AUDIT_TIME", payload: null});
        dispatch({type: "SET_ACTIVITY_TYPE", payload: null});
        dispatch({type: "SET_ERROR_AUDITOR_MISSING", payload: false});
        dispatch({type: "SET_ERROR_SALES_HANDLER_MISSING", payload: false});
        dispatch({type: "SET_ERROR_RETAILER_MISSING", payload: false});
        dispatch({type: "SET_ERROR_AUDIT_DATE_MISSING", payload: false});
        dispatch({type: "SET_ERROR_AUDIT_TIME_MISSING", payload: false});
        dispatch({type: "SET_ERROR_SALES_DIARY_ID_MISSING", payload: false});
        dispatch({type: "SET_ERROR_ACTIVITY_TYPE_MISSING", payload: false});
        dispatch({type: "SET_AMOUNT_FROM_RETAILER", payload: null});
        dispatch({type: "SET_KILOGRAMS_COMMITTED", payload: null});
        dispatch({type: "SET_RETAILER_GIFT_AIM", payload: null});
    }
}

