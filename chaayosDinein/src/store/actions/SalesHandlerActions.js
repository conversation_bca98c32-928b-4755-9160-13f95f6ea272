import RestService from "./../services/RestService";
import UtilityService from "./../services/UtilityService";
import apis from "./../services/APIs";
import {ToastAndroid} from "react-native";

export function fetchHandlerSummary(salesHandlerId, noOfDays, withLoader, props) {
    return dispatch => {
        if (withLoader == true) {
            dispatch({type: "SALES_HANDLER_SUMMARY_LOADING", payload: true});
        }
        RestService.getJSON(apis.getUrls().salesHandler.summary,
            [{salesHandlerId: salesHandlerId}, {noOfDays: noOfDays}])
            .then((response) => {
                dispatch({type: "SALES_HANDLER_SUMMARY_LOADING", payload: false});
                if (response != null && Array.isArray(response) && response.length > 0) {
                let salesHandlerSummaries = [];
                let totalOrderCount = 0;
                let metadata = {};
                let handlerCount = 0;
                    for (let i in response) {
                        handlerCount ++;
                        response[i].orders = response[i].orders.slice();
                        let handlerInfo = {};
                        handlerInfo.handlerId = response[i].handlerId;
                        handlerInfo.handlerName = response[i].handlerName;
                        handlerInfo.handlerContact = response[i].handlerContact;
                        handlerInfo.handlerDesignationName = response[i].handlerDesignationName;
                        let salesHandlerSummary = {handlerInfo: handlerInfo, team: prepareTeamMap(response[i].handlerId, response[i])};
                        totalOrderCount = totalOrderCount + response[i].orderCount;
                        salesHandlerSummaries.push(salesHandlerSummary);
                    }
                    metadata.startDate = response[0].startDate;
                    metadata.endDate = response[0].endDate;
                    metadata.orderCount = totalOrderCount;
                    metadata.currentBusinessDate = response[0].currentBusinessDate;

                    dispatch({type: "SET_METADATA", payload: metadata});
                    dispatch({type: "SET_SALES_HANDLER_SUMMARIES", payload: salesHandlerSummaries});

                    if(response.length === 1){
                        //Pending orders to be shown only for area manager and sales handler
                        let pendingOrders = [];
                        response[0].orders.map(function (order) {
                            if(order.status === "INITIATED" || order.status === "SETTLED" || order.status === "CANCELLED_REQUESTED"){
                                pendingOrders.push({
                                    amount: order.amount,
                                    orderDate: order.orderDate,
                                    orderId: order.orderId,
                                    orderSummary: order.orderSummary,
                                    quantity: order.quantity,
                                    generatedOrderId: order.generatedOrderId,
                                    status: order.status,
                                    retailerName: order.customerName,
                                    retailerStatus: order.customerStatus,
                                    unitId: salesHandlerId
                                });
                            }
                        });
                        dispatch({type: "SET_PENDING_ORDERS", payload: pendingOrders});
                    }
                }
            })
            .catch((error) => {
                dispatch({type: "SALES_HANDLER_SUMMARY_LOADING", payload: false});
                console.error(error);
                ToastAndroid.show('Error loading order data!', ToastAndroid.LONG);
            });
    }
}

function prepareTeamMap(salesHandlerId, salesHandlerSummary) {
    let handlerMap = {};
    let selfData = {summary: UtilityService.getHandlerSummaryObj(), handlers: null};
    salesHandlerSummary.orders.map(function (item) {
        if (handlerMap[item.salesHandlerId] == null) {
            handlerMap[item.salesHandlerId] = {summary: UtilityService.getHandlerSummaryObj(), retailers: {}}
        }

        if (handlerMap[item.salesHandlerId].retailers[item.customerId] == null) {
            handlerMap[item.salesHandlerId].retailers[item.customerId] = {
                summary: UtilityService.getRetailerSummaryObj(),
                orders: []
            }
        }
        handlerMap[item.salesHandlerId].retailers[item.customerId].orders.push({
            generatedOrderId: item.generatedOrderId,
            orderId: item.orderId,
            orderDate: item.orderDate,
            amount: item.amount,
            orderSummary: item.orderSummary,
            status: item.status,
            unitId: item.salesHandlerId,
            retailerName: item.customerName,
            retailerContact: item.customerContact,
            retailerStatus: item.customerStatus,
            retailerOrderFrequency: item.customerOrderFrequency,
            retailerTargetType: item.customerTargetType,
            retailerTargetValue: item.customerTargetValue
        });
        handlerMap[item.salesHandlerId].retailers[item.customerId].summary.orderCount++;
        handlerMap[item.salesHandlerId].retailers[item.customerId].summary.id = item.customerId;
        handlerMap[item.salesHandlerId].retailers[item.customerId].summary.target = 50;
        handlerMap[item.salesHandlerId].retailers[item.customerId].summary.quantity += item.quantity;
        handlerMap[item.salesHandlerId].retailers[item.customerId].summary.retailerContact = item.customerContact;
        handlerMap[item.salesHandlerId].retailers[item.customerId].summary.retailerStatus = item.customerStatus;
        handlerMap[item.salesHandlerId].retailers[item.customerId].summary.retailerOrderFrequency = item.customerOrderFrequency;
        handlerMap[item.salesHandlerId].retailers[item.customerId].summary.retailerTargetType = item.customerTargetType;
        handlerMap[item.salesHandlerId].retailers[item.customerId].summary.retailerTargetValue = item.customerTargetValue;
        handlerMap[item.salesHandlerId].summary.orderCount++;
        handlerMap[item.salesHandlerId].summary.target = 100;
        handlerMap[item.salesHandlerId].summary.quantity += item.quantity;
        handlerMap[item.salesHandlerId].summary.currentBusinessDate = salesHandlerSummary.currentBusinessDate;
        selfData.summary.orderCount++;
        if (item.status == "INITIATED") {
            handlerMap[item.salesHandlerId].retailers[item.customerId].summary.newReceivedOrderCount++;
            handlerMap[item.salesHandlerId].summary.newReceivedOrderCount++;
            selfData.summary.newReceivedOrderCount++;
        }
        if (item.status == "SETTLED") {
            handlerMap[item.salesHandlerId].retailers[item.customerId].summary.pendingOrderCount++;
            handlerMap[item.salesHandlerId].summary.pendingOrderCount++;
            selfData.summary.pendingOrderCount++;
        }
        if (item.status == "CANCELLED_REQUESTED") {
            handlerMap[item.salesHandlerId].retailers[item.customerId].summary.cancelRequestOrderCount++;
            handlerMap[item.salesHandlerId].summary.cancelRequestOrderCount++;
            selfData.summary.cancelRequestOrderCount++;
        }
        if (item.status == "CANCELLED") {
            handlerMap[item.salesHandlerId].retailers[item.customerId].summary.cancelledOrderCount++;
            handlerMap[item.salesHandlerId].summary.cancelledOrderCount++;
            selfData.summary.cancelledOrderCount++;
        }
        if (item.status == "DELIVERED") {
            handlerMap[item.salesHandlerId].retailers[item.customerId].summary.deliveredOrderCount++;
            handlerMap[item.salesHandlerId].summary.deliveredOrderCount++;
            selfData.summary.deliveredOrderCount++;
        }
        if (handlerMap[item.salesHandlerId].retailers[item.customerId].summary.name == null) {
            handlerMap[item.salesHandlerId].retailers[item.customerId].summary.name = item.customerName;
        }
        if (handlerMap[item.salesHandlerId].retailers[item.customerId].summary.contact == null) {
            handlerMap[item.salesHandlerId].retailers[item.customerId].summary.contact = item.customerContact;
        }
        if (handlerMap[item.salesHandlerId].retailers[item.customerId].summary.status == null) {
            handlerMap[item.salesHandlerId].retailers[item.customerId].summary.status = item.customerStatus;
        }
        handlerMap[item.salesHandlerId].summary.id = item.salesHandlerId;
        handlerMap[item.salesHandlerId].summary.name = item.salesHandlerName;
        handlerMap[item.salesHandlerId].summary.contact = item.salesHandlerContact;
    });
    Object.values(handlerMap).map(function (handlerData) {
        handlerData.summary.retailerCount = Object.keys(handlerData.retailers).length;
        selfData.summary.retailerCount = selfData.summary.retailerCount + handlerData.summary.retailerCount;
        selfData.summary.target = selfData.summary.target + handlerData.summary.target;
        selfData.summary.quantity += handlerData.summary.quantity;
    });
    if(Object.keys(handlerMap).length === 1 && Object.values(handlerMap)[0].summary.id === salesHandlerId){
        selfData.summary.handlerCount = Object.keys(handlerMap).length -1;
    } else {
        selfData.summary.handlerCount = Object.keys(handlerMap).length;
    }
    selfData.handlers = handlerMap;
    return selfData;
}

export function showTeamDetail(handlerSummary, props) {
    return dispatch => {
        if(UtilityService.checkEmpty(handlerSummary.team.handlers)){
            ToastAndroid.show('No Salesmen Found!', ToastAndroid.LONG);
            return;
        }
        if (handlerSummary.team.summary.handlerCount > 0) {
            dispatch({type: "SET_HANDLERS_MAP", payload: handlerSummary.team.handlers});
            props.navigation.navigate("HandlerList");
        } else {
            dispatch({
                type: "SET_RETAILERS_MAP",
                payload: Object.values(handlerSummary.team.handlers)[0].retailers
            });
            props.navigation.navigate("RetailerList");
        }
    }
}

export function showRetailerList(handlerId, props) {
    return dispatch => {
        dispatch({type: "SET_RETAILERS_MAP", payload: props.handlersMap[handlerId].retailers});
        props.navigation.navigate("RetailerList");
    }
}

export function showOrdersList(retailerId, props) {
    return dispatch => {
        dispatch({type: "SET_ORDER_LIST", payload: props.retailersMap[retailerId].orders});
        props.navigation.navigate("OrderList");
    }
}

export function updateNoOfDays(count, props) {
    return dispatch => {
        var days = props.noOfDays + count;
        dispatch({type: "SET_NO_OF_DAYS", payload: days});
        dispatch(fetchHandlerSummary(props.salesHandler.id, days));
    }
}

export function resetNoOfDaysToToday(noOfDays, salesHandlerId) {
    return dispatch => {
        dispatch({type: "SET_NO_OF_DAYS", payload: noOfDays});
        dispatch(fetchHandlerSummary(salesHandlerId, noOfDays));
    }
}

export function setRefreshHandlerOrders(salesHandlerId, orderCount, noOfDays) {
    return dispatch => {
        RestService.getJSON(apis.getUrls().salesHandler.summaryCount,
            [{salesHandlerId: salesHandlerId}, {noOfDays: noOfDays}])
            .then((latestOrderCount) => {
                if (latestOrderCount != null && latestOrderCount !== 0) {
                    dispatch({type:'SET_ORDER_COUNT_DIFF', payload: latestOrderCount - orderCount});
                    if (latestOrderCount > orderCount) {
                        ToastAndroid.show('Refreshing orders.', ToastAndroid.LONG);
                        dispatch(fetchHandlerSummary(salesHandlerId, noOfDays));
                    }
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }
}

export function updateOrderStatus(orderId, unitId, status, props) {
    return dispatch => {
        let data = {
            "orderId":orderId,
            "approvedBy":unitId,
            "orderStatus":status,
            "unitId":unitId,
            "unitCategory":"CAFE",
            "reasonId":0,
            "wastageType":"no wastage",
            "channelPartner": 1
        };
        RestService.postJSON(apis.getUrls().order.updateStatus, data).then((response) => {
            if(response != null && response === true){
                dispatch(fetchHandlerSummary(props.salesHandler.id, props.noOfDays));
                props.navigation.navigate("Home");
                ToastAndroid.show('Order Status changed successfully', ToastAndroid.LONG);
            }
        }).catch((error) => {
            console.error(error);
        });
    }
}

export function putItemTOTop(ordersList, notifiedOrderID) {
    return dispatch => {
        if(ordersList!=null && notifiedOrderID!=null){
            const index = ordersList.map(e => e.generatedOrderId).indexOf(notifiedOrderID);
            if(index!=0){
                if(ordersList!=null && notifiedOrderID!=null){
                    var newOrdersList = ordersList;
                    newOrdersList.sort(function(x,y){ return x.generatedOrderId == notifiedOrderID ? -1 : y.generatedOrderId == notifiedOrderID ? 1 : 0; });
                    dispatch({type: "SET_ORDER_LIST", payload: newOrdersList});
                }
            }
        }
    }
}