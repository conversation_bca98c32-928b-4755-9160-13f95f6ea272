import apis from "../services/APIs";
import RestService from "../services/RestService";
import {ToastAndroid} from "react-native";


export function fetchRetailers(salesHandlerId, status, props){
    return dispatch => {
        if(status === 'ALL'){
            status = "";
        }
        dispatch({type: "MANAGE_RETAILERS_LOADING", payload: true});
        RestService.getJSON(apis.getUrls().salesHandler.fetchRetailers,
            [{salesHandlerId: salesHandlerId}, {statusFilter: status}])
            .then((response) => {
                if(response == null){
                    dispatch({type: "SET_TOTAL_RETAILERS", payload: 0});
                    dispatch({type: "SET_TOTAL_ACTIVE_RETAILERS", payload: 0});
                    dispatch({type: "SET_RETAILERS", payload: []});
                    return;
                }
                if (response.length > 0) {
                    //response.sort((a, b) => a.customerName.toLowerCase().localeCompare(b.customerName.toLowerCase()));
                    let totalRetailer = 0;
                    let totalActiveRetailer = 0;
                    for (var i in response) {
                        totalRetailer = totalRetailer + 1;
                        if (response[i].status === "ACTIVE") {
                            totalActiveRetailer = totalActiveRetailer + 1;
                        }
                    }
                    dispatch({type: "SET_TOTAL_RETAILERS", payload: totalRetailer});
                    dispatch({type: "SET_TOTAL_ACTIVE_RETAILERS", payload: totalActiveRetailer});
                    dispatch({type: "SET_RETAILERS", payload: response});
                } else {
                    dispatch({type: "SET_TOTAL_RETAILERS", payload: 0});
                    dispatch({type: "SET_TOTAL_ACTIVE_RETAILERS", payload: 0});
                    dispatch({type: "SET_RETAILERS", payload: []});
                }
                dispatch({type: "MANAGE_RETAILERS_LOADING", payload: false});
            })
            .catch((error) => {
                console.error(error);
            });
    }
}

export function fetchRetailer(retailerId, props){
    return dispatch => {
        dispatch({type: "MANAGE_RETAILERS_LOADING", payload: true});
        RestService.getJSON(apis.getUrls().salesHandler.fetchRetailer,
            [{retailerId: retailerId}])
            .then((response) => {
                if (response!=null) {
                    dispatch({type: "SET_RETAILER", payload: response});
                    props.navigation.navigate('UpdateRetailer');
                } else {
                    dispatch({type: "SET_RETAILER", payload: null});
                }
                dispatch({type: "MANAGE_RETAILERS_LOADING", payload: false});
            })
            .catch((error) => {
                console.error(error);
                dispatch({type: "MANAGE_RETAILERS_LOADING", payload: false});
            });
    }
}

export function updateSalesDiaryId(updatedSalesDiaryId, retailer) {
    return dispatch => {
        let isValid = true;
        if (updatedSalesDiaryId == null
            || updatedSalesDiaryId == 'undefined'
            || updatedSalesDiaryId == "") {
            dispatch({type: "SET_ERROR_SALES_DIARY_ID_MISSING", payload: true});
            isValid = false;
        } else {
            dispatch({type: "SET_ERROR_SALES_DIARY_ID_MISSING", payload: false});
            dispatch({type: "SET_ERROR_SALES_DIARY_ID_INVALID", payload: false});
            if (!/^[0-9]+$/.test(updatedSalesDiaryId)) {
                dispatch({type: "SET_ERROR_SALES_DIARY_ID_INVALID", payload: true});
                isValid = false;
            } else {
                dispatch({type: "SET_ERROR_SALES_DIARY_ID_INVALID", payload: false});
            }
        }
        if (isValid) {
            dispatch({type: "MANAGE_RETAILERS_LOADING", payload: true});
            let retailerJSON = {
                "id": parseInt(retailer.customerId),
                "salesDiaryId": parseInt(updatedSalesDiaryId)
            };
            let salesHandler = {
                "id": retailer.salesHandlerId
            };
            let data = {
                "retailer": retailerJSON,
                "salesHandler": salesHandler
            };
            RestService.postJSON(apis.getUrls().salesHandler.updateSalesDiaryId, data).then((response) => {
                if (response != null) {
                    if(response.hasOwnProperty('errorTitle') && response.errorTitle == "Duplicate sales diary id") {
                        dispatch({type: "SET_IS_DUPLICATE_SALES_DIARY_ID", payload: true});

                    }else{
                        dispatch({type: "SET_IS_DUPLICATE_SALES_DIARY_ID", payload: false});
                    }
                    if (response.hasOwnProperty('id')) {
                        dispatch({type: "SET_IS_ERROR_SALES_DIARY_ID_UPDATE", payload: false});
                        dispatch({type: "SET_RETAILER", payload: {...retailer, salesDiaryId:response.salesDiaryId}});
                    }
                }else{
                    dispatch({type: "SET_IS_ERROR_SALES_DIARY_ID_UPDATE", payload: true});
                }
                dispatch({type: "MANAGE_RETAILERS_LOADING", payload: false});
            }).catch((error) => {
                console.error(error);
                dispatch({type: "MANAGE_RETAILERS_LOADING", payload: false});
                dispatch({type: "SET_IS_ERROR_SALES_DIARY_ID_UPDATE", payload: true});
            });
        }
    }
}


export function updateStatus(retailer, status, props){
    return dispatch => {
        if(retailer==null ||
            retailer.customerId == null ||
            retailer.status == null ||
            retailer.salesHandlerId == null){
            return false;
        }
        let retailerJSON = {
            "id": parseInt(retailer.customerId),
            "status": status
        };
        let salesHandler = {
            "id": retailer.salesHandlerId
        };
        let data = {
            "retailer": retailerJSON,
            "salesHandler": salesHandler
        };
        RestService.postJSON(apis.getUrls().salesHandler.updateRetailerStatus, data).then((response) => {
            if(response != null && response == true){
                dispatch({type: "SET_RETAILER", payload: {...retailer, status:status}});
            }
        }).catch((error) => {
                console.error(error);
        });
    }
}

export function getUnitProductPrices(unitId, retailerId){
    return dispatch => {
        dispatch({type: "UPDATE_RETAILER_LOADER", payload: true});
        RestService.postJSON(apis.getUrls().neoCache.getUnitProductPrice, {}, [{unitId: unitId}, {retailerId:retailerId}]).then((response) => {
            if (response!=null && Array.isArray(response)) {
                var productPrices = [];
                for (var i = 0; i < response.length; i++) {
                    let product = {
                        "productId": response[i].productId,
                        "productName": response[i].productName,
                        "dimension": response[i].dimension,
                        "price": null,
                        "originalPrice": response[i].currentPrice
                    };
                    productPrices.push(product);
                }
                dispatch({type: "SET_PRODUCTS", payload: productPrices});
            }else{
                dispatch({type: "SET_PRODUCTS", payload: null});
            }
            dispatch({type: "UPDATE_RETAILER_LOADER", payload: false});
        }).catch((error) => {
            console.error(error);
        });
    }
}

export function updateProductPrices(unitId, retailerId, products) {
    return dispatch => {
        dispatch({type: "UPDATE_RETAILER_LOADER", payload: true});
        if(products != null){
            var data = {
                "updatedBy": {
                    "id": unitId
                },
                "retailerId": retailerId,
                "productPrices": products
            };
            RestService.postJSON(apis.getUrls().retailer.changeNegotiatedPrice, data).then((response) => {
                if (response != null && response == true) {
                    for (var i = 0; i < products.length; i++) {
                        products[i].originalPrice = products[i].price;
                        products[i].price = null;
                    }
                    dispatch({type: "SET_PRODUCTS", payload: [...products.slice()]});
                    dispatch({type: "SET_IS_PRICES_CHANGED", payload: false})
                    ToastAndroid.show('Product prices updated successfully', ToastAndroid.LONG);
                }else{
                    // console.log('failed to update the prices, setting it back to the original');
                }
                dispatch({type: "UPDATE_RETAILER_LOADER", payload: false});
            }).catch((error) => {
                // console.error(error);
            });
        }


    }
}

export function setPrice(products, index, price) {
    return dispatch => {
        let updatedProduct = {...products[index], price:price};
        products.splice(index, 1, updatedProduct);
        dispatch({type:"SET_PRODUCTS", payload:[...products]})
    }
}

export function getTargetAttributes(salesHandlerId) {
    return dispatch => {
        let data = {
            "retailer": null,
            "salesHandler": {
                "id": salesHandlerId
            }
        };
        dispatch({type: "UPDATE_RETAILER_LOADER", payload: true});
        RestService.postJSON(apis.getUrls().retailer.getTargetAttributes, data).then((response) => {
            if (response != null) {
                response.map(function (targetAttribute) {
                    if (targetAttribute.optionId!=null && targetAttribute.optionId === "targetOptions") {
                        dispatch({type: "SET_TARGETS", payload: targetAttribute.options});
                    }

                    if (targetAttribute.optionId!=null && targetAttribute.optionId === "orderFrequencies") {
                        dispatch({type: "SET_ORDER_FREQUENCIES", payload: targetAttribute.options});
                    }
                });

                dispatch({type: "UPDATE_RETAILER_LOADER", payload: false});
            }
            dispatch({type: "UPDATE_RETAILER_LOADER", payload: false});
        }).catch((error) => {
        });
    }
}

export function updateTarget(retailer, targetType, targetValue, props){
    return dispatch => {
        if(retailer==null ||
            retailer.customerId == null ||
            targetType == null ||
            targetValue == null ||
            retailer.salesHandlerId == null){
            return false;
        }
        let retailerJSON = {
            "id": parseInt(retailer.customerId),
            "targetType": targetType,
            "targetValue": targetValue
        };
        let salesHandler = {
            "id": retailer.salesHandlerId
        };
        let data = {
            "retailer": retailerJSON,
            "salesHandler": salesHandler
        };
        RestService.postJSON(apis.getUrls().salesHandler.updateRetailerTarget, data).then((response) => {
            if(response != null && response == true){
                dispatch({type: "SET_RETAILER",
                          payload: {...retailer,
                                    targetType :targetType,
                                    targetValue :targetValue
                                    }
                });
                dispatch({type: "SET_UPDATED_TARGET", payload: null});
            }
        }).catch((error) => {
            console.error(error);
        });
    }
}

export function updateOrderFrequency(retailer, orderFrequency, props){
    return dispatch => {
        if(retailer==null ||
            retailer.customerId == null ||
            orderFrequency == null ||
            retailer.salesHandlerId == null){
            return false;
        }
        let retailerJSON = {
            "id": parseInt(retailer.customerId),
            "orderFrequency": orderFrequency,
        };
        let salesHandler = {
            "id": retailer.salesHandlerId
        };
        let data = {
            "retailer": retailerJSON,
            "salesHandler": salesHandler
        };
        RestService.postJSON(apis.getUrls().salesHandler.updateRetailerOrderFrequency, data).then((response) => {
            if(response != null && response == true){
                dispatch({type: "SET_RETAILER",
                    payload: {...retailer,
                        orderFrequency :orderFrequency
                    }
                });
                dispatch({type: "SET_UPDATED_ORDER_FREQUENCY", payload: null});
            }
        }).catch((error) => {
            console.error(error);
        });
    }
}

export function lookupRetailerContact(contact) {
    return dispatch => {
        dispatch({type: "SET_NEW_CONTACT", payload: null});
        dispatch({type: "UPDATE_RETAILER_LOADER", payload: true});
        if (contact.length == 10) {
            RestService.postJSON(apis.getUrls().retailer.lookupContact, contact).then((response) => {
                if (response === true) {
                    dispatch({type: "SET_RU_CONTACT_ALREADY_EXISTS", payload: true});
                } else {
                    dispatch({type: "SET_NEW_CONTACT", payload: contact});
                    dispatch({type: "SET_RU_CONTACT_ALREADY_EXISTS", payload: false});
                }
            }).catch((error) => {
                console.error(error);
            });
        }
        dispatch({type: "UPDATE_RETAILER_LOADER", payload: false});
    }
}

export function addRetailerNewContact(newContact, retailerId, contactType, salesHandlerId, props) {
    return dispatch => {
        dispatch({type: "UPDATE_RETAILER_LOADER", payload: true});
        if(newContact == null || retailerId == null ||
            contactType == null || salesHandlerId == null){
            return false;
        }
        let retailerJSON = {
            "id": retailerId,
            "contactNumber": newContact,
            "contactType" : contactType,
            "countryCode" : "+91"
        };
        let salesHandler = {
            "id": salesHandlerId
        };
        let data = {
            "retailer": retailerJSON,
            "salesHandler": salesHandler
        };
        //console.log('add retailer contact data:: ',data);
        RestService.postJSON(apis.getUrls().salesHandler.addRetailerNewContact, data).then((response) => {
            if (response === true) {
                dispatch({type: "SET_NEW_CONTACT_ADDED_SUCCESS", payload: true});
                let contactDetails = [];
                contactDetails = props.retailer.contactDetails;
                let newContact = {
                    "contactNumber": data.retailer.contactNumber,
                    "contactType": data.retailer.contactType,
                    "countryCode": "+91",
                    "customerId": data.retailer.id,
                    "status": "ACTIVE"
                };
                contactDetails.push(newContact);
                var retailer = props.retailer;
                retailer.contactDetails = [];
                dispatch({type: "SET_RETAILER",
                    payload: {...retailer,
                        contactDetails :contactDetails,
                    }
                });
                dispatch({type: "SET_NEW_CONTACT", payload: null});
            } else {
                dispatch({type: "SET_NEW_CONTACT_ADDED_SUCCESS", payload: false});
            }
        }).catch((error) => {
            dispatch({type: "SET_NEW_CONTACT_ADDED_SUCCESS", payload: false});
            console.error(error);
        });
        dispatch({type: "UPDATE_RETAILER_LOADER", payload: false});
    }
}

export function makeContactPrimary(contact, retailerId, contactType, salesHandlerId, props) {
    return dispatch => {
        dispatch({type: "UPDATE_RETAILER_LOADER", payload: true});
        if(contact == null || retailerId == null ||
            contactType == null || salesHandlerId == null){
            return false;
        }
        let retailerJSON = {
            "id": retailerId,
            "contactNumber": contact,
            "contactType" : contactType,
            "countryCode" : "+91"
        };
        let salesHandler = {
            "id": salesHandlerId
        };
        let data = {
            "retailer": retailerJSON,
            "salesHandler": salesHandler
        };
        //console.log('add retailer contact data:: ',data);
        RestService.postJSON(apis.getUrls().salesHandler.changeRetailerContactType, data).then((response) => {
            console.log('change retailer contact type:: ',response);
            if (response != null) {
                var retailer = props.retailer;
                retailer.contactDetails = [];
                dispatch({type: "SET_RETAILER",
                    payload: {...retailer,
                        customerContact: contact,
                        contactDetails :response
                    }
                });
            }
        }).catch((error) => {
            console.error(error);
        });
        dispatch({type: "UPDATE_RETAILER_LOADER", payload: false});
    }
}