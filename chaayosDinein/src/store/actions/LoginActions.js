import apis from "./../services/APIs";
import RestService from "./../services/RestService";
import StorageService from "./../services/StorageService";
import UtilityService from "./../services/UtilityService";
import {Alert} from "react-native"
// import i18n from './../../i18n/i18n';
// import en from './../../i18n/translations/en.json';
// import hi from './../../i18n/translations/hi.json';
import NavigationService from "./../services/NavigationService";
//import firebase from "react-native-firebase";

export function handleRouting(props) {
    return dispatch => {
        StorageService.getLocale().then(locale => {
            if (locale == null) {
                locale = 'en';
            }
            dispatch(setLocale(props, locale));
        });

        StorageService.getAuthDetail().then((authDetail) => {
            authDetail = authDetail != null ? JSON.parse(authDetail) : null;
            UtilityService.setAuthDetail(authDetail);
            if (authDetail != null) {
                dispatch({type: "SET_AUTH_DETAIL", payload: authDetail});
                if (authDetail.sessionKey != null && authDetail.authKey != null) {
                    StorageService.getSalespersonDetail().then(salespersonDetail => {
                        salespersonDetail = salespersonDetail != null ? JSON.parse(salespersonDetail) : null;
                        if (salespersonDetail != null) {
                            dispatch({type: "SET_SALESPERSON_DETAIL", payload: salespersonDetail});
                            subscribeToTopics(salespersonDetail.id);
							props.navigation.navigate("Drawer");
                        }
                    });
                } else {
                    props.navigation.navigate("Login");
                }
            } else {
                props.navigation.navigate("Login");
            }
        });
    }
}

export function loginSalesPerson(userId, password, props) {
    return dispatch => {
        dispatch({
            type: "SET_ERROR_SALES_LOGIN",
            payload: {errorSalesLogin: false, errorSalesLoginMessage: null}
        });
        if(isNaN(parseInt(userId)) || userId.toString().trim().length != 7){
            Alert.alert(
                'Invalid User Id',
                'Please enter valid 7 digit user id',
                [
                    {text: 'OK', onPress: () => {}},
                ],
                { cancelable: false }
            );
        }else if((!/^[0-9]+$/.test(userId))){
            Alert.alert(
                'Invalid User Id',
                'You can not enter special character in user id',
                [
                    {text: 'OK', onPress: () => {}},
                ],
                { cancelable: false }
            );
        }
        else if(password == null || password.length == 0) {
            Alert.alert(
                'Password not set',
                'Please provide password in order to sign in.',
                [
                    {text: 'OK', onPress: () => {}},
                ],
                { cancelable: false }
            );
        } else {
            dispatch({type: "SET_USER_ID", payload: userId});
            dispatch({type: "SET_PASSWORD", payload: password});
            dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: true, loadingMessage: "Signing in. Please wait..."}});
            if (userId!="") {
                let data = {
                    "userId": userId,
                    "unitId": 1,
                    "password": password,
                    "terminalId": 1,
                    "application": "SALES_APP",
                    "screenType": "POS"
                };
                dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: true, loadingMessage: "Signing in. Please wait..."}});
                RestService.postJSON(apis.getUrls().salePersonLogin.login, data).then((response) => {
                    if (response != null && response.user != null) {
                        let salespersonDetail = {
                            id: response.user.id,
                            departmentName : response.user.departmentName,
                            designation : response.user.designation,
                            reportingManagerId : response.user.reportingManagerId,
                            contact: response.user.contactNumber,
                            name: response.user.name,
                            email: response.user.emailId
                        };
                        dispatch({type: "SET_SALESPERSON_DETAIL", payload: salespersonDetail});
                        StorageService.setSalespersonDetail(salespersonDetail);
                        let authDetail = {
                            sessionKey: response.sessionKeyId,
                            authKey: response.jwtToken
                        };
                        dispatch({type: "SET_AUTH_DETAIL", payload: authDetail});
                        dispatch({type: "SET_SALES_HANDLER_SUMMARY", payload: null});
                        dispatch({type: "SET_NO_OF_DAYS", payload: 0});
                        StorageService.setAuthDetail(authDetail);
                        UtilityService.setAuthDetail(authDetail);
                        subscribeToTopics(response.user.id);
                        props.navigation.navigate("Drawer");
                    } else {
                        dispatch({type: "SET_USER_ID", payload: null});
                        dispatch({type: "SET_PASSWORD", payload: null});
                        dispatch({
                            type: "SET_ERROR_SALES_LOGIN",
                            payload: {errorSalesLogin: true, errorSalesLoginMessage: response.errorMsg}
                        });
                    }
                    dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                }).catch((error) => {
                    dispatch({type: "SET_USER_ID", payload: null});
                    dispatch({type: "SET_PASSWORD", payload: null});
                    dispatch({
                        type: "SET_ERROR_SALES_LOGIN",
                        payload: {errorSalesLogin: true, errorSalesLoginMessage: response.errorMsg}
                    });
                    dispatch({type: "SET_SHOW_LOADER", payload: {showLoader: false, loadingMessage: null}});
                    dispatch({type: "SET_SHOW_SALES_LOGIN_FORM", payload: true});
                });
            }
        }
    }
}

function subscribeToTopics(userId){
    /*let topics = ['/topics/app_updates', '/topics/'+userId+'_order'];
    topics.forEach(function(topic, index) {
        firebase.messaging().subscribeToTopic(topic);
    });*/
}

function unsubscribeFromTopic(userId){
    /*let topic = '/topics/'+userId+'_order';
    firebase.messaging().unsubscribeFromTopic(topic);*/
}

export function logoutCustomer(props) {
    return dispatch => {
        StorageService.getAuthDetail().then((authDetail) => {
            authDetail = (authDetail != null) ? JSON.parse(authDetail) : null;
            authDetail.sessionKey = null;
            StorageService.setAuthDetail(authDetail);
            StorageService.getSalespersonDetail().then(salespersonDetail => {
                salespersonDetail = salespersonDetail != null ? JSON.parse(salespersonDetail) : null;
                if (salespersonDetail != null) {
                    unsubscribeFromTopic(salespersonDetail.id);
                }
            });
            StorageService.removeSalespersonDetail();
            dispatch({type: "SET_AUTH_DETAIL", payload: authDetail});
            props.navigation.navigate("Login");
            dispatch({type: "SET_SALESPERSON_DETAIL", payload: null});
        });
    }
}

export function resetLogin() {
    return dispatch => {
        dispatch({type: "SET_SHOW_SALES_LOGIN_FORM", payload: true});
        dispatch({type: "SET_SHOW_LOADER", payload: false});
        dispatch({type: "SET_USER_ID", payload: null});
        dispatch({type: "SET_PASSWORD", payload: null});
        dispatch({type: "SET_ERROR_SALES_LOGIN", payload: {error: false, message: null}});
    }
}

export function setLocale(props, locale){
    /*return dispatch => {
        if(locale == null) {
            locale = props.locale;
        }
        i18n.locale = locale;
        i18n.fallbacks = true;
        i18n.translations = {en, hi};
        StorageService.setLocale(locale);
        dispatch({type: "SET_LOCALE", payload: locale});
    }*/
}

export function setIsConnected(connected) {
    return dispatch => {
        UtilityService.setConnected(connected);
        dispatch({type:'SET_INTERNET_CONNECTED', payload:connected});
        if(connected == false) {
                NavigationService.navigate('ConnectionError');
        }
    }
}