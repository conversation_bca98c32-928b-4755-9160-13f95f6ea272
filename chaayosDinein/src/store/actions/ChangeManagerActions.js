import RestService from "../services/RestService";
import apis from "../services/APIs";
import UtilityService from "../services/UtilityService";
import {ToastAndroid} from "react-native"

export function getManagerList(auditorId) {
    return dispatch => {
        dispatch({type: "CM_LOADING", payload: true});
        RestService.getJSON(apis.getUrls().salesHandler.getManagers).then((response) => {
            if (response != null && response.length > 0) {
                let managers = {
                    "0": " Select "
                };
                if (Array.isArray(response)) {
                    for(let i = 0; i < response.length; i++) {
                        let obj = response[i];
                        managers[obj.id] = obj.name;
                    }
                    dispatch({type: "SET_CM_MANAGERS", payload: managers});
                    dispatch({type: "CM_LOADING", payload: false});
                }
            }
            dispatch({type: "CM_LOADING", payload: false});
        }).catch((error) => {
            console.error(error);
            dispatch({type: "CM_LOADING", payload: false});
            ToastAndroid.show('Server Error Occurred!', ToastAndroid.LONG);
        });
    }
}

export function setManagerList(managerId, props) {
    return dispatch => {
        if (managerId !== undefined && managerId !== 0) {
            props.setManager(managerId);
            props.setManagerName(props.managers[managerId]);
            props.setSelectedSalesHandlers(null);
            dispatch(getSalesHandlers(managerId));
        }
    }
}

export function getSalesHandlers(managerId) {
    return dispatch => {
        dispatch({type: "CM_LOADING", payload: true});
        RestService.getJSON(apis.getUrls().salesHandler.getSalesHandlers,
            [{auditorId : managerId}])
            .then((response) => {
                console.log("SalesHandler's response:>>  ", response);
                if (response != null && response.length > 0) {
                    let salesHandlers = [
                    ];
                    if (Array.isArray(response)) {
                        for (let i = 0; i < response.length; i++) {
                            let obj = response[i];
                            let salesHandler = {"label" : obj.name, "value" : obj.id};
                            salesHandlers.push(salesHandler);
                        }
                        dispatch({type: "SET_CM_SALES_HANDLERS", payload: [...salesHandlers]});
                        dispatch({type: "SET_NO_SALES_HANDLERS_FOUND", payload: false});
                    }
                }else{
                    console.log("No salesHandlers found");
                    dispatch({type: "SET_CM_SALES_HANDLERS", payload: null});
                    dispatch({type: "SET_NO_SALES_HANDLERS_FOUND", payload: true});
                }
                dispatch({type: "CM_LOADING", payload: false});
            }).catch((error) => {
            console.error(error);
            dispatch({type: "CM_LOADING", payload: false});
            ToastAndroid.show('Server Error Occurred!', ToastAndroid.LONG);
        });
    }
}

export function changeManager(props) {
    return dispatch => {
        dispatch({type: "CM_LOADING", payload: true});
        if (!UtilityService.checkEmpty(props.selectedSalesHandlers) && props.newManager != null) {
            let salesHandlers = [];
            for(let i = 0; i < props.selectedSalesHandlers.length; i++) {
                let salesHandler = {"id": props.selectedSalesHandlers[i].value, "name" : props.selectedSalesHandlers[i].label};
                salesHandlers.push(salesHandler);
            }
            let data = {
                "manager" : {"id": props.newManager , "name": props.managers[props.newManager]},
                "updatedBy" : {"id": props.salespersonDetail.id , "name": props.salespersonDetail.name},
                "salesHandlers" : salesHandlers
            };
            RestService.postJSON(apis.getUrls().salesHandler.changeManager, data).then((response) => {
                if(response != null && response === true){
                    dispatch({type: "SET_CM_FORM_VIEW_ENABLED", payload: false});
                    dispatch({type: "SET_ERROR_CHANGE_MANAGER", payload: false});
                    dispatch({type: "SET_SUCCESS_CHANGE_MANAGER", payload: true});
                    console.log("Manager Changed Successfully!!");
                    dispatch({type: "CM_LOADING", payload: false});
                }else{
                    dispatch({type: "SET_CM_FORM_VIEW_ENABLED", payload: false});
                    dispatch({type: "SET_ERROR_CHANGE_MANAGER", payload: true});
                    dispatch({type: "SET_SUCCESS_CHANGE_MANAGER", payload: false});
                    dispatch({type: "CM_LOADING", payload: false});
                }
            }).catch((error) => {
                console.error(error);
                dispatch({type: "CM_LOADING", payload: false});
                ToastAndroid.show('Server Error Occurred!', ToastAndroid.LONG);
            });
        }
    }
}