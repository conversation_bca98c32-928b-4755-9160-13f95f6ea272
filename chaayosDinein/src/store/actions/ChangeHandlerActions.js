import RestService from "../services/RestService";
import apis from "../services/APIs";
import UtilityService from "../services/UtilityService";
import {ToastAndroid} from "react-native"

export function getSalesHandlerList(auditorId) {
    return dispatch => {
        dispatch({type: "CH_LOADING", payload: true});
        RestService.getJSON(apis.getUrls().salesHandler.getSalesHandlers, [{auditorId : auditorId}]).then((response) => {
            if (response != null && response.length > 0) {
                let salesHandlers = {
                    "0": " Select "
                };
                if (Array.isArray(response)) {
                    for(let i = 0; i < response.length; i++) {
                        let obj = response[i];
                        salesHandlers[obj.id] = obj.name;
                    }
                    dispatch({type: "SET_CH_SALES_HANDLERS", payload: salesHandlers});
                    dispatch({type: "CH_LOADING", payload: false});
                }
            }
            dispatch({type: "CH_LOADING", payload: false});
        }).catch((error) => {
            console.error(error);
            dispatch({type: "CH_LOADING", payload: false});
            ToastAndroid.show('Server Error Occurred!', ToastAndroid.LONG);
        });
    }
}

export function setSalesHandlerList(salesHandlerId, props) {
    return dispatch => {
        if (salesHandlerId !== undefined && salesHandlerId !== 0) {
            props.setSalesHandler(salesHandlerId);
            props.setSalesHandlerName(props.salesHandlers[salesHandlerId]);
            props.setSelectedRetailers(null);
            dispatch(getRetailers(salesHandlerId));
        }
    }
}

export function getRetailers(salesHandlerId) {
    return dispatch => {
        dispatch({type: "CH_LOADING", payload: true});
        RestService.getJSON(apis.getUrls().salesHandler.fetchRetailers,
            [{salesHandlerId: salesHandlerId}, {statusFilter: 'ACTIVE'}])
            .then((response) => {
                console.log("Retailer's response:>>  ", response);
                if (response != null && response.length > 0) {
                    //response.sort((a, b) => a.customerName.toLowerCase().localeCompare(b.customerName.toLowerCase()));
                    let retailers = [
                    ];
                    if (Array.isArray(response)) {
                        for (let i = 0; i < response.length; i++) {
                            let obj = response[i];
                            let retailer = {"label" : obj.customerName, "value" : obj.customerId};
                            retailers.push(retailer);
                        }
                        dispatch({type: "SET_CH_RETAILERS", payload: [...retailers]});
                        dispatch({type: "SET_NO_RETAILERS_FOUND", payload: false});
                    }
                }else{
                    console.log("No retailers found");
                    dispatch({type: "SET_CH_RETAILERS", payload: null});
                    dispatch({type: "SET_NO_RETAILERS_FOUND", payload: true});
                }
                dispatch({type: "CH_LOADING", payload: false});
            }).catch((error) => {
            console.error(error);
            dispatch({type: "CH_LOADING", payload: false});
            ToastAndroid.show('Server Error Occurred!', ToastAndroid.LONG);
        });
    }
}

export function changeHandler(props) {
    return dispatch => {
        dispatch({type: "CH_LOADING", payload: true});
        if (!UtilityService.checkEmpty(props.selectedRetailers) && props.newSalesHandler != null) {
            let retailers = [];
            for(let i = 0; i < props.selectedRetailers.length; i++) {
                let retailer = {"id": props.selectedRetailers[i].value, "name" : props.selectedRetailers[i].label};
                retailers.push(retailer);
            }
            let data = {
                "salesHandler" : {"id": props.newSalesHandler , "name": props.salesHandlers[props.newSalesHandler]},
                "updatedBy" : {"id": props.salespersonDetail.id , "name": props.salespersonDetail.name},
                "retailers" : retailers
            };
            RestService.postJSON(apis.getUrls().salesHandler.changeHandler, data).then((response) => {
                if(response != null && response === true){
                    dispatch({type: "SET_CH_FORM_VIEW_ENABLED", payload: false});
                    dispatch({type: "SET_ERROR_CHANGE_HANDLER", payload: false});
                    dispatch({type: "SET_SUCCESS_CHANGE_HANDLER", payload: true});
                    console.log("Handler Changed Successfully!!");
                    dispatch({type: "CH_LOADING", payload: false});
                }else{
                    dispatch({type: "SET_CH_FORM_VIEW_ENABLED", payload: false});
                    dispatch({type: "SET_ERROR_CHANGE_HANDLER", payload: true});
                    dispatch({type: "SET_SUCCESS_CHANGE_HANDLER", payload: false});
                    dispatch({type: "CH_LOADING", payload: false});
                }
            }).catch((error) => {
                console.error(error);
                dispatch({type: "CH_LOADING", payload: false});
                ToastAndroid.show('Server Error Occurred!', ToastAndroid.LONG);
            });
        }
    }
}