import React, {Component} from 'react';
import {connect} from 'react-redux';
import {
    StyleSheet,
    Text,
    View,
    ScrollView,
    Image,
    TextInput,
    TouchableOpacity,
    BackHandler,
    PixelRatio,
    Alert,
    KeyboardAvoidingView,
    ActivityIndicator,
    Picker
} from 'react-native';
import PhotoUpload from 'react-native-photo-upload';
import HeaderView from "./HeaderView";
import * as SalesHandlerRegistrationActions from "./../store/actions/SalesHandlerRegistrationActions";
import MaterialIcon from "react-native-vector-icons/MaterialIcons";
import AntDesignIcon from "react-native-vector-icons/AntDesign";
import EntypoIcon from "react-native-vector-icons/Entypo";
const default_avatar = require("./../assets/img/profileAvatar.png");
import NavigationService from "./../store/services/NavigationService";
var Dimensions = require('Dimensions');
var { width, height } = Dimensions.get('window');


class SalesHandlerRegistrationScreen extends Component {

    componentWillMount() {
        this.props.navigation.addListener(
            'didFocus',
            payload => {
                console.debug('didFocus', payload);
                this.props.setEmployeeId(null);
                this.props.setManager(null);
                this.props.setFirstName(null);
                this.props.setLastName(null);
                this.props.setEmail(null);
                this.props.setContact(null);
                this.props.setAddressLineOne(null);
                this.props.setAddressLineTwo(null);
                this.props.setCity(null);
                this.props.setZipCode(null);
                this.props.setStateName(null);
                this.props.setCountry(null);

                this.props.setEmployeeId(null);
                this.props.setIsValidEmployeeId(true);
                this.props.setErrorSHRegistration(false);
                this.props.setSuccessSHRegistration(false);
                this.props.setEmployeeIDAlreadyExists(false);
                this.props.setIsInputIDViewEnabled(true);
                this.props.setIsSHFormViewEnabled(false);
                this.props.setIsDuplicateNameError(false);
                this.props.setIsDuplicateContactError(false);
            }
        );
    }

    componentWillUnmount() {
        if (this.props.navigation && this.props.navigation.removeListener) {
            this.props.navigation.removeListener('didFocus');
            this.props.setShowLoader(false);
        }
    }

    checkIfValidEmployeeID(input) {
        this.props.setIsValidEmployeeId(true);
        this.props.setEmployeeIDAlreadyExists(false);
        if (!(input.match(/^[0-9]*$/))) {
            this.props.setIsValidEmployeeId(false);
            this.props.setIsInputIDViewEnabled(true);
            this.props.setIsSHFormViewEnabled(false);
        }else{
            if(input.length == 7){
                this.props.lookupSalesHandler(input);
            }
        }
        this.props.setEmployeeId(input);
    }

    resetFields(){
        this.props.setEmployeeId(null);
        this.props.setManager(null);
        this.props.setFirstName(null);
        this.props.setLastName(null);
        this.props.setEmail(null);
        this.props.setContact(null);
        this.props.setAddressLineOne(null);
        this.props.setAddressLineTwo(null);
        this.props.setCity(null);
        this.props.setZipCode(null);
        this.props.setStateName(null);
        this.props.setCountry(null);
    }

    alertBack(){
        Alert.alert(
            'Go Back',
            'Are you sure you want to discard your changes?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        NavigationService.navigate('Home');
                    } },
            ],
            { cancelable: false }
        )
    }

    validateAndSetContact(contact){
        this.props.setIsContactInvalid(false);
        this.props.setIsContactMissing(false);
        if (!(contact.match(/^[0-9]*$/))) {
            this.props.setIsContactInvalid(true);
        }
        if(contact.length == 0){
            this.props.setIsContactMissing(true);
        }
        if(contact.length != 10){
            this.props.setIsContactInvalid(true);
        }
        if(contact.match(/^[0-5]\d{9}$/)){
            this.props.setIsContactInvalid(true);
        }
        this.props.setContact(contact);
    }



    render() {
        return (
            <View style={styles.container}>
                {this.props.showLoader ? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#f0af3b"/>
                        <Text style={{color: '#f0af3b'}}>Please wait ...</Text>
                    </View>
                ) : null}
                <HeaderView menuIcon={false}
                            title={'Add Salesman'}
                            backHandler={ (this.props.employeeId != null && this.props.employeeId != "") ? this.alertBack : null }
                            {...this.props} headerColor={"#4a4e4d"}/>
                <KeyboardAvoidingView style={styles.keyboardContainer}>
                    <ScrollView style={styles.container}>
                        {/*Input EMP ID view Starts*/}
                        { this.props.isInputIDViewEnabled ? (
                            <View style={styles.enterEmployeeIdContainer}>
                                <AntDesignIcon name='idcard' style={styles.icon}/>
                                <Text style={styles.alertTextStyle}> Please enter the employee id below: </Text>
                                <View style={{
                                    marginLeft: 50,
                                    marginRight: 50,
                                    flex: 1,
                                    flexDirection:'row',
                                    flexWrap:'wrap',
                                    borderColor: "#000",
                                    borderBottomWidth: 1,
                                    marginBottom: 8
                                }}>
                                    <Text style={{fontSize: 20, backgroundColor:'#eee', paddingTop:8 }}> # </Text>
                                    <TextInput
                                        style={styles.inputEmployeeIdBox}
                                        placeholder="Employee Id"
                                        keyboardType="number-pad"
                                        underlineColorAndroid='transparent'
                                        placeholderTextColor='#999'
                                        maxLength={7}
                                        value={this.props.employeeId}
                                        onChangeText={(employeeId) => this.checkIfValidEmployeeID(employeeId)}
                                    />
                                </View>
                                { this.props.employeeIdAlreadyExists == true ? (
                                    <Text style={styles.alertAlreadyExistTextStyle}> The employee with this id already exists in the database! </Text>
                                ): null }
                                { this.props.isValidEmployeeId == false ? (
                                    <Text style={styles.alertAlreadyExistTextStyle}> This employee id is of invalid format! </Text>
                                ): null }
                            </View>
                        ) : null}
                        {/*Input EMP ID view Ends*/}
                        {/*Error Info view Starts*/}
                        {this.props.errorSHRegistration ? (
                            <View style={styles.errorAddingContainer}>
                                <MaterialIcon name='error' style={styles.errorIcon}/>
                                <Text style={styles.alertTextStyle}> Some error occured, Please Try Again! </Text>
                                <View style={{flex: 1, alignItems: 'stretch'}}>
                                    <TouchableOpacity style={styles.buttonStyle}
                                                      onPress={() => {
                                                          this.props.setIsInputIDViewEnabled(true);
                                                          this.props.setIsSHFormViewEnabled(false);
                                                          this.props.setErrorSHRegistration(false)}
                                                      }>
                                        <Text style={styles.buttonTitleStyle}>Try Again</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ):null}
                        {/*Error Info view Ends*/}
                        {/*Success Info view Starts*/}
                        {this.props.successSHRegistration ? (
                            <View style={styles.successAddingContainer}>
                                <MaterialIcon name='check-box' style={styles.successIcon}/>
                                <Text style={styles.alertTextStyle}> Sales Handler Added Successfully! </Text>
                                <View style={{flex: 1, alignItems: 'stretch'}}>
                                    <TouchableOpacity style={styles.buttonStyle}
                                                      onPress={() => {
                                                          this.props.setIsInputIDViewEnabled(true);
                                                          this.props.setIsSHFormViewEnabled(false);
                                                          this.props.setSuccessSHRegistration(false)}
                                                      }>
                                        <Text style={styles.buttonTitleStyle}>Add Another</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ):null}
                        {/*Success Info view Ends*/}
                        {/*Form view Starts*/}
                        { this.props.isSHFormViewEnabled ? (
                            <View style={styles.formContainer}>
                                <PhotoUpload onPhotoSelect={avatar => {}}>
                                    <Image style={styles.imageStyle} resizeMode='cover' source={default_avatar} />
                                </PhotoUpload>
                                <Text style={{}}>*Select Manager </Text>
                                <Picker
                                    style={{backgroundColor:"#CCC", marginBottom:5}}
                                    mode="dropdown"
                                    selectedValue={this.props.manager}
                                    onValueChange={(manager) => this.props.setManager(manager)}>
                                    {Object.keys(this.props.managers).map((key) => {
                                        return (
                                            <Picker.Item  label={this.props.managers[key]} value={key} key={key}/>
                                        )
                                    })}
                                </Picker>
                                {this.props.isManagerMissing?(<Text style={styles.errorText}>*Please select a manager from the dropdown.</Text>) : null}
                                <View style={styles.inputContainer}>
                                    <AntDesignIcon name='idcard' style={styles.formIcon}/>
                                    <TextInput
                                        style={styles.input}
                                        placeholder="Id"
                                        keyboardType="number-pad"
                                        underlineColorAndroid='transparent'
                                        placeholderTextColor='#999'
                                        editable={false}
                                        value={this.props.employeeId}
                                        onChangeText={(employeeId) => this.props.setEmployeeId(employeeId)}
                                    />
                                </View>
                                <View style={styles.inputGroup}>
                                    <View style={styles.inputContainer}>
                                        <MaterialIcon name='person-outline' style={styles.formIcon}/>
                                        <TextInput
                                            style={styles.input}
                                            placeholder="*Sales Handler Name"
                                            keyboardType="default"
                                            underlineColorAndroid='transparent'
                                            placeholderTextColor='#999'
                                            value={this.props.firstName}
                                            onChangeText={(firstName) => this.props.setFirstName(firstName)}
                                        />
                                    </View>
                                </View>
                                {this.props.isDuplicateNameError?(<Text style={styles.errorText}>*Sales handler with this name already exists.</Text>) : null}
                                {this.props.isFirstNameMissing?(<Text style={styles.errorText}>*Sales handler name cannot be blank.</Text>) : null}
                                {this.props.isFirstNameInvalid?(<Text style={styles.errorText}>*Sales Handler name should only have alphabets.</Text>) : null}
                                <View style={styles.inputContainer}>
                                    <MaterialIcon name='email' style={styles.formIcon}/>
                                    <TextInput
                                        style={styles.input}
                                        placeholder="Email"
                                        keyboardType="default"
                                        underlineColorAndroid='transparent'
                                        placeholderTextColor='#999'
                                        autoCapitalize = 'none'
                                        value={this.props.email}
                                        onChangeText={(email) => this.props.setEmail(email)}
                                    />
                                </View>
                                {this.props.isEmailInvalid?(<Text style={styles.errorText}>*Please enter a valid email address.</Text>) : null}
                                <View style={styles.inputContainer}>
                                    <MaterialIcon name='phone' style={styles.formIcon}/>
                                    <TextInput
                                        style={styles.input}
                                        placeholder="*Contact"
                                        keyboardType="number-pad"
                                        underlineColorAndroid='transparent'
                                        placeholderTextColor='#999'
                                        value={this.props.contact}
                                        maxLength={10}
                                        onChangeText={(contact) => this.validateAndSetContact(contact)}
                                    />
                                </View>
                                {this.props.isDuplicateContactError?(<Text style={styles.errorText}>*Sales handler with this contact already exists.</Text>) : null}
                                {this.props.isContactMissing?(<Text style={styles.errorText}>*Contact cannot be blank.</Text>) : null}
                                {this.props.isContactInvalid?(<Text style={styles.errorText}>*Please enter a valid 10 digit contact number.</Text>) : null}
                                <View style={styles.inputContainer}>
                                    <EntypoIcon name='address' style={styles.formIcon}/>
                                    <TextInput
                                        style={styles.input}
                                        placeholder="*Address Line 1"
                                        keyboardType="default"
                                        underlineColorAndroid='transparent'
                                        placeholderTextColor='#999'
                                        value={this.props.addressLineOne}
                                        onChangeText={(addressLineOne) => this.props.setAddressLineOne(addressLineOne)}
                                    />
                                </View>
                                {this.props.isAddressMissing?(<Text style={styles.errorText}>*Address cannot be blank.</Text>) : null}
                                <View style={styles.inputContainer}>
                                    <EntypoIcon name='address' style={styles.formIcon}/>
                                    <TextInput
                                        style={styles.input}
                                        placeholder="Address Line 2"
                                        keyboardType="default"
                                        underlineColorAndroid='transparent'
                                        placeholderTextColor='#999'
                                        value={this.props.addressLineTwo}
                                        onChangeText={(addressLineTwo) => this.props.setAddressLineTwo(addressLineTwo)}
                                    />
                                </View>
                                <View style={styles.inputGroup}>
                                    <View style={styles.inputContainer}>
                                        <MaterialIcon name='location-city' style={styles.formIcon}/>
                                        <TextInput
                                            style={styles.input}
                                            placeholder="*City"
                                            keyboardType="default"
                                            underlineColorAndroid='transparent'
                                            placeholderTextColor='#999'
                                            value={this.props.city}
                                            onChangeText={(city) => this.props.setCity(city)}
                                        />
                                    </View>
                                    <View style={styles.inputContainer}>
                                        <MaterialIcon name='confirmation-number' style={styles.formIcon}/>
                                        <TextInput
                                            style={styles.input}
                                            placeholder="ZipCode"
                                            keyboardType="number-pad"
                                            underlineColorAndroid='transparent'
                                            placeholderTextColor='#999'
                                            maxLength={6}
                                            value={this.props.zipCode}
                                            onChangeText={(zipCode) => this.props.setZipCode(zipCode)}
                                        />
                                    </View>
                                </View>
                                {this.props.isZipCodeMissing?(<Text style={styles.errorText}>*ZipCode cannot be blank.</Text>) : null}
                                {this.props.isZipCodeInvalid?(<Text style={styles.errorText}>*ZipCode should have 6 numeric digits.</Text>) : null}
                                {this.props.isCityMissing?(<Text style={styles.errorText}>*City cannot be blank.</Text>) : null}
                                {this.props.isCityInvalid?(<Text style={styles.errorText}>*City should only have alphabets.</Text>) : null}
                                <View style={styles.inputGroup}>
                                    <View style={styles.inputContainer}>
                                        <MaterialIcon name='flag' style={styles.formIcon}/>
                                        <TextInput
                                            style={styles.input}
                                            placeholder="*State"
                                            keyboardType="default"
                                            underlineColorAndroid='transparent'
                                            placeholderTextColor='#999'
                                            value={this.props.stateName}
                                            onChangeText={(stateName) => this.props.setStateName(stateName)}
                                        />
                                    </View>
                                    <View style={styles.inputContainer}>
                                        <MaterialIcon name='flag' style={styles.formIcon}/>
                                        <TextInput
                                            style={styles.input}
                                            placeholder="Country"
                                            keyboardType="default"
                                            underlineColorAndroid='transparent'
                                            placeholderTextColor='#999'
                                            editable={false}
                                            value='India'
                                            onChangeText={(country) => this.props.setCountry(country)}
                                        />
                                    </View>
                                </View>
                                {this.props.isStateNameMissing?(<Text style={styles.errorText}>*State Name cannot be blank.</Text>) : null}

                                <View>
                                    <TouchableOpacity onPress={() =>
                                        this.props.validateAndRegister(
                                            /*consolePrint(*/
                                            this.props.employeeId,
                                            this.props.manager,
                                            this.props.firstName,
                                            this.props.lastName,
                                            this.props.email,
                                            this.props.contact,
                                            this.props.addressLineOne,
                                            this.props.addressLineTwo,
                                            this.props.city,
                                            this.props.zipCode,
                                            this.props.stateName,
                                            this.props.country,
                                            this.props)}>
                                        <View style={styles.buttonStyle}>
                                            <Text style={styles.buttonTitleStyle}>Add Salesman</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>

                            </View>
                        ):null}
                        {/*Form view Ends*/}
                    </ScrollView>
                </KeyboardAvoidingView>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FFF',
    },
    formContainer: {
        flex: 1,
        backgroundColor: '#FFF',
        paddingLeft: 15,
        paddingRight: 15
    },
    imageStyle:{
        paddingVertical: 30,
        width: 80,
        height: 80,
        borderRadius: 75,
        marginTop: 10,
        marginBottom: 10
    },
    inputGroup:{
        flexDirection:'row',
        flex:1
    },
    inputContainer: {
        marginLeft: 3,
        flex: 1,
        flexDirection:'row',
        flexWrap:'wrap',
        borderColor: "#000",
        borderBottomWidth: 1,
        marginBottom: 8
    },
    enterEmployeeIdContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff'
    },
    errorAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff'
    },
    successAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff'
    },
    alertTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: '#f0af3b'
    },
    alertAlreadyExistTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: 'red'
    },
    label: {
        color:'#000',
    },
    input: {
        flex:1,
        flexDirection: "row",
        paddingLeft: 5,
        paddingBottom:0
    },
    buttonStyle: {
        flex: 0,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0af3b',
        borderWidth: 1,
        borderRadius: 5,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        borderColor: '#fff',
        marginTop: 20,
        marginBottom:50
    },
    buttonTitleStyle: {
        textDecorationColor: '#fff'
    },
    avatarContainer: {
        borderColor: '#9B9B9B',
        borderWidth: 1 / PixelRatio.get(),
        justifyContent: 'center',
        alignItems: 'center',
    },
    avatar: {
        borderRadius: 75,
        width: 150,
        height: 150,
    },
    inputEmployeeIdBox: {
        height: 45,
        marginLeft: 10,
        flex: 1,
        color: '#d4272a',
        fontSize: 20
    },
    icon: {
        fontSize: 60,
        color: '#737373',
        textAlign: 'center',
    },
    errorIcon: {
        fontSize: 60,
        color: '#d4272a',
        textAlign: 'center',
    },
    successIcon: {
        fontSize: 60,
        color: '#009933',
        textAlign: 'center',
    },
    formIcon: {
        fontSize: 20,
        color: '#CCC',
        textAlign: 'left',
        paddingTop:12,
        marginRight:0,
        marginBottom:0,
        paddingBottom:-5,
        //backgroundColor:'#eee'
    },
    errorText:{
        marginTop: 5,
        marginBottom: 5,
        justifyContent: 'center',
        alignItems: 'center',
        color: 'red',
        fontSize: 7,
    },
    keyboardContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#d4272a',
    },
    loaderContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: 'black',
        width: width,
        height: height,
        zIndex: 1
    }
});

const mapStateToProps = state => {
    return {
        authDetail: state.loginReducer.authDetail,
        salespersonDetail: state.loginReducer.salespersonDetail,

        employeeId: state.salesHandlerRegistrationReducer.employeeId,
        firstName: state.salesHandlerRegistrationReducer.firstName,
        lastName: state.salesHandlerRegistrationReducer.lastName,
        email: state.salesHandlerRegistrationReducer.email,
        contact: state.salesHandlerRegistrationReducer.contact,
        addressLineOne: state.salesHandlerRegistrationReducer.addressLineOne,
        addressLineTwo: state.salesHandlerRegistrationReducer.addressLineTwo,
        city: state.salesHandlerRegistrationReducer.city,
        zipCode: state.salesHandlerRegistrationReducer.zipCode,
        stateName: state.salesHandlerRegistrationReducer.stateName,
        country: state.salesHandlerRegistrationReducer.country,
        avatarSource: state.salesHandlerRegistrationReducer.avatarSource,
        manager: state.salesHandlerRegistrationReducer.manager,
        managers: state.salesHandlerRegistrationReducer.managers,
        showLoader: state.salesHandlerRegistrationReducer.showLoader,
        errorSHRegistration: state.salesHandlerRegistrationReducer.errorSHRegistration,
        successSHRegistration: state.salesHandlerRegistrationReducer.successSHRegistration,

        employeeIdAlreadyExists: state.salesHandlerRegistrationReducer.employeeIdAlreadyExists,
        isValidEmployeeId: state.salesHandlerRegistrationReducer.isValidEmployeeId,
        isManagerMissing: state.salesHandlerRegistrationReducer.isManagerMissing,
        isFirstNameMissing: state.salesHandlerRegistrationReducer.isFirstNameMissing,
        isContactMissing: state.salesHandlerRegistrationReducer.isContactMissing,
        isAddressMissing: state.salesHandlerRegistrationReducer.isAddressMissing,
        isCityMissing: state.salesHandlerRegistrationReducer.isCityMissing,
        isZipCodeMissing: state.salesHandlerRegistrationReducer.isZipCodeMissing,
        isZipCodeInvalid: state.salesHandlerRegistrationReducer.isZipCodeInvalid,
        isStateNameMissing: state.salesHandlerRegistrationReducer.isStateNameMissing,
        isFirstNameInvalid: state.salesHandlerRegistrationReducer.isFirstNameInvalid,
        isCityInvalid: state.salesHandlerRegistrationReducer.isCityInvalid,
        isEmailInvalid: state.salesHandlerRegistrationReducer.isEmailInvalid,
        isContactInvalid: state.salesHandlerRegistrationReducer.isContactInvalid,
        isDuplicateNameError: state.salesHandlerRegistrationReducer.isDuplicateNameError,
        isDuplicateContactError: state.salesHandlerRegistrationReducer.isDuplicateContactError,

        isInputIDViewEnabled: state.salesHandlerRegistrationReducer.isInputIDViewEnabled,
        isSHFormViewEnabled: state.salesHandlerRegistrationReducer.isSHFormViewEnabled
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setShowLoader: (showLoader) => dispatch({type:"SET_SHOW_LOADER", payload:showLoader}),
        setEmployeeId: (employeeId) => dispatch({type: "SET_EMPLOYEE_ID", payload: employeeId}),
        setFirstName: (firstName) => dispatch({type: "SET_FIRST_NAME", payload: firstName}),
        setLastName: (lastName) => dispatch({type: "SET_LAST_NAME", payload: lastName}),
        setEmail: (email) => dispatch({type: "SET_EMAIL", payload: email}),
        setContact: (contact) => dispatch({type: "SET_CONTACT", payload: contact}),
        setAddressLineOne: (addressLineOne) => dispatch({type: "SET_ADDRESS_LINE_ONE", payload: addressLineOne}),
        setAddressLineTwo: (addressLineTwo) => dispatch({type: "SET_ADDRESS_LINE_TWO", payload: addressLineTwo}),
        setCity: (city) => dispatch({type: "SET_CITY", payload: city}),
        setZipCode: (zipCode) => dispatch({type: "SET_ZIP_CODE", payload: zipCode}),
        setStateName: (stateName) => dispatch({type: "SET_STATE_NAME", payload: stateName}),
        setCountry: (country) => dispatch({type: "SET_COUNTRY", payload: country}),
        //setAvatarSource: (avatarSource) => dispatch({type:"SET_AVATAR_SOURCE",payload:avatarSource}),
        setManager: (manager) => dispatch({type: "SET_MANAGER", payload: manager}),
        setErrorSHRegistration: (errorSHRegistration) => dispatch({type: "SET_ERROR_SH_REGISTRATION", payload: errorSHRegistration}),
        setSuccessSHRegistration: (successSHRegistration) => dispatch({type: "SET_SUCCESS_SH_REGISTRATION", payload: successSHRegistration}),
        setEmployeeIDAlreadyExists: (employeeIdAlreadyExists) => dispatch({type: "SET_EMPLOYEE_ID_ALREADY_EXISTS", payload: employeeIdAlreadyExists}),
        setIsValidEmployeeId: (isValidEmployeeId) => dispatch({type: "SET_IS_VALID_EMPLOYEE_ID", payload: isValidEmployeeId}),
        setIsInputIDViewEnabled: (isInputIDViewEnabled) => dispatch({type: "SET_IS_INPUT_ID_VIEW_ENABLED", payload: isInputIDViewEnabled}),
        setIsSHFormViewEnabled: (isSHFormViewEnabled) => dispatch({type: "SET_IS_SH_FORM_VIEW_ENABLED", payload: isSHFormViewEnabled}),
        setIsContactInvalid: (isContactInvalid) => dispatch({type: "SET_ERROR_CONTACT_INVALID", payload: isContactInvalid}),
        setIsContactMissing: (isContactMissing) => dispatch({type: "SET_ERROR_CONTACT_MISSING", payload: isContactMissing}),
        setIsDuplicateNameError: (isDuplicateNameError) => dispatch({type: "SET_IS_DUPLICATE_NAME_ERROR", payload: isDuplicateNameError}),
        setIsDuplicateContactError: (isDuplicateContactError) => dispatch({type: "SET_IS_DUPLICATE_CONTACT_ERROR", payload: isDuplicateContactError}),
        validateAndRegister: (employeeId,manager,firstName,lastName,email,contact,addressLineOne,addressLineTwo,city,zipCode,stateName,country,props) =>
            dispatch(SalesHandlerRegistrationActions.validateAndRegister(employeeId,manager,firstName,lastName,email,contact,addressLineOne,addressLineTwo,city,zipCode,stateName,country,props)),
        lookupSalesHandler: (employeeId) => dispatch(SalesHandlerRegistrationActions.lookupSalesHandler(employeeId)),
    }
};
export default connect(mapStateToProps, mapDispatchToProps)(SalesHandlerRegistrationScreen);