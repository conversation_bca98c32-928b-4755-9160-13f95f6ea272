import React, {Component} from 'react';
import {connect} from 'react-redux';
import {StyleSheet, Text, FlatList, View, TouchableOpacity} from 'react-native';
import HeaderView from "./HeaderView";
import Communications from 'react-native-communications';
import * as SalesHandlerActions from "./../store/actions/SalesHandlerActions";

class RetailerListScreen extends Component {

    getProgressValue(quantity, target){
        let progress = (quantity * .5) / target;
        progress = progress.toPrecision(2);
        progress = parseFloat(progress);
        return progress;
    }

    getValue(value){
        if(value == null){
            return "N/A";
        }else{
            return value;
        }
    }

    render() {

        return (
            <View style={styles.container}>
                <HeaderView menuIcon={false} title={'Retailers'} {...this.props} headerColor={"#4a4e4d"}/>
                <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'space-between', position: 'relative'}}>
                    {this.props.retailersMap != null ? (
                        <FlatList style={{marginBottom: 0}} data={Object.values(this.props.retailersMap)}
                                  renderItem={({item}) => (
                                      <View key={item.summary.id} style={{flex: 1, justifyContent: 'flex-start', alignItems: 'stretch'}}>
                                          <View style={styles.tile}>
                                              <View style={item.summary.status === "IN_ACTIVE" ? styles.tileHeadDeactivated : styles.tileHead }>
                                                  <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'space-between'}}>
                                                      <Text style={styles.tileHeadText}>{item.summary.name}</Text>
                                                      <Text style={styles.tileHeadText}>Express Chai #{item.summary.id}</Text>
                                                  </View>



                                                  <View style={{flex: 0, alignItems: 'stretch', justifyContent: 'space-between'}}>
                                                      <TouchableOpacity style={styles.titleButton}
                                                                        onPress={() => this.props.showOrdersList(item.summary.id, this.props)}>
                                                          <Text style={styles.titleBtnText}>Orders</Text>
                                                      </TouchableOpacity>
                                                      <TouchableOpacity style={styles.titleButton}
                                                                        onPress={() => Communications.phonecall(item.summary.contact, true)}>
                                                          <Text style={styles.titleBtnText}>Call</Text>
                                                      </TouchableOpacity>
                                                  </View>




                                              </View>
                                              <View style={styles.tileBody}>
                                                  <View style={styles.summaryContainer}>
                                                      <View style={styles.summaryRow}>
                                                          <View style={styles.summaryCell}>
                                                              {item.summary.retailerTargetType==null?(<Text style={{color: '#333', fontSize: 12, textAlign:'center'}}> N/A </Text>): null}
                                                              {item.summary.retailerTargetType!=null && item.summary.retailerTargetType === "TARGET_50_KG"?(<Text style={styles.summaryCellValue}> 50 KG</Text>): null}
                                                              {item.summary.retailerTargetType!=null && item.summary.retailerTargetType === "TARGET_30_KG"?(<Text style={styles.summaryCellValue}> 30 KG</Text>): null}
                                                              {item.summary.retailerTargetType!=null && item.summary.retailerTargetType === "TARGET_4_KG"?(<Text style={styles.summaryCellValue}> 4 KG</Text>): null}
                                                              {item.summary.retailerTargetType!=null && item.summary.retailerTargetType === "TARGET_OTHER"?(<Text style={styles.summaryCellValue}> Other</Text>): null}
                                                              <Text style={styles.summaryCellTag}>Target Type</Text>
                                                          </View>
                                                          {item.summary.retailerTargetType!=null && item.summary.retailerTargetType === "TARGET_OTHER"?(
                                                              <View style={styles.summaryCell}>
                                                                  <Text style={styles.summaryCellValue}>{this.getValue(item.summary.retailerTargetValue)}</Text>
                                                                  <Text style={styles.summaryCellTag}>Target Value</Text>
                                                              </View>):null}
                                                          <View style={styles.summaryCell}>
                                                              <Text style={styles.summaryCellValue}>{this.getValue(item.summary.retailerOrderFrequency)}</Text>
                                                              <Text style={styles.summaryCellTag}>Order Frequency</Text>
                                                          </View>
                                                      </View>

                                                      <View style={styles.summaryRow}>
                                                          <View style={styles.summaryCell}>
                                                              <Text style={styles.summaryCellValue}>{item.summary.newReceivedOrderCount}</Text>
                                                              <Text style={styles.summaryCellTag}>Orders to Accept</Text>
                                                          </View>
                                                          <View style={styles.summaryCell}>
                                                              <Text style={styles.summaryCellValue}>{item.summary.pendingOrderCount}</Text>
                                                              <Text style={styles.summaryCellTag}>Pending Delivery</Text>
                                                          </View>
                                                          <View style={styles.summaryCell}>
                                                              <Text style={styles.summaryCellValue}>{item.summary.cancelRequestOrderCount}</Text>
                                                              <Text style={styles.summaryCellTag}>Cancel Requests</Text>
                                                          </View>
                                                          <View style={styles.summaryCell}>
                                                              <Text style={styles.summaryCellValue}>{item.summary.deliveredOrderCount}</Text>
                                                              <Text style={styles.summaryCellTag}>Delivered Orders</Text>
                                                          </View>
                                                      </View>
                                                  </View>
                                              </View>
                                          </View>

                                      </View>
                                  )}
                                  keyExtractor={(item, index) => index.toString()}
                        />


                    ) : null}
                    {this.props.retailersMap == null ? (
                        <Text>No data available. Please contact your manager.</Text>
                    ):null}
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#EFEFEF'
    },
    tile: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        margin: 10,
        borderWidth: 1,
        borderColor: '#CCC',
        backgroundColor: '#FDFBF8',
        borderRadius: 4
    },
    tileHead: {
        flex: 0,
        flexDirection: 'row',
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        backgroundColor: '#7B3868',
        padding: 2,
        borderRadius: 4
    },
    tileHeadDeactivated: {
        flex: 0,
        flexDirection: 'row',
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        backgroundColor: '#ff3377',
        padding: 2,
    },
    tileHeadText: {
        padding: 5,
        color: '#FDFBF8',
        textShadowColor: 'rgba(0, 0, 0, 0.75)',
        textShadowOffset: {width: -1, height: 1},
        textShadowRadius: 10
    },
    titleButton: {
        backgroundColor: '#FDFBF8',
        borderWidth: 1,
        borderColor: '#777',
        borderRadius: 5,
        padding: 3,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 2,
        elevation: 5
    },
    titleBtnText: {color: '#7B3868', textAlign: 'center', fontSize: 12, fontWeight: 'bold'},
    tileBody: {
        padding: 3,
        backgroundColor: '#FDFBF8',
        borderRadius: 4
    },
    target: {
        flex: 0,
        flexDirection: 'row',
        alignItems: 'stretch',
        justifyContent: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#EFEFEF',
        marginBottom: 5
    },
    targetLeft: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        borderRightWidth: 1,
        borderRightColor: '#EFEFEF'
    },
    targetRight:{
        flex: 1,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        padding: 3
    },
    summaryContainer:{
        flex:0,
        alignItems:'stretch',
        justifyContent:'flex-start'
    },
    summaryRow: {
        flex:0,
        flexDirection:'row',
        alignItems: 'flex-start',
        justifyContent:'flex-start'
    },
    summaryCell:{
        flex: 1,
        borderWidth:2,
        borderColor:'#7B3868',
        borderRadius: 5,
        margin:2,
        backgroundColor: "#e6e6ea",
        elevation: 2
    },
    summaryCellValue:{
        textAlign:'center',
        color:'#000',
        fontSize: 12
    },
    summaryCellTag:{
        textAlign:'center',
        color:'#000',
        fontSize: 10
    }
});

const mapStateToProps = state => {
    return {
        retailersMap: state.salesHandlerReducer.retailersMap,
    }
};

const mapDispatchToProps = dispatch => {
    return {
        showOrdersList: (retailerId, props) => dispatch(SalesHandlerActions.showOrdersList(retailerId, props)),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(RetailerListScreen);