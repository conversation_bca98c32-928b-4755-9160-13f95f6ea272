import React, {Component} from 'react';
import {connect} from 'react-redux';
import {ActivityIndicator, FlatList, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import HeaderView from "./HeaderView";
import MaterialIcon from "react-native-vector-icons/MaterialIcons";
import AntDesignIcon from "react-native-vector-icons/AntDesign";
import * as RetailerManagementActions from "../store/actions/RetailerManagementActions";

import Communications from "react-native-communications";

var Dimensions = require('Dimensions');
var {width, height} = Dimensions.get('window');

class RetailerManagementScreen extends Component {

    constructor(props) {
        super(props);
        this.applyStatusFilter = this.applyStatusFilter.bind(this);
    }

    componentWillMount() {
        this.props.navigation.addListener(
            'didFocus',
            payload => {
                console.debug('didFocus', payload);
                this.props.setRetailer(null);
                this.fetchRetailers();
            }
        );

    }

    componentWillUnmount() {
        if (this.props.navigation && this.props.navigation.removeListener) {
            this.props.navigation.removeListener('didFocus');
        }
    }

    fetchRetailers() {
        this.props.setFilter({"status": 'ACTIVE'});
        this.props.fetchRetailers(this.props.salesHandler.id, 'ACTIVE', this.props);
    }

    applyStatusFilter(status) {
        if (status === 'Active') {
            status = 'ACTIVE';
        } else if (status === 'Inactive') {
            status = 'IN_ACTIVE';
        } else if (status === 'Initiated') {
            status = 'INITIATED';
        } else {
            status = 'ALL';
        }
        this.props.setFilter({"status": status});
        this.props.fetchRetailers(this.props.salesHandler.id, status, this.props);
    }


    render() {
        return (
            <View style={styles.container}>
                <HeaderView menuIcon={false} title={'Manage Retailers'} backScreen={'Home'} {...this.props}
                            headerColor={"#4a4e4d"}/>
                <View style={styles.retailerSummary}>
                    <View><Text style={{padding: 7}}>Total Retailers: {this.props.totalRetailers}</Text></View>
                    <View><Text style={{padding: 7}}>Total Active: {this.props.totalActiveRetailers}</Text></View>
                    <View>
                        <TouchableOpacity style={styles.addBtn}
                                          onPress={() => this.props.navigation.navigate('AddRetailer')}>
                            <Text style={{color: '#FFF'}}>Add</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                {this.props.showLoader ? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#f0af3b"/>
                        <Text style={{color: '#f0af3b'}}>Loading Retailers ...</Text>
                    </View>
                ) : null}
                {this.props.retailers == null ? (
                    <Text style={styles.noDataText}> No Retailer Allocated. Please contact your manager. </Text>
                ) : null}
                <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'space-between', position: 'relative'}}>
                    {(this.props.retailers != null && this.props.retailers.length > 0) ? (
                        <FlatList style={{marginBottom: 0}} data={this.props.retailers}
                                  renderItem={({item}) => (
                                      <View key={item.customerId}
                                            style={{flex: 1, justifyContent: 'flex-start', alignItems: 'stretch'}}>
                                          <View style={styles.tile}>
                                              <View style={styles.tileBar}>
                                                  <Text style={{fontSize: 12, marginBottom: 5}}>Express Chai
                                                      #{item.customerId}</Text>
                                                  <Text style={{fontSize: 12, marginBottom: 5}}>Sales Diary
                                                      #{item.salesDiaryId === 0 ? "NA" : item.salesDiaryId}</Text>
                                                  {item.status === "ACTIVE" ? (<Text style={{
                                                      fontSize: 12,
                                                      color: 'green',
                                                      marginBottom: 5
                                                  }}>Active</Text>) : null}
                                                  {item.status === "INITIATED" ? (<Text style={{
                                                      fontSize: 12,
                                                      color: '#f0af3b',
                                                      marginBottom: 5
                                                  }}>Initiated</Text>) : null}
                                                  {item.status === "IN_ACTIVE" ? (<Text style={{
                                                      fontSize: 12,
                                                      color: 'red',
                                                      marginBottom: 5
                                                  }}>Inactive</Text>) : null}
                                                  {item.status === "ON_BOARDED" ? (
                                                      <Text style={{fontSize: 12, color: '#000', marginBottom: 5}}>On
                                                          boarded</Text>) : null}
                                                  {item.status === "REJECTED" ? (<Text style={{
                                                      fontSize: 12,
                                                      color: '#000',
                                                      marginBottom: 5
                                                  }}>Rejected</Text>) : null}
                                                  {item.status === "DISABLED" ? (<Text style={{
                                                      fontSize: 12,
                                                      color: '#000',
                                                      marginBottom: 5
                                                  }}>Disabled</Text>) : null}
                                                  {item.status === "BLACK_LISTED" ? (<Text style={{
                                                      fontSize: 12,
                                                      color: '#000',
                                                      marginBottom: 5
                                                  }}>BlackListed</Text>) : null}
                                              </View>
                                              <View style={styles.tileBody}>
                                                  <View style={styles.contactContainer}>
                                                      <MaterialIcon name='person-outline' style={styles.icon}/>
                                                      <Text style={{
                                                          flex: 1,
                                                          flexWrap: 'wrap',
                                                          fontSize: 14,
                                                          color: '#000',
                                                          marginBottom: 5
                                                      }}>{item.customerName}</Text>
                                                  </View>
                                                  <View style={styles.contactContainer}>
                                                      <MaterialIcon name='phone' style={styles.icon}/>
                                                      <TouchableOpacity
                                                          onPress={() => Communications.phonecall(item.customerContact, true)}>
                                                          <Text style={{
                                                              fontSize: 14,
                                                              color: '#000',
                                                              marginBottom: 5
                                                          }}>{item.customerContact}</Text>
                                                      </TouchableOpacity>
                                                  </View>
                                                  <TouchableOpacity style={styles.updateRetailerButton}
                                                                    onPress={() => {
                                                                        this.props.fetchRetailer(item.customerId, this.props);
                                                                    }}>
                                                      <AntDesignIcon name='right'
                                                                     style={[styles.icon, {color: '#d4272a'}]}/>
                                                  </TouchableOpacity>
                                              </View>
                                          </View>
                                      </View>
                                  )}
                                  keyExtractor={(item, index) => index.toString()}
                        />
                    ) : null}
                    {(this.props.retailers != null && this.props.retailers.length == 0) ? (
                        <Text style={{margin: 20, color: '#000'}}>No retailer found.</Text>
                    ) : null}
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FFF',
    },
    retailerSummary: {
        flex: 0,
        backgroundColor: '#EEE',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    addBtn: {
        padding: 5,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 5,
        backgroundColor: '#68A4CC',
        borderRadius: 4
    },
    noDataText: {
        textAlign: 'center',
        marginTop: 50
    },
    tile: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        margin: 10,
        borderWidth: 1,
        borderColor: '#ececec',
        backgroundColor: '#fff',
        borderRadius: 4,
        paddingLeft: 0,
        paddingRight: 0
    },
    tileBody: {
        // padding:10,
        backgroundColor: '#FFF',
        borderRadius: 4,
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    tileBar: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        padding: 8,
        justifyContent: 'space-between',
        backgroundColor: '#eee'
    },
    contactContainer: {
        marginLeft: 5,
        flex: 1,
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    updateRetailerButton: {
        padding: 15,
    },
    icon: {
        fontSize: 16,
        color: '#CCC',
        textAlign: 'left',
        marginRight: 5,
    },
    titleButton: {
        backgroundColor: '#fff',
        borderRadius: 4,
        padding: 3,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 2
    },
    loaderContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: 'black',
        width: width,
        height: height,
        zIndex: 1
    }
});

const mapStateToProps = state => {
    return {
        showLoader: state.retailerManagementReducer.showLoader,
        authDetail: state.loginReducer.authDetail,
        salesHandler: state.loginReducer.salespersonDetail,
        totalRetailers: state.retailerManagementReducer.totalRetailers,
        totalActiveRetailers: state.retailerManagementReducer.totalActiveRetailers,
        retailers: state.retailerManagementReducer.retailers,
        retailer: state.retailerManagementReducer.retailer,
        filter: state.retailerManagementReducer.filter,
        beforeFilterRetailers: state.retailerManagementReducer.beforeFilterRetailers
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setRetailer: (retailer) => dispatch({type: "SET_RETAILER", payload: retailer}),
        fetchRetailer: (retailerId, props) => dispatch(RetailerManagementActions.fetchRetailer(retailerId, props)),
        setFilter: (filter) => dispatch({type: "SET_FILTER", payload: filter}),
        fetchRetailers: (salesHandlerId, status, props) =>
            dispatch(RetailerManagementActions.fetchRetailers(salesHandlerId, status, props)),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(RetailerManagementScreen);