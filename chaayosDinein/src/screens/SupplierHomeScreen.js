import React, {Component} from 'react';
import {connect} from "react-redux";
import {ActivityIndicator, <PERSON><PERSON>, <PERSON>List, ScrollView, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import HeaderView from "./HeaderView";
import Communications from "react-native-communications";
import UtilityService from "../store/services/UtilityService";
import * as SalesHandlerActions from "./../store/actions/SalesHandlerActions";
import Ionicons from "react-native-vector-icons/Ionicons";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import TargetsView from "./TargetsView";

let Dimensions = require('Dimensions');
let { width, height } = Dimensions.get('window');

class SupplierHomeScreen extends Component {

    constructor(){
        super();
        this.interval = null;
    }

    componentWillMount() {
        this.props.navigation.addListener(
            'didFocus', (payload => {
                                console.debug('didFocus', payload);
                                this.loadHandlerSummary();
                            })
        );
        this.interval = setInterval(() => {
            this.props.refreshHandlerOrders(this.props.salesHandler.id,
                this.props.metadata.orderCount,
                    this.props.noOfDays)
        }, 15 * 60 * 1000);
        if(this.props.orderReceivedData != null && this.props.orderReceivedData.orderId !=null){
            this.reloadHandlerSummary();
        }
    }

    componentWillUnmount() {
        if (this.props.navigation && this.props.navigation.removeListener) {
            this.props.navigation.removeListener('didFocus');
        }
        if(this.interval != null && (typeof this.interval.clearInterval == 'function')) {
            this.interval.clearInterval();
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.salesHandlerSummaries !== this.props.salesHandlerSummaries) {
            if(this.props.orderReceivedData != null && this.props.orderReceivedData.orderId !=null){
                this.props.setNotifiedOrderID(this.props.orderReceivedData.orderId);
            }
        }
    }

    componentDidMount(){
            if(this.props.notifiedOrderID != null){
                this.props.resetNoOfDaysToToday(0, this.props.salesHandler.id);
            }
    }

    loadHandlerSummary() {
        if (UtilityService.checkEmpty(this.props.salesHandlerSummaries)) {
            this.props.fetchHandlerSummary(this.props.salesHandler.id, this.props.noOfDays, true);
        }
    }

    reloadHandlerSummary() {
        this.props.fetchHandlerSummary(this.props.salesHandler.id, this.props.noOfDays, true);
    }

    getDate(currentBusinessDate){
        return UtilityService.formatDate(UtilityService.addDateToDate(( currentBusinessDate != null) ? currentBusinessDate : null, this.props.noOfDays), 'dd-MM-yyyy');
    }

    alertCancelOrder(item){
        const prop = this.props;
        Alert.alert(
            'Cancel Order',
            'Are you sure you want to cancel the order?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        this.props.updateOrderStatus(item.orderId, item.unitId, "CANCELLED", this.props)
                    } },
            ],
            { cancelable: false }
        )
    }

    render() {
        return (
            <View style={styles.container}>
                {this.props.salesTargetsLoading || this.props.salesHandlerSummaryLoading? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#f0af3b"/>
                        <Text style={{color: '#f0af3b'}}>Loading data ...</Text>
                    </View>
                ) : null}
                <HeaderView menuIcon={true} title={'Home'} {...this.props} headerColor={"#4a4e4d"}/>
                <ScrollView style={{flex: 1}} contentContainerStyle={{alignItems: 'stretch', justifyContent: 'flex-start'}}>
                    <View style={styles.daysNavigator}>
                        <TouchableOpacity style={styles.navBtn}
                                          onPress={() => this.props.updateNoOfDays(-1, this.props)}>
                            <Text style={{color:'#4a4e4d'}}>Prev</Text>
                        </TouchableOpacity>
                        <Text style={{padding:7}}>
                            {UtilityService.formatDate(UtilityService.addDateToDate((this.props.metadata != null) ?
                                this.props.metadata.currentBusinessDate: null, this.props.noOfDays), 'dd MMM yyyy')}
                            {this.props.noOfDays === 0 ? ' Today': null}
                        </Text>
                        {this.props.noOfDays < 0 ? (
                            <TouchableOpacity style={styles.navBtn}
                                              onPress={() => this.props.updateNoOfDays(1, this.props)}>
                                <Text style={{color:'#4a4e4d'}}>Next</Text>
                            </TouchableOpacity>
                        ) :
                            <Text style={[styles.navBtn, {color:'#FDFBF8',backgroundColor:'#DDD'}]}>Next</Text>
                        }
                    </View>



                <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'space-between', position: 'relative'}}>


                    {!this.props.salesHandlerSummaryLoading
                    && !UtilityService.checkEmpty(this.props.salesHandlerSummaries)
                    && this.props.metadata.orderCount > 0 ? (
                    <FlatList style={{marginBottom: 0}} data={Object.values(this.props.salesHandlerSummaries)}
                                      renderItem={({item}) => (


                        <View style={{flex: 0, justifyContent: 'flex-start', alignItems: 'stretch'}}>
                            <View style={[styles.tile, {marginTop:10}]}>
                                <View style={styles.tileHead}>
                                    <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'space-between'}}>
                                        <Text style={styles.tileHeadText}>{item.handlerInfo.handlerName}</Text>
                                        <Text style={styles.tileHeadText}>{item.handlerInfo.handlerId}</Text>
                                    </View>

                                    <View style={{flex: 0, alignItems: 'stretch', justifyContent: 'space-between', marginRight:5}}>
                                        <TouchableOpacity style={styles.titleButton}
                                                          onPress={() => this.props.showTeamDetail(item, this.props)}>

                                                {this.props.salespersonDetail.designation !== "Territory Sales Executive" ?
                                                    <Text style={styles.titleBtnText}>Salesmen</Text>
                                                    : <Text style={styles.titleBtnText}>Retailers</Text> }
                                        </TouchableOpacity>
                                        <TouchableOpacity style={styles.titleButton}
                                                          onPress={() => Communications.phonecall(item.handlerInfo.handlerContact, true)}>
                                            <Text style={styles.titleBtnText}>Call</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                                <View style={styles.tileBody}>

                                    <View style={styles.summaryContainer}>
                                        <View style={styles.summaryRow}>
                                            <View style={styles.summaryCell}>
                                                <Text style={styles.summaryCellValue}>{item.team.summary.orderCount}</Text>
                                                <Text style={styles.summaryCellTag}>Total Orders</Text>
                                            </View>
                                            {item.team.summary.handlerCount !== 0?(
                                                <View style={styles.summaryCell}>
                                                    <Text style={styles.summaryCellValue}>{item.team.summary.handlerCount}</Text>
                                                    <Text style={styles.summaryCellTag}>Total Salesmen</Text>
                                                </View>
                                            ):null}
                                            <View style={styles.summaryCell}>
                                                <Text style={styles.summaryCellValue}>{item.team.summary.retailerCount}</Text>
                                                <Text style={styles.summaryCellTag}>Total Retailers</Text>
                                            </View>
                                        </View>
                                        <View style={styles.summaryRow}>
                                            <View style={styles.summaryCell}>
                                                <Text style={styles.summaryCellValue}>{item.team.summary.newReceivedOrderCount}</Text>
                                                <Text style={styles.summaryCellTag}>Orders to Accept</Text>
                                            </View>
                                            <View style={styles.summaryCell}>
                                                <Text style={styles.summaryCellValue}>{item.team.summary.pendingOrderCount}</Text>
                                                <Text style={styles.summaryCellTag}>Pending Delivery</Text>
                                            </View>
                                            <View style={styles.summaryCell}>
                                                <Text style={styles.summaryCellValue}>{item.team.summary.cancelRequestOrderCount}</Text>
                                                <Text style={styles.summaryCellTag}>Cancel Requests</Text>
                                            </View>
                                            <View style={styles.summaryCell}>
                                                <Text style={styles.summaryCellValue}>{item.team.summary.deliveredOrderCount}</Text>
                                                <Text style={styles.summaryCellTag}>Delivered Orders</Text>
                                            </View>
                                        </View>
                                    </View>

                                    {/*Targets Container for each handler starts*/}
                                    <TargetsView openedTargetType={"DAILY"}
                                                 handlerId={item.handlerInfo.handlerId}
                                                 date={this.getDate(this.props.metadata.currentBusinessDate)}
                                                 isCurrentDay = {this.props.noOfDays === 0 ? true : false}
                                                 {...this.props} />
                                    {/*Targets Container for each handler ends*/}

                                </View>
                            </View>
                        </View>  )}
                        keyExtractor={(item, index) => index.toString()}
                    />
                    ) :null}
                </View>

                    {(this.props.pendingOrders != null && this.props.pendingOrders.length > 0) ? (
                        <View style={{flex:1, backgroundColor:'#FDFBF8'}}>
                            <View style={styles.pendingOrdersHead}>
                                <Text style={{color:'#04828C', padding: 10}}>Pending Orders</Text>
                                {!(this.props.noOfDays < 0) ?(
                                    <View>
                                        <TouchableOpacity
                                            onPress={() => this.reloadHandlerSummary()}>
                                            <Ionicons name={'md-refresh'} style={styles.refreshIcon} />
                                        </TouchableOpacity>
                                    </View>
                                ):null}
                            </View>
                            <FlatList style={{marginBottom: 0}} data={this.props.pendingOrders.sort((o1, o2) => new Date(o2.orderDate) - new Date(o1.orderDate))}
                                      renderItem={({item, index}) => (
                                              <View key={item.id} style={{flex: 1, justifyContent: 'flex-start', alignItems: 'stretch'}}>
                                                  <View style={styles.tile}>
                                                      <View style={[{padding: 10, borderRadius: 4, borderWidth:1, borderColor:"#C2C3C3"},
                                                          {backgroundColor:(item.generatedOrderId === this.props.notifiedOrderID)?'#adcbe3':'#C2C3C3'}]}>
                                                          <View style={{flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                                              <Text style={{fontSize:16,fontWeight:'bold', color:'#675A63', marginBottom:8}}>{item.retailerName}</Text>
                                                              <View style={{flexDirection:'row', justifyContent:'center', alignItems:'center'}}>
                                                                  <FontAwesome size={12} name='rupee' color='#675A63' style={{marginRight:2}} />
                                                                  <Text style={{fontSize:12,fontWeight:'bold', color:'#675A63',marginTop:-2}}>{item.amount}</Text>
                                                              </View>
                                                          </View>
                                                          <View style={{flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                                              <Text style={{fontSize:10, marginBottom:5}}>{item.generatedOrderId}</Text>
                                                              <Text style={{fontSize:12,fontWeight:'bold', color:'#675A63', marginBottom:5}}>
                                                                  {item.status === "INITIATED" ? (<Text style={{fontSize:15, color:'#25A1AC', marginBottom:5}}>Order Received</Text>) :null}
                                                                  {item.status === "SETTLED" ? (<Text style={{fontSize:15, color:'#F19033', marginBottom:5}}>Pending Delivery</Text>) :null}
                                                                  {item.status === "CANCELLED_REQUESTED" ? (<Text style={{fontSize:15, color:'#D53943', marginBottom:5}}>Cancel Requested</Text>) :null}
                                                              </Text>
                                                          </View>
                                                          <Text style={{fontSize:12,fontWeight:'bold', color:'#675A63', marginBottom:5}}>{item.orderSummary}</Text>

                                                          <View style={{flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                                              <Text style={{fontSize:12,fontWeight:'bold', color:'#675A63', marginBottom:5}}>
                                                                  {UtilityService.formatDate(item.orderDate, 'dd MMM yyyy at hh:mm A')}
                                                              </Text>
                                                              {item.status === "INITIATED" ? (
                                                                  <View style={{flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                                                      <TouchableOpacity style={{padding:5,
                                                                          marginLeft: 35,
                                                                          paddingLeft: 15,
                                                                          paddingRight: 15,
                                                                          backgroundColor:'#25A1AC',
                                                                          borderRadius: 4,
                                                                          borderWidth: 1,
                                                                          borderColor:"#25A1AC",
                                                                          elevation:3}}
                                                                                        onPress={() => this.props.updateOrderStatus(item.orderId, item.unitId, "SETTLED", this.props)}>
                                                                          <Text style={{fontSize:10, color:'#F2F3DB'}}>Accept</Text>
                                                                      </TouchableOpacity>
                                                                      <TouchableOpacity style={{padding:5,
                                                                          paddingLeft: 15,
                                                                          paddingRight: 15,
                                                                          backgroundColor:'#D53943',
                                                                          borderRadius: 4,
                                                                          borderWidth: 1,
                                                                          borderColor:"#D53943",
                                                                          elevation:3}}
                                                                                        onPress={() => this.alertCancelOrder(item)}>
                                                                          <Text style={{fontSize:10, color:'#F2F3DB'}}>Cancel</Text>
                                                                      </TouchableOpacity>
                                                                  </View>
                                                              ) : null}
                                                              {item.status === "SETTLED" ? (
                                                                  <TouchableOpacity style={styles.orderDeliveredBtn}
                                                                                    onPress={() => this.props.updateOrderStatus(item.orderId, item.unitId, "DELIVERED", this.props)}>
                                                                      <Text style={{fontSize:10, color:'#F2F3DB'}}>Delivered</Text>
                                                                  </TouchableOpacity>
                                                              ) : null}
                                                              {item.status === "CANCELLED_REQUESTED" ? (
                                                                  <TouchableOpacity style={styles.orderDeliveredBtn}
                                                                                    onPress={() => this.props.updateOrderStatus(item.orderId, item.unitId, "CANCELLED", this.props)}>
                                                                      <Text style={{fontSize:10, color:'#F2F3DB'}}>Confirm</Text>
                                                                  </TouchableOpacity>
                                                              ) : null}
                                                          </View>
                                                      </View>
                                                  </View>
                                              </View>
                                      )}
                                      keyExtractor={(item, index) => index.toString()}
                            />
                        </View>
                    ) : null}

                    {!this.props.salesHandlerSummaryLoading && (UtilityService.checkEmpty(this.props.salesHandlerSummaries)
                            || this.props.metadata.orderCount === 0) ? (
                        <View>
                            {this.props.noOfDays === 0 && this.props.salespersonDetail.designation !== "Senior Manager"? (
                                <View style={[styles.tile, {marginTop:10}]}>
                                    {/*Targets Container for handler with 0 orders, starts*/}
                                    <TargetsView openedTargetType={"DAILY"}
                                                 handlerId={this.props.salespersonDetail.id}
                                                 date={this.getDate(new Date())}
                                                 isCurrentDay = {this.props.noOfDays === 0 ? true : false}
                                                 {...this.props} />
                                    {/*Targets Container for handler with 0 orders, ends*/}
                                </View>
                            ) : null}
                            <View style={{flex:1, alignItems:'center', justifyContent:'center'}}>
                                <Text style={{margin:50, textAlign:'center'}}>No data available. Please contact your manager.</Text>
                            </View>
                        </View>
                    ):null}

                </ScrollView>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#EFEFEF'
    },
    daysNavigator:{
        flex: 0,
        backgroundColor:'#FDFBF8',
        flexDirection:'row',
        alignItems:'stretch',
        justifyContent: 'space-between',
    },
    navBtn:{
        padding:5,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 5,
        backgroundColor:'#fed766',
        borderRadius: 4,
        elevation: 5
    },
    tile: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        margin: 10,
        marginTop: 0,
        borderWidth: 1,
        borderColor: '#CCC',
        backgroundColor: '#FDFBF8',
        borderRadius: 4
    },
    tileHead: {
        flex: 0,
        flexDirection: 'row',
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        backgroundColor: '#436180',
        padding: 2,
        borderRadius: 4
    },
    tileHeadText: {
        padding: 5,
        color: '#FDFBF8',
        textShadowColor: 'rgba(0, 0, 0, 0.75)',
        textShadowOffset: {width: -1, height: 1},
        textShadowRadius: 10
    },
    titleButton: {
        backgroundColor: '#FDFBF8',
        borderWidth: 1,
        borderColor: '#777',
        borderRadius: 5,
        padding: 3,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 2,
        elevation: 5
    },
    titleBtnText: {color: '#436180', textAlign: 'center', fontSize: 10, fontWeight: 'bold'},
    tileBody: {
        padding: 3,
        borderRadius: 4
        // backgroundColor: '#FFF'
    },
    target: {
        flex: 0,
        flexDirection: 'row',
        alignItems: 'stretch',
        justifyContent: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#EFEFEF',
        marginBottom: 5
    },
    targetLeft: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        borderRightWidth: 1,
        borderRightColor: '#EFEFEF'
    },
    targetRight:{
        flex: 1,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        padding: 3
    },
    summaryContainer:{
        flex:0,
        alignItems:'stretch',
        justifyContent:'flex-start'
    },
    summaryRow: {
        flex:0,
        flexDirection:'row',
        alignItems: 'flex-start',
        justifyContent:'flex-start'
    },
    summaryCell:{
        flex: 1,
        borderWidth:2,
        borderColor:'#436180',
        borderRadius: 5,
        margin:2,
        backgroundColor: "#e6e6ea",
        elevation: 2
    },
    summaryCellValue:{textAlign:'center', color:'#000', fontSize: 12},
    summaryCellTag:{
        textAlign:'center',
        color:'#000',
        fontSize: 10
    },
    pendingOrdersHead:{flex:0, alignItems:'stretch', justifyContent:'space-between', flexDirection:'row'},
    refreshIcon:{color:'#04828C', padding: 10, paddingRight:15, paddingLeft:15, fontSize: 21},
    orderDeliveredBtn:{
        padding:5,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 5,
        backgroundColor:'#25A1AC',
        borderRadius: 4,
        borderWidth: 1,
        borderColor:"#25A1AC",
        elevation:3
    },
    targetTitleButton: {
        backgroundColor: '#FDFBF8',
        borderRadius: 1,
        padding: 3,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 2
    },
    targetTile: {flex: 0,alignItems: 'stretch',justifyContent: 'flex-start',margin: 10,marginTop: 0,borderWidth: 1,borderColor: '#CCC',backgroundColor: '#FDFBF8'},
    loaderContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: 'black',
        width: width,
        height: height,
        zIndex: 1
    }
});

const mapStateToProps = state => {
    return {
        internetConnected: state.loginReducer.internetConnected,
        salespersonDetail: state.loginReducer.salespersonDetail,
        authDetail: state.loginReducer.authDetail,
        salesHandler: state.loginReducer.salespersonDetail,
        noOfDays: state.salesHandlerReducer.noOfDays,
        salesHandlerSummaryLoading: state.salesHandlerReducer.salesHandlerSummaryLoading,
        salesHandlerSummaries: state.salesHandlerReducer.salesHandlerSummaries,
        pendingOrders: state.salesHandlerReducer.pendingOrders,
        orderCountDiff: state.salesHandlerReducer.orderCountDiff,
        metadata: state.salesHandlerReducer.metadata,
        orderReceivedData: state.firebaseReducer.orderReceivedData,
        notifiedOrderID: state.firebaseReducer.notifiedOrderID,
        salesTargetsLoading: state.targetsReducer.salesTargetsLoading,
        latestAppUpdates: state.firebaseReducer.latestAppUpdates
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setNotifiedOrderID: (notifiedOrderID) => dispatch({type: "SET_NOTIFIED_ORDER_ID", payload: notifiedOrderID}),
        fetchHandlerSummary: (salesHandlerId, noOfDays, withLoader) => dispatch(SalesHandlerActions.fetchHandlerSummary(salesHandlerId, noOfDays, withLoader)),
        showTeamDetail: (handlerSummary, props) => dispatch(SalesHandlerActions.showTeamDetail(handlerSummary, props)),
        updateNoOfDays: (count, props) => dispatch(SalesHandlerActions.updateNoOfDays(count, props)),
        refreshHandlerOrders: (salesHandlerId, orderCount, noOfDays) => dispatch(SalesHandlerActions.setRefreshHandlerOrders(salesHandlerId, orderCount, noOfDays)),
        updateOrderStatus: (orderId, unitId, status, props) => dispatch(SalesHandlerActions.updateOrderStatus(orderId, unitId, status, props)),
        resetNoOfDaysToToday: (noOfDays, salesHandlerId) => dispatch(SalesHandlerActions.resetNoOfDaysToToday(noOfDays, salesHandlerId)),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(SupplierHomeScreen);