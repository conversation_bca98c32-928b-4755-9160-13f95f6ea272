import React, {Component} from "react";
import {FlatList, Platform, StyleSheet, Text, TouchableNativeFeedback, View} from "react-native";

import {connect} from "react-redux";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import * as LoginActions from "../store/actions/LoginActions";


const seniorManagerMenuItems = [
    {id: 1,name: 'drawer.home',route: "Home",icon: "home",bg: "#7B3868"},
    {id: 2,name: 'drawer.manageRetailers',route: "RetailerManagement",icon: "account-group",bg: "#7B3868"},
    {id: 3,name: 'drawer.addSalesHandler',route: "SalesHandlerRegistration",icon: "account-multiple-plus",bg: "#7B3868"},
    {id: 4,name: 'drawer.activityManagement',route: "ActivityManagement",icon: "arrange-bring-forward",bg: "#7B3868"},
    {id: 5,name: 'drawer.changeHandler',route: "ChangeHandlerScreen",icon: "account-switch",bg: "#7B3868"},
    {id: 6,name: 'drawer.changeManager',route: "ChangeManagerScreen",icon: "account-switch",bg: "#7B3868"},
    {id: 7,name: 'drawer.changePassword',route: "ChangePasswordScreen",icon: "key-change",bg: "#7B3868"},
    {id: 8,name: 'drawer.settings',route: "Settings",icon: "settings",bg: "#7B3868"}
];

const areaManagerMenuItems = [
    {id: 1,name: 'drawer.home',route: "Home",icon: "home",bg: "#7B3868"},
    {id: 2,name: 'drawer.manageRetailers',route: "RetailerManagement",icon: "account-group",bg: "#7B3868"},
    {id: 3,name: 'drawer.addSalesHandler',route: "SalesHandlerRegistration",icon: "account-multiple-plus",bg: "#7B3868"},
    {id: 4,name: 'drawer.activityManagement',route: "ActivityManagement",icon: "arrange-bring-forward",bg: "#7B3868"},
    {id: 5,name: 'drawer.audit',route: "AuditForm",icon: "playlist-check",bg: "#7B3868"},
    {id: 6,name: 'drawer.changeHandler',route: "ChangeHandlerScreen",icon: "account-switch",bg: "#7B3868"},
    {id: 7,name: 'drawer.changePassword',route: "ChangePasswordScreen",icon: "key-change",bg: "#7B3868"},
    {id: 8,name: 'drawer.settings',route: "Settings",icon: "settings",bg: "#7B3868"}
];

const saleHandlerMenuItems = [
    {id: 1,name: 'drawer.home',route: "Home",icon: "home",bg: "#7B3868"},
    {id: 2,name: 'drawer.manageRetailers',route: "RetailerManagement",icon: "account-group",bg: "#7B3868"},
    {id: 3,name: 'drawer.activityManagement',route: "ActivityManagement",icon: "arrange-bring-forward",bg: "#7B3868"},
    {id: 4,name: 'drawer.audit',route: "AuditForm",icon: "playlist-check",bg: "#7B3868"},
    {id: 5,name: 'drawer.changePassword',route: "ChangePasswordScreen",icon: "key-change",bg: "#7B3868"},
    {id: 6,name: 'drawer.settings',route: "Settings",icon: "settings",bg: "#7B3868"}
];

const defaultMenuItems = [
    {id: 1,name: 'drawer.home',route: "Home",icon: "home",bg: "#7B3868"},
    {id: 3,name: 'drawer.changePassword',route: "ChangePasswordScreen",icon: "key-change",bg: "#7B3868"},
    {id: 2,name: 'drawer.settings',route: "Settings",icon: "settings",bg: "#7B3868"}
];


const styles = StyleSheet.create({
    drawerCoverContainer: {
        paddingTop:10,
        flex: 0,
        height: 100,
        backgroundColor: '#A3A7B6',
        alignItems: 'center',
        justifyContent: 'center',
    },
    initialsText: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        width: 70,
        height: 70,
        borderRadius: 35,
        fontSize: 50,
        color: '#fff',
        textAlign: 'center',
        marginBottom: 15,
        backgroundColor: '#A3A7B6',
    },
    userName: {
        color: "#FDFBF8",
        fontSize: 18
    },
    listItem: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'stretch',
        justifyContent: 'center',
        padding: 15,
        paddingLeft: 20,
    },
    icon: {
        color: "#7B3868",
        fontSize: 26,
        width: 30
    },
    text: {
        color: "#777",
        fontWeight: Platform.OS === "ios" ? "500" : "400",
        fontSize: 18,
        marginLeft: 15,
        flex: 1
    },
    badgeText: {
        fontSize: Platform.OS === "ios" ? 13 : 11,
        fontWeight: "400",
        textAlign: "center",
        marginTop: Platform.OS === "android" ? -3 : undefined
    },
    btn: {
        backgroundColor: '#d4272a',
        marginLeft:10,
        marginRight:10,
        marginBottom:10,
        padding: 10,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center'
    },
    btnText:{
        color:"#FDFBF8",
        fontSize: 18
    },
    footerItem: {
        borderBottomWidth: 1,
        borderBottomColor: '#efefef'
    }

});

class DrawerSidebar extends Component {

    constructor(props) {
        super(props);
        this.state = {
            shadowOffsetWidth: 1,
            shadowRadius: 4
        };
    }

    navigate(route) {
        this.props.navigation.navigate(route);
        this.props.navigation.closeDrawer();
    }

    getMenuItems(){
        if(this.props.salespersonDetail.designation === "Territory Sales Executive"){
            return  saleHandlerMenuItems;
        }
        if(this.props.salespersonDetail.designation === "Area Manager"){
            return areaManagerMenuItems;
        }
        if(this.props.salespersonDetail.designation === "Senior Manager"){
            return seniorManagerMenuItems;
        }
        return defaultMenuItems;
    }

    render() {
        return (
            <View style={{flex:1, alignItems:'stretch', justifyContent:'flex-start'}}>
                <View bounces={false} style={{flex: 1, backgroundColor: "#FDFBF8", top: -1}}>
                    <View style={styles.drawerCoverContainer}>
                        <Text style={styles.initialsText}>
                            {this.props.salespersonDetail.name.substr(0, 1)}
                        </Text>
                        <Text style={styles.userName}>
                            {this.props.salespersonDetail != null ? this.props.salespersonDetail.name : ''}
                        </Text>
                    </View>
                    <FlatList style={{marginBottom: 20}}
                              data={this.getMenuItems()}
                              renderItem={({item}) => (
                                  <TouchableNativeFeedback onPress={() => this.navigate(item.route)}
                                                           background={Platform.Version >= 21 ? TouchableNativeFeedback.Ripple(item.bg) : null}
                                                           delayPressIn={1}>
                                      <View key={item.id} style={styles.listItem}>
                                          <MaterialCommunityIcons active name={item.icon} style={styles.icon}/>
                                          <Text style={styles.text}>
                                              {item.name}
                                          </Text>
                                      </View>
                                  </TouchableNativeFeedback>
                              )}
                              keyExtractor={(item, index) => index.toString()}
                    />
                </View>
            </View>
        );
    }
}

const mapStateToProps = state => {
    return {
        locale: state.loginReducer.locale,
        salespersonDetail: state.loginReducer.salespersonDetail,
        prevRoute: state.loginReducer.prevRoute
    }
};

const mapDispatchToProps = dispatch => {
    return {
        logout: (props) => dispatch(LoginActions.logoutCustomer(props))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(DrawerSidebar);