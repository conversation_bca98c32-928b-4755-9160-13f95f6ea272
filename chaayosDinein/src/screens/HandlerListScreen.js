import React, {Component} from 'react';
import {connect} from 'react-redux';
import {ActivityIndicator, FlatList, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import HeaderView from "./HeaderView";
import Communications from 'react-native-communications';
import UtilityService from "./../store/services/UtilityService";
import * as SalesHandlerActions from "./../store/actions/SalesHandlerActions";
import TargetsView from "./TargetsView";

let Dimensions = require('Dimensions');
let { width, height } = Dimensions.get('window');

class TeamScreen extends Component {

    componentWillMount() {
    }

    componentWillUnmount() {
    }

    getProgressValue(quantity, target){
        let progress = (quantity * .5) / target;
        progress = progress.toPrecision(2);
        progress = parseFloat(progress);
        return progress;
    }

    getDate(currentBusinessDate){
        return UtilityService.formatDate(UtilityService.addDateToDate(( currentBusinessDate != null) ? currentBusinessDate : null, 0), 'dd-MM-yyyy');
    }

    render() {
        return (
            <View style={styles.container}>

                {this.props.salesTargetsLoading ? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#f0af3b"/>
                        <Text style={{color: '#f0af3b'}}>Loading data ...</Text>
                    </View>
                ) : null}

                <HeaderView menuIcon={false} title={'Salesmen'} {...this.props} headerColor={"#4a4e4d"}/>
                <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'space-between', position: 'relative'}}>
                    {this.props.handlersMap != null ? (
                        <FlatList style={{marginBottom: 0}} data={Object.values(this.props.handlersMap)}
                                  renderItem={({item}) => (
                                      <View style={{flex: 1, justifyContent: 'flex-start', alignItems: 'stretch'}}>
                                          <View style={styles.tile}>
                                              <View style={styles.tileHead}>
                                                  <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'space-between'}}>
                                                      <Text style={styles.tileHeadText}>{item.summary.name}</Text>
                                                      <Text style={styles.tileHeadText}>{item.summary.id}</Text>
                                                  </View>

                                                  <View style={{flex: 0, alignItems: 'stretch', justifyContent: 'space-between'}}>
                                                      <TouchableOpacity style={styles.titleButton}
                                                                        onPress={() => this.props.showRetailerList(item.summary.id, this.props)}>
                                                          <Text style={styles.titleBtnText}>Retailers</Text>
                                                      </TouchableOpacity>
                                                      <TouchableOpacity style={styles.titleButton}
                                                                        onPress={() => Communications.phonecall(item.summary.contact, true)}>
                                                          <Text style={styles.titleBtnText}>Call</Text>
                                                      </TouchableOpacity>
                                                  </View>
                                              </View>
                                              <View style={styles.tileBody}>

                                                  <View style={styles.summaryContainer}>
                                                      <View style={styles.summaryRow}>
                                                          <View style={styles.summaryCell}>
                                                              <Text style={styles.summaryCellValue}>{item.summary.orderCount}</Text>
                                                              <Text style={styles.summaryCellTag}>Total Orders</Text>
                                                          </View>
                                                          <View style={styles.summaryCell}>
                                                              <Text style={styles.summaryCellValue}>{item.summary.retailerCount}</Text>
                                                              <Text style={styles.summaryCellTag}>Total Retailers</Text>
                                                          </View>
                                                      </View>
                                                      <View style={styles.summaryRow}>
                                                          <View style={styles.summaryCell}>
                                                              <Text style={styles.summaryCellValue}>{item.summary.newReceivedOrderCount}</Text>
                                                              <Text style={styles.summaryCellTag}>Orders to Accept</Text>
                                                          </View>
                                                          <View style={styles.summaryCell}>
                                                              <Text style={styles.summaryCellValue}>{item.summary.pendingOrderCount}</Text>
                                                              <Text style={styles.summaryCellTag}>Pending Delivery</Text>
                                                          </View>
                                                          <View style={styles.summaryCell}>
                                                              <Text style={styles.summaryCellValue}>{item.summary.cancelRequestOrderCount}</Text>
                                                              <Text style={styles.summaryCellTag}>Cancel Requests</Text>
                                                          </View>
                                                          <View style={styles.summaryCell}>
                                                              <Text style={styles.summaryCellValue}>{item.summary.deliveredOrderCount}</Text>
                                                              <Text style={styles.summaryCellTag}>Delivered Orders</Text>
                                                          </View>
                                                      </View>
                                                  </View>

                                                  {/*Targets Container starts*/}
                                                  <TargetsView openedTargetType={'NONE'}
                                                               handlerId={item.summary.id}
                                                               date={this.getDate(item.summary.currentBusinessDate)}
                                                               isCurrentDay = {this.props.noOfDays === 0 ? true : false}
                                                               {...this.props} />
                                                  {/*Targets Container ends*/}

                                              </View>
                                          </View>
                                      </View>
                                  )}
                                  keyExtractor={(item, index) => index.toString()}
                        />


                    ) : null}
                    {this.props.handlersMap == null ? (
                        <Text>No data available. Please contact your manager.</Text>
                    ):null}
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#EFEFEF'
    },
    tile: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        margin: 10,
        borderWidth: 1,
        borderColor: '#CCC',
        backgroundColor: '#FDFBF8',
        borderRadius: 4
    },
    tileHead: {
        flex: 0,
        flexDirection: 'row',
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        backgroundColor: '#B66868',
        padding: 2,
        borderRadius: 4
    },
    tileHeadText: {
        padding: 5,
        color: '#FDFBF8',
        textShadowColor: 'rgba(0, 0, 0, 0.75)',
        textShadowOffset: {width: -1, height: 1},
        textShadowRadius: 10
    },
    titleButton: {
        backgroundColor: '#FDFBF8',
        borderWidth: 1,
        borderColor: '#777',
        borderRadius: 5,
        padding: 3,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 2,
        elevation: 5
    },
    titleBtnText: {color: '#B66868', textAlign: 'center', fontSize: 12, fontWeight: 'bold'},
    tileBody: {
        padding: 3,
        backgroundColor: '#FDFBF8',
        borderRadius: 4
    },
    target: {
        flex: 0,
        flexDirection: 'row',
        alignItems: 'stretch',
        justifyContent: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#EFEFEF',
        marginBottom: 5
    },
    targetLeft: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        borderRightWidth: 1,
        borderRightColor: '#EFEFEF'
    },
    targetRight:{
        flex: 1,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        padding: 3
    },
    summaryContainer:{
        flex:0,
        alignItems:'stretch',
        justifyContent:'flex-start'
    },
    summaryRow: {
        flex:0,
        flexDirection:'row',
        alignItems: 'flex-start',
        justifyContent:'flex-start'
    },
    summaryCell:{
        flex: 1,
        borderWidth:2,
        borderColor:'#B66868',
        borderRadius: 5,
        margin:2,
        backgroundColor: "#e6e6ea",
        elevation: 2
    },
    summaryCellValue:{
        textAlign:'center',
        color:'#000',
        fontSize: 12
    },
    summaryCellTag:{
        textAlign:'center',
        color:'#000',
        fontSize: 10
    },
    loaderContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: 'black',
        width: width,
        height: height,
        zIndex: 1
    }
});

const mapStateToProps = state => {
    return {
        handlersMap: state.salesHandlerReducer.handlersMap,
        salesTargetsLoading: state.salesHandlerReducer.salesTargetsLoading
    }
};

const mapDispatchToProps = dispatch => {
    return {
        showRetailerList: (handlerId, props) => dispatch(SalesHandlerActions.showRetailerList(handlerId, props)),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(TeamScreen);