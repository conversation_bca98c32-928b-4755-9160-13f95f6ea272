import React, {Component} from 'react';
import {connect} from 'react-redux';
import {StyleSheet, Text, FlatList, View, TouchableOpacity, Alert} from 'react-native';
import HeaderView from "./HeaderView";
import UtilityService from "./../store/services/UtilityService";
import * as SalesHandlerActions from "./../store/actions/SalesHandlerActions";
import AntDesignIcon from "react-native-vector-icons/AntDesign";
import SimpleLineIcons from "react-native-vector-icons/SimpleLineIcons";
import firebaseReducer from "../store/reducers/FirebaseReducer";

class OrderListScreen extends Component {

    alertCancelOrder(item){
        const prop = this.props;
        Alert.alert(
            'Cancel Order',
            'Are you sure you want to cancel the order?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        this.props.updateOrderStatus(item.orderId, item.unitId, "CANCELLED", this.props)
                    } },
            ],
            { cancelable: false }
        )
    }

    render() {
        return (
            <View style={styles.container}>
                <HeaderView menuIcon={false} title={'Orders'} {...this.props} headerColor={"#4a4e4d"}/>
                <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'space-between', position: 'relative'}}>
                    {this.props.ordersList != null ? (
                        <FlatList style={{marginBottom: 0}} data={this.props.ordersList}
                                  renderItem={({item}) => (
                                      <View key={item.id} style={{flex: 1, justifyContent: 'flex-start', alignItems: 'stretch'}}>
                                          <View style={item.retailerStatus === "IN_ACTIVE" ? styles.tileDeactivated : styles.tile }>
                                              <View style={styles.tileBody}>
                                                  <View style={{flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                                      <Text style={{fontSize:10, marginBottom:5}}>{item.generatedOrderId}</Text>
                                                      {item.status === "INITIATED" ? (<Text style={{fontSize:12, color:'#737373', marginBottom:5}}>Order Received</Text>) :null}
                                                      {item.status === "SETTLED" ? (<Text style={{fontSize:12, color:'#f0af3b', marginBottom:5}}>Pending Delivery</Text>) :null}
                                                      {item.status === "DELIVERED" ? (<Text style={{fontSize:12, color:'green', marginBottom:5}}>Delivered</Text>) :null}
                                                      {item.status === "CANCELLED_REQUESTED" ? (<Text style={{fontSize:12, color:'#ff3377', marginBottom:5}}>Cancel requested</Text>) :null}
                                                      {item.status === "CANCELLED" ? (<Text style={{fontSize:12, color:'#ff3377', marginBottom:5}}>Cancelled</Text>) :null}
                                                  </View>
                                                  <Text style={{fontSize:12, color:'#000', marginBottom:5}}>{item.orderSummary}</Text>
                                                  <Text style={{fontSize:12, color:'#000', marginBottom:5}}>Amount : {item.amount}</Text>
                                                  <Text style={{fontSize:12, color:'#000', marginBottom:5}}>Ordered By : {item.retailerName}</Text>
                                                  <Text style={{fontSize:12, color:'#000', marginBottom:5}}>Retailer Contact : {item.retailerContact}</Text>



                                                  <View style={{flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                                      <Text style={{fontSize:12, color:'#000', marginBottom:5}}>
                                                          {UtilityService.formatDate(item.orderDate, 'dd MMM yyyy at hh:mm A')}
                                                      </Text>
                                                      {item.status === "INITIATED" ? (
                                                          <View style={{flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                                              <TouchableOpacity style={{padding:5,
                                                                  marginLeft: 35,
                                                                  paddingLeft: 15,
                                                                  paddingRight: 15,
                                                                  backgroundColor:'#25A1AC',
                                                                  borderRadius: 4,
                                                                  borderWidth: 1,
                                                                  borderColor:"#25A1AC",
                                                                  elevation:3}}
                                                                                onPress={() => this.props.updateOrderStatus(item.orderId, item.unitId, "SETTLED", this.props)}>
                                                                  <Text style={{fontSize:10, color:'#FDFBF8'}}>Accept</Text>
                                                              </TouchableOpacity>
                                                              <TouchableOpacity style={{padding:5,
                                                                  paddingLeft: 15,
                                                                  paddingRight: 15,
                                                                  backgroundColor:'#D53943',
                                                                  borderRadius: 4,
                                                                  borderWidth: 1,
                                                                  borderColor:"#D53943",
                                                                  elevation:3}}
                                                                        onPress={() => this.alertCancelOrder(item)}>
                                                                  <Text style={{fontSize:10, color:'#FDFBF8'}}>Cancel</Text>
                                                              </TouchableOpacity>
                                                          </View>
                                                      ) : null}
                                                      {item.status === "SETTLED" ? (
                                                          <TouchableOpacity style={styles.orderDeliveredBtn}
                                                                            onPress={() => this.props.updateOrderStatus(item.orderId, item.unitId, "DELIVERED", this.props)}>
                                                              <Text style={{fontSize:10, color:'#FDFBF8'}}>Delivered</Text>
                                                          </TouchableOpacity>
                                                      ) : null}
                                                      {item.status === "CANCELLED_REQUESTED" ? (
                                                          <TouchableOpacity style={styles.orderDeliveredBtn}
                                                                            onPress={() => this.props.updateOrderStatus(item.orderId, item.unitId, "CANCELLED", this.props)}>
                                                              <Text style={{fontSize:10, color:'#FDFBF8'}}>Confirm</Text>
                                                          </TouchableOpacity>
                                                      ) : null}
                                                  </View>
                                              </View>
                                          </View>
                                      </View>
                                  )}
                                  keyExtractor={(item, index) => index.toString()}
                        />
                    ) : null}
                    {this.props.ordersList == null ? (
                        <Text style={{textAlign: 'center'}}>No data available. Please contact your manager.</Text>
                    ) : null}
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    deliveredIcon: {
        fontSize: 15,
        color: '#009933',
        textAlign: 'center',
        marginRight: 10
    },
    cancelledIcon: {
        fontSize: 15,
        color: '#ff3377',
        textAlign: 'center',
        marginRight: 10
    },
    activeIcon: {
        fontSize: 15,
        color: '#eee',
        textAlign: 'center',
        marginRight: 10
    },
    container: {
        flex: 1,
        backgroundColor: '#777'
    },
    tile: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        margin: 10,
        borderWidth: 1,
        borderColor: '#ececec',
        backgroundColor: '#FDFBF8',
        borderRadius: 4,
        padding:10
    },
    tileBody: {
        padding: 3,
        backgroundColor: '#FDFBF8',
        borderRadius: 4
    },
    tileDeactivated: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        margin: 10,
        borderWidth: 1,
        borderColor: '#ececec',
        backgroundColor: '#ff3377',
        borderRadius: 4,
        padding:10
    },
    tileBodyHighlighted: {
        padding: 3,
        backgroundColor: '#ff3377',
        borderRadius: 4
    },
    orderCreatedBtn:{
        padding:5,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 5,
        backgroundColor:'#f0af3b',
        borderRadius: 4
    },
    orderDeliveredBtn:{
        padding:5,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 5,
        backgroundColor:'#25A1AC',
        borderRadius: 4,
        borderWidth: 1,
        borderColor:"#25A1AC",
        elevation:3
    }
});

const mapStateToProps = state => {
    return {
        ordersList: state.salesHandlerReducer.ordersList,
        noOfDays: state.salesHandlerReducer.noOfDays,
        salesHandler: state.loginReducer.salespersonDetail,
        orderReceivedData: state.firebaseReducer.orderReceivedData,
        notifiedOrderID: state.firebaseReducer.notifiedOrderID
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setOrderReceivedData: (orderReceivedData) => dispatch({type: "SET_ORDER_RECEIVED_DATA", payload: orderReceivedData}),
        setNotifiedOrderID: (notifiedOrderID) => dispatch({type: "SET_NOTIFIED_ORDER_ID", payload: notifiedOrderID}),
        putItemTOTop: (ordersList, notifiedOrderID) => dispatch(SalesHandlerActions.putItemTOTop(ordersList, notifiedOrderID)),
        updateOrderStatus: (orderId, unitId, status, props) => dispatch(SalesHandlerActions.updateOrderStatus(orderId, unitId, status, props)),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(OrderListScreen);