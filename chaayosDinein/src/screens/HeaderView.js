import React, {Component} from 'react';
import {TouchableHighlight, Text, Header} from "react-native";
import {connect} from "react-redux";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
//import {Header, Left, Right, Icon, Body, Title, Subtitle} from "native-base";

class HeaderView extends Component {

    static navigationOptions = {};

    toggleDrawer = () => {
        this.props.navigation.toggleDrawer()
    };

    goBack() {
        if(this.props.backHandler != null){
            this.props.backHandler();
        } else {
            if (this.props.backScreen != null) {
                this.props.navigation.navigate(this.props.backScreen);
            } else {
                this.props.navigation.goBack();
            }
        }
    }

    render() {
        return (
            <Header statusBarProps={{ barStyle: 'light-content', backgroundColor:this.props.headerColor }}
                    containerStyle={{backgroundColor: this.props.headerColor, justifyContent: 'space-around'}}>
                <View style={{flex: 0}}>
                    {this.props.menuIcon ? (
                        <TouchableHighlight style={{borderRadius:55}} onPress={this.toggleDrawer} underlayColor="#EAC14F">
                            <MaterialCommunityIcons ios="menu" android="md-menu" style={{fontSize: 20, color: 'white', padding: 15}}/>
                        </TouchableHighlight>

                    ) : (
                        <TouchableHighlight style={{borderRadius:55}} onPress={() => this.goBack()} underlayColor="#EAC14F">
                            <MaterialCommunityIcons ios="ios-arrow-back" android="md-arrow-back"
                                  style={{fontSize: 20, color: 'white', padding: 15}}/>
                        </TouchableHighlight>
                    )}
                </View>
                <View>
                    <View style={{paddingLeft: 3}}>
                        {this.props.title}
                    </View>
                    {this.props.body != null ? (
                        <View>{this.props.customerDetail != null ? this.props.customerDetail.name : ''}</View>
                    ):null}
                </View>
                <View style={{flex: 0}} />
            </Header>
        );
    }
}

const styles = {
    header: {
        backgroundColor: '#f0af3b'
    }
};

const mapStateToProps = state => {
    return {
        //customerDetail: state.loginReducer.customerDetail
    }
};

const mapDispatchToProps = dispatch => {
    return {
        //lookupCustomer: (mobileNumber) => dispatch(LoginActions.lookupCustomer(mobileNumber)),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(HeaderView);
