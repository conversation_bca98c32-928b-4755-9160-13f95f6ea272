import React, {Component} from 'react';
import {connect} from 'react-redux';
import {
    StyleSheet,
    Text,
    View,
    ScrollView,
    Image,
    TextInput,
    TouchableOpacity,
    BackHandler,
    PixelRatio,
    Alert,
    KeyboardAvoidingView,
    ActivityIndicator,
    Picker
} from 'react-native';
import PhotoUpload from 'react-native-photo-upload';
import HeaderView from "./HeaderView";
import * as RetailerRegistrationActions from "./../store/actions/RetailerRegistrationActions";
import MaterialIcon from "react-native-vector-icons/MaterialIcons";
import SimpleLineIcons from "react-native-vector-icons/SimpleLineIcons";
import EntypoIcon from "react-native-vector-icons/Entypo";
const default_avatar = require("./../assets/img/profileAvatar.png");
import NavigationService from "./../store/services/NavigationService";
var Dimensions = require('Dimensions');
var { width, height } = Dimensions.get('window');


class RetailerRegistrationScreen extends Component {

    constructor(props) {
        super(props);
        this.handleBackButtonClick = this.handleBackButtonClick.bind(this);
    }

    componentWillMount() {
        this.props.setIsContactViewEnabled(true);
        this.props.setIsFormViewEnabled(false);
        this.props.setErrorRegistration(false);
        this.props.setSuccessRegistration(false);
        this.props.resetFormFields();
        this.props.resetErrorMessages();
        BackHandler.addEventListener('hardwareBackPress', this.handleBackButtonClick);
    }

    componentWillUnmount() {
        BackHandler.removeEventListener('hardwareBackPress', this.handleBackButtonClick);
    }

    handleBackButtonClick() {
        this.props.navigation.goBack(null);
        return true;
    }

    ifValidPhoneNumber(input) {
        this.props.setIsValidContactNo(true);
        this.props.setContactAlreadyExists(false);
        if (!(input.match(/^[0-9]*$/))) {
            this.props.setIsValidContactNo(false);
            this.props.setIsContactViewEnabled(true);
            this.props.setIsFormViewEnabled(false);
        }
        if(input.match(/^[6-9]\d{9}$/)){
            this.props.lookupRetailer(input);
        }
        if(input.match(/^[0-5]\d{9}$/)){
            this.props.setIsValidContactNo(false);
            this.props.setIsContactViewEnabled(true);
            this.props.setIsFormViewEnabled(false);
        }
    }

    validateAndRegister(){
        let targetValue = 0;
        let targetType = "";
        if(this.props.targets != null
            && this.props.target != null
                && this.props.target !== 'NO_TARGET'
                    && this.props.target !=='TARGET_OTHER'){
            for (var i = 0; i < this.props.targets.length; i++) {
                var obj = this.props.targets[i];
                if(obj.code === this.props.target){
                    targetType = obj.code;
                    targetValue = parseInt(obj.value);
                }
            }
        }
        if(this.props.customTarget!=null){
            targetType = "TARGET_OTHER";
            targetValue = this.props.customTarget;
        }
        let data = {
            "firstName": this.props.firstName,
            "lastName": this.props.lastName,
            "email": this.props.email,
            "salesDiaryId": this.props.salesDiaryId,
            "contact": this.props.contact,
            "addressLineOne": this.props.addressLineOne,
            "addressLineTwo": this.props.addressLineTwo,
            "city": this.props.city,
            "zipCode": this.props.zipCode,
            "stateName": this.props.stateName,
            "country": this.props.country,
            "targetType": targetType,
            "targetValue": targetValue,
            "orderFrequency": this.props.orderFrequency
        };
       this.props.validateAndRegister(data, this.props);
    }

    alertBack(){
        const prop = this.props;
        Alert.alert(
            'Go Back',
            'Are you sure you want to discard your changes?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                    NavigationService.navigate('ManageRetailers');
                } },
            ],
            { cancelable: false }
        )
    }

    addAnotherRetailer(){
        this.props.setIsContactViewEnabled(true);
        this.props.setIsFormViewEnabled(false);
        this.props.setSuccessRegistration(false)
    }

    tryAgain(){
        this.props.setIsContactViewEnabled(true);
        this.props.setIsFormViewEnabled(false);
        this.props.setErrorRegistration(false)
    }



    render() {
        return (
            <View style={styles.container}>
                {this.props.showLoader ? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#f0af3b"/>
                        <Text style={{color: '#f0af3b'}}>Please wait ...</Text>
                    </View>
                ) : null}
                <HeaderView menuIcon={false}
                            title={'Add Retailer'}
                            backHandler={ (this.props.contact != null && this.props.contact !== "") ? this.alertBack : null }
                            {...this.props} headerColor={"#4a4e4d"}/>
                <KeyboardAvoidingView style={styles.keyboardContainer}>
                    <ScrollView style={styles.container}>
                        {/*Contact view Starts*/}
                        { this.props.isContactViewEnabled ? (
                                <View style={styles.enterContactContainer}>
                                    <MaterialIcon name='contact-phone' style={styles.icon}/>
                                    <Text style={styles.alertTextStyle}> Please enter the contact details below: </Text>
                                    <View style={{
                                        marginLeft: 50,
                                        marginRight: 50,
                                        flex: 1,
                                        flexDirection:'row',
                                        flexWrap:'wrap',
                                        borderColor: "#000",
                                        borderBottomWidth: 1,
                                        marginBottom: 8
                                    }}>
                                        <Text style={{fontSize: 20, backgroundColor:'#eee', paddingTop:8 }}> +91 </Text>
                                        <TextInput
                                            style={styles.inputContactBox}
                                            placeholder="Contact"
                                            keyboardType="number-pad"
                                            underlineColorAndroid='transparent'
                                            placeholderTextColor='#999'
                                            maxLength={10}
                                            onChangeText={(contact) => this.ifValidPhoneNumber(contact)}
                                        />
                                    </View>
                                    { this.props.contactAlreadyExists == true ? (
                                        <Text style={styles.alertAlreadyExistTextStyle}> This number already exists in the database! </Text>
                                    ): null }
                                    { this.props.isValidContactNo == false ? (
                                        <Text style={styles.alertAlreadyExistTextStyle}> This mobile number is of invalid format! </Text>
                                    ): null }
                                </View>
                        ) : null}
                        {/*Contact view Ends*/}
                        {/*Error Info view Starts*/}
                        {this.props.errorRegistration ? (
                                <View style={styles.errorAddingContainer}>
                                    <MaterialIcon name='error' style={styles.errorIcon}/>
                                    <Text style={styles.alertTextStyle}> Some error occured, Please Try Again! </Text>
                                    <View style={{flex: 1, alignItems: 'stretch'}}>
                                        <TouchableOpacity style={styles.buttonStyle}
                                                          onPress={() => {
                                                              this.tryAgain()}
                                                          }>
                                            <Text style={styles.buttonTitleStyle}>Try Again</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                        ):null}
                        {/*Error Info view Ends*/}
                        {/*Success Info view Starts*/}
                        {this.props.successRegistration ? (
                                <View style={styles.successAddingContainer}>
                                    <MaterialIcon name='check-box' style={styles.successIcon}/>
                                    <Text style={styles.alertTextStyle}> Retailer Added Successfully! </Text>
                                    <View style={{flex: 1, alignItems: 'stretch'}}>
                                        <TouchableOpacity style={styles.buttonStyle}
                                                          onPress={() => {this.addAnotherRetailer()}
                                                          }>
                                            <Text style={styles.buttonTitleStyle}>Add Another</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                        ):null}
                        {/*Success Info view Ends*/}
                        {/*Form view Starts*/}
                        { this.props.isFormViewEnabled ? (
                                <View style={styles.formContainer}>
                                    <PhotoUpload onPhotoSelect={avatar => {}}>
                                        <Image style={styles.imageStyle} resizeMode='cover' source={default_avatar} />
                                    </PhotoUpload>

                                    <View style={styles.inputGroup}>
                                        <View style={styles.inputContainer}>
                                            <MaterialIcon name='person-outline' style={styles.formIcon}/>
                                            <TextInput
                                                style={styles.input}
                                                placeholder="Shop Name"
                                                keyboardType="default"
                                                underlineColorAndroid='transparent'
                                                placeholderTextColor='#999'
                                                value={this.props.firstName}
                                                onChangeText={(firstName) => this.props.setFirstName(firstName)}
                                            />
                                        </View>
                                        {/*<View style={styles.inputContainer}>
                                            <MaterialIcon name='person-outline' style={styles.formIcon}/>
                                            <TextInput
                                                style={styles.input}
                                                placeholder="LastName"
                                                keyboardType="default"
                                                underlineColorAndroid='transparent'
                                                placeholderTextColor='#999'
                                                value={this.props.lastName}
                                                onChangeText={(lastName) => this.props.setLastName(lastName)}
                                            />
                                        </View>*/}
                                    </View>
                                    {this.props.isFirstNameMissing?(<Text style={styles.errorText}>*First Name cannot be blank</Text>) : null}
                                    {this.props.isFirstNameInvalid?(<Text style={styles.errorText}>*First Name should only have alphabets</Text>) : null}

                                    <View style={styles.inputContainer}>
                                        <MaterialIcon name='phone' style={styles.formIcon}/>
                                        <TextInput
                                            style={styles.input}
                                            placeholder="Contact"
                                            keyboardType="number-pad"
                                            underlineColorAndroid='transparent'
                                            placeholderTextColor='#999'
                                            editable={false}
                                            value={this.props.contact}
                                            onChangeText={(contact) => this.props.lookupRetailer(contact)}
                                        />
                                    </View>
                                    <View style={styles.inputContainer}>
                                        <MaterialIcon name='book' style={styles.formIcon}/>
                                        <TextInput
                                            style={styles.input}
                                            placeholder="Sale Diary Id"
                                            keyboardType="number-pad"
                                            underlineColorAndroid='transparent'
                                            placeholderTextColor='#999'
                                            maxLength={10}
                                            value={this.props.salesDiaryId}
                                            onChangeText={(salesDiaryId) => this.props.setSalesDiaryId(salesDiaryId)}
                                        />
                                    </View>
                                    {this.props.isDuplicateSalesDiaryId?(<Text style={styles.errorText}>*Retailer already exists with this Sales Diary Id</Text>) : null}
                                    {this.props.isSalesDiaryIdMissing?(<Text style={styles.errorText}>*Sales Diary Id cannot be blank</Text>) : null}




                                    { (this.props.targets != null) ? (
                                        <View>
                                            <View style={{marginTop:15}}>
                                                <Text style={{marginBottom:10}}>Retailer Targets </Text>
                                                <Picker
                                                    style={this.props.target == null ? {backgroundColor:"#CCC", marginBottom:5} : {backgroundColor:"#66ccff", marginBottom:5}}
                                                    mode="dropdown"
                                                    selectedValue={this.props.target}
                                                    onValueChange={(target) => this.props.setTarget(target)}>
                                                    {this.props.targets != null?
                                                        Object.keys(this.props.targets).map((key) => {
                                                            return (
                                                                <Picker.Item  label={this.props.targets[key].name}
                                                                              value={this.props.targets[key].code} key={key}/>
                                                            )
                                                        })
                                                        : null}
                                                </Picker>
                                            </View>
                                            {this.props.isTargetMissing?(<Text style={styles.errorText}>*Please provide sales target for retailer.</Text>) : null}
                                        </View>
                                    ):null}

                                    { (this.props.targets != null && this.props.target === 'TARGET_OTHER') ? (
                                        <View>
                                            <View style={styles.inputContainer}>
                                                <SimpleLineIcons name='target' style={styles.formIcon}/>
                                                <TextInput
                                                    style={styles.input}
                                                    placeholder="Target Value in Kilograms"
                                                    keyboardType="number-pad"
                                                    underlineColorAndroid='transparent'
                                                    placeholderTextColor='#999'
                                                    maxLength={4}
                                                    value={this.props.customTarget}
                                                    onChangeText={(customTarget) => this.props.setCustomTarget(customTarget)}
                                                />
                                            </View>
                                            {this.props.isOrderFrequencyMissing?(<Text style={styles.errorText}>*Please provide order frequency of the retailer.</Text>) : null}
                                        </View>
                                    ):null}



                                    { (this.props.orderFrequencies != null) ? (
                                        <View>
                                            <View style={{marginTop:15}}>
                                                <Text style={{marginBottom:10}}>Retailer Order Frequencies </Text>
                                                <Picker
                                                    style={this.props.orderFrequency == null ? {backgroundColor:"#CCC", marginBottom:5} : {backgroundColor:"#66ccff", marginBottom:5}}
                                                    mode="dropdown"
                                                    selectedValue={this.props.orderFrequency}
                                                    onValueChange={(orderFrequency) => this.props.setOrderFrequency(orderFrequency)}>
                                                    {this.props.orderFrequencies != null?
                                                        Object.keys(this.props.orderFrequencies).map((key) => {
                                                            return (
                                                                <Picker.Item  label={this.props.orderFrequencies[key].name} value={this.props.orderFrequencies[key].value} key={key}/>
                                                            )
                                                        })
                                                        : null}
                                                </Picker>
                                            </View>
                                            {this.props.isOrderFrequencyMissing?(<Text style={styles.errorText}>*Please provide order frequency of the retailer, Minimum 1 order per day</Text>) : null}
                                        </View>
                                    ):null}





                                    {this.props.salespersonDetail!=null && this.props.salespersonDetail.designation !== "Territory Sales Executive" ?(
                                        <View>
                                            <View style={styles.inputContainer}>
                                                <MaterialIcon name='email' style={styles.formIcon}/>
                                                <TextInput
                                                    style={styles.input}
                                                    placeholder="Email"
                                                    keyboardType="default"
                                                    underlineColorAndroid='transparent'
                                                    placeholderTextColor='#999'
                                                    autoCapitalize = 'none'
                                                    value={this.props.email}
                                                    onChangeText={(email) => this.props.setEmail(email)}
                                                />
                                            </View>
                                            {this.props.isEmailInvalid?(<Text style={styles.errorText}>*Please enter a valid email address</Text>) : null}
                                            <View style={styles.inputContainer}>
                                                <EntypoIcon name='address' style={styles.formIcon}/>
                                                <TextInput
                                                    style={styles.input}
                                                    placeholder="*Address Line 1"
                                                    keyboardType="default"
                                                    underlineColorAndroid='transparent'
                                                    placeholderTextColor='#999'
                                                    value={this.props.addressLineOne}
                                                    onChangeText={(addressLineOne) => this.props.setAddressLineOne(addressLineOne)}
                                                />
                                            </View>
                                            {this.props.isAddressMissing?(<Text style={styles.errorText}>*Address cannot be blank</Text>) : null}
                                            <View style={styles.inputContainer}>
                                                <EntypoIcon name='address' style={styles.formIcon}/>
                                                <TextInput
                                                    style={styles.input}
                                                    placeholder="Address Line 2"
                                                    keyboardType="default"
                                                    underlineColorAndroid='transparent'
                                                    placeholderTextColor='#999'
                                                    value={this.props.addressLineTwo}
                                                    onChangeText={(addressLineTwo) => this.props.setAddressLineTwo(addressLineTwo)}
                                                />
                                            </View>
                                            <View style={styles.inputGroup}>
                                                <View style={styles.inputContainer}>
                                                    <MaterialIcon name='location-city' style={styles.formIcon}/>
                                                    <TextInput
                                                        style={styles.input}
                                                        placeholder="*City"
                                                        keyboardType="default"
                                                        underlineColorAndroid='transparent'
                                                        placeholderTextColor='#999'
                                                        value={this.props.city}
                                                        onChangeText={(city) => this.props.setCity(city)}
                                                    />
                                                </View>
                                                <View style={styles.inputContainer}>
                                                    <MaterialIcon name='confirmation-number' style={styles.formIcon}/>
                                                    <TextInput
                                                        style={styles.input}
                                                        placeholder="ZipCode"
                                                        keyboardType="number-pad"
                                                        underlineColorAndroid='transparent'
                                                        placeholderTextColor='#999'
                                                        maxLength={6}
                                                        value={this.props.zipCode}
                                                        onChangeText={(zipCode) => this.props.setZipCode(zipCode)}
                                                    />
                                                </View>
                                            </View>
                                            {this.props.isZipCodeMissing?(<Text style={styles.errorText}>*ZipCode cannot be blank</Text>) : null}
                                            {this.props.isZipCodeInvalid?(<Text style={styles.errorText}>*ZipCode should have 6 numeric digits</Text>) : null}
                                            {this.props.isCityMissing?(<Text style={styles.errorText}>*City cannot be blank</Text>) : null}
                                            {this.props.isCityInvalid?(<Text style={styles.errorText}>*City should only have alphabets</Text>) : null}
                                            <View style={styles.inputGroup}>
                                                <View style={styles.inputContainer}>
                                                    <MaterialIcon name='flag' style={styles.formIcon}/>
                                                    <TextInput
                                                        style={styles.input}
                                                        placeholder="*State"
                                                        keyboardType="default"
                                                        underlineColorAndroid='transparent'
                                                        placeholderTextColor='#999'
                                                        value={this.props.stateName}
                                                        onChangeText={(stateName) => this.props.setStateName(stateName)}
                                                    />
                                                </View>
                                                <View style={styles.inputContainer}>
                                                    <MaterialIcon name='flag' style={styles.formIcon}/>
                                                    <TextInput
                                                        style={styles.input}
                                                        placeholder="Country"
                                                        keyboardType="default"
                                                        underlineColorAndroid='transparent'
                                                        placeholderTextColor='#999'
                                                        editable={false}
                                                        value='India'
                                                        onChangeText={(country) => this.props.setCountry(country)}
                                                    />
                                                </View>
                                            </View>
                                            {this.props.isStateNameMissing?(<Text style={styles.errorText}>*State Name cannot be blank</Text>) : null}
                                        </View>):null}

                                    <View>
                                        <TouchableOpacity onPress={() =>
                                            this.validateAndRegister()}>
                                            <View style={styles.buttonStyle}>
                                                <Text style={styles.buttonTitleStyle}>Add Retailer</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                </View>
                        ):null}
                        {/*Form view Ends*/}
                    </ScrollView>
                </KeyboardAvoidingView>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FFF',
    },
    formContainer: {
        flex: 1,
        backgroundColor: '#FFF',
        paddingLeft: 15,
        paddingRight: 15
    },
    imageStyle:{
        paddingVertical: 30,
        width: 80,
        height: 80,
        borderRadius: 75,
        marginTop: 10,
        marginBottom: 10
    },
    inputGroup:{
        flexDirection:'row',
        flex:1
    },
    inputContainer: {
        marginLeft: 3,
        flex: 1,
        flexDirection:'row',
        flexWrap:'wrap',
        borderColor: "#000",
        borderBottomWidth: 1,
        marginBottom: 8
    },
    enterContactContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff'
    },
    errorAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff'
    },
    successAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff'
    },
    alertTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: '#f0af3b'
    },
    alertAlreadyExistTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: 'red'
    },
    label: {
        color:'#000',
    },
    input: {
        flex:1,
        flexDirection: "row",
        paddingLeft: 5,
        paddingBottom:0
    },
    buttonStyle: {
        flex: 0,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0af3b',
        borderWidth: 1,
        borderRadius: 5,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        borderColor: '#fff',
        marginTop: 20,
        marginBottom:50
    },
    buttonTitleStyle: {
        textDecorationColor: '#fff'
    },
    avatarContainer: {
        borderColor: '#9B9B9B',
        borderWidth: 1 / PixelRatio.get(),
        justifyContent: 'center',
        alignItems: 'center',
    },
    avatar: {
        borderRadius: 75,
        width: 150,
        height: 150,
    },
    inputContactBox: {
        height: 45,
        marginLeft: 10,
        flex: 1,
        color: '#d4272a',
        fontSize: 20
    },
    icon: {
        fontSize: 60,
        color: '#737373',
        textAlign: 'center',
    },
    errorIcon: {
        fontSize: 60,
        color: '#d4272a',
        textAlign: 'center',
    },
    successIcon: {
        fontSize: 60,
        color: '#009933',
        textAlign: 'center',
    },
    formIcon: {
        fontSize: 20,
        color: '#CCC',
        textAlign: 'left',
        paddingTop:12,
        marginRight:0,
        marginBottom:0,
        paddingBottom:-5,
        /*backgroundColor:'#eee'*/

    },
    errorText:{
        marginTop: 5,
        marginBottom: 5,
        justifyContent: 'center',
        alignItems: 'center',
        color: 'red',
        fontSize: 7,
    },
    keyboardContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#d4272a',
    },
    loaderContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: 'black',
        width: width,
        height: height,
        zIndex: 1
    }
});

const mapStateToProps = state => {
    return {
        authDetail: state.loginReducer.authDetail,
        salespersonDetail: state.loginReducer.salespersonDetail,

        firstName: state.retailerRegistrationReducer.firstName,
        lastName: state.retailerRegistrationReducer.lastName,
        email: state.retailerRegistrationReducer.email,
        salesDiaryId: state.retailerRegistrationReducer.salesDiaryId,
        contact: state.retailerRegistrationReducer.contact,
        addressLineOne: state.retailerRegistrationReducer.addressLineOne,
        addressLineTwo: state.retailerRegistrationReducer.addressLineTwo,
        city: state.retailerRegistrationReducer.city,
        zipCode: state.retailerRegistrationReducer.zipCode,
        stateName: state.retailerRegistrationReducer.stateName,
        country: state.retailerRegistrationReducer.country,
        avatarSource: state.retailerRegistrationReducer.avatarSource,

        showRegistrationScreen: state.retailerRegistrationReducer.showRegistrationScreen,
        showLoader: state.retailerRegistrationReducer.showLoader,
        errorRegistration: state.retailerRegistrationReducer.errorRegistration,
        successRegistration: state.retailerRegistrationReducer.successRegistration,
        errorRegistrationMessage: state.retailerRegistrationReducer.errorRegistrationMessage,

        contactAlreadyExists: state.retailerRegistrationReducer.contactAlreadyExists,
        isValidContactNo: state.retailerRegistrationReducer.isValidContactNo,
        isFirstNameMissing: state.retailerRegistrationReducer.isFirstNameMissing,
        isContactMissing: state.retailerRegistrationReducer.isContactMissing,
        isAddressMissing: state.retailerRegistrationReducer.isAddressMissing,
        isCityMissing: state.retailerRegistrationReducer.isCityMissing,
        isZipCodeMissing: state.retailerRegistrationReducer.isZipCodeMissing,
        isZipCodeInvalid: state.retailerRegistrationReducer.isZipCodeInvalid,
        isStateNameMissing: state.retailerRegistrationReducer.isStateNameMissing,
        isFirstNameInvalid: state.retailerRegistrationReducer.isFirstNameInvalid,
        isCityInvalid: state.retailerRegistrationReducer.isCityInvalid,
        isEmailInvalid: state.retailerRegistrationReducer.isEmailInvalid,

        isContactViewEnabled: state.retailerRegistrationReducer.isContactViewEnabled,
        isFormViewEnabled: state.retailerRegistrationReducer.isFormViewEnabled,
        isSalesDiaryIdMissing: state.retailerRegistrationReducer.isSalesDiaryIdMissing,
        isSalesDiaryIdInvalid: state.retailerRegistrationReducer.isSalesDiaryIdInvalid,
        isDuplicateSalesDiaryId: state.retailerRegistrationReducer.isDuplicateSalesDiaryId,

        targets: state.retailerRegistrationReducer.targets,
        target: state.retailerRegistrationReducer.target,
        customTarget: state.retailerRegistrationReducer.customTarget,
        orderFrequencies: state.retailerRegistrationReducer.orderFrequencies,
        orderFrequency: state.retailerRegistrationReducer.orderFrequency,
        isTargetMissing: state.retailerRegistrationReducer.isTargetMissing,
        isOrderFrequencyMissing: state.retailerRegistrationReducer.isOrderFrequencyMissing
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setFirstName: (firstName) => dispatch({type: "SET_FIRST_NAME", payload: firstName}),
        setLastName: (lastName) => dispatch({type: "SET_LAST_NAME", payload: lastName}),
        setEmail: (email) => dispatch({type: "SET_EMAIL", payload: email}),
        setContact: (contact) => dispatch({type: "SET_CONTACT", payload: contact}),
        setSalesDiaryId: (salesDiaryId) => dispatch({type: "SET_SALES_DIARY_ID", payload: salesDiaryId}),
        setAddressLineOne: (addressLineOne) => dispatch({type: "SET_ADDRESS_LINE_ONE", payload: addressLineOne}),
        setAddressLineTwo: (addressLineTwo) => dispatch({type: "SET_ADDRESS_LINE_TWO", payload: addressLineTwo}),
        setCity: (city) => dispatch({type: "SET_CITY", payload: city}),
        setZipCode: (zipCode) => dispatch({type: "SET_ZIP_CODE", payload: zipCode}),
        setStateName: (stateName) => dispatch({type: "SET_STATE_NAME", payload: stateName}),
        setCountry: (country) => dispatch({type: "SET_COUNTRY", payload: country}),
        //setAvatarSource: (avatarSource) => dispatch({type:"SET_AVATAR_SOURCE",payload:avatarSource}),
        validateAndRegister:(retailer ,props) => dispatch(RetailerRegistrationActions.validateAndRegister(retailer,props)),
        setErrorRegistration: (errorRegistration) => dispatch({type: "SET_ERROR_REGISTRATION", payload: errorRegistration}),
        setSuccessRegistration: (successRegistration) => dispatch({type: "SET_SUCCESS_REGISTRATION", payload: successRegistration}),
        lookupRetailer: (contact) => dispatch(RetailerRegistrationActions.lookupRetailer(contact)),
        setContactAlreadyExists: (isExists) => dispatch({type: "SET_CONTACT_ALREADY_EXISTS", payload: isExists}),
        setIsValidContactNo: (isValidContactNo) => dispatch({type: "SET_IS_VALID_CONTACT_NO", payload: isValidContactNo}),
        setIsContactViewEnabled: (isContactViewEnabled) => dispatch({type: "SET_IS_CONTACT_VIEW_ENABLED", payload: isContactViewEnabled}),
        setIsFormViewEnabled: (isFormViewEnabled) => dispatch({type: "SET_IS_FORM_VIEW_ENABLED", payload: isFormViewEnabled}),
        setTarget: (target) => dispatch({type: "SET_TARGET", payload: target}),
        setCustomTarget: (customTarget) => dispatch({type: "SET_CUSTOM_TARGET", payload: customTarget}),
        setOrderFrequency: (orderFrequency) => dispatch({type: "SET_ORDER_FREQUENCY", payload: orderFrequency}),
        resetFormFields: () => dispatch(RetailerRegistrationActions.resetFormFields()),
        resetErrorMessages: () => dispatch(RetailerRegistrationActions.resetErrorMessages())
     }
};

export default connect(mapStateToProps, mapDispatchToProps)(RetailerRegistrationScreen);