import React, {Component} from 'react';
import {connect} from 'react-redux';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import HeaderView from "./HeaderView";
import DeviceInfo from "react-native-device-info";
import * as LoginActions from "./../store/actions/LoginActions";

class SettingsScreen extends Component {

    render() {
        return (
            <View style={styles.container}>
                <HeaderView menuIcon={false} title={i18n.t('settings.settings')} {...this.props} headerColor={"#4a4e4d"}/>
                <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'flex-start'}}>
                    <View style={styles.listItem}>
                        <Text style={styles.title}>{i18n.t('settings.version')}</Text>
                        <Text style={styles.detail}>{DeviceInfo.getVersion()}</Text>
                    </View>
                    <View style={styles.listItem}>
                        <TouchableOpacity style={styles.btn} onPress={() => this.props.logout(this.props)}>
                            <Text style={styles.btnText}>{i18n.t('settings.logout')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FFF',
        alignItems: 'stretch',
        justifyContent: 'flex-start'
    },
    icon: {
        width: 24,
        height: 24,
    },
    listItem: {
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#efefef'
    },
    title: {
        color: "#000",
        marginBottom: 10,
        fontSize: 18
    },
    detail: {
        fontSize: 12
    },
    btn: {
        backgroundColor: '#d4272a',
        padding: 10,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center'
    },
    btnText:{
        color:"#FDFBF8",
        fontSize: 18
    }
});


const mapStateToProps = state => {
    return {
        locale: state.loginReducer.locale
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setLocale: (props, locale) => dispatch(LoginActions.setLocale(props, locale)),
        logout: (props) => dispatch(LoginActions.logoutCustomer(props))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(SettingsScreen);