import React, {Component} from 'react';
import {connect} from 'react-redux';
import {
    StyleSheet,
    Text,
    View,
    ActivityIndicator,
    FlatList,
    ScrollView,
    TouchableOpacity
} from 'react-native';
import HeaderView from "./HeaderView";
import * as ManageActivityActions from "../store/actions/ManageActivityActions";
import UtilityService from "../store/services/UtilityService";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import DateTimePicker from 'react-native-modal-datetime-picker';

var Dimensions = require('Dimensions');
var { width, height } = Dimensions.get('window');

class ManageActivityScreen extends Component {

    constructor(props) {
        super(props);
    }
    componentWillMount() {
        this.props.navigation.addListener(
            'didFocus',
            payload => {
                console.debug('didFocus', payload);
                this.init();
            }
        );

    }

    componentWillUnmount() {
        if (this.props.navigation && this.props.navigation.removeListener) {
            this.props.navigation.removeListener('didFocus');
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.fromDate !== this.props.fromDate && this.props.toDate != null) {
            this.props.setCurrentSalesHandler(null);
            this.props.setCurrentRetailer(null);
            this.props.setSHActivities(null);
            this.props.fetchSalesHandlersForActivityAudit(this.props.salespersonDetail.id, this.props.fromDate, this.props.toDate);
        }
        if (prevProps.toDate !== this.props.toDate && this.props.fromDate != null) {
            this.props.setCurrentSalesHandler(null);
            this.props.setCurrentRetailer(null);
            this.props.setSHActivities(null);
            this.props.fetchSalesHandlersForActivityAudit(this.props.salespersonDetail.id, this.props.fromDate, this.props.toDate);
        }
    }

    init(){
        this.setFromDatePicked(new Date);
        this.setToDatePicked(new Date);
        this.props.fetchSalesHandlersForActivityAudit(this.props.salespersonDetail.id, new Date(), new Date());
    }

    getStatusName(status) {
        if(status!=null && status!==undefined){
            if(status === "INITIATED"){
                return "Initiated";
            }
            if(status === "CREATED"){
                return "Created";
            }
            if(status === "CANCELLED"){
                return "Cancelled";
            }
            if(status === "APPROVED"){
                return "Approved";
            }
            if(status === "REJECTED"){
                return "Rejected";
            }
            if(status === "COMPLETED"){
                return "Completed";
            }
            if(status === "AUDIT_SUCCESS"){
                return "Audit Success";
            }
            if(status === "AUDIT_FAILED"){
                return "Audit Failed";
            }
        }
    }

    isStatusPositive(status) {
        if(status === "CANCELLED" || status === "REJECTED" || status === "AUDIT_FAILED"){
            return false;
        }
        return true;
    }

    async openAndroidDatePicker() {
        try {
            const {action, year, month, day} = await DatePickerAndroid.open({
                date: new Date()
            });
        } catch ({code, message}) {
            console.warn('Cannot open date picker', message);
        }
    }

    setFromDatePicked(date) {
        console.log("from date selected is ", date);
        if(date == null || date === undefined){
            date = new Date();
        }
        this.props.setFromDate(date);
        this.props.setIsFromDatePickerVisible(false);

    }

    setToDatePicked(date) {
        console.log("to date selected is ", date);
        if(date == null || date === undefined){
            date = new Date();
        }
        this.props.setToDate(date);
        this.props.setIsToDatePickerVisible(false);
    }

    getDisplayDate(date) {
        return UtilityService.formatDate(date, 'dd MMM yyyy');
    }

    render() {
        return (
            <View style={styles.container}>
                <HeaderView menuIcon={false} title={'Manage Activities'} backScreen={'Home'} {...this.props} headerColor={"#4a4e4d"}/>
                <View style={{flex: 0,backgroundColor:'#fff',flexDirection:'row',alignItems:'center',justifyContent: 'space-between'}}>
                    <View>
                        <TouchableOpacity  style={this.props.fromDate == null ? styles.auditTimeDateButtonInactive : styles.auditTimeDateButtonActive}
                                           onPress={() => {this.props.setIsFromDatePickerVisible(true)}}>
                            {this.props.fromDate!=null?
                                <View><Text>From: {this.getDisplayDate(this.props.fromDate)}</Text></View>
                                :   <View><Text>From Date</Text></View>}
                        </TouchableOpacity>
                        <DateTimePicker
                            isVisible={this.props.isFromDatePickerVisible}
                            onConfirm={(date) => {this.setFromDatePicked(date)}}
                            onCancel={() => {this.props.setIsFromDatePickerVisible(false)}}
                            maximumDate = {this.props.toDate!=null ? this.props.toDate : new Date()}
                        />
                    </View>
                    <View>
                        <TouchableOpacity  style={this.props.toDate == null ? styles.auditTimeDateButtonInactive : styles.auditTimeDateButtonActive}
                                           onPress={() => {this.props.setIsToDatePickerVisible(true)}}>
                            {this.props.toDate!=null?
                                <View><Text>To: {this.getDisplayDate(this.props.toDate)}</Text></View>
                                :   <View><Text>To Date</Text></View>}
                        </TouchableOpacity>
                        <DateTimePicker
                            isVisible={this.props.isToDatePickerVisible}
                            onConfirm={(date) => {this.setToDatePicked(date)}}
                            onCancel={() => {this.props.setIsToDatePickerVisible(false)}}
                            minimumDate = {this.props.fromDate!=null ? this.props.fromDate : new Date()}
                        />
                    </View>
                    {/*<TouchableOpacity style={{
                        padding:5,
                        paddingLeft: 15,
                        paddingRight: 15,
                        margin: 5,
                        backgroundColor:'#68A4CC',
                        borderRadius: 4
                    }}
                                      onPress={() => this.props.navigation.navigate('AddRetailer')}>
                        <Text style={{color:'#FFF'}}>Add</Text>
                    </TouchableOpacity>*/}
                </View>
                {this.props.showLoader ? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#f0af3b"/>
                        <Text style={{color: '#f0af3b'}}>Loading Activities ...</Text>
                    </View>
                ) : null}
                {this.props.handlers == null ? (
                    <Text style={styles.noDataText}> No activity data found. Please contact your manager. </Text>
                ):null}
                <ScrollView style={{flex: 1}} contentContainerStyle={{alignItems: 'stretch', justifyContent: 'flex-start'}}>

                {/*sales handler activity block starts*/}
                <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'space-between', position: 'relative'}}>
                    {!UtilityService.checkEmpty(this.props.handlers)? (
                        <FlatList style={{marginBottom: 0}} data={Object.values(this.props.handlers)}
                                  renderItem={({item}) => (


                                      <View style={{flex: 0, justifyContent: 'flex-start', alignItems: 'stretch'}}>
                                          <View style={[styles.tile, {marginTop:10}]}>
                                              <TouchableOpacity style={{flex: 1,
                                                  flexDirection: 'row',
                                                  alignItems: 'stretch',
                                                  justifyContent: 'flex-start',
                                                  backgroundColor: '#2ab7ca',
                                                  padding: 2,
                                                  borderRadius: 4}}
                                                                onPress={() => {this.props.fetchActivities(item.salesHandlerId, this.props)}}>
                                                  <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'space-between'}}>
                                                      <Text style={styles.tileHeadText}>{item.salesHandlerId}</Text>
                                                      <Text style={styles.tileHeadText}>{item.salesHandlerName}</Text>
                                                  </View>
                                                  <View style={{flex: 0, alignItems: 'stretch', justifyContent: 'space-between', marginRight:5}}>
                                                      <Text style={styles.tileHeadText}>SH</Text>
                                                  </View>
                                              </TouchableOpacity>


                                              {this.props.salesHandlerActivities!=null && this.props.currentSalesHandler === item.salesHandlerId ?
                                                  (<View style={styles.tileBody}>
                                                      <View style={styles.summaryContainer}>
                                                          <View style={styles.summaryRow}>
                                                              <View style={styles.summaryCell}>
                                                                  <Text style={styles.summaryCellValue}>{this.props.salesHandlerActivities.activitySummary.createdActivities}</Text>
                                                                  <Text style={styles.summaryCellTag}>Total Created</Text>
                                                              </View>
                                                              <View style={styles.summaryCell}>
                                                                  <Text style={styles.summaryCellValue}>{this.props.salesHandlerActivities.activitySummary.approvedActivities}</Text>
                                                                  <Text style={styles.summaryCellTag}>Total Approved</Text>
                                                              </View>
                                                              <View style={styles.summaryCell}>
                                                                  <Text style={styles.summaryCellValue}>{this.props.salesHandlerActivities.activitySummary.cancelledActivities}</Text>
                                                                  <Text style={styles.summaryCellTag}>Total Cancelled</Text>
                                                              </View>
                                                              <View style={styles.summaryCell}>
                                                                  <Text style={styles.summaryCellValue}>{this.props.salesHandlerActivities.activitySummary.completedActivities}</Text>
                                                                  <Text style={styles.summaryCellTag}>Total Completed</Text>
                                                              </View>
                                                              <View style={styles.summaryCell}>
                                                                  <Text style={styles.summaryCellValue}>{this.props.salesHandlerActivities.activitySummary.rejectedActivities}</Text>
                                                                  <Text style={styles.summaryCellTag}>Total Rejected</Text>
                                                              </View>
                                                              <View style={styles.summaryCell}>
                                                                  <Text style={styles.summaryCellValue}>{this.props.salesHandlerActivities.activitySummary.auditFailedActivities}</Text>
                                                                  <Text style={styles.summaryCellTag}>Failed Audits</Text>
                                                              </View>
                                                              <View style={styles.summaryCell}>
                                                                  <Text style={styles.summaryCellValue}>{this.props.salesHandlerActivities.activitySummary.auditSuccessActivities}</Text>
                                                                  <Text style={styles.summaryCellTag}>Successful Audits</Text>
                                                              </View>
                                                          </View>
                                                      </View>


                                                      {!UtilityService.checkEmpty(this.props.salesHandlerActivities.activities)? (
                                                          <FlatList style={{marginBottom: 0}} data={Object.values(this.props.salesHandlerActivities.activities)}
                                                                    renderItem={({item}) => (
                                                                        <View>

                                                                            <TouchableOpacity style={styles.retailerTileHead}
                                                                                              onPress={() => {this.props.setCurrentRetailer(item.retailerId)}}>

                                                                                <View style={{flex: 1, alignItems: 'stretch', justifyContent: 'space-between'}}>
                                                                                    <Text style={styles.tileHeadText}>{item.retailerId}</Text>
                                                                                    <Text style={styles.tileHeadText}>{item.retailerName}</Text>
                                                                                </View>
                                                                                <View style={{flex: 0, alignItems: 'stretch', justifyContent: 'space-between', marginRight:5}}>
                                                                                    <Text style={styles.tileHeadText}>R</Text>
                                                                                </View>
                                                                            </TouchableOpacity>

                                                                            {item.activityData!=null && this.props.currentRetailer === item.retailerId ?
                                                                            (<View>
                                                                                {!UtilityService.checkEmpty(item.activityData)? (
                                                                                    <FlatList style={{marginBottom: 0}} data={Object.values(item.activityData)}
                                                                                              renderItem={({item}) => (

                                                                                                  <View style={{flex: 0, justifyContent: 'flex-start', alignItems: 'stretch'}}>
                                                                                                      <View style={{
                                                                                                          flex: 0,
                                                                                                          alignItems: 'stretch',
                                                                                                          justifyContent: 'flex-start',
                                                                                                          marginTop: 10,
                                                                                                          marginLeft: 30,
                                                                                                          marginRight: 30,
                                                                                                          borderWidth: 1,
                                                                                                          borderColor: '#CCC',
                                                                                                          backgroundColor: '#e6e6ea',
                                                                                                          borderRadius: 4
                                                                                                      }}>

                                                                                                          <View style={[styles.tileBody, {backgroundColor: "#e6e6ea"}]}>

                                                                                                              <View style={styles.summaryContainer}>
                                                                                                                      <View style={styles.summaryRow}>
                                                                                                                          <View style={{flex: 1, margin:2}}>
                                                                                                                              <MaterialIcons name='date-range' style={{textAlign:'center', color:'#000', fontSize: 15}}/>
                                                                                                                              <Text style={styles.summaryCellValue}>{this.getDisplayDate(item.activityCreatedDate)}</Text>
                                                                                                                              {/*<Text style={styles.summaryCellTag}>Created Date</Text>*/}
                                                                                                                          </View>
                                                                                                                      </View>
                                                                                                                      <View style={styles.summaryRow}>
                                                                                                                          <View style={{flex: 1, margin:2}}>
                                                                                                                              <Text style={styles.summaryCellValue}>{item.activityName}</Text>
                                                                                                                          </View>
                                                                                                                      </View>
                                                                                                                      <View style={styles.summaryRow}>
                                                                                                                          <View style={this.isStatusPositive(item.status)?{flex: 1, marginLeft:80, marginRight:80, backgroundColor:"#CCC", borderWidth:2, borderColor:"#a8e6cf", borderRadius:10}: {flex: 1, marginLeft:80, marginRight:80, backgroundColor:"#CCC", borderWidth:2, borderColor:"#ff8b94", borderRadius:10}}>
                                                                                                                              <Text style={styles.summaryCellValue}>{this.getStatusName(item.status)}</Text>
                                                                                                                          </View>
                                                                                                                      </View>
                                                                                                                      {item.status === "CREATED" ? (
                                                                                                                          <View style={styles.summaryRow}>
                                                                                                                              <View style={{flex: 1, margin:2}}>
                                                                                                                                  {this.props.salespersonDetail.id === item.salesHandlerId?
                                                                                                                                      (<Text style={styles.summaryCellValue}>Status to be updated by You</Text>) :
                                                                                                                                      (<Text style={styles.summaryCellValue}>Status to be updated by {item.salesHandlerName}</Text>)}
                                                                                                                              </View>
                                                                                                                          </View>
                                                                                                                      ):null}

                                                                                                                      {item.status === "COMPLETED" || item.status === "APPROVED" ? (
                                                                                                                          <View style={styles.summaryRow}>
                                                                                                                              <View style={{flex: 1, margin:2}}>
                                                                                                                                  {this.props.salespersonDetail.id === item.auditorId?
                                                                                                                                      (<Text style={styles.summaryCellValue}>Status to be updated by You</Text>) :
                                                                                                                                      (<Text style={styles.summaryCellValue}>Status to be updated by {item.auditorName}</Text>)}
                                                                                                                              </View>
                                                                                                                          </View>
                                                                                                                      ):null}


                                                                                                                      {item.status === "CREATED" && this.props.salespersonDetail.id === this.props.currentSalesHandler? (
                                                                                                                          <View style={styles.summaryRow}>
                                                                                                                              <TouchableOpacity style={[styles.changeStatusBtn, {marginRight:20, backgroundColor:'#a8e6cf'}]}
                                                                                                                                                onPress={() => this.props.updateActivityStatus(item.activityId, this.props.currentSalesHandler, "COMPLETED", this.props)}>
                                                                                                                                  <Text style={{fontSize:10, color:'#5e5656'}}>Completed</Text>
                                                                                                                              </TouchableOpacity>
                                                                                                                              <TouchableOpacity style={[styles.changeStatusBtn, {backgroundColor:'#ff8b94'}]}
                                                                                                                                                onPress={() => this.props.updateActivityStatus(item.activityId, this.props.currentSalesHandler, "CANCELLED", this.props)}>
                                                                                                                                  <Text style={{fontSize:10, color:'#5e5656'}}>Cancelled</Text>
                                                                                                                              </TouchableOpacity>
                                                                                                                          </View>
                                                                                                                      ) : null}
                                                                                                                      {item.status === "COMPLETED" && this.props.salespersonDetail.id === item.auditorId ? (
                                                                                                                          <View style={styles.summaryRow}>
                                                                                                                              <TouchableOpacity style={[styles.changeStatusBtn, {marginRight:20, backgroundColor:'#a8e6cf'}]}
                                                                                                                                                onPress={() => this.props.updateActivityStatus(item.activityId, this.props.currentSalesHandler, "APPROVED", this.props)}>
                                                                                                                                  <Text style={{fontSize:10, color:'#5e5656'}}>Approved</Text>
                                                                                                                              </TouchableOpacity>
                                                                                                                              <TouchableOpacity style={[styles.changeStatusBtn, {backgroundColor:'#ff8b94'}]}
                                                                                                                                                onPress={() => this.props.updateActivityStatus(item.activityId, this.props.currentSalesHandler, "REJECTED", this.props)}>
                                                                                                                                  <Text style={{fontSize:10, color:'#5e5656'}}>Rejected</Text>
                                                                                                                              </TouchableOpacity>
                                                                                                                          </View>
                                                                                                                      ) : null}
                                                                                                                      {item.status === "APPROVED" && this.props.salespersonDetail.id === item.auditorId ? (
                                                                                                                          <View style={styles.summaryRow}>
                                                                                                                              <TouchableOpacity style={[styles.changeStatusBtn, {marginRight:20, backgroundColor:'#a8e6cf'}]}
                                                                                                                                                onPress={() => this.props.updateActivityStatus(item.activityId, this.props.currentSalesHandler, "AUDIT_SUCCESS", this.props)}>
                                                                                                                                  <Text style={{fontSize:10, color:'#5e5656'}}>Audit Success</Text>
                                                                                                                              </TouchableOpacity>
                                                                                                                              <TouchableOpacity style={[styles.changeStatusBtn, {backgroundColor:'#ff8b94'}]}
                                                                                                                                                onPress={() => this.props.updateActivityStatus(item.activityId, this.props.currentSalesHandler, "AUDIT_FAILED", this.props)}>
                                                                                                                                  <Text style={{fontSize:10, color:'#5e5656'}}>Audit Failed</Text>
                                                                                                                              </TouchableOpacity>
                                                                                                                          </View>
                                                                                                                      ) : null}

                                                                                                              </View>




                                                                                                          </View>
                                                                                                      </View>
                                                                                                  </View>  )}
                                                                                              keyExtractor={(item, index) => index.toString()}
                                                                                    />
                                                                                ):null}
                                                                            </View>):null}
                                                                        </View>
                                                                    )}
                                                                    keyExtractor={(item, index) => index.toString()}
                                                          />):null}
                                                  </View>):null}
                                          </View>

                                      </View>  )}
                                  keyExtractor={(item, index) => index.toString()}/>
                    ) :null}
                </View>
                {/*sales handler activity block ends*/}
                </ScrollView>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#EFEFEF'
    },
    daysNavigator:{
        flex: 0,
        backgroundColor:'#FFF',
        flexDirection:'row',
        alignItems:'stretch',
        justifyContent: 'space-between',
    },
    navBtn:{
        padding:5,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 5,
        backgroundColor:'#68A4CC',
        borderRadius: 4,
        elevation: 5
    },
    tile: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        margin: 10,
        marginTop: 0,
        borderWidth: 1,
        borderColor: '#CCC',
        backgroundColor: '#fff',
        borderRadius: 4
    },
    tileHead: {
        flex: 0,
        flexDirection: 'row',
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        backgroundColor: '#5A383B',
        padding: 2,
        borderRadius: 4
    },
    tileHeadText: {
        padding: 5,
        color: '#f4f4f8',
        fontSize: 15,
        fontWeight: 'bold'
        /*textShadowColor: 'rgba(0, 0, 0, 0.75)',
        textShadowOffset: {width: -1, height: 1},
        textShadowRadius: 10*/
    },
    titleButton: {
        backgroundColor: '#CCC',
        borderWidth: 1,
        borderColor: '#777',
        borderRadius: 5,
        padding: 3,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 2,
        elevation: 5
    },
    titleBtnText: {color: '#133861', textAlign: 'center', fontSize: 10, fontWeight: 'bold'},
    tileBody: {
        padding: 3,
        borderRadius: 4
        // backgroundColor: '#FFF'
    },
    target: {
        flex: 0,
        flexDirection: 'row',
        alignItems: 'stretch',
        justifyContent: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#EFEFEF',
        marginBottom: 5
    },
    targetLeft: {
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        borderRightWidth: 1,
        borderRightColor: '#EFEFEF'
    },
    targetRight:{
        flex: 1,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        padding: 3
    },
    summaryContainer:{
        flex:0,
        alignItems:'stretch',
        justifyContent:'flex-start'
    },
    summaryRow: {
        flex:0,
        flexDirection:'row',
        alignItems: 'flex-start',
        justifyContent:'flex-start'
    },
    summaryCell:{
        flex: 1,
        borderWidth:2,
        borderColor:'#fed766',
        borderRadius: 5,
        margin:2,
        backgroundColor: "#e6e6ea",
        elevation: 2
    },
    summaryCellValue:{textAlign:'center', color:'#5A383B', fontSize: 12},
    summaryCellTag:{
        textAlign:'center',
        color:'#5A383B',
        fontSize: 8
    },
    pendingOrdersHead:{flex:0, alignItems:'stretch', justifyContent:'space-between', flexDirection:'row'},
    refreshIcon:{color:'#037004', padding: 10, paddingRight:15, paddingLeft:15, fontSize: 21},
    orderDeliveredBtn:{
        padding:5,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 5,
        backgroundColor:'#68A4CC',
        borderRadius: 4,
        borderWidth: 1,
        borderColor:"#FFF"
    },
    targetTitleButton: {
        backgroundColor: '#fff',
        borderRadius: 1,
        padding: 3,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 2
    },
    loaderContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: 'black',
        width: width,
        height: height,
        zIndex: 1
    },
    btn:{
        padding:5,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 5,
        backgroundColor:'#68A4CC',
        borderRadius: 4
    },
    icon: {
        fontSize: 20,
        color: '#777'
    },
    retailerTileHead: {
        flex: 0,
        flexDirection: 'row',
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        backgroundColor: '#fe4a49',
        padding: 2,
        borderRadius: 4,
        marginLeft: 20,
        marginRight: 20,
        marginTop: 5,
        marginBottom: 10,
        elevation: 5
    },
    changeStatusBtn: {
            flex: 1,
            borderRadius: 5,
            margin:2,
            padding:5,
            paddingLeft: 15,
            paddingRight: 15,
            alignItems: 'center',
            justifyContent:'center',
            elevation: 5
    },
    auditTimeDateButtonInactive : {
        width:150,
        backgroundColor: '#e6e6ea',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop:5,
        marginBottom:5,
        marginRight:10,
        marginLeft:10,
        borderWidth: 3,
        borderRadius: 10,
        paddingTop: 10,
        paddingBottom: 10,
        borderColor: '#fff',
        elevation:5
    },
    auditTimeDateButtonActive : {
        width:150,
        backgroundColor: '#fed766',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop:5,
        marginBottom:5,
        marginRight:10,
        marginLeft:10,
        borderWidth: 3,
        borderRadius: 10,
        paddingTop: 10,
        paddingBottom: 10,
        borderColor: '#fff',
        elevation:5
    }
});

const mapStateToProps = state => {
    return {
        salespersonDetail: state.loginReducer.salespersonDetail,

        showLoader: state.manageActivityReducer.showLoader,
        handlers: state.manageActivityReducer.handlers,
        salesHandlerActivities: state.manageActivityReducer.salesHandlerActivities,
        currentSalesHandler: state.manageActivityReducer.currentSalesHandler,
        retailerActivities: state.manageActivityReducer.retailerActivities,
        currentRetailer: state.manageActivityReducer.currentRetailer,
        fromDate: state.manageActivityReducer.fromDate,
        toDate: state.manageActivityReducer.toDate,
        isFromDatePickerVisible: state.manageActivityReducer.isFromDatePickerVisible,
        isToDatePickerVisible: state.manageActivityReducer.isToDatePickerVisible
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setFromDate: (fromDate) => dispatch({type: "SET_FROM_DATE", payload: fromDate}),
        setToDate: (toDate) => dispatch({type: "SET_TO_DATE", payload: toDate}),
        setIsFromDatePickerVisible: (isFromDatePickerVisible) => dispatch({type: "SET_IS_FROM_DATE_PICKER_VISIBLE", payload: isFromDatePickerVisible}),
        setIsToDatePickerVisible: (isToDatePickerVisible) => dispatch({type: "SET_IS_TO_DATE_PICKER_VISIBLE", payload: isToDatePickerVisible}),
        setCurrentRetailer: (retailerId) => dispatch({type: "SET_CURRENT_RETAILER", payload: retailerId}),
        setCurrentSalesHandler: (salesHandlerId) => dispatch({type: "SET_CURRENT_SALES_HANDLER", payload: salesHandlerId}),
        fetchSalesHandlersForActivityAudit: (handlerId, fromDate, toDate) => dispatch(ManageActivityActions.fetchSalesHandlersForActivityAudit(handlerId, fromDate, toDate)),
        fetchActivities: (salesHandlerId, props) => dispatch(ManageActivityActions.fetchActivities(salesHandlerId, props)),
        setSHActivities: (activities) => dispatch({type: "SET_SALES_HANDLER_ACTIVITIES", payload: activities}),
        updateActivityStatus: (activityId, handlerId, status, props) => dispatch(ManageActivityActions.updateActivityStatus(activityId, handlerId, status, props)),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(ManageActivityScreen);