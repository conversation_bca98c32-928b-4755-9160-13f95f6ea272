import React, {Component} from 'react';
import {connect} from 'react-redux';
import {
    ActivityIndicator,
    <PERSON><PERSON>,
    BackHandler,
    KeyboardAvoidingView,
    PixelRatio,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import HeaderView from "./HeaderView";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import MaterialIcon from "react-native-vector-icons/MaterialIcons";
import Entypo from "react-native-vector-icons/Entypo";
import NavigationService from "./../store/services/NavigationService";
import * as ChangePasswordAction from "../store/actions/ChangePasswordAction";

var Dimensions = require('Dimensions');
var { width, height } = Dimensions.get('window');


class ChangePasswordScreen extends Component {

    constructor(props) {
        super(props);
        this.handleBackButtonClick = this.handleBackButtonClick.bind(this);
    }

    componentWillMount() {
        /*this.props.setIsOPFormViewEnabled(true);
        this.props.setIsCPFormViewEnabled(false);
        BackHandler.addEventListener('hardwareBackPress', this.handleBackButtonClick);*/

        this.props.navigation.addListener(
            'didFocus',
            payload => {
                console.debug('didFocus', payload);
                this.init();
            }
        );
    }

    componentWillUnmount() {
        //BackHandler.removeEventListener('hardwareBackPress', this.handleBackButtonClick);

        if (this.props.navigation && this.props.navigation.removeListener) {
            this.props.navigation.removeListener('didFocus');
            this.init();
        }
    }

    handleBackButtonClick() {
        this.props.navigation.goBack(null);
        return true;
    }

    init() {
        this.props.reset();
    }

    alertBack(){
        Alert.alert(
            'Go Back',
            'Are you sure you want to discard your changes?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        NavigationService.navigate('Home');
                    } },
            ],
            { cancelable: false }
        )
    }

    validateOldPassword(oldPassword){
        if(oldPassword != null && oldPassword.length >= 6){
            this.props.setIsOldPasswordValid(true);
            this.props.setOldPassword(oldPassword);
        }else{
            this.props.setIsOldPasswordValid(false);
        }
    }

    validateConfirmPassword(confirmNewPassword){
        console.log(confirmNewPassword);
        console.log("new password:: ", this.props.newPassword);
        if(this.props.newPassword === null){
            return;
        }
        if(confirmNewPassword === null){
            return;
        }
        if(this.props.newPassword.length >= 6 && this.props.newPassword.length <= 10){
            this.props.setIsNewPasswordIncorrect(false);
        }else{
            this.props.setIsNewPasswordIncorrect(true);
        }
        if(confirmNewPassword === this.props.newPassword){
            this.props.setIsPasswordMismatch(false);
            this.props.setIsConfirmNewPasswordValid(true);
        }else{
            this.props.setIsPasswordMismatch(true);
        }
    }

    goToHome() {
        this.props.reset();
        NavigationService.navigate('Home');
    }

    toggleOPView(){
        if(this.props.hideOPassword === true){
            this.props.setHideOPassword(false);
        }else{
            this.props.setHideOPassword(true);
        }
    }

    toggleNPView(){
        if(this.props.hideNPassword === true){
            this.props.setHideNPassword(false);
        }else{
            this.props.setHideNPassword(true);
        }
    }
    toggleCNPView(){
        if(this.props.hideCNPassword === true){
            this.props.setHideCNPassword(false);
        }else{
            this.props.setHideCNPassword(true);
        }
    }
    render() {
        return (
            <View style={styles.container}>
                {this.props.showLoader ? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#f0af3b"/>
                        <Text style={{color: '#f0af3b'}}>Please wait ...</Text>
                    </View>
                ) : null}
                <HeaderView menuIcon={false}
                            title={'Change Password'}
                            backHandler={ (this.props.oldPassword != null && this.props.oldPassword !== "") ? this.alertBack : null }
                            {...this.props} headerColor={"#4a4e4d"}/>

                <KeyboardAvoidingView style={styles.keyboardContainer}>
                    <ScrollView style={styles.container}>
                        {/*Contact view Starts*/}
                            <View style={styles.enterContactContainer}>

                                {this.props.isOPFormViewEnabled && this.props.isOldPasswordIncorrect?
                                    (<Text style={styles.errorText}>Entered current password is wrong, please try again</Text>) : null}
                                {this.props.isCPFormViewEnabled  && this.props.isPasswordMismatch?
                                    (<Text style={styles.errorText}>Password did not match</Text>) : null}
                                {this.props.isCPFormViewEnabled  && this.props.isNewPasswordIncorrect?
                                    (<Text style={styles.errorText}>Password must be min 6 and max 10 characters.</Text>) : null}

                                { this.props.isOPFormViewEnabled ? (
                                <View style={{ width: 300, marginTop: 10, marginBottom: 5 }}>
                                    <MaterialCommunityIcons name='onepassword' style={styles.icon}/>
                                    <Text style={styles.alertTextStyle}> Enter the current/old password below: </Text>

                                    <View style={styles.inputContainer}>
                                            <MaterialCommunityIcons name='textbox-password' style={styles.formIcon}/>
                                            <TextInput
                                                style={styles.input}
                                                defaultValue={this.props.oldPassword}
                                                placeholder="Old Password"
                                                keyboardType="default"
                                                underlineColorAndroid='transparent'
                                                placeholderTextColor='#999'
                                                autoCapitalize = 'none'
                                                maxLength={10}
                                                secureTextEntry = { this.props.hideOPassword }
                                                onChangeText={(oldPassword) => this.validateOldPassword(oldPassword)}
                                            />

                                            <TouchableOpacity style={styles.showPasswordButton} onPress={() => {this.toggleOPView()} }>
                                                { this.props.hideOPassword ?
                                                    <Entypo name='eye-with-line' style={styles.showPasswordIcon}/>
                                                        :
                                                    <Entypo name='eye' style={styles.showPasswordIcon}/>
                                                }
                                            </TouchableOpacity>
                                    </View>

                                    {this.props.isOldPasswordValid ? (
                                        <TouchableOpacity style={[styles.buttonStyle, {backgroundColor:'#f0af3b'}]}
                                                          onPress={() => {this.props.checkPassword(this.props.salespersonDetail.id, this.props.oldPassword)}}>
                                            <Text style={{color:'#FFF'}}>Submit</Text>
                                        </TouchableOpacity>
                                    ) : (
                                        <TouchableOpacity style={styles.buttonStyle}
                                                          disabled={true}>
                                            <Text style={{color:'#FFF'}}>Submit</Text>
                                        </TouchableOpacity>
                                    )}
                                </View>
                                ):null}

                                { this.props.isCPFormViewEnabled ? (
                                <View style={{ width: 300, marginTop: 10, marginBottom: 5 }}>
                                    <MaterialCommunityIcons name='key-change' style={styles.icon}/>
                                    <Text style={styles.alertTextStyle}> Enter the new password below: </Text>

                                        <View style={styles.inputContainer}>
                                            <MaterialCommunityIcons name='textbox-password' style={styles.formIcon}/>
                                            <TextInput
                                                style={styles.input}
                                                defaultValue={null}
                                                placeholder="New Password"
                                                keyboardType="default"
                                                underlineColorAndroid='transparent'
                                                placeholderTextColor='#999'
                                                autoCapitalize = 'none'
                                                maxLength={10}
                                                secureTextEntry = { this.props.hideNPassword }
                                                onChangeText={(newPassword) => this.props.setNewPassword(newPassword)}
                                            />
                                            <TouchableOpacity style={styles.showPasswordButton} onPress={() => {this.toggleNPView()} }>
                                                { this.props.hideNPassword ?
                                                    <Entypo name='eye-with-line' style={styles.showPasswordIcon}/>
                                                    :
                                                    <Entypo name='eye' style={styles.showPasswordIcon}/>
                                                }
                                            </TouchableOpacity>
                                        </View>
                                        <View style={styles.inputContainer}>
                                            <MaterialCommunityIcons name='textbox-password' style={styles.formIcon}/>
                                            <TextInput
                                                style={styles.input}
                                                defaultValue={null}
                                                placeholder="Confirm New Password"
                                                keyboardType="default"
                                                underlineColorAndroid='transparent'
                                                placeholderTextColor='#999'
                                                autoCapitalize = 'none'
                                                maxLength={10}
                                                secureTextEntry = { this.props.hideCNPassword }
                                                onChangeText={(confirmNewPassword) => this.validateConfirmPassword(confirmNewPassword)}
                                            />
                                            <TouchableOpacity style={styles.showPasswordButton} onPress={() => {this.toggleCNPView()} }>
                                                { this.props.hideCNPassword ?
                                                    <Entypo name='eye-with-line' style={styles.showPasswordIcon}/>
                                                    :
                                                    <Entypo name='eye' style={styles.showPasswordIcon}/>
                                                }
                                            </TouchableOpacity>
                                        </View>

                                        {this.props.isConfirmNewPasswordValid ? (
                                            <TouchableOpacity style={[styles.buttonStyle, {backgroundColor:'#f0af3b'}]}
                                                              onPress={() => {this.props.changePassword(this.props.salespersonDetail.id, this.props.oldPassword, this.props.newPassword)}}>
                                                <Text style={{color:'#FFF'}}>Submit</Text>
                                            </TouchableOpacity>
                                        ) : (
                                            <TouchableOpacity style={styles.buttonStyle}
                                                              disabled={true}>
                                                <Text style={{color:'#FFF'}}>Submit</Text>
                                            </TouchableOpacity>
                                        )}
                                </View>
                                ):null}

                            </View>
                        {/*Contact view Ends*/}
                        {/*Error Info view Starts*/}
                        {this.props.errorChangePassword ? (
                            <View style={styles.errorAddingContainer}>
                                <MaterialIcon name='error' style={styles.errorIcon}/>
                                <Text style={styles.alertTextStyle}> Some error occured, Please Try Again! </Text>
                                <View style={{flex: 1, alignItems: 'stretch'}}>
                                    <TouchableOpacity style={[styles.buttonStyle, {backgroundColor:'#f0af3b'}]}
                                                      onPress={() => {
                                                          this.props.reset()}
                                                      }>
                                        <Text style={styles.buttonTitleStyle}>Try Again</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ):null}
                        {/*Error Info view Ends*/}
                        {/*Success Info view Starts*/}
                        {this.props.successChangePassword ? (
                            <View style={styles.successAddingContainer}>
                                <MaterialIcon name='check-box' style={styles.successIcon}/>
                                <Text style={styles.alertTextStyle}> Password Changed Successfully! </Text>
                                <View style={{flex: 1, alignItems: 'stretch'}}>
                                    <TouchableOpacity style={[styles.buttonStyle, {backgroundColor:'#f0af3b'}]}
                                                      onPress={() => {this.goToHome()}
                                                      }>
                                        <Text style={styles.buttonTitleStyle}>Go to Home</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ):null}
                        {/*Success Info view Ends*/}
                    </ScrollView>
                </KeyboardAvoidingView>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FFF',
    },
    formContainer: {
        flex: 1,
        backgroundColor: '#FFF',
        paddingLeft: 15,
        paddingRight: 15
    },
    imageStyle:{
        paddingVertical: 30,
        width: 80,
        height: 80,
        borderRadius: 75,
        marginTop: 10,
        marginBottom: 10
    },
    inputGroup:{
        flexDirection:'row',
        flex:1
    },
    inputContainer: {
        marginLeft: 20,
        marginRight: 20,
        flex: 1,
        flexDirection:'row',
        flexWrap:'wrap',
        borderColor: "#000",
        borderBottomWidth: 1,
        marginBottom: 8
    },
    enterContactContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff'
    },
    errorAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff'
    },
    successAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff'
    },
    alertTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: '#f0af3b'
    },
    alertAlreadyExistTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: 'red'
    },
    label: {
        color:'#000',
    },
    input: {
        flex:1,
        flexDirection: "row",
        paddingLeft: 5,
        paddingBottom:0
    },
    buttonStyle: {
        flex: 0,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#737373',
        borderWidth: 1,
        borderRadius: 5,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        borderColor: '#fff',
        marginTop: 20,
        marginBottom:50
    },
    buttonTitleStyle: {
        textDecorationColor: '#fff'
    },
    avatarContainer: {
        borderColor: '#9B9B9B',
        borderWidth: 1 / PixelRatio.get(),
        justifyContent: 'center',
        alignItems: 'center',
    },
    avatar: {
        borderRadius: 75,
        width: 150,
        height: 150,
    },
    inputContactBox: {
        height: 45,
        marginLeft: 10,
        flex: 1,
        color: '#d4272a',
        fontSize: 20
    },
    icon: {
        fontSize: 60,
        color: '#737373',
        textAlign: 'center'
    },
    errorIcon: {
        fontSize: 60,
        color: '#d4272a',
        textAlign: 'center',
    },
    successIcon: {
        fontSize: 60,
        color: '#009933',
        textAlign: 'center',
    },
    formIcon: {
        fontSize: 20,
        color: '#CCC',
        textAlign: 'left',
        paddingTop:12,
        marginRight:0,
        marginBottom:0,
        paddingBottom:-5,
    },
    showPasswordIcon: {
        fontSize: 20,
        color: '#777',
        textAlign: 'left',
        marginRight:0,
        marginBottom:0,
    },
    showPasswordButton: {
        flex: 0,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0af3b',
        borderWidth: 1,
        borderRadius: 5,
        padding: 5,
        borderColor: '#fff',
        marginTop: 2,
        marginBottom:2
    },
    oldPasswordContainer:{
        paddingVertical: 2,
        marginTop: 2,
        marginBottom: 2,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff'
    },
    errorText:{
        marginTop: 10,
        marginBottom: 5,
        justifyContent: 'center',
        alignItems: 'center',
        color: '#d4272a',
        fontSize: 12,
    },
    keyboardContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#d4272a',
    },
    loaderContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: 'black',
        width: width,
        height: height,
        zIndex: 1
    },
    updateBtn:{
        padding:5,
        marginBottom: 5,
        backgroundColor:'#737373',
        alignItems:'center',
        borderRadius: 4
    }
});

const mapStateToProps = state => {
    return {
        salespersonDetail: state.loginReducer.salespersonDetail,
        oldPassword:state.changePasswordReducer.oldPassword,
        newPassword:state.changePasswordReducer.newPassword,
        confirmNewPassword:state.changePasswordReducer.confirmNewPassword,
        isOldPasswordIncorrect:state.changePasswordReducer.isOldPasswordIncorrect,
        isOldPasswordValid:state.changePasswordReducer.isOldPasswordValid,
        errorChangePassword:state.changePasswordReducer.errorChangePassword,
        successChangePassword:state.changePasswordReducer.successChangePassword,
        isOPFormViewEnabled:state.changePasswordReducer.isOPFormViewEnabled,
        isCPFormViewEnabled:state.changePasswordReducer.isCPFormViewEnabled,
        hideOPassword:state.changePasswordReducer.hideOPassword,
        hideNPassword:state.changePasswordReducer.hideNPassword,
        hideCNPassword:state.changePasswordReducer.hideCNPassword,
        isNewPasswordIncorrect: state.changePasswordReducer.isNewPasswordIncorrect,
        isConfirmNewPasswordValid: state.changePasswordReducer.isConfirmNewPasswordValid,
        isPasswordMismatch: state.changePasswordReducer.isPasswordMismatch
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setIsOPFormViewEnabled: (isOPFormViewEnabled) => dispatch({type: "SET_IS_OP_FORM_VIEW_ENABLED", payload: isOPFormViewEnabled}),
        setIsCPFormViewEnabled: (isCPFormViewEnabled) => dispatch({type: "SET_IS_CP_FORM_VIEW_ENABLED", payload: isCPFormViewEnabled}),
        setOldPassword: (oldPassword) => dispatch({type: "SET_OLD_PASSWORD", payload: oldPassword}),
        setNewPassword: (newPassword) => dispatch({type: "SET_NEW_PASSWORD", payload: newPassword}),
        setIsOldPasswordValid: (isOldPasswordValid) => dispatch({type: "SET_IS_OLD_PASSWORD_VALID", payload: isOldPasswordValid}),
        setIsNewPasswordIncorrect: (isNewPasswordIncorrect) => dispatch({type: "SET_IS_NEW_PASSWORD_INCORRECT", payload: isNewPasswordIncorrect}),
        setIsConfirmNewPasswordValid: (isConfirmNewPasswordValid) => dispatch({type: "SET_IS_CONFIRM_NEW_PASSWORD_VALID", payload: isConfirmNewPasswordValid}),
        setHideOPassword: (hideOPassword) => dispatch({type: "SET_HIDE_OLD_PASSWORD", payload: hideOPassword}),
        setHideNPassword: (hideNPassword) => dispatch({type: "SET_HIDE_NEW_PASSWORD", payload: hideNPassword}),
        setHideCNPassword: (hideCNPassword) => dispatch({type: "SET_HIDE_CONFIRM_NEW_PASSWORD", payload: hideCNPassword}),
        checkPassword:(userId, password) => dispatch(ChangePasswordAction.checkPassword(userId, password)),
        changePassword:(userId, oldPassword, newPassword) => dispatch(ChangePasswordAction.changePassword(userId, oldPassword, newPassword)),
        setIsPasswordMismatch: (isPasswordMismatch) => dispatch({type: "SET_IS_PASSWORD_MISMATCH", payload: isPasswordMismatch}),
        setErrorChangePassword: (isErrorChangePassword) => dispatch({type: "SET_ERROR_CHANGE_PASSWORD", payload: isErrorChangePassword}),
        setSuccessChangePassword: (isSuccessChangePassword) => dispatch({type: "SET_SUCCESS_CHANGE_PASSWORD", payload: isSuccessChangePassword}),
        reset: () => dispatch(ChangePasswordAction.reset()),
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(ChangePasswordScreen);