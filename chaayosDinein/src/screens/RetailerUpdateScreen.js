import React, {Component} from 'react';
import {connect} from 'react-redux';
import {
    StyleSheet,
    Text,
    View,
    BackHandler,
    TouchableOpacity,
    ActivityIndicator,
    Alert,
    FlatList,
    TextInput,
    Picker,
    KeyboardAvoidingView,
    ScrollView
} from 'react-native';
import HeaderView from "./HeaderView";
import * as RetailerManagementActions from "./../store/actions/RetailerManagementActions";
import MaterialIcon from "react-native-vector-icons/MaterialIcons";
import AntDesignIcon from "react-native-vector-icons/AntDesign";
import EntypoIcon from "react-native-vector-icons/Entypo";
import SimpleLineIcons from "react-native-vector-icons/SimpleLineIcons";
import NavigationService from "../store/services/NavigationService";
import Communications from "react-native-communications";
import { Dropdown } from 'react-native-material-dropdown';
import * as RetailerRegistrationActions from "../store/actions/RetailerRegistrationActions";
var Dimensions = require('Dimensions');
var { width, height } = Dimensions.get('window');


class RetailerUpdateScreen extends Component {

    constructor(props) {
        super(props);
        this.handleBackButtonClick = this.handleBackButtonClick.bind(this);
        this.setContactType = this.setContactType.bind(this);
    }

    componentWillMount() {
        this.init();
        BackHandler.addEventListener('hardwareBackPress', this.handleBackButtonClick);
    }

    componentWillUnmount() {
        BackHandler.removeEventListener('hardwareBackPress', this.handleBackButtonClick);
    }

    handleBackButtonClick() {
        this.props.navigation.goBack(null);
        return true;
    }

    init(){
        this.props.getUnitProductPrices(this.props.retailer.salesHandlerId, this.props.retailer.customerId);
        this.props.setIsPricesChanged(false);
        this.props.setIsValidPrice(true);
        this.props.fetchTargetAttributes(this.props.retailer.salesHandlerId);
        this.props.setUpdatedOrderFrequency(null);
        this.props.setUpdatedTarget(null);
        this.props.setUpdatedCustomTarget(null);
    }

    ifValidPhoneNumber(input) {
        this.props.setIsRUValidContactNo(true);
        this.props.setContactRUAlreadyExists(false);
        if (!(input.match(/^[0-9]*$/))) {
            this.props.setIsRUValidContactNo(false);
        }
        if(input.match(/^[6-9]\d{9}$/)){
            this.props.lookupRetailerContact(input);
        }
        if(input.match(/^[0-5]\d{9}$/)){
            this.props.setIsRUValidContactNo(false);
        }
    }

    alertBack(){
        Alert.alert(
            'Go Back',
            'Are you sure you want to discard your changes?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        NavigationService.navigate('ManageRetailers');
                    } },
            ],
            { cancelable: false }
        )
    }

    alertStatusUpdate(status){
        var statusText = "deactivate";
        if(status === "ACTIVE"){
            statusText = "activate";
        }
        Alert.alert(
            'Status Change',
            'Are you sure you want to ' + statusText +' the retailer?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        this.props.updateStatus(this.props.retailer, status, this.props);
                    } },
            ],
            { cancelable: false }
        )
    }

    alertProductPriceUpdate(salesHandlerId, customerId, products){
        Alert.alert(
            'Price Change',
            'Are you sure you want to update product prices?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        this.props.updateProductPrices(salesHandlerId, customerId, products);
                    } },
            ],
            { cancelable: false }
        )
    }

    updateSDId() {
        Alert.alert(
            'Sales Diary Id Update',
            'Are you sure you want to update sales diary id?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        this.props.updateSalesDiaryId(this.props.updatedSalesDiaryId, this.props.retailer);
                    } },
            ],
            { cancelable: false }
        )
    }

    updateProductPrice(product, updatedPrice, index){
        if((updatedPrice.match(/^\d{0,8}(\.\d{1,4})?$/))){
            this.props.setIsValidPrice(true);
            if(this.props.products != null){
                for (var i = 0; i < this.props.products.length; i++) {
                    if (this.props.products[i].productId == product.productId) {
                        //this.props.products[i].price = updatedPrice;
                        this.props.setIsPricesChanged(true);
                        break;
                    }
                }
            }
        }else{
            this.props.setIsValidPrice(false);
            this.props.setIsPricesChanged(false);
        }
        this.props.setPrice(this.props.products, index, updatedPrice);
    }

    setIsPricesChanged(products){
        var changeDetected = false;
        if(products!=null){
            for (var i = 0; i < products.length; i++) {
                    if(products[i].originalPrice == products[i].price){
                        changeDetected = true;
                    }
            }
        }
        this.props.setIsPricesChanged(changeDetected);
    }

    updateTarget(){
        let targetValue = 0;
        let targetType = "";
        if(this.props.targets != null
            && this.props.updatedTarget != null
            && this.props.updatedTarget !== 'NO_TARGET'
            && this.props.updatedTarget !=='TARGET_OTHER'){
            for (var i = 0; i < this.props.targets.length; i++) {
                var obj = this.props.targets[i];
                if(obj.code === this.props.updatedTarget){
                    targetType = obj.code;
                    targetValue = parseInt(obj.value);
                }
            }
        }
        if(this.props.updatedCustomTarget!=null){
            targetType = "TARGET_OTHER";
            targetValue = this.props.updatedCustomTarget;
        }
        this.props.updateTarget(this.props.retailer, targetType, targetValue, this.props);
    }

    alertUpdateTarget(){
        if(this.props.updatedTarget === "TARGET_OTHER"
            && this.props.updatedCustomTarget == null){
            this.props.setUpdatedTargetMissing(true);
            return;
        }else{
            this.props.setUpdatedTargetMissing(false);
            if(this.props.updatedCustomTarget!=null && (!/^[0-9]+$/.test(this.props.updatedCustomTarget)) ){
                this.props.setUpdatedTargetInvalid(true);
                return;
            }else{
                this.props.setUpdatedTargetInvalid(false);
            }
        }
        Alert.alert(
            'Sales Target Update',
            'Are you sure you want to update the retailer sales target?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        this.updateTarget();
                    } },
            ],
            { cancelable: false }
        )
    }

    alertUpdateOrderFrequency(){
        if(this.props.updatedOrderFrequency != null){
            this.props.setUpdatedOrderFrequencyMissing(false);
            Alert.alert(
                'Order Frequency Update',
                'Are you sure you want to update the retailer order frequency?',
                [
                    {text: 'No', onPress: () => {}},
                    {text: 'Yes', onPress: () =>{
                            this.props.updateOrderFrequency(this.props.retailer, this.props.updatedOrderFrequency, this.props);
                        } },
                ],
                { cancelable: false }
            )
        }else{
            this.props.setUpdatedOrderFrequencyMissing(true);
        }
    }

    getOrderFrequencyName(){
        let orderFrequencyName = "0 order per day";
        if(this.props.retailer.orderFrequency != null && this.props.orderFrequencies != null){
            for (var i = 0; i < this.props.orderFrequencies.length; i++) {
                var obj = this.props.orderFrequencies[i];
                if(obj.code === this.props.retailer.orderFrequency){
                    orderFrequencyName = obj.name;
                }
            }
        }
        return orderFrequencyName;
    }

    isReportingManager(){
        if(this.props.salespersonDetail.id === this.props.retailer.reportingManagerId){
            return true;
        }
        return false;
    }

    alertUpdateContact(){
        let newContact = this.props.newContact;
        if(newContact!=null && newContact.length === 10 && this.props.newContactType != null){
            Alert.alert(
                'Adding new retailer contact',
                'Are you sure you want to add new retailer contact?',
                [
                    {text: 'No', onPress: () => {}},
                    {text: 'Yes', onPress: () =>{
                            this.props.addRetailerNewContact(this.props.newContact, this.props.retailer.customerId,
                                this.props.newContactType.type, this.props.retailer.salesHandlerId, this.props);
                        } },
                ],
                { cancelable: false }
            )
        }else{
            this.props.setIsRUValidContactNo(false);
        }
    }

    setContactType(contactType) {
        console.log('enter setNewContactType');
        console.log('new contact type is ::: ', contactType);
        this.props.setNewContactType({"type": contactType});
    }

    makeContactPrimary(contact){
        if(contact!=null){
            Alert.alert(
                'Updating Contact To Primary!',
                'Are you sure you want to make '+contact+ ' as primary contact?',
                [
                    {text: 'No', onPress: () => {}},
                    {text: 'Yes', onPress: () =>{
                            this.props.makeContactPrimary(contact, this.props.retailer.customerId,
                                'PRIMARY', this.props.retailer.salesHandlerId, this.props);
                        } },
                ],
                { cancelable: false }
            )
        }
    }

    alertSalesDiaryIdUpdate() {
        if(this.props.updatedSalesDiaryId != null){

        }else{

        }
    }

    render() {
        return (
            <View style={styles.container}>
                {this.props.showUpdateLoader ? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#f0af3b"/>
                        <Text style={{color: '#f0af3b'}}>Please wait ...</Text>
                    </View>
                ) : null}
                <HeaderView menuIcon={false}
                            title={this.isReportingManager()?'Update Retailer':'Retailer Info'}
                            /*backHandler={ (this.props.productPrice != null && this.props.productPrice != "") ? this.alertBack : null }*/
                            {...this.props}
                            headerColor={"#4a4e4d"}/>
                <KeyboardAvoidingView style={styles.keyboardContainer}>
                <ScrollView style={styles.container}>
                    <View style={{backgroundColor:"#FFF", padding:15}}>
                        <Text style={{fontWeight: 'bold', marginBottom:5}}>Basic Info</Text>
                        <Text style={{fontSize:14, marginBottom:5}}><MaterialIcon name='person-outline' style={styles.icon}/>  {this.props.retailer.customerName}</Text>
                        <View style={{marginTop: 10, flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                            <Text style={{fontSize:14, marginBottom:5}}><AntDesignIcon name='idcard' style={styles.icon} />  {this.props.retailer.customerId}</Text>
                            {this.props.retailer.status === "ACTIVE" ? (<Text style={{fontSize:14, color:'green', marginBottom:5}}><AntDesignIcon name='checkcircle' style={styles.activeIcon}/>  Active</Text>) :null}
                            {this.props.retailer.status === "INITIATED" ? (<Text style={{fontSize:14, color:'#f0af3b', marginBottom:5}}><AntDesignIcon name='checkcircle' style={styles.icon}/>  Initiated</Text>) :null}
                            {this.props.retailer.status === "IN_ACTIVE" ? (<Text style={{fontSize:14, color:'red', marginBottom:5}}><EntypoIcon name='circle-with-cross' style={styles.deactivateIcon}/>  Inactive</Text>) :null}
                            {this.props.retailer.status === "ON_BOARDED" ? (<Text style={{fontSize:14, color:'#000', marginBottom:5}}><AntDesignIcon name='checkcircle' style={styles.icon}/>  On boarded</Text>) :null}
                            {this.props.retailer.status === "REJECTED" ? (<Text style={{fontSize:14, color:'#000', marginBottom:5}}><EntypoIcon name='circle-with-cross' style={styles.deactivateIcon}/>  Rejected</Text>) :null}
                            {this.props.retailer.status === "DISABLED" ? (<Text style={{fontSize:14, color:'#000', marginBottom:5}}><EntypoIcon name='circle-with-cross' style={styles.deactivateIcon}/>  Disabled</Text>) :null}
                            {this.props.retailer.status === "BLACK_LISTED" ? (<Text style={{fontSize:14, color:'#000', marginBottom:5}}><EntypoIcon name='circle-with-cross' style={styles.deactivateIcon}/>  BlackListed</Text>) :null}
                        </View>
                        <TouchableOpacity onPress={() => Communications.phonecall(this.props.retailer.customerContact, true)}>
                            <Text style={{fontSize:14, marginBottom:5,marginTop:10}}><MaterialIcon name='phone' style={styles.icon}/>  {this.props.retailer.customerContact}</Text>
                        </TouchableOpacity>
                        <Text style={{fontSize:14, marginBottom:5,marginTop:10}}>Sales Handler: {this.props.retailer.salesHandlerName}</Text>

                        {/*Retailer Status update starts*/}
                        { (this.isReportingManager()) ? (
                            <View style={{marginTop:15}}>
                                {this.props.retailer.status === "ACTIVE" ? (
                                        <TouchableOpacity style={styles.activateBtn}
                                                          onPress={() => this.alertStatusUpdate("IN_ACTIVE")}>
                                            <Text style={{color:'#FFF'}}>Deactivate</Text>
                                        </TouchableOpacity>
                                    ) : (<TouchableOpacity style={[styles.activateBtn,{backgroundColor:'#009933'}]}
                                                      onPress={() => this.alertStatusUpdate("ACTIVE")}>
                                        <Text style={{color:'#FFF'}}>Activate</Text>
                                    </TouchableOpacity>)
                                }
                            </View>
                        ):null}
                        {/*Retailer Status update ends*/}
                    </View>


                    {/*update retailer sales diary id starts*/}
                    <View style={{marginTop:2, backgroundColor:"#FFF", padding:15, borderRadius: 2, borderColor: "#000"}}>
                        <View style={{margin:5, marginLeft:0}}>
                            <Text style={{fontWeight: 'bold', marginBottom:5}}>Sales Diary Info</Text>
                            <Text style={{fontSize:14, marginBottom:5,marginTop:10}}>Sales Diary# {this.props.retailer.salesDiaryId === 0?"NA":this.props.retailer.salesDiaryId}</Text>

                            <View style={styles.inputContainer}>
                                <MaterialIcon name='book' style={styles.formIcon}/>
                                <TextInput
                                    style={styles.input}
                                    placeholder="New Sale Diary Id"
                                    keyboardType="number-pad"
                                    underlineColorAndroid='transparent'
                                    placeholderTextColor='#999'
                                    maxLength={10}
                                    value={this.props.updatedSalesDiaryId}
                                    onChangeText={(salesDiaryId) => this.props.setUpdatedSalesDiaryId(salesDiaryId)}
                                />
                            </View>
                            {this.props.isDuplicateSalesDiaryId?(<Text style={styles.errorText}>Retailer already exists with this Sales Diary Id</Text>) : null}
                            {this.props.isSalesDiaryIdMissing?(<Text style={styles.errorText}>Sales Diary Id cannot be blank</Text>) : null}
                            {this.props.isErrorSalesDiaryIdUpdate?(<Text style={styles.errorText}>Failed to update sales diary id</Text>):null}
                            {this.props.isSalesDiaryIdInvalid?(<Text style={styles.errorText}>Please enter a valid sales diary id</Text>):null}


                            <View style={{marginTop:15}}>
                                <TouchableOpacity style={[styles.activateBtn,{backgroundColor:'#009933'}]}
                                                  onPress={() => this.updateSDId()}>
                                    <Text style={{color:'#FFF'}}>Update</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                    {/*update retailer sales diary id ends*/}

                    {/*Add retailer contact view starts*/}
                    <View style={{marginTop:2, backgroundColor:"#FFF", padding:15, borderRadius: 2, borderColor: "#000"}}>
                        <View style={{margin:5, marginLeft:0}}>
                            <Text style={{fontWeight: 'bold', marginBottom:5}}>Contact Info</Text>


                            <FlatList style={{marginBottom: 0}} data={this.props.retailer.contactDetails}
                                      renderItem={({item}) => (
                                          <View key={item.customerId} style={{marginTop: 10, flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                              <Text>{item.countryCode} {item.contactNumber}</Text>
                                              <Text>{item.contactType}</Text>

                                              {item.contactType !== "PRIMARY" ?
                                                  (<TouchableOpacity style={{padding:5, backgroundColor:'#009933', alignItems:'center', borderRadius: 4}}
                                                                     onPress={() => this.makeContactPrimary(item.contactNumber)}>
                                                      <Text style={{color:'#FFF'}}>Make Primary</Text>
                                                  </TouchableOpacity>) :
                                                  (<TouchableOpacity style={{padding:5, backgroundColor:'#737373', alignItems:'center', borderRadius: 4}}>
                                                      <Text style={{color:'#FFF'}}>Make Primary</Text>
                                                  </TouchableOpacity>) }
                                          </View>
                                      )}
                                      keyExtractor={(item, index) => index.toString()}
                            />

                            { this.props.contactRUAlreadyExists === true ? (
                                <Text style={{
                                    fontSize: 10,
                                    textDecorationColor: '#d4272a',
                                    textAlign: 'center',
                                    paddingTop: 10,
                                    color: 'red'
                                }}> This number already exists in the database! </Text>
                            ): null }
                            { this.props.isRUValidContactNo === false ? (
                                <Text style={{
                                    fontSize: 10,
                                    textDecorationColor: '#d4272a',
                                    textAlign: 'center',
                                    paddingTop: 10,
                                    color: 'red'
                                }}> This mobile number is invalid! </Text>
                            ): null }

                            <View style={{marginTop: 10, flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                <View style={{
                                    marginTop: 5,
                                    marginBottom: 5,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    backgroundColor: '#fff'
                                }}>
                                    <View style={{width:120, flex: 1,flexDirection:'row',flexWrap:'wrap',borderColor: "#000",borderBottomWidth: 1,}}>
                                        <Text style={{fontSize: 13, backgroundColor:'#eee', paddingTop:12 }}> +91 </Text>
                                        <TextInput
                                            style={{height: 45, flex: 1,color: '#d4272a',fontSize: 13}}
                                            placeholder="New Contact"
                                            keyboardType="number-pad"
                                            underlineColorAndroid='transparent'
                                            placeholderTextColor='#999'
                                            maxLength={10}
                                            onChangeText={(contact) => this.ifValidPhoneNumber(contact)}
                                        />
                                    </View>
                                </View>
                                <View style={{width:70}}>
                                    <Dropdown
                                        rippleCentered={true}
                                        labelFontSize={10}
                                        fontSize={10}
                                        itemPadding={5}
                                        itemColor={'rgba(0, 0, 0, .54)'}
                                        selectedItemColor={'rgba(0, 0, 0, .87)'}
                                        label={'Type'}
                                        data={[{value: 'BROTHER'},{value: 'SISTER'},{value: 'MOTHER'},
                                            {value: 'FATHER'},{value: 'SPOUSE'},{value: 'RELATIVE'},{value: 'OTHER'}]}
                                        value={this.props.newContactType.type}
                                        onChangeText={this.setContactType}
                                    />
                                </View>

                                {this.props.contactRUAlreadyExists === false
                                        && this.props.isRUValidContactNo === true ?
                                    (<TouchableOpacity style={{padding:5, marginTop: 15, marginBottom: 5, backgroundColor:'#009933', alignItems:'center', borderRadius: 4}}
                                                       onPress={() => this.alertUpdateContact()}>
                                        <Text style={{color:'#FFF'}}>Add Contact</Text>
                                    </TouchableOpacity>) :
                                    (<TouchableOpacity style={{padding:5, marginTop: 15, marginBottom: 5, backgroundColor:'#737373', alignItems:'center', borderRadius: 4}}>
                                        <Text style={{color:'#FFF'}}>Add Contact</Text>
                                    </TouchableOpacity>)
                                }

                            </View>


                        </View>

                    </View>
                    {/*Add Retailer contact view ends*/}

                    {/*update retailer sales target view starts*/}
                    <View style={{marginTop:2, backgroundColor:"#FFF", padding:15, borderRadius: 2, borderColor: "#000"}}>
                        <View style={{margin:5, marginLeft:0}}>
                            <Text style={{fontWeight: 'bold', marginBottom:5}}>Sales Target</Text>
                            <Text style={{fontSize:14, marginBottom:5}}>
                                <SimpleLineIcons name='target' style={styles.icon}/>  {this.props.retailer.targetValue} Kilograms
                            </Text>
                        </View>

                        { (this.isReportingManager()) ? (
                            <View>
                                { (this.props.targets != null) ? (
                                    <View>
                                        <View style={{marginTop:15}}>
                                            <Text style={{marginBottom:10}}>Select Target </Text>
                                            <Picker
                                                style={this.props.updatedTarget == null ? {backgroundColor:"#CCC", marginBottom:5} : {backgroundColor:"#68A4CC", marginBottom:5}}
                                                mode="dropdown"
                                                selectedValue={this.props.updatedTarget}
                                                onValueChange={(target) => this.props.setUpdatedTarget(target)}>
                                                {this.props.targets != null?
                                                    Object.keys(this.props.targets).map((key) => {
                                                        return (
                                                            <Picker.Item  label={this.props.targets[key].name}
                                                                          value={this.props.targets[key].code} key={key}/>
                                                        )
                                                    })
                                                    : null}
                                            </Picker>
                                        </View>
                                    </View>
                                ):null}

                                { (this.props.targets != null && this.props.updatedTarget === 'TARGET_OTHER' && this.isReportingManager()) ? (
                                    <View>
                                        <View style={styles.inputContainer}>
                                            <SimpleLineIcons name='target' style={styles.formIcon}/>
                                            <TextInput
                                                style={styles.input}
                                                placeholder="Target Value in Kilograms"
                                                keyboardType="number-pad"
                                                underlineColorAndroid='transparent'
                                                placeholderTextColor='#999'
                                                maxLength={4}
                                                value={this.props.updatedCustomTarget}
                                                onChangeText={(updatedCustomTarget) => this.props.setUpdatedCustomTarget(updatedCustomTarget)}
                                            />
                                        </View>
                                        {this.props.isUpdatedTargetMissing?(<Text style={styles.errorText}>*Please provide sales target for retailer.</Text>) : null}
                                        {this.props.isUpdatedTargetInvalid?(<Text style={styles.errorText}>*Please provide a valid sales target.</Text>) : null}
                                    </View>
                                ):null}

                                {(this.props.updatedTarget != null || this.props.updatedCustomTarget != null) ?
                                    (<TouchableOpacity style={{
                                        padding: 5,
                                        marginTop: 15,
                                        marginBottom: 5,
                                        backgroundColor: '#009933',
                                        alignItems: 'center',
                                        borderRadius: 4
                                    }}
                                                       onPress={() => this.alertUpdateTarget()}>
                                        <Text style={{color: '#FFF'}}>Update Target</Text>
                                    </TouchableOpacity>) :
                                    (<TouchableOpacity style={{
                                        padding: 5,
                                        marginTop: 15,
                                        marginBottom: 5,
                                        backgroundColor: '#737373',
                                        alignItems: 'center',
                                        borderRadius: 4
                                    }}>
                                        <Text style={{color: '#FFF'}}>Update Target</Text>
                                    </TouchableOpacity>)
                                }

                            </View>
                        ):null}

                    </View>
                    {/*update retailer sales target view ends*/}


                    {/*update retailer order frequency view starts*/}
                    <View style={{marginTop:2, backgroundColor:"#FFF", padding:15, borderRadius: 2, borderColor: "#000"}}>
                        <View style={{margin:5, marginLeft:0}}>
                            <Text style={{fontWeight: 'bold', marginBottom:5}}>Order Frequency</Text>
                            <Text style={{fontSize:14, marginBottom:5}}>
                                <AntDesignIcon name='carryout' style={styles.icon}/> {this.getOrderFrequencyName()}</Text>
                        </View>
                        {this.props.orderFrequencies != null && this.isReportingManager()? (
                            <View>
                                <View>
                                    <View style={{marginTop:15}}>
                                        <Text style={{marginBottom:10}}>Select Order Frequency </Text>
                                        <Picker
                                            style={this.props.updatedOrderFrequency == null ? {backgroundColor:"#CCC", marginBottom:5} : {backgroundColor:"#68A4CC", marginBottom:5}}
                                            mode="dropdown"
                                            selectedValue={this.props.updatedOrderFrequency}
                                            onValueChange={(orderFrequency) => this.props.setUpdatedOrderFrequency(orderFrequency)}>
                                            {this.props.orderFrequencies != null?
                                                Object.keys(this.props.orderFrequencies).map((key) => {
                                                    return (
                                                        <Picker.Item  label={this.props.orderFrequencies[key].name}
                                                                      value={this.props.orderFrequencies[key].value} key={key}/>
                                                    )
                                                })
                                                : null}
                                        </Picker>
                                    </View>
                                    {this.props.isUpdatedOrderFrequencyMissing?(<Text style={styles.errorText}>*Please provide order frequency of the retailer, Minimum 1 order per day</Text>) : null}
                                </View>

                                {this.props.updatedOrderFrequency != null?
                                    (<TouchableOpacity style={{padding:5, marginTop: 15, marginBottom: 5, backgroundColor:'#009933', alignItems:'center', borderRadius: 4}}
                                                       onPress={() => this.alertUpdateOrderFrequency()}>
                                        <Text style={{color:'#FFF'}}>Update Order Frequency</Text>
                                    </TouchableOpacity>) :
                                    (<TouchableOpacity style={{padding:5, marginTop: 15, marginBottom: 5, backgroundColor:'#737373', alignItems:'center', borderRadius: 4}}>
                                        <Text style={{color:'#FFF'}}>Update Order Frequency</Text>
                                    </TouchableOpacity>)
                                }

                            </View>
                        ):null}

                    </View>
                    {/*update retailer order frequency view ends*/}



                    {/*update pricing view starts*/}
                    <View style={{marginTop:2, padding:10}}>
                        <View style={{margin:5, marginLeft:0}}>
                            <Text style={{fontWeight: 'bold', color:'#000'}}>Products</Text>
                        </View>
                        {this.props.products == null? (<Text style={styles.noDataText}> No Products Allocated. Please contact your sales handler. </Text>):null}
                        {this.props.products != null? (
                            <FlatList style={{marginBottom: 0}} data={this.props.products}
                                      renderItem={({item, index}) => (
                                          <View style={styles.tile}>
                                              <View style={styles.tileBar}>
                                                  <Text style={{marginBottom:1, color:'#000'}}>{item.productName}</Text>
                                              </View>
                                              <View style={{marginTop: 2, flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                                  <Text style={{marginLeft:10, marginBottom:1, color:'#000'}}>Dimensions</Text>
                                                  <Text style={{marginBottom:1, color:'#000'}}>{item.dimension}</Text>
                                              </View>
                                              <View style={{marginTop: 2, flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                                  <Text style={{marginLeft:10, marginBottom:1, color:'#000'}}>Price</Text>
                                                  <Text style={{marginBottom:1, color:'#000'}}>{item.originalPrice}</Text>
                                              </View>
                                              { (this.isReportingManager()) ? (
                                                  <View style={{marginTop: 2, flex:1, flexDirection:'row', alignItems:'center', justifyContent:'space-between'}}>
                                                      <Text style={{marginLeft:10, marginBottom:1, color:'#000'}}>Updated Price</Text>
                                                      <TextInput
                                                          style={styles.input}
                                                          placeholder="00.00"
                                                          keyboardType="number-pad"
                                                          underlineColorAndroid='transparent'
                                                          placeholderTextColor='#999'
                                                          maxLength={6}
                                                          value={item.price}
                                                          onChangeText={(price) => this.updateProductPrice(item, price, index)}
                                                      />
                                                  </View>
                                              ):null}
                                              {!this.props.isValidPrice?(<Text style={styles.errorText}>*Please enter a valid price</Text>) : null}
                                          </View>
                                      )}
                                      keyExtractor={(item, index) => index.toString()}
                            />
                        ):null}
                        { (this.isReportingManager()) ? (
                            <View>
                                {this.props.isPricesChanged ? (
                                    <TouchableOpacity style={[styles.updateBtn, {backgroundColor:'#009933'}]}
                                                      onPress={() => this.alertProductPriceUpdate(
                                                          this.props.retailer.salesHandlerId,
                                                          this.props.retailer.customerId,
                                                          this.props.products)}>
                                        <Text style={{color:'#FFF'}}>Update Pricing</Text>
                                    </TouchableOpacity>
                                ) : (
                                    <TouchableOpacity style={styles.updateBtn}
                                                      disabled={true}>
                                        <Text style={{color:'#FFF'}}>Update Pricing</Text>
                                    </TouchableOpacity>
                                )}
                            </View>
                        ):null}
                    </View>
                    {/*update pricing view ends*/}


                </ScrollView>
                </KeyboardAvoidingView>
            </View>
        );
    }


}

const styles = StyleSheet.create({
    tileBar: {
        flex:1,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        paddingRight:10,
        justifyContent:'space-between',
        backgroundColor:'#eee'
    },
    noDataText:{
        textAlign:'center',
        marginTop:50
    },
    icon: {
        fontSize: 14,
        color: '#737373',
        textAlign: 'center',
        marginRight: 10
    },
    formIcon: {
        fontSize: 20,
        color: '#CCC',
        textAlign: 'left',
        paddingTop:12,
        marginRight:0,
        marginBottom:0,
        paddingBottom:-5,
        /*backgroundColor:'#eee'*/
    },
    activeIcon: {
        fontSize: 14,
        color: '#009933',
        textAlign: 'center',
        marginRight: 10
    },
    deactivateIcon: {
        fontSize: 14,
        color: '#ff3333',
        textAlign: 'center',
        marginRight: 10
    },
    bold: {},
    italic: {fontStyle: 'italic'},
    underline: {},
    tile:{
        flex: 0,
        alignItems: 'stretch',
        justifyContent: 'flex-start',
        marginBottom: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        backgroundColor: '#fff',
        borderRadius: 4,
        padding:10
    },
    tileBody: {
        padding: 3,
        backgroundColor: '#FFF',
        borderRadius: 4
    },
    deactivateBtn:{
        padding:2,
        paddingLeft: 6,
        paddingRight: 6,
        margin: 5,
        backgroundColor:'#ff3333',
        borderRadius: 4
    },
    inputGroup:{
        flexDirection:'row',
        flex:1
    },
    container: {
        flex: 1,
        backgroundColor: '#DDD',
    },
    retailerInfo: {
        marginTop: 5,
        marginBottom: 5,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#009933'
    },
    loaderContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: 'black',
        width: width,
        height: height,
        zIndex: 1
    },
    errorText:{
        marginTop: 5,
        marginBottom: 5,
        justifyContent: 'center',
        alignItems: 'center',
        color: 'red',
        fontSize: 14,
    },
    updateBtn:{
        padding:5,
        marginBottom: 5,
        backgroundColor:'#737373',
        alignItems:'center',
        borderRadius: 4
    },
    activateBtn:{
        padding:5,
        backgroundColor:'#ff3333',
        alignItems:'center',
        borderRadius: 4
    },
    inputContainer: {
        marginLeft: 3,
        flex: 1,
        flexDirection:'row',
        flexWrap:'wrap',
        borderColor: "#000",
        borderBottomWidth: 1,
        marginBottom: 8
    },
    keyboardContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#d4272a',
    }
});

const mapStateToProps = state => {
    return {
        authDetail: state.loginReducer.authDetail,
        salespersonDetail: state.loginReducer.salespersonDetail,
        showUpdateLoader: state.retailerManagementReducer.showUpdateLoader,
        productPrice: state.retailerManagementReducer.productPrice,
        errorUpdatingPrice: state.retailerManagementReducer.errorUpdatingPrice,
        successUpdatingPrice: state.retailerManagementReducer.successUpdatingPrice,
        retailer:state.retailerManagementReducer.retailer,
        products:state.retailerManagementReducer.products,
        updatedProducts: state.retailerManagementReducer.updatedProducts,
        isPricesChanged: state.retailerManagementReducer.isPricesChanged,
        isValidPrice: state.retailerManagementReducer.isValidPrice,

        targets: state.retailerManagementReducer.targets,
        updatedTarget: state.retailerManagementReducer.updatedTarget,
        updatedCustomTarget: state.retailerManagementReducer.updatedCustomTarget,
        orderFrequencies: state.retailerManagementReducer.orderFrequencies,
        updatedOrderFrequency: state.retailerManagementReducer.updatedOrderFrequency,
        isUpdatedTargetMissing: state.retailerManagementReducer.isUpdatedTargetMissing,
        isUpdatedOrderFrequencyMissing: state.retailerManagementReducer.isUpdatedOrderFrequencyMissing,
        isUpdatedTargetInvalid: state.retailerManagementReducer.isUpdatedTargetInvalid,
        contactRUAlreadyExists: state.retailerManagementReducer.contactRUAlreadyExists,
        isRUValidContactNo: state.retailerManagementReducer.isRUValidContactNo,
        newContact: state.retailerManagementReducer.newContact,
        newContactType: state.retailerManagementReducer.newContactType,
        updatedSalesDiaryId: state.retailerManagementReducer.updatedSalesDiaryId,
        isSalesDiaryIdInvalid: state.retailerManagementReducer.isSalesDiaryIdInvalid,
        isSalesDiaryIdMissing: state.retailerManagementReducer.isSalesDiaryIdMissing,
        isDuplicateSalesDiaryId: state.retailerManagementReducer.isDuplicateSalesDiaryId,
        isErrorSalesDiaryIdUpdate: state.retailerManagementReducer.isErrorSalesDiaryIdUpdate
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setProductPrice: (productPrice) => dispatch({type: "SET_PRODUCT_PRICE", payload: productPrice}),
        updateProductPrices:(unitId, retailerId, products) => dispatch(RetailerManagementActions.updateProductPrices(unitId, retailerId, products)),
        setErrorUpdatingPrice: (errorUpdatingPrice) => dispatch({type: "SET_ERROR_UPDATING_PRICE", payload: errorUpdatingPrice}),
        setSuccessUpdatingPrice: (successUpdatingPrice) => dispatch({type: "SET_SUCCESS_UPDATING_PRICE", payload: successUpdatingPrice}),
        updateStatus: (retailer, status, props) => dispatch(RetailerManagementActions.updateStatus(retailer, status, props)),
        getUnitProductPrices: (unitId, retailerId) => dispatch(RetailerManagementActions.getUnitProductPrices(unitId, retailerId)),
        setUpdatedProducts: (updatedProducts) => dispatch({type: "SET_UPDATED_PRODUCTS", payload: updatedProducts}),
        setIsPricesChanged: (isPricesChanged) => dispatch({type: "SET_IS_PRICES_CHANGED", payload: isPricesChanged}),
        setIsValidPrice: (isValidPrice) => dispatch({type: "SET_IS_VALID_PRICE", payload: isValidPrice}),
        setPrice: (products, index, price) => dispatch(RetailerManagementActions.setPrice(products, index, price)),
        setUpdatedTarget: (updatedTarget) => dispatch({type: "SET_UPDATED_TARGET", payload: updatedTarget}),
        setUpdatedCustomTarget: (updatedCustomTarget) => dispatch({type: "SET_UPDATED_CUSTOM_TARGET", payload: updatedCustomTarget}),
        setUpdatedOrderFrequency: (updatedOrderFrequency) => dispatch({type: "SET_UPDATED_ORDER_FREQUENCY", payload: updatedOrderFrequency}),
        fetchTargetAttributes: (salesHandlerId) => dispatch(RetailerManagementActions.getTargetAttributes(salesHandlerId)),
        setUpdatedTargetMissing: (isUpdatedTargetMissing) => dispatch({type: "SET_ERROR_UPDATED_TARGET_MISSING", payload: isUpdatedTargetMissing}),
        setUpdatedTargetInvalid: (isUpdatedTargetInvalid) => dispatch({type: "SET_ERROR_UPDATED_TARGET_INVALID", payload: isUpdatedTargetInvalid}),
        setUpdatedOrderFrequencyMissing: (isUpdatedOrderFrequencyMissing) => dispatch({type: "SET_ERROR_UPDATED_ORDER_FREQUENCY_MISSING", payload: isUpdatedOrderFrequencyMissing}),
        updateTarget: (retailer, targetType, targetValue, props) => dispatch(RetailerManagementActions.updateTarget(retailer, targetType, targetValue, props)),
        updateOrderFrequency: (retailer, orderFrequency, props) => dispatch(RetailerManagementActions.updateOrderFrequency(retailer, orderFrequency, props)),
        setIsRUValidContactNo: (isValidContactNo) => dispatch({type: "SET_IS_RU_VALID_CONTACT_NO", payload: isValidContactNo}),
        setContactRUAlreadyExists: (isExists) => dispatch({type: "SET_RU_CONTACT_ALREADY_EXISTS", payload: isExists}),
        lookupRetailerContact: (contact) => dispatch(RetailerManagementActions.lookupRetailerContact(contact)),
        addRetailerNewContact: (newContact, retailerId, contactType, salesHandlerId, props) =>
            dispatch(RetailerManagementActions.addRetailerNewContact(newContact, retailerId, contactType, salesHandlerId, props)),
        setNewContactType: (contactType) => dispatch({type: "SET_NEW_CONTACT_TYPE", payload: contactType}),
        makeContactPrimary: (contact, retailerId, contactType, salesHandlerId, props) =>
            dispatch(RetailerManagementActions.makeContactPrimary(contact, retailerId, contactType, salesHandlerId, props)),
        setUpdatedSalesDiaryId: (updatedSalesDiaryId) => dispatch({type: "SET_UPDATED_SALES_DIARY_ID", payload: updatedSalesDiaryId}),
        updateSalesDiaryId:(updatedSalesDiaryId, retailer) => dispatch(RetailerManagementActions.updateSalesDiaryId(updatedSalesDiaryId, retailer))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(RetailerUpdateScreen);