import React, {Component} from 'react';
import {connect} from 'react-redux';
import {
    StyleSheet,
    Text,
    View,
    ScrollView,
    Alert,
    KeyboardAvoidingView,
    ActivityIndicator,
    Picker,
    TouchableOpacity,
    Switch
} from 'react-native';
import SelectMultiple from 'react-native-select-multiple'
import HeaderView from "./HeaderView";
import MaterialIcon from "react-native-vector-icons/MaterialIcons";
import NavigationService from "./../store/services/NavigationService";
import * as ChangeManagerActions from "./../store/actions/ChangeManagerActions";
import UtilityService from "../store/services/UtilityService";
var Dimensions = require('Dimensions');
var { width, height } = Dimensions.get('window');


class ChangeManagerScreen extends Component {

    constructor(props) {
        super(props);
        this.handleBackButtonClick = this.handleBackButtonClick.bind(this);
    }

    componentWillMount() {
        this.props.navigation.addListener(
            'didFocus',
            payload => {
                console.debug('didFocus', payload);
                this.init();
            }
        );
    }

    componentWillUnmount() {
        if (this.props.navigation && this.props.navigation.removeListener) {
            this.props.navigation.removeListener('didFocus');
        }
    }

    handleBackButtonClick() {
        this.props.navigation.goBack(null);
    }

    init() {
        this.props.setManager(null);
        if(this.props.manager!=null){
            this.props.setManagerList(this.props.manager, this.props)
        }
        this.props.setErrorChangeManager(false);
        this.props.setSuccessChangeManager(false);
        this.props.setSelectedSalesHandlers(null);
        this.props.setSelectAllSalesHandlers(false);
        this.props.setNewManager(null);
        this.props.setCMFormViewEnabled(true);
        this.props.getManagerList(this.props.salespersonDetail.id);
    }

    toggleSwitch = (value) => {
        if(value === true){
            this.props.setSelectAllSalesHandlers(true);
            this.props.setSelectedSalesHandlers(this.props.salesHandlers);
        }else{
            this.props.setSelectAllSalesHandlers(false);
            this.props.setSelectedSalesHandlers(null);
        }
    };

    selectNewHandlerView(){
        this.props.setCMFormViewEnabled(false);
    }

    alertBack(){
        const prop = this.props;
        Alert.alert(
            'Go Back',
            'Are you sure you want to discard your changes?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        NavigationService.navigate('Home');
                    } },
            ],
            { cancelable: false }
        )
    }

    changeManagerConfirm(){
        Alert.alert(
            'Changing Manager!',
            'Are you sure, you want to change manager for the selected salesHandlers?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        this.props.changeManager(this.props);
                    } },
            ],
            { cancelable: false }
        )
    }

    render() {
        return (
            <View style={styles.container}>
                {this.props.showCMLoader ? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#f0af3b"/>
                        <Text style={{color: '#f0af3b'}}>Please wait ...</Text>
                    </View>
                ) : null}
                <HeaderView menuIcon={false}
                            title={'Change Manager'}
                            backHandler={ (this.props.manager != null && this.props.manager !== "") ? this.alertBack : null }
                            {...this.props} headerColor={"#4a4e4d"}/>

                <KeyboardAvoidingView style={styles.keyboardContainer}>
                    <ScrollView style={styles.container} ref='_scrollView'>
                        {/*Audit Form view Starts*/}
                        {this.props.cMFormViewEnabled ? (
                            <View style={styles.formContainer}>

                                <View style={{
                                    flex:0,
                                    flexDirection:'row',
                                    alignItems: 'flex-start',
                                    justifyContent:'flex-start',
                                    marginTop:10,
                                    marginBottom:10,
                                    padding:10
                                }}>
                                    <View style={{width:"50%"}}>
                                        <Text style={{marginBottom:10}}>Old Manager </Text>
                                        <Picker
                                            style={this.props.manager == null ?
                                                {backgroundColor:"#CCC", marginBottom:5, height: 40} :
                                                {backgroundColor:"#44A3A2", marginBottom:5,height: 40}}
                                            mode="dropdown"
                                            selectedValue={this.props.manager}
                                            onValueChange={(manager) => this.props.setManagerList(manager, this.props)}>

                                            {this.props.managers !=null?
                                                Object.keys(this.props.managers).map((key) => {
                                                    return (
                                                        <Picker.Item  label={this.props.managers[key]} value={key} key={key}/>
                                                    )
                                                })
                                                : null}
                                        </Picker>
                                    </View>

                                    <View style={{marginLeft:15, width:"50%"}}>
                                        <Text style={{marginBottom:10}}>New Manager </Text>
                                        <Picker
                                            style={this.props.newManager == null ?
                                                {backgroundColor:"#CCC", marginBottom:5, height: 40} :
                                                {backgroundColor:"#AEC491", marginBottom:5, height: 40}
                                            }
                                            mode="dropdown"
                                            selectedValue={this.props.newManager}
                                            onValueChange={(newManager) => this.props.setNewManager(newManager)}>

                                            {this.props.managers !=null?
                                                Object.keys(this.props.managers).map((key) => {
                                                    return (
                                                        <Picker.Item  label={this.props.managers[key]} value={key} key={key}/>
                                                    )
                                                })
                                                : null}
                                        </Picker>
                                    </View>

                                </View>

                                { this.props.noSalesHandlersFound ? (
                                    <Text style={styles.alertValidationTextStyle}> No sales Handlers found under {this.props.managerName} </Text>
                                ): null }

                                {this.props.salesHandlers != null && !UtilityService.checkEmpty(this.props.salesHandlers) ?
                                    (<View>
                                        <View style={{
                                            flex:0,
                                            flexDirection:'row',
                                            alignItems: 'flex-start',
                                            justifyContent:'flex-start',
                                            marginTop:10,
                                            marginBottom:10,
                                            padding:10
                                        }}>
                                            <Switch
                                                onValueChange = {this.toggleSwitch}
                                                value = {this.props.selectAllSalesHandlers}/>
                                            <Text>Select All</Text>

                                            {this.props.selectedSalesHandlers !=null
                                            && !UtilityService.checkEmpty(this.props.selectedSalesHandlers)
                                            && this.props.newManager !=null
                                            && this.props.newManager !== this.props.manager ? (
                                                    <TouchableOpacity style={styles.navBtn}
                                                                      onPress={() => this.changeManagerConfirm()}>
                                                        <Text style={{color:'#4a4e4d'}}>Change Manager</Text>
                                                    </TouchableOpacity>
                                                ) :
                                                <Text style={[styles.navBtn, {color:'#FDFBF8',backgroundColor:'#DDD'}]}>Change Manager</Text>
                                            }

                                        </View>

                                        <View style={{padding:10}}>

                                            <SelectMultiple
                                                rowStyle={{backgroundColor: '#F9E7B9', border:2, borderRadius:5}}
                                                items={this.props.salesHandlers}
                                                selectedItems={this.props.selectedSalesHandlers}
                                                onSelectionsChange={(selectedSalesHandlers) => {
                                                    this.props.setSelectedSalesHandlers(selectedSalesHandlers)
                                                }}
                                                keyExtractor={(item, index) => index.toString()}
                                            />

                                        </View>
                                    </View>):null}
                            </View>
                        ):null}

                        {/*Error Info view Starts*/}
                        {this.props.errorChangeManager ? (
                            <View style={styles.errorAddingContainer}>
                                <MaterialIcon name='error' style={styles.errorIcon}/>
                                <Text style={styles.alertTextStyle}> Some error occured, Please Try Again ! </Text>
                                <View style={{flex: 1, alignItems: 'stretch'}}>
                                    <TouchableOpacity style={styles.buttonStyle}
                                                      onPress={() => {this.init()}}>
                                        <Text style={{textDecorationColor: '#FDFBF8'}}>Try Again</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ):null}
                        {/*Error Info view Ends*/}
                        {/*Success Info view Starts*/}
                        {this.props.successChangeManager ? (
                            <View style={styles.successAddingContainer}>
                                <MaterialIcon name='check-box' style={styles.successIcon}/>
                                <Text style={styles.alertTextStyle}> Manager Changed Successfully ! </Text>
                                <View style={{flex: 1, alignItems: 'stretch'}}>
                                    <TouchableOpacity style={styles.buttonStyle}
                                                      onPress={() => {this.init()}}>
                                        <Text style={{textDecorationColor: '#FDFBF8'}}>Change Another</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ):null}
                        {/*Success Info view Ends*/}
                        {/*Audit Form view Ends*/}
                    </ScrollView>
                </KeyboardAvoidingView>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FDFBF8',
    },
    formContainer: {
        flex: 1,
        backgroundColor: '#FDFBF8',
        paddingLeft:10,
        paddingRight:10
    },
    loaderContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: 'black',
        width: width,
        height: height,
        zIndex: 1
    },
    keyboardContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#d4272a',
    },
    titleButton: {
        backgroundColor: '#fff',
        borderRadius: 4,
        padding: 3,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 2
    },
    titleBtnText: {color: '#436180', textAlign: 'center', fontSize: 10, fontWeight: 'bold'},
    navBtn:{
        padding:5,
        paddingLeft: 15,
        paddingRight: 15,
        marginLeft: 90,
        backgroundColor:'#fed766',
        borderRadius: 4,
        elevation: 3
    },
    alertValidationTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: 'red'
    },
    errorAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#FDFBF8'
    },
    successAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#FDFBF8'
    },
    errorIcon: {
        fontSize: 60,
        color: '#d4272a',
        textAlign: 'center',
    },
    successIcon: {
        fontSize: 60,
        color: '#009933',
        textAlign: 'center',
    },
    alertTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: '#f0af3b'
    },
    buttonStyle: {
        flex: 0,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0af3b',
        borderWidth: 1,
        borderRadius: 5,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        borderColor: '#FDFBF8',
        marginTop: 20,
        marginBottom:10
    }
});

const mapStateToProps = state => {
    return {
        salespersonDetail: state.loginReducer.salespersonDetail,
        showCMLoader: state.changeManagerReducer.showCMLoader,
        manager: state.changeManagerReducer.manager,
        newManager: state.changeManagerReducer.newManager,
        managerName: state.changeManagerReducer.managerName,
        managers: state.changeManagerReducer.managers,
        salesHandlers: state.changeManagerReducer.salesHandlers,
        selectedSalesHandlers: state.changeManagerReducer.selectedSalesHandlers,
        selectAllSalesHandlers: state.changeManagerReducer.selectAllSalesHandlers,
        cMFormViewEnabled:state.changeManagerReducer.cMFormViewEnabled,
        noSalesHandlersFound: state.changeManagerReducer.noSalesHandlersFound,
        errorChangeManager: state.changeManagerReducer.errorChangeManager,
        successChangeManager: state.changeManagerReducer.successChangeManager
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setManager: (manager) => dispatch({type: "SET_CM_MANAGER", payload: manager}),
        setNewManager: (newManager) => dispatch({type: "SET_NEW_MANAGER", payload: newManager}),
        setManagerName: (managerName) => dispatch({type: "SET_CM_MANAGER_NAME", payload: managerName}),
        setManagers: (managers) => dispatch({type: "SET_CM_MANAGERS", payload: managers}),
        setManagerList: (managerId, props) => dispatch(ChangeManagerActions.setManagerList(managerId, props)),
        getManagerList: (auditorId) => dispatch(ChangeManagerActions.getManagerList(auditorId)),
        setSelectedSalesHandlers: (selectedSalesHandlers) => dispatch({type: "SET_CM_SELECTED_SALES_HANDLERS", payload: selectedSalesHandlers}),
        setSelectAllSalesHandlers: (selectAllSalesHandlers) => dispatch({type: "SET_CM_SELECT_ALL_SALES_HANDLERS", payload: selectAllSalesHandlers}),
        setCMFormViewEnabled: (cMFormViewEnabled) => dispatch({type: "SET_CM_FORM_VIEW_ENABLED", payload: cMFormViewEnabled}),
        setErrorChangeManager: (errorChangeManager) => dispatch({type: "SET_ERROR_CHANGE_MANAGER", payload: errorChangeManager}),
        setSuccessChangeManager: (successChangeManager) => dispatch({type: "SET_SUCCESS_CHANGE_MANAGER", payload: successChangeManager}),
        changeManager:(props) => dispatch(ChangeManagerActions.changeManager(props))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(ChangeManagerScreen);