import React, {Component} from 'react';
import {connect} from 'react-redux';
import {
    StyleSheet,
    Text,
    View,
    ScrollView,
    Alert,
    KeyboardAvoidingView,
    ActivityIndicator,
    Picker,
    TouchableOpacity,
    Switch
} from 'react-native';
import SelectMultiple from 'react-native-select-multiple'
import HeaderView from "./HeaderView";
import MaterialIcon from "react-native-vector-icons/MaterialIcons";
import NavigationService from "./../store/services/NavigationService";
import * as ChangeHandlerActions from "./../store/actions/ChangeHandlerActions";
import UtilityService from "../store/services/UtilityService";
var Dimensions = require('Dimensions');
var { width, height } = Dimensions.get('window');


class ChangeHandlerScreen extends Component {

    constructor(props) {
        super(props);
        this.handleBackButtonClick = this.handleBackButtonClick.bind(this);
    }

    componentWillMount() {
        this.props.navigation.addListener(
            'didFocus',
            payload => {
                console.debug('didFocus', payload);
                this.init();
            }
        );
    }

    componentWillUnmount() {
        if (this.props.navigation && this.props.navigation.removeListener) {
            this.props.navigation.removeListener('didFocus');
        }
    }

    handleBackButtonClick() {
        this.props.navigation.goBack(null);
    }

    init() {
        this.props.setSalesHandler(null);
        if(this.props.salesHandler!=null){
           this.props.setSalesHandlerList(this.props.salesHandler, this.props)
        }
        this.props.setErrorChangeHandler(false);
        this.props.setSuccessChangeHandler(false);
        this.props.setSelectedRetailers(null);
        this.props.setSelectAllRetailers(false);
        this.props.setNewSalesHandler(null);
        this.props.setCHFormViewEnabled(true);
        this.props.getSalesHandlerList(this.props.salespersonDetail.id);
    }

    toggleSwitch = (value) => {
        if(value === true){
            this.props.setSelectAllRetailers(true);
            this.props.setSelectedRetailers(this.props.retailers);
        }else{
            this.props.setSelectAllRetailers(false);
            this.props.setSelectedRetailers(null);
        }
    };

    selectNewHandlerView(){
        this.props.setCHFormViewEnabled(false);
    }

    alertBack(){
        const prop = this.props;
        Alert.alert(
            'Go Back',
            'Are you sure you want to discard your changes?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        NavigationService.navigate('Home');
                    } },
            ],
            { cancelable: false }
        )
    }

    changeSalesHandlerConfirm(){
        Alert.alert(
            'Changing Sales Handler!',
            'Are you sure, you want to change sales handler for the selected retailers?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        this.props.changeHandler(this.props);
                    } },
            ],
            { cancelable: false }
        )
    }

    render() {
        return (
            <View style={styles.container}>
                {this.props.showCHLoader ? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#f0af3b"/>
                        <Text style={{color: '#f0af3b'}}>Please wait ...</Text>
                    </View>
                ) : null}
                <HeaderView menuIcon={false}
                            title={'Change Handler'}
                            backHandler={ (this.props.salesHandler != null && this.props.salesHandler !== "") ? this.alertBack : null }
                            {...this.props} headerColor={"#4a4e4d"}/>

                <KeyboardAvoidingView style={styles.keyboardContainer}>
                    <ScrollView style={styles.container} ref='_scrollView'>
                        {/*Audit Form view Starts*/}
                        {this.props.cHFormViewEnabled ? (
                            <View style={styles.formContainer}>

                                    <View style={{
                                        flex:0,
                                        flexDirection:'row',
                                        alignItems: 'flex-start',
                                        justifyContent:'flex-start',
                                        marginTop:10,
                                        marginBottom:10,
                                        padding:10
                                    }}>
                                        <View style={{width:"50%"}}>
                                            <Text style={{marginBottom:10}}>Old Sales Handler </Text>
                                            <Picker
                                                style={this.props.salesHandler == null ?
                                                    {backgroundColor:"#CCC", marginBottom:5, height: 40} :
                                                    {backgroundColor:"#44A3A2", marginBottom:5,height: 40}}
                                                mode="dropdown"
                                                selectedValue={this.props.salesHandler}
                                                onValueChange={(salesHandler) => this.props.setSalesHandlerList(salesHandler, this.props)}>

                                                {this.props.salesHandlers !=null?
                                                    Object.keys(this.props.salesHandlers).map((key) => {
                                                        return (
                                                            <Picker.Item  label={this.props.salesHandlers[key]} value={key} key={key}/>
                                                        )
                                                    })
                                                    : null}
                                            </Picker>
                                        </View>

                                        <View style={{marginLeft:15, width:"50%"}}>
                                            <Text style={{marginBottom:10}}>New Sales Handler </Text>
                                            <Picker
                                                style={this.props.newSalesHandler == null ?
                                                    {backgroundColor:"#CCC", marginBottom:5, height: 40} :
                                                    {backgroundColor:"#AEC491", marginBottom:5, height: 40}
                                                }
                                                mode="dropdown"
                                                selectedValue={this.props.newSalesHandler}
                                                onValueChange={(newSalesHandler) => this.props.setNewSalesHandler(newSalesHandler)}>

                                                {this.props.salesHandlers !=null?
                                                    Object.keys(this.props.salesHandlers).map((key) => {
                                                        return (
                                                            <Picker.Item  label={this.props.salesHandlers[key]} value={key} key={key}/>
                                                        )
                                                    })
                                                    : null}
                                            </Picker>
                                        </View>

                                    </View>

                                { this.props.noRetailersFound ? (
                                    <Text style={styles.alertValidationTextStyle}> No retailers found under {this.props.salesHandlerName} </Text>
                                ): null }

                                {this.props.retailers != null && !UtilityService.checkEmpty(this.props.retailers) ?
                                    (<View>
                                        <View style={{
                                            flex:0,
                                            flexDirection:'row',
                                            alignItems: 'flex-start',
                                            justifyContent:'flex-start',
                                            marginTop:10,
                                            marginBottom:10,
                                            padding:10
                                        }}>
                                            <Switch
                                                onValueChange = {this.toggleSwitch}
                                                value = {this.props.selectAllRetailers}/>
                                            <Text>Select All</Text>

                                            {this.props.selectedRetailers !=null
                                                && !UtilityService.checkEmpty(this.props.selectedRetailers)
                                                && this.props.newSalesHandler !=null
                                                && this.props.newSalesHandler !== this.props.salesHandler ? (
                                                    <TouchableOpacity style={styles.navBtn}
                                                                      onPress={() => this.changeSalesHandlerConfirm()}>
                                                        <Text style={{color:'#4a4e4d'}}>Change Handler</Text>
                                                    </TouchableOpacity>
                                                ) :
                                                <Text style={[styles.navBtn, {color:'#FDFBF8',backgroundColor:'#DDD'}]}>Change Handler</Text>
                                            }

                                        </View>

                                        <View style={{padding:10}}>

                                            <SelectMultiple
                                                rowStyle={{backgroundColor: '#F9E7B9', border:2, borderRadius:5}}
                                                items={this.props.retailers}
                                                selectedItems={this.props.selectedRetailers}
                                                onSelectionsChange={(selectedRetailers) => {
                                                    this.props.setSelectedRetailers(selectedRetailers)
                                                }}
                                                keyExtractor={(item, index) => index.toString()}
                                            />

                                        </View>
                                    </View>):null}
                            </View>
                        ):null}

                        {/*Error Info view Starts*/}
                        {this.props.errorChangeHandler ? (
                            <View style={styles.errorAddingContainer}>
                                <MaterialIcon name='error' style={styles.errorIcon}/>
                                <Text style={styles.alertTextStyle}> Some error occured, Please Try Again ! </Text>
                                <View style={{flex: 1, alignItems: 'stretch'}}>
                                    <TouchableOpacity style={styles.buttonStyle}
                                                      onPress={() => {this.init()}}>
                                        <Text style={{textDecorationColor: '#FDFBF8'}}>Try Again</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ):null}
                        {/*Error Info view Ends*/}
                        {/*Success Info view Starts*/}
                        {this.props.successChangeHandler ? (
                            <View style={styles.successAddingContainer}>
                                <MaterialIcon name='check-box' style={styles.successIcon}/>
                                <Text style={styles.alertTextStyle}> Handler Changed Successfully ! </Text>
                                <View style={{flex: 1, alignItems: 'stretch'}}>
                                    <TouchableOpacity style={styles.buttonStyle}
                                                      onPress={() => {this.init()}}>
                                        <Text style={{textDecorationColor: '#FDFBF8'}}>Change Another</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ):null}
                        {/*Success Info view Ends*/}
                        {/*Audit Form view Ends*/}
                    </ScrollView>
                </KeyboardAvoidingView>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FDFBF8',
    },
    formContainer: {
        flex: 1,
        backgroundColor: '#FDFBF8',
        paddingLeft:10,
        paddingRight:10
    },
    loaderContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: 'black',
        width: width,
        height: height,
        zIndex: 1
    },
    keyboardContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#d4272a',
    },
    titleButton: {
        backgroundColor: '#fff',
        borderRadius: 4,
        padding: 3,
        paddingLeft: 15,
        paddingRight: 15,
        margin: 2
    },
    titleBtnText: {color: '#436180', textAlign: 'center', fontSize: 10, fontWeight: 'bold'},
    navBtn:{
        padding:5,
        paddingLeft: 15,
        paddingRight: 15,
        marginLeft: 90,
        backgroundColor:'#fed766',
        borderRadius: 4,
        elevation: 3
    },
    alertValidationTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: 'red'
    },
    errorAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#FDFBF8'
    },
    successAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#FDFBF8'
    },
    errorIcon: {
        fontSize: 60,
        color: '#d4272a',
        textAlign: 'center',
    },
    successIcon: {
        fontSize: 60,
        color: '#009933',
        textAlign: 'center',
    },
    alertTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: '#f0af3b'
    },
    buttonStyle: {
        flex: 0,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0af3b',
        borderWidth: 1,
        borderRadius: 5,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        borderColor: '#FDFBF8',
        marginTop: 20,
        marginBottom:10
    }
});

const mapStateToProps = state => {
    return {
        salespersonDetail: state.loginReducer.salespersonDetail,
        showCHLoader: state.changeHandlerReducer.showCHLoader,
        salesHandler: state.changeHandlerReducer.salesHandler,
        newSalesHandler: state.changeHandlerReducer.newSalesHandler,
        salesHandlerName: state.changeHandlerReducer.salesHandlerName,
        salesHandlers: state.changeHandlerReducer.salesHandlers,
        retailers: state.changeHandlerReducer.retailers,
        selectedRetailers: state.changeHandlerReducer.selectedRetailers,
        selectAllRetailers: state.changeHandlerReducer.selectAllRetailers,
        cHFormViewEnabled:state.changeHandlerReducer.cHFormViewEnabled,
        noRetailersFound: state.changeHandlerReducer.noRetailersFound,
        errorChangeHandler: state.changeHandlerReducer.errorChangeHandler,
        successChangeHandler: state.changeHandlerReducer.successChangeHandler
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setSalesHandler: (salesHandler) => dispatch({type: "SET_CH_SALES_HANDLER", payload: salesHandler}),
        setNewSalesHandler: (newSalesHandler) => dispatch({type: "SET_NEW_SALES_HANDLER", payload: newSalesHandler}),
        setSalesHandlerName: (salesHandlerName) => dispatch({type: "SET_CH_SALES_HANDLER_NAME", payload: salesHandlerName}),
        setSalesHandlers: (salesHandlers) => dispatch({type: "SET_CH_SALES_HANDLERS", payload: salesHandlers}),
        setSalesHandlerList: (salesHandlerId, props) => dispatch(ChangeHandlerActions.setSalesHandlerList(salesHandlerId, props)),
        getSalesHandlerList: (auditorId) => dispatch(ChangeHandlerActions.getSalesHandlerList(auditorId)),
        setSelectedRetailers: (selectedRetailers) => dispatch({type: "SET_CH_SELECTED_RETAILERS", payload: selectedRetailers}),
        setSelectAllRetailers: (selectAllRetailers) => dispatch({type: "SET_CH_SELECT_ALL_RETAILERS", payload: selectAllRetailers}),
        setCHFormViewEnabled: (cHFormViewEnabled) => dispatch({type: "SET_CH_FORM_VIEW_ENABLED", payload: cHFormViewEnabled}),
        setErrorChangeHandler: (errorChangeHandler) => dispatch({type: "SET_ERROR_CHANGE_HANDLER", payload: errorChangeHandler}),
        setSuccessChangeHandler: (successChangeHandler) => dispatch({type: "SET_SUCCESS_CHANGE_HANDLER", payload: successChangeHandler}),
        changeHandler:(props) => dispatch(ChangeHandlerActions.changeHandler(props))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(ChangeHandlerScreen);