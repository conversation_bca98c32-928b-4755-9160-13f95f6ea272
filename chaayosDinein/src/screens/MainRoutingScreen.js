import React, {Component} from 'react';
import {connect} from "react-redux";
import {ActivityIndicator, Image, StyleSheet, Text, View} from 'react-native';
import NetInfo from "@react-native-community/netinfo";
import * as LoginActions from "./../store/actions/LoginActions";
import SplashScreen from "react-native-splash-screen";

class MainRoutingScreen extends Component {

    static navigationOptions = {
        header: null
    };

    componentWillMount() {
        NetInfo.fetch().then(state => {
            console.log("Connection type", state.type);
            console.log("Is connected?", state.isConnected);
            if (state.isConnected != false) {
                this.props.handleRouting(this.props);
            } else {
                this.props.setIsConnected(state.isConnected, this.props);
            }
        });
        NetInfo.addEventListener(state => {
            if (state.isConnected != true) {
                this.props.setIsConnected(state.isConnected, this.props);
            }
        });
        //unsubscribe();
    }

    componentDidMount(){
        SplashScreen.hide();
    }

    componentWillUnmount(){

    }

    render() {
        return (
                <View style={styles.container}>
                    <View style={styles.loginContainer}>
                        <Image resizeMode="contain" style={styles.logo}
                               source={require('../assets/img/loginLogo.png')}/>
                    </View>
                    <View style={styles.formContainer}>
                        <View style={{height: 300, marginTop: 100}}>
                            <ActivityIndicator size="large" color='#d4272a'/>
                            <Text style={{color: '#d4272a'}}>
                                Please wait while we load your app.
                            </Text>
                        </View>
                    </View>
                </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        //backgroundColor: 'rgba(45,61,84,1)',
        backgroundColor: '#f9cd54',
        top: 0
    },
    loginContainer: {
        alignItems: 'center',
        flexGrow: 1,
        justifyContent: 'center'
    },
    logo: {
        marginTop: 100,
        width: 120,
        height: 120
    },
    formContainer: {
        alignItems: 'center',
        flexGrow: 1,
        justifyContent: 'center'
    },
    errorMessage: {
        color: 'red'
    }
});

const mapStateToProps = state => {
    return {
        locale: state.loginReducer.locale,
        userId: state.loginReducer.userId,
        password: state.loginReducer.password,
        authDetail: state.loginReducer.authDetail,
        internetConnected: state.loginReducer.internetConnected
    }
};

const mapDispatchToProps = dispatch => {
    return {
        handleRouting: (props) => dispatch(LoginActions.handleRouting(props)),
        setIsConnected: (connected) => dispatch(LoginActions.setIsConnected(connected))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(MainRoutingScreen);
