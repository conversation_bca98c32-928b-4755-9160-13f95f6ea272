import React, {Component} from 'react';
import {connect} from 'react-redux';
import {
    StyleSheet,
    Text,
    View,
    ScrollView,
    Image,
    TextInput,
    TouchableOpacity,
    BackHandler,
    PixelRatio,
    Alert,
    KeyboardAvoidingView,
    ActivityIndicator,
    Picker
} from 'react-native';
import HeaderView from "./HeaderView";
import MaterialIcon from "react-native-vector-icons/MaterialIcons";
import NavigationService from "./../store/services/NavigationService";
import * as AuditFormActions from "./../store/actions/AuditFormActions";
import RadioForm from 'react-native-simple-radio-button';
import DateTimePicker from 'react-native-modal-datetime-picker';
import UtilityService from "../store/services/UtilityService";
var Dimensions = require('Dimensions');
var { width, height } = Dimensions.get('window');


class AuditFormScreen extends Component {

    constructor(props) {
        super(props);
        this.handleBackButtonClick = this.handleBackButtonClick.bind(this);
    }

    componentWillMount() {
        this.props.navigation.addListener(
            'didFocus',
            payload => {
                console.debug('didFocus', payload);
                this.init();
            }
        );
    }

    componentWillUnmount() {
        if (this.props.navigation && this.props.navigation.removeListener) {
            this.props.navigation.removeListener('didFocus');
        }
    }

    handleBackButtonClick() {
        this.props.navigation.goBack(null);
    }

    init() {
        this.props.getActivityTypes();
        this.props.setAuditFormViewEnabled(true);
        this.props.setErrorActivityAudit(false);
        this.props.setSuccessActivityAudit(false);

        if(this.props.salespersonDetail.designation === "Territory Sales Executive") {
            this.props.setSalesHandlerList(this.props.salespersonDetail.id, this.props)
        }else{
            this.props.getSalesHandlerList(this.props.salespersonDetail.id);
        }
    }

    setRetailer(retailerId) {
        if (retailerId != null && retailerId !== 0) {
            this.props.setRetailer(retailerId);
            if(this.props.retailers[retailerId]!=null){
                let retailerName = this.props.retailers[retailerId];
                this.props.setRetailerName(retailerName);

                let salesDiaryId = this.props.retailersSalesDiaryIds[retailerId];
                this.props.setRetailerSalesDiaryId(salesDiaryId);
            }
        }
    }

    setAuditDatePicked(date) {
        this.props.setAuditDate(date);
        this.props.setIsDatePickerVisible(false);
    }

    setAuditTimePicked(time) {
        this.props.setAuditTime(time);
        this.props.setIsTimePickerVisible(false);
    }

    getDisplayDate(date) {
        return UtilityService.formatDate(date, 'dd-MM-yyyy');
    }

    getDisplayTime(time) {
        let hours = time.getHours();
        let mins = time.getMinutes();
        let auditTime = [hours, mins].join(':');
        return auditTime;
    }

    submit() {
        let auditorId = this.props.salespersonDetail.id;
        if(this.props.salespersonDetail.designation === "Territory Sales Executive"){
            auditorId = this.props.salespersonDetail.reportingManagerId;
        }
        let auditData = {
            "auditor": auditorId,
            "auditorName": this.props.salespersonDetail.name,
            "salesHandler": this.props.salesHandler,
            "salesHandlerName":this.props.salesHandlerName,
            "retailer":this.props.retailer,
            "retailerName": this.props.retailerName,
            "salesDiaryId": this.props.retailerSalesDiaryId,
            "activityType" : this.props.activityType,
            "isPromoter":this.props.isPromoter,
            "isNewRetailer":this.props.isNewRetailer,
            "auditDate":this.props.auditDate,
            "auditTime":this.props.auditTime,
            "retailerAgreedForSS": this.props.retailerAgreedForSS,
            "retailerSSOrderQty": this.props.retailerSSOrderQty,
            "retailerSSPaymentStatus": this.props.retailerSSPaymentStatus,
            "amountFromRetailer": this.props.amountFromRetailer,
            "orderPlacedViaIVR": this.props.orderPlacedViaIVR,
            "kilogramsCommitted": this.props.kilogramsCommitted,
            "retailerGIFTAIM": this.props.retailerGIFTAIM,
            "signUPFormSigned": this.props.signUPFormSigned
        };
        this.props.validateAndSaveAudit(auditData);
        this.refs._scrollView.scrollTo(0,0, true);
    }

    alertBack(){
        const prop = this.props;
        Alert.alert(
            'Go Back',
            'Are you sure you want to discard your changes?',
            [
                {text: 'No', onPress: () => {}},
                {text: 'Yes', onPress: () =>{
                        NavigationService.navigate('Home');
                    } },
            ],
            { cancelable: false }
        )
    }

    render() {
        return (
            <View style={styles.container}>
                {this.props.showAuditLoader ? (
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#f0af3b"/>
                        <Text style={{color: '#f0af3b'}}>Please wait ...</Text>
                    </View>
                ) : null}
                <HeaderView menuIcon={false}
                            title={'Activity Audit Form'}
                            backHandler={ (this.props.salesHandler != null && this.props.salesHandler !== "") ? this.alertBack : null }
                            {...this.props} headerColor={"#4a4e4d"}/>
                <KeyboardAvoidingView style={styles.keyboardContainer}>
                    <ScrollView style={styles.container} ref='_scrollView'>
                        {/*Audit Form view Starts*/}
                        {this.props.auditFormViewEnabled ? (
                            <View style={styles.formContainer}>

                                <View style={{marginTop:15}}>
                                    { this.props.isSalesHandlerMissing ? (
                                        <Text style={styles.alertValidationTextStyle}> Please select a Sales Handler ! </Text>
                                    ): null }
                                    { this.props.noSalesHandlersFound ? (
                                        <Text style={styles.alertValidationTextStyle}> No sales handler found under this auditor ! </Text>
                                    ): null }
                                    { this.props.isAuditorMissing ? (
                                        <Text style={styles.alertValidationTextStyle}> Please select an Auditor ! </Text>
                                    ): null }
                                    { this.props.noRetailersFound ? (
                                        <Text style={styles.alertValidationTextStyle}> No retailer found under this sales handler ! </Text>
                                    ): null }
                                    { this.props.isRetailerMissing ? (
                                        <Text style={styles.alertValidationTextStyle}> Please select a Retailer ! </Text>
                                    ): null }
                                    { this.props.isSalesDiaryIdMissing ? (
                                        <Text style={styles.alertValidationTextStyle}> Invalid Retailer! Sales Diary Id missing! </Text>
                                    ): null }
                                    { this.props.isAuditDateMissing ? (
                                        <Text style={styles.alertValidationTextStyle}> Please select the activity completed date ! </Text>
                                    ): null }
                                    { this.props.isAuditTimeMissing ? (
                                        <Text style={styles.alertValidationTextStyle}> Please select the activity completed time ! </Text>
                                    ): null }
                                    { this.props.isActivityTypeMissing ? (
                                        <Text style={styles.alertValidationTextStyle}> Please select an activity type ! </Text>
                                    ): null }
                                    { this.props.missingAmountOfPayment ? (
                                        <Text style={styles.alertValidationTextStyle}> Please enter amount of payment taken from retailer! </Text>
                                    ): null }
                                    { this.props.missingKGCommitted ? (
                                        <Text style={styles.alertValidationTextStyle}> Please enter kilograms committed by retailer! </Text>
                                    ): null }
                                    { this.props.missingAIMToWIN ? (
                                        <Text style={styles.alertValidationTextStyle}> Please enter retailer AIM to win which gift! </Text>
                                    ): null }
                                </View>


                                {(this.props.salespersonDetail.designation === "Territory Sales Executive")?
                                    (<View style={{marginTop:2}}>
                                        <Text style={{marginBottom:10}}>Handler:  {this.props.salespersonDetail.name}</Text>
                                    </View>) :
                                    (<View style={{marginTop:2}}>
                                        <Text style={{marginBottom:10}}>Auditor:  {this.props.salespersonDetail.name}</Text>
                                    </View>)
                                }


                                {this.props.salespersonDetail.designation !== "Territory Sales Executive"?
                                    <View style={{marginTop:15}}>
                                        <Text style={{marginBottom:10}}>Sales Handler </Text>
                                        <Picker
                                            style={this.props.salesHandler == null ? {backgroundColor:"#CCC", marginBottom:5, height: 40} : {backgroundColor:"#66ccff", marginBottom:5, height: 40}}
                                            mode="dropdown"
                                            selectedValue={this.props.salesHandler}
                                            onValueChange={(salesHandler) => this.props.setSalesHandlerList(salesHandler, this.props)}>

                                            {this.props.salesHandlers !=null?
                                                Object.keys(this.props.salesHandlers).map((key) => {
                                                    return (
                                                        <Picker.Item  label={this.props.salesHandlers[key]} value={key} key={key}/>
                                                    )
                                                })
                                                : null}
                                        </Picker>
                                    </View> : null}


                                <View style={{marginTop:15}}>
                                    <Text style={{marginBottom:10}}>Retailer </Text>
                                    <Picker
                                        style={this.props.retailer == null ? {backgroundColor:"#CCC", marginBottom:5, height: 40} : {backgroundColor:"#66ccff", marginBottom:5, height: 40}}
                                        mode="dropdown"
                                        selectedValue={this.props.retailer}
                                        onValueChange={(retailer) => this.setRetailer(retailer)}>
                                        {this.props.retailers != null?
                                            Object.keys(this.props.retailers).map((key) => {
                                                return (
                                                    <Picker.Item  label={this.props.retailers[key]} value={key} key={key}/>
                                                )
                                            })
                                            : null}
                                    </Picker>
                                    { (this.props.retailerSalesDiaryId != null && this.props.retailerSalesDiaryId !== 0) ? (
                                        <Text style={{marginBottom:10}}>Sales Diary Id : {this.props.retailerSalesDiaryId} </Text>
                                    ):null}
                                </View>

                                <View style={{marginTop:15}}>
                                    <Text style={{marginBottom:10}}>Activity Type</Text>
                                    <Picker
                                        style={this.props.activityType == null ? {backgroundColor:"#CCC", marginBottom:5, height: 40} : {backgroundColor:"#66ccff", marginBottom:5, height: 40}}
                                        mode="dropdown"
                                        selectedValue={this.props.activityType}
                                        onValueChange={(activityType) => this.props.setActivityType(activityType)}>
                                        {this.props.activityTypes != null?
                                            Object.keys(this.props.activityTypes).map((key) => {
                                                return (
                                                    <Picker.Item  label={this.props.activityTypes[key]} value={key} key={key}/>
                                                )
                                            })
                                            : null}
                                    </Picker>
                                </View>

                                {this.props.activityType!=null? (
                                    <View style={{marginTop:15}}>
                                        <Text style={{marginBottom:10}}>Activity Created Date.</Text>
                                        <TouchableOpacity  style={this.props.auditDate == null ? styles.auditTimeDateButtonInactive : styles.auditTimeDateButtonActive}
                                                           onPress={() => {this.props.setIsDatePickerVisible(true)}}>
                                            {this.props.auditDate!=null?
                                                <View><Text>Date : {this.getDisplayDate(this.props.auditDate)}</Text></View>
                                                :   <View><Text>Select Date</Text></View>}
                                        </TouchableOpacity>
                                        <DateTimePicker
                                            isVisible={this.props.isDatePickerVisible}
                                            onConfirm={(date) => {this.setAuditDatePicked(date)}}
                                            onCancel={() => {this.props.setIsDatePickerVisible(false)}}
                                        />

                                        {this.props.activityType === "SUPERSTAR_ENROLLMENT"? (
                                            <View>
                                                <View style={{marginTop:15}}>
                                                    <Text style={{marginBottom:10}}>Retailer Agreed For Superstar</Text>
                                                    <RadioForm
                                                        radio_props={[{label: '  Yes   ', value: "YES" },
                                                            {label: '  No  ', value: "NO" },
                                                            {label: '  Maybe  ', value: "MAYBE" },
                                                            {label: '  Need Time  ', value: "NEED_TIME" }]}
                                                        formHorizontal={false}
                                                        initial={0}
                                                        onPress={(value) => {this.props.setRetailerAgreedForSS(value)}}
                                                    />
                                                </View>
                                                <View style={{marginTop:15}}>
                                                    <Text style={{marginBottom:10}}>Superstar Enroll Order Qty [ Packets ]</Text>
                                                    <RadioForm
                                                        radio_props={[{label: '  1 Packet   ', value: "1_PKT" },
                                                            {label: '  2 Packet  ', value: "2_PKT" },
                                                            {label: '  3 Packet  ', value: "3_PKT" },
                                                            {label: '  4 Packet  ', value: "4_PKT" },
                                                            {label: '  5 Packet  ', value: "5_PKT" },
                                                            {label: '  5 Packet +  ', value: "5_PKT+" }]}
                                                        formHorizontal={false}
                                                        initial={0}
                                                        onPress={(value) => {this.props.setRetailerSSOrderQty(value)}}
                                                    />
                                                </View>
                                                <View style={{marginTop:15}}>
                                                    <Text style={{marginBottom:10}}>Payment Taken from Retailer & Updated in Sales Diary?</Text>
                                                    <RadioForm
                                                        radio_props={[{label: '  No  ', value: "NO" },
                                                            {label: '  Yes   ', value: "YES" },
                                                            {label: '  Yes but Sales Diary Not Updated  ', value: "YES_BUT_SD_NOT_UPDATED" }]}
                                                        formHorizontal={false}
                                                        initial={0}
                                                        onPress={(value) => {this.props.setRetailerSSPaymentStatus(value)}}
                                                    />
                                                </View>
                                                <View style={{marginTop:15,borderColor: "#000",
                                                    borderBottomWidth: 1}}>
                                                    <Text style={{marginBottom:10}}>Amount of Payment taken from Retailer?</Text>
                                                    <TextInput
                                                        style={styles.input}
                                                        placeholder="Enter Amount"
                                                        keyboardType="number-pad"
                                                        underlineColorAndroid='transparent'
                                                        placeholderTextColor='#999'
                                                        maxLength={10}
                                                        value={this.props.amountFromRetailer}
                                                        onChangeText={(amountFromRetailer) => this.props.setAmountFromRetailer(amountFromRetailer)}
                                                    />
                                                </View>
                                                <View style={{marginTop:15}}>
                                                    <Text style={{marginBottom:10}}>Order placed via IVR also?</Text>
                                                    <RadioForm
                                                        radio_props={[{label: '  No  ', value: "NO" },
                                                            {label: '  NO - Retailer Phone Issue  ', value: "PHONE_ISSUE" },
                                                            {label: '  Yes   ', value: "YES" }]}
                                                        formHorizontal={false}
                                                        initial={0}
                                                        onPress={(value) => {this.props.setOrderPlacedViaIVR(value)}}
                                                    />
                                                </View>
                                                <View style={{marginTop:15,borderColor: "#000",
                                                    borderBottomWidth: 1}}>
                                                    <Text style={{marginBottom:10}}>KG Commited by Retailer in SuperStar Program?</Text>
                                                    <TextInput
                                                        style={styles.input}
                                                        placeholder="Enter Kilograms"
                                                        keyboardType="number-pad"
                                                        underlineColorAndroid='transparent'
                                                        placeholderTextColor='#999'
                                                        maxLength={10}
                                                        value={this.props.kilogramsCommitted}
                                                        onChangeText={(kilogramsCommitted) => this.props.setKilogramsCommitted(kilogramsCommitted)}
                                                    />
                                                </View>
                                                <View style={{marginTop:15, borderColor: "#000",
                                                    borderBottomWidth: 1}}>
                                                    <Text style={{marginBottom:10}}>Retailer AIM to win which Gift?</Text>
                                                    <TextInput
                                                        style={styles.input}
                                                        placeholder="Enter AIM"
                                                        keyboardType="default"
                                                        underlineColorAndroid='transparent'
                                                        placeholderTextColor='#999'
                                                        maxLength={300}
                                                        value={this.props.retailerGIFTAIM}
                                                        onChangeText={(retailerGIFTAIM) => this.props.setRetailerGIFTAIM(retailerGIFTAIM)}
                                                    />
                                                </View>
                                                <View style={{marginTop:15}}>
                                                    <Text style={{marginBottom:10}}>Retailer Sign-Up form signed for Program?</Text>
                                                    <RadioForm
                                                        radio_props={[{label: '  No  ', value: "NO" },
                                                            {label: '  Yes   ', value: "YES" },
                                                            {label: '  No Form Received  ', value: "NO_FORM_RECEIVED" }]}
                                                        formHorizontal={false}
                                                        initial={0}
                                                        onPress={(value) => {this.props.setSignUPFormSigned(value)}}
                                                    />
                                                </View>
                                            </View>

                                        ):null}

                                        {/*<TouchableOpacity  style={this.props.auditTime == null ? styles.auditTimeDateButtonInactive : styles.auditTimeDateButtonActive}
                                                           onPress={() => {this.props.setIsTimePickerVisible(true)}}>
                                            {this.props.auditTime!=null?
                                                <View><Text>Time : {this.getDisplayTime(this.props.auditTime)}</Text></View>
                                                :   <View><Text>Select Time</Text></View>}
                                        </TouchableOpacity>
                                        <DateTimePicker
                                            mode={'time'}
                                            isVisible={this.props.isTimePickerVisible}
                                            onConfirm={(time) => {this.setAuditTimePicked(time)}}
                                            onCancel={() => {this.props.setIsTimePickerVisible(false)}}
                                        />*/}
                                    </View>) : null}

                                <View style={{marginTop:15}}>
                                    <Text style={{marginBottom:10}}>New Retailer</Text>
                                    <RadioForm
                                        radio_props={[{label: '  Yes   ', value: true },{label: '  No  ', value: false }]}
                                        formHorizontal={true}
                                        initial={1}
                                        onPress={(value) => {this.props.setIsNewRetailer(value)}}
                                    />
                                </View>

                                <View style={{marginTop:15}}>
                                    <Text style={{marginBottom:10}}>Promoter</Text>
                                    <RadioForm
                                        radio_props={[{label: '  Yes   ', value: true },{label: '  No  ', value: false }]}
                                        formHorizontal={true}
                                        initial={1}
                                        onPress={(value) => {this.props.setIsPromoter(value)}}
                                    />
                                </View>

                                <View style={{marginTop:15}}>
                                    <TouchableOpacity onPress={() => this.submit()}>
                                        <View style={styles.buttonStyle}>
                                            <Text style={styles.buttonTitleStyle}>Submit</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => this.props.resetAuditFormFields()}>
                                        <View style={[styles.buttonStyle, {backgroundColor:"#9B9B9B"}]}>
                                            <Text style={styles.buttonTitleStyle}>Reset</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>

                            </View>
                        ):null}
                        {/*Audit Form view Ends*/}

                        {/*Error Info view Starts*/}
                        {this.props.errorActivityAudit ? (
                            <View style={styles.errorAddingContainer}>
                                <MaterialIcon name='error' style={styles.errorIcon}/>
                                <Text style={styles.alertTextStyle}> Some error occured, Please Try Again ! </Text>
                                <View style={{flex: 1, alignItems: 'stretch'}}>
                                    <TouchableOpacity style={styles.buttonStyle}
                                                      onPress={() => {this.init()}}>
                                        <Text style={styles.buttonTitleStyle}>Try Again</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ):null}
                        {/*Error Info view Ends*/}
                        {/*Success Info view Starts*/}
                        {this.props.successActivityAudit ? (
                            <View style={styles.successAddingContainer}>
                                <MaterialIcon name='check-box' style={styles.successIcon}/>
                                <Text style={styles.alertTextStyle}> Activity Audit Added ! </Text>
                                <View style={{flex: 1, alignItems: 'stretch'}}>
                                    <TouchableOpacity style={styles.buttonStyle}
                                                      onPress={() => {this.init()}}>
                                        <Text style={styles.buttonTitleStyle}>Add Another</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ):null}
                        {/*Success Info view Ends*/}
                    </ScrollView>
                </KeyboardAvoidingView>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FDFBF8',
    },
    formContainer: {
        flex: 1,
        backgroundColor: '#FDFBF8',
        paddingLeft:30,
        paddingRight:30
    },
    imageStyle:{
        paddingVertical: 30,
        width: 80,
        height: 80,
        borderRadius: 75,
        marginTop: 10,
        marginBottom: 10
    },
    inputGroup:{
        flexDirection:'row',
        flex:1
    },
    inputContainer: {
        marginLeft: 3,
        flex: 1,
        flexDirection:'row',
        flexWrap:'wrap',
        borderColor: "#000",
        borderBottomWidth: 1,
        marginBottom: 8
    },
    errorAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#FDFBF8'
    },
    successAddingContainer:{
        paddingVertical: 30,
        marginTop: 20,
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#FDFBF8'
    },
    alertTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: '#f0af3b'
    },
    alertValidationTextStyle: {
        textDecorationColor: '#d4272a',
        textAlign: 'center',
        padding: 10,
        color: 'red'
    },
    label: {
        color:'#000',
    },
    input: {
        flex:1,
        flexDirection: "row",
        paddingLeft: 5,
        paddingBottom:0
    },
    buttonStyle: {
        flex: 0,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0af3b',
        borderWidth: 1,
        borderRadius: 5,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        borderColor: '#FDFBF8',
        marginTop: 20,
        marginBottom:10
    },
    buttonTitleStyle: {
        textDecorationColor: '#FDFBF8'
    },
    avatarContainer: {
        borderColor: '#9B9B9B',
        borderWidth: 1 / PixelRatio.get(),
        justifyContent: 'center',
        alignItems: 'center',
    },
    avatar: {
        borderRadius: 75,
        width: 150,
        height: 150,
    },
    inputContactBox: {
        height: 45,
        marginLeft: 10,
        flex: 1,
        color: '#d4272a',
        fontSize: 20
    },
    icon: {
        fontSize: 60,
        color: '#737373',
        textAlign: 'center',
    },
    errorIcon: {
        fontSize: 60,
        color: '#d4272a',
        textAlign: 'center',
    },
    successIcon: {
        fontSize: 60,
        color: '#009933',
        textAlign: 'center',
    },
    formIcon: {
        fontSize: 20,
        color: '#CCC',
        textAlign: 'left',
        paddingTop:12,
        marginRight:0,
        marginBottom:0,
        paddingBottom:-5,
        /*backgroundColor:'#eee'*/

    },
    errorText:{
        marginTop: 5,
        marginBottom: 5,
        justifyContent: 'center',
        alignItems: 'center',
        color: 'red',
        fontSize: 7,
    },
    keyboardContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'stretch',
        backgroundColor: '#d4272a',
    },
    loaderContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: 'black',
        width: width,
        height: height,
        zIndex: 1
    },
    auditTimeDateButtonInactive : {
        backgroundColor: '#9B9B9B',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight:50,
        marginLeft:50,
        borderWidth: 3,
        borderRadius: 50,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        borderColor: '#FDFBF8'
    },
    auditTimeDateButtonActive : {
        backgroundColor: '#66ccff',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight:50,
        marginLeft:50,
        borderWidth: 3,
        borderRadius: 50,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        borderColor: '#FDFBF8'
    }
});

const mapStateToProps = state => {
    return {
        authDetail: state.loginReducer.authDetail,
        salespersonDetail: state.loginReducer.salespersonDetail,
        showAuditLoader: state.auditFormReducer.showAuditLoader,
        salesHandler: state.auditFormReducer.salesHandler,
        salesHandlerName: state.auditFormReducer.salesHandlerName,
        salesHandlers: state.auditFormReducer.salesHandlers,
        retailer: state.auditFormReducer.retailer,
        retailerName: state.auditFormReducer.retailerName,
        retailers: state.auditFormReducer.retailers,
        activityTypes: state.auditFormReducer.activityTypes,
        activityType: state.auditFormReducer.activityType,
        isNewRetailer: state.auditFormReducer.isNewRetailer,
        isPromoter: state.auditFormReducer.isPromoter,
        noSalesHandlersFound: state.auditFormReducer.noSalesHandlersFound,
        noRetailersFound: state.auditFormReducer.noRetailersFound,
        isAuditorMissing: state.auditFormReducer.isAuditorMissing,
        isSalesHandlerMissing: state.auditFormReducer.isSalesHandlerMissing,
        isRetailerMissing: state.auditFormReducer.isRetailerMissing,
        isAuditDateMissing: state.auditFormReducer.isAuditDateMissing,
        isAuditTimeMissing: state.auditFormReducer.isAuditTimeMissing,
        successActivityAudit:state.auditFormReducer.successActivityAudit,
        errorActivityAudit:state.auditFormReducer.errorActivityAudit,
        auditFormViewEnabled:state.auditFormReducer.auditFormViewEnabled,
        auditDate: state.auditFormReducer.auditDate,
        auditTime: state.auditFormReducer.auditTime,
        isDatePickerVisible: state.auditFormReducer.isDatePickerVisible,
        isTimePickerVisible: state.auditFormReducer.isTimePickerVisible,
        retailerSalesDiaryId: state.auditFormReducer.retailerSalesDiaryId,
        retailersSalesDiaryIds: state.auditFormReducer.retailersSalesDiaryIds,
        isSalesDiaryIdMissing: state.auditFormReducer.isSalesDiaryIdMissing,
        isActivityTypeMissing: state.auditFormReducer.isActivityTypeMissing,
        retailerAgreedForSS: state.auditFormReducer.retailerAgreedForSS,
        retailerSSOrderQty: state.auditFormReducer.retailerSSOrderQty,
        retailerSSPaymentStatus: state.auditFormReducer.retailerSSPaymentStatus,
        amountFromRetailer: state.auditFormReducer.amountFromRetailer,
        orderPlacedViaIVR: state.auditFormReducer.orderPlacedViaIVR,
        kilogramsCommitted: state.auditFormReducer.kilogramsCommitted,
        retailerGIFTAIM: state.auditFormReducer.retailerGIFTAIM,
        signUPFormSigned: state.auditFormReducer.signUPFormSigned,
        missingAmountOfPayment:state.auditFormReducer.missingAmountOfPayment,
        missingKGCommitted:state.auditFormReducer.missingKGCommitted,
        missingAIMToWIN:state.auditFormReducer.missingAIMToWIN
    }
};

const mapDispatchToProps = dispatch => {
    return {
        getActivityTypes: () => dispatch(AuditFormActions.getActivityTypes()),
        setSalesHandler: (salesHandler) => dispatch({type: "SET_AUDIT_SALES_HANDLER", payload: salesHandler}),
        setSalesHandlerName: (salesHandlerName) => dispatch({type: "SET_AUDIT_SALES_HANDLER_NAME", payload: salesHandlerName}),
        setSalesHandlers: (salesHandlers) => dispatch({type: "SET_AUDIT_SALES_HANDLERS", payload: salesHandlers}),
        setRetailer: (retailer) => dispatch({type: "SET_AUDIT_RETAILER", payload: retailer}),
        setRetailerName: (retailerName) => dispatch({type: "SET_AUDIT_RETAILER_NAME", payload: retailerName}),
        setActivityType: (activityType) => dispatch({type: "SET_ACTIVITY_TYPE", payload: activityType}),
        setIsNewRetailer: (isNewRetailer) => dispatch({type: "SET_IS_NEW_RETAILER", payload: isNewRetailer}),
        setIsPromoter: (isPromoter) => dispatch({type: "SET_IS_PROMOTER", payload: isPromoter}),
        getSalesHandlerList: (auditorId) => dispatch(AuditFormActions.getSalesHandlerList(auditorId)),
        getRetailers: (salesHandlerId) => dispatch(AuditFormActions.getRetailers(salesHandlerId)),
        setSuccessActivityAudit: (successActivityAudit) => dispatch({type: "SET_ACTIVITY_AUDIT_SUCCESS", payload: successActivityAudit}),
        setErrorActivityAudit: (errorActivityAudit) => dispatch({type: "SET_ACTIVITY_AUDIT_ERROR", payload: errorActivityAudit}),
        setAuditFormViewEnabled: (auditFormViewEnabled) => dispatch({type: "SET_AUDIT_FORM_VIEW_ENABLED", payload: auditFormViewEnabled}),
        setAuditDate: (auditDate) => dispatch({type: "SET_AUDIT_DATE", payload: auditDate}),
        setAuditTime: (auditTime) => dispatch({type: "SET_AUDIT_TIME", payload: auditTime}),
        setIsDatePickerVisible: (isDatePickerVisible) => dispatch({type: "SET_IS_DATE_PICKER_VISIBLE", payload: isDatePickerVisible}),
        setIsTimePickerVisible: (isTimePickerVisible) => dispatch({type: "SET_IS_TIME_PICKER_VISIBLE", payload: isTimePickerVisible}),
        validateAndSaveAudit: (auditData) => dispatch(AuditFormActions.validateAndSaveAudit(auditData)),
        setRetailerSalesDiaryId: (retailerSalesDiaryId) => dispatch({type: "SET_RETAILER_SALES_DIARY_ID", payload: retailerSalesDiaryId}),
        setSalesHandlerList: (salesHandlerId, props) => dispatch(AuditFormActions.setSalesHandlerList(salesHandlerId, props)),
        resetAuditFormFields: () => dispatch(AuditFormActions.resetAuditFormFields()),
        setRetailerAgreedForSS: (retailerAgreedForSS) => dispatch({type: "SET_RETAILER_AGREED_FOR_SS", payload: retailerAgreedForSS}),
        setRetailerSSOrderQty: (retailerSSOrderQty) => dispatch({type: "SET_RETAILER_SS_ORDER_QTY", payload: retailerSSOrderQty}),
        setRetailerSSPaymentStatus: (retailerSSPaymentStatus) => dispatch({type: "SET_RETAILER_SS_PAYMENT_STATUS", payload: retailerSSPaymentStatus}),
        setAmountFromRetailer: (amountFromRetailer) => dispatch({type: "SET_AMOUNT_FROM_RETAILER", payload: amountFromRetailer}),
        setOrderPlacedViaIVR: (orderPlacedViaIVR) => dispatch({type: "SET_ORDER_PLACED_VIA_IVR", payload: orderPlacedViaIVR}),
        setKilogramsCommitted: (kilogramsCommitted) => dispatch({type: "SET_KILOGRAMS_COMMITTED", payload: kilogramsCommitted}),
        setRetailerGIFTAIM: (retailerGIFTAIM) => dispatch({type: "SET_RETAILER_GIFT_AIM", payload: retailerGIFTAIM}),
        setSignUPFormSigned: (signUPFormSigned) => dispatch({type: "SET_SIGN_UP_FORM_SIGNED", payload: signUPFormSigned})
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(AuditFormScreen);