import React, {Component} from 'react';
import {View, Text, ActivityIndicator, TouchableOpacity} from "react-native";
import {connect} from "react-redux";
import UtilityService from "../store/services/UtilityService";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import FontAwesome5 from "react-native-vector-icons/FontAwesome5";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

import * as TargetsActions from "../store/actions/TargetsActions";

class TargetsView extends Component {

    componentWillMount() {
        console.log("is current day in targets view>>>>>>>>>>>>>>>>>>", this.props.isCurrentDay);
        if(this.props.isCurrentDay === false){
            this.props.setTargets(null);
            this.props.setAchievedTargets(null);
            this.props.setSelectedTargetType(null);
        }
        if(this.props.openedTargetType != null
                && this.props.openedTargetType !== 'NONE'
                    && this.props.isCurrentDay === true){
            this.loadHandlerTargets(this.props.openedTargetType, this.props.handlerId, this.props.date);
        }
        console.log("Loader Sales Handler Summary Loading.... ", this.props.salesHandlerSummaryLoading);
    }

    loadHandlerTargets(targetType, handlerId, date) {
        console.log("Enter loadHandlerTargets");
        let isTargetsEmpty =  UtilityService.checkEmpty(this.props.targets);
        console.log("isTargetsEmpty", isTargetsEmpty);
        console.log("targetType", targetType);
        console.log("handlerId", handlerId);
        console.log("date", date);
        if(targetType === "NONE"){
            return;
        }
        if(!isTargetsEmpty && this.props.targets.targetType === this.props.selectedTargetType){
            this.props.setTargets(null);
            this.props.setAchievedTargets(null);
            this.props.setSelectedTargetType(this.props.selectedTargetType);
        }
        if((isTargetsEmpty || this.props.targets.targetType !== targetType)) {
            let today = new Date();
            let dd = today.getDate();
            let mm = today.getMonth() + 1; //January is 0!
            let yyyy = today.getFullYear();

            if (dd < 10) { dd = '0' + dd; }
            if (mm < 10) { mm = '0' + mm; }
            today = dd + '-' + mm + '-' + yyyy;

            if(targetType === "DAILY"){
                this.props.fetchHandlerTargets("TARGET", targetType, handlerId, date, 0, 0, 0);
                this.props.fetchHandlerTargets("ACHIEVED", targetType, handlerId, date, 0, 0, 0);
            }
            if(targetType === "WEEKLY"){
                let week = Math.floor((dd - 1)/7) + 1;
                console.log("Fetching Weekly targets for week: ", week);
                this.props.fetchHandlerTargets("TARGET", targetType, handlerId, "", week, mm, yyyy);
                this.props.fetchHandlerTargets("ACHIEVED", targetType, handlerId, "", week, mm, yyyy);
            }
            if(targetType === "MONTHLY"){
                console.log("Fetching Monthly targets for month: ", mm);
                this.props.fetchHandlerTargets("TARGET", targetType, handlerId, "", 0, mm, yyyy);
                this.props.fetchHandlerTargets("ACHIEVED", targetType, handlerId, "", 0, mm, yyyy);
            }
            if(targetType === "YEARLY"){
                console.log("Fetching Yearly targets for year: ", yyyy);
                this.props.fetchHandlerTargets("TARGET", targetType, handlerId, "", 0, 0, yyyy);
                this.props.fetchHandlerTargets("ACHIEVED", targetType, handlerId, "", 0, mm, yyyy);
            }
        }
    }

    getValue(value){
        if(value == null || value === 0){
            return "N/A";
        }
        if(isNaN(value)) return value;
        if(value < 100000) {
            return Math.round(value);
        }
        if( value < 10000000) {
            return (value/100000).toFixed(2) + " L";
        }
        if(value < 1000000000) {
            return Math.round((value/10000000)) + " Cr";
        }
    }

    render() {
        return (

            <View>

                <View style={{flex:0,
                    alignItems:'stretch',
                    justifyContent:'flex-start'}}>
                    <View style={{flex:0,
                        flexDirection:'row',
                        alignItems: 'flex-start',
                        justifyContent:'flex-start'}}>
                        <View style=
                                  {this.props.selectedTargetType === "DAILY" && this.props.selectedHandlerId === this.props.handlerId?
                                    {   flex: 1,margin: 5,backgroundColor:"#5e7e47",borderColor:"#FDFBF8",borderRadius:5, elevation: 5}
                                    :
                                    {   flex: 1,margin: 5,backgroundColor:"#777",borderColor:"#FDFBF8",borderRadius:5, elevation: 5}
                                  }>
                            <TouchableOpacity style={styles.targetTypeHeaderBtn}
                                              onPress={() => this.loadHandlerTargets("DAILY", this.props.handlerId, this.props.date)}>
                                <Text style={{flex: 3, color: '#FDFBF8', textAlign: 'center', fontSize: 10}}>Daily</Text>
                            </TouchableOpacity>
                        </View>
                        <View style=
                                  {this.props.selectedTargetType === "WEEKLY" && this.props.selectedHandlerId === this.props.handlerId?
                                    {   flex: 1,margin: 5,backgroundColor:"#5e7e47",borderColor:"#FDFBF8",borderRadius:5, elevation: 5}
                                    :
                                    {   flex: 1,margin: 5,backgroundColor:"#777",borderColor:"#FDFBF8",borderRadius:5, elevation: 5}
                                }>
                            <TouchableOpacity style={styles.targetTypeHeaderBtn}
                                              onPress={() => this.loadHandlerTargets("WEEKLY", this.props.handlerId, this.props.date)}>
                                <Text style={{flex: 3, color: '#FDFBF8', textAlign: 'center', fontSize: 10}}>Weekly</Text>
                            </TouchableOpacity>
                        </View>
                        <View style=
                                  {this.props.selectedTargetType === "MONTHLY" && this.props.selectedHandlerId === this.props.handlerId?
                                      {   flex: 1,margin: 5,backgroundColor:"#5e7e47",borderColor:"#FDFBF8",borderRadius:5, elevation: 5}
                                      :
                                      {   flex: 1,margin: 5,backgroundColor:"#777",borderColor:"#FDFBF8",borderRadius:5, elevation: 5}
                                  }>
                            <TouchableOpacity style={styles.targetTypeHeaderBtn}
                                              onPress={() => this.loadHandlerTargets("MONTHLY", this.props.handlerId, this.props.date)}>
                                <Text style={{flex: 3, color: '#FDFBF8', textAlign: 'center', fontSize: 10}}>Monthly</Text>
                            </TouchableOpacity>
                        </View>
                        <View style=
                                  {this.props.selectedTargetType === "YEARLY" && this.props.selectedHandlerId === this.props.handlerId?
                                      {   flex: 1, margin: 5, backgroundColor:"#5e7e47",borderColor:"#FDFBF8",borderRadius:5, elevation: 5}
                                      :
                                      {   flex: 1,margin: 5,backgroundColor:"#777",borderColor:"#FDFBF8",borderRadius:5, elevation: 5}
                                  }>
                            <TouchableOpacity style={styles.targetTypeHeaderBtn}
                                              onPress={() => this.loadHandlerTargets("YEARLY", this.props.handlerId, this.props.date)}>
                                <Text style={{flex: 3, color: '#FDFBF8', textAlign: 'center', fontSize: 10}}>Yearly</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    { this.props.selectedHandlerId === this.props.handlerId ?
                        (<View>

                            { this.props.targets != null && this.props.achievedTargets != null ? (
                                <View style={{flex: 0, justifyContent: 'flex-start', alignItems: 'stretch'}}>

                                    <View style={styles.targetTileBody}>

                                        <View style={styles.targetContainer}>
                                            <View style={styles.targetHeaderRow}>
                                                {/*50 Kg Retailer Block Starts*/}
                                                <View style={styles.targetHeaderCell}>
                                                    <View style={styles.targetBody}>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{backgroundColor:'#9dc6d8'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome5 name={'hashtag'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.targets.retailerCount50kg)}</Text>
                                                            </View>
                                                            <View style={[styles.targetValueBox,{ backgroundColor:'#d2b29b'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome name={'rupee'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.targets.retailerSale50kg)}</Text>
                                                            </View>
                                                        </View>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{backgroundColor:'#9dc6d8'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome5 name={'hashtag'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.achievedTargets.retailerCount50kg)}</Text>
                                                            </View>
                                                            <View style={[styles.targetValueBox,{ backgroundColor:'#d2b29b'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome name={'rupee'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.achievedTargets.retailerSale50kg)}</Text>
                                                            </View>
                                                        </View>
                                                    </View>

                                                    <Text style={styles.targetHeaderCellValue}>50Kg Retailer</Text>
                                                </View>
                                                {/*50 Kg Retailer Block Ends*/}

                                                {/*30 Kg Retailer Block Starts*/}
                                                <View style={styles.targetHeaderCell}>

                                                    <View style={styles.targetBody}>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{backgroundColor:'#9dc6d8'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome5 name={'hashtag'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.targets.retailerCount30kg)}</Text>
                                                            </View>
                                                            <View style={[styles.targetValueBox,{ backgroundColor:'#d2b29b'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome name={'rupee'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.targets.retailerSale30kg)}</Text>
                                                            </View>
                                                        </View>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{backgroundColor:'#9dc6d8'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome5 name={'hashtag'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.achievedTargets.retailerCount30kg)}</Text>
                                                            </View>
                                                            <View style={[styles.targetValueBox,{backgroundColor:'#d2b29b'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome name={'rupee'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.achievedTargets.retailerSale30kg)}</Text>
                                                            </View>
                                                        </View>
                                                    </View>

                                                    <Text style={styles.targetHeaderCellValue}>30Kg Retailer</Text>
                                                </View>
                                                {/*30 Kg Retailer Block Ends*/}

                                                {/*4 Kg Retailer Block Starts*/}
                                                <View style={styles.targetHeaderCell}>

                                                    <View style={styles.targetBody}>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{ backgroundColor:'#9dc6d8'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome5 name={'hashtag'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.targets.retailerCount4kg)}</Text>
                                                            </View>
                                                            <View style={[styles.targetValueBox,{ backgroundColor:'#d2b29b'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome name={'rupee'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.targets.retailerSale4kg)}</Text>
                                                            </View>
                                                        </View>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{ backgroundColor:'#9dc6d8'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome5 name={'hashtag'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.achievedTargets.retailerCount4kg)}</Text>
                                                            </View>
                                                            <View style={[styles.targetValueBox,{backgroundColor:'#d2b29b'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome name={'rupee'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.achievedTargets.retailerSale4kg)}</Text>
                                                            </View>
                                                        </View>
                                                    </View>

                                                    <Text style={styles.targetHeaderCellValue}>4Kg Retailer</Text>
                                                </View>
                                                {/*4 Kg Retailer Block Ends*/}

                                                {/*50 Cup Retailer Block Starts*/}
                                                <View style={styles.targetHeaderCell}>

                                                    <View style={styles.targetBody}>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={styles.target50CupValueBox}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome5 name={'hashtag'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.targets.retailers50Cup)}</Text>
                                                            </View>
                                                        </View>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={styles.target50CupValueBox}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome5 name={'hashtag'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.achievedTargets.retailers50Cup)}</Text>
                                                            </View>
                                                        </View>

                                                    </View>

                                                    <Text style={styles.targetHeaderCellValue}>50Cup Retailer</Text>
                                                </View>
                                                {/*50 Cup Retailer Block Ends*/}

                                            </View>
                                        </View>


                                        <View style={styles.totalTargetContainer}>
                                            <View style={styles.targetHeaderRow}>
                                                <View style={styles.targetHeaderCell}>

                                                    <View style={styles.targetBody}>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{backgroundColor:'#d2b29b'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome name={'rupee'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.targets.totalSales)}</Text>
                                                            </View>
                                                        </View>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{backgroundColor:'#d2b29b'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome name={'rupee'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.achievedTargets.totalSales)}</Text>
                                                            </View>
                                                        </View>
                                                    </View>

                                                    <Text style={styles.targetHeaderCellValue}>Total Sales</Text>
                                                </View>
                                                <View style={styles.targetHeaderCell}>

                                                    <View style={styles.targetBody}>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{backgroundColor:'#9dc6d8'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome5 name={'hashtag'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.targets.totalRetailers)}</Text>
                                                            </View>
                                                        </View>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{backgroundColor:'#9dc6d8'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <FontAwesome5 name={'hashtag'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.achievedTargets.totalRetailers)}</Text>
                                                            </View>
                                                        </View>
                                                    </View>

                                                    <Text style={styles.targetHeaderCellValue}>Total Retailers</Text>
                                                </View>
                                                <View style={styles.targetHeaderCell}>

                                                    <View style={styles.targetBody}>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{ backgroundColor:'#7dd0b6'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <MaterialCommunityIcons name={'weight-kilogram'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.targets.totalKilograms)}</Text>
                                                            </View>
                                                        </View>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{ backgroundColor:'#7dd0b6'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <MaterialCommunityIcons name={'weight-kilogram'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.achievedTargets.totalKilograms)}</Text>
                                                            </View>
                                                        </View>
                                                    </View>

                                                    <Text style={styles.targetHeaderCellValue}>Total Kilograms</Text>
                                                </View>
                                                <View style={styles.targetHeaderCell}>

                                                    <View style={styles.targetBody}>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{ backgroundColor:'#f69256'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <MaterialCommunityIcons name={'package-variant'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.targets.totalPacks)}</Text>
                                                            </View>
                                                        </View>
                                                        <View style={styles.targetAlignLeft}>
                                                            <View style={[styles.targetValueBox,{ backgroundColor:'#f69256'}]}>
                                                                <View style={styles.targetValueTypeIndicator}>
                                                                    <MaterialCommunityIcons name={'package-variant'} style={styles.targetValueTypeSymbol} />
                                                                </View>
                                                                <Text style={styles.targetValues}>{this.getValue(this.props.achievedTargets.totalPacks)}</Text>
                                                            </View>
                                                        </View>
                                                    </View>

                                                    <Text style={styles.targetHeaderCellValue}>Total Packs</Text>
                                                </View>
                                            </View>
                                            <View style={{flex: 1, flexDirection: 'row', alignItems: 'stretch', justifyContent: 'center'}}>
                                                <View style={{ flex: 0, margin: 5,backgroundColor:"#000",borderColor:"#5e7e47",borderRadius:5}}>
                                                    <Text style={{fontSize: 8,
                                                        textAlign:'center',
                                                        color:'#FDFBF8',
                                                        padding: 3,
                                                        fontWeight: 'bold'}}> # DSH : {this.props.achievedTargets.distinctHandlers}</Text>
                                                </View>
                                            </View>
                                        </View>
                                    </View>

                                </View>
                            ) : null}
                        </View>):null}
                    { this.props.isTargetsMissing && this.props.selectedHandlerId === this.props.handlerId? (
                        <View style={{flex: 0, justifyContent: 'flex-start', alignItems: 'stretch'}}>
                            <View style={[{flex: 0,alignItems: 'stretch',justifyContent: 'flex-start',margin: 10,marginTop: 0,borderWidth: 1,borderColor: '#CCC',backgroundColor: '#FDFBF8'}, {marginTop:10}]}>
                                <View style={{padding: 5, backgroundColor: '#FDFBF8'}}>
                                    <View style={{flex: 0, flexDirection: 'row', alignItems: 'stretch', justifyContent: 'center'}}>
                                        <Text> No Targets found </Text>
                                    </View>
                                </View>
                            </View>
                        </View>
                    ):null}

                </View>

            </View>
        );
    }
}

const styles = {
    targetContainer:{flex:0,alignItems:'stretch',justifyContent:'flex-start',backgroundColor:'#FDFBF8'},
    totalTargetContainer:{flex:0,alignItems:'stretch',justifyContent:'flex-start',backgroundColor:'#FDFBF8'},
    targetHeaderRow: {flex:0,flexDirection:'row',alignItems: 'flex-start',justifyContent:'flex-start'},
    targetHeaderCell:{flex: 1, borderWidth:2, borderColor: '#777',borderRadius: 5, margin:2},
    targetHeaderCellValue:{fontSize: 10, textAlign:'center', color:'#777', fontWeight: 'bold'},
    targetBody:{flex: 1, flexDirection: 'row', alignItems: 'stretch', justifyContent: 'center'},
    targetTileBody:{backgroundColor: '#000'},
    targetAlignLeft: {flex: 1, marginTop: 5, backgroundColor:'#FDFBF8'},
    targetValueBox:{flex:3, padding:2, margin:2},
    targetValues:{flex: 4, color: '#000', fontSize: 10, textAlign:'center', fontWeight: 'bold', marginTop:3, marginBottom:3},
    targetBoxLabel: {color: '#FDFBF8', fontSize: 10, textAlign:'center', fontWeight: 'bold'},
    targetValueTypeIndicator:{width:10,height:10,justifyContent: 'center',alignItems:'center'},
    targetValueTypeSymbol: {color: '#FDFBF8',textAlign:'center', fontSize:10, fontWeight:'bold',textShadowColor: 'rgba(0, 0, 0, 0.75)',
        textShadowOffset: {width: -1, height: 1},
        textShadowRadius: 10},
    targetTypeHeaderBtn:{flex: 2, padding: 5, marginLeft: 5, marginRight: 5},
    targetTypeHeaderBtnText:{flex: 3, color: '#000', textAlign: 'center', fontSize: 10},
    target50CupValueBox: {borderLeftWidth:1, borderRightWidth:1, borderTopWidth:2, borderColor:'#FDFBF8',
        backgroundColor:'#9dc6d8', paddingTop:17, paddingBottom:18},

};

const mapStateToProps = state => {
    return {
        targets: state.targetsReducer.targets,
        achievedTargets: state.targetsReducer.achievedTargets,
        selectedTargetType: state.targetsReducer.selectedTargetType,
        isTargetsMissing: state.targetsReducer.isTargetsMissing,
        selectedHandlerId: state.targetsReducer.selectedHandlerId
    }
};

const mapDispatchToProps = dispatch => {
    return {
        setTargets: (targets) => dispatch({type: "SET_TARGETS", payload: targets}),
        setAchievedTargets: (achievedTargets) => dispatch({type: "SET_ACHIEVED_TARGETS", payload: achievedTargets}),
        setSelectedTargetType: (selectedTargetType) => dispatch({type: "SET_SELECTED_TARGET_TYPE", payload: selectedTargetType}),
        fetchHandlerTargets: (type, targetType, handlerId, date, week, month, year) => dispatch(TargetsActions.getTargets(type, targetType, handlerId, date, week, month, year))
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(TargetsView);
