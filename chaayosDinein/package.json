{"name": "chaa<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"start": "node node_modules/react-native/local-cli/cli.js start", "test": "jest"}, "dependencies": {"@react-native-community/async-storage": "^1.5.0", "@react-native-community/netinfo": "^3.2.1", "axios": "^0.19.0", "fetch-intercept": "^2.3.1", "react": "16.8.3", "react-native": "0.59.9", "react-native-communications": "^2.2.1", "react-native-device-info": "^2.1.3", "react-native-gesture-handler": "^1.3.0", "react-native-splash-screen": "^3.2.0", "react-native-vector-icons": "^6.5.0", "react-navigation": "^3.11.0", "react-navigation-backhandler": "^1.3.2", "react-redux": "^7.1.0", "redux": "^4.0.1", "redux-logger": "^3.0.6", "redux-promise-middleware": "^6.1.1", "redux-thunk": "^2.3.0"}, "devDependencies": {"@babel/core": "7.4.5", "@babel/runtime": "7.4.5", "babel-jest": "24.8.0", "jest": "24.8.0", "metro-react-native-babel-preset": "0.54.1", "react-test-renderer": "16.8.3"}, "jest": {"preset": "react-native"}}