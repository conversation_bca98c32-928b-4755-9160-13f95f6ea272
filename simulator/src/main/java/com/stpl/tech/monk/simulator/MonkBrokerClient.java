package com.stpl.tech.monk.simulator;

import org.eclipse.paho.client.mqttv3.IMqttClient;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.MqttPersistenceException;
import org.eclipse.paho.client.mqttv3.MqttSecurityException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MonkBrokerClient {

	private static final Logger LOG = LoggerFactory.getLogger(MonkBrokerClient.class);
	private static final String TOPIC_COMMON = "/common/";
	private IMqttClient client;
	private String publisherId;

	public MonkBrokerClient(String publisherId) {
		super();
		try {
			this.publisherId = publisherId;
			this.client = new MqttClient(MQTTBrokerConfig.DEFAULT_CONFIG.getUrl(), publisherId);
		} catch (MqttException e) {
			LOG.error("Unable to create the client", e);
		}
	}

	public IMqttClient getClient() {
		return client;
	}

	public void setClient(IMqttClient publisher) {
		this.client = publisher;
	}

	public String getPublisherId() {
		return publisherId;
	}

	public void setPublisherId(String publisherId) {
		this.publisherId = publisherId;
	}

	public void connect() throws MqttSecurityException, MqttException {
		MqttConnectOptions options = new MqttConnectOptions();
		options.setAutomaticReconnect(true);
		options.setCleanSession(true);
		options.setConnectionTimeout(10);
		options.setKeepAliveInterval(8);
		options.setUserName(MQTTBrokerConfig.DEFAULT_CONFIG.getUserName());
		options.setPassword(MQTTBrokerConfig.DEFAULT_CONFIG.getPassword().toCharArray());
		options.setWill(TOPIC_COMMON, "LAST_WILL".getBytes(), 0, false);
		client.connect(options);
	}

	public void register() throws MqttPersistenceException, MqttException {
		MqttMessage message = new MqttMessage(String.format("/%s:%s", this.publisherId, "connected").getBytes());
		client.publish(TOPIC_COMMON, message);
		MqttMessage txmessage = new MqttMessage(String.format("/%s/%s", this.publisherId, "TX").getBytes());
		client.publish(TOPIC_COMMON, txmessage);
		MqttMessage rxmessage = new MqttMessage(String.format("/%s/%s", this.publisherId, "RX").getBytes());
		client.publish(TOPIC_COMMON, rxmessage);
	}

	public void publishTx(String payload, int count) throws MqttPersistenceException, MqttException {
		MqttMessage message = new MqttMessage(payload.getBytes());
		message.setQos(1);
		LOG.info("Sending Message to Monk : " + this.publisherId + " message: " + message);
		try {
			client.publish(String.format("/%s/%s/", this.publisherId, "TX"), message);
		} catch (Exception e) {
			LOG.info("Error Sending Message to Monk : " + this.publisherId + " message: " + message + " Retry Count :"
					+ count);
			count++;
			LOG.info("Error Sending Message to Monk : " + this.publisherId + " message: " + message + " Retry Count :"
					+ count);
			if (count < 5) {
				publishTx(payload, count);
			}
		}
	}

	public void subscribeRx() throws MqttSecurityException, MqttException {
		client.subscribe(String.format("/%s/%s/", this.publisherId, "RX"), 0);
	}

}
