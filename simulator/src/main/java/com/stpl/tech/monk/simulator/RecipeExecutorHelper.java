package com.stpl.tech.monk.simulator;

public class RecipeExecutorHelper {
	private static final String DENIED = "Denied";
	private static final String CONFIRMED = "Confirmed";
	private static final String CONST_STATUS = "/%s/S%s#%d#$E";
	private static final String CONST_COMMAND = "/%s/%s";

	public String ready(int count) {
		return String.format(CONST_STATUS, "500", "9,583,12,13,0,582,0", count);
	}
	
	public String status(String timeStamp, String status, int count) {
		return String.format(CONST_STATUS, timeStamp, status, count);
	}
	
	public String received(String timeStamp) {
		return String.format(CONST_COMMAND, timeStamp, "received");
	}

	public boolean isConfirmed(String string) {
		return CONFIRMED.equalsIgnoreCase(string);
	}
	
	public boolean isRecipe(String string) {
		return (string.startsWith("S") || string.startsWith("T")) && string.endsWith("$E");
	}

	public boolean isDenied(String string) {
		return DENIED.equalsIgnoreCase(string);
	}

	
	

}
