package com.stpl.tech.monk.simulator;

import java.sql.Timestamp;
import java.util.List;

import org.apache.commons.math3.util.Pair;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MonkClient implements Runnable, MqttCallback {
	private static final Logger LOG = LoggerFactory.getLogger(MonkClient.class);
	private static final long TIME_TO_SLEEP_FOR_READY = 5000;
	private int monkNumber;
	private String macId;
	private boolean stopReady = false;
	private boolean startRecipe = false;
	private boolean connected = false;
	Pair<String, String> currentRecipe = null;
	private RecipeExecutorHelper helper = new RecipeExecutorHelper();
	private MonkBrokerClient publisher = null;
	private int speed=1;
	private RecipeCache cache = new RecipeCache();
	
	public MonkClient(int monkNumber, int speed) {
		super();
		this.monkNumber = monkNumber;
		this.speed = speed;
		this.macId = String.format("CHAI_MONK%d", monkNumber);
	}

	public int getMonkNumber() {
		return monkNumber;
	}

	public void setMonkNumber(int monkNumber) {
		this.monkNumber = monkNumber;
	}

	public void run() {
		while (true) {
			if(publisher == null) {
				publisher = new MonkBrokerClient(macId);
			}
			while (!connected) {
				try {
					publisher.connect();
					publisher.register();
					publisher.subscribeRx();
					publisher.getClient().setCallback(this);
					connected = true;
				} catch (MqttException e) {
					LOG.error("Error while setting up the Monk Client: " + this.monkNumber, e);
				}
			}
			int count = 1;
			while (!stopReady) {
				try {
					LOG.info("Publishing ready message to broker: " + this.monkNumber + " count:" + count);
					if(!stopReady) {
						publisher.publishTx(helper.ready(count),0);
					}
				} catch (MqttException e) {
					LOG.error("Error while publishing ready message to broker: " + this.monkNumber, e);
				}
				count++;
				try {
					Thread.sleep(TIME_TO_SLEEP_FOR_READY);
				} catch (InterruptedException e) {
					LOG.error("Error while publishing ready message to broker: " + this.monkNumber, e);
				}
			}

			try {
				Thread.sleep(TIME_TO_SLEEP_FOR_READY);
			} catch (InterruptedException e) {
				LOG.error("Error while publishing ready message to broker: " + this.monkNumber, e);
			}
			
			while(startRecipe) {
				if(currentRecipe != null) {
					int counter = 1;
					List<Pair<Integer, String>> steps= cache.getRecipeSteps(currentRecipe.getValue());
					for(Pair<Integer, String> step : steps) {
						
						try {
							this.publisher.publishTx(helper.status(currentRecipe.getKey(), step.getValue(), counter),0);
						} catch (MqttException e) {
							LOG.error("Error while publishing recipe status message to broker: " + this.monkNumber
									+ " status: " + step.getValue(), e);

						}
						counter++;
						try {
							Thread.sleep(Long.valueOf((step.getKey()*speed)+""));
						} catch (NumberFormatException | InterruptedException e) {
							LOG.error("Error while sleeping after recipe status message publish to broker: " + this.monkNumber
									+ " status: " + step.getValue(), e);
						}
					}
					startRecipe = false;
					stopReady = false;
					currentRecipe = null;
				}
			}

		}
	}

	public void messageArrived(String topic, MqttMessage message) throws Exception {
		String time = new Timestamp(System.currentTimeMillis()).toString();
		String payload = new String(message.getPayload());
		LOG.info("\nMonk : " + this.macId + " - " + "Received a Message!" + "\n\tTime:    " + time + "\n\tTopic:   "
				+ topic + "\n\tMessage: " + payload + "\n\tQoS:     " + message.getQos()
				+ "\n");
		String msg = payload.substring(1);
		String[] splits = msg.split(":");
		if(splits.length == 2) {
			
			if(currentRecipe == null && helper.isRecipe(splits[1])) {
				if("F90000000000000000000000$E".equals(splits[0])) {
					this.startRecipe = false;
					this.stopReady = false;
					return ;
					
				}
				currentRecipe = new Pair<String, String>(splits[0], splits[1]);
				this.publisher.publishTx(helper.received(currentRecipe.getKey()),0);
				//this.publisher.publishTx(helper.received("timestamp"));
				
				this.startRecipe = true;
				this.stopReady = true;
				return;
			}
			if(helper.isConfirmed(splits[1])) {
				this.startRecipe = true;
				return;
			}
			if(helper.isDenied(splits[1])) {
				this.startRecipe = false;
				this.stopReady = false;
				return;
			}

		}
	}


	public void connectionLost(Throwable cause) {
		System.out.println("Connection to broker lost!" + cause.getMessage());
	}

	public void deliveryComplete(IMqttDeliveryToken token) {
	}
}