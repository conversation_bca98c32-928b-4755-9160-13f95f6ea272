package com.stpl.tech.monk.simulator.dispenser;

public class DispenserRecipeExecutorHelper {
	private static final String DENIED = "Denied";
	private static final String CONFIRMED = "Confirmed";
	private static final String CONST_STATUS_READY = "/%s/S%s#%d#$E";
	private static final String CONST_STATUS = "/%s/%s";
	private static final String PDENIED = "PDENIED";
	private static final String PCONFIRMED = "PCONFIRMED";
	private static final String CONST_COMMAND = "/%s/%s#%s$E";///1599035079032/received#0$E
	public static final String ARDUINO_HARD_RESET_COMMAND = "F100000000000000000000000$E";
	public static final String ARDUINO_RESET_RECEIPE_COMMAND = "F90000000000000000000000$E";
	public static final String CONFIRMED_COMMAND = "Confirmed";
	public static final String DENIED_COMMAND = "Denied";
	public static final String RESET_COMPLETED = "reset_completed";
	public static final String HARD_RESET = "hard_reset_done";

	public String ready(int count) {
		return String.format(CONST_STATUS_READY, "500", "9,583,12,13,0,582,0", count);
	}
	
	public String status(String timeStamp, String status, int count) {
		return String.format(CONST_STATUS, timeStamp, status, count);
	}
	
	public String received(String timeStamp) {
		return String.format(CONST_COMMAND, timeStamp, "received", 0);
	}

	public boolean isConfirmed(String string) {
		return CONFIRMED.equalsIgnoreCase(string);
	}
	
	public boolean isRecipe(String string) {
		return (string.startsWith("SI") && string.endsWith("$E"));
	}

	public boolean isDenied(String string) {
		return DENIED.equalsIgnoreCase(string);
	}

}
