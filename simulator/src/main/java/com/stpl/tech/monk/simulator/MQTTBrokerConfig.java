package com.stpl.tech.monk.simulator;

public class MQTTBrokerConfig {

	public static final MQTTBrokerConfig DEFAULT_CONFIG = new MQTTBrokerConfig("*************", "1883", "chaayosMqtt",
			"cha@2016");
	private String ipAddress;
	private String portNumber;
	private String userName;
	private String password;

	public MQTTBrokerConfig(String ipAddress, String portNumber, String userName, String password) {
		super();
		this.ipAddress = ipAddress;
		this.portNumber = portNumber;
		this.userName = userName;
		this.password = password;
	}

	public String getPortNumber() {
		return portNumber;
	}

	public void setPortNumber(String portNumber) {
		this.portNumber = portNumber;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	
	public String getUrl() {
		return String.format("tcp://%s:%s", ipAddress, portNumber);
	}

}
