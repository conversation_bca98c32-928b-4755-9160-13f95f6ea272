package com.stpl.tech.monk.simulator;

import org.apache.commons.math3.util.Pair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RecipeCache {

    public static final String DEFAULT_RECIPE_STRING_BOIL = "DEFAULT_RECIPE_STRING_BOIL";
    public static final String DEFAULT_RECIPE_STRING_STEEP = "DEFAULT_RECIPE_STRING_STEEP";
    public static final String DEFAULT_RECIPE_STRING_DISPENSER = "DEFAULT_RECIPE_STRING_DISPENSER";
    private static final Map<String, List<Pair<Integer, String>>> recipeMap = new HashMap<>();

    static {
        recipeMap.put(DEFAULT_RECIPE_STRING_BOIL, new ArrayList<>());
        List<Pair<Integer, String>> list = recipeMap.get(DEFAULT_RECIPE_STRING_BOIL);
        //list.add(new Pair<Integer, String>(4,"0,582,12,23,0,581,0"));
        list.add(new Pair<Integer, String>(2, "9,582,12,23,0,581,0"));
        list.add(new Pair<Integer, String>(1, "8,582,11,0,200,581,196"));
        list.add(new Pair<Integer, String>(6, "8,55,11,6,200,54,196"));
        list.add(new Pair<Integer, String>(5, "7,80,10,11,200,81,196"));
        list.add(new Pair<Integer, String>(7, "7,159,10,18,200,159,196"));
        list.add(new Pair<Integer, String>(6, "7,198,10,24,200,198,196"));
        list.add(new Pair<Integer, String>(6, "7,231,10,30,200,232,196"));
        list.add(new Pair<Integer, String>(2, "16,959,22,32,200,240,196"));
        list.add(new Pair<Integer, String>(6, "16,959,22,38,200,240,196"));
        list.add(new Pair<Integer, String>(6, "16,1023,22,44,200,-589,196"));
        list.add(new Pair<Integer, String>(6, "16,1023,22,49,200,-591,196"));
        list.add(new Pair<Integer, String>(6, "16,1018,22,54,200,-588,196"));
        list.add(new Pair<Integer, String>(7, "6,1023,25,62,221,265,209"));
        list.add(new Pair<Integer, String>(6, "6,1012,25,68,221,263,209"));
        list.add(new Pair<Integer, String>(5, "6,1023,25,73,221,264,209"));
        list.add(new Pair<Integer, String>(5, "6,935,25,78,221,264,209"));
        list.add(new Pair<Integer, String>(6, "6,944,25,84,221,261,209"));
        list.add(new Pair<Integer, String>(5, "6,944,25,89,221,260,209"));
        list.add(new Pair<Integer, String>(14, "5,907,37,103,221,258,209"));
        list.add(new Pair<Integer, String>(12, "4,907,12,114,221,253,209"));
        list.add(new Pair<Integer, String>(7, "4,1023,12,120,221,254,209"));
        list.add(new Pair<Integer, String>(9, "3,1023,13,128,221,250,209"));
        list.add(new Pair<Integer, String>(5, "3,1023,13,134,221,251,209"));
        list.add(new Pair<Integer, String>(12, "2,1014,15,145,221,247,209"));
        list.add(new Pair<Integer, String>(5, "1,156,13,151,221,245,209"));
        list.add(new Pair<Integer, String>(4, "0,0,3,154,221,583,209"));
        //list.add(new Pair<Integer, String>(10,"0,0,3,154,221,583,209"));
        list.add(new Pair<Integer, String>(10, "10,0,0,0,0,-2,0"));

        //list.add(new Pair<Integer, String>(10,"0,0,3,154,221,583,209"));


        recipeMap.put(DEFAULT_RECIPE_STRING_STEEP, new ArrayList<>());
        List<Pair<Integer, String>> list1 = recipeMap.get(DEFAULT_RECIPE_STRING_STEEP);
        list1.add(new Pair<Integer, String>(1, "9,563,19,43,0,562,0"));
        list1.add(new Pair<Integer, String>(7, "8,563,24,0,240,562,0"));
        list1.add(new Pair<Integer, String>(6, "8,92,24,7,240,91,0"));
        list1.add(new Pair<Integer, String>(2, "8,198,24,13,240,198,0"));
        list1.add(new Pair<Integer, String>(5, "6,208,15,15,240,208,0"));
        list1.add(new Pair<Integer, String>(6, "6,208,15,20,240,207,0"));
        list1.add(new Pair<Integer, String>(5, "6,208,15,25,240,205,0"));
        list1.add(new Pair<Integer, String>(5, "6,208,15,31,240,207,0"));
        list1.add(new Pair<Integer, String>(5, "6,208,15,36,240,208,0"));
        list1.add(new Pair<Integer, String>(6, "6,208,15,41,240,206,0"));
        list1.add(new Pair<Integer, String>(5, "6,208,15,46,240,204,0"));
        list1.add(new Pair<Integer, String>(5, "6,208,15,51,240,205,0"));
        list1.add(new Pair<Integer, String>(4, "6,208,15,57,240,202,0"));
        list1.add(new Pair<Integer, String>(5, "1,0,45,60,240,200,0"));
        list1.add(new Pair<Integer, String>(5, "1,0,45,66,240,199,0"));
        list1.add(new Pair<Integer, String>(6, "1,0,45,71,240,198,0"));
        list1.add(new Pair<Integer, String>(9, "1,0,45,76,240,197,0"));
        list1.add(new Pair<Integer, String>(5, "1,0,45,85,240,197,0"));
        list1.add(new Pair<Integer, String>(5, "1,0,45,91,240,195,0"));
        list1.add(new Pair<Integer, String>(10, "1,0,45,96,240,194,0"));
        list1.add(new Pair<Integer, String>(5, "1,0,45,105,240,193,0"));
        list1.add(new Pair<Integer, String>(5, "1,0,45,110,240,192,0"));
        list1.add(new Pair<Integer, String>(2, "1,0,45,116,240,191,0"));
        list1.add(new Pair<Integer, String>(2, "0,0,57,117,240,-572,0"));

        recipeMap.put(DEFAULT_RECIPE_STRING_DISPENSER, new ArrayList<>());
        List<Pair<Integer, String>> dispenserList = recipeMap.get(DEFAULT_RECIPE_STRING_DISPENSER);
//        dispenserList.add(new Pair<Integer, String>(9, "S9,563,19,43,0,562,0#1#$E"));
        dispenserList.add(new Pair<Integer, String>(7, "PAC010075A020050A030030A040030A050010A060040A070030A080015A090010I100000I110000I120000I130000I140000I150000I160000I170000I180000I190000I200000I210000#1780$E"));
        dispenserList.add(new Pair<Integer, String>(6, "PAC010075E020050A030030A040030C050010A060040P070030A080015I090010I100010I110010I120010I130040I140040I150030I160030I170050I180000I190000I200000I210000#1780$E"));
        dispenserList.add(new Pair<Integer, String>(5, "PAE010075P020050A030030A040030P050010A060040P070030A080015P090010P100010P110010I120010I130040I140040I150030I160030I170050I180000I190000I200000I210000#1780$E"));
        dispenserList.add(new Pair<Integer, String>(4, "PAE010075P020050A030030A040030P050010A060040P070030A080015P090010I100010I110010I120010I130040I140040I150030I160030I170050I180000I190000I200000I210000#1780$E"));
        dispenserList.add(new Pair<Integer, String>(2, "PAE010075P020050A030030A040030C050010A060040C070030A080015E090010E100010I110010I120010I130040I140040I150030I160030I170050I180000I190000I200000I210000#1780$E"));
        dispenserList.add(new Pair<Integer, String>(3, "PCC010075C020050C030030E040030E050010C060040C070030C080015E090010I100010I110010I120010I130040I140040I150030I160030I170050I180000I190000I200000I210000#1780$E"));
        dispenserList.add(new Pair<Integer, String>(1, "PCC010075C020050C030030C040030C050010C060040C070030C080015C090010I100000I110000I120000I130000I140000I150000I160000I170000I180000I190000I200000I210000#1780$E"));

        dispenserList.add(new Pair<Integer, String>(8, "S8,92,24,7,240,91,0#1#$E"));
        dispenserList.add(new Pair<Integer, String>(12, "S12,198,24,13,240,198,0#1#$E"));
        dispenserList.add(new Pair<Integer, String>(11, "S11,208,15,15,240,208,0#1#$E"));
        dispenserList.add(new Pair<Integer, String>(10, "S10,208,15,20,240,207,0#1#$E"));
    }

    public List<Pair<Integer, String>> getRecipeSteps(String recipeString) {
        if (!recipeMap.containsKey(recipeString)) {
            if (isBoil(recipeString)) {
                return recipeMap.get(DEFAULT_RECIPE_STRING_BOIL);
            } else {
                return recipeMap.get(DEFAULT_RECIPE_STRING_STEEP);
            }
        }
        return recipeMap.get(recipeString);
    }

    public List<Pair<Integer, String>> getDispenserRecipeSteps(String recipeString) {
        if (!recipeMap.containsKey(recipeString)) {
            return recipeMap.get(DEFAULT_RECIPE_STRING_DISPENSER);
        }
        return recipeMap.get(recipeString);
    }

    private boolean isBoil(String recipeString) {
        return recipeString.startsWith("S");
    }
}
