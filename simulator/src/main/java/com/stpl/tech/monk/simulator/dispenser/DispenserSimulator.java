package com.stpl.tech.monk.simulator.dispenser;

public class DispenserSimulator {

    public static void main(String[] args) {
        int speed = 0;
        if (args == null || args.length == 0) {
            speed = 500;
        }
        speed = Integer.valueOf(args[0]);
        DispenserClient client = new DispenserClient(speed);
        Thread th = new Thread(client);
        th.start();
    }
}
