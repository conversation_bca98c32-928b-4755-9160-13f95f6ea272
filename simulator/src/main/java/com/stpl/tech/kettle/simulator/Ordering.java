package com.stpl.tech.kettle.simulator;

import java.util.concurrent.TimeUnit;

import org.junit.Test;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.Select;

public class Ordering {
	@Test
	public void main() throws InterruptedException {

		System.setProperty("webdriver.chrome.driver", ".\\chromedriver.exe");
		WebDriver driver = new ChromeDriver();
		driver.get("http://localhost:9595/kettle-service/indexnew.html#/cover");
		driver.manage().window().maximize();
		driver.manage().timeouts().implicitlyWait(20, TimeUnit.SECONDS);
		// Thread.sleep(3000);
		driver.findElement(By.xpath("//*[@id=\"login-pills\"]/div/ul/li[1]/a"));
		// Thread.sleep(3000);
		driver.findElement(By.xpath("//input[@id='userId']")).sendKeys("120057");
		// Thread.sleep(3000);
		driver.findElement(By.xpath("//input[@id='password']")).sendKeys("321321");
		// Thread.sleep(3000);
		Select unit = new Select(driver.findElement(By.xpath("//select[@id='unit']")));
		// Thread.sleep(5000);
		unit.selectByValue("number:26005");
		// Thread.sleep(3000);
		driver.findElement(By.xpath("//button[@type='submit']")).click();
		for (int i = 0; i < 5; i++) {

			Thread.sleep(5000);
			// driver.findElement(By.xpath("//button[@data-ng-click='orderStart()']"));
			// Thread.sleep(3000);
			driver.findElement(By.xpath("//button[@data-ng-click='orderStart()']")).click();
			// Thread.sleep(3000);
			// driver.findElement(By.xpath("//span[text()='Sulemani Nimbu Chai']")).click();
			// Thread.sleep(3000);
			driver.findElement(By.xpath("//span[text()='Gur Wali Chai']")).click();

			driver.findElement(By.xpath("//span[text()='Desi Chai']")).click();
			Thread.sleep(3000);
			driver.findElement(By.xpath("//button[text()='Tulsi']")).click();
			driver.findElement(By.xpath("//button[text()='Adrak']")).click();
			driver.findElement(By.xpath("//button[@data-ng-click='submit()']")).click();

			driver.findElement(By.xpath("//a[text()='Make Payments']")).click();
			Thread.sleep(5000);
			driver.findElement(By.xpath("//input[@data-ng-change='addCustomerName(name)']")).sendKeys("test");
			// Thread.sleep(3000);
			driver.findElement(
					By.xpath("/html/body/div[3]/div/div/div[2]/div[2]/form/div[2]/div/div/div[2]/div/button[1]"))
					.click();
			Thread.sleep(3000);
			driver.findElement(By.xpath("/html/body/div[3]/div/div/div[3]/div/div[2]/button[2]")).click();
			Thread.sleep(7000);
		}

		for (int i = 0; i < 10; i++) {
			driver.findElement(By.xpath("//button[@data-ng-click='orderStart()']")).click();
			Thread.sleep(3000);
			// driver.findElement(By.xpath("//span[text()='Sulemani Nimbu Chai']")).click();
			// Thread.sleep(3000);
			// driver.findElement(By.xpath("//span[text()='Shahi Chai']")).click();

			driver.findElement(By.xpath("//span[text()='Desi Chai']")).click();
			Thread.sleep(3000);
			driver.findElement(By.xpath("//button[text()='Tulsi']")).click();
			driver.findElement(By.xpath("//button[text()='Adrak']")).click();
			driver.findElement(By.xpath("//button[@data-ng-click='submit()']")).click();
			Thread.sleep(3000);
			driver.findElement(
					By.xpath("/html/body/div/div[1]/div[2]/div[2]/div[2]/div/div/div/div[1]/div[3]/button[4]")).click();

			driver.findElement(By.xpath("//a[text()='Make Payments']")).click();
			Thread.sleep(5000);
			driver.findElement(By.xpath("//input[@data-ng-change='addCustomerName(name)']")).sendKeys("test2");
			Thread.sleep(3000);
			driver.findElement(
					By.xpath("/html/body/div[3]/div/div/div[2]/div[2]/form/div[2]/div/div/div[2]/div/button[1]"))
					.click();
			Thread.sleep(3000);
			driver.findElement(By.xpath("/html/body/div[3]/div/div/div[3]/div/div[2]/button[2]")).click();
			Thread.sleep(5000);

		}

		// driver.quit();
	}

	// TODO Auto-generated method stub

}
