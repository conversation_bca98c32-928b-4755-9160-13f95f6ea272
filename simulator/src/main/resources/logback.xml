<?xml version="1.0" encoding="UTF-8"?>

<!-- For assistance related to logback-translator or configuration  -->
<!-- files in general, please contact the logback user mailing list -->
<!-- at http://www.qos.ch/mailman/listinfo/logback-user             -->
<!--                                                                -->
<!-- For professional support please see                            -->
<!--    http://www.qos.ch/shop/products/professionalSupport         -->
<!--                                                                -->
<configuration>
<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
        <pattern>%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %-5p %10c: %m%n</pattern>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
        <level>INFO</level>
    </filter>
</appender>
<root level="INFO">
    <appender-ref ref="CONSOLE"/>
</root>
<logger name="org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver" level="OFF"/>
</configuration>