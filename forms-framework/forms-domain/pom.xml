<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>forms-framework</artifactId>
        <groupId>com.stpl.tech.forms</groupId>
        <version>6.2.41</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>forms-domain</artifactId>
    <packaging>jar</packaging>

    <name>forms-domain</name>
    <url>http://maven.apache.org</url>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.stpl.tech.kettle.master</groupId>
            <artifactId>master-domain</artifactId>
            <version>6.2.41</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.util</groupId>
            <artifactId>utility-service</artifactId>
            <version>6.2.41</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.hibernate.javax.persistence</groupId>-->
        <!--            <artifactId>hibernate-jpa-2.1-api</artifactId>-->
        <!--            <version>1.0.0.Final</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.fasterxml.jackson.core</groupId>-->
        <!--            <artifactId>jackson-annotations</artifactId>-->
        <!--            <version>2.5.0</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.data</groupId>-->
        <!--            <artifactId>spring-data-mongodb</artifactId>-->
        <!--            <version>1.9.6.RELEASE</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.0.13.Final</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>javax.el</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <version>2.7.1</version>
        </dependency>
    </dependencies>
</project>
