<?xml version="1.0"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
           elementFormDefault="qualified">
    <xs:complexType name="IdCodeName">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="code" type="xs:string" nillable="true"/>
            <xs:element name="name" type="xs:string" nillable="true"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AuditForm">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="name" type="xs:string" nillable="false"/>
            <xs:element name="description" type="xs:string" nillable="true"/>
            <xs:element name="createdBy" type="IdCodeName" nillable="true"/>
            <xs:element name="lastUpdatedBy" type="IdCodeName" nillable="true"/>
            <xs:element name="creationTime" type="xs:date" nillable="true"/>
            <xs:element name="lastUpdateTime" type="xs:date" nillable="true"/>
            <xs:element name="notificationEmail" type="xs:string" nillable="true"/>
            <xs:element name="notificationSlackChannels" type="xs:string" nillable="true"/>
            <xs:element name="status" type="xs:string" nillable="true"/>
            <xs:element name="values" type="AuditFormValues" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AuditFormValues">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="auditForm" type="AuditForm" nillable="true"/>
            <xs:element name="entityLabel" type="xs:string" nillable="true"/>
            <xs:element name="entityDescription" type="xs:string" nillable="true"/>
            <xs:element name="entityType" type="EntityType" nillable="true"/>
            <xs:element name="entityValues" type="xs:string" nillable="true"/>
            <xs:element name="entityMetadata" type="xs:string" nillable="true"/>
            <xs:element name="linkedDataType" type="xs:string" nillable="true"/>
            <xs:element name="linkedEntity" type="AuditFormValues" nillable="true"/>
            <xs:element name="answeredBy" type="xs:string" nillable="true"/>
            <xs:element name="appearanceOrder" type="xs:int" nillable="false"/>
            <xs:element name="linkedApi" type="xs:string" nillable="true"/>
            <xs:element name="scoreCounted" type="xs:boolean" nillable="false"/>
            <xs:element name="status" type="xs:string" nillable="false"/>
            <xs:element name="maxScore" type="xs:decimal" nillable="true"/>
            <xs:element name="attachDoc" type="xs:boolean" nillable="false"/>
            <xs:element name="additionalComment" type="xs:boolean" nillable="false"/>
            <xs:element name="isMandatory" type="xs:boolean" nillable="false" default="false" />
            <xs:element name="questionOptional" type="xs:boolean" nillable="false" default="false" />
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Audit">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="auditForm" type="IdCodeName" nillable="false"/>
            <xs:element name="auditor" type="IdCodeName" nillable="false"/>
            <xs:element name="auditUnit" type="IdCodeName" nillable="false"/>
            <xs:element name="auditDate" type="xs:date" nillable="false"/>
            <xs:element name="auditTime" type="xs:date" nillable="false"/>
            <xs:element name="auditType" type="AuditType" nillable="false"/>
            <xs:element name="auditSubmissionDate" type="xs:date" nillable="true"/>
            <xs:element name="cafeManager" type="IdCodeName" nillable="true"/>
            <xs:element name="managerOnDuty" type="IdCodeName" nillable="true"/>
            <xs:element name="areaManager" type="IdCodeName" nillable="true"/>
            <xs:element name="totalScore" type="xs:decimal" nillable="true"/>
            <xs:element name="acquiredScore" type="xs:decimal" nillable="true"/>
            <xs:element name="projectedTotalScore" type="xs:decimal" nillable="true"/>
            <xs:element name="projectedAcquiredScore" type="xs:decimal" nillable="true"/>
            <xs:element name="percentage" type="xs:decimal" nillable="true"/>
            <xs:element name="values" type="AuditValues" minOccurs="1" maxOccurs="unbounded"/>
            <xs:element name="cancel" type="xs:boolean" nillable="true"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AuditValues">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="auditFormValue" type="AuditFormValues" nillable="false"/>
            <xs:element name="employeeId" type="xs:int" nillable="true"/>
            <xs:element name="employeeName" type="xs:string" nillable="true"/>
            <xs:element name="employeeDesignation" type="xs:string" nillable="true"/>
            <xs:element name="unitId" type="xs:int" nillable="true"/>
            <xs:element name="unitName" type="xs:string" nillable="true"/>
            <xs:element name="productId" type="xs:int" nillable="true"/>
            <xs:element name="productName" type="xs:string" nillable="true"/>
            <xs:element name="option1" type="xs:string" nillable="true"/>
            <xs:element name="option1Marks" type="xs:decimal" nillable="true"/>
            <xs:element name="option2" type="xs:string" nillable="true"/>
            <xs:element name="option2Marks" type="xs:decimal" nillable="true"/>
            <xs:element name="option3" type="xs:string" nillable="true"/>
            <xs:element name="option3Marks" type="xs:decimal" nillable="true"/>
            <xs:element name="option4" type="xs:string" nillable="true"/>
            <xs:element name="option4Marks" type="xs:decimal" nillable="true"/>
            <xs:element name="option5" type="xs:string" nillable="true"/>
            <xs:element name="option5Marks" type="xs:decimal" nillable="true"/>
            <xs:element name="option6" type="xs:string" nillable="true"/>
            <xs:element name="option6Marks" type="xs:decimal" nillable="true"/>
            <xs:element name="option7" type="xs:string" nillable="true"/>
            <xs:element name="option7Marks" type="xs:decimal" nillable="true"/>
            <xs:element name="textArea" type="xs:string" nillable="true"/>
            <xs:element name="yesNo" type="xs:string" nillable="true"/>
            <xs:element name="dateValue" type="xs:date" nillable="true"/>
            <xs:element name="timeValue" type="xs:time" nillable="true"/>
            <xs:element name="numberValue" type="xs:int" nillable="true"/>
            <xs:element name="textValue" type="xs:string" nillable="true"/>
            <xs:element name="answerComment" type="xs:string" nillable="true"/>
            <xs:element name="attachedDoc" type="DocumentDetail" nillable="true"/>
            <xs:element name="maxScore" type="xs:decimal" nillable="true"/>
            <xs:element name="acquiredScore" type="xs:decimal" nillable="true"/>
            <xs:element name="questionOpted" type="xs:boolean" nillable="false" default="true" />
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentDetail">
        <xs:sequence>
            <xs:element name="documentId" type="xs:int" nillable="true"/>
            <xs:element name="documentLink" type="xs:string" nillable="false"/>
            <xs:element name="fileType" type="xs:string" nillable="true"/>
            <xs:element name="mimeType" type="MimeType" nillable="true"/>
            <xs:element name="uploadType" type="DocUploadType" nillable="false"/>
            <xs:element name="uploadTypeId" type="xs:int" nillable="false"/>
            <xs:element name="updateTime" type="xs:date" nillable="false"/>
            <xs:element name="updatedBy" type="IdCodeName" nillable="false"/>
        </xs:sequence>
    </xs:complexType>
	<xs:complexType name="EmployeeWarning">
		<xs:sequence>
			<xs:element name="id" type="xs:int" nillable="true" />
			<xs:element name="audit" type="IdCodeName" nillable="true" />
			<xs:element name="unit" type="IdCodeName" nillable="true" />
			<xs:element name="areaManager" type="IdCodeName" nillable="true" />
			<xs:element name="managerOnDuty" type="IdCodeName"
				nillable="true" />
			<xs:element name="guiltyPerson" type="IdCodeName"
				nillable="true" />
			<xs:element name="warningStage" type="xs:string" nillable="true" />
			<xs:element name="warningStatus" type="WarningStatus"
				nillable="true" />
			<xs:element name="impactType" type="WarningImpactType" nillable="true" />
			<xs:element name="updateTime" type="xs:date" nillable="false" />
			<xs:element name="lastStatusId" type="xs:int" nillable="true" />
			<xs:element name="initiator" type="EmployeeWarningStatus"
				nillable="true" />
			<xs:element name="amResponse" type="EmployeeWarningStatus"
				nillable="true" />
			<xs:element name="dgmResponse" type="EmployeeWarningStatus"
				nillable="true" />
			<xs:element name="hrResponse" type="EmployeeWarningStatus"
				nillable="true" />
			<xs:element name="cancelResponse" type="EmployeeWarningStatus"
				nillable="true" />
			<xs:element name="images" type="WarningImageDetail" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EmployeeWarningStatus">
		<xs:sequence>
			<xs:element name="id" type="xs:int" nillable="true" />
			<xs:element name="warningId" type="xs:int" nillable="true" />
			<xs:element name="fromStatus" type="xs:string" nillable="true" />
			<xs:element name="toStatus" type="xs:string" nillable="true" />
			<xs:element name="authorisedPerson" type="IdCodeName"
				nillable="true" />
			<xs:element name="comment" type="xs:string" nillable="true" />
			<xs:element name="actionTakenBy" type="xs:string"
				nillable="true" />
			<xs:element name="actionType" type="WarningActionType"
				nillable="true" />
			<xs:element name="actionTakenOn" type="xs:date" nillable="false" />
			<xs:element name="reasons" type="EmployeeWarningReason" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EmployeeWarningReason">
		<xs:sequence>
			<xs:element name="id" type="xs:int" nillable="true" />
			<xs:element name="warningId" type="xs:int" nillable="true" />
			<xs:element name="warningStatusId" type="xs:int" nillable="true" />
			<xs:element name="reasonId" type="xs:int" nillable="true" />
			<xs:element name="reasonName" type="xs:string" nillable="true" />
			<xs:element name="category" type="xs:string" nillable="true" />
			<xs:element name="reasonAddedBy" type="ReasonOwner" nillable="true" />
			<xs:element name="status" type="xs:string" nillable="true" />
			<xs:element name="updateRequired" type="xs:boolean" nillable="true" />
			<xs:element name="updatedOn" type="xs:date" nillable="false" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="WarningImageDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" nillable="true" />
			<xs:element name="warningId" type="xs:int" nillable="true" />
			<xs:element name="imageUrl" type="xs:string" nillable="true" />
			<xs:element name="imageName" type="xs:string" nillable="true" />
			<xs:element name="status" type="xs:string" nillable="true" />
			<xs:element name="createdOn" type="xs:date" nillable="false" />
		</xs:sequence>
	</xs:complexType>

    <xs:simpleType name="EntityType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="QUESTION_GROUP"/>
            <xs:enumeration value="TEXT"/>
            <xs:enumeration value="HEADING"/>
            <xs:enumeration value="SUBHEADING"/>
            <xs:enumeration value="TEXTAREA"/>
            <xs:enumeration value="BOOLEAN"/>
            <xs:enumeration value="PRODUCT"/>
            <xs:enumeration value="SELECT_BOX"/>
            <xs:enumeration value="SNIPPET"/>
            <xs:enumeration value="DATE"/>
            <xs:enumeration value="TIME"/>
            <xs:enumeration value="UNIT_EMPLOYEE"/>
            <xs:enumeration value="UNIT"/>
            <xs:enumeration value="NUMBER"/>
            <xs:enumeration value="RADIO"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AuditType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="TRIAL"/>
            <xs:enumeration value="REGISTERED"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="MimeType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PDF"/>
            <xs:enumeration value="XLS/XLSX"/>
            <xs:enumeration value="IMG"/>
            <xs:enumeration value="CSV"/>
            <xs:enumeration value="TEXT"/>
        </xs:restriction>
    </xs:simpleType>
	<xs:simpleType name="DocUploadType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AUDIT" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="WarningStatus" final="restriction">
	<xs:restriction base="xs:string">
		<xs:enumeration value="PENDING" />
		<xs:enumeration value="APPROVED" />
		<xs:enumeration value="REJECTED" />
		<xs:enumeration value="CANCELLED" />
	</xs:restriction>
</xs:simpleType>
	<xs:simpleType name="ReasonOwner" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AM" />
			<xs:enumeration value="DGM" />
			<xs:enumeration value="AUDITOR" />
			<xs:enumeration value="HR" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ReasonStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="IN_ACTIVE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="WarningImpactType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ZTZ" />
			<xs:enumeration value="CRITICAL" />
			<xs:enumeration value="BOTH" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ActionTakenBy" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SELF" />
			<xs:enumeration value="SYSTEM" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="WarningActionType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACCEPT" />
			<xs:enumeration value="REJECT" />
			<xs:enumeration value="REOPEN" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
