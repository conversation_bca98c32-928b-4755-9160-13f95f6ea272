package com.stpl.tech.expense.domain.model;

import com.stpl.tech.forms.domain.model.IdCodeName;
import com.stpl.tech.forms.domain.model.Voucher;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderRefundClaimVo {
    private IdCodeName requestedBy;
    private List<OrderRefundVoucher> orderRefundVouchers;
}
