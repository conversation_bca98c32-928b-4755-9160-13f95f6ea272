package com.stpl.tech.forms.domain.model;

import java.math.BigDecimal;
import java.util.Objects;

public class ScoreVO {

    private BigDecimal acquiredScore;

    private BigDecimal maxScore;

    private BigDecimal percentage;

    private String auditDate;

    public BigDecimal getAcquiredScore() {
        return acquiredScore;
    }

    public void setAcquiredScore(BigDecimal acquiredScore) {
        this.acquiredScore = acquiredScore;
    }

    public BigDecimal getMaxScore() {
        return maxScore;
    }

    public void setMaxScore(BigDecimal maxScore) {
        this.maxScore = maxScore;
    }

    public BigDecimal getPercentage(){
        return this.percentage;
    }

    public void setPercentage(BigDecimal percentage){
        this.percentage = percentage;
    }

    public String getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(String auditDate) {
        this.auditDate = auditDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ScoreVO scoreVO = (ScoreVO) o;
        return Objects.equals(acquiredScore, scoreVO.acquiredScore) &&
                Objects.equals(maxScore, scoreVO.maxScore);
    }

    @Override
    public int hashCode() {

        return Objects.hash(acquiredScore, maxScore);
    }
}
