package com.stpl.tech.forms.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VoucherCostCenterAllocation {
    private Integer voucherAllocationId;
    private Integer voucherId;
    @NotNull(message = "Business Cost Center Id Cannot Be Empty")
    private Integer businessCostCenterId;
    @NotNull(message = "Business Cost Center Name Cannot Be Empty")
    private String businessCostCenter;
    @NotNull(message = "Allocated Issue Amount Cannot Be Empty")
    private BigDecimal allocatedIssuedAmount;
    private String forceAllocation;
    private Date issuedTime;
}
