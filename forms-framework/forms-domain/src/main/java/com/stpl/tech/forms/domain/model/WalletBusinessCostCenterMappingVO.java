package com.stpl.tech.forms.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class WalletBusinessCostCenterMappingVO {

    private Integer walletBccMappingId;
    private Integer walletId;
    private Integer bccId;
    private String bccName;
    private String bccType;
    private String bccCode;
    private String mappingStatus;
    private Date addTime;
    private Date lastUpdateTime;
    private String lastUpdatedBy;
}
