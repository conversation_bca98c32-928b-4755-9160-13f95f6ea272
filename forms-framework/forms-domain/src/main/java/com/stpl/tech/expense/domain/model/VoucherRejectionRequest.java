package com.stpl.tech.expense.domain.model;

import com.stpl.tech.forms.domain.model.IdCodeName;
import com.stpl.tech.master.budget.metadata.model.ExpenseValidation;

import java.util.List;

public class VoucherRejectionRequest {

    private Integer voucherId;
    private String comment;
    private IdCodeName actionBy;
    private List<ExpenseValidation> rejections;

    public Integer getVoucherId() {
        return voucherId;
    }

    public void setVoucherId(Integer voucherId) {
        this.voucherId = voucherId;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public IdCodeName getActionBy() {
        return actionBy;
    }

    public void setActionBy(IdCodeName actionBy) {
        this.actionBy = actionBy;
    }

    public List<ExpenseValidation> getRejections() {
        return rejections;
    }

    public void setRejections(List<ExpenseValidation> rejections) {
        this.rejections = rejections;
    }
}
