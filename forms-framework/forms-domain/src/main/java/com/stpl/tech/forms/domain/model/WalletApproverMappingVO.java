package com.stpl.tech.forms.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WalletApproverMappingVO {
    private Integer walletApproverMappingId;

    private Integer walletId;

    private Integer approverId;

    private String approverName;

    private String mappingStatus;

    private Date addTime;

    private Date updatedTime;

    private String updatedBy;


}
