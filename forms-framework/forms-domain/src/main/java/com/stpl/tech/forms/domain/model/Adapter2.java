//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.17 at 05:17:52 PM IST 
//


package com.stpl.tech.forms.domain.model;

import javax.xml.bind.annotation.adapters.XmlAdapter;
import java.sql.Time;
import java.text.ParseException;

public class Adapter2
    extends XmlAdapter<String, Time>
{


    public Time unmarshal(String value) throws ParseException {
        return (com.stpl.tech.forms.domain.model.DateAdapter.parseTime(value));
    }

    public String marshal(Time value) {
        return (com.stpl.tech.forms.domain.model.DateAdapter.printTime(value));
    }

}
