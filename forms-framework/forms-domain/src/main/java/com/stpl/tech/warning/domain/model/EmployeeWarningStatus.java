//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.03.30 at 02:07:46 PM IST
// Generated on: 2018.04.14 at 03:08:16 PM IST
//


package com.stpl.tech.warning.domain.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.forms.domain.model.Adapter3;
import com.stpl.tech.forms.domain.model.IdCodeName;


/**
 * <p>Java class for EmployeeWarningStatus complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="EmployeeWarningStatus"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="warningId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="fromStatus" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="toStatus" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="authorisedPerson" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="actionTakenBy" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="actionType" type="{http://www.w3schools.com}WarningActionType"/&gt;
 *         &lt;element name="actionTakenOn" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="reasons" type="{http://www.w3schools.com}EmployeeWarningReason" maxOccurs="unbounded"/&gt;
 *         &lt;element name="systemResponse" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EmployeeWarningStatus", propOrder = {
    "id",
    "warningId",
    "fromStatus",
    "toStatus",
    "authorisedPerson",
    "comment",
    "actionTakenBy",
    "actionType",
    "actionTakenOn",
    "reasons",
    "systemResponse"
})
public class EmployeeWarningStatus {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer warningId;
    @XmlElement(required = true, nillable = true)
    protected String fromStatus;
    @XmlElement(required = true, nillable = true)
    protected String toStatus;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName authorisedPerson;
    @XmlElement(required = true, nillable = true)
    protected String comment;
    @XmlElement(required = true, nillable = true)
    protected String actionTakenBy;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected WarningActionType actionType;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date actionTakenOn;
    @XmlElement(required = true)
    protected List<EmployeeWarningReason> reasons;
    @XmlElement(defaultValue = "false")
    protected boolean systemResponse;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the warningId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getWarningId() {
        return warningId;
    }

    /**
     * Sets the value of the warningId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setWarningId(Integer value) {
        this.warningId = value;
    }

    /**
     * Gets the value of the fromStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFromStatus() {
        return fromStatus;
    }

    /**
     * Sets the value of the fromStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFromStatus(String value) {
        this.fromStatus = value;
    }

    /**
     * Gets the value of the toStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getToStatus() {
        return toStatus;
    }

    /**
     * Sets the value of the toStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setToStatus(String value) {
        this.toStatus = value;
    }

    /**
     * Gets the value of the authorisedPerson property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getAuthorisedPerson() {
        return authorisedPerson;
    }

    /**
     * Sets the value of the authorisedPerson property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setAuthorisedPerson(IdCodeName value) {
        this.authorisedPerson = value;
    }

    /**
     * Gets the value of the comment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the actionTakenBy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getActionTakenBy() {
        return actionTakenBy;
    }

    /**
     * Sets the value of the actionTakenBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setActionTakenBy(String value) {
        this.actionTakenBy = value;
    }

    /**
     * Gets the value of the actionType property.
     * 
     * @return
     *     possible object is
     *     {@link WarningActionType }
     *     
     */
    public WarningActionType getActionType() {
        return actionType;
    }

    /**
     * Sets the value of the actionType property.
     * 
     * @param value
     *     allowed object is
     *     {@link WarningActionType }
     *     
     */
    public void setActionType(WarningActionType value) {
        this.actionType = value;
    }

    /**
     * Gets the value of the actionTakenOn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getActionTakenOn() {
        return actionTakenOn;
    }

    /**
     * Sets the value of the actionTakenOn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setActionTakenOn(Date value) {
        this.actionTakenOn = value;
    }

    /**
     * Gets the value of the reasons property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the reasons property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getReasons().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link EmployeeWarningReason }
     * 
     * 
     */
    public List<EmployeeWarningReason> getReasons() {
        if (reasons == null) {
            reasons = new ArrayList<EmployeeWarningReason>();
        }
        return this.reasons;
    }

    /**
     * Gets the value of the systemResponse property.
     *
     */
    public boolean isSystemResponse() {
        return systemResponse;
    }

    /**
     * Sets the value of the systemResponse property.
     *
     */
    public void setSystemResponse(boolean value) {
        this.systemResponse = value;
    }

}
