package com.stpl.tech.expense.domain.model;

import com.stpl.tech.forms.domain.model.IdCodeName;
import com.stpl.tech.forms.domain.model.Voucher;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Claim {
    private Integer claimId;
    private ClaimType claimType;
    private Date creationTime;
    private Date lastUpdateTime;
    private IdCodeName createdBy;
    private IdCodeName lastUpdatedBy;
    private ClaimStatus claimCurrentStatus;
    private IdCodeName unit;
    private IdCodeName employee;
    private BigDecimal claimRequestedAmount;
    private BigDecimal claimApprovedAmount;
    private String happayId;
    private Integer walletId;
    private List<ClaimLog> claimLogs = new ArrayList<>();
    private List<Voucher> vouchers = new ArrayList<>();

    public Integer getClaimId() {
        return claimId;
    }

    public void setClaimId(Integer claimId) {
        this.claimId = claimId;
    }

    public ClaimType getClaimType() {
        return claimType;
    }

    public void setClaimType(ClaimType claimType) {
        this.claimType = claimType;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(IdCodeName createdBy) {
        this.createdBy = createdBy;
    }

    public IdCodeName getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(IdCodeName lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public ClaimStatus getClaimCurrentStatus() {
        return claimCurrentStatus;
    }

    public void setClaimCurrentStatus(ClaimStatus claimCurrentStatus) {
        this.claimCurrentStatus = claimCurrentStatus;
    }

    public IdCodeName getUnit() {
        return unit;
    }

    public void setUnit(IdCodeName unit) {
        this.unit = unit;
    }

    public IdCodeName getEmployee() {
        return employee;
    }

    public void setEmployee(IdCodeName employee) {
        this.employee = employee;
    }

    public BigDecimal getClaimRequestedAmount() {
        return claimRequestedAmount;
    }

    public void setClaimRequestedAmount(BigDecimal claimRequestedAmount) {
        this.claimRequestedAmount = claimRequestedAmount;
    }

    public BigDecimal getClaimApprovedAmount() {
        return claimApprovedAmount;
    }

    public void setClaimApprovedAmount(BigDecimal claimApprovedAmount) {
        this.claimApprovedAmount = claimApprovedAmount;
    }

    public String getHappayId() {
        return happayId;
    }

    public void setHappayId(String happayId) {
        this.happayId = happayId;
    }

    public List<ClaimLog> getClaimLogs() {
        return claimLogs;
    }

    public void setClaimLogs(List<ClaimLog> claimLogs) {
        this.claimLogs = claimLogs;
    }

    public List<Voucher> getVouchers() {
        return vouchers;
    }

    public void setVouchers(List<Voucher> vouchers) {
        this.vouchers = vouchers;
    }

    public Integer getWalletId() {
        return walletId;
    }

    public void setWalletId(Integer walletId) {
        this.walletId = walletId;
    }
}
