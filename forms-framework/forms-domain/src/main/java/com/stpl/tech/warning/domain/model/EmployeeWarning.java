//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.04.04 at 10:36:45 PM IST 
//


package com.stpl.tech.warning.domain.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.forms.domain.model.Adapter3;
import com.stpl.tech.forms.domain.model.IdCodeName;


/**
 * <p>Java class for EmployeeWarning complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="EmployeeWarning"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="audit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="unit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="areaManager" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="managerOnDuty" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="guiltyPerson" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="warningStage" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="warningStatus" type="{http://www.w3schools.com}WarningStatus"/&gt;
 *         &lt;element name="impactType" type="{http://www.w3schools.com}WarningImpactType"/&gt;
 *         &lt;element name="updateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastStatusId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="initiator" type="{http://www.w3schools.com}EmployeeWarningStatus"/&gt;
 *         &lt;element name="amResponse" type="{http://www.w3schools.com}EmployeeWarningStatus"/&gt;
 *         &lt;element name="dgmResponse" type="{http://www.w3schools.com}EmployeeWarningStatus"/&gt;
 *         &lt;element name="hrResponse" type="{http://www.w3schools.com}EmployeeWarningStatus"/&gt;
 *         &lt;element name="cancelResponse" type="{http://www.w3schools.com}EmployeeWarningStatus"/&gt;
 *         &lt;element name="images" type="{http://www.w3schools.com}WarningImageDetail" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EmployeeWarning", propOrder = {
    "id",
    "audit",
    "unit",
    "areaManager",
    "managerOnDuty",
    "guiltyPerson",
    "warningStage",
    "warningStatus",
    "impactType",
    "updateTime",
    "lastStatusId",
    "initiator",
    "amResponse",
    "dgmResponse",
    "hrResponse",
    "cancelResponse",
    "images",
    "doi"
})
public class EmployeeWarning {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName audit;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName unit;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName areaManager;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName managerOnDuty;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName guiltyPerson;
    @XmlElement(required = true, nillable = true)
    protected String warningStage;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected WarningStatus warningStatus;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected WarningImpactType impactType;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date updateTime;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer lastStatusId;
    @XmlElement(required = true, nillable = true)
    protected EmployeeWarningStatus initiator;
    @XmlElement(required = true, nillable = true)
    protected EmployeeWarningStatus amResponse;
    @XmlElement(required = true, nillable = true)
    protected EmployeeWarningStatus dgmResponse;
    @XmlElement(required = true, nillable = true)
    protected EmployeeWarningStatus hrResponse;
    @XmlElement(required = true, nillable = true)
    protected EmployeeWarningStatus cancelResponse;
    @XmlElement(required = true)
    protected List<WarningImageDetail> images;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date doi;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the audit property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getAudit() {
        return audit;
    }

    /**
     * Sets the value of the audit property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setAudit(IdCodeName value) {
        this.audit = value;
    }

    /**
     * Gets the value of the unit property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getUnit() {
        return unit;
    }

    /**
     * Sets the value of the unit property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setUnit(IdCodeName value) {
        this.unit = value;
    }

    /**
     * Gets the value of the areaManager property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getAreaManager() {
        return areaManager;
    }

    /**
     * Sets the value of the areaManager property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setAreaManager(IdCodeName value) {
        this.areaManager = value;
    }

    /**
     * Gets the value of the managerOnDuty property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getManagerOnDuty() {
        return managerOnDuty;
    }

    /**
     * Sets the value of the managerOnDuty property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setManagerOnDuty(IdCodeName value) {
        this.managerOnDuty = value;
    }

    /**
     * Gets the value of the guiltyPerson property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getGuiltyPerson() {
        return guiltyPerson;
    }

    /**
     * Sets the value of the guiltyPerson property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setGuiltyPerson(IdCodeName value) {
        this.guiltyPerson = value;
    }

    /**
     * Gets the value of the warningStage property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWarningStage() {
        return warningStage;
    }

    /**
     * Sets the value of the warningStage property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWarningStage(String value) {
        this.warningStage = value;
    }

    /**
     * Gets the value of the warningStatus property.
     * 
     * @return
     *     possible object is
     *     {@link WarningStatus }
     *     
     */
    public WarningStatus getWarningStatus() {
        return warningStatus;
    }

    /**
     * Sets the value of the warningStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link WarningStatus }
     *     
     */
    public void setWarningStatus(WarningStatus value) {
        this.warningStatus = value;
    }

    /**
     * Gets the value of the impactType property.
     * 
     * @return
     *     possible object is
     *     {@link WarningImpactType }
     *     
     */
    public WarningImpactType getImpactType() {
        return impactType;
    }

    /**
     * Sets the value of the impactType property.
     * 
     * @param value
     *     allowed object is
     *     {@link WarningImpactType }
     *     
     */
    public void setImpactType(WarningImpactType value) {
        this.impactType = value;
    }

    /**
     * Gets the value of the updateTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * Sets the value of the updateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdateTime(Date value) {
        this.updateTime = value;
    }

    /**
     * Gets the value of the lastStatusId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getLastStatusId() {
        return lastStatusId;
    }

    /**
     * Sets the value of the lastStatusId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setLastStatusId(Integer value) {
        this.lastStatusId = value;
    }

    /**
     * Gets the value of the initiator property.
     * 
     * @return
     *     possible object is
     *     {@link EmployeeWarningStatus }
     *     
     */
    public EmployeeWarningStatus getInitiator() {
        return initiator;
    }

    /**
     * Sets the value of the initiator property.
     * 
     * @param value
     *     allowed object is
     *     {@link EmployeeWarningStatus }
     *     
     */
    public void setInitiator(EmployeeWarningStatus value) {
        this.initiator = value;
    }

    /**
     * Gets the value of the amResponse property.
     * 
     * @return
     *     possible object is
     *     {@link EmployeeWarningStatus }
     *     
     */
    public EmployeeWarningStatus getAmResponse() {
        return amResponse;
    }

    /**
     * Sets the value of the amResponse property.
     * 
     * @param value
     *     allowed object is
     *     {@link EmployeeWarningStatus }
     *     
     */
    public void setAmResponse(EmployeeWarningStatus value) {
        this.amResponse = value;
    }

    /**
     * Gets the value of the dgmResponse property.
     * 
     * @return
     *     possible object is
     *     {@link EmployeeWarningStatus }
     *     
     */
    public EmployeeWarningStatus getDgmResponse() {
        return dgmResponse;
    }

    /**
     * Sets the value of the dgmResponse property.
     * 
     * @param value
     *     allowed object is
     *     {@link EmployeeWarningStatus }
     *     
     */
    public void setDgmResponse(EmployeeWarningStatus value) {
        this.dgmResponse = value;
    }

    /**
     * Gets the value of the hrResponse property.
     * 
     * @return
     *     possible object is
     *     {@link EmployeeWarningStatus }
     *     
     */
    public EmployeeWarningStatus getHrResponse() {
        return hrResponse;
    }

    /**
     * Sets the value of the hrResponse property.
     * 
     * @param value
     *     allowed object is
     *     {@link EmployeeWarningStatus }
     *     
     */
    public void setHrResponse(EmployeeWarningStatus value) {
        this.hrResponse = value;
    }

    /**
     * Gets the value of the cancelResponse property.
     * 
     * @return
     *     possible object is
     *     {@link EmployeeWarningStatus }
     *     
     */
    public EmployeeWarningStatus getCancelResponse() {
        return cancelResponse;
    }

    /**
     * Sets the value of the cancelResponse property.
     * 
     * @param value
     *     allowed object is
     *     {@link EmployeeWarningStatus }
     *     
     */
    public void setCancelResponse(EmployeeWarningStatus value) {
        this.cancelResponse = value;
    }

    /**
     * Gets the value of the images property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the images property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getImages().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WarningImageDetail }
     * 
     * 
     */
    public List<WarningImageDetail> getImages() {
        if (images == null) {
            images = new ArrayList<WarningImageDetail>();
        }
        return this.images;
    }

	public void setImages(List<WarningImageDetail> images) {
		this.images = images;
	}

	public Date getDoi() {
		return doi;
	}

	public void setDoi(Date doi) {
		this.doi = doi;
	}
	
}
