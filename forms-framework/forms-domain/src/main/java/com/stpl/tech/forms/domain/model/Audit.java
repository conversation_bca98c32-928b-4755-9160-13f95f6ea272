//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.04.03 at 03:07:31 PM IST 
//


package com.stpl.tech.forms.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for Audit complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Audit"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="auditForm" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="auditor" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="auditUnit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="auditDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="auditTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="auditType" type="{http://www.w3schools.com}AuditType"/&gt;
 *         &lt;element name="auditSubmissionDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="cafeManager" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="managerOnDuty" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="areaManager" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="totalScore" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="acquiredScore" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="projectedTotalScore" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="projectedAcquiredScore" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="percentage" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="values" type="{http://www.w3schools.com}AuditValues" maxOccurs="unbounded"/&gt;
 *         &lt;element name="cancel" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Audit", propOrder = {
    "id",
    "auditForm",
    "auditor",
    "auditUnit",
    "auditDate",
    "auditTime",
    "auditType",
    "auditSubmissionDate",
    "cafeManager",
    "managerOnDuty",
    "areaManager",
    "totalScore",
    "acquiredScore",
    "projectedTotalScore",
    "projectedAcquiredScore",
    "percentage",
    "values",
    "cancel"
})
public class Audit {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true)
    protected IdCodeName auditForm;
    @XmlElement(required = true)
    protected IdCodeName auditor;
    @XmlElement(required = true)
    protected IdCodeName auditUnit;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date auditDate;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date auditTime;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected AuditType auditType;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter3 .class)
    @XmlSchemaType(name = "date")
    protected Date auditSubmissionDate;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName cafeManager;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName managerOnDuty;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName areaManager;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal totalScore;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal acquiredScore;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal projectedTotalScore;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal projectedAcquiredScore;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal percentage;
    @XmlElement(required = true)
    protected List<AuditValues> values;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean cancel;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the auditForm property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getAuditForm() {
        return auditForm;
    }

    /**
     * Sets the value of the auditForm property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setAuditForm(IdCodeName value) {
        this.auditForm = value;
    }

    /**
     * Gets the value of the auditor property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getAuditor() {
        return auditor;
    }

    /**
     * Sets the value of the auditor property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setAuditor(IdCodeName value) {
        this.auditor = value;
    }

    /**
     * Gets the value of the auditUnit property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getAuditUnit() {
        return auditUnit;
    }

    /**
     * Sets the value of the auditUnit property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setAuditUnit(IdCodeName value) {
        this.auditUnit = value;
    }

    /**
     * Gets the value of the auditDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getAuditDate() {
        return auditDate;
    }

    /**
     * Sets the value of the auditDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAuditDate(Date value) {
        this.auditDate = value;
    }

    /**
     * Gets the value of the auditTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getAuditTime() {
        return auditTime;
    }

    /**
     * Sets the value of the auditTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAuditTime(Date value) {
        this.auditTime = value;
    }

    /**
     * Gets the value of the auditType property.
     * 
     * @return
     *     possible object is
     *     {@link AuditType }
     *     
     */
    public AuditType getAuditType() {
        return auditType;
    }

    /**
     * Sets the value of the auditType property.
     * 
     * @param value
     *     allowed object is
     *     {@link AuditType }
     *     
     */
    public void setAuditType(AuditType value) {
        this.auditType = value;
    }

    /**
     * Gets the value of the auditSubmissionDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getAuditSubmissionDate() {
        return auditSubmissionDate;
    }

    /**
     * Sets the value of the auditSubmissionDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAuditSubmissionDate(Date value) {
        this.auditSubmissionDate = value;
    }

    /**
     * Gets the value of the cafeManager property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getCafeManager() {
        return cafeManager;
    }

    /**
     * Sets the value of the cafeManager property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setCafeManager(IdCodeName value) {
        this.cafeManager = value;
    }

    /**
     * Gets the value of the managerOnDuty property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getManagerOnDuty() {
        return managerOnDuty;
    }

    /**
     * Sets the value of the managerOnDuty property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setManagerOnDuty(IdCodeName value) {
        this.managerOnDuty = value;
    }

    /**
     * Gets the value of the areaManager property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getAreaManager() {
        return areaManager;
    }

    /**
     * Sets the value of the areaManager property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setAreaManager(IdCodeName value) {
        this.areaManager = value;
    }

    /**
     * Gets the value of the totalScore property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getTotalScore() {
        return totalScore;
    }

    /**
     * Sets the value of the totalScore property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTotalScore(BigDecimal value) {
        this.totalScore = value;
    }

    /**
     * Gets the value of the acquiredScore property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getAcquiredScore() {
        return acquiredScore;
    }

    /**
     * Sets the value of the acquiredScore property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcquiredScore(BigDecimal value) {
        this.acquiredScore = value;
    }

    /**
     * Gets the value of the projectedTotalScore property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getProjectedTotalScore() {
        return projectedTotalScore;
    }

    /**
     * Sets the value of the projectedTotalScore property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProjectedTotalScore(BigDecimal value) {
        this.projectedTotalScore = value;
    }

    /**
     * Gets the value of the projectedAcquiredScore property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getProjectedAcquiredScore() {
        return projectedAcquiredScore;
    }

    /**
     * Sets the value of the projectedAcquiredScore property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProjectedAcquiredScore(BigDecimal value) {
        this.projectedAcquiredScore = value;
    }

    /**
     * Gets the value of the percentage property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getPercentage() {
        return percentage;
    }

    /**
     * Sets the value of the percentage property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPercentage(BigDecimal value) {
        this.percentage = value;
    }

    /**
     * Gets the value of the values property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the values property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getValues().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AuditValues }
     * 
     * 
     */
    public List<AuditValues> getValues() {
        if (values == null) {
            values = new ArrayList<AuditValues>();
        }
        return this.values;
    }

    /**
     * Gets the value of the cancel property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isCancel() {
        return cancel;
    }

    /**
     * Sets the value of the cancel property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setCancel(Boolean value) {
        this.cancel = value;
    }

}
