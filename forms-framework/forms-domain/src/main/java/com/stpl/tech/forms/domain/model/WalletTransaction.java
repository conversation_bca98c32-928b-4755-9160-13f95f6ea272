package com.stpl.tech.forms.domain.model;

import com.stpl.tech.expense.domain.model.WalletTransactionEntityType;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WalletTransaction {
	private Integer id;
	private Integer walletId;
	private WalletTransactionEntityType entityType;
	private Integer entityId;
	private WalletTransactionType transactionType;
	private BigDecimal transactionAmount;
	private WalletTransactionCode transactionCode;
	private WalletTransactionCodeType transactionCodeType;
	private WalletTransactionStatus transactionStatus;
	private Date transactionTime;
	private IdCodeName createdBy;
	private IssuingEntity entity;
	private List<TopupDenominationDetail> denomList = new ArrayList<>();
	private Boolean forced;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getWalletId() {
		return walletId;
	}

	public void setWalletId(Integer walletId) {
		this.walletId = walletId;
	}

	public WalletTransactionEntityType getEntityType() {
		return entityType;
	}

	public void setEntityType(WalletTransactionEntityType entityType) {
		this.entityType = entityType;
	}

	public Integer getEntityId() {
		return entityId;
	}

	public void setEntityId(Integer entityId) {
		this.entityId = entityId;
	}

	public WalletTransactionType getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(WalletTransactionType transactionType) {
		this.transactionType = transactionType;
	}

	public BigDecimal getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(BigDecimal transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	public WalletTransactionCode getTransactionCode() {
		return transactionCode;
	}

	public void setTransactionCode(WalletTransactionCode transactionCode) {
		this.transactionCode = transactionCode;
	}

	public WalletTransactionCodeType getTransactionCodeType() {
		return transactionCodeType;
	}

	public void setTransactionCodeType(WalletTransactionCodeType transactionCodeType) {
		this.transactionCodeType = transactionCodeType;
	}

	public WalletTransactionStatus getTransactionStatus() {
		return transactionStatus;
	}

	public void setTransactionStatus(WalletTransactionStatus transactionStatus) {
		this.transactionStatus = transactionStatus;
	}

	public Date getTransactionTime() {
		return transactionTime;
	}

	public void setTransactionTime(Date transactionTime) {
		this.transactionTime = transactionTime;
	}

	public IdCodeName getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(IdCodeName createdBy) {
		this.createdBy = createdBy;
	}

	public IssuingEntity getEntity() {
		return entity;
	}

	public void setEntity(IssuingEntity entity) {
		this.entity = entity;
	}

	public List<TopupDenominationDetail> getDenomList() {
		return denomList;
	}

	public void setDenomList(List<TopupDenominationDetail> denomList) {
		this.denomList = denomList;
	}

	public Boolean getForced() {
		return forced;
	}

	public void setForced(Boolean forced) {
		this.forced = forced;
	}

}
