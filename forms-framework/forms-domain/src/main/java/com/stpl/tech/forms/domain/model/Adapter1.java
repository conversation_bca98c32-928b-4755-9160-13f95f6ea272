//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.17 at 05:17:52 PM IST 
//


package com.stpl.tech.forms.domain.model;

import java.math.BigDecimal;
import javax.xml.bind.annotation.adapters.XmlAdapter;

public class Adapter1
    extends XmlAdapter<String, BigDecimal>
{


    public BigDecimal unmarshal(String value) {
        return (com.stpl.tech.forms.domain.model.BigDecimalAdapter.parseBigDecimal(value));
    }

    public String marshal(BigDecimal value) {
        return (com.stpl.tech.forms.domain.model.BigDecimalAdapter.printBigDecimal(value));
    }

}
