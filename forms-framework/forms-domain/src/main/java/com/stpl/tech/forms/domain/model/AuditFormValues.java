//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.17 at 05:17:52 PM IST 
//


package com.stpl.tech.forms.domain.model;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for AuditFormValues complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AuditFormValues"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="auditForm" type="{http://www.w3schools.com}AuditForm"/&gt;
 *         &lt;element name="entityLabel" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="entityDescription" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="entityType" type="{http://www.w3schools.com}EntityType"/&gt;
 *         &lt;element name="entityValues" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="entityMetadata" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="linkedDataType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="linkedEntity" type="{http://www.w3schools.com}AuditFormValues"/&gt;
 *         &lt;element name="answeredBy" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="appearanceOrder" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="linkedApi" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="scoreCounted" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="maxScore" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="attachDoc" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="additionalComment" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="isMandatory" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="questionOptional" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AuditFormValues", propOrder = {
    "id",
    "auditForm",
    "entityLabel",
    "entityDescription",
    "entityType",
    "entityValues",
    "entityMetadata",
    "linkedDataType",
    "linkedEntity",
    "answeredBy",
    "appearanceOrder",
    "linkedApi",
    "scoreCounted",
    "status",
    "maxScore",
    "attachDoc",
    "additionalComment",
    "isMandatory",
    "questionOptional"
})
public class AuditFormValues {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, nillable = true)
    protected AuditForm auditForm;
    @XmlElement(required = true, nillable = true)
    protected String entityLabel;
    @XmlElement(required = true, nillable = true)
    protected String entityDescription;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected EntityType entityType;
    @XmlElement(required = true, nillable = true)
    protected String entityValues;
    @XmlElement(required = true, nillable = true)
    protected String entityMetadata;
    @XmlElement(required = true, nillable = true)
    protected String linkedDataType;
    @XmlElement(required = true, nillable = true)
    protected AuditFormValues linkedEntity;
    @XmlElement(required = true, nillable = true)
    protected String answeredBy;
    protected int appearanceOrder;
    @XmlElement(required = true, nillable = true)
    protected String linkedApi;
    protected boolean scoreCounted;
    @XmlElement(required = true)
    protected String status;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal maxScore;
    protected boolean attachDoc;
    protected boolean additionalComment;
    @XmlElement(defaultValue = "false")
    protected boolean isMandatory;
    @XmlElement(defaultValue = "false")
    protected boolean questionOptional;
    protected boolean showAnswerScore;
    private boolean showMaxScore;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the auditForm property.
     * 
     * @return
     *     possible object is
     *     {@link AuditForm }
     *     
     */
    public AuditForm getAuditForm() {
        return auditForm;
    }

    /**
     * Sets the value of the auditForm property.
     * 
     * @param value
     *     allowed object is
     *     {@link AuditForm }
     *     
     */
    public void setAuditForm(AuditForm value) {
        this.auditForm = value;
    }

    /**
     * Gets the value of the entityLabel property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEntityLabel() {
        return entityLabel;
    }

    /**
     * Sets the value of the entityLabel property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEntityLabel(String value) {
        this.entityLabel = value;
    }

    /**
     * Gets the value of the entityDescription property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEntityDescription() {
        return entityDescription;
    }

    /**
     * Sets the value of the entityDescription property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEntityDescription(String value) {
        this.entityDescription = value;
    }

    /**
     * Gets the value of the entityType property.
     * 
     * @return
     *     possible object is
     *     {@link EntityType }
     *     
     */
    public EntityType getEntityType() {
        return entityType;
    }

    /**
     * Sets the value of the entityType property.
     * 
     * @param value
     *     allowed object is
     *     {@link EntityType }
     *     
     */
    public void setEntityType(EntityType value) {
        this.entityType = value;
    }

    /**
     * Gets the value of the entityValues property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEntityValues() {
        return entityValues;
    }

    /**
     * Sets the value of the entityValues property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEntityValues(String value) {
        this.entityValues = value;
    }

    /**
     * Gets the value of the entityMetadata property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEntityMetadata() {
        return entityMetadata;
    }

    /**
     * Sets the value of the entityMetadata property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEntityMetadata(String value) {
        this.entityMetadata = value;
    }

    /**
     * Gets the value of the linkedDataType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLinkedDataType() {
        return linkedDataType;
    }

    /**
     * Sets the value of the linkedDataType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLinkedDataType(String value) {
        this.linkedDataType = value;
    }

    /**
     * Gets the value of the linkedEntity property.
     * 
     * @return
     *     possible object is
     *     {@link AuditFormValues }
     *     
     */
    public AuditFormValues getLinkedEntity() {
        return linkedEntity;
    }

    /**
     * Sets the value of the linkedEntity property.
     * 
     * @param value
     *     allowed object is
     *     {@link AuditFormValues }
     *     
     */
    public void setLinkedEntity(AuditFormValues value) {
        this.linkedEntity = value;
    }

    /**
     * Gets the value of the answeredBy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAnsweredBy() {
        return answeredBy;
    }

    /**
     * Sets the value of the answeredBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAnsweredBy(String value) {
        this.answeredBy = value;
    }

    /**
     * Gets the value of the appearanceOrder property.
     * 
     */
    public int getAppearanceOrder() {
        return appearanceOrder;
    }

    /**
     * Sets the value of the appearanceOrder property.
     * 
     */
    public void setAppearanceOrder(int value) {
        this.appearanceOrder = value;
    }

    /**
     * Gets the value of the linkedApi property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLinkedApi() {
        return linkedApi;
    }

    /**
     * Sets the value of the linkedApi property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLinkedApi(String value) {
        this.linkedApi = value;
    }

    /**
     * Gets the value of the scoreCounted property.
     * 
     */
    public boolean isScoreCounted() {
        return scoreCounted;
    }

    /**
     * Sets the value of the scoreCounted property.
     * 
     */
    public void setScoreCounted(boolean value) {
        this.scoreCounted = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the maxScore property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getMaxScore() {
        return maxScore;
    }

    /**
     * Sets the value of the maxScore property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMaxScore(BigDecimal value) {
        this.maxScore = value;
    }

    /**
     * Gets the value of the attachDoc property.
     * 
     */
    public boolean isAttachDoc() {
        return attachDoc;
    }

    /**
     * Sets the value of the attachDoc property.
     * 
     */
    public void setAttachDoc(boolean value) {
        this.attachDoc = value;
    }

    /**
     * Gets the value of the additionalComment property.
     * 
     */
    public boolean isAdditionalComment() {
        return additionalComment;
    }

    /**
     * Sets the value of the additionalComment property.
     * 
     */
    public void setAdditionalComment(boolean value) {
        this.additionalComment = value;
    }

    /**
     * Gets the value of the isMandatory property.
     *
     */
    public boolean isIsMandatory() {
        return isMandatory;
    }

    /**
     * Sets the value of the isMandatory property.
     *
     */
    public void setIsMandatory(boolean value) {
        this.isMandatory = value;
    }

    /**
     * Gets the value of the questionOptional property.
     *
     */
    public boolean isQuestionOptional() {
        return questionOptional;
    }

    /**
     * Sets the value of the questionOptional property.
     *
     */
    public void setQuestionOptional(boolean value) {
        this.questionOptional = value;
    }

    public boolean isShowAnswerScore() {
        return showAnswerScore;
    }

    public void setShowAnswerScore(boolean showAnswerScore) {
        this.showAnswerScore = showAnswerScore;
    }

    public boolean isShowMaxScore() {
        return showMaxScore;
    }

    public void setShowMaxScore(boolean showMaxScore) {
        this.showMaxScore = showMaxScore;
    }
}
