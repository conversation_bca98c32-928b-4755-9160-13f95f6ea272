package com.stpl.tech.expense.domain.model;

import com.stpl.tech.forms.domain.model.IdCodeName;

import java.math.BigDecimal;
import java.util.List;

public class ClaimRequestVO {
    private ClaimType claimType;
    private IdCodeName requestBy;
    private List<Integer> voucherIds;
    private String accountNo;
    private Integer claimId;
    private ClaimStatus status;
    private String comment;
    private String happayId;
    private BigDecimal claimAmount;

    public ClaimType getClaimType() {
        return claimType;
    }

    public void setClaimType(ClaimType claimType) {
        this.claimType = claimType;
    }

    public IdCodeName getRequestBy() {
        return requestBy;
    }

    public void setRequestBy(IdCodeName requestBy) {
        this.requestBy = requestBy;
    }

    public List<Integer> getVoucherIds() {
        return voucherIds;
    }

    public void setVoucherIds(List<Integer> voucherIds) {
        this.voucherIds = voucherIds;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public Integer getClaimId() {
        return claimId;
    }

    public void setClaimId(Integer claimId) {
        this.claimId = claimId;
    }

    public ClaimStatus getStatus() {
        return status;
    }

    public void setStatus(ClaimStatus status) {
        this.status = status;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getHappayId() {
        return happayId;
    }

    public void setHappayId(String happayId) {
        this.happayId = happayId;
    }

    public BigDecimal getClaimAmount() {
        return claimAmount;
    }

    public void setClaimAmount(BigDecimal claimAmount) {
        this.claimAmount = claimAmount;
    }
}
