package com.stpl.tech.expense.domain.model;

import com.stpl.tech.forms.domain.model.IdCodeName;

import java.util.Date;

public class ClaimLog {

    private Integer id;
    private Integer claimId;
    private ClaimStatus fromStatus;
    private ClaimStatus toStatus;
    private IdCodeName updatedBy;
    private String comments;
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getClaimId() {
        return claimId;
    }

    public void setClaimId(Integer claimId) {
        this.claimId = claimId;
    }

    public ClaimStatus getFromStatus() {
        return fromStatus;
    }

    public void setFromStatus(ClaimStatus fromStatus) {
        this.fromStatus = fromStatus;
    }

    public ClaimStatus getToStatus() {
        return toStatus;
    }

    public void setToStatus(ClaimStatus toStatus) {
        this.toStatus = toStatus;
    }

    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(IdCodeName updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
