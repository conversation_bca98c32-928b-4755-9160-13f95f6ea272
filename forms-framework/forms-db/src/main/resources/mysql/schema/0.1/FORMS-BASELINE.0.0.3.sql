ALTER TABLE KETTLE_DEV.VOUCHER_DATA ADD COLUMN CLAIM_ID INTEGER DEFAULT NULL;

CREATE TABLE KETTLE_DEV.CLAIM_DETAIL (
  CLAIM_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  C<PERSON>IM_TYPE VARCHAR(50) NOT NULL,
  CREATION_TIME TIMESTAMP,
  LAST_UPDATE_TIME TIMESTAMP,
  CREATED_BY INTEGER NOT NULL,
  LAST_UPDATED_BY INTEGER NOT NULL,
  CURRENT_STATUS VARCHAR(50) NOT NULL,
  UNIT_ID INTEGER,
  EMPLOYEE_ID INTEGER,
  REQUESTED_AMOUNT DECIMAL(16,6) NOT NULL,
  APPROVED_AMOUNT DECIMAL(16,6)
);

CREATE TABLE KETTLE_DEV.CLAIM_LOG_DETAIL (
  CLAIM_LOG_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  <PERSON><PERSON>IM_ID INTEGER NOT NULL,
  FROM_STATUS VARCHAR(50) NOT NULL,
  TO_STATUS VARCHAR(50) NOT NULL,
  UPDATED_BY INTEGER NOT NULL,
  COMMENTS VARCHAR(1000) DEFAULT NULL,
  UPDATE_TIME TIMESTAMP
);

ALTER TABLE KETTLE_DEV.CLAIM_DETAIL ADD COLUMN HAPPAY_ID VARCHAR(50);
ALTER TABLE KETTLE_DEV.CLAIM_DETAIL ADD COLUMN WALLET_ID INTEGER NOT NULL;

ALTER TABLE KETTLE_DEV.WALLET_TRANSACTION_DATA ADD COLUMN ENTITY_TYPE VARCHAR(50);
ALTER TABLE KETTLE_DEV.WALLET_TRANSACTION_DATA ADD COLUMN ENTITY_ID INTEGER;

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES ('PETTY_CASH_MANAGER_CAFE', 'Access to petty cash management cafe side', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES ('PETTY_CASH_MANAGER_AM', 'Access to petty cash management for area manager', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES ('PETTY_CASH_MANAGER_FINANCE', 'Access to petty cash management finance', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('FSPCMC', '11', 'MENU', 'VIEW', 'Forms Service -> voucher/claim management -> view', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('FSPCMAM', '11', 'MENU', 'VIEW', 'Forms Service -> petty cash management AM -> View', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('FSPCMF', '11', 'MENU', 'VIEW', 'Forms Service -> petty cash management finance -> View', 'ACTIVE');


INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME) VALUES
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PETTY_CASH_MANAGER_CAFE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSPCMC'), 'ACTIVE', 120103, '2018-11-03 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PETTY_CASH_MANAGER_AM'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSPCMAM'), 'ACTIVE', 120103, '2018-11-03 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PETTY_CASH_MANAGER_FINANCE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSPCMF'), 'ACTIVE', 120103, '2018-11-03 00:00:00');

ALTER TABLE KETTLE_DEV.WALLET_DATA
CHANGE COLUMN `ACCOUNT_HOLDER_CONTACT` `ACCOUNT_HOLDER_CONTACT` VARCHAR(11) NOT NULL ;

CREATE TABLE KETTLE_MASTER_DEV.EXPENSE_VALIDATION (
  VALIDATION_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  VALIDATION_NAME VARCHAR(100) NOT NULL,
  VALIDATION_TYPE VARCHAR(50) NOT NULL,
  VALIDATION_STATUS VARCHAR(30) NOT NULL
);

CREATE TABLE KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING(
  ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  EXPENSE_ID INTEGER NOT NULL,
  VALIDATION_ID INTEGER NOT NULL,
  MAPPING_STATUS VARCHAR(30) NOT NULL
);

ALTER TABLE KETTLE_DEV.VOUCHER_FILE_DATA ADD COLUMN FILE_TYPE VARCHAR(20) NOT NULL;
ALTER TABLE KETTLE_DEV.VOUCHER_FILE_DATA CHANGE COLUMN MIME_TYPE CONTENT_TYPE VARCHAR(10) NOT NULL;


CREATE TABLE KETTLE_DEV.VOUCHER_REJECTION_DATA(
  REJECTION_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  NAME VARCHAR(250) NOT NULL,
  REJECTED_BY INTEGER NOT NULL,
  REJECTION_ENTITY VARCHAR(20) NOT NULL,
  VOUCHER_ID INTEGER NOT NULL
);

INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Invoice Mandatory', 'RESTRICTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Supporting Mandatory', 'RESTRICTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Our Company Name Not Mentioned', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Stamp of our company not available in case of cash memo', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Date on Bill not available', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Bill not visible clearly', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Amount calculation error', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Revenue stamp with crossed signature not available on bills amounting Rs.5000/- or above', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Undetermined expense.(eg. incentives which are already being paid as part of salary)', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Unrecognisable expense', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Exchange commission on haryana cafes', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Duplicate bills', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('GRN required on all invoices regarding Goods purchase', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Incorrect Date', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Incorrect Invoice', 'REJECTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_VALIDATION` (`VALIDATION_NAME`, `VALIDATION_TYPE`, `VALIDATION_STATUS`) VALUES ('Incorrect supporting', 'REJECTION', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '1', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('3', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('13', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('18', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('19', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('20', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('26', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('42', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '2', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '3', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '3', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('3', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('6', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('7', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('8', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('13', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('18', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('19', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('20', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('24', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('25', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('26', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '4', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('42', '4', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('13', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('18', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('19', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('20', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('26', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('42', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '5', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '5', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('13', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('18', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('19', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('20', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('26', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('42', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '6', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '6', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('3', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('6', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('7', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('8', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('13', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('18', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('19', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('20', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('24', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('25', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('26', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('42', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '7', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '7', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('3', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('6', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('7', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('8', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('13', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('18', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('19', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('20', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('24', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('25', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('26', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('42', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '8', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '8', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('3', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('6', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('7', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('8', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('13', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('18', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('19', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('20', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('24', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('25', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('26', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('42', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '9', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '9', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('3', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('6', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('7', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('8', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('13', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('18', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('19', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('20', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('24', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('25', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('26', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('42', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '10', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '10', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '11', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('3', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('6', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('7', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('8', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('13', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('18', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('19', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('20', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('24', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('25', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('26', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('42', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '12', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '12', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '13', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '13', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '13', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '13', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('13', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('18', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('19', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('20', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('26', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('42', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '14', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '14', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '15', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '15', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('1', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('2', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('3', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('4', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('5', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('9', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('10', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('11', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('12', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('13', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('14', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('15', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('16', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('17', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('18', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('19', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('20', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('21', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('22', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('23', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('26', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('27', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('42', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('43', '16', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_VALIDATION_MAPPING (`EXPENSE_ID`, `VALIDATION_ID`, `MAPPING_STATUS`) VALUES ('44', '16', 'ACTIVE');