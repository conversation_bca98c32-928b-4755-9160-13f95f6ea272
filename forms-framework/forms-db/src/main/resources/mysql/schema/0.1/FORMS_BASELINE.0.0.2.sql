  
 CREATE TABLE `KETTLE_DEV`.`<PERSON><PERSON>LOYEE_WARNING_DETAIL` (
  `WARNING_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `PQSC_AUDIT_ID` INT(11) NULL,
  `UNIT_ID` INT(11) NOT NULL,
  `MANAGER_ON_DUTY` INT(11) NULL,
  `GUILTY_PERSON_ID` INT(11) NOT NULL,
  `GUILTY_PERSON_NAME` VARCHAR(100) NOT NULL,
  `GUILTY_PERSON_DESIGNATION` VARCHAR(100) NULL,
  `IMPACT_TYPE` VARCHAR(10) NOT NULL,
  `WARNING_STAGE` VARCHAR(100) NOT NULL,
  `WARNING_STATUS` VARCHAR(45) NOT NULL,
  `HR_DGM_CONFLICT` VARCHAR(1) NULL default NULL,
  `CREATION_TIME` TIMESTAMP NULL,
  `LATEST_STATUS_ID` INT(11) NULL,
  `AREA_MANAGER_ID` INT(11) NOT NULL,
  `WARNING_LETTER_REPORT_ID` INTEGER NULL,
  PRIMARY KEY (`WARNING_ID`),
  INDEX `EMPLOYEE_WARNING_DETAIL_WARNING_STAGE` USING BTREE (`WARNING_STAGE` ASC));


 CREATE TABLE `KETTLE_DEV`.`EMPLOYEE_WARNING_STATUS` (
  `EMPLOYEE_WARNING_STATUS_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `WARNING_ID` INT(11) NOT NULL,
  `FROM_STATUS` VARCHAR(100) NOT NULL,
  `TO_STATUS` VARCHAR(100) NOT NULL,
  `AUTHORITY_ID` INT(11) NOT NULL,
  `AUTHORITY_DESIGNATION` VARCHAR(100) NULL,
  `AUTHORITY_NAME` VARCHAR(100) NOT NULL,
  `COMMENT` VARCHAR(1000) NULL,
  `ACTION_TAKEN_BY` VARCHAR(45) NOT NULL,
  `ACTION_TAKEN_ON` TIMESTAMP NULL,
  PRIMARY KEY (`EMPLOYEE_WARNING_STATUS_ID`));
  
  
 CREATE TABLE `KETTLE_DEV`.`EMPLOYEE_WARNING_REASON` (
  `EMPLOYEE_WARNING_REASON_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `WARNING_ID` INT(11) NOT NULL,
  `EMPLOYEE_WARNING_STATUS_ID` INT(11) NULL,
  `REASON_ADDED_BY` VARCHAR(20) NOT NULL,
  `REASON_ID` INT(11) NOT NULL,
  `REASON_NAME` VARCHAR(150) NOT NULL,
  `REASON_CATEGORY` VARCHAR(45) NOT NULL,
  `STATUS` VARCHAR(20) NOT NULL,
  `LAST_UPDATED_ON` TIMESTAMP NULL,
  PRIMARY KEY (`EMPLOYEE_WARNING_REASON_ID`));
  

CREATE TABLE `KETTLE_DEV`.`WARNING_IMAGE_DETAIL_DATA` (
  `IMAGE_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `WARNING_ID` INT(11) NOT NULL,
  `IMAGE_URL` VARCHAR(500) NOT NULL,
  `IMAGE_NAME` VARCHAR(100) NOT NULL,
  `STATUS` VARCHAR(10) NOT NULL,
  `CREATED_ON` TIMESTAMP NULL,
  DOCUMENT_MIME_TYPE VARCHAR(45) NOT NULL,
  DOCUMENT_S3_KEY VARCHAR(180) NULL,
  DOCUMENT_S3_BUCKET VARCHAR(180) NULL,
  PRIMARY KEY (`IMAGE_ID`));
  
 
  ALTER TABLE `KETTLE_DEV`.`EMPLOYEE_WARNING_DETAIL` ADD COLUMN DATE_OF_INCIDENCE DATE NOT NULL;
  
  UPDATE KETTLE_DEV.AUDIT_FORM SET `NAME`='Support Visit', `DESCRIPTION`='Support Visit of AM/DGM' WHERE `NAME`='Area Manager Visit';
  
  


  