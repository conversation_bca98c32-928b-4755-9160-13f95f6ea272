INSERT INTO KETTLE.VOUCHER_STATUS_DATA(VOUCHER_ID, FROM_STATUS, TO_STATUS, GENERATED_BY, ACTION_COMMENT, ACTION_TIME, TRANSITION_STATUS)
select a.VOUCHER_ID,s.TO_STATUS,'SETTLED', 120056, 'Auto Settled In Bulk', a.LAST_UPDATE_TIME , 'SUCCESS' from VOUCHER_STATUS_DATA s, (
select v.VOUCHER_ID, v.CURRENT_STATUS,v.LAST_UPDATE_TIME, max(s.ID) ID from KETTLE.VOUCHER_DATA v, KETTLE.VOUCHER_STATUS_DATA s where 
v.VOUCHER_ID = s.VOUCHER_ID and 
CURRENT_STATUS = 'SETTLED'
AND EXPENSE_AMOUNT > 0
group by s.VOUCHER_ID
)a
where 
s.ID = a.ID
;