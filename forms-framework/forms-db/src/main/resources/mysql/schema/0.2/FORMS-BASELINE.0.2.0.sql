CREATE TABLE `KETTLE_DEV`.`BATCH_CODE` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `BATCH_DATA_ID` INT(11) NOT NULL,
  `BATCH_CODE` VARCHAR(50) NOT NULL,
  `STATUS` VARCHAR(10) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE INDEX `ID_UNIQUE` (`ID` ASC));

CREATE TABLE `KETTLE_DEV`.`BATCH_CODE_COUNTER` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `CODE_PREFIX` VARCHAR(45) NOT NULL,
  `CURRENT_COUNTER` INT(10) NOT NULL DEFAULT '0',
  `NEXT_COUNTER` INT(10) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  UNIQUE INDEX `ID_UNIQUE` (`ID` ASC),
  UNIQUE INDEX `CODE_PREFIX_UNIQUE` (`CODE_PREFIX` ASC));

CREATE TABLE `KETTLE_DEV`.`BATCH_CODE_PREFIX` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `KEY_ID` VARCHAR(10) NOT NULL,
  `KEY_TYPE` VARCHAR(10) NOT NULL,
  `CODE_PREFIX` VARCHAR(10) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE INDEX `ID_UNIQUE` (`ID` ASC),
  UNIQUE INDEX `CODE_PREFIX_UNIQUE` (`CODE_PREFIX` ASC),
  UNIQUE INDEX `KEY_ID_UNIQUE` (`KEY_ID` ASC));

CREATE TABLE `KETTLE_DEV`.`BATCH_DATA` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `CODE_PREFIX` VARCHAR(11) NOT NULL,
  `MANUFACTURE_DATE` DATETIME NOT NULL,
  `EXPIRY_DATE` DATETIME NOT NULL,
  `CREATED_BY` INT(11) NOT NULL,
  `COUNT` INT(11) NOT NULL,
  `LENGTH` INT(11) NOT NULL,
  `UNIT_ID` INT(11) NOT NULL,
  `UNIT_BATCH_CODE` VARCHAR(10) NOT NULL,
  `UNIT_NAME` VARCHAR(45) NULL DEFAULT NULL,
  `PRODUCT_ID` INT(11) NOT NULL,
  `PRODUCT_BATCH_CODE` VARCHAR(10) NOT NULL,
  `PRODUCT_NAME` VARCHAR(45) NULL DEFAULT NULL,

  PRIMARY KEY (`ID`),
  UNIQUE INDEX `ID_UNIQUE` (`ID` ASC),
  UNIQUE INDEX `CODE_PREFIX_UNIQUE` (`CODE_PREFIX` ASC));


ALTER TABLE KETTLE_DEV.BATCH_DATA
ADD COLUMN CODE_GENERATION_DATE DATE NULL,
ADD COLUMN DATE_BATCH_CODE VARCHAR(2) NULL;

UPDATE `KETTLE_MASTER_DEV`.`UNIT_DETAIL` SET `UNIT_SUB_CATEGORY`='DKC_PLANT' WHERE `UNIT_ID`='26080';

INSERT INTO `KETTLE_DEV`.`BATCH_CODE_PREFIX` (`KEY_ID`, `KEY_TYPE`, `CODE_PREFIX`) VALUES ('26080', 'UNIT', 'B');
INSERT INTO `KETTLE_DEV`.`BATCH_CODE_PREFIX` (`KEY_ID`, `KEY_TYPE`, `CODE_PREFIX`) VALUES ('1650', 'SKU', 'D');
INSERT INTO `KETTLE_DEV`.`BATCH_CODE_PREFIX` (`KEY_ID`, `KEY_TYPE`, `CODE_PREFIX`) VALUES ('1589', 'SKU', 'E');
INSERT INTO `KETTLE_DEV`.`BATCH_CODE_PREFIX` (`KEY_ID`, `KEY_TYPE`, `CODE_PREFIX`) VALUES ('1611', 'SKU', 'F');

