<!doctype html>

<html>
<head>
    <meta charset="utf-8">
    <title>Chaayos Forms</title>
    <meta name="description" content="Chaayos Forms">
    <meta name="author" content="@chaayos">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="theme-color" content="#5e7e47" id="themeColor" />
    <meta name="full-screen" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="#5e7e47" />
    <meta name="apple-mobile-web-app-title" content="Chaayos" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link rel="manifest" href="manifest.json" />

    <link rel="icon" type="image/png" sizes="32x32" href="../img/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="../img/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../img/favicon-16x16.png">

    <link rel="stylesheet" href="../css/bootstrap.min.css" />
    <link rel="stylesheet" href="../css/angular-toastr.min.css" />
    <link rel="stylesheet" href="../css/fontawesome-all.min.css" />
    <link rel="stylesheet" href="../css/mobile.css" />
    <link rel="stylesheet" href="../css/stylemobile.css" />

    <script type="application/javascript">
        window.version = ".";
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|BB|PlayBook|IEMobile|Windows Phone|Kindle|Silk|Opera Mini/i.test(navigator.userAgent)) {
            window.device = "mobile";
        }else{
            window.device = "desktop";
        }
    </script>
</head>
<body>

<div data-ng-app="formsApp">
    <div data-ui-view></div>
    <div id="backdrop"></div>
    <div data-ng-if="rootLoading" class="rootLoader">
        <div class="text-center" style="margin-top: 100px">
            <div class="loader l"></div>
        </div>
    </div>
</div>

<script src="../js/libs/jquery.min.js"></script>
<script src="../js/libs/angular.min.js"></script>
<script src="../js/libs/angular-animate.min.js"></script>
<script src="../js/libs/angular-cookies.min.js"></script>
<script src="../js/libs/angular-resource.min.js"></script>
<script src="../js/libs/angular-route.min.js"></script>
<script src="../js/libs/angular-sanitize.min.js"></script>
<script src="../js/libs/angular-ui-router.min.js"></script>
<script src="../js/libs/lodash.min.js"></script>
<link href="../js/libs/select2/select2.min.css" rel="stylesheet">
<script src="../js/libs/select2/select2.min.js"></script>
<script src="../js/libs/select2/ui-select2.js"></script>
<script src="../js/libs/FileSaver.min.js"></script>
<script src="../js/libs/angularjs-dropdown-multiselect.min.js"></script>

<script src="../js/libs/ui-bootstrap-tpls-2.4.0.min.js"></script>
<script src="../js/libs/angular-toastr.tpls.min.js"></script>

<!-- App core & templates -->
<script src="../js/app.js?v=1.7"></script>

<!-- controllers -->
<script src="../js/controllers/loginCtrl.js"></script>
<script src="../js/controllers/dashboardCtrl.js"></script>
<script src="../js/controllers/formsCtrl.js"></script>
<script src="../js/controllers/auditCtrl.js"></script>
<script src="../js/controllers/auditPreviewModalCtrl.js"></script>
<script src="../js/controllers/searchAuditCtrl.js"></script>
<script src="../js/controllers/expenseTrackingController.js"></script>
<script src="../js/controllers/warningDetailCtrl.js"></script>
<script src="../js/controllers/searchWarningDetailCtrl.js"></script>
<script src="../js/controllers/warningImagePreviewCtrl.js"></script>
<script src="../js/controllers/homeCtrl.js"></script>
<script src="../js/controllers/autoLoginCtrl.js"></script>
<script src="../js/controllers/createVoucherCtrl.js"></script>
<script src="../js/controllers/manageVoucherCtrl.js"></script>
<script src="../js/controllers/requestClaimCtrl.js"></script>
<script src="../js/controllers/manageClaimCtrl.js"></script>
<script src="../js/controllers/manageWalletCtrl.js"></script>

<!-- services -->
<script src="../js/services/AppUtil.js?v=1.7"></script>
<script src="../js/services/APIJson.js?v=1.7"></script>
<script src="../js/services/StorageUtil.js"></script>
<script src="../js/services/MetadataService.js"></script>
<script src="../js/services/WalletService.js"></script>
<script type="application/javascript">
    $("#backdrop").on("click touch", function () {
        var node = document.getElementById("sidebar");
        var backdrop = document.getElementById("backdrop");
        if(node.classList.contains("open")){
            node.classList.remove("open");
            backdrop.classList.remove("open");
        }else{
            node.classList.add("open");
            backdrop.classList.add("open");
        }
    })
</script>
</body>
</html>
