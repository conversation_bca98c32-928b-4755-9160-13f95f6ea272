<div data-ng-init="init()">
    <nav class="navbar navbar-primary navbar-fixed-top">
        <div class="container-fluid">
            <div class="navbar-header">
                <button class="navbar-toggle collapsed" data-ng-click="toggleSidebar()"><i class="fas fa-bars"></i></button>
                <a class="navbar-brand" href="#"><span data-ng-if ="selectedUnit == null">Chaayos</span> <span data-ng-if ="selectedUnit != null">{{selectedUnit.name}}</span></a>
                <div class="navbar-nav navbar-right" uib-dropdown is-open="status.isopen" style="display: inline-block;float: right; margin-right: 5px;">
                    <button id="single-button" type="button" class="btn" uib-dropdown-toggle ng-disabled="disabled" style="background: none;color: #fff;">
                        {{userData.name}}<span class="caret" style="margin-left: 5px;"></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right" uib-dropdown-menu role="menu" aria-labelledby="single-button">
                        <li role="menuitem"><a href="#" data-ng-click="logout()">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="navbar-default sidebar" id="sidebar" role="navigation">
            <div class="navbar-primary">
                <div class="navbar-header">
                    <button class="navbar-toggle collapsed" data-ng-click="toggleSidebar()"><i class="fas fa-bars"></i></button>
                    <a class="navbar-brand" href="#">Chaayos</a>
                </div>
            </div>

            <div class="sidebar-nav">
                <ul class="nav" id="side-menu">
                    <li data-ng-click="slideMenu($event)" acl-menu="FSAM">
                        <a>Audit Management <span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a data-ng-click="startAudit()" acl-sub-menu="FSSTAM"> Start Audit </a>
                                <a data-ng-click="searchAudit()" acl-sub-menu="FSSEAM">Search Audit </a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSME">
                        <a>Maintenance Expense <span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a  ui-sref="dashboard.expense({expenseType : 'maintenance', viewType: 'add'})" acl-sub-menu="FSMEAE"> Add Expense </a>
                                <a  ui-sref="dashboard.expense({expenseType : 'maintenance', viewType: 'display'})" acl-sub-menu="FSMEVE">View Expense </a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSMRE">
                        <a> Marketing Expense<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a  ui-sref="dashboard.expense({expenseType : 'marketing', viewType: 'add'})" acl-sub-menu="FSMREAE"> Add Expense </a>
                                <a ui-sref="dashboard.expense({expenseType : 'marketing', viewType: 'display'})" acl-sub-menu="FSMREVE">View Expense </a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSWM">
                        <a> Warning Details<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                 <a data-ng-click="toggleSidebar()"  ui-sref="dashboard.warningDetail({audit : null , viewType: 'add', warningId : null, actionBy : null})" 
                                 acl-sub-menu="FSWMIWWA">Issue Warning Letter</a>
                                <a data-ng-click="toggleSidebar()" ui-sref="dashboard.searchWarningDetail({searchParams : null})" >Search Issued Warning </a>
                            </li>
                        </ul>
                    </li>
                    <!--<li data-ng-click="slideMenu($event)">
                        <a> Wallet Management<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a data-ng-click="toggleSidebar()" data-ui-sref="dashboard.createVoucher">Create Voucher</a>
                                <a data-ng-click="toggleSidebar()" data-ui-sref="dashboard.manageVoucher({actionBy : 'AM'})" >Manage Voucher - AM</a>
                                <a  data-ng-click="toggleSidebar()" data-ui-sref="dashboard.manageVoucher({actionBy : 'FM'})" > Manage Voucher - Finance </a>
                            </li>
                        </ul>
                    </li>-->
                    <li data-ng-click="slideMenu($event)" acl-menu="FSPCMC">
                        <a>Voucher/Claim Request<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a data-ng-click="toggleSidebar()"  data-ui-sref="dashboard.createVoucher">Create Voucher</a>
                                <a data-ng-click="toggleSidebar()"  data-ui-sref="dashboard.requestClaim">Request Claims</a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSPCMAM">
                        <a>Voucher Management AM<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a data-ng-click="toggleSidebar()" data-ui-sref="dashboard.manageVoucher({actionBy : 'AM'})" >Manage Voucher - AM</a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSPCMF">
                        <a>Wallet Management Finance<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a data-ng-click="toggleSidebar()"  data-ui-sref="dashboard.manageClaim">Manage Claims</a>
                                <a  data-ng-click="toggleSidebar()" data-ui-sref="dashboard.manageVoucher({actionBy : 'FM'})" >Manage Voucher</a>
                                <a  data-ng-click="toggleSidebar()" data-ui-sref="dashboard.manageWallet" >Manage Wallet</a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSCL">
                        <a>Checklist<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a  data-ng-click="toggleSidebar()" ui-sref="dashboard.forms({formType:'checkList'})" acl-sub-menu="FSCCL" >Cafe CheckList</a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
            <!-- /.sidebar-collapse -->
        </div>
    </nav>
    <div id="page-wrapper">
        <div ui-view></div>
    </div>
</div>


