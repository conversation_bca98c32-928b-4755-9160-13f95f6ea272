<style>
.row-spacing {
	margin-top: 10px;
}

.warning-container {
	padding-right: 5px;
	padding-left: 5px;
	margin-right: 5%;
	margin-left: 5%;
}
.page-heading{
    font-size: 26px;
    color: red;
    margin-bottom: 13px;
    font-family: 'typewriter';
}
</style>
<div class="warning-container" data-ng-init="init()">
	<div class="row">
		<div class="col-xs-12">
			<h2 class="text-center page-heading" >
				Search Warning Letter Details</h2>
		</div>
	</div>

	<div class="row">
		<div class="col-xs-12">
			<div class="row">
				<div class="col-xs-12">
					<div class="form-group">
						<label for="unitSearchWarning" class="control-label">Select
							Unit </label> <select class="form-control" id="unitSearchWarning"
							name="unitSearchWarning" data-ng-model="searchWarning.selectedUnit"
							data-ng-options="unit as unit.name for unit in units"
							required="required">
						</select>
					</div>
				</div>
			</div>
		</div>
		<div class="col-xs-12">
			<div class="row">
				<div class="col-xs-12 form-group">
					<label class="control-label" for="searchWarningStatus">Warning
						Letter Status</label> <select data-ng-model="selectedStatus"
						class='form-control' id="searchWarningStatus"
						name="searchWarningStatus">
						<option value="APPROVED">Approved</option>
						<option value="CANCELLED">Cancelled</option>
						<option value="PENDING">Pending</option>
						<option value="REJECTED">Rejected</option>
					</select>

				</div>
			</div>
		</div>
		<div class="col-xs-12">
			<div class="row">
				<div class="col-xs-12 form-group">
					<label for="warningStartDate" class="control-label">Start
						Date </label>
					<p class="input-group">
						<input type="text" class="form-control" id="warningStartDate"
							uib-datepicker-popup="yyyy-MM-dd" ng-model="startDate"
							is-open="startDate.opened" datepicker-options="dateOptions"
							placeholder="yyyy-MM-dd" ng-required="false" close-text="Close" />
						<span class="input-group-btn">
							<button type="button" class="btn btn-default"
								ng-click="startDate.opened=true">
								<i class="glyphicon glyphicon-calendar"></i>
							</button>
						</span>
					</p>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-12 form-group">
					<label for="warningEndDate" class="control-label">End date
					</label>
					<p class="input-group">
						<input type="text" class="form-control"
							uib-datepicker-popup="yyyy-MM-dd" ng-model="endDate"
							is-open="endDate.opened" datepicker-options="dateOptions"
							placeholder="yyyy-MM-dd" ng-required="false" close-text="Close" />
						<span class="input-group-btn">
							<button type="button" class="btn btn-default"
								ng-click="endDate.opened=true">
								<i class="glyphicon glyphicon-calendar"></i>
							</button>
						</span>
					</p>
				</div>
			</div>
		</div>
		<div class="col-xs-12">
			<div class="row">
				<div class="col-xs-12 form-group">
					<button class="btn btn-danger"
								data-ng-click="resetDetails()">Reset</button>
					<button class="btn btn-primary pull-right"
						data-ng-click="searchWarningLetter()" style="margin-top: 10px;">Search
						Warning Letter</button>
				</div>
			</div>
		</div>
	</div>
	<div class="row" data-ng-if="warningDetails != null">
		<div class="col-xs-12">
			<table class="table" data-ng-if="warningDetails.length > 0">
				<thead>
					<tr>
						<th scope="col">Guilty Person</th>
						<th scope="col">Issued By</th>
						 <th scope="col">Status</th>
						<!-- <th scope="col">AM Comment</th><th scope="col">DGM Comment</th><th scope="col">HR Comment</th> -->
						<th scope="col">Created On</th>
						<th scope="col">Action</th>
					</tr>
				</thead>
				<tbody>
					<tr data-ng-repeat="item in warningDetails track by item.id">
						<td>{{item.guiltyPerson.name}}</td>
						<td>{{item.initiator.authorisedPerson.name}}</td>
						<td>{{item.warningStage | strReplace:'_':' ' }}</td>
						<!--  <td>{{item.amResponse != null ? item.amResponse.authorisedPerson.name : '-'}}</td>
                                    <td>{{item.dgmResponse != null ? item.dgmResponse.authorisedPerson.name : '-'}}</td>
                                    <td>{{item.hrResponse != null ? item.hrResponse.authorisedPerson.name : '-'}}</td> -->
						<td>{{item.updateTime | date : 'dd/MM/yyyy'}}</td>
						<td>
							<button class="btn btn-sm btn-warning"
								data-ng-click="warningAction(item.id,'cancelResponse')"
								acl-action="FSWMIWBA"
								data-ng-if="(userDetails.id == item.initiator.authorisedPerson.id) && (item.warningStage == 'PENDING_AM_APPROVAL' || (item.warningStage == 'PENDING_DGM_APPROVAL' && item.audit != null))">Cancel</button>
							<button class="btn btn-sm btn-primary"
								data-ng-click="warningAction(item.id,'amResponse')"
								data-ng-if="item.warningStage == 'PENDING_AM_APPROVAL'" acl-action="FSWMAMR">AM</button>
							<button class="btn btn-sm btn-primary"
								data-ng-click="warningAction(item.id,'dgmResponse')"
								data-ng-if="item.warningStage == 'PENDING_DGM_APPROVAL'" acl-action="FSWMDGMR">DGM</button>
							<button class="btn btn-sm btn-primary"
								data-ng-click="warningAction(item.id,'hrResponse')"
								data-ng-if="item.warningStage == 'PENDING_HR_APPROVAL_FOR_ACCEPT' || item.warningStage == 'PENDING_HR_APPROVAL_FOR_REJECT'" acl-action="FSWMHRR">HR</button>
							<button class="btn btn-sm btn-info"
								data-ng-click="showWarningDetail(item.id)">View</button>
							<button class="btn"
								data-ng-click="downloadWarningLetterReport(item.id)"
								data-ng-if="item.warningStatus == 'APPROVED' || item.warningStatus == 'REJECTED'">
								<i class="fas fa-download"
									style="color: green; font-size: 20px;"></i>
							</button>

						</td>
					</tr>
				</tbody>
			</table>
			<div class="alert alert-info" data-ng-if="warningDetails.length == 0">No
				warnings found.</div>
		</div>
	</div>
</div>