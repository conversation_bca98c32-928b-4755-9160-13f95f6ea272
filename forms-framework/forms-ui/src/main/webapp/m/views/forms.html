<div data-ng-init="init()">
    <div class="container-fluid"><h3>Select audit</h3></div>
    <div class="text-center" style="margin-top: 100px" data-ng-if="loading">
        <div class="loader xl"></div>
    </div>
    <div data-ng-if="!loading">
        <div data-ng-if="forms.length==0">
            <div uib-alert class="alert alert-info">No forms found!</div>
        </div>
        <div data-ng-if="forms.length>=0" class="list">
            <div class="listItem" data-ng-repeat="form in forms | filter: (((formType == true) && {id: 5}) || (formType == false)) track by form.id"
                 data-ng-click="startAudit(form)">
                <div class="head">{{form.name}}</div>
                <div class="desc">{{form.description}}</div>
            </div>
        </div>
    </div>
</div>