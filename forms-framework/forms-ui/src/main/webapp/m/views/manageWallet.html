<div data-ng-init="init()">
    <div class="row">
        <div class="col-xs-12">
            <h1 class="text-center">Manage Wallet</h1>
            <div class="col-xs-12">
                <div class="scrollmenu">
                    <a href="" data-ng-repeat="tab in tabData" data-ng-click="selectTab(tab)"
                       data-ng-class="{'menu-highlight' : tab == selectedTab}">
                        {{tab.name}}
                    </a>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <div data-ng-show="selectedTab.name == 'Wallet Details'">
                            <div class="row">
                                <div class="col-xs-4">
                                    <div class="form-group">
                                        <label>Account Type</label>
                                        <select class="form-control" data-ng-model="selectedAccountType"
                                                data-ng-options="accountType as accountType for accountType in accountTypes | orderBy"
                                                data-ng-change="resetWalletAndAccountDetails(selectedAccountType)"></select>
                                    </div>
                                </div>
                                <div class="col-xs-4">
                                    <div class="form-group">
                                        <label>Wallet Type</label>
                                        <select data-ng-model="selectedWalletType"
                                                class="form-control"
                                                data-ng-options="walletType as walletType.name for walletType in walletTypes | orderBy"></select>
                                    </div>
                                </div>
                                <div class="col-xs-4 " data-ng-show="selectedAccountType == 'UNIT'">
                                    <div class="form-group">
                                        <label>Select Unit Category</label>
                                        <select class="form-control" data-ng-model="selectedFamily"
                                                data-ng-options="family as family for family in families"
                                                data-ng-change="getUnits(selectedFamily)">
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>Select Company</label>
                                        <select data-ng-model="selectedCompany" class="form-control" data-ng-options="company as company.name for company in companies">
                                        </select>
                                    </div>
                                </div>
                                <div class="col-xs-6" data-ng-if="selectedAccountType == 'UNIT'">
                                    <div class="form-group">
                                        <label>Select Unit</label>
                                        <select ui-select2 class="form-control" data-ng-model="selectedUnit" data-ng-options="unit as unit.name for unit in units" data-ng-change="setSelectedDetails(selectedCompany,selectedUnit,selectedEmployee)" >
                                        </select>
                                    </div>
                                </div>
                                <div class="col-xs-6" data-ng-if="selectedAccountType == 'EMPLOYEE'">
                                    <div class="form-group">
                                        <label>Select Employee</label>
                                        <select data-ui-select2 class="form-control" data-ng-model="selectedEmployee" data-placeholder="Select an Employee"
                                                data-ng-change="setSelectedDetails(selectedCompany,selectedUnit,selectedEmployee)">
                                            <option value=""></option>
                                            <option data-ng-repeat="employee in activeEmployees" value="{{employee}}">
                                                {{employee}}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!--<div class="form-group" data-ng-show="selectedCompany !=undefined && selectedCompany != null &&
                            ((selectedAccountType !=undefined && selectedAccountType!=null && selectedAccountType=='UNIT' && selectedUnit !=null && selectedUnit !=undefined) ||(selectedAccountType !=undefined && selectedAccountType!=null && selectedAccountType=='EMPLOYEEE')) ">
                                <input type="button" class="btn btn-primary" value="Get Associated Business Cost Centers" data-ng-click="getBusinessCostCenters(selectedAccountType,selectedUnit)" />
                            </div>-->
                            <!--<div class="row" data-ng-show="showBusinessCostCentreList!=null && showBusinessCostCentreList">
                                <div class="form-group">
                                    <label class="col-xs-6 ">Select Business Cost Centers</label>
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                                         options="businessCostCenters" selected-model="businessCostCenterMultiSelect" class="col-xs-4">
                                    </div>
                                </div>
                            </div>-->
                            <div class="form-group" data-ng-show ="selectedAccountType !=null && selectedWalletType !=null && ((selectedAccountType =='UNIT' && selectedUnit !=null )||(selectedAccountType == 'EMPLOYEE' && selectedEmployee!=null))">
                                <input type="button" class="btn btn-primary" value="Get Wallet Details" data-ng-click="getWalletList()" />
                            </div>

                            <div data-ng-show="walletList.length > 0">
                                <div data-ng-repeat="wallet in walletList track by wallet.id" style="margin-bottom: 10px;border: #ccc 2px solid;">
                                    <div style="background: #ddd; padding: 10px;">
                                        <div class="row">
                                            <div class="col-lg-3">
                                                <label>Account Type</label>
                                                <p>{{wallet.accountType}}</p>
                                            </div>
                                            <div class="col-lg-3">
                                                <label>Account No.</label>
                                                <p>{{wallet.accountNo}}</p>
                                            </div>
                                            <div class="col-lg-3">
                                                <label>Account Holder</label>
                                                <p>{{wallet.accountHolderName}}</p>
                                            </div>
                                            <div class="col-lg-3">
                                                <label>Account Holder Contact</label>
                                                <p>{{wallet.accountHolderContact}}</p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-3">
                                                <label>Account Holder Email</label>
                                                <p>{{wallet.accountHolderEmail}}</p>
                                            </div>
                                            <div class="col-lg-3">
                                                <label>Last updated</label>
                                                <p>{{wallet.lastUpdateTime | date : 'dd-MM-yyyy hh:mm:ss'}}</p>
                                            </div>
                                            <div class="col-lg-3">
                                                <label>Status</label>
                                                <p>{{wallet.status}}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="background: #fff; padding: 10px;">
                                        <div class="row">
                                            <div class="col-xs-3">
                                                <label>Wallet Limit</label>
                                                <p>{{wallet.openingAmount}}</p>
                                            </div>
                                            <div class="col-xs-3">
                                                <label>Available Amount</label>
                                                <p>{{wallet.currentBalance}}</p>
                                            </div>
                                            <div class="col-xs-3">
                                                <label>Issued Amount</label>
                                                <p>{{wallet.issuedAmount}}</p>
                                            </div>
                                            <div class="col-xs-3">
                                                <label>Pending approval</label>
                                                <p>{{wallet.pendingApproval}}</p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-xs-3">
                                                <label>Approved Amount</label>
                                                <p>{{wallet.approvedAmount}}</p>
                                            </div>
                                            <div class="col-xs-3">
                                                <label>Rejected Amount</label>
                                                <p>{{wallet.rejectedAmount}}</p>
                                            </div>
                                            <div class="col-xs-3">
                                                <label>Spent Amount</label>
                                                <p>{{wallet.spentAmount}}</p>
                                            </div>
                                            <div class="col-xs-3">
                                                <label>Total Amount</label>
                                                <p>{{wallet.totalAmount}}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div data-ng-show="selectedTab.name == 'Create Wallet'">
                            <div class="row">
                                <div class="col-xs-4">
                                    <div class="form-group">
                                        <label>Account Type</label>
                                        <select class="form-control" data-ng-model="selectedAccountType"
                                                data-ng-options="accountType as accountType for accountType in accountTypes | orderBy" data-ng-change="resetWalletAndAccountDetails(selectedAccountType)"></select>
                                    </div>
                                </div>
                                <div class="col-xs-4">
                                    <div class="form-group">
                                        <label>Wallet Type</label>
                                        <select data-ng-model="selectedWalletType"
                                                class="form-control" data-ng-options="walletType as walletType.name for walletType in walletTypes | orderBy"></select>
                                    </div>
                                </div>
                                <div class="col-xs-4 " data-ng-show="selectedAccountType == 'UNIT'">
                                    <div class="form-group">
                                        <label>Select Unit Category</label>
                                        <select class="form-control" data-ng-model="selectedFamily" data-ng-options="family as family for family in families"
                                                data-ng-change="getUnits(selectedFamily)">
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label>Select Company</label>
                                        <select data-ng-model="selectedCompany" class="form-control" data-ng-options="company as company.name for company in companies">
                                        </select>
                                    </div>
                                </div>
                                <div class="col-xs-6" data-ng-if="selectedAccountType == 'UNIT'">
                                    <div class="form-group">
                                        <label>Select Unit</label>
                                        <select ui-select2 class="form-control" data-ng-model="selectedUnit" data-ng-options="unit as unit.name for unit in units" data-ng-change="setSelectedDetails(selectedCompany,selectedUnit,selectedEmployee)" >
                                        </select>
                                    </div>
                                </div>
                                <div class="col-xs-6" data-ng-if="selectedAccountType == 'EMPLOYEE'">
                                    <div class="form-group">
                                        <label>Select Employee</label>
                                        <select data-ui-select2 class="form-control" data-ng-model="selectedEmployee" data-placeholder="Select an Employee"
                                                data-ng-change="setSelectedDetails(selectedCompany,selectedUnit,selectedEmployee)">
                                            <option value=""></option>
                                            <option data-ng-repeat="employee in activeEmployees" value="{{employee}}">
                                                {{employee}}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group" data-ng-show="selectedAccountType !=null && selectedWalletType !=null && ((selectedAccountType =='UNIT' && selectedUnit !=null )||(selectedAccountType == 'EMPLOYEE' && selectedEmployee!=null))">
                                <input type="button" class="btn btn-primary" value="Get Wallet Details" data-ng-click="getWalletDetail()" />
                            </div>
                            <!--<div class="form-group" data-ng-show="showBusinessCostCenterBtn !=null && showBusinessCostCenterBtn ">
                                <input type="button" class="btn btn-primary" value="Get Associated Business Cost Centers" data-ng-click="getBusinessCostCenters(selectedAccountType,selectedUnit)" />
                            </div>-->
                            <div class="row" data-ng-show="selectedWalletType !=null && selectedAccountType !=null && (selectedUnit !=null || selectedEmployee !=null) && walletData==null && showBusinessCostCenterBtn">
                                <div class="form-group">
                                    <label class="col-xs-6 ">Select Business Cost Centers</label>
                                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                                         options="businessCostCentersNames" selected-model="businessCostCenterMultiSelect" class="col-xs-4">
                                    </div>
                                </div>
                            </div>
                            <div >
                                <div class="form-group" data-ng-show="selectedWalletType !=null && selectedAccountType !=null && (selectedUnit !=null || selectedEmployee !=null) && walletData==null && showBusinessCostCenterBtn">
                                    <label >Opening Amount :</label>
                                    <input class="form-control" type="text" data-ng-model="walletRequest.openingAmount"  data-ng-show="selectedAccountType !=null && selectedAccountType=='UNIT'" disabled/>
                                    <input  class="form-control" type="text" data-ng-model="walletRequest.openingAmount"  placeholder="Enter value here " data-ng-show="selectedAccountType !=null && selectedAccountType !='UNIT'" data-ng-change="setOpeningBalance()" >
                                </div>
                            </div>
                        </div>
                        <div data-ng-if="walletData != null"  data-ng-include="'views/walletView.html'"></div>
                        <div data-ng-if="showCreateWallet">
                            <div class="form-group">
                                <label>Account No:</label>
                                <input type="text" data-ng-model="walletRequest.accountNo" class="form-control" disabled />
                            </div>
                            <div class="form-group">
                                <label>Account type:</label>
                                <input type="text" data-ng-model="walletRequest.accountType" class="form-control" disabled />
                            </div>
                            <div class="col-xs-4" data-ng-if="selectedAccountType=='EMPLOYEE'">
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" data-ng-model="walletRequest.canAllocateCostToCafes" data-ng-change="setCafeCostAllocationFlag(walletRequest.canAllocateCostToCafes)"> Can allocate cst to all cafes
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Create Wallet" data-ng-click="createWallet()" />
                            </div>
                        </div>
                    </div>
                    <div data-ng-show="selectedTab.name == 'Update Wallet'">
                        <div class="row">
                            <div class="col-xs-4">
                                <div class="form-group">
                                    <label>Account Type</label>
                                    <select class="form-control" data-ng-model="selectedAccountType"
                                            data-ng-options="accountType as accountType for accountType in accountTypes | orderBy" data-ng-change="resetWalletAndAccountDetails(selectedAccountType)"></select>
                                </div>
                            </div>
                            <div class="col-xs-4">
                                <div class="form-group">
                                    <label>Wallet Type</label>
                                    <select data-ng-model="selectedWalletType"
                                            class="form-control" data-ng-options="walletType as walletType.name for walletType in walletTypes | orderBy"></select>
                                </div>
                            </div>
                            <div class="col-xs-4 " data-ng-show="selectedAccountType == 'UNIT'">
                                <div class="form-group">
                                    <label>Select Unit Category</label>
                                    <select class="form-control" data-ng-model="selectedFamily" data-ng-options="family as family for family in families"
                                            data-ng-change="getUnits(selectedFamily)">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xs-6">
                                <div class="form-group">
                                    <label>Select Company</label>
                                    <select data-ng-model="selectedCompany" class="form-control" data-ng-options="company as company.name for company in companies">
                                    </select>
                                </div>
                            </div>
                            <div class="col-xs-6" data-ng-if="selectedAccountType == 'UNIT'">
                                <div class="form-group">
                                    <label>Select Unit</label>
                                    <select ui-select2 class="form-control" data-ng-model="selectedUnit" data-ng-options="unit as unit.name for unit in units" data-ng-change="setSelectedDetails(selectedCompany,selectedUnit,selectedEmployee)" >
                                    </select>
                                </div>
                            </div>
                            <div class="col-xs-6" data-ng-if="selectedAccountType == 'EMPLOYEE'">
                                <div class="form-group">
                                    <label>Select Employee</label>
                                    <select data-ui-select2 class="form-control" data-ng-model="selectedEmployee" data-placeholder="Select an Employee"
                                            data-ng-change="setSelectedDetails(selectedCompany,selectedUnit,selectedEmployee)">
                                        <option value=""></option>
                                        <option data-ng-repeat="employee in activeEmployees" value="{{employee}}">
                                            {{employee}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group" data-ng-show="selectedAccountType !=null && selectedWalletType !=null && ((selectedAccountType =='UNIT' && selectedUnit !=null )||(selectedAccountType == 'EMPLOYEE' && selectedEmployee!=null))">
                            <input type="button" class="btn btn-primary" value="Get Wallet Details" data-ng-click="getWalletDetail()" />
                        </div>
                        <table class="table table-bordered table-striped" data-ng-show="walletData != null">
                            <tr>
                                <th>Id</th>
                                <th>Business Cost Center Name</th>
                                <th>Business Cost Center Code</th>
                                <th>Business Cost Center Type</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                            <tr data-ng-repeat="bccMapping in filtered = (allWalletBccMappings | filter:search | orderBy : predicate :reverse) track by bccMapping.walletBccMappingId">
                                <td style="font-size: 12px">{{bccMapping.walletBccMappingId}}</td>
                                <td style="font-size: 12px">{{bccMapping.bccName}}</td>
                                <td style="font-size: 12px">{{bccMapping.bccCode}}</td>
                                <td style="font-size: 12px">{{bccMapping.bccType}}</td>
                                <td style="font-size: 12px">{{bccMapping.mappingStatus}}</td>
                                <td style="font-size: 12px">
                                    <button class="btn btn-success" data-ng-if="bccMapping.mappingStatus == 'IN_ACTIVE'"
                                            data-ng-click="updateWalletBusinessCostCenterMapping(bccMapping, true)">Activate
                                    </button>
                                    <button class="btn btn-danger" data-ng-if="bccMapping.mappingStatus == 'ACTIVE'"
                                            data-ng-click="updateWalletBusinessCostCenterMapping(bccMapping, false)">
                                        De-activate
                                    </button>
                                </td>
                            </tr>
                        </table>
                        <div data-ng-show="walletData != null" style="margin-bottom: 10px;border: #ccc 2px solid;">
                            <div style="background: #ddd; padding: 10px;">
                                <div class="row">
                                    <div class="col-lg-3">
                                        <label>Account Type</label>
                                        <p>{{walletData.accountType}}</p>
                                    </div>
                                    <div class="col-lg-3">
                                        <label>Account No.</label>
                                        <p>{{walletData.accountNo}}</p>
                                    </div>
                                    <div class="col-lg-3">
                                        <label>Account Holder</label>
                                        <p>{{walletData.accountHolderName}}</p>
                                    </div>
                                    <div class="col-lg-3">
                                        <label>Account Holder Contact</label>
                                        <p>{{walletData.accountHolderContact}}</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-3">
                                        <label>Account Holder Email</label>
                                        <p>{{walletData.accountHolderEmail}}</p>
                                    </div>
                                    <div class="col-lg-3">
                                        <label>Last updated</label>
                                        <p>{{walletData.lastUpdateTime | date : 'dd-MM-yyyy hh:mm:ss'}}</p>
                                    </div>
                                    <div class="col-lg-3">
                                        <label>Status</label>
                                        <p>{{walletData.status}}</p>
                                    </div>
                                </div>
                            </div>
                            <div style="background: #fff; padding: 10px;">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <label>Wallet Limit</label>
                                        <p>{{walletData.openingAmount}}</p>
                                    </div>
                                    <div class="col-xs-3">
                                        <label>Available Amount</label>
                                        <p>{{walletData.currentBalance}}</p>
                                    </div>
                                    <div class="col-xs-3">
                                        <label>Issued Amount</label>
                                        <p>{{walletData.issuedAmount}}</p>
                                    </div>
                                    <div class="col-xs-3">
                                        <label>Pending approval</label>
                                        <p>{{walletData.pendingApproval}}</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-3">
                                        <label>Approved Amount</label>
                                        <p>{{walletData.approvedAmount}}</p>
                                    </div>
                                    <div class="col-xs-3">
                                        <label>Rejected Amount</label>
                                        <p>{{walletData.rejectedAmount}}</p>
                                    </div>
                                    <div class="col-xs-3">
                                        <label>Spent Amount</label>
                                        <p>{{walletData.spentAmount}}</p>
                                    </div>
                                    <div class="col-xs-3">
                                        <label>Total Amount</label>
                                        <p>{{walletData.totalAmount}}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div data-ng-show="walletData != null">
                            <div class="form-group">
                                <label>Select Action</label>
                                <select data-ng-model="walletUpdateType" class="form-control">
                                    <option value=""></option>
                                    <option value="UPGRADE">Upgrade</option>
                                    <option value="DOWNGRADE">Downgrade</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Amount</label>
                                <input type="number" class="form-control" data-ng-model="walletUpdateAmount" />
                            </div>
                            <div class="form-group">
                                <label>Transaction Id</label>
                                <input type="text" class="form-control" data-ng-model="walletUpdateTransactionId" />
                            </div>
                            <div class="form-group">
                                <input type="button" class="btn btn-primary" value="Update Wallet" data-ng-click="updateWallet()" />
                                <input type="button" class="btn btn-primary" value="Edit BCC Mappings" data-ng-click="editBCCMappings()" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--
</div>-->
<!--    modal 2 for wallet bcc mapping-->
<div
        class="modal fade"
        id="editWalletBccMappingModal"
        tabindex="-1"
        role="dialog"
        aria-labelledby="myModalLabel">
    <div
            class="modal-dialog modal-lg"
            role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"
                    id="myModalLabel">{{action}} Wallet BusinessCostCenter Mappings
                </h4>
            </div>
            <h3 style="position:relative;left:20px">Select Business Cost Centers</h3>
            <div class="row">
                <div
                        style="position:relative;left:20px"
                        class="col-xs-10">
                    <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                         options="businessCostCenters"
                         selected-model="storeSelectedBCCMappings" class="region-card">
                    </div>
                </div>
                <div class="col-xs-2">
                    <button class="btn btn-success" ng-click="addWalletBccMapping()">
                        <i class="fa fa-plus fw"></i>
                    </button>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <ul>
                        <li ng-repeat="mapping in activeWalletBccMappings track by mapping.walletBccMappingId">
                            {{mapping.bccName}}
                            <button
                                    class="btn btn-danger btn-xs"
                                    ng-click="removeWalletBccMapping(mapping.walletBccMappingId)">
                                <i class="fa fa-close fw"></i>
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
            <div
                    style="position:relative;left:20px"
                    class="form-group">
                <button class="btn btn-primary"
                        type="button"
                        ng-click="saveUpdateWalletBccMapping(activeWalletBccMappings)">SAVE</button>
            </div>

        </div>
    </div>
</div>
<script type="text/ng-template" id="updateWalletBCCMappings.html">
    <div class="modal-content">
        <div class="modal-header" data-ng-init="init()">
            <h3 class="modal-title"><span style="text-transform: capitalize;">{{action}}</span> Wallet BusinessCostCenter Mappings</h3>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <h5 style="position:relative;left:20px">Select Business Cost Centers</h5>
        <div class="modal-body">
            <div class="row">
                <div class="row">
                    <div style="position:relative;left:20px" class="col-xs-10">
                        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
                             options="businessCostCentersNames"
                             selected-model="businessCostCenterMultiSelect" class="region-card">
                        </div>
                    </div>
                    <div class="col-xs-2">
                        <button class="btn btn-success" ng-click="addWalletBccMapping()">
                            <i class="fa fa-plus fw"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <ul>
                        <li ng-repeat="mapping in activeWalletBccMappings track by $index" data-ng-show="mapping.mappingStatus=='ACTIVE'">
                            {{mapping.bccName}}
                            <button class="btn btn-danger btn-xs" ng-click="removeWalletBccMapping(mapping)">
                                <i class="fa fa-close fw"></i>
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-danger" type="button" ng-click="cancel()">Close</button>
            <button class="btn btn-default" type="button" ng-click="saveUpdateWalletBccMapping(activeWalletBccMappings)">SUBMIT</button>
        </div>
    </div>
</script>

