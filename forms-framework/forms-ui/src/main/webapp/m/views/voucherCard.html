<style>
.reimbursed {
	background: #d8d31073;
}

.not-reimbursed {
	background: #f5f5f5;
}
</style>

<div class="panel-heading" style="font-size: 15px;"
	data-ng-class="{'not-reimbursed' : voucher.isReimbursed != true , 
										'reimbursed' : voucher.isReimbursed == true}">
	<div class="row row-spacing">
		<div class="col-xs-12">
			Voucher Id : <label>{{voucher.generatedVoucherId}}</label>
			<span style="display: inline-block;margin: 0 5px;" data-ng-repeat="img in voucher.fileDetails">
                <i data-ng-click="downloadInvoice(img.id,img.fileName, 'download')">Download {{img.fileType.toLowerCase()}}</i>
            </span>
		</div>
	</div>
	<div class="row row-spacing" data-ng-if="actionDetail.type == 'settle'">
		<div class="col-xs-6">
			Amount : {{voucher.issuedAmount}}<span
				data-ng-if="voucher.expenseAmount != null"
				data-ng-class="{'text-success' : voucher.expenseAmount <= voucher.issuedAmount , 
										'text-danger' : voucher.expenseAmount > voucher.issuedAmount}">({{voucher.expenseAmount}})</span>
		</div>
		<div class="col-xs-6">Date : {{voucher.businessDate}}</div>
	</div>
	<div class="row row-spacing" data-ng-if="actionDetail.type != 'settle'">
		<div class="col-xs-6">
			Amount : <span
				data-ng-class="{'text-success' : voucher.expenseAmount <= voucher.issuedAmount , 
										'text-danger' : voucher.expenseAmount > voucher.issuedAmount}">{{voucher.expenseAmount}}</span>
		</div>
		<div class="col-xs-6">Date : {{voucher.businessDate}}</div>
	</div>
	<div class="row row-spacing">
		<div class="col-xs-12">
			Status : {{voucher.currentStatus}}<span
				data-ng-if="voucher.isReimbursed == true">(Reimbursed)</span>
		</div>
	</div>
	<div class="row row-spacing"
		data-ng-if="stateDetails.actionBy == 'AM' || stateDetails.actionBy == 'FM'">
		<div class="col-xs-12">A/c Detail : {{voucher.wallet.name}}-{{voucher.wallet.code}}</div>
	</div>
</div>
