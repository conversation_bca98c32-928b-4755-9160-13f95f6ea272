<style>
    body, html {
        overflow-x: hidden;
    }
</style>
<div data-ng-init="init()" class="wallet-container">
	<div class="row">
		<div class="col-xs-12">
			<h2 class="text-center page-heading">
				Voucher - <span data-ng-if="stateDetails.actionBy == 'AM'">Area
					Manager</span> <span data-ng-if="stateDetails.actionBy == 'FM'">Finance</span>
			</h2>
		</div>
	</div>
	<div class="row-spacing">
		<div class="row">
			<div class="col-xs-6">
				<div class="form-group">
					<label>Account Type</label>
					<select class="form-control" data-ng-model="selectedAccountType"
							data-ng-options="accountType as accountType for accountType in accountTypes | orderBy"
							data-ng-change="getWalletsByAccountType(selectedAccountType)" required></select>
				</div>
			</div>
			<div class="col-xs-6">
				<div class="form-group">
					<label class="col-xs-6">Select Accounts</label>
					<div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"
						 options="walletsNamesByAccType" selected-model="walletsByAccTypeMultiSelect">
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-12">
				<!-- <div class="col-xs-12">
                    <div class="form-group">
                        <label for="voucherAccountNo" class="control-label">Select
                            Unit Account </label> <select class="form-control" id="voucherAccountNo"
                            name="voucherAccountNo" data-ng-model="searchVoucher.accountNo"
                            data-ng-options="unit.id as unit.name for unit in units"
                            required="required">
                        </select>
                    </div>
                </div> -->
				<!-- <div class="col-xs-3">
                     <div class="form-group">
                         <label class="control-label">Select Unit Account </label>
                         <div ng-dropdown-multiselect="" options="units"
                              selected-model="unitMultiSelect"
                              extra-settings="multiSelectSettings"></div>
                     </div>
                 </div>-->
				<div class="col-xs-3 form-group">
					<label class="control-label" for="searchVoucherStatus">Voucher Status</label>
					<select data-ng-model="searchVoucher.status" class='form-control' id="searchVoucherStatus"
							name="searchVoucherStatus" data-ng-options="status for status in statusList">
					</select>
				</div>
				<div class="col-xs-3 form-group">
					<label for="voucherStartDate" class="control-label">Start
						Date </label>
					<p class="input-group">
						<input type="text" class="form-control" id="voucherStartDate"
							   uib-datepicker-popup="yyyy-MM-dd"
							   data-ng-model="searchVoucher.startDate"
							   is-open="searchVoucher.startDate.opened"
							   datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
							   data-ng-required="false" close-text="Close"/> <span
							class="input-group-btn">
						<button type="button" class="btn btn-default"
								data-ng-click="searchVoucher.startDate.opened=true">
							<i class="glyphicon glyphicon-calendar"></i>
						</button>
					</span>
					</p>
				</div>
				<div class="col-xs-3 form-group">
					<label for="voucherEndDate" class="control-label">End date </label>
					<p class="input-group">
						<input type="text" class="form-control" id="voucherEndDate"
							   uib-datepicker-popup="yyyy-MM-dd" ng-model="searchVoucher.endDate"
							   is-open="searchVoucher.endDate.opened"
							   datepicker-options="dateOptions" placeholder="yyyy-MM-dd"
							   ng-required="false" close-text="Close"/> <span
							class="input-group-btn">
						<button type="button" class="btn btn-default"
								data-ng-click="searchVoucher.endDate.opened=true">
							<i class="glyphicon glyphicon-calendar"></i>
						</button>
					</span>
					</p>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-12 form-group"
				 data-ng-if="searchVoucher.actionType != 'view'">
				<div class="col-xs-4">
					<button class="btn btn-danger" data-ng-click="resetSearchDetails()">Reset</button>
				</div>
				<div class="col-xs-4">
				</div>
				<div class="col-xs-4">
					<button class="btn btn-primary pull-right"
							data-ng-click="getVoucherDetails(true)">Search
					</button>
				</div>
			</div>
			<div class="col-xs-4 col-xs-offset-8"
				 data-ng-if="searchVoucher.actionType == 'view'">
				<button class="btn  btn-info "
						data-ng-click="searchAction(null, 'back')">Back
				</button>
			</div>
		</div>
	</div>
	<div data-ng-if="activeEntity == 'voucher'">
		<div class="row-spacing"
			 data-ng-if="searchVoucher.actionType != 'view'">
			<div class="col-xs-12">
				<div data-ng-repeat="voucher in voucherList">
					<div class="panel panel-default"
						 data-ng-if="voucher.currentStatus != 'PENDING_SETTLEMENT' && voucher.currentStatus != 'CANCELLED'"
						 style="border: 5px solid; margin-bottom: 5px;" id="voucher_{{voucher.id}}">
						<!-- View of Voucher Card -->
						<div data-ng-include="'views/voucherCard.html'"></div>
						<div class="panel-body">
							<div class="row">
								<div
										data-ng-if="selectedVoucher == null || selectedVoucher.id != voucher.id">
									<div
											data-ng-if="(voucher.currentStatus == 'AM_PENDING' && stateDetails.actionBy == 'AM') ||
									(voucher.currentStatus == 'FINANCE_PENDING' && stateDetails.actionBy == 'FM')">
										<div class="col-xs-4">
											<button class="btn  btn-warning"
													data-ng-click="searchAction(voucher,'reject', 'Reject', 'action')">Reject
											</button>
										</div>
										<div class="col-xs-4">
											<button class="btn btn-success "
													data-ng-click="searchAction(voucher,'approve', 'Approve', 'action')">Approve
											</button>
										</div>
									</div>
									<div class="col-xs-4">
										<button class="btn  btn-info "
												data-ng-click="searchAction(voucher, 'view', 'View')">View
										</button>
									</div>
								</div>
								<div class="col-xs-4 col-xs-offset-8"
									 data-ng-if="selectedVoucher != null && selectedVoucher.id == voucher.id">
									<button class="btn  btn-info "
											data-ng-click="searchAction(null, 'back', 'Back')">Back
									</button>
								</div>
							</div>
						</div>
						<div class="row"
							 data-ng-if="(action.actionType == 'reject' || action.actionType == 'approve') && selectedVoucher.id == voucher.id">
							<div data-ng-include="'views/approveVoucher.html'"></div>
							<!--<div class="col-xs-12">
                                <div class="row" data-ng-if="(searchVoucher.actionType == 'reject')">
                                    <div class="col-xs-12">
                                        <div style="padding: 5px 10px;" data-ng-repeat="reason in rejectionReasons" data-ng-if="reason.validationType == 'REJECTION'">
                                            <input id="{{reason.id}}" type="checkbox" data-ng-model="reason.checked" />
                                            <label for="{{reason.id}}">{{reason.validationName}}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="background: #ddd; margin: 10px;" data-ng-if="(searchVoucher.actionType == 'approve')">
                                    <div class="col-xs-12">
                                        <h3>Please check following errors before approval.</h3>
                                        <div style="padding: 5px 10px;" data-ng-repeat="reason in rejectionReasons">
                                            {{reason.validationName}}
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-12 form-group">
                                        <label for="comment" class="control-label">{{searchVoucher.actionLabel}}
                                            Comment </label>
                                        <textarea style="width: 100%;" rows="5" name="comment"
                                                  id="comment" data-ng-model="voucherDetail.actionComment"
                                                  required="required"> </textarea>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-4 " style="margin-bottom: 5px;">
                                        <button class="btn " data-ng-click="updateVoucher()"
                                                data-ng-class="{'btn-danger' : searchVoucher.actionType ==  'approve',
                                        'btn-success' : searchVoucher.actionType == 'reject'}">
                                            {{searchVoucher.actionLabel}}
                                            Voucher
                                        </button>
                                    </div>
                                </div>
                            </div>-->
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- View of Voucher  -->
		<div data-ng-if="searchVoucher.actionType == 'view'"
			 data-ng-include="'views/voucherView.html'"></div>
	</div>
</div>