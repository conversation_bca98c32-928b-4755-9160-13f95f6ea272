/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function() {
    'use strict';

    angular.module('formsApp').factory('StorageUtil', StorageUtil);

    StorageUtil.$inject = [];

    function StorageUtil() {

        var service = {};

        service.setLocalStoreItem = setLocalStoreItem;
        service.getLocalStoreItem = getLocalStoreItem;
        service.removeLocalStoreItem = removeLocalStoreItem;
        service.setCookie = setCookie;
        service.getCookie = getCookie;
        service.eraseCookie = eraseCookie;
        service.previousLocation = ['/login'];
        service.resetCustomerSocket = resetCustomerSocket;
        service.getUserMeta = getUserMeta;
        service.setUserMeta = setUserMeta;
        service.userMeta = {};
        service.getAclData = getAclData;
        service.setAclData = setAclData;

        function setLocalStoreItem(key, data){
            if(window.localStorage && data != null){
                try{
                    localStorage.setItem(key, JSON.stringify(data));
                }catch(e){}
            }
        }

        function getLocalStoreItem(key){
            if (window.localStorage) {
                try{
                    var data = localStorage.getItem(key);
                    if(data != null){
                        return JSON.parse(data)
                    }else{
                        return null;
                    }
                }catch(e){}
            }
        }

        function removeLocalStoreItem(key){
            if (window.localStorage && this.getItem(key)!=null) {
                try{
                    localStorage.removeItem(key);
                }catch(e){}
            }
        }

        function setCookie(name, value, days) {
            var expires = "";
            if (days) {
                var date = new Date();
                date.setTime(date.getTime() + (days*24*60*60*1000));
                expires = "; expires=" + date.toUTCString();
            }
            document.cookie = name + "=" + value + expires + "; path=/";
        }

        function getCookie(cname) {
            var name = cname + "=";
            var decodedCookie = decodeURIComponent(document.cookie);
            var ca = decodedCookie.split(';');
            for (var i = 0; i < ca.length; i++) {
                var c = ca[i];
                while (c.charAt(0) == ' ') {
                    c = c.substring(1);
                }
                if (c.indexOf(name) == 0) {
                    return c.substring(name.length, c.length);
                }
            }
            return null;
        }

        function eraseCookie(name) {
            this.setCookie(name, "", -1);
        }

        function resetCustomerSocket() {
            return {
                id: null,
                name: null,
                contact: null,
                email: null,
                loyalityPoints: 0,
                contactVerified: false,
                emailVerified: false,
                unitId: service.unitDetails.id,
                newCustomer: false,
                otp: null,
                chaiRedeemed: 0,
                productId: 10,
                otpVerified: false
            };
        };

        function setUserMeta(data) {
            service.userMeta = data;
            //service.setLocalStoreItem("ud",data);
        }

        function getUserMeta(data) {
            /*var meta = service.getLocalStoreItem("ud",data);
            return meta || {};*/
            return service.userMeta;
        }

        function getAclData() {
            return service.aclData;
        }

        function setAclData(data) {
            service.aclData = data;
        }

        return service;

    }

})();