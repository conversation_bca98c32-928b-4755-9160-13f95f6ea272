(function() {
    'use strict';

    angular.module('formsApp').factory('AppUtil', AppUtil);

    AppUtil.$inject = ['$uibModal', 'toastr', '$http', '$rootScope', 'APIJson'];

    function AppUtil( $uibModal, toastr, $http, $rootScope, APIJson) {

        var service = {};

        service.previousLocation = ['/login'];
        service.auditForm = null;
        service.getAuditForm = getAuditForm;
        service.setAuditForm = setAuditForm;
        service.formatDate = formatDate;
        service.JSONToCSVConvertor = JSONToCSVConvertor;
        service.getIdCodeName = getIdCodeName;
        service.addDate = addDate;
        service.expensesList = [];
        service.getExpenseType = getExpenseType;
        
        service.isEmptyObject = function (obj) {
            if (obj != undefined && obj != null) {
                if (typeof obj == 'string' || typeof obj == 'number')
                    return obj.toString().length == 0;
                else
                    return Object.keys(obj).length == 0;
            }
            return true;
        };
        
        service.getDate = function(days){
        	var time = new Date();
        	if(days !== undefined){
        		time.setDate(time.getDate() + days);
        	}
            return time;
        };
        
        service.getDaysDifference = function(fromDate, toDate){
        	var timeDiff = Math.abs(toDate.getTime() - fromDate.getTime());
        	return Math.ceil(timeDiff / (1000 * 3600 * 24));
        };
        
        service.confirmModal = function(title, message){
        	var result = $uibModal.open({
    			templateUrl: 'views/confirm-modal.html',
    			controller: function($scope){
    				$scope.data = {};
    				$scope.data.title= title;
    				$scope.data.message = message;
    			}
    		}).result;
        	return result;
        };

        function getAuditForm() {
            return service.auditForm;
        }

        function setAuditForm(form) {
            service.auditForm = form;
        }

        function formatDate(date, format){
            var time = new Date(date);
            var yyyy = time.getFullYear();
            var M = time.getMonth()+1;
            var d = time.getDate();
            var MM = M;
            var dd = d;
            var hh = time.getHours();
            var mm = time.getMinutes();
            var ss = time.getSeconds();
            if(M<10){
                MM = "0"+M;
            }
            if(d<10){
                dd = "0"+d;
            }
            if(format=="yyyy-MM-dd"){
                return yyyy+"-"+MM+"-"+dd;
            }
            if(format=="dd/MM/yyyy"){
                return dd+"/"+MM+"/"+yyyy;
            }
            if(format=="dd-MM-yyyy-hh-mm-ss"){
                return dd+"-"+MM+"-"+yyyy+"-"+hh+"-"+mm+"-"+ss;
            }
            if(format=="dd-MM-yyyy"){
                return dd+"-"+MM+"-"+yyyy;
            }
        }

        function covertToSentenceCase(text){
            var result = text.replace( /([A-Z])/g, " $1" );
            return result.charAt(0).toUpperCase() + result.slice(1);
        }

        function getIdCodeName(dataId, name, code){
            var idCodeName ={
                id : dataId,
                code : (code === undefined ? null : code),
                name : (name === undefined ? null : name)
            };
            return idCodeName;
        }

        function JSONToCSVConvertor(JSONData, ReportTitle, ShowLabel) {
            // If JSONData is not an object then JSON.parse will parse the JSON
            // string in an Object
            var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;
            var CSV = '';
            // This condition will generate the Label/Header
            if (ShowLabel) {
                var row = "";

                // This loop will extract the label from 1st index of on array
                for ( var index in arrData[0]) {
                    // Now convert each value to string and comma-seprated
                    row += covertToSentenceCase(index) + ',';
                }
                row = row.slice(0, -1);
                // append Label row with line break
                CSV += row + '\r\n';
            }

            // 1st loop is to extract each row
            for (var i = 0; i < arrData.length; i++) {
                var row = "";
                // 2nd loop will extract each column and convert it in string
                // comma-seprated
                for ( var index in arrData[i]) {
                    row += '"' + arrData[i][index] + '",';
                }
                row.slice(0, row.length - 1);
                // add a line break after each row
                CSV += row + '\r\n';
            }

            if (CSV == '') {
                alert("Invalid data");
                return;
            }

            // this trick will generate a temp "a" tag
            var link = document.createElement("a");
            link.id = "lnkDwnldLnk";

            // this part will append the anchor tag and remove it after automatic
            // click
            document.body.appendChild(link);

            var csv = CSV;
            var blob = new Blob([ csv ], {
                type : 'text/csv'
            });
            var csvUrl = window.webkitURL.createObjectURL(blob);
            var filename = ReportTitle + '.csv';
            $("#lnkDwnldLnk").attr({
                'download' : filename,
                'href' : csvUrl
            });

            $('#lnkDwnldLnk')[0].click();
            document.body.removeChild(link);
        }

        function addDate(date, days){
            var time = new Date(date);
            if(days !== undefined){
                time.setDate(time.getDate() + days);
            }
            return time;
        };

        function getExpenseType(expenseCategory, callback) {
            if(service.expensesList != null && service.expensesList.length > 0) {
                callback(service.expensesList);
            } else {
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.budgetManagement.getExpenseHeader,
                    data: expenseCategory.name
                }).then(function success(response) {
                    if (response.data != null && Array.isArray(response.data)) {
                        service.expensesList = response.data;
                        callback(service.expensesList);
                    } else {
                        toastr.error("Error while getting expense header.");
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error("Error while getting expense header.");
                    $rootScope.rootLoading = false;
                });
            }
        };

        return service;
    }

})();