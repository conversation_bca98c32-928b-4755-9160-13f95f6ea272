'use strict';
angular.module('formsApp')
    .controller('manageVoucherCtrl', ['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', '$http', 'AppUtil', 'MetadataService', 'toastr', '$uibModal', '$stateParams', 'StorageUtil', '$state', '$anchorScroll', 'WalletService',
        function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, AppUtil, MetadataService, toastr, $uibModal, $stateParams, StorageUtil, $state, $anchorScroll, WalletService) {
            $scope.init = function () {
                $scope.expenseCategory = {
                    name: "PETTY_CASH",
                    label: "PETTY CASH"
                };
                $scope.resetSearchDetails();

                $scope.dateOptions = {
                    dateDisabled: function () {
                        return false;
                    },
                    formatYear: 'yyyy',
                    maxDate: new Date(),
                    startingDay: 1
                };
                $scope.voucherList = [];
                $scope.changeByPass = false;
                $scope.stateDetails = {};
                $scope.stateDetails.actionBy = $stateParams.actionBy;
                $scope.userDetails = StorageUtil.getUserMeta();
                // $scope.multiSelectSettings = {showEnableSearchButton: false, template: '<b>{{option.name}}</b>'};
                $scope.accountTypes=["UNIT","EMPLOYEE"];
                $scope.selectedAccountType=null;
                $scope.walletsByAccTypeMultiSelect=[];

                $scope.allVoucherExpenseType = [];
                $scope.selectedAllVoucherExpenseType = [];
                $scope.UpdatedVoucherListAfterExpenseType = [];
                $scope.finalListOfExpenseTypes = {};
                $scope.showExpenseTypes = false;

                $scope.selectedWalletByAccType  = {} ;
                $scope.units = [];
                getUnits();
                initSearchParams();
                $scope.statusList = [];
                if ($scope.stateDetails.actionBy == 'FM') {
                    $scope.statusList = ["FINANCE_PENDING", "PENDING_REJECTION_FINANCE", "REJECTED", "APPROVED", "SETTLED"];
                } else if ($scope.stateDetails.actionBy == 'AM') {
                    $scope.statusList = WalletService.getStatusList();
                }
            };

            function initSearchParams() {
                $scope.searchVoucher.startDate = AppUtil.getDate(-7);
                $scope.searchVoucher.endDate = AppUtil.getDate();
                $scope.action = {actionType: null};
            }

            function getUnits() {
                if ($scope.stateDetails.actionBy == 'FM') {
                    MetadataService.getUnitList(function (unitList) {
                        multiSelectUnitModal(unitList);
                    }, function (error) {
                        toastr.error(error);
                    });
                } else if ($scope.stateDetails.actionBy == 'AM') {
                    multiSelectUnitModal(MetadataService.getUserUnits());
                    /*MetadataService.getAreaManagerUnits(StorageUtil.getUserMeta().id).then(function (unitList) {
                        multiSelectUnitModal(unitList);
                    }, function (error) {
                        toastr.error(error);
                    });*/
                }

            }

            function multiSelectUnitModal(unitsList) {
                for (var i = 0; i < unitsList.length; i++) {
                    $scope.units.push({name: unitsList[i].name, id: unitsList[i].id});
                }
            }

            $scope.getWalletsByAccountType = function (selectedAccountType){
                if(!AppUtil.isEmptyObject($scope.selectedAccountType)){
                    $rootScope.rootLoading = true;
                    if ($scope.stateDetails.actionBy == 'AM') {
                       $scope.changeByPass= false;
                       }
                    if ($scope.stateDetails.actionBy == 'FM') {
                        $scope.changeByPass = true;
                       }
                    $http({
                        method: 'GET',
                        url: APIJson.urls.walletManagement.getWalletsByAccountType,
                        params: {
                            walletAccountType :$scope.selectedAccountType,
                            employeeCode :$scope.userDetails.id,
                            byPass : $scope.changeByPass,
                        }
                    }).then(function success(response) {
                        if (response.data != null) {
                            $scope.walletsByAccType = response.data;
                            $scope.walletsNamesByAccType= $scope.walletsByAccType.map(function(wallet){
                                return wallet.associatedId+'_'+wallet.accountHolderName+'_'+wallet.walletType;
                            });
                            // console.log('Wallets by Account Type ::', $scope.walletsNamesByAccType);
                        } else {
                            toastr.error("Error while getting accounts for this accountType.");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error(response.errorMsg);
                        $rootScope.rootLoading = false;
                    });
                }
            }

            $scope.searchAction = function (voucher, actionType, actionLabel, actionEffect) {
                if(actionEffect == undefined) {
                    actionEffect = "view"
                }
                updateEntity('voucher');
                if(actionEffect == "view") {
                    $scope.showExpenseTypes = false;
                    $scope.searchVoucher.actionType = actionType;
                } else {
                    $scope.action.actionType = actionType;
                }
                $scope.searchVoucher.actionLabel = actionLabel;
                $scope.voucherDetail = {};
                $scope.selectedVoucher = voucher;
                if (actionType == 'approve' || actionType == 'reject') {
                    $scope.voucherDetail = angular.copy(voucher);
                    $scope.voucherDetail.issuedBy = AppUtil.getIdCodeName(StorageUtil.getUserMeta().id);
                    $scope.voucherDetail.entity = 'CAFE';
                    AppUtil.getExpenseType($scope.expenseCategory, function (expenses) {
                        expenses.map(function (expense) {
                            if(expense.desc == $scope.voucherDetail.expenseType){
                                $scope.rejectionReasons = expense.validations;
                            }
                        });
                    });
                }
                if (actionType == 'view') {
                    $scope.action.actionType = null;
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'GET',
                        url: APIJson.urls.walletManagement.voucherDetail,
                        params: {
                            voucherId: voucher.id
                        }
                    }).then(function success(response) {
                        $scope.selectedVoucher = response.data;
                        $scope.activeVoucherId = $scope.selectedVoucher.id;
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        $rootScope.rootLoading = false;
                    });
                }
                if (actionType == 'back') {
                    $scope.showExpenseTypes = true;
                    $scope.action.actionType = null;
                    scrollToElement();
                }
            };

            $scope.getVoucherDetails = function (click) {
                var identifier ="";
                if (!$scope.searchVoucher.startDate || !$scope.searchVoucher.endDate) {
                    toastr.warning("Please select start date and end date!");
                    return false;
                }
                if(AppUtil.isEmptyObject($scope.selectedAccountType)){
                    toastr.warning("Please select accountType first!");
                    return false;
                }else{
                    identifier= $scope.selectedAccountType==='UNIT'?'U':'E';
                }

                if($scope.walletsByAccTypeMultiSelect.length===0){
                    toastr.warning("No account selected !");
                    return false;
                }

                $scope.searchAction(null, 'back', 'Back');
                if ($scope.walletsByAccTypeMultiSelect.length > 0) {
                    $scope.searchVoucher.accountNo = [];
                    for (var i = 0; i < $scope.walletsByAccTypeMultiSelect.length; i++) {
                        var arr = $scope.walletsByAccTypeMultiSelect[i].split('_');
                        var accountNo="";
                        if($scope.walletsByAccTypeMultiSelect[i].includes('PETTY')){
                            accountNo= identifier +'_'+arr[2]+'_'+arr[3]+'_'+arr[0];
                        }else{
                            accountNo = identifier+'_'+arr[2]+'_'+arr[0];
                        }
                        $scope.searchVoucher.accountNo.push(accountNo);
                    }
                }
                /*if ($scope.unitMultiSelect.length > 0) {
                    $scope.searchVoucher.accountNo = [];
                    for (var i = 0; i < $scope.unitMultiSelect.length; i++) {
                        $scope.searchVoucher.accountNo.push($scope.unitMultiSelect[i].id);
                    }
                } else if ($scope.unitMultiSelect.length == 0 && $scope.stateDetails.actionBy == 'AM') {
                    if ($scope.units.length == 0) {
                        toastr.error("Unit mapping not found!");
                        return false;
                    }
                    $scope.searchVoucher.accountNo = [];
                    for (var i = 0; i < $scope.units.length; i++) {
                        $scope.searchVoucher.accountNo.push($scope.units[i].id);
                    }
                }*/

                /*if($scope.searchVoucher.status === undefined){
                    if($scope.stateDetails.actionBy == 'AM'){
                        $scope.searchVoucher.status = 'AM_PENDING';
                    }else if($scope.stateDetails.actionBy == 'FM'){
                        $scope.searchVoucher.status = 'FINANCE_PENDING';
                    }
                }*/
                if (!WalletService.downloadVoucher($scope.searchVoucher, false)) {
                    var url = APIJson.urls.walletManagement.voucherDetails;
                    if ($scope.stateDetails.actionBy == 'FM') {
                        url = APIJson.urls.walletManagement.voucherListFinance;
                    }
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'GET',
                        url: url,
                        params: {
                            accountNo: ($scope.searchVoucher.accountNo != undefined ? $scope.searchVoucher.accountNo.join() : null),
                            startDate: AppUtil.formatDate($scope.searchVoucher.startDate, 'yyyy-MM-dd'),
                            endDate: AppUtil.formatDate($scope.searchVoucher.endDate, 'yyyy-MM-dd'),
                            status: ($scope.searchVoucher.status != undefined ? $scope.searchVoucher.status : null)
                        }
                    }).then(function success(response) {
                        $scope.voucherList = response.data;

                        $scope.showExpenseTypes = false;
                        if($scope.voucherList.length > 0) {
                            $scope.showExpenseTypes = true;
                        }
                        $scope.allVoucherExpenseType = [];
                        $scope.UpdatedVoucherListAfterExpenseType = $scope.voucherList;
                        $scope.makeMapOfExpenseTypes();
                        for(var i = 0; i<$scope.voucherList.length; i++) {
                            var expenseType = $scope.voucherList[i].expenseType;
                            if($scope.allVoucherExpenseType.indexOf(expenseType) === -1) {
                                $scope.allVoucherExpenseType.push(expenseType);
                            }
                        }
                        if($scope.selectedAllVoucherExpenseType.length !== 0) {
                            $scope.checkUpdatedExpenseType();
                        }
                        if (click === false) {
                            scrollToElement();
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        $rootScope.rootLoading = false;
                    });
                }
            };

            $scope.checkUpdatedExpenseType = function() {
                var tempListOfExpenseType = [];
                for(var i = 0; i < $scope.selectedAllVoucherExpenseType.length; i++) {
                    var expenseType = $scope.selectedAllVoucherExpenseType[i];
                    if($scope.finalListOfExpenseTypes[expenseType] !== undefined &&
                        $scope.finalListOfExpenseTypes[expenseType] !== null &&
                        $scope.finalListOfExpenseTypes[expenseType].length > 0) {
                        tempListOfExpenseType.push(expenseType);
                    }
                }
                $scope.selectedAllVoucherExpenseType = tempListOfExpenseType;
                $scope.changeExpenseTypeFunction();
            }

            $scope.changeExpenseTypeFunction = function() {
                if (!$scope.voucherList) {
                    return;
                }
                if($scope.selectedAllVoucherExpenseType.length === 0) {
                    $scope.UpdatedVoucherListAfterExpenseType = $scope.voucherList;
                    return;
                }
                $scope.UpdatedVoucherListAfterExpenseType = [];
                for(var i = 0; i<$scope.selectedAllVoucherExpenseType.length; i++) {
                    $scope.finalListOfExpenseTypes[$scope.selectedAllVoucherExpenseType[i]].forEach(function(item) {
                        $scope.UpdatedVoucherListAfterExpenseType.push(item);
                    })
                }
            }

            $scope.makeMapOfExpenseTypes = function(){
                $scope.finalListOfExpenseTypes = {};
                for(var i = 0; i<$scope.voucherList.length; i++) {
                    var expenseType = $scope.voucherList[i].expenseType;
                    if(expenseType in $scope.finalListOfExpenseTypes){
                      $scope.finalListOfExpenseTypes[expenseType].push($scope.voucherList[i]);
                   }else{
                      $scope.finalListOfExpenseTypes[expenseType] = [];
                      $scope.finalListOfExpenseTypes[expenseType].push($scope.voucherList[i]);
                   }
                 }
            };
            $scope.$watch('selectedAllVoucherExpenseType', function() {
                $scope.changeExpenseTypeFunction();
            }, true);

            $scope.multiSelectSettings = {
                showEnableSearchButton: true, template: '<b> {{option}}</b>', scrollable: true,
                scrollableHeight: '200px'
            };


            $scope.createWallet = function () {
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.walletManagement.createWallet,
                    data: getWalletChangedBy($scope.walletData)
                }).then(function success(response) {
                    if (response.data != null) {
                        toastr.warning("Wallet created Successfully");
                        $scope.walletData = response.data;
                    } else {
                        toastr.error("Error occurred while creating wallet.Please try again!");
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error("Error occured while creating wallet : " + response.errorMsg);
                    $rootScope.rootLoading = false;
                });
            };

            $scope.initWallet = function () {
                $scope.walletData = {};
                $scope.walletData.accountNo = $scope.selectedUnit.id;
                $scope.walletData.accountHolderName = $scope.selectedUnit.name;
                $scope.walletData.accountType = 'UNIT';
                $scope.walletData.create = true;
            };

            $scope.showInvoice = function (voucher) {
                WalletService.showInvoice(voucher);
            };


            $scope.updateWallet = function () {
                var message = 'Are you sure you want to update wallet of unit ' + $scope.walletData.accountHolderName + '?';

                AppUtil.confirmModal("Update Wallet", message).then(function () {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.walletManagement.updateWallet,
                        data: getWalletChangedBy($scope.walletData)
                    }).then(function success(response) {
                        if (response.data != null) {
                            toastr.warning("Wallet updated Successfully");
                            $scope.walletData = response.data;
                        } else {
                            toastr.error("Error occurred while updating wallet.Please try again!");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error("Error occurred while updating wallet : " + response.errorMsg);
                        $rootScope.rootLoading = false;
                    });
                });
            };

            $scope.getWalletDetails = function (unit) {
                updateEntity('wallet');
                $rootScope.rootLoading = true;
                $scope.selectedUnit = unit;
                $http({
                    method: 'GET',
                    url: APIJson.urls.walletManagement.walletDetail,
                    params: {
                        accountNo: $scope.selectedUnit.id,
                        accountType: 'UNIT',
                        walletType: 'PETTY_CASH'
                    }
                }).then(function success(response) {
                    if (AppUtil.isEmptyObject(response.data)) {
                        $scope.walletData = null;
                    } else {
                        $scope.walletData = response.data;
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    $rootScope.rootLoading = false;
                });
            };

            function scrollToElement() {
                var newHash = 'voucher_' + $scope.activeVoucherId;
                if ($scope.activeVoucherId != -1) {
                    $location.hash(newHash);
                } else {
                    $anchorScroll();
                }
                $scope.activeVoucherId = -1;
            }

            $scope.updateVoucher = function () {
                var reject = ($scope.action.actionType == 'reject');
                if (reject) {
                    if (AppUtil.isEmptyObject($scope.voucherDetail.actionComment)) {
                        toastr.error("Please fill comment.");
                        return false;
                    }
                    var selectedRejections = [];
                    $scope.rejectionReasons.map(function (reason) {
                        if(reason.checked){
                            selectedRejections.push(reason);
                        }
                    });
                    if(selectedRejections.length == 0) {
                        toastr.error("Please select reason for rejection.");
                        return false;
                    }
                }
                if ($scope.stateDetails.actionBy == 'AM') {
                    updateVoucherAM(reject, selectedRejections);
                } else if ($scope.stateDetails.actionBy == 'FM') {
                    updateVoucherFinance(reject, selectedRejections);
                }

            };

            function updateEntity(entity) {
                $scope.activeEntity = entity;
            }

            function updateVoucherAM(reject, selectedRejections) {
                var url = null;
                var data = $scope.voucherDetail;
                if (reject) {
                    url = APIJson.urls.walletManagement.amRejectVoucher;
                    data = {
                        voucherId:$scope.voucherDetail.id,
                        comment:$scope.voucherDetail.actionComment,
                        actionBy:{id:$scope.userDetails.id,code:"", name:""},
                        rejections:selectedRejections,
                    }
                } else {
                    url = APIJson.urls.walletManagement.amApproveVoucher;
                }
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: url,
                    data: data
                }).then(function success(response) {
                    if (response.data != null && response.data == true) {
                        toastr.warning("Voucher " + (reject? "rejected" : "approved") + " successfully");
                        updateVoucherList();
                    } else {
                        toastr.error(getMsg());
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error(getMsg() + response.errorMsg);
                    $rootScope.rootLoading = false;
                });

            };

            function updateVoucherFinance(reject, selectedRejections) {
                var url = null;
                var data = $scope.voucherDetail;
                if (reject) {
                    url = APIJson.urls.walletManagement.financeRejectVoucher;
                    data = {
                        voucherId:$scope.voucherDetail.id,
                        comment:$scope.voucherDetail.actionComment,
                        actionBy:{id:$scope.userDetails.id,code:"", name:""},
                        rejections:selectedRejections,
                    }
                } else {
                    url = APIJson.urls.walletManagement.financeApproveVoucher;
                }
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: url,
                    data: data
                }).then(function success(response) {
                    if (response.data != null && response.data == true) {
                        toastr.warning("Voucher " + $scope.searchVoucher.actionLabel + "ed Successfully");
                        updateVoucherList();
                    } else {
                        toastr.error(getMsg());
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error(getMsg() + response.errorMsg);
                    $rootScope.rootLoading = false;
                });
            };

            function getWalletChangedBy(walletData) {
                walletData.createdBy = AppUtil.getIdCodeName(StorageUtil.getUserMeta().id);
                return walletData;
            }

            function getMsg() {
                return "Error while " + $scope.searchVoucher.actionLabel + "ing voucher! Please Try again.";
            }

            function updateVoucherList() {
                $scope.action.actionType = null;
                $scope.activeVoucherId = $scope.voucherDetail.id;
                $scope.getVoucherDetails(false);
            }

            $scope.resetSearchDetails = function () {
                $scope.walletData = null;
                $scope.voucherList = [];
                $scope.searchVoucher = {};
                $scope.selectedVoucher = null;
                $scope.activeVoucherId = -1;
                $scope.voucherDetail = {};
                $scope.unitMultiSelect = [];
                $scope.activeEntity = null;
                $scope.selectedUnit = null;
                $scope.allVoucherExpenseType = [];
                $scope.selectedAllVoucherExpenseType = [];
                $scope.UpdatedVoucherListAfterExpenseType = [];
                $scope.showExpenseTypes = false;
            };

            $scope.downloadInvoice = function (imageId, imageName, actionType) {
                WalletService.downloadInvoice(imageId, imageName, actionType);
            };

        }]);
