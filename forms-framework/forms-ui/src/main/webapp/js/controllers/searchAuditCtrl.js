'use strict';

angular.module('formsApp')
    .controller('searchAuditCtrl', ['$rootScope', 'MetadataService', '$scope', 'StorageUtil', '$location', '$http', 'APIJson', 'AppUtil', 'toastr','$state',
            function ($rootScope, MetadataService, $scope, StorageUtil, $location, $http, APIJson, AppUtil, toastr, $state) {

                $scope.init = function () {
                    MetadataService.getUnitList(function (unitList) {
                        $scope.units = unitList;
                    }, function (error) {
                        toastr.error(error);
                    });
                    getAuditFormList();
                    $scope.search = true;
                };
                
                function getAuditFormList() {
                	$rootScope.rootLoading = true;
                	$http({
                		method: 'GET',
                		url: APIJson.urls.auditFormManagement.getActiveAuditForms
                	}).then(function success(response) {
                		if (response && response.data) {
                			$scope.auditFormList = response.data;
                		} else {
                			toastr.error('Error getting audit form list.');
                		}
                		$rootScope.rootLoading = false;
                	}, function error(response) {
                		if (response && response.errorMsg) {
                			toastr.error(errorMsg);
                		} else {
                			toastr.error('Error getting audit form list.');
                		}
                		$rootScope.rootLoading = false;
                	});
                };
                
                $scope.setAuditForm = function (selectedAuditForm) {
                    if (selectedAuditForm != null) {
                        $scope.selectedAuditForm = selectedAuditForm;
                    }
                };

                $scope.dateOptions = {
                    dateDisabled: function () {
                        return false;
                    },
                    formatYear: 'yyyy',
                    maxDate: new Date(),
                    minDate: null,
                    startingDay: 1
                };

                $scope.setAuditUnit = function (selectedUnit) {
                    if (selectedUnit != null) {
                        $scope.selectedUnit = selectedUnit;
                    }
                };
                
                $scope.issueWarningLetter = function(audit) {
					var auditDetails = {
							unitDetails : audit.auditUnit,
							mod : audit.managerOnDuty,
							auditId : audit.id
					} 
					var params = {
							viewType : 'add',
							audit : auditDetails,
							warningId : null
					}
					$state.go("dashboard.warningDetail", params);
				};

                $scope.searchAudit = function () {
                    if ($scope.startDate && $scope.endDate) {
                        $rootScope.rootLoading = true;
                        $http({
                            method: 'GET',
                            url: APIJson.urls.auditManagement.searchAudit,
                            params: {
                                unitId: ($scope.selectedUnit == null ? null : $scope.selectedUnit.id),
                                auditFormId : ($scope.selectedAuditForm == null ? null : $scope.selectedAuditForm.id),
                                startDate: AppUtil.formatDate($scope.startDate, 'yyyy-MM-dd'),
                                endDate: AppUtil.formatDate($scope.endDate, 'yyyy-MM-dd')
                            }
                        }).then(function success(response) {
                            if (response && response.data) {
                                $scope.auditList = response.data;
                            } else {
                                toastr.error('Error getting audit list.');
                            }
                            $rootScope.rootLoading = false;
                            $scope.search = false;
                        }, function error(response) {
                            if (response && response.errorMsg) {
                                toastr.error(errorMsg);
                            } else {
                                toastr.error('Error getting audit list.');
                            }
                            $rootScope.rootLoading = false;
                        });
                    } else {
                        toastr.info("Please start date and end date!")
                    }
                };

                $scope.backToSearch = function () {
                    $scope.search = true;
                };

                $scope.downloadAuditReport = function (id) {
                    $http({
                        method: 'POST',
                        url: APIJson.urls.auditManagement.downloadAuditReport,
                        data: id,
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/pdf'
                        }
                    }).then(function success(response) {
                        if (response && response.data != null) {
                            var fileName = "PQSC.pdf";
                            var blob = new Blob([response.data], {
                                type: 'c'
                            }, fileName);
                            saveAs(blob, fileName);
                        } else {
                            toastr.error("Could not fetch report.");
                        }
                        $scope.loading = false;
                    }, function error(response) {
                        if (response && response.errorMsg) {
                            toastr.error(errorMsg);
                        } else {
                            toastr.error('Error getting audit list.');
                        }
                        $scope.loading = false;
                    });
                };

                $scope.generateAuditReport = function (id) {
                    $http({
                        method: 'POST',
                        url: APIJson.urls.auditManagement.downloadGenerateAuditReport,
                        data: id,
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/pdf'
                        }
                    }).then(function success(response) {
                        if (response && response.data != null) {
                            var fileName = "PQSC.pdf";
                            var blob = new Blob([response.data], {
                                type: 'c'
                            }, fileName);
                            saveAs(blob, fileName);
                        } else {
                            toastr.error("Could not fetch report.");
                        }
                        $scope.loading = false;
                    }, function error(response) {
                        if (response && response.errorMsg) {
                            toastr.error(errorMsg);
                        } else {
                            toastr.error('Error getting audit list.');
                        }
                        $scope.loading = false;
                    });
                };

                $scope.emailAuditReport = function (id) {
                    $http({
                        method: 'POST',
                        url: APIJson.urls.auditManagement.auditReportEmail,
                        data: id
                    }).then(function success(response) {
                        if (response && response.data == true) {
                            toastr.error("Report sent successfully.");
                        } else {
                            toastr.error("Could not fetch report.");
                        }
                        $scope.loading = false;
                    }, function error(response) {
                        if (response && response.errorMsg) {
                            toastr.error(errorMsg);
                        } else {
                            toastr.error('Error getting audit list.');
                        }
                        $scope.loading = false;
                    });
                };

                /////////////////// mobile specific methods //////////////////

                $scope.hideAutocomplete = function () {
                    $scope.autocompletePanel = false;
                };

                $scope.showAutocomplete = function (list, name, model, id, data, dataName) {
                    $scope.autocompleteList = list;
                    $scope.autocompleteName = name;
                    $scope.autocompletePanel = true;
                    $scope.filteredList = list;
                    $scope.model = model;
                    $scope.qid = id;
                    $scope.ans = data || {};
                    if($scope.model == "AUDIT_UNIT"){
                        $scope.autocompleteCallback = function (item) {
                            $scope.setAuditUnit(item);
                        };
                    } else if($scope.model == "UNIT_MANAGER"){
                        $scope.autocompleteCallback = function (item) {
                            $scope.setUnitManager(item);
                        };
                    } else if($scope.model == "MANAGER_ON_DUTY"){
                        $scope.autocompleteCallback = function (item) {
                            $scope.setManagerOnDuty(item);
                        };
                    }else{
                        $scope.autocompleteCallback = function (item) {
                            if(dataName != null && dataName != undefined){
                                $scope.ans[dataName] = item;
                            }else{
                                $scope.ans = item;
                            }
                            $scope.setEntityValue($scope.qid, $scope.ans);
                        };
                    }
                };

                $scope.autocompleteInit = function () {
                    var elem = document.getElementById("autocompleteInput");
                    elem.focus();
                };

                $scope.selectItem = function (item) {
                    if($scope.model == "AUDIT_UNIT"){
                        $scope.autocompleteCallback(item);
                    } else if($scope.model == "UNIT_MANAGER"){
                        $scope.autocompleteCallback(item);
                    } else if($scope.model == "MANAGER_ON_DUTY"){
                        $scope.autocompleteCallback(item);
                    }else{
                        $scope.autocompleteCallback(item);
                    }
                    $scope.autocompletePanel = false;
                };

                $scope.clearAutoComplete = function () {
                    $scope.autocompleteCallback(null);
                    $scope.autocompletePanel = false;
                }
            }
        ]
    );