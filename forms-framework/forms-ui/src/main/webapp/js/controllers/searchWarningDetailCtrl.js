'use strict';

angular.module('formsApp')
.controller('searchWarningDetailCtrl',['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', '$http', 'AppUtil', 'MetadataService', 'toastr', '$uibModal','$stateParams', 'StorageUtil','$state',
	function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, AppUtil, MetadataService, toastr, $uibModal,$stateParams, StorageUtil,$state) {
	var searchParams = {};
	$scope.init = function () {
		$scope.userDetails = StorageUtil.getUserMeta();
		$scope.impactTypeList = [{name :"ZTZ" ,label :"ZTZ"},{name :"CRITICAL" ,label :"Critical"},{name :"BOTH" ,label :"Both"}];
		$scope.warningDetails = [];
		$scope.searchWarning = {};
		
		MetadataService.getUnitList(function (unitList) {
			$scope.units = unitList;
			if($stateParams.searchParams){
				$scope.startDate = $stateParams.searchParams.startDate;
				$scope.endDate = $stateParams.searchParams.endDate;
				if($stateParams.searchParams.selectedUnit != null && $stateParams.searchParams.selectedUnit != undefined){
					for(var i=0;i<$scope.units.length;i++){
						if($scope.units[i].id == $stateParams.searchParams.selectedUnit.id){
							$scope.searchWarning.selectedUnit = $scope.units[i];
							break;
						}
					}
				}
				$scope.searchWarning.selectedStatus = $stateParams.searchParams.selectedStatus;
				$scope.searchWarningLetter(true);
			}
		}, function (error) {
			toastr.error(error);
		});
	};
	
	$scope.resetDetails = function(){
		$scope.warningDetails = [];
		$scope.searchWarning = {};
		$scope.startDate = undefined;
		$scope.endDate = undefined;
	};

	$scope.dateOptions = {
			dateDisabled: function () {
				return false;
			},
			formatYear: 'yyyy',
			maxDate: new Date(),
			minDate: null,
			startingDay: 1
	};

	$scope.searchWarningLetter = function(params) {
		if(params === undefined || params === false){
			if (!$scope.startDate || !$scope.endDate) {
				toastr.info("Please select start date and end date!");
				return false;
			}
		}
		
		$scope.searchWarning.startDate = $scope.startDate;
		$scope.searchWarning.endDate = $scope.endDate;
	//	$scope.searchWarning.selectedUnit = $scope.selectedUnit;
		//$scope.searchWarning.selectedUnit = $scope.selectedStatus;
		
		searchParams = {
				unitId: ($scope.searchWarning.selectedUnit == undefined ? null : $scope.searchWarning.selectedUnit.id),
				startDate: AppUtil.formatDate($scope.startDate, 'yyyy-MM-dd'),
				endDate: AppUtil.formatDate($scope.endDate, 'yyyy-MM-dd'),
				status : $scope.searchWarning.selectedStatus == undefined ? null  : $scope.searchWarning.selectedStatus,
				amId : (StorageUtil.getUserMeta().designation == "Area Manager" ? StorageUtil.getUserMeta().id : null)
		};
		
		$rootScope.rootLoading = true;
		$http({
			method: 'GET',
			url: APIJson.urls.warningManagement.warningLetter,
			params: searchParams
		}).then(function success(response) {
			//console.log("response.data",response.data);
			if (response && response.data) {
				$scope.warningDetails = response.data;
			} else {
				toastr.error('Error getting warning letter list.');
			}
			$rootScope.rootLoading = false;
		}, function error(response) {
			toastr.error('Error getting warning letter  list.');
			$rootScope.rootLoading = false;
		});
	};

	$scope.warningAction = function(warningId, actionTakenBy){
		var params = {
				viewType :(actionTakenBy == "cancelResponse" ? 'cancel' : 'edit'),
				audit : null,
				warningId : warningId,
				actionBy : actionTakenBy,
				searchParams : $scope.searchWarning
		}
		$state.go("dashboard.warningDetail", params);
	};
	
	 $scope.downloadWarningLetterReport = function (warningId) {
		 $rootScope.rootLoading = true;
         $http({
             method: 'POST',
             url: APIJson.urls.warningManagement.downloadWarningReport,
             data: warningId,
             responseType: 'arraybuffer',
             headers: {
                 'Content-type': 'application/json',
                 'Accept': 'application/pdf'
             }
         }).then(function success(response) {
             if (response && response.data != null) {
                 var fileName = "warning.pdf";
                 var blob = new Blob([response.data], {
                     type: 'c'
                 }, fileName);
                 saveAs(blob, fileName);
             } else {
                 toastr.error("Could not fetch report.");
             }
             $rootScope.rootLoading = false;
         }, function error(response) {
             if (response && response.errorMsg) {
                 toastr.error(errorMsg);
             } else {
                 toastr.error('Error getting warning report.');
             }
             $rootScope.rootLoading = false;
         });
     };


	$scope.showWarningDetail = function(warningId) {
		var params = {
				viewType : 'view',
				audit : null,
				warningId : warningId,
				searchParams : $scope.searchWarning
		}
		$state.go("dashboard.warningDetail", params);
	};

}]);
