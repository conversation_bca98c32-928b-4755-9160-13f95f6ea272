'use strict';

angular.module('formsApp')
    .controller('loginCtrl', ['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', '$http', 'StorageUtil','MetadataService',
            function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, StorageUtil, MetadataService) {

                $scope.init = function () {
                    $scope.permissions = null;
                    $scope.userId = null;
                    $scope.passcode = null;
                    $("#userId").focus();
                    $scope.loading = false;
                    $rootScope.loginType = "AGENT";
                    var type = $location.search().type;
                    if (type != null) {
                        $scope.loginType = type === 'CAFE' ? 'CAFE' : 'AGENT';
                    }
                    $scope.outletList = [];
                };

                $scope.selectLoginType = function (type) {
                    $rootScope.loginType = type;
                    if(type === 'CAFE' && $scope.userId != null){
                        $scope.fetchOutletsForEmployee();
                    }
                };

                $scope.selectOutlet = function (unitId) {
                    $scope.unitId = unitId;
                };

                $scope.fetchOutletsForEmployee = function () {
                    var id = $scope.userId == null ? '' :$scope.userId.toString();
                    if($scope.loginType === 'CAFE' && id.length === 6){
                        $scope.loading = true;
                        $scope.outletList = [];
                        $http({
                            method: 'POST',
                            url: APIJson.urls.userManagement.activeUnitsForUser,
                            data: {employeeId: parseInt($scope.userId), onlyActive: false}
                        }).then(function success(response) {
                            var units = response.data;
                            if (units != null && units.length > 0) {
                                for (var i in units) {
                                    if (units[i].category === 'CAFE' || units[i].category === 'KITCHEN' || units[i].category === 'WAREHOUSE') {
                                        $scope.outletList.push(units[i])
                                    }
                                }
                                MetadataService.setUserUnits($scope.outletList);
                            }
                            $scope.loading = false;
                        }, function error(response) {
                            $scope.errorMessage = 'Error loading units for employee.';
                            $scope.error = true;
                            $scope.loading = false;
                        });
                    }

                };

                $scope.login = function () {
                    if ($scope.userId == null) {
                        $scope.errorMessage = 'Please fill user id';
                        $scope.error = true;
                    } else if ($scope.password == null) {
                        $scope.errorMessage = 'Please fill password';
                        $scope.error = true;
                    } else if ($scope.loginType === "CAFE" && $scope.unitId == null) {
                        $scope.errorMessage = 'Please select unit';
                        $scope.error = true;
                    } else {
                        var userObj = {
                            userId: $scope.userId,
                            password: $scope.password,
                            unitId: $scope.loginType === "CAFE" ? $scope.unitId : 0,
                            terminalId: 0,
                            application: "FORMS_SERVICE"
                        };
                        $scope.loading = true;
                        $http({
                            method: 'POST',
                            url: APIJson.urls.users.login,
                            data: userObj
                        }).then(function success(response) {
                            if (response.data == null || response.data.sessionKeyId == null) {
                                $scope.errorMessage = 'Credentials are not correct!';
                                $scope.error = true;
                            } else {
                                $scope.message = 'Authentication successful. Redirecting to dashboard!';
                                $scope.successMessage = true;
                                $cookieStore.put("auth", response.data.jwtToken);
                                $rootScope.auth = response.data.jwtToken;
                                $rootScope.aclData = response.data.acl;
                                var meta = StorageUtil.getUserMeta();
                                meta.name = response.data.user.name;
                                meta.id = response.data.user.id;
                                meta.department = response.data.user.department.name;
                                meta.designation = response.data.user.designation.name;
                                meta.unitId = ($scope.loginType === "CAFE" ? $scope.unitId : null);
                                setUnit(meta.unitId);
                                StorageUtil.setUserMeta(meta);
                                StorageUtil.setAclData($rootScope.aclData);
                                $location.path("/dashboard");
                            }
                            $scope.loading = false;
                        }, function error(response) {
                            $scope.errorMessage = 'Authentication failed! Please make sure you are authorised for audits';
                            $scope.error = true;
                            $scope.loading = false;
                        });
                    }

                };
                
                function setUnit(unitId){
                	if(unitId != null){
                		MetadataService.getUnitList(function (unitList) {
                			for (var i = 0; i < unitList.length; i++) {
                				if(unitList[i].id == $scope.unitId){
                					$rootScope.selectedUnit = unitList[i];
                					break;
                				}
                			}
                		}, function (error) {
                			toastr.error(error);
                		});
                	}else{
                		$rootScope.selectedUnit = null;
                	}
                }
            }
        ]
    );