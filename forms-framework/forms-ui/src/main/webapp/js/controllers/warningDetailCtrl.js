'use strict';

angular.module('formsApp')
.controller('warningDetailCtrl',['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', '$http', 'AppUtil', 'MetadataService', 'toastr', '$uibModal','$stateParams', 'StorageUtil','$state',
	function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, AppUtil, MetadataService, toastr, $uibModal,$stateParams, StorageUtil,$state) {

	$scope.init = function () {
		$scope.impactTypeList = [{name :"ZTZ" ,label :"ZTZ"},{name :"CRITIC<PERSON>" ,label :"CRITIC<PERSON>"},{name :"BOTH" ,label :"BOTH (ZTZ + CRITICAL)"}];
		$scope.userDetails = StorageUtil.getUserMeta();
		$scope.commentLength = 1000;
		$scope.uploadImage = false;
		/**
		 * viewType : (add/edit/view)
		 * audit : audit details for issuing warning letter
		 * warningId : to view/edit existing warning
		 *actionBy : owner of action 
		 *searchParmas : details of search params to restore the last state
		 */
		$scope.stateDetails = {
				viewType : $stateParams.viewType,
				audit : $stateParams.audit,
				warningId : $stateParams.warningId,
				actionBy : $stateParams.actionBy,
				searchParams : $stateParams.searchParams
		};
		MetadataService.getUnitList(function (unitList) {
			$scope.units = unitList;
		}, function (error) {
			toastr.error(error);
		});
		/**
		 * This executes when we issue warning from audit  
		 */
		if($scope.stateDetails.audit != null){
			$scope.setWarningUnit($scope.stateDetails.audit.unitDetails);
		}
		$scope.resetDetails(false);
		/**
		 * This executes when we edit warning for action details of(HR/DGM/AM) or view warning details 
		 */
		if($scope.stateDetails.warningId != null){
			getwarningDetail();
		}
		$scope.noOfImages = 5;
		$scope.imageDetails = {};
		
		$scope.dateOptions = {
				dateDisabled: function () {
					return false;
				},
				formatYear: 'yyyy',
				maxDate: new Date(),
				startingDay: 1
		};
	};


	$scope.updateImpactType = function(impactType){
		$scope.reasonList = [];
		$scope.loading = true;
		$http({
			method: 'GET',
			url: APIJson.urls.budgetManagement.warningReasonDetail,
			params : {impactType : impactType}
		}).then(function success(response) {
			if (response.data != null && Array.isArray(response.data)) {
				$scope.warningReasonList = response.data;
			} else {
				toastr.error("Error while getting Reason List.");
			}
			$scope.loading = false;
		}, function error(response) {
			toastr.error("Error while getting Reason List.");
			$scope.loading = false;
		});
	};


	function getwarningDetail(){
		$scope.reasonList = [];
		$rootScope.rootLoading = true;
		$http({
			method: 'GET',
			url: APIJson.urls.warningManagement.getWarningDetail,
			params : {warningId : $stateParams.warningId}
		}).then(function success(response) {
			var result = response.data;
			$scope.warningDetails = result;
			/**
			 * $scope.warningStatus holds data of warning initiator
			 */
			$scope.warningStatus = result.initiator;
			$scope.reasonList = result.initiator.reasons;
			for(var i=0;i<$scope.units.length;i++){
				if($scope.warningDetails.unit.id == $scope.units[i].id){
					$scope.selectedUnit  = $scope.units[i];
					$scope.setWarningUnit($scope.selectedUnit);
				}
			}
			$rootScope.rootLoading = false;
		}, function error(response) {
			toastr.error("Error while getting warning detail.");
			$rootScope.rootLoading = false;
		});
	};

	$scope.updateReasonlist = function(reason,type) {
		if(reason === undefined || reason === null){
			return false;
		}
		if(type == 'add'){
			for(var i = 0; i< $scope.reasonList.length; i++){
				if(reason.reasonId == $scope.reasonList[i].reasonId){
					toastr.error("Reason already added");
					return false;
				}
			}
			$scope.reasonList.push(reason);
		}else if(type == 'remove'){
			for(var i = 0; i< $scope.reasonList.length; i++){
				if(reason.reasonId == $scope.reasonList[i].reasonId){
					$scope.reasonList.splice(i,1);
					toastr.success("Reason successfully removed");
					break;
				}
			}
		}
	};

	$scope.setWarningUnit = function (selectedUnit) {
		if (selectedUnit != null) {
			$scope.selectedUnit = selectedUnit;
			$rootScope.rootLoading = true;
			MetadataService.getAuditUnitEmployees(selectedUnit.id, function (unitEmployees) {
				$rootScope.rootLoading = false;
				$scope.unitEmployees = unitEmployees;
				if($scope.stateDetails.warningId !=null){
					updateEmployeeDetails();
				}
			}, function (error) {
				$rootScope.rootLoading = false;
				toastr.error(error);
			});
		}
	};

	function updateEmployeeDetails(){
		for(var i=0;i<$scope.unitEmployees.length;i++){
			if($scope.warningDetails.managerOnDuty.id == $scope.unitEmployees[i].id){
				$scope.empDetails.managerOnDuty  = $scope.unitEmployees[i];
			}
			if($scope.warningDetails.guiltyPerson.id == $scope.unitEmployees[i].id){
				$scope.empDetails.guiltyPerson  = $scope.unitEmployees[i];
			}
		}
	}

	$scope.issueWarningLetter = function(){
		if($scope.reasonList.length == 0){
			toastr.error("Please select few Reasons.");
			return false;
		} else if (!$scope.warningDetails.doi) {
            toastr.error('Please fill date of incidence.');
        }

		if($scope.stateDetails.audit != null){
			$scope.warningDetails.managerOnDuty = AppUtil.getIdCodeName($scope.stateDetails.audit.mod.id, $scope.stateDetails.audit.mod.name);
			$scope.warningDetails.unit = AppUtil.getIdCodeName($scope.stateDetails.audit.unitDetails.id);
			$scope.warningDetails.audit = AppUtil.getIdCodeName($scope.stateDetails.audit.auditId);
		}else{
			$scope.warningDetails.unit = AppUtil.getIdCodeName($scope.selectedUnit.id);
			$scope.warningDetails.managerOnDuty = AppUtil.getIdCodeName($scope.empDetails.managerOnDuty.id,$scope.empDetails.managerOnDuty.name);
		}


		$scope.warningDetails.guiltyPerson = AppUtil.getIdCodeName($scope.empDetails.guiltyPerson.id,$scope.empDetails.guiltyPerson.name,$scope.empDetails.guiltyPerson.designation);
		$scope.warningStatus.authorisedPerson = AppUtil.getIdCodeName(StorageUtil.getUserMeta().id);
		$scope.warningStatus.reasons = [];
		var reasonOwner = null;
		if($scope.stateDetails.audit == null || StorageUtil.getUserMeta().designation == "Area Manager"){
			reasonOwner = "INITIATOR_AM" ; 
		}else{
			reasonOwner = "INITIATOR_AUDITOR";
		}
		for(var i = 0; i< $scope.reasonList.length; i++){
			$scope.warningStatus.reasons.push(getWarningReason($scope.reasonList[i], reasonOwner));
		}
		$scope.warningDetails.initiator = $scope.warningStatus;
		//console.log("$scope.warningDetails",$scope.warningDetails);
		$rootScope.rootLoading = true;
		$http({
			method: 'POST',
			url: APIJson.urls.warningManagement.issueWarningLetter,
			data : $scope.warningDetails
		}).then(function success(response) {
			var result = response.data; 
			if(result !== null && typeof response.data ==  'number'){
				toastr.success("Warning Letter Generated Successfully.");
				initUploadImage(result);
				$scope.uploadImage = true;
			}else{
				toastr.error("Error while genearating Warning Letter.");
			}
			$rootScope.rootLoading = false;
		}, function error(response) {
			toastr.error("Error while genearating Warning Letter.");
			$rootScope.rootLoading = false;
		});

	};

	$scope.resetDetails = function(redirect) {

		$scope.reasonList = [];
		$scope.empDetails  = {};
		$scope.warningDetails = {};
		$scope.warningStatus = {};
		$scope.warningReasonList = [];
		$scope.selectedUnit = null;
		$scope.uploadImage = false;
		$scope.warningDetails.doi = new Date();
		if(redirect){
			if($scope.stateDetails.viewType == 'add'){
				$state.go('dashboard.searchWarningDetail',{ 'searchParams':null});
			}else{
				$state.go('dashboard.searchWarningDetail',{ 'searchParams':$scope.stateDetails.searchParams});
			}
		}
	};


	function getWarningReason(reasonDetail,addedBy){
		var reason = {
				reasonId : reasonDetail.reasonId,
				reasonName : reasonDetail.reasonName,
				category : reasonDetail.category,
				reasonAddedBy : addedBy
		}
		return reason;
	}

	$scope.warningActionDetail = function(actionType){
		//console.log("$scope.warningDetails",$scope.warningDetails);
		var actionComment = $scope.warningDetails[$scope.stateDetails.actionBy].comment;
		if(actionComment == null || actionComment == undefined || actionComment.length == 0 ){
			toastr.error("Please fill your valuable comment.");
			return false;
		}
		$scope.warningDetails[$scope.stateDetails.actionBy].actionTakenBy = $scope.stateDetails.actionBy;
		$scope.warningDetails[$scope.stateDetails.actionBy].actionType = actionType;
		$scope.warningDetails[$scope.stateDetails.actionBy].warningId = $scope.warningDetails.id;
		$scope.warningDetails[$scope.stateDetails.actionBy].authorisedPerson = AppUtil.getIdCodeName(StorageUtil.getUserMeta().id);
		$scope.warningDetails[$scope.stateDetails.actionBy].fromStatus = $scope.warningDetails.warningStage;

		$rootScope.rootLoading = true;
		$http({
			method: 'POST',
			url: APIJson.urls.warningManagement.warningAction,
			data : $scope.warningDetails[$scope.stateDetails.actionBy]
		}).then(function success(response) {
			var result = response.data; 
			if(result === true){
				toastr.success("Your response recorded Successfully.");
				$scope.resetDetails(true);
			}else{
				toastr.error("Error while recording your respone.Please try later!");
			}
			$rootScope.rootLoading = false;
		}, function error(response) {
			toastr.error("Error while recording your respone.Please try later!",+response);
			$rootScope.rootLoading = false;
		});
	};


	function initUploadImage(warningId) {
		$scope.imageDetails.warningId = warningId;
		$scope.imageDetails.files = [];
		/*for (var i = 0; i < $scope.noOfImages; i++) {
			 = warningId;
		}*/
	}

	$scope.uploadImageFile = function() {  
		if($scope.imageDetails.files.length == 0){
			toastr.error("Please choose images to upload or skip this!");
			return false;
		}
		var data = new FormData();
		data.append("warningId", $scope.imageDetails.warningId);
		for (var i = 0; i < $scope.imageDetails.files.length; i++) {
			data.append("files", $scope.imageDetails.files[i]);
		}
		var config = {
				transformRequest: angular.identity,
				transformResponse: angular.identity,
				headers: {
					'Content-Type': undefined
				}
		}
		$rootScope.rootLoading = true;
		$http.post(APIJson.urls.warningManagement.uploadWarningImage, data, config).then(
				function(response) {
					if(response.data == "true"){
						$rootScope.rootLoading = false;
						console.log("response",response);
						toastr.success("Warning image uploaded Successfully.");
						$scope.resetDetails(true);
					}else{
						$rootScope.rootLoading = false;
						toastr.error("Error while uploading image.Please try later!");
					}
				},
				function(response) {
					$rootScope.rootLoading = false;
					toastr.error("Error while uploading image.Please try later!");
				});
	};

	$scope.downloadWarningImage = function (imageId, imageName, actionType) {
		$rootScope.rootLoading = true;
		$http({
			method: 'POST',
			url: APIJson.urls.warningManagement.downloadWarningImage,
			data: imageId,
			responseType: 'arraybuffer',
			headers: {
				'Content-type': 'application/json',
				'Accept': 'application/pdf'
			}
		}).then(function success(response) {
			if (response && response.data != null) {
				var blob = new Blob([response.data], {
					type : 'image/*'
				}, imageName);
				$rootScope.rootLoading = false;
				if(actionType == 'view'){
					var imageUrl =  URL.createObjectURL(blob);
					previewWarningImage(imageUrl, imageName, imageId, blob);
				}else{
					saveAs(blob, imageName);
				}
			} else {
				toastr.error("Could not fetch images.");
			}
		}, function error(response) {
			if (response && response.errorMsg) {
				toastr.error(errorMsg);
			} else {
				toastr.error('Error getting warning image.');
			}
			$rootScope.rootLoading = false;
		});
	};

	function previewWarningImage(imageUrl, imageName, imageId, imageFile) {

		var modalInstance = $uibModal.open({
			ariaLabelledBy: 'modal-title',
			ariaDescribedBy: 'modal-body',
			templateUrl: 'views/warningImagePreviewModal.html',
			controller: 'WarningImagePreviewCtrl',
			backdrop: 'static',
			keyboard: false,
			size : 'lg',
			resolve: {
				warningImageDetails: function () {
                    return {
                        imageUrl : imageUrl,
                        imageName : imageName,
                        imageId : imageId,
                        imageFile : imageFile
                    }
                }
            }
		});
		modalInstance.result.then(function() {

		});
	};

	$scope.cancel = function() {
		$uibModalInstance.dismiss('cancel');
	};

}]);
