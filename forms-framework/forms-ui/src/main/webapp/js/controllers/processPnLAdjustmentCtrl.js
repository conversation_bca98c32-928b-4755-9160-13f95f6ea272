'use strict';

angular.module('formsApp')
    .controller('processPnLAdjustmentCtrl', ['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', '$http', 'AppUtil', 'MetadataService', 'toastr', '$uibModal', '$stateParams', 'StorageUtil', '$state', '$anchorScroll', 'WalletService',
        function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, AppUtil, MetadataService, toastr, $uibModal, $stateParams, StorageUtil, $state, $anchorScroll, WalletService) {
            $scope.init = function () {
                $scope.units = [];
                $scope.selectedUnit=[];
                getUnits();
                $scope.resetSearchDetails();
                $scope.months=[];
                for( var i=1; i<13;i++){
                    $scope.months.push(i);
                }
                $scope.years=[];
                var currentdate = new Date();
                for(var i=currentdate.getFullYear();i>=1985;i--){
                    $scope.years.push(i);
                }
                $scope.adjustmentResponse=[];
                $scope.adjustmentObject={};
                $scope.selectedStartMonth=$scope.selectedStartYear=null;
                $scope.selectedEndMonth=$scope.selectedEndYear=null;
                $scope.status=null;
                $scope.stateDetails = {};
                $scope.stateDetails.actionBy = $stateParams.actionBy;
                $scope.userDetails = StorageUtil.getUserMeta();
                $scope.multiSelectSettings = {showEnableSearchButton: false, template: '<b>{{option.name}}</b>'};
                $scope.adjustmentObject.Comment=null;
                $scope.CommentText=null;
                $scope.adjustmentObject.CommentText=null;

                initSearchParams();
                $scope.statusList = ["CREATED", "APPROVED", "REJECTED","CANCELLED"];
            };

            function initSearchParams() {
                $scope.searchVoucher.startDate = AppUtil.getDate(-7);
                $scope.searchVoucher.endDate = AppUtil.getDate();
                $scope.action = {actionType: null};
            }

            function getUnits() {
                    MetadataService.getUnitList(function (unitList) {
                        multiSelectUnitModal(unitList);
                    }, function (error) {
                        toastr.error(error);
                    });
            }

            function multiSelectUnitModal(unitsList) {
                for (var i = 0; i < unitsList.length; i++) {
                    $scope.units.push({name: unitsList[i].name, id: unitsList[i].id});
                }
            }


            $scope.getDetails = function () {
                if ($scope.selectedStartMonth ==null || $scope.selectedStartYear==null) {
                    toastr.warning("Please select Year and Month!");
                    return false;
                }
                for(var i=0;i<$scope.unitMultiSelect.length;i++){
                    $scope.selectedUnit.push($scope.unitMultiSelect[i].id);
                }
                $rootScope.rootLoading = true;
                var obj={
                    unitId:$scope.selectedUnit,
                    month:$scope.selectedStartMonth,
                    year:$scope.selectedStartYear,
                    endMonth:$scope.selectedEndMonth,
                    endYear:$scope.selectedEndYear,
                    status:$scope.status
                }
                $http({
                    method: 'POST',
                    url: APIJson.urls.PnLManagement.getAdjustment,
                    data:obj
                }).then(function success(response) {
                        $scope.voucherList = response.data;
                        $rootScope.rootLoading = false;
                        $scope.resetValue();
                    },
                    function error(response) {
                        $rootScope.rootLoading = false;
                    });
            };

            $scope.searchAction = function (type,object){
                $scope.adjustmentFinalObject=object;
                $scope.type=type;
                console.log($scope.adjustmentFinalObject);
                $http({
                    method : 'GET',
                    url : APIJson.urls.PnLManagement.getAdjustmentReason
                }).then(function success(response){
                    $scope.adjustment=response.data;
                    if($scope.type=='APPROVED') {
                        $scope.adjustmentResponse=[];
                        for (var i = 0; i < $scope.adjustment['APPROVE_ADJUSTMENT_REASONS'].content.length; i++) {
                            $scope.adjustmentResponse.push($scope.adjustment['APPROVE_ADJUSTMENT_REASONS'].content[i].name);
                        }
                    }
                    if($scope.type=='REJECTED') {
                        $scope.adjustmentResponse=[];
                        for (var i = 0; i < $scope.adjustment['REJECT_ADJUSTMENT_REASONS'].content.length; i++) {
                            $scope.adjustmentResponse.push($scope.adjustment['REJECT_ADJUSTMENT_REASONS'].content[i].name);
                        }
                    }
                    if($scope.type=='CANCELLED') {
                        $scope.adjustmentResponse=[];
                        for (var i = 0; i < $scope.adjustment['CANCEL_ADJUSTMENT_REASONS'].content.length; i++) {
                            $scope.adjustmentResponse.push($scope.adjustment['CANCEL_ADJUSTMENT_REASONS'].content[i].name);
                        }
                    }
                    console.log($scope.adjustmentResponse);
                },function error(response) {
                    console.log("error:" + response);
                });
            }
            $scope.resetSearchDetails = function () {
                $scope.searchVoucher = {};
                $scope.unitMultiSelect = [];
                $scope.selectedStartMonth=$scope.selectedStartYear=null;
                $scope.selectedEndMonth=$scope.selectedEndYear=null;
                $scope.selectedUnit = [];
                $scope.adjustmentObject={};
            }

            $scope.updatePnLStatus = function (){
                if($scope.type=='REJECTED'){
                    // $scope.adjustmentFinalObject.status=$scope.type;
                    $scope.adjustmentFinalObject.rejectComment=$scope.adjustmentObject.Comment;
                    $scope.adjustmentFinalObject.rejectCommentText=$scope.adjustmentObject.CommentText;
                }
                if($scope.type=='CANCELLED'){
                    // $scope.adjustmentFinalObject.status=$scope.type;
                    $scope.adjustmentFinalObject.cancellationComment=$scope.adjustmentObject.Comment;
                    $scope.adjustmentFinalObject.cancellationCommentText=$scope.adjustmentObject.CommentText;
                }
                if($scope.type=='APPROVED'){
                    // $scope.adjustmentFinalObject.status=$scope.type;
                    $scope.adjustmentFinalObject.approvedComment=$scope.adjustmentObject.Comment;
                    $scope.adjustmentFinalObject.approvedCommentText=$scope.adjustmentObject.CommentText;
                }
                console.log($scope.adjustmentFinalObject);
                console.log($scope.type);
                $http({
                    method: 'POST',
                    url: APIJson.urls.PnLManagement.updateAdjustmentStatus,
                    params: {
                        status: $scope.type
                    },
                    data: $scope.adjustmentFinalObject
                }).then(function success(response) {
                        $scope.voucherList = response.data;
                        toastr.success($scope.type);
                        $scope.getDetails();
                        $rootScope.rootLoading = false;
                     }, function error(response) {
                        $rootScope.rootLoading = false;
                });
            };
            $scope.resetValue = function (){
                $scope.adjustmentObject.CommentText=null;
                $scope.adjustmentObject.Comment=null;
                $scope.selectedUnit = [];
                $scope.unitMultiSelect = [];

            };
            $scope.resetResponse = function (){
                $scope.voucherList={};
            }
        }]);
