/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
angular.module('formsApp')
    .controller('createPnlAdjusmentCtrl', ['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore',
        '$http', '$stateParams', 'MetadataService', 'StorageUtil', '$state', 'AppUtil', 'toastr', '$uibModal', '$filter',
        function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, $stateParams, MetadataService, StorageUtil, $state, AppUtil, toastr, $uibModal, $filter) {
            $scope.init = function () {
                $scope.userDetails = StorageUtil.getUserMeta();
                console.log($scope.userDetails);
                MetadataService.getUnitList(function (unitList) {
                    $scope.units = [];
                    for (var i = 0; i < unitList.length; i++) {
                        $scope.units.push(unitList[i]);
                    }
                }, function (error) {
                    toastr.error(error);
                });
                $scope.adjustmentResponse = [];
                $scope.calculationType = ["EARNING", "COST"];
                $scope.selectedUnit = {};
                $scope.PnLHeaderList = [];
                $scope.comment = "";
                getAdjustmentReason();
                $scope.selectedUnit.unitDate = new Date();
            };
            $scope.months = [];
            for (var i = 1; i < 13; i++) {
                $scope.months.push(i);
            }
            $scope.years = [];
            var currentdate = new Date();
            for (var i = currentdate.getFullYear(); i >= 1985; i--) {
                $scope.years.push(i);
            }
            console.log($scope.years);
            console.log($scope.months);
            $scope.updateUnitAdd = function (value) {
                $scope.selectedUnit.unitDetail = value;
            }

            function getAdjustmentReason() {
                $http({
                    method: 'GET',
                    url: APIJson.urls.PnLManagement.getAdjustmentReason
                }).then(function success(response) {
                    $scope.adjustment = response.data;
                    for (var i = 0; i < $scope.adjustment['CREATE_ADJUSTMENT_REASONS'].content.length; i++) {
                        $scope.adjustmentResponse.push($scope.adjustment['CREATE_ADJUSTMENT_REASONS'].content[i].name);
                    }
                    console.log($scope.adjustmentResponse);
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            $scope.getAttribute = function (value) {
                $scope.selectedUnit.calcType = value;
                console.log($scope.selectedUnit.calcType);
                $http({
                    method: 'GET',
                    url: APIJson.urls.PnLManagement.getAdjustmentField,
                    params: {
                        "type": $scope.selectedUnit.calcType
                    }
                }).then(function success(response) {
                        $scope.PnLHeaderList = [];
                        $scope.attributes = response.data;
                        for (var i = 0; i < $scope.attributes.fields.length; i++) {
                            $scope.PnLHeaderList.push($scope.attributes.fields[i]);
                        }
                        console.log($scope.PnLHeaderList);
                    },
                    function error(response) {
                        console.log("error:" + response);
                    });
            }
            $scope.setPnLHeader = function (value) {
                $scope.selectedUnit.PnLHeaderType = value;
            };
            $scope.setMonth = function (value) {
                $scope.selectedUnit.month = value;
            };
            $scope.setYear = function (value) {
                $scope.selectedUnit.year = value;
            };
            $scope.setAmount = function (value) {
                $scope.selectedUnit.adjustmentValue = value;
            }
            $scope.setCommentText = function (value) {
                $scope.selectedUnit.createCommentText = value;
            };
            $scope.setCreateComment = function (value) {
                $scope.selectedUnit.createComment = value;
            }
            $scope.createPnLAdjustment = function () {
                $rootScope.rootLoading = true;
                if ($scope.selectedUnit.unitDetail == null) {
                    toastr.error("Please Select a Unit .Please try again!");
                    return false;
                }
                if ($scope.selectedUnit.unitDetail == null) {
                    toastr.error("Please Select a Unit .Please try again!");
                    return false;
                }
                var obj = {
                    pnlHeaderName: $scope.selectedUnit.PnLHeaderType.name,
                    pnlHeaderDetail: $scope.selectedUnit.PnLHeaderType.detail,
                    pnlHeaderColumnName: $scope.selectedUnit.PnLHeaderType.columnName,
                    pnlHeaderType: $scope.selectedUnit.PnLHeaderType.type,
                    adjustmentType: $scope.selectedUnit.calcType,
                    adjustmentValue: $scope.selectedUnit.adjustmentValue,
                    unitId: $scope.selectedUnit.unitDetail.id,
                    status: "CREATED",
                    month: $scope.selectedUnit.month,
                    year: $scope.selectedUnit.year,
                    createdBy: $scope.userDetails.id,
                    creationTime: new Date(),
                    createComment: $scope.selectedUnit.createComment,
                    createCommentText: $scope.selectedUnit.createCommentText
                };

                console.log(obj);
                $http({
                    method: 'POST',
                    url: APIJson.urls.PnLManagement.createAdjustment,
                    data: obj
                }).then(function success(response) {
                        $scope.attributes = response.data;
                        toastr.success("Adjustment created Successfully!! with Adjustment Id:" + $scope.attributes.adjustmentId);
                        $rootScope.rootLoading = false;
                        reset();
                    },
                    function error(response) {
                        $rootScope.rootLoading = false;
                        toastr.warning("ERROR");
                        console.log("error:" + response);
                    });
                console.log($scope.selectedUnit);
            };

            function reset() {
                $scope.selectedUnit = {};
            }
        }]);