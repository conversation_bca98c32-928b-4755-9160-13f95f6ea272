'use strict';

angular.module('formsApp')
    .controller('requestClaimCtrl', ['$rootScope', '$scope', '$location', 'APIJson', '$cookieStore', '$http', 'AppUtil',
        'MetadataService', 'toastr', '$uibModal', '$stateParams', 'StorageUtil',
        function ($rootScope, $scope, $location, APIJson, $cookieStore, $http, AppUtil,
                  MetadataService, toastr, $uibModal, $stateParams, StorageUtil) {

            $scope.init = function () {
                $scope.showMenu = true;
                $scope.claimTypes = [
//                    {id: 1, name: "HAPPAY"},
                    {id: 1, name: "ICICI" },
                    /*{id: 4, name: "Reimbursements"},
                    {id: 5, name: "Part of Salary"}*/
                ];
                //$scope.selectedClaimType = $scope.claimTypes[0];
                $scope.walletTypes = [
                    {id: 1, name: "PETTY_CASH"},
                ];
                $scope.selectedAccount={};
                $scope.walletDataForPettyCash = {};
                $scope.selectedWalletType = $scope.walletTypes[0];
                $scope.claimStatuses = [
                    {id: 1, name: "CREATED"},
                    {id: 2, name: "APPROVED"},
                    {id: 3, name: "REJECTED"},
                    {id: 4, name: "SETTLED"}
                ];
                $scope.accounts = [];
                $scope.otpVerificationPending = true;
                $scope.userDetails = StorageUtil.getUserMeta();
                $scope.authDetails = {};
                $scope.otpDetails = {};
                $scope.loadedPettyCashWallet = false;
                setSelectedAccountType();

            };

            function setSelectedAccountType (){
                if(!AppUtil.isEmptyObject($rootScope.loginType)){
                    if($rootScope.loginType==='AGENT'){
                        $scope.selectedAccountType='EMPLOYEE';
                        $scope.associatedId = $scope.userDetails.id;
                    }else{
                        $scope.selectedAccountType='UNIT';
                        $scope.associatedId = $scope.userDetails.unitId;
                    }
                }
            }

            function getAccounts(){
                if(!AppUtil.isEmptyObject($scope.walletDataForPettyCash)){
                    $scope.accounts=[];
                    for(var wallet of $scope.walletDataForPettyCash){
                        $scope.accounts.push(wallet);
                    }
                }
            }

            $scope.dateOptions = {
                dateDisabled: function () {
                    return false;
                },
                formatYear: 'yyyy',
                maxDate: new Date(),
                minDate: $scope.minDate,
                startingDay: 1
            };

            $scope.verifyUser = function () {
                if ($scope.authDetails.password == null) {
                    toastr.error('Please fill password');
                } else {
                    var userObj = {
                        userId: $scope.userDetails.id,
                        password: $scope.authDetails.password,
                        unitId: $scope.userDetails.unitId,
                        terminalId: 0,
                        application: "FORMS_SERVICE"
                    };
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.users.login,
                        data: userObj
                    }).then(function success(response) {
                        if (response.status != 200 || response.data == null || response.data.sessionKeyId == null) {
                            toastr.error('Credentials are not correct!');
                        } else {
                            var user = response.data.user;
                            if (user.primaryContact != null) {
                                $scope.otpDetails.otpSent = true;
                                $scope.otpDetails.otpCount = 1;
                                $scope.primaryContact = user.primaryContact;
                                $scope.generateOTP();
                            } else {
                                toastr.error('Please Update user primary contact!');
                            }
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error('Authentication failed! Please make sure you are authorised for access wallet!');
                        $rootScope.rootLoading = false;
                    });
                }
            };

            function isDev() {
                return window.location.href.indexOf("dev.kettle.chaayos.com") >= 0;
            }

            $scope.generateOTP = function () {
                if (isDev()) {
                    $scope.otpDetails.otpCount++;
                } else {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.walletManagement.sendOTP,
                        data: {
                            contactNumber: $scope.primaryContact,
                            unit: AppUtil.getIdCodeName($scope.userDetails.unitId),
                            terminalId: 0,
                            otpPin: null,
                            transType: 'Wallet Transaction'
                        }
                    }).then(function success(response) {
                        if (response.status == 200 && response.data) {
                            $scope.otpDetails.otpCount++;
                        } else {
                            toastr.error('Network problem. Please try again!');
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        $rootScope.rootLoading = true;
                    });
                }
            };

            $scope.resendOtp = function () {
                if (isDev()) {
                    $scope.otpDetails.otpCount++;
                } else {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.walletManagement.resendOTP,
                        data: {
                            contactNumber: $scope.primaryContact,
                            unit: AppUtil.getIdCodeName($scope.userDetails.unitId),
                            terminalId: 0,
                            otpPin: null,
                            transType: 'Wallet Transaction'
                        }
                    }).then(function success(response) {
                        if (response.status == 200 && response.data) {
                            $scope.otpDetails.otpCount++;
                        } else {
                            toastr.error('Network problem. Please try again!');
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        $rootScope.rootLoading = true;
                    });
                }
            };

            $scope.verifyOTP = function () {
                if ($scope.authDetails.otp == null || $scope.authDetails.otp == undefined) {
                    toastr.error('Please Enter OTP to verify!');
                    return false;
                }
                if ($scope.authDetails.otp.length == 4) {
                    if (isDev()) {
                        if ($scope.authDetails.otp == '1234') {
                            $scope.otpDetails.otpVerified = true;
                            $scope.otpVerificationPending = false;
                            $scope.initTabView();
                        } else {
                            toastr.error('Incorrect One Time Password. Please enter again!');
                        }
                    } else {
                        $rootScope.rootLoading = true;
                        $http({
                            method: 'POST',
                            url: APIJson.urls.walletManagement.verifyOTP,
                            data: {
                                contactNumber: $scope.primaryContact,
                                unit: AppUtil.getIdCodeName($scope.userDetails.unitId),
                                terminalId: 0,
                                otpPin: $scope.authDetails.otp,
                                transType: 'Wallet Transaction'
                            }
                        }).then(function success(response) {
                            if (response.status == 200 && response.data) {
                                $scope.otpDetails.otpVerified = true;
                                $scope.otpVerificationPending = false;
                                $scope.initTabView();
                            } else {
                                toastr.error('Incorrect One Time Password. Please enter again!');
                            }
                            $rootScope.rootLoading = false;
                        }, function error(response) {
                            $rootScope.rootLoading = false;
                        });
                    }
                } else {
                    toastr.error("OTP must be of 4 characters.");
                }
            };

            $scope.initTabView = function () {
                $scope.tabData = [
                    {id: 1, name: "Wallet Details"},
                    {id: 2, name: "Request Claim"},
                    {id: 3, name: "Search Claims"},
                ];
                $scope.selectedTab = $scope.tabData[0];
                $scope.startDate = AppUtil.getDate(-2);
                $scope.endDate = AppUtil.getDate();
                $scope.walletData = null;
            };

            $scope.selectTab = function (tab) {
                $scope.selectedTab = tab;
                $scope.showMenu = false;
                if(tab.name == "Wallet Details") {
                    $scope.loadedPettyCashWallet = false;
                }
            };

            $scope.getWalletDetails = function() {
                $rootScope.rootLoading = true;
                   console.log($scope.loadedPettyCashWallet);
                $http({
                    method: 'GET',
                    url: APIJson.urls.walletManagement.walletDetailByLoginType + '/' + $scope.selectedAccountType,
                    params: {
                        associatedId: $scope.selectedAccountType == 'UNIT' ? $scope.userDetails.unitId : $scope.userDetails.id,
                    }
                }).then(function success(response) {
                    $scope.walletDataForPettyCash = response.data;
                    for(var i in response.data){
                        if(response.data[i].walletType == "PETTY_CASH"){
                            $scope.walletData = response.data[i];
                            break;
                        }
                    }
                    $scope.loadedPettyCashWallet = true;
                    console.log($scope.loadedPettyCashWallet);
                    getAccounts();
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    $rootScope.rootLoading = false;
                });
            };

            $scope.setDate = function(date, param) {
                $scope[param] = date;
            };

            $scope.setSelectedWalletType = function (selectedWalletType) {
                $scope.selectedWalletType = selectedWalletType;
            };

            $scope.setSelectedClaimType = function (selectedClaimType) {
                $scope.selectedClaimType = selectedClaimType;
            };

            $scope.startClaimRequest = function () {
                if ($scope.selectedClaimType.name == 'HAPPAY'|| $scope.selectedClaimType.name == 'ICICI') {
                    $scope.startHappayClaimProcess();
                }
            };

            $scope.startHappayClaimProcess = function (silent) {
                // if($scope.userDetails.unitId != null) {
                console.log("Account NO :::::::::", $scope.selectedAccount.accountNo.accountNo);
                if($scope.selectedAccount ==null ){
                    toastr.error("First select an account !");
                    return false;
                }
                    $scope.claimAmount = 0;
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.walletManagement.getClaimPendingVouchers,
                        data: JSON.stringify($scope.selectedAccount.accountNo.accountNo)
                    }).then(function success(response) {
                        if (response.status == 200 && response.data != null && Array.isArray(response.data)) {
                            $scope.claimPendingVouchers = response.data;
                            if ($scope.claimPendingVouchers.length == 0) {
                                if(!silent) {
                                    toastr.info("No vouchers available for claim.");
                                }
                            } else {
                                $scope.claimPendingVouchers.map(function (voucher) {
                                    $scope.claimAmount += voucher.expenseAmount;
                                });
                            }
                        } else {
                            toastr.error("Error getting claim pending vouchers.");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error("Error while getting expense header.");
                        $rootScope.rootLoading = false;
                    });
                // } else {
                  //  toastr.error("No unit selected. Please logout and login in Cafe Login mode.");
                //}
            };

            $scope.setSelectedClaimStatus = function (selectedClaimStatus) {
                $scope.selectedClaimStatus = selectedClaimStatus;
            };

            $scope.createHappayClaimRequest = function () {
                if($scope.claimAmount >= 1000) {
                    var voucherIds = [];
                    $scope.claimPendingVouchers.map(function (voucher) {
                        voucherIds.push(voucher.id);
                    });
                    if (voucherIds.length > 0) {
                        $rootScope.rootLoading = true;
                        $http({
                            method: 'POST',
                            url: APIJson.urls.claimManagement.addClaim,
                            data: {
                                claimType: $scope.selectedClaimType.name,
                                requestBy: {id: $scope.userDetails.id, name: "", code: ""},
                                voucherIds: voucherIds,
                                accountNo: $scope.selectedAccount.accountNo.accountNo
                            }
                        }).then(function success(response) {
                            if (response.status == 200 && response.data != null) {
                                $scope.claimId = response.data;
                                $scope.showMessage("New claim created with claim id " + $scope.claimId);
                                $scope.startHappayClaimProcess(true);
                            } else {
                                toastr.error("Error creating Happay claim.");
                            }
                            $rootScope.rootLoading = false;
                        }, function error(response) {
                            toastr.error("Error creating Happay claim.");
                            $rootScope.rootLoading = false;
                        });
                    } else {
                        toastr.error("No vouchers pending for claim.");
                    }
                } else {
                    toastr.error("Claim can be filled only if total expense is at least 1000 rupees.");
                }
            };

            $scope.findClaims = function () {
                /*if($scope.selectedClaimType.name == "HAPPAY" && $scope.userDetails.unitId == null) {
                    toastr.error("No unit selected. Please logout and login again in Cafe Login mode.");
                } else*/
                if($scope.startDate == null) {
                    toastr.error("Please select start date.");
                } else if($scope.endDate == null) {
                    toastr.error("Please select end date.");
                } else {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.claimManagement.findClaims,
                        data: {
                            startDate: $scope.startDate,
                            endDate: AppUtil.addDate($scope.endDate, 1),
                            type: $scope.selectedClaimType != null ? $scope.selectedClaimType.name : null,
                            status: $scope.selectedClaimStatus != null ? $scope.selectedClaimStatus.name : null,
                            unitIds: [$scope.selectedAccount.accountNo.accountNo]
                        }
                    }).then(function success(response) {
                        if (response.status == 200 && response.data != null && Array.isArray(response.data)) {
                            $scope.claimList = response.data;
                        } else {
                            toastr.error("Error getting claims.");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error("Error getting claims.");
                        $rootScope.rootLoading = false;
                    });
                }
            };

            $scope.downloadClaim = function (claimId) {
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.claimManagement.downloadClaim,
                    data: claimId,
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/pdf'
                    }
                }).then(function success(response) {
                    if (response && response.data != null) {
                        var fileName = "claimDetail" + claimId + ".pdf";
                        var blob = new Blob([response.data], {
                            type: 'c'
                        }, fileName);
                        saveAs(blob, fileName);
                    } else {
                        toastr.error("Could not download claim.");
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error("Error getting claims.");
                    $rootScope.rootLoading = false;
                });
            };

            $scope.addHappayModal = function (claim) {
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'addHappayModal.html',
                    controller: 'AddHappayModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    resolve: {}
                });
                modalInstance.result.then(function (happayId) {
                    $scope.updateHappayId(claim, happayId);
                }, function () {
                    //console.log("closed::::::::::::::::::::::::::::::::::::::")
                });
            };

            $scope.updateHappayId = function (claim, happayId) {
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.claimManagement.setHappayToClaim + "?happayId=" + happayId + "&claimId=" + claim.claimId
                    + "&userId=" + $scope.userDetails.id
                }).then(function success(response) {
                    if (response.status == 200 && response.data) {
                        toastr.success("Happay id updated to claim successfully.");
                        $scope.findClaims();
                    } else {
                        toastr.error("Error getting claims.");
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error("Error getting claims.");
                    $rootScope.rootLoading = false;
                });
            };

            $scope.openActionModal = function (claim, action) {
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'claimAction.html',
                    controller: 'ClaimActionModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    resolve: {
                        actionData: function () {
                            return {
                                action: action
                            }
                        }
                    }
                });
                modalInstance.result.then(function (comment) {
                    if (action == 'ACKNOWLEDGE') {
                        $scope.acknowledgeClaim(claim, comment);
                    }
                }, function () {
                    //console.log("closed::::::::::::::::::::::::::::::::::::::")
                });
            };

            $scope.acknowledgeClaim = function (claim, comment) {
                $rootScope.rootLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.claimManagement.acknowledgeClaim,
                    data: {
                        requestBy: {id: $scope.userDetails.id},
                        comment: comment,
                        claimId: claim.claimId
                    }
                }).then(function success(response) {
                    if (response.status == 200 && response.data) {
                        toastr.success("Claim acknowledged successfully.");
                        $scope.findClaims();
                    } else {
                        toastr.error("Error acknowledging claim.");
                    }
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    toastr.error("Error acknowledging claim.");
                    $rootScope.rootLoading = false;
                });
            };

            $scope.viewClaimVouchers = function (claim) {
                if(claim.vouchers.length > 0) {
                    $scope.showVoucherModal(claim);
                } else {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.claimManagement.getClaimVouchers,
                        data: claim.claimId
                    }).then(function success(response) {
                        if (response.status == 200) {
                            if (response.data.length > 0) {
                                claim.vouchers = response.data;
                                $scope.showVoucherModal(claim);
                            } else {
                                toastr.error("No vouchers found.");
                            }
                        } else {
                            toastr.error("Error getting claim vouchers.");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error("Error getting claim vouchers.");
                        $rootScope.rootLoading = false;
                    });
                }
            };

            $scope.viewClaimLogs = function (claim) {
                if(claim.claimLogs.length > 0) {
                    $scope.showLogModal(claim);
                } else {
                    $rootScope.rootLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.claimManagement.getClaimLogs,
                        data: claim.claimId
                    }).then(function success(response) {
                        if (response.status == 200) {
                            if (response.data.length > 0) {
                                claim.claimLogs = response.data;
                                $scope.showLogModal(claim);
                            } else {
                                toastr.error("No logs found.");
                            }
                        } else {
                            toastr.error("Error getting claim logs.");
                        }
                        $rootScope.rootLoading = false;
                    }, function error(response) {
                        toastr.error("Error acknowledging claim.");
                        $rootScope.rootLoading = false;
                    });
                }
            };

            $scope.showVoucherModal = function (claim) {
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'claimVoucher.html',
                    controller: 'ClaimVoucherModalCtrl',
                    backdrop: 'static',
                    size: 'lg',
                    keyboard: false,
                    resolve: {
                        data: function () {
                            return {
                                claim: claim
                            }
                        }
                    }
                });
                modalInstance.result.then(function () {
                }, function () {
                });
            };

            $scope.showLogModal = function (claim) {
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'claimLog.html',
                    controller: 'ClaimVoucherModalCtrl',
                    backdrop: 'static',
                    size: 'lg',
                    keyboard: false,
                    resolve: {
                        data: function () {
                            return {
                                claim: claim
                            }
                        }
                    }
                });
                modalInstance.result.then(function () {
                }, function () {
                });
            };

            $scope.showMessage = function (message, title) {
                var modalInstance = $uibModal.open({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'messageModal.html',
                    controller: 'MessageModalCtrl',
                    backdrop: 'static',
                    size: 'lg',
                    keyboard: false,
                    resolve: {
                        message: function () {
                            return {
                                title: title,
                                detail:message
                            }
                        }
                    }
                });
                modalInstance.result.then(function () {
                }, function () {
                });
            };


            ///////////////////////////mobile view controls//////////////////////////

            $scope.backToMenu = function () {
                $scope.showMenu = true;
            }


        }]).controller('AddHappayModalCtrl', ['$scope', 'toastr', '$uibModal', '$uibModalInstance',
    function ($scope, toastr, $uibModal, $uibModalInstance) {

        $scope.init = function () {
            $scope.happayId = null;
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.addHappayId = function () {
            $uibModalInstance.close($scope.happayId);
        }

    }]).controller('ClaimActionModalCtrl', ['$scope', 'toastr', '$uibModal', '$uibModalInstance', 'actionData',
    function ($scope, toastr, $uibModal, $uibModalInstance, actionData) {

        $scope.init = function () {
            $scope.claimAction = actionData.action;
            $scope.comment = null;
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.submit = function () {
            if ($scope.claimAction == 'REJECT' && $scope.comment == null) {
                toastr.error("Please add comment.");
            } else {
                $uibModalInstance.close($scope.comment);
            }
        }

    }]).controller('ClaimVoucherModalCtrl', ['$scope', '$uibModal', '$uibModalInstance', 'data',
    function ($scope, $uibModal, $uibModalInstance, data) {

        $scope.init = function () {
            $scope.claim = data.claim;
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss('cancel');
        };

    }]).controller('MessageModalCtrl', ['$scope', '$uibModal', '$uibModalInstance', 'message',
    function ($scope, $uibModal, $uibModalInstance, message) {

        $scope.init = function () {
            $scope.message = message;
            if(!message.title) {
                message.title = "Message";
            }
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss('cancel');
        };

    }]);
