<style>
    td:nth-child(2) {
        text-align: right;
    }
    .total{
        font-size: 26px;
        color: #005a00;
        font-weight: bold;
    }
    .walletHead{
        border: #ccc 1px solid;
        border-bottom: none;
        padding: 10px;
        margin-bottom: 0;
        font-weight: bold;
    }
    .walletHead > span{
        float: right;
        color: #000;
    }
    .walletHead > span > span {
        float: right;
        font-style: italic;
        font-size: 12px;
        color: #5e5e5e;
    }
    td{
        font-size: 18px;
    }
    td span{
        font-style: italic;
        font-size: 12px;
        color: #5e5e5e;
    }
</style>
<div class="row">
    <div class="col-xs-12">
        <h2 class="page-heading walletHead">
            {{selectedUnit.name}} Wallet
            <span>Wallet Limit: {{walletData.openingAmount}}<br /><span>Maximum Limit to which wallet can be recharged</span></span>
        </h2>
        <table class="table table-striped table-bordered">
            <tr>
                <td>
                    Available Balance<br />
                    <span>Balance available for issue</span>
                </td>
                <td>{{walletData.currentBalance}}</td>
            </tr>
            <tr>
                <td>
                    Approved Amount<br />
                    <span>Amount approved by finance but yet to be claimed</span>
                </td>
                <td>{{walletData.approvedAmount}}</td>
            </tr>
            <tr>
                <td>
                    Issued Amount<br />
                    <span>Amount issued but pending for expenses submission</span>
                </td>
                <td>{{walletData.issuedAmount}}</td>
            </tr>
            <tr>
                <td>
                    Rejected Amount<br />
                    <span>Amount rejected by finance</span>
                </td>
                <td>{{walletData.rejectedAmount}}</td>
            </tr>
            <tr>
                <td>
                    Pending Approval<br />
                    <span>Approval Pending from finance</span>
                </td>
                <td>{{walletData.pendingApproval}}</td>
            </tr>
            <tr class="total">
                <td>
                    Total Amount<br />
                    <span>Total amount including all above</span>
                </td>
                <td>{{walletData.totalAmount}}</td>
            </tr>
        </table>
        <div style="text-align: center;">
            <img style="margin-top: 10px; max-width: 100%;" src="/forms-ui/img/petty_cash_flow.png" />
        </div>

    </div>
</div>