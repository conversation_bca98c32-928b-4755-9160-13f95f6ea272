<style>
    .reimbursed {
        background: #d8d31073;
    }

    .not-reimbursed {
        background: #f5f5f5;
    }
</style>
<div class="panel-heading" style="font-size: 17px;"
     data-ng-class="{'not-reimbursed' : voucher.isReimbursed != true , 'reimbursed' : voucher.isReimbursed == true}">
    <div class="row row-spacing">
        <div class="col-xs-6">
            Request Id : <label>{{voucher.generatedVoucherId}}</label>
        </div>
        <div class="col-xs-6">
            Current Status : {{voucher.currentStatus}}
            <span data-ng-if="voucher.isReimbursed == true">(Reimbursed)</span>
        </div>
    </div>
    <div class="row row-spacing">
        <div class="col-xs-6">Unit Name: {{voucher.expenseType}}</div>
    </div>
    <div class="row row-spacing">
        <div class="col-xs-6">PnL Header: {{voucher.expenseType}}</div>
    </div>
    <div class="row row-spacing" data-ng-if="actionDetail.type == 'settle'">
        <div class="col-xs-6">
            Adjustment Amount : {{voucher.issuedAmount}}
            <span data-ng-class="{'text-success' : voucher.expenseAmount <= voucher.issuedAmount ,
				'text-danger' : voucher.expenseAmount > voucher.issuedAmount}">
                ({{voucher.expenseAmount != null ? voucher.expenseAmount : '-'}})
            </span>
        </div>
        <div class="col-xs-6">Issue Time : {{voucher.issuedTime | date :'dd/MM/yyyy hh:mm:ss a'}}</div>
    </div>
    <div class="row row-spacing">
        <div class="col-xs-6">Adjustment Type: {{voucher.expenseType}}</div>
    </div>
    <div class="row row-spacing" data-ng-if="stateDetails.actionBy == 'AM'">
        <div class="col-xs-6">
            Issued By : {{voucher.issuedBy.name}} - {{voucher.issuedBy.code}}
        </div>
    </div>
</div>