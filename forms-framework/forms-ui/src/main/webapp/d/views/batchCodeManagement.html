<style>
	.row-spacing {
		margin-top: 10px;
	}

	.batchCode-container {
		padding-right: 5px;
		padding-left: 5px;
		margin-right: 5%;
		margin-left: 5%;
	}

	.errorText {
		color : #b92c28;
	}
</style>
<div class="batchCode-container" data-ng-init="init()">
	<div class="row">
		<div class="col-xs-12">
			<h2 class="text-center" style="font-family: 'typewriter';">
				Pack Code Generation Form</h2>
		</div>
	</div>

	<form name="generateBatchCodesForm">
		<div class="row" ng-if="errorDetail != null && errorDetail.errorMsg != null">
			<div class="col-xs-12 alert free-kettle  text-center">{{errorDetail.errorMsg}}</div>
		</div>
		<div class="row">
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label for="selectedUnit" class="control-label">Select Unit</label>
							<select class="form-control" id="selectedUnit"
									name="selectedUnit" data-ng-model="selectedUnit"
									data-ng-options="unit as unit.name for unit in units"
									required="required"
									data-ng-change="setBatchUnit(selectedUnit)">
							</select>
							<div data-ng-if="isUnitMissing != undefined && !isUnitMissing && unitPrefix != ''"><p>{{unitPrefix}}</p></div>
						</div>
					</div>
					<div class="col-xs-12 errorText" data-ng-if="isUnitMissing != undefined && isUnitMissing">
						Please select a unit with a valid prefix
					</div>
				</div>
			</div>

			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label for="selectedProduct" class="control-label">Select Product</label>
							<select class="form-control" id="selectedProduct"
									name="selectedProduct" data-ng-model="selectedProduct"
									data-ng-options="product as product.name for product in unitProducts"
									required="required"
									data-ng-change="setBatchProduct(selectedProduct)">
							</select>
							<div data-ng-if="isProductMissing != undefined && !isProductMissing && productPrefix != ''"><p>{{productPrefix}}</p></div>
						</div>
					</div>
					<div class="col-xs-12 errorText" data-ng-if="isProductMissing != undefined && isProductMissing">
						Please select a product with a valid prefix
					</div>
				</div>
			</div>

			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label for="batchCodeCount" class="control-label">Number of Codes</label>
							<input type="number"
								   data-ng-model="batchCodeCount"
								   data-ng-change="validateBatchCodeCount()"
								   class='form-control' required="required" />
						</div>
						<div class="col-xs-6 errorText" data-ng-if="isCodeCountMissing != undefined && isCodeCountMissing">
							Please enter number of codes to generate.
						</div>
						<div class="col-xs-6 errorText" data-ng-if="isCodeCountInvalid != undefined && isCodeCountInvalid">
							Pack code count is not valid
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label for="batchCodeLength" class="control-label">Length of a Code (Current 6)</label>
							<input type="number"
								   data-ng-disabled="true"
								   data-ng-model="batchCodeLength"
								   data-ng-change="validateBatchCodeLength()"
								   class='form-control' required="required" />
						</div>
						<div class="col-xs-6 errorText" data-ng-if="isCodeLengthMissing != undefined && isCodeLengthMissing">
							Please enter length of a code to be generated.
						</div>
						<div class="col-xs-6 errorText" data-ng-if="isCodeLengthInvalid != undefined && isCodeLengthInvalid">
							Pack code's length is not valid
						</div>
					</div>
				</div>
			</div>

			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<button class="btn btn-primary pull-left"
									data-ng-click="generatePackCodes()" style="margin-top: 29px;"
									data-ng-disabled="false">Generate</button>
						</div>
					</div>
				</div>
			</div>

			<div class="col-xs-12"
				data-ng-if="isDownloadButtonActive != undefined && isDownloadButtonActive">
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<p>
								{{generatedBatchCode}} Batch Generated Successfully</a>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<a class="btn btn-info" ng-href="{{packCodesFilePath}}"
								download="{{packCodesFilePath}}">Download</a>
						</div>
					</div>
				</div>
			</div>

		</div>
	</form>

</div>