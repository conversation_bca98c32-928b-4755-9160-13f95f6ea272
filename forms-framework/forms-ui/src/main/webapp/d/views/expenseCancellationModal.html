<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header" data-ng-init="initCancellation()">
	<div flash-message="5000"></div>
	<h3 class="text-center">Expense Cancel Details</h3>
</div>

<div class="modal-body">
	<form novalidate role="form" name="expenseCancelForm" autocomplete="off">
		<div class="row" style="margin: 15px;">
			<div class="col-xs-3">
				<label>Expense Type</label>
			</div>
			<div class="col-xs-3">
				<label>{{expenseDetail.expenseType}}</label>
			</div>
			<div class="col-xs-3">
				<label>Expense Amount</label>
			</div>
			<div class="col-xs-3">
				<label>{{expenseDetail.amount}}</label>
			</div>
		</div>
		<div class="row" style="margin: 15px;">
			<div class="col-xs-3">
				<label for="comment">Reason:</label>
			</div>
			<div class="col-xs-9">
				<textarea style="width: 100%;" rows="5" name="comment" id="comment"
					ng-model="cancelReason" required>
                        </textarea>
				<div ng-messages="expenseCancelForm.comment.$error"
					style="color: maroon" role="alert">
					<div ng-if="orderCancelForm.comment.$touched">
						<div ng-message="required">Reason can't be empty field</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>

<div class="modal-footer">
	<div class="btn-group">
		<a href="#" class="btn btn-danger" ng-click="cancel()">Cancel</a>
		<a href="#" class="btn btn-default" ng-click="cancelExpenseDetail(cancelReason)"
			ng-disabled="!expenseCancelForm.$valid">Submit</a>
	</div>
</div>
