<div class="col-xs-12">
    <div class="row" data-ng-if="(action.actionType == 'reject')">
        <div class="col-xs-12">
            <div style="padding: 5px 10px;" data-ng-repeat="reason in rejectionReasons" data-ng-if="reason.validationType == 'REJECTION'">
                <input id="{{reason.id}}" type="checkbox" data-ng-model="reason.checked" />
                <label for="{{reason.id}}">{{reason.validationName}}</label>
            </div>
        </div>
    </div>
    <div class="row" style="background: #ddd; margin: 10px;" data-ng-if="(action.actionType == 'approve')">
        <div class="col-xs-12">
            <h3>Please check following errors before approval.</h3>
            <div style="padding: 5px 10px;" data-ng-repeat="reason in rejectionReasons">
                {{reason.validationName}}
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12 form-group">
            <label for="comment" class="control-label">{{searchVoucher.actionLabel}}
                Comment </label>
            <textarea style="width: 100%;" rows="5" name="comment"
                      id="comment" data-ng-model="voucherDetail.actionComment"
                      required="required"> </textarea>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-4 " style="margin-bottom: 5px;">
            <button class="btn " data-ng-click="updateVoucher()"
                    data-ng-class="{'btn-danger' : action.actionType ==  'approve',
										'btn-success' : action.actionType == 'reject'}">
                {{searchVoucher.actionLabel}}
                Voucher
            </button>
        </div>
    </div>
</div>