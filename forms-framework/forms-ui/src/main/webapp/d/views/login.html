<div class="loginContainer" data-ng-init="init()">
    <div data-ng-if="error" class="alert alert-danger" role="alert">{{errorMessage}}</div>
    <div data-ng-if="success" class="alert alert-success" role="alert">{{successMessage}}</div>
    <div class="btn-group btn-group-justified">
        <div class="btn-group">
            <button type="button" data-ng-class="{'btn btn-primary':loginType=='AGENT','btn btn-default':loginType!='AGENT'}"
            data-ng-click="selectLoginType('AGENT')">Agent Login</button>
        </div>
        <div class="btn-group">
            <button type="button" data-ng-class="{'btn btn-default':loginType=='AGENT','btn btn-primary':loginType!='AGENT'}"
                    data-ng-click="selectLoginType('CAFE')">Cafe Login</button>
        </div>
    </div>
    <h2>Login</h2>
    <p>Please submit your user id and password to login to the Audit Service dashboard.</p>
    <form class="loginForm" autocomplete="off" data-ng-submit="login()">
        <div class="form-group">
            <label for="userId" class="sr-only">User Id</label>
            <input type="text" id="userId" class="form-control" placeholder="User Id" autofocus="" data-ng-model="userId"
                   ng-change="fetchOutletsForEmployee()" />
        </div>
        <div class="form-group">
            <label for="inputPassword" class="sr-only">Password</label>
            <input type="password" id="inputPassword" class="form-control" placeholder="Password" autocomplete="" data-ng-model="password" />
        </div>
        <div class="form-group" data-ng-if="loginType=='CAFE'">
            <label for="unitId" class="sr-only">Select Unit</label>
            <select class="form-control" id="unitId" ng-model="unitId" data-ng-change="selectOutlet(unitId)"
                    data-ng-options="outlet.id as outlet.name for outlet in outletList">
            </select>
        </div>
        <button class="btn btn-lg btn-success btn-block" type="submit">
            <span data-ng-hide="loading">Sign in</span>
            <div class="loader xs primary lfast" data-ng-show="loading"></div>
        </button>
    </form>

</div>