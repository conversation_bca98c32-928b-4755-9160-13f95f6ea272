<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .expense-container {
        padding-right: 5px;
        padding-left: 5px;
        margin-right: 5%;
        margin-left: 5%;
    }

    .active-status {
        color: green;
    }

    .cancel-status {
        color: red;
    }

    .selected-view {
        background: green;
    }

    .free-kettle {
        color: #EFE8E8;
        background-color: #D82F2F;
        border-color: #ebccd1;
        font-size: 20px;
        padding: 10px;
        margin-bottom:0px;
    }
    .cost-label{
        font-size: 25px;
        color: green !important;
        border: 2px solid darkblue;
        margin-top: 2%;
    }
    .region-card {
        font-weight: 600;
        color: green;
    }

    .delete-unit {
        color: red;
        font-size: 20px;
        float: right;
    }
</style>
<div  class="expense-container">
    <div data-ng-init="init()" class="row">
        <div class="col-xs-12">
            <h2 class="text-center" style="font-family: 'typewriter';">
                PnL Adjustment Creation</h2>
        </div>
    </div>
    <form>
        <div class="row">
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="control-label">Select
                            Unit </label>
                        <input type="text" data-ng-model="selectedUnit.unitDetail" class="form-control"
                               uib-typeahead="unit as unit.name for unit in units | filter:$viewValue"
                               typeahead-show-hint="true" typeahead-min-length="0"
                               typeahead-append-to-body="true"
                               typeahead-on-select="updateUnitAdd(selectedUnit.unitDetail)"/>
                    </div>
                </div>
                <div class="col-xs-6 form-group">
                    <label class="control-label" >Calculation Type</label>
                    <select data-ng-model="selectedUnit.adjustmentType" class='form-control'
                            data-ng-options="type for type in calculationType" data-ng-change="getAttribute(selectedUnit.adjustmentType)"
                            required="required">
                    </select>
                </div>

            </div>

            <div>
                <div class="col-xs-3">
                    <label class="control-label">Month
                    </label>
                    <select class="form-control"
                            select class="form-control" data-ng-model="selectedUnit.month"
                            data-ng-options="month for month in months"
                            required="required" data-ng-change="setMonth(selectedUnit.month)">
                    </select>
                </div>
                <div class="col-xs-3">
                    <label class="control-label">Year
                    </label>
                    <select class="form-control"
                            select class="form-control" data-ng-model="selectedUnit.year"
                            data-ng-options="year for year in years"
                            required="required" data-ng-change="setYear(selectedUnit.year)">
                    </select>
                </div>

                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="control-label">
                            PnL Header Type
                        </label>
                        <input type="text" data-ng-model="selectedUnit.PnLHeaderType" class="form-control"
                               uib-typeahead="header as header.detail for header in PnLHeaderList | filter:$viewValue"
                               typeahead-show-hint="true" typeahead-min-length="0"
                               typeahead-append-to-body="true"
                               typeahead-on-select="setPnLHeader(selectedUnit.PnLHeaderType)"/>
                    </select>
                    </div>
                </div>
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-6">
                        <div class="form-group">
                            <label class="control-label">Amount
                            </label> <input type="number" data-ng-model="selectedUnit.adjustmentValue"
                                            class='form-control' required="required"
                                            data-ng-change="setAmount(selectedUnit.adjustmentValue)"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">
                        Adjustment Reason
                    </label> <select class="form-control" data-ng-model="selectedUnit.createComment"
                                     data-ng-options="response for response in adjustmentResponse"
                                     data-ng-change="setCreateComment(selectedUnit.createComment)">
                </select>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-12">
                        <label class="control-label">Adjustment Comment </label>
                        <textarea style="width: 100%;" rows="5" data-ng-model="selectedUnit.createCommentText"
                            data-ng-change="setCommentText(selectedUnit.createCommentText)">
					</textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <button class="btn btn-primary pull-left"
                                    data-ng-click="createPnLAdjustment()" style="margin-top: 29px;">Submit</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>


