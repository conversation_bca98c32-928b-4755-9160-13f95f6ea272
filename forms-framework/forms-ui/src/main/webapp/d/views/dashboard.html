<div data-ng-init="init()">
    <nav class="navbar navbar-default navbar-static-top">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle collapsed" data-toggle="collapse"
                        data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="#">Chaayos <span data-ng-if ="selectedUnit != null">{{selectedUnit.name}}</span></a>
            </div>

            <!-- Collect the nav links, forms, and other content for toggling -->
            <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
                <ul class="nav navbar-nav navbar-right" style="margin: 0; padding: 0 10px; cursor: pointer;">
                    <li class="dropdown">
                        <div style="padding: 10px 0;" uib-dropdown is-open="status.isopen">
                            <span uib-dropdown-toggle ng-disabled="disabled">
                                {{userData.name}} <span class="caret"></span>
                            </span>
                            <ul class="dropdown-menu" uib-dropdown-menu role="menu" aria-labelledby="single-button">
                                <li role="menuitem"><a href="#" data-ng-click="logout()">Logout</a></li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div><!-- /.navbar-collapse -->
        </div><!-- /.container-fluid -->
        <div class="navbar-default sidebar" role="navigation">
            <div class="sidebar-nav navbar-collapse">
                <ul class="nav" id="side-menu">
                    <li data-ng-click="slideMenu($event)" acl-menu="FSAM">
                        <a>Audit Management <span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a data-ng-click="startAudit()" acl-sub-menu="FSSTAM"> Start Audit </a>
                                <a data-ng-click="searchAudit()" acl-sub-menu="FSSEAM">Search Audit </a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)"  acl-menu="FSME">
                        <a>Maintenance Expense <span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a  ui-sref="dashboard.expense({expenseType : 'maintenance', viewType: 'add'})" acl-sub-menu="FSMEAE"> Add Expense </a>
                                <a  ui-sref="dashboard.expense({expenseType : 'maintenance', viewType: 'display'})" acl-sub-menu="FSMEVE">View Expense </a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)"  acl-menu="FSMRE">
                        <a> Marketing Expense<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                 <a  ui-sref="dashboard.expense({expenseType : 'marketing', viewType: 'add'})" acl-sub-menu="FSMREAE"> Add Expense </a>
                                <a ui-sref="dashboard.expense({expenseType : 'marketing', viewType: 'display'})"  acl-sub-menu="FSMREVE">View Expense </a>
                            </li>
                        </ul>
                    </li>
                     <li data-ng-click="slideMenu($event)"  acl-menu="FSWM">
                        <a> Warning Details<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                 <a  ui-sref="dashboard.warningDetail({audit : null , viewType: 'add', warningId : null, actionBy : null})"  acl-sub-menu="FSWMIWWA">Issue Warning Letter</a>
                                <a ui-sref="dashboard.searchWarningDetail({searchParams: null})" >Search Issued Warning </a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSPCMC">
                        <a>Voucher/Claim Request<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a  data-ui-sref="dashboard.createVoucher">Create Voucher</a>
                                <a  data-ui-sref="dashboard.requestClaim">Request Claims</a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSPCMAM">
                        <a>Voucher Management AM<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a data-ui-sref="dashboard.manageVoucher({actionBy : 'AM'})" >Manage Voucher - AM</a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSPCMF">
                        <a>Wallet Management Finance<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a data-ui-sref="dashboard.manageClaim">Manage Claims</a>
                                <a data-ui-sref="dashboard.manageVoucher({actionBy : 'FM'})" >Manage Voucher</a>
                                <a data-ui-sref="dashboard.manageWallet" >Manage Wallet</a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSBCM">
                        <a> Batch Code Management<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                        	<li>
                        		<a ui-sref="dashboard.manageBatchCodes({searchParams: null})" >Generate Pack Codes</a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSCL">
                        <a>Checklist<span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a ui-sref="dashboard.forms({formType:'checkList'})" acl-sub-menu="FSCCL" >Cafe CheckList</a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" acl-menu="FSPNL">
                        <a>PnL Adjustment <span class="arrow"></span></a>
                        <ul class="nav nav-second-level collapse">
                            <li>
                                <a data-ui-sref="dashboard.createPnLAdjusment({actionBy : 'AM'})" acl-sub-menu="FSPNL_CR"> Create Adjustment </a>
                                <a data-ui-sref="dashboard.processPnLAdjustment({actionBy : 'AM'})" acl-sub-menu="FSPNL_ARA">Approve/Reject Adjustment</a>
                            </li>
                        </ul>
                    </li>
                    <li data-ng-click="slideMenu($event)" data-ng-if="loginType==='AGENT'" acl-menu="FSRECON">
                        <a data-ui-sref="dashboard.reconciliation">Reconciliation</a>
                    </li>
                </ul>
            </div>
            <!-- /.sidebar-collapse -->
        </div>
    </nav>
    <div id="page-wrapper">
        <div ui-view></div>
    </div>
</div>


