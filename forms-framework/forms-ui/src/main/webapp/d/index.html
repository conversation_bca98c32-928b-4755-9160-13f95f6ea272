<!doctype html>

<html>
<head>
    <meta charset="utf-8">
    <title><PERSON><PERSON><PERSON></title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="description" content="kettle POS">
    <meta name="author" content="@chaayos">

    <link rel="icon" type="image/png" sizes="32x32" href="../img/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="../img/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../img/favicon-16x16.png">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

    <link rel="stylesheet" href="../css/bootstrap.min.css" />
    <link rel="stylesheet" href="../css/angular-toastr.min.css" />
    <link rel="stylesheet" href="../css/styledesktop.css" />
      <link rel="stylesheet" href="../css/fontawesome-all.min.css" />

    <script type="application/javascript">
        window.version = ".";
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|BB|PlayBook|IEMobile|Windows Phone|Kindle|Silk|Opera Mini/i.test(navigator.userAgent)) {
            window.device = "mobile";
        }else{
            window.device = "desktop";
        }
    </script>
</head>
<body>


<div data-ng-app="formsApp">
    <div data-ui-view></div>
    <div data-ng-if="rootLoading" class="rootLoader">
        <div class="text-center" style="margin-top: 100px">
            <div class="loader xl"></div>
        </div>
    </div>
</div>

<script src="../js/libs/jquery.min.js"></script>
<script src="../js/libs/angular.min.js"></script>
<script src="../js/libs/angular-animate.min.js"></script>
<script src="../js/libs/angular-cookies.min.js"></script>
<script src="../js/libs/angular-resource.min.js"></script>
<script src="../js/libs/angular-route.min.js"></script>
<script src="../js/libs/angular-sanitize.min.js"></script>
<script src="../js/libs/angular-ui-router.min.js"></script>
<script src="../js/libs/lodash.min.js"></script>
<script src="../js/libs/FileSaver.min.js"></script>
<link href="../js/libs/select2/select2.min.css" rel="stylesheet">
<script src="../js/libs/select2/select2.min.js"></script>
<script src="../js/libs/select2/ui-select2.js"></script>
<script src="../js/libs/angularjs-dropdown-multiselect.min.js"></script>

<script src="../js/libs/ui-bootstrap-tpls-2.4.0.min.js"></script>
<script src="../js/libs/angular-toastr.tpls.min.js"></script>

<!-- App core & templates -->
<script src="../js/app.js?v=1.7"></script>

<!-- controllers -->
<script src="../js/controllers/loginCtrl.js"></script>
<script src="../js/controllers/dashboardCtrl.js"></script>
<script src="../js/controllers/formsCtrl.js"></script>
<script src="../js/controllers/auditCtrl.js"></script>
<script src="../js/controllers/auditPreviewModalCtrl.js"></script>
<script src="../js/controllers/searchAuditCtrl.js"></script>
<script src="../js/controllers/expenseTrackingController.js"></script>
<script src="../js/controllers/warningDetailCtrl.js"></script>
<script src="../js/controllers/searchWarningDetailCtrl.js"></script>
<script src="../js/controllers/warningImagePreviewCtrl.js"></script>
<script src="../js/controllers/homeCtrl.js"></script>
<script src="../js/controllers/autoLoginCtrl.js"></script>
<script src="../js/controllers/createVoucherCtrl.js"></script>
<script src="../js/controllers/manageVoucherCtrl.js"></script>
<script src="../js/controllers/requestClaimCtrl.js"></script>
<script src="../js/controllers/manageClaimCtrl.js"></script>
<script src="../js/controllers/manageWalletCtrl.js"></script>
<script src="../js/controllers/batchCodeManagementCtrl.js"></script>
<script src="../js/controllers/createPnlAdjusmentCtrl.js"></script>
<script src="../js/controllers/processPnLAdjustmentCtrl.js"></script>
<script src="../js/controllers/reconciliationCtrl.js"></script>

<!-- services -->
<script src="../js/services/AppUtil.js?v=1.7"></script>
<script src="../js/services/APIJson.js?v=1.7"></script>
<script src="../js/services/StorageUtil.js"></script>
<script src="../js/services/MetadataService.js"></script>
<script src="../js/services/WalletService.js"></script>

</body>
</html>
