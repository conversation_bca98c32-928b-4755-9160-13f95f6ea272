#page-wrapper{
    margin-top: 50px;
}
/*************** login styles ****************/
.loginContainer{
    padding: 30px;
    max-width: 500px;
    margin: auto;
}

/************** forms page styles ************/

.list{
}

.listItem{
    border-bottom: #f3f3f3 1px solid;
    padding:15px;
    background: #fff;
    position: relative;
}
.listItem:first-child{
    border-top: #f3f3f3 1px solid;
}
.listItem .head{
    font-size: 18px;
    margin-bottom: 8px;
}
.listItem .desc{
    color: #8a8a8a;
    font-size: 14px;
}


/************** audit page styles ******************/
.pageHeader{
    background: #4CAF50;
    color: #fff;
    padding: 0;
    font-size: 16px;
    margin-bottom: 5px;
}
.auditFormBody .nav-tabs{
    display: none;
}
.auditFormFooter {
    position: fixed;
    bottom: 0;
    width: 100%;
    left: 0;
    background: #dedede;
}
.progress {
    height: 5px;
    margin-bottom: 0;
    overflow: hidden;
}

.autocompletePanel{
    position: fixed;
    top:0;
    left:0;
    width: 100%;
    height: 100%;
    background: #fff;
    z-index: 9999;
}
.autocompletePanel .aPanelHead{
    height: 40px;
}
.autocompletePanel .aPanelBack{
    padding: 10px 15px;
    float: left;
}
.autocompletePanel .aPanelClear{
    float: right;
    padding: 10px;
}
.autocompletePanel .aPanelName{
    line-height: 40px;
    float: left;
}
.autocompletePanel .aPanelSearch{

}
.autocompletePanel .searchList{
    overflow: auto;
    position: absolute;
    top: 74px;
    bottom: 0;
    left: 0;
    right: 0;
}
.autocompletePanel .listItem{
    padding: 10px;
    list-style: none;
    margin-left: -40px;
}

/**
**Wallet Css
*/

.wallet-container {
	padding: 7px;
}

.menu-highlight {
	background-color: #777;
}

.page-heading {
	font-size: 26px;
	color: red;
	margin-bottom: 13px;
}

.tab-container {
	margin-left: 7px;
	margin-right: 7px;
	margin-top: 10px;
}

.label-font {
	font-size: 20px;
}

.row-spacing {
	margin-top: 10px;
}

.wallet-detail-label {
	font-size: 15px;
	color: blue;
}

.wallet-detail-heading {
	font-size: 20px;
	color: red;
	margin-bottom: 13px;
}

.invoiceUploadPlus {
	font-size: 23px;
	margin-left: 10px;
	color: green;
}

.invoiceUploadMinus {
	font-size: 23px;
	margin-left: 10px;
	color: red;
}

.action-comment {
	font-family: sans-serif;
	color: blue;
	font-size: 18px;
}

.action-type {
	font-family: sans-serif;
	font-size: 20px;
	color: red;
}

.wallet-amount {
	font-family: sans-serif;
	font-size: 20px;
}

.btn {
    margin-bottom: 5px;
}