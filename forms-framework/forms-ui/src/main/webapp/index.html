<!doctype html>

<html>
<head>
    <script type="application/javascript">
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|BB|PlayBook|IEMobile|Windows Phone|Kindle|Silk|Opera Mini/i.test(navigator.userAgent)) {
            window.device = "mobile";
            window.location.href = window.location.href.substr(0, window.location.href.indexOf("forms-ui/")) + "forms-ui/m" +
                window.location.hash;
        } else {
            window.device = "desktop";
            window.location.href = window.location.href.substr(0, window.location.href.indexOf("forms-ui/")) + "forms-ui/d" +
                window.location.hash;
        }
    </script>
</head>
<body>
</body>
</html>
