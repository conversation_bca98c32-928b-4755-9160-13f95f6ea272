<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<title>Warning Letter</title>
<style type="text/css">
* {
	font-size: 16px;
}

.page-heading{
    font-size: 34px;
    font-weight: 700;
    color: brown;
}
.content {
	padding: 0 20px 20px 20px;
	color: #000;
}

.block-top {
	margin-top: 20px;
}

.block {
	margin-bottom: 30px;
}

.heading {
	background: #689045;
	color: #fff;
	padding: 10px;
	margin-bottom: 10px;
	font-size: 18px;
}

.table {
	width: 100%;
}

.table thead {
	border-bottom: #ccc 2px solid;
}

.table td {
	border-top: #ccc 1px solid;
	padding: 2px 2px;
}

.table thead td {
	font-weight: bold;
}

.action-comment {
	color: blue;
	font-size: 18px;
}

.action-type {
	color: red;
	font-size: 20px;
}
</style>
</head>
<body>

	<div class="content">
		<div class="block">
			<div style="text-align: center; padding: 5px; margin-bottom: 20px;">
				<p class="page-heading">Warning Letter -
					$data.warning.warningStatus</p>
			</div>
			<p class="heading">Warning Letter Details</p>
			<div style="padding: 20px;">
				<table style="width: 100%;">
					<tr>
						<td>Cafe Id:</td>
						<td>$data.warning.unit.id</td>
					</tr>
					<tr>
						<td>Cafe Name:</td>
						<td>$data.warning.unit.name</td>
					</tr>
					<tr>
						<td>Manager on Duty:</td>
						<td>$data.warning.managerOnDuty.name</td>
					</tr>
					<tr>
						<td>Area Manager:</td>
						<td>$data.warning.areaManager.name</td>
					</tr>
					#if ($data.warning.hrResponse)
					<tr>
						<td>Approval/Rejection Date:</td>
						<td>$date.format('medium',
							$data.warning.hrResponse.actionTakenOn)</td>
					</tr>
					#end
					<tr>
						<td>Guilty Person Name - Employee Code</td>
						<td>$data.guiltyDetail.name - $data.guiltyDetail.employeeCode</td>
					</tr>
					<tr>
						<td>Guilty Person Designation</td>
						<td>$data.guiltyDetail.designation</td>
					</tr>
					#if($data.warning.doi)
					<tr>
						<td>Date Of Incidence :</td>
						<td>$date.format('full_date',
							$data.warning.doi)</td>
					</tr>
					#end
					#if ($data.warning.audit)
					<tr>
						<td>Based On :</td>
						<td>Audit</td>
					</tr>
					<tr>
						<td>Audit Id :</td>
						<td>$data.warning.audit.id</td>
					</tr>
					<tr>
						<td>Audit Date:</td>
						<td>$date.format('medium',
							$data.auditDetail.auditSubmissionDate)</td>
					</tr>
					<tr>
						<td>Auditor Name:</td>
						<td>$data.auditDetail.auditor.name</td>
					</tr>
					<tr>
						<td>Audit Type:</td>
						<td>$data.auditDetail.auditType</td>
					</tr>
					#end
				</table>
			</div>
		</div>

		<div class="block" style="margin-bottom: 50px">
			<p class="heading">Initiator Details</p>
			<div style="padding: 20px;">
				<table class="table">
					<thead>
						<tr>
							<td>Name</td>
							<td>Designation</td>
							<td>Impact Type</td>
							<td>Initiated On</td>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td>$data.warning.initiator.authorisedPerson.name</td>
							<td>$data.warning.initiator.authorisedPerson.code</td>
							#set($impactType = "BOTH")
							#if($data.warning.impactType == $impactType)
								<td>BOTH (ZTZ + CRITICAL)</td>
								#else
								<td>$data.warning.impactType</td>
								#end
							<td>$date.format('medium',
								$data.warning.initiator.actionTakenOn)</td>
						</tr>
					</tbody>
				</table>
				<div class="block-top">
					<div style="font-weight: bold">Initiator Comment :</div>
					<div class="action-comment">$data.warning.initiator.comment</div>
				</div>
				<div style="font-weight: bold; color: red;" class="block-top">Reasons
					:( Reason Name - Category)</div>
				<ul>
					#set( $reasons = $data.warning.initiator.reasons) #foreach( $reason
					in $reasons )
					<li>$reason.reasonName - $reason.category</li> #end
				</ul>
				<div style="clear: both"></div>
			</div>
		</div>
		#if ($data.warning.amResponse)
		<div class="block" style="margin-bottom: 50px">
			<p class="heading">AM Action Details</p>
			<div style="padding: 20px;">
				<table class="table">
					<thead>
						<tr>
							<td>Name</td>
							<td>Action Taken By</td>
							<td>Action Type</td>
							<td>Action Taken On</td>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td>$data.warning.amResponse.authorisedPerson.name</td>
							<td>$data.warning.amResponse.actionTakenBy</td>
							<td>Forwarded To DGM</td>
							<td>$date.format('medium',
								$data.warning.amResponse.actionTakenOn)</td>
						</tr>
					</tbody>
				</table>
				<div class="block-top">
					<div style="font-weight: bold">AM Comment :</div>
					<div class="action-comment">$data.warning.amResponse.comment
					</div>
				</div>
			</div>
		</div>
		#end 
		#if ($data.warning.dgmResponse)
		<div class="block" style="margin-bottom: 50px">
			<p class="heading">DGM Action Details</p>
			<div style="padding: 20px;">
				<table class="table">
					<thead>
						<tr>
							<td>Name</td>
							<td>Action Taken By</td>
							<td>Action Type</td>
							<td>Action Taken On</td>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td>$data.warning.dgmResponse.authorisedPerson.name</td>
							<td>$data.warning.dgmResponse.actionTakenBy</td>
							<td>$data.warning.dgmResponse.toStatus</td>
							<td>$date.format('medium',
								$data.warning.dgmResponse.actionTakenOn)</td>
						</tr>
					</tbody>
				</table>
				<div class="block-top">
					<div style="font-weight: bold">DGM Comment :</div>
					<div class="action-comment">
						$data.warning.dgmResponse.comment</div>
				</div>
			</div>
		</div>
		#end 
		#if ($data.warning.hrResponse)
		<div class="block" style="margin-bottom: 50px">
			<p class="heading">HR Action Details</p>
			<div style="padding: 20px;">
				<table class="table">
					<thead>
						<tr>
							<td>Name</td>
							<td>Action Taken By</td>
							<td>Action Type</td>
							<td>Action Taken On</td>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td>$data.warning.hrResponse.authorisedPerson.name</td>
							<td>$data.warning.hrResponse.actionTakenBy</td>
							<td>$data.warning.warningStatus</td>
							<td>$date.format('medium',
								$data.warning.hrResponse.actionTakenOn)</td>
						</tr>
					</tbody>
				</table>
				<div class="block-top">
					<div style="font-weight: bold">HR Comment :</div>
					<div class="action-comment">$data.warning.hrResponse.comment
					</div>
				</div>
			</div>
		</div>
		#end
	</div>
</body>
</html>