/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.forms.service.config;

import com.stpl.tech.forms.core.config.FormsConfig;
import com.stpl.tech.master.core.config.KettleInterceptorConfig;
import com.stpl.tech.master.core.config.MasterExternalConfig;
import com.stpl.tech.spring.config.MasterSecurityConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

@SpringBootApplication
@Configuration
@EnableScheduling
@ComponentScan({ "com.stpl.tech.master.core.external.interceptor", "com.stpl.tech.master.core.external.acl.service",
        "com.stpl.tech.master.core.external.cache" })
@Import(value = {FormsConfig.class,
        MasterExternalConfig.class, MasterSecurityConfiguration.class, KettleInterceptorConfig.class})
public class FormsServiceConfig extends SpringBootServletInitializer {

    static {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
    }

    public static void main(String[] args) {
        SpringApplication.run(FormsServiceConfig.class,args);
    }
}
