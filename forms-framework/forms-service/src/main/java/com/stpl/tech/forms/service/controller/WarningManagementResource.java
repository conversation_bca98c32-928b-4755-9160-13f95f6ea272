package com.stpl.tech.forms.service.controller;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.forms.core.FormsServiceConstants;
import com.stpl.tech.forms.core.exception.WarningLetterException;
import com.stpl.tech.forms.core.service.WarningService;
import com.stpl.tech.forms.domain.model.DocumentDetail;
import com.stpl.tech.forms.request.AuditImageUpload;
import com.stpl.tech.forms.request.ImageUpload;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.warning.domain.model.EmployeeWarning;
import com.stpl.tech.warning.domain.model.EmployeeWarningStatus;

@RestController
@RequestMapping(value = FormsServiceConstants.API_VERSION + FormsServiceConstants.SEPARATOR
		+ FormsServiceConstants.WARNING_LETTER_MANAGEMENT_ROOT_CONTEXT)
public class WarningManagementResource {

	private static final Logger LOG = LoggerFactory.getLogger(WarningManagementResource.class);

	@Autowired
	private WarningService warningService;

	@RequestMapping(method = RequestMethod.POST, value = "issue-warning-letter", produces = MediaType.APPLICATION_JSON)
	public Integer issueWarningLetter(@RequestBody EmployeeWarning employeeWarning)
			throws WarningLetterException, TemplateRenderingException {
		LOG.info("Issue Warning letter to employee " + employeeWarning.getGuiltyPerson().getId() + " for unit : "
				+ employeeWarning.getUnit().getId());
		return warningService.addWarningLetter(employeeWarning);
	}

	@RequestMapping(method = RequestMethod.GET, value = "warning-letter", produces = MediaType.APPLICATION_JSON)
	public List<EmployeeWarning> getWarningLetters(@RequestParam(required = false) Integer unitId,
			@RequestParam(required = true) String startDate, @RequestParam(required = true) String endDate,
			@RequestParam(required = false) String status, @RequestParam(required = false) Integer amId) {
		LOG.info("Get Warning letter for unit : " + unitId);
		return warningService.getWarningLetters(unitId,
				startDate != null && startDate.trim().length() > 0 ? AppUtils.getDate(AppUtils.parseDate(startDate))
						: null,
				endDate != null && endDate.trim().length() > 0 ? AppUtils.getDate(AppUtils.parseDate(endDate)) : null,
				status, amId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "warning-letter-detail", produces = MediaType.APPLICATION_JSON)
	public EmployeeWarning issueWarningLetter(@RequestParam Integer warningId) {
		LOG.info("Get detail of warning id  " + warningId);
		return warningService.getWarningLetter(warningId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "warning-action-detail", produces = MediaType.APPLICATION_JSON)
	public boolean recordWarningResponse(@RequestBody EmployeeWarningStatus warningStatus)
			throws TemplateRenderingException, IOException, WarningLetterException {
		LOG.info("Process action by " + warningStatus.getActionTakenBy());
		return warningService.processWarningAction(warningStatus);
	}

	@RequestMapping(method = RequestMethod.POST, value = "warning-report-download")
	public void downloadWarningReport(HttpServletResponse response, @RequestBody Integer warningId)
			throws DocumentException, TemplateRenderingException, IOException, WarningLetterException {
		warningService.downloadWarningReport(response, warningId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "warning-image-upload")
	public boolean uploadFileMulti(@ModelAttribute ImageUpload form) throws Exception {
		LOG.info("Image upload request for warning id : " + form.getWarningId());
		return warningService.saveImages(form.getFiles(), form.getWarningId());
	}
	
	@RequestMapping(method = RequestMethod.POST, value = "image-upload")
	public DocumentDetail uploadImage(@ModelAttribute AuditImageUpload form) throws Exception {
		LOG.info("Image upload request");
		return warningService.saveImage(form.getFile(), form.getUserId());
	}

	@RequestMapping(method = RequestMethod.POST, value = "warning-image-download")
	public void downloadWarningImage(HttpServletResponse response, @RequestBody Integer imageId) throws IOException, WarningLetterException {
		warningService.downloadWarningImage(response, imageId);
	}

}
