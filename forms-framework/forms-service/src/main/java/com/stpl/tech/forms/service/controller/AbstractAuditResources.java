package com.stpl.tech.forms.service.controller;

import com.stpl.tech.forms.core.exception.AuditError;
import com.stpl.tech.forms.core.exception.AuditException;
import com.stpl.tech.forms.core.exception.WalletException;
import com.stpl.tech.forms.core.exception.WarningLetterException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by <PERSON><PERSON> on 17-01-2018.
 */
public class AbstractAuditResources {
    private static final Logger LOG = LoggerFactory.getLogger(AbstractAuditResources.class);

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(AuditException.class)
    @ResponseBody
    public AuditError handleAuditException(HttpServletRequest req, AuditException ex) {
        LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
        return ex.getCode();
    }
    
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(WarningLetterException.class)
    @ResponseBody
    public AuditError handleWarningLetterException(HttpServletRequest req, AuditException ex) {
        LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
        return ex.getCode();
    }
    
    @ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
    @ExceptionHandler(WalletException.class)
    @ResponseBody
    public WalletException handleWalletException(HttpServletRequest req, WalletException ex) {
        LOG.error(HttpStatus.NOT_ACCEPTABLE.name(), ex);
        return ex;
    }
}
