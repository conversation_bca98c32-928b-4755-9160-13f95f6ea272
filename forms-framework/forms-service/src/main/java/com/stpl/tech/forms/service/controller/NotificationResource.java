package com.stpl.tech.forms.service.controller;

import com.stpl.tech.forms.core.exception.WalletException;
import com.stpl.tech.forms.core.service.EnvProperties;
import com.stpl.tech.forms.core.service.FormAuthorizationService;
import com.stpl.tech.kettle.core.notification.sms.WalletSMSNotificationType;
import com.stpl.tech.master.core.exception.DuplicateRequestException;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.OtpType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import javax.jms.JMSException;
import java.io.IOException;

public abstract class NotificationResource extends AbstractAuditResources {
    private static final Logger LOG = LoggerFactory.getLogger(NotificationResource.class);

    @Autowired
    private SMSClientProviderService providerService;
    
    @Autowired
    private EnvProperties props;

    protected boolean sendCustomerAuthorizationOTP(String contactNumber) throws WalletException {
        if (!StringUtils.isEmpty(contactNumber)) {
            try {
                String token = getNotificationService().getOTPMapperInstance().generateOTP(props.getSendOTPLastFourDigits(), OtpType.PETTY_CASH,
                        contactNumber, getEnvironmentProperties().getEnvType());
                return sendCustomerAuthorizationMessage(contactNumber, token);
            } catch (DuplicateRequestException e) {
                LOG.error("Error in send Customer Authorization OTP", e);
                return false;
            }
        } else {
            throw new WalletException("Invalid contact number");
        }
    }

    protected boolean resendCustomerAuthorizationOTP(String contactNumber) throws WalletException {
        if (!StringUtils.isEmpty(contactNumber)) {
            try {
                String token = getNotificationService().getOTPMapperInstance().getOTP(OtpType.PETTY_CASH, contactNumber);
                if (token == null) {
                    token = getNotificationService().getOTPMapperInstance().generateOTP(props.getSendOTPLastFourDigits(),OtpType.PETTY_CASH, contactNumber,
                            getEnvironmentProperties().getEnvType());
                }
                return sendCustomerAuthorizationMessage(contactNumber, token);
            } catch (DuplicateRequestException e) {
                LOG.error("Error in resend Customer Authorization OTP", e);
                return false;
            }
        } else {
            throw new WalletException("Invalid contact number");
        }
    }

    protected boolean sendCustomerAuthorizationMessage(String contactNumber, String token) throws WalletException {
        if (!StringUtils.isEmpty(contactNumber) && !StringUtils.isEmpty(token)) {
            try {
                String message = WalletSMSNotificationType.WALLET_AUTHENTICATION_MESSAGE.getMessage(token);
                boolean sentOtp = getNotificationService().sendNotification(
                        WalletSMSNotificationType.WALLET_AUTHENTICATION_MESSAGE.name(), message, contactNumber,
                        providerService.getSMSClient(
                                WalletSMSNotificationType.WALLET_AUTHENTICATION_MESSAGE.getTemplate().getSMSType(),
                                ApplicationName.FORMS_SERVICE),
                        getEnvironmentProperties().getSendWalletOTPSMS(),null);
                getAuthorizationService().createSMSAuthorizationRequest(contactNumber, token, message);
                return sentOtp;
            } catch (IOException | JMSException e) {
                LOG.error("Error while sending the OTP message to " + contactNumber, e);
            }
            return false;
        } else {
            throw new WalletException("Please provide all request parameters");
        }

    }

    protected boolean verifyOTPInCurrentSession(String otpPin, String contactNumber) throws WalletException {
        if (!StringUtils.isEmpty(contactNumber) && !StringUtils.isEmpty(otpPin)) {
            boolean result = otpPin
                    .equals(getNotificationService().getOTPMapperInstance().getOTP(OtpType.PETTY_CASH, contactNumber));
            if (result) {
                getNotificationService().getOTPMapperInstance().removeOTP(OtpType.PETTY_CASH, contactNumber);
                getAuthorizationService().updateSMSAuthorizationRequest(contactNumber, otpPin);
            }
            return result;
        } else {
            throw new WalletException("Please provide all request parameters");
        }

    }

    public abstract EnvProperties getEnvironmentProperties();

    public abstract FormAuthorizationService getAuthorizationService();

    public abstract NotificationService getNotificationService();

}
