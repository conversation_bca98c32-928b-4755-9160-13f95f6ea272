package com.stpl.tech.forms.service.controller;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.View;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.forms.core.FormsServiceConstants;
import com.stpl.tech.forms.core.exception.AuditException;
import com.stpl.tech.forms.core.service.AuditService;
import com.stpl.tech.forms.core.util.AuditUtil;
import com.stpl.tech.forms.domain.model.Audit;
import com.stpl.tech.util.TemplateRenderingException;

@RestController
@RequestMapping(value = FormsServiceConstants.API_VERSION + FormsServiceConstants.SEPARATOR
        + FormsServiceConstants.AUDIT_MANAGEMENT_ROOT_CONTEXT)
public class AuditResources extends AbstractAuditResources {

    @Autowired
    private AuditService auditService;

    @RequestMapping(method = RequestMethod.POST, value = "audit", produces = MediaType.APPLICATION_JSON)
    public Integer submitAudit(@RequestBody Audit audit) throws AuditException, TemplateRenderingException, IOException {
        return auditService.submitAudit(audit);
    }

    @RequestMapping(method = RequestMethod.GET, value = "audit-search", produces = MediaType.APPLICATION_JSON)
    public List<Audit> findAudit(@RequestParam(required = false) Integer unitId,
                                 @RequestParam(required = true) String startDate,
                                 @RequestParam(required = true) String endDate,
                                 @RequestParam(required = false) Integer auditFormId) throws AuditException {
        Date start = startDate != null && startDate.trim().length() > 0 ? AuditUtil.getDate(AuditUtil.parseDate(startDate)) : null;
        Date end = endDate != null && endDate.trim().length() > 0 ? AuditUtil.getDate(AuditUtil.parseDate(endDate)) : null;
        return auditService.findAudit(unitId, start, end, auditFormId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "audit-download")
    public void downloadAudit(HttpServletResponse response, @RequestBody Integer auditId) throws AuditException, DocumentException, TemplateRenderingException, IOException {
        auditService.downloadAuditReport(response, auditId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "audit-report-email")
    public void emailAuditReport(@RequestBody Integer auditId) throws AuditException, TemplateRenderingException, IOException, DocumentException {
        auditService.emailAuditReport(auditId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "audit-generate-download")
    public View downloadGenerateAudit(@RequestBody Integer auditId) throws AuditException, TemplateRenderingException, IOException {
        return auditService.downloadGenerateAudit(auditId);
    }
}