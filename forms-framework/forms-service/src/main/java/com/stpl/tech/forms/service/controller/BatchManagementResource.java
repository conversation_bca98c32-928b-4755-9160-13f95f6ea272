package com.stpl.tech.forms.service.controller;

import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.forms.core.FormsServiceConstants;
import com.stpl.tech.forms.core.domain.model.BatchCodeResponse;
import com.stpl.tech.forms.core.service.BatchManagementService;
import com.stpl.tech.master.core.exception.DataUpdationException;

@RestController
@RequestMapping(value = FormsServiceConstants.API_VERSION + FormsServiceConstants.SEPARATOR
		+ FormsServiceConstants.BATCH_MANAGEMENT_ROOT_CONTEXT)
public class BatchManagementResource {

	@Autowired
	BatchManagementService batchManagementService;

	@RequestMapping(method = RequestMethod.GET, value = "generate-pack-codes", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public BatchCodeResponse generatePackCodes(@RequestParam(required = true) Integer unitId,
			@RequestParam(required = true) Integer productId, @RequestParam(required = true) String productName,
			@RequestParam(required = true) Integer count, @RequestParam(required = true) Integer length) throws DataUpdationException {
		if (unitId != null && productId != null && count != null && count > 0 && length != null && length > 0) {
			return batchManagementService.generatePackCodes(unitId, productId, productName, count, length);
		}
		return null;
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-prefix", produces = MediaType.APPLICATION_JSON)
	public String fetchPrefix(@RequestParam(required = true) String keyId,
			@RequestParam(required = true) String keyType) {
		return batchManagementService.getCodePrefix(keyId, keyType);
	}

}
