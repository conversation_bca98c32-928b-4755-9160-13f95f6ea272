package com.stpl.tech.forms.service.controller;

import com.google.gson.Gson;
import com.stpl.tech.expense.domain.model.Claim;
import com.stpl.tech.expense.domain.model.ClaimFindVO;
import com.stpl.tech.expense.domain.model.ClaimLog;
import com.stpl.tech.expense.domain.model.ClaimRequestVO;
import com.stpl.tech.expense.domain.model.ClaimStatus;
import com.stpl.tech.expense.domain.model.OrderRefundClaimVo;
import com.stpl.tech.forms.core.FormsServiceConstants;
import com.stpl.tech.forms.core.exception.WalletException;
import com.stpl.tech.forms.core.service.ClaimManagementService;
import com.stpl.tech.forms.core.service.EnvProperties;
import com.stpl.tech.forms.core.service.WalletService;
import com.stpl.tech.forms.domain.model.Voucher;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.FileNotFoundException;
import java.util.List;

@RestController
@RequestMapping(value = FormsServiceConstants.API_VERSION + FormsServiceConstants.SEPARATOR
        + FormsServiceConstants.CLAIM_MANAGEMENT_ROOT_CONTEXT)
public class ClaimManagementResource extends AbstractAuditResources {

    @Autowired
    private WalletService walletService;

    @Autowired
    private ClaimManagementService claimManagementService;

    @Autowired
    private EnvProperties envProperties;

    private static final Logger LOG = LoggerFactory.getLogger(ClaimManagementResource.class);

    @RequestMapping(method = RequestMethod.POST, value = "find-claims", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public List<Claim> findClaims(@RequestBody ClaimFindVO request) {
        LOG.info("Got request to find claim with params {}", new Gson().toJson(request));
        return claimManagementService.findClaims(request.getStartDate(), request.getEndDate(), request.getStatus(), request.getType(), request.getUnitIds(),
                request.getWalletId(), request.getEmployeeId());
    }

    @RequestMapping(method = RequestMethod.POST, value = "find-claims-by-status", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public List<Claim> getClaimsWithStatus(@RequestBody ClaimStatus status) {
        LOG.info("Request to find claims with status {}", status);
        return null;
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-claim-logs", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public List<ClaimLog> getClaimLogs(@RequestBody Integer claimId) {
        LOG.info("Got request to get logs for claim id {}", claimId);
        return claimManagementService.getClaimLogs(claimId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-claim-vouchers", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public List<Voucher> getClaimVouchers(@RequestBody Integer claimId) {
        LOG.info("Got request to get vouchers for claim id {}", claimId);
        return claimManagementService.getClaimVouchers(claimId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-claim", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Integer addHappayClaim(@RequestBody ClaimRequestVO request) throws WalletException {
        LOG.info("Request to create claim {}", new Gson().toJson(request));
        return claimManagementService.addClaim(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-happay-id-to-claim", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean setHappayIdToClaim(@RequestParam String happayId, @RequestParam Integer claimId, @RequestParam Integer userId) throws WalletException {
        LOG.info("Request to update Happay id {} to claim {} by user id {}", happayId, claimId, userId);
        return claimManagementService.setHappayIdToClaim(happayId, claimId, userId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "approve-claim", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean approveClaim(@RequestBody ClaimRequestVO request) throws WalletException, EmailGenerationException {
        LOG.info("Request to approve claim {}", new Gson().toJson(request));
        return claimManagementService.approveRejectClaim(request, true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "reject-claim", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean rejectClaim(@RequestBody ClaimRequestVO request) throws WalletException, EmailGenerationException {
        LOG.info("Request to reject claim {}", new Gson().toJson(request));
        return claimManagementService.approveRejectClaim(request, false);
    }

    @RequestMapping(method = RequestMethod.POST, value = "acknowledge-claim", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean acknowledgeClaim(@RequestBody ClaimRequestVO request) throws WalletException {
        LOG.info("Request to acknowledge claim {}", new Gson().toJson(request));
        return claimManagementService.acknowledgeClaim(request);
    }

    @Scheduled(cron = "0 30 8 * * *", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value="auto-acknowledge-claims-testing",produces = MediaType.APPLICATION_JSON)
    public void autoAcknowledgeClaims() throws WalletException, EmailGenerationException {
        LOG.info("Auto acknowledging UN_SETTLED claims!");
        claimManagementService.autoAcknowledgeClaims();
    }

    @RequestMapping(method = RequestMethod.POST, value = "download-claim", consumes = MediaType.APPLICATION_JSON)
    public void downloadClaimDetail(HttpServletResponse response, @RequestBody Integer claimId) throws WalletException, TemplateRenderingException, FileNotFoundException {
        LOG.info("Request to download claim {}", claimId);
        claimManagementService.downloadClaimDetail(response, claimId);

    }

    @RequestMapping(method = RequestMethod.POST,value = "bulk-download",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View bulkDownloadClaim(@RequestBody ClaimFindVO claimFindVO, HttpServletResponse response)throws WalletException,TemplateRenderingException{
        LOG.info("Requesting to bulk download");
        return claimManagementService.bulkDownload(claimFindVO);
    }

    @RequestMapping(method = RequestMethod.GET,value = "download-by-id",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View downloadById(@RequestParam Integer claimId)throws WalletException,TemplateRenderingException{
        LOG.info("Requesting to download claim by id");
        return claimManagementService.downloadById(claimId);
    }

    @RequestMapping(method = RequestMethod.GET,value = "download-template",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View downloadTemplate()throws WalletException,TemplateRenderingException{
        LOG.info("Requesting to download claim by id");
        return claimManagementService.downloadTemplate();
    }
    @RequestMapping(method = RequestMethod.POST, value = "upload-approvals", consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean uploadApprovals(HttpServletRequest request,
                                                   final MultipartFile file, @RequestParam Integer requestBy) throws Exception {
        LOG.info("Request to upload approvals");
        return claimManagementService.uploadApprovals(file, requestBy);
    }

    @RequestMapping(method = RequestMethod.POST, value = "refund-voucher")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean serviceChargeRefundVoucher(@RequestBody OrderRefundClaimVo orderRefundClaimVo) throws WalletException, EmailGenerationException, DataNotFoundException {
        return claimManagementService.serviceChargeRefundVoucher(orderRefundClaimVo);
    }

    @GetMapping("get/exclude-customers")
    public List<Integer> excludeCustomersList() {
        return AppConstants.EXCLUDE_CUSTOMER_IDS;
    }

    @Scheduled(cron = "0 30 8 * * *", zone = "GMT+05:30")
    @PostMapping("claim-initiate/send-mail-cron")
    public void sendMailForInitiateClaim() {
        claimManagementService.sendMailForInitiateClaim();
    }

    @Scheduled(cron = "0 45 8 * * *", zone = "GMT+05:30")
    @PostMapping("claim-auto-initiate")
    public void autoInitiateClaims() {
        claimManagementService.autoInitiateClaims();
    }

}
