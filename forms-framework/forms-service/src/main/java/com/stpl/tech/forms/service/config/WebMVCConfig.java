package com.stpl.tech.forms.service.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import com.stpl.tech.forms.core.FormsServiceConstants;
import com.stpl.tech.master.core.external.interceptor.ACLInterceptor;
import com.stpl.tech.master.core.external.interceptor.ApiTokenInterceptor;
import com.stpl.tech.master.core.external.interceptor.SessionAuthInterceptor;

@Configuration
@EnableWebMvc
@EnableAspectJAutoProxy
@ComponentScan("com.stpl.tech.forms.service.controller")
public class WebMVCConfig extends WebMvcConfigurerAdapter {

    @Autowired
    private ACLInterceptor aclInterceptor;

    @Autowired
    private SessionAuthInterceptor sessionAuthInterceptor;

    @Autowired
    private ApiTokenInterceptor apiTokenInterceptor;

    @Override
    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
        configurer.enable();
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(new MappingJackson2HttpMessageConverter());
        super.configureMessageConverters(converters);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(sessionAuthInterceptor).addPathPatterns("/**");
        registry.addInterceptor(aclInterceptor).addPathPatterns("/**");
        registry.addInterceptor(apiTokenInterceptor).addPathPatterns("/**");
    }

    @Bean(name = "multipartResolver")
    public CommonsMultipartResolver commonsMultipartResolver() {
        CommonsMultipartResolver commonsMultipartResolver = new CommonsMultipartResolver();
        commonsMultipartResolver.setDefaultEncoding(FormsServiceConstants.CHARSET);
        commonsMultipartResolver.setMaxUploadSize(8388608);
        return commonsMultipartResolver;
    }

    @Bean
    WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> enableDefaultServlet() {
        return (factory) -> factory.setRegisterDefaultServlet(true);
    }
}