package com.stpl.tech.forms.service.controller;

import com.stpl.tech.forms.core.FormsServiceConstants;
import com.stpl.tech.forms.core.exception.AuditException;
import com.stpl.tech.forms.core.service.AuditFormService;
import com.stpl.tech.forms.domain.model.AuditForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.List;

@RestController
@RequestMapping(value = FormsServiceConstants.API_VERSION + FormsServiceConstants.SEPARATOR
        + FormsServiceConstants.AUDIT_FORM_MANAGEMENT_ROOT_CONTEXT)
public class AuditFormResources extends AbstractAuditResources {

    @Autowired
    private AuditFormService auditFormService;

    @RequestMapping(method = RequestMethod.GET, value = "audit-forms", produces = MediaType.APPLICATION_JSON)
    public List<AuditForm> getAllAuditForms() {
        return auditFormService.getAllAuditForms();
    }

    @RequestMapping(method = RequestMethod.GET, value = "audit-forms-active", produces = MediaType.APPLICATION_JSON)
    public List<AuditForm> getActiveAuditForms() {
        return auditFormService.getActiveAuditForms();
    }

    @RequestMapping(method = RequestMethod.GET, value = "audit-form", produces = MediaType.APPLICATION_JSON)
    public AuditForm getAuditForm(@RequestParam(required = true) Integer formId) throws AuditException {
        return auditFormService.getAuditForm(formId);
    }
}
