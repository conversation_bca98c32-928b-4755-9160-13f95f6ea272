package com.stpl.tech.forms.service.controller;

import com.google.gson.Gson;
import com.stpl.tech.expense.domain.model.VoucherRejectionRequest;
import com.stpl.tech.expense.domain.model.VoucherSearchDetail;
import com.stpl.tech.expense.domain.model.WalletRequest;
import com.stpl.tech.forms.core.FormsServiceConstants;
import com.stpl.tech.forms.core.exception.WalletException;
import com.stpl.tech.forms.core.service.EnvProperties;
import com.stpl.tech.forms.core.service.FormAuthorizationService;
import com.stpl.tech.forms.core.service.WalletService;
import com.stpl.tech.forms.core.util.SpreadsheetUtil;
import com.stpl.tech.forms.domain.model.OtpRequest;
import com.stpl.tech.forms.domain.model.Voucher;
import com.stpl.tech.forms.domain.model.VoucherCostCenterAllocation;
import com.stpl.tech.forms.domain.model.Wallet;
import com.stpl.tech.forms.domain.model.WalletAccountType;
import com.stpl.tech.forms.domain.model.WalletApproverMappingVO;
import com.stpl.tech.forms.domain.model.WalletBusinessCostCenterMappingVO;
import com.stpl.tech.forms.request.UploadDetail;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping(value = FormsServiceConstants.API_VERSION + FormsServiceConstants.SEPARATOR
        + FormsServiceConstants.WALLET_MANAGEMENT_ROOT_CONTEXT)
public class WalletManagementResource extends NotificationResource {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private FormAuthorizationService authorizationDao;
    @Autowired
    private WalletService walletService;
    @Autowired
    private EnvProperties envProperties;

    private static final Logger LOG = LoggerFactory.getLogger(WalletManagementResource.class);

    @RequestMapping(method = RequestMethod.POST, value = "send-otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Boolean sendOtp(@RequestBody final OtpRequest detail) throws WalletException {
        LOG.info("Got request to send otp for {} on contact no {}", detail.getTransType(), detail.getContactNumber());
        return sendCustomerAuthorizationOTP(detail.getContactNumber());
    }

    @RequestMapping(method = RequestMethod.POST, value = "resend-otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Boolean resendOtp(@RequestBody final OtpRequest detail) throws WalletException {
        LOG.info("Got request to resend otp for {} on contact no {}", detail.getTransType(), detail.getContactNumber());
        return resendCustomerAuthorizationOTP(detail.getContactNumber());
    }

    @RequestMapping(method = RequestMethod.POST, value = "verify-otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Boolean verifyOTP(@RequestBody final OtpRequest userSession) throws WalletException {
        LOG.info(String.format("Verifying otp request for contact number ## %s from unit %d and otp %s",
                userSession.getContactNumber(), userSession.getUnit().getId(), userSession.getOtpPin()));
        return verifyOTPInCurrentSession(userSession.getOtpPin(), userSession.getContactNumber());
    }

    @RequestMapping(method = RequestMethod.GET, value = "wallet-details")
    public Wallet getWalletDetails(@RequestParam String accountType, @RequestParam String walletType,
                                   @RequestParam String accountNo) throws WalletException {
        LOG.info("getWalletDetails : account type  : " + accountType + " account no : " + accountNo);
        return walletService.getWalletDetails(accountType, walletType, accountNo);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-wallets")
    public List<Wallet> getWallets(@RequestBody WalletRequest walletRequest) throws WalletException {
        LOG.info("getWallets : " + new Gson().toJson(walletRequest));
        return walletService.getWallets(walletRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = "check-pending-voucher")
    public boolean checkPendingVoucher(@RequestParam Integer walletId,@RequestParam Integer associatedId) throws WalletException {
        LOG.info("checkPendingVoucher : walletId  : " + walletId);
        return walletService.checkPendingVouchers(walletId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "check-pending-voucher/associatedId")
    public boolean checkPendingVoucher(@RequestParam Integer associatedId) throws WalletException {
        LOG.info("checkPendingVoucher : associatedId  : " + associatedId);
        return walletService.checkPendingVouchersWithAssociatedId(associatedId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "issue-voucher")
    public String issueVoucher(@Valid @RequestBody Voucher voucher) throws WalletException {
        LOG.info("issueVoucher : voucher amount  : " + voucher.getIssuedAmount() + " account no : "
                + voucher.getAccountNo());
        return walletService.issueVoucher(voucher);
    }

    @RequestMapping(method = RequestMethod.GET, value = "voucher-list")
    public List<Voucher> getVoucherList(@RequestParam(required = false) String status,
                                        @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate,
                                        @RequestParam(required = false) String accountNo,
                                        @RequestParam(required = false) Boolean isReimbursed) {
        List<String> accountList = Arrays.asList(accountNo.split(","));
        List<String> statusList = Arrays.asList(status.split(","));
        LOG.info("Request to get vouchers list:");
        LOG.info(new Gson().toJson(accountList));
        LOG.info(new Gson().toJson(statusList));
        return walletService.getVoucherList(statusList,
                startDate != null && startDate.trim().length() > 0 ? AppUtils.getDate(AppUtils.parseDate(startDate))
                        : null,
                endDate != null && endDate.trim().length() > 0 ? AppUtils.getDate(AppUtils.parseDate(endDate)) : null,
                accountList, isReimbursed, false, true);
    }

    @RequestMapping(method = RequestMethod.GET,value = "wallet-accounts-by-accountType")
    public List<Wallet> getWalletAccounts(@RequestParam String walletAccountType,
                                          @RequestParam String employeeCode,@RequestParam(required = false) boolean byPass) throws WalletException {
        return walletService.getWalletAccounts(WalletAccountType.valueOf(walletAccountType),employeeCode,byPass);
    }

    @RequestMapping(method = RequestMethod.GET, value = "voucher-list-finance")
    public List<Voucher> getVouchersByFinancePendingDate(@RequestParam(required = false) String status,
                                        @RequestParam(required = false) String startDate,
                                        @RequestParam(required = false) String endDate,
                                        @RequestParam(required = false) String accountNo,
                                        @RequestParam(required = false) Boolean isReimbursed) {
        List<String> accountList = Arrays.asList(accountNo.split(","));
        List<String> statusList = Arrays.asList(status.split(","));
        LOG.info("Request to get finance vouchers list:");
        LOG.info(new Gson().toJson(accountList));
        LOG.info(new Gson().toJson(statusList));
        return walletService.getVouchersByFinancePendingDate(statusList,
                startDate != null && startDate.trim().length() > 0 ? AppUtils.getDate(AppUtils.parseDate(startDate)) : null,
                endDate != null && endDate.trim().length() > 0 ? AppUtils.getDate(AppUtils.parseDate(endDate)) : null,
                accountList, isReimbursed, false, true);
    }

    @RequestMapping(method = RequestMethod.GET, value = "voucher-detail")
    public Voucher getVoucherDetail(@RequestParam Integer voucherId) throws WalletException {
        return walletService.getVoucherDetail(voucherId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-voucher-doc")
    public boolean uploadFileVoucherInvoice(@ModelAttribute UploadDetail form) throws WalletException {
        LOG.info("Invoive upload for voucher id : " + form.getEntityId());
        return walletService.uploadVoucherDoc(form.getFile(), form.getEntityId(), form.getFileType().name());
    }

    @RequestMapping(method = RequestMethod.POST, value = "cancel-voucher")
    public boolean cancelVoucher(@RequestBody Voucher voucher) throws WalletException {
        LOG.info("cancelVoucher for voucher id : " + voucher.getId());
        return walletService.cancelVoucher(voucher);
    }

    @RequestMapping(method = RequestMethod.POST, value = "settle-voucher")
    public boolean settleVoucher(@RequestBody Voucher voucher) throws WalletException {
        LOG.info("settleVoucher for voucher id : " + voucher.getId());
        return walletService.settleVoucher(voucher);
    }

    @RequestMapping(method = RequestMethod.POST, value = "am-approve-voucher")
    public Boolean amApproveVoucher(@RequestBody Voucher voucher) throws WalletException {
        LOG.info("amApproveVoucher for voucher id : " + voucher.getId());
        return walletService.amApproveVoucher(voucher);
    }

    @RequestMapping(method = RequestMethod.POST, value = "am-reject-voucher")
    public Boolean amRejectVoucher(@RequestBody VoucherRejectionRequest request) throws WalletException {
        LOG.info("amRejectVoucher for voucher id : " + new Gson().toJson(request));
        return walletService.amRejectVoucher(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "finance-approve-voucher")
    public Boolean financeApproveVoucher(@RequestBody Voucher voucher) throws WalletException {
        LOG.info("financeApproveVoucher for voucher id : " + voucher.getId());
        return walletService.financeApproveVoucher(voucher);
    }

    @RequestMapping(method = RequestMethod.POST, value = "finance-reject-voucher")
    public Boolean financeRejectVoucher(@RequestBody VoucherRejectionRequest request) throws WalletException {
        LOG.info("financeRejectVoucher for voucher id : " + new Gson().toJson(request));
        return walletService.financeRejectVoucher(request);
    }

    /*@RequestMapping(method = RequestMethod.POST, value = "topup-approved-vouchers")
    public String topupApprovedVouchers(@RequestBody WalletTransaction transaction) throws WalletException {
        LOG.info("topupApprovedVouchers for wallet  : " + transaction.getWalletId());
        return walletService.topupApprovedVouchers(transaction);
    }*/

    @RequestMapping(method = RequestMethod.POST, value = "acknowledge-rejected-voucher")
    public Boolean acknowledgePendingRejectedVoucher(@RequestBody Voucher voucher) throws WalletException {
        LOG.info("acknowledgePendingRejectedVoucher for voucher id : " + voucher.getId());
        return walletService.acknowledgePendingRejectedVoucher(voucher);
    }

    @RequestMapping(method = RequestMethod.POST, value = "invoice-download")
    public void downloadInvoice(HttpServletResponse response, @RequestBody Integer imageId)
            throws IOException, WalletException {
        walletService.downloadInvoice(response, imageId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "pending-rejected-count")
    public Long getPendingRejectedVoucherCount(@RequestParam Integer walletId) {
        return walletService.getPendingRejectedVoucherCount(walletId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "pending-rejected-count/{associatedId}")
    public Long getPendingRejectedVoucherCountWithAssociatedId(@PathVariable Integer associatedId) {
        return walletService.getPendingRejectedVoucherCountWithAssociatedId(associatedId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "cash-denomination")
    public List<DenominationDetail> getCashDenomination() {
        return walletService.getCashDenomination();
    }

    @RequestMapping(method = RequestMethod.POST, value = "create-wallet")
    public Wallet createWallet(@RequestBody Wallet wallet) throws WalletException {
        LOG.info("createWallet for account no : " + wallet.getAccountNo());
        return walletService.createWallet(wallet);
    }

    /*@RequestMapping(method = RequestMethod.POST, value = "update-wallet")
    @ResponseBody
    public Wallet updateWallet(@RequestBody Wallet wallet) throws WalletException {
        LOG.info("updateWallet for  account no : " + wallet.getAccountNo());
        return walletService.updateWallet(wallet);
    }*/

    @RequestMapping(method = RequestMethod.POST, value = "download-voucher-list")
    public View getVoucherListViewExcel(@RequestBody VoucherSearchDetail searchDetail) {
        List<Voucher> details = walletService.getVoucherList(searchDetail.getStatus(),
                searchDetail.getStartDate() != null && searchDetail.getStartDate().trim().length() > 0
                        ? AppUtils.getDate(AppUtils.parseDate(searchDetail.getStartDate()))
                        : null,
                searchDetail.getEndDate() != null && searchDetail.getEndDate().trim().length() > 0
                        ? AppUtils.getDate(AppUtils.parseDate(searchDetail.getEndDate()))
                        : null,
                searchDetail.getAccountNo(), searchDetail.getIsReimbursed(), true, true);
        return SpreadsheetUtil.getVoucherDetailsSheet(
                details, "\"VoucherDetails- " + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xls\"");

    }

    @RequestMapping(method = RequestMethod.GET , value = "get/wallet-data/{accountType}")
    public List<Wallet> getUserWalletData(@PathVariable String accountType,@RequestParam Integer associatedId){
        return walletService.getUserWalletData(accountType,associatedId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-claim-pending-vouchers")
    public List<Voucher> getClaimPendingVouchers(@RequestBody String accountNumber) {
        return walletService.getClaimPendingVouchers(accountNumber);
    }

    @RequestMapping(method = RequestMethod.GET,value = "get-wallet-bcc-mappings",produces = MediaType.APPLICATION_JSON)
    public List<WalletBusinessCostCenterMappingVO> getWalletBusinessCostCenterMappings(@RequestParam final int walletId){
        return walletService.getWalletBusinessCostCenterMappings(walletId);
    }

    @RequestMapping(method = RequestMethod.GET,value = "get-wallet-by-account-no",produces = MediaType.APPLICATION_JSON)
    public Wallet getWalletBusinessCostCenterMappings(@RequestParam final String accountNo) throws WalletException {
        return walletService.findWalletByAccountNo(accountNo);
    }

    @RequestMapping(method = RequestMethod.GET,value = "get-wallet-approver-mapping",produces = MediaType.APPLICATION_JSON)
    public List<WalletApproverMappingVO> getWalletApproverMappings(@RequestParam final int walletId){
        return walletService.getWalletApproverMappings(walletId);
    }

    @RequestMapping(method = RequestMethod.GET,value= "get-reporting-managers",produces = MediaType.APPLICATION_JSON)
    public List<String> getReportingManagersForAccountNo(@RequestParam final String accountNo) throws WalletException{
        return walletService.getReportingManagersForAccountNo(accountNo);
    }

    @RequestMapping(method = RequestMethod.POST,value = "update-all-wallet-bcc-mapping",produces = MediaType.APPLICATION_JSON)
    public boolean updateAllWalletBusinessCostCenterMappings(@RequestBody List<WalletBusinessCostCenterMappingVO>  walletBusinessCostCenterMappingVOList, @RequestParam String lastUpdatedBy ,@RequestParam Integer walletId){
        LOG.info("Before sending request to update all wallet business cost center mapping ");
        return walletService.updateAllWalletBusinessCostCenterMappings(walletBusinessCostCenterMappingVOList,lastUpdatedBy,walletId);
    }

    @RequestMapping(method = RequestMethod.POST,value = "update-approver-mapping",produces = MediaType.APPLICATION_JSON)
    public boolean updateWalletApproverMappings(@RequestParam Integer walletMappingId, @RequestParam String status, @RequestParam String lastUpdatedBy){
        LOG.info("Before sending request to update all wallet approver mapping ");
        return walletService.updateWalletApproverMappings(walletMappingId,status,lastUpdatedBy);
    }
    @RequestMapping(method = RequestMethod.POST,value = "get-voucher-cost-center",produces = MediaType.APPLICATION_JSON)
    public List<VoucherCostCenterAllocation> getVoucherCostCenterAllocation(@RequestParam Integer voucherId){
        LOG.info("Getting Voucher Cost Center Allocation for voucher Id {}",voucherId);
        return walletService.getVoucherCostCenterAllocation(voucherId);
    }

    @RequestMapping(method = RequestMethod.POST,value = "update-wallet-bcc-mapping",produces = MediaType.APPLICATION_JSON)
    public boolean updateAllWalletBusinessCostCenterMappings(@RequestParam Integer walletBccMappingId, @RequestParam String status, @RequestParam String lastUpdatedBy){
        LOG.info("Before sending request update  wallet business cost center mapping for mappingId ::{}", walletBccMappingId);
        return walletService.updateWalletBusinessCostCenterMapping(walletBccMappingId,status,lastUpdatedBy);
    }

    @RequestMapping(method = RequestMethod.POST,value = "update-all-wallet-approver-mapping",produces = MediaType.APPLICATION_JSON)
    public boolean updateAllWalletApproverMappings(@RequestBody List<WalletApproverMappingVO> walletApproverMappingVOList, @RequestParam String lastUpdatedBy, @RequestParam Integer walletId){
        LOG.info("Before sending request to update all wallet approver mapping ");
        return walletService.updateAllWalletApproverMappings(walletApproverMappingVOList,lastUpdatedBy,walletId);
    }


    @Override
    public EnvProperties getEnvironmentProperties() {
        return envProperties;
    }

    @Override
    public NotificationService getNotificationService() {
        return notificationService;
    }

    @Override
    public FormAuthorizationService getAuthorizationService() {
        return authorizationDao;
    }


}
