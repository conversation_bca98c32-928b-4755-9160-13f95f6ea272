# FROM alpine:3.16.2
FROM tomcat:9.0
LABEL org.opencontainers.image.authors="<EMAIL>"
ENV CATALINA_OPTS="-Dspring.profiles.active=sprod -Denv.type=sprod -Dprimary.server=true -Dprimary.channel.partner.server=false -Dhazelcast.discovery.public.ip.enabled=true"
WORKDIR $CATALINA_HOME/webapps
COPY target/forms-service*.war .
EXPOSE 8080
CMD ["/usr/local/tomcat/bin/catalina.sh", "run", "-Xms1024m", "-Xmx4096m"]
