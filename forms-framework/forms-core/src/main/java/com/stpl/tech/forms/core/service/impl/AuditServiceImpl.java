package com.stpl.tech.forms.core.service.impl;

import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.text.DocumentException;
import com.stpl.tech.forms.core.FormsErrorConstants;
import com.stpl.tech.forms.core.FormsServiceConstants;
import com.stpl.tech.forms.core.exception.AuditException;
import com.stpl.tech.forms.core.notification.email.AuditReportNotification;
import com.stpl.tech.forms.core.notification.email.template.AuditNotificationTemplate;
import com.stpl.tech.forms.core.service.AuditService;
import com.stpl.tech.forms.core.service.EnvProperties;
import com.stpl.tech.forms.core.templates.PQSCAuditPDFTemplate;
import com.stpl.tech.forms.core.util.AbstractPdfViewHelper;
import com.stpl.tech.forms.core.util.AuditUtil;
import com.stpl.tech.forms.data.AuditDataConverter;
import com.stpl.tech.forms.data.dao.AuditDao;
import com.stpl.tech.forms.data.model.AuditDetailData;
import com.stpl.tech.forms.data.model.AuditDetailValuesData;
import com.stpl.tech.forms.data.model.AuditFormData;
import com.stpl.tech.forms.data.model.AuditFormValuesData;
import com.stpl.tech.forms.data.model.DocumentDetailData;
import com.stpl.tech.forms.domain.model.Audit;
import com.stpl.tech.forms.domain.model.AuditForm;
import com.stpl.tech.forms.domain.model.AuditValues;
import com.stpl.tech.forms.domain.model.DocUploadType;
import com.stpl.tech.forms.domain.model.EntityType;
import com.stpl.tech.forms.domain.model.FileType;
import com.stpl.tech.forms.domain.model.MimeType;
import com.stpl.tech.forms.domain.model.ScoreVO;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Service
public class AuditServiceImpl implements AuditService {

    private static final Logger LOG = LoggerFactory.getLogger(AuditServiceImpl.class);

    @Autowired
    private AuditDao auditDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private EnvProperties envProperties;

    @Autowired
    FileArchiveService fileArchiveService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer submitAudit(Audit audit) throws AuditException, TemplateRenderingException, IOException {
        if (audit.getAuditForm() != null && audit.getAuditForm().getId() != null) {
            AuditFormData auditFormData = auditDao.find(AuditFormData.class, audit.getAuditForm().getId());
            if (auditFormData != null) {
                AuditDetailData auditDetailData = new AuditDetailData();
                auditDetailData.setAcquiredScore(BigDecimal.ZERO);
                Unit unit = masterDataCache.getUnit(audit.getAuditUnit().getId());
                auditDetailData.setAuditorId(audit.getAuditor().getId());
                auditDetailData.setAuditorName(audit.getAuditor().getName());
                auditDetailData.setAreaManager(unit.getManagerName());
                auditDetailData.setAreaManagerId(unit.getManagerId());
                auditDetailData.setAuditDate(AuditUtil.getDate(audit.getAuditDate()));
                auditDetailData.setAuditTime(audit.getAuditTime());
                auditDetailData.setAuditFormData(auditFormData);
                auditDetailData.setAuditSubmitDate(AuditUtil.getCurrentTimestamp());
                auditDetailData.setAuditType(audit.getAuditType().value());
                auditDetailData.setAuditUnitId(audit.getAuditUnit().getId());
                auditDetailData.setAuditUnitName(audit.getAuditUnit().getName());
                auditDetailData.setCafeManager(audit.getCafeManager().getName());
                auditDetailData.setCafeManagerId(audit.getCafeManager().getId());
                auditDetailData.setManagerOnDuty(audit.getManagerOnDuty().getName());
                auditDetailData.setManagerOnDutyId(audit.getManagerOnDuty().getId());
                auditDetailData.setStatus(FormsServiceConstants.ACTIVE);
                auditDetailData.setTotalScore(BigDecimal.ZERO);
                auditDetailData.setProjectedTotalScore(BigDecimal.ZERO);
                auditDetailData.setProjectedAcquiredScore(BigDecimal.ZERO);
                auditDetailData = auditDao.add(auditDetailData, true);
                auditDetailData.setAuditDetailValuesDataList(saveAuditValuesData(auditDetailData, audit));
                File auditReport = uploadAuditReportToS3(auditDetailData);
                List<String> emails = new ArrayList<>();
                emails.addAll(Arrays.asList(auditDetailData.getAuditFormData().getNotificationEmails().split(",")));
                EmployeeBasicDetail employee = masterDataCache.getEmployeeBasicDetail(audit.getAuditor().getId());
                if (("Support Visit").equals(audit.getAuditForm().getName()) && employee.getDesignation().equals("DGM")) {
                    emails.addAll(masterDataCache.getEmailHeirarchy(auditDetailData.getAuditUnitId(), false));
                } else {
                    emails.addAll(masterDataCache.getEmailHeirarchy(auditDetailData.getAuditUnitId(), true));
                }
                AuditNotificationTemplate auditNotificationTemplate = new AuditNotificationTemplate(envProperties.getBasePath(),
                        AuditDataConverter.convert(auditDetailData, false), emails);
                sendAuditReportNotification(auditNotificationTemplate, auditReport);
                return auditDetailData.getId();
            } else {
                throw new AuditException("Audit form provided is not valid!");
            }
        } else {
            throw new AuditException("Please provide audit form detail!");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Audit> findAudit(Integer unitId, Date start, Date end, Integer auditFormId) throws AuditException {
        if (start != null && end != null) {
            List<Audit> audits = new ArrayList<>();
            List<AuditDetailData> auditDetailData = auditDao.searchAudit(unitId, start, end, auditFormId);
            auditDetailData.forEach(auditDetailData1 -> {
                audits.add(AuditDataConverter.convert(auditDetailData1, false));
            });
            return audits;
        } else {
            throw new AuditException("Please provide all search params: unitId, start date, end date");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void downloadAuditReport(HttpServletResponse response, Integer auditId) throws AuditException, IOException {

        if (auditId != null) {
            AuditDetailData auditDetailData = auditDao.find(AuditDetailData.class, auditId);
            if (auditDetailData != null) {
                DocumentDetailData document = auditDao.find(DocumentDetailData.class, auditDetailData.getAuditReportId());
                FileDetail fileDetail = new FileDetail(document.getS3Bucket(), document.getS3Key(),
                        document.getFileUrl());
                File file = fileArchiveService.getFileFromS3(envProperties.getBasePath() + File.separator + "s3", fileDetail);
                if (file != null) {
                    response.setContentType(document.getMimeType());
                    response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
                    byte[] bytesArray = new byte[(int) file.length()];
                    response.setContentLength(bytesArray.length);
                    OutputStream outputStream = null;
                    try (InputStream inputStream = new FileInputStream(file)) {
                        outputStream = response.getOutputStream();
                        int counter = 0;
                        while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
                            outputStream.write(bytesArray, 0, counter);
                            outputStream.flush();
                        }
                        response.getOutputStream().flush();
                        outputStream.close();
                        Files.delete(file.toPath()); // delete the temporary file created after completing request
                    }
                }
            } else {
                throw new AuditException(FormsErrorConstants.PROVIDE_AUDIT_ID);
            }
        } else {
            throw new AuditException(FormsErrorConstants.PROVIDE_AUDIT_ID);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void emailAuditReport(Integer auditId) throws AuditException, TemplateRenderingException, IOException, DocumentException {
        if (auditId != null) {
            AuditDetailData auditDetailData = auditDao.find(AuditDetailData.class, auditId);
            if (auditDetailData != null) {
                DocumentDetailData document = auditDao.find(DocumentDetailData.class, auditDetailData.getAuditReportId());
                FileDetail fileDetail = new FileDetail(document.getS3Bucket(), document.getS3Key(),
                        document.getFileUrl());
                File file = fileArchiveService.getFileFromS3(envProperties.getBasePath() + File.separator + "s3", fileDetail);
                if (file != null) {
                    List<String> emails = new ArrayList<>();
                    emails.addAll(Arrays.asList(auditDetailData.getAuditFormData().getNotificationEmails().split(",")));
                    emails.addAll(masterDataCache.getEmailHeirarchy(auditDetailData.getAuditUnitId(), true));
                    AuditNotificationTemplate auditNotificationTemplate = new AuditNotificationTemplate(envProperties.getBasePath(),
                            AuditDataConverter.convert(auditDetailData, false), emails);
                    sendAuditReportNotification(auditNotificationTemplate, file);
                    file.delete(); // delete the temporary file created after completing request
                }
            } else {
                throw new AuditException(FormsErrorConstants.PROVIDE_AUDIT_ID);
            }
        } else {
            throw new AuditException("Please provide audit id");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View downloadGenerateAudit(Integer auditId) throws AuditException, TemplateRenderingException, IOException {
        if (auditId != null) {
            AuditDetailData auditDetailData = auditDao.find(AuditDetailData.class, auditId);
            if (auditDetailData != null) {
                String auditReportHTMLTemplate = generateAuditReportTemplate(auditDetailData);
                AbstractPdfViewHelper view = new AbstractPdfViewHelper();
                view.createPdfFromHTML(auditReportHTMLTemplate, auditDetailData.getAuditFormData().getName() + auditDetailData.getAuditSubmitDate());
                return view;
            } else {
                throw new AuditException(FormsErrorConstants.PROVIDE_AUDIT_ID);
            }
        } else {
            throw new AuditException(FormsErrorConstants.PROVIDE_AUDIT_ID);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Audit getAudit(Integer auditId) {
        AuditDetailData auditDetailData = auditDao.find(AuditDetailData.class, auditId);
        if (auditDetailData != null) {
            return AuditDataConverter.convert(auditDetailData, false);
        }
        return null;
    }

    private void sendAuditReportNotification(AuditNotificationTemplate template, File auditReport) throws AuditException {
        try {
            String mailSubject = "";
            String fileName = "";
            EmployeeBasicDetail employee = masterDataCache.getEmployeeBasicDetail(template.getAudit().getAuditor().getId());
            if (("Support Visit").equals(template.getAudit().getAuditForm().getName()) && employee.getDesignation().equals("DGM")) {
                mailSubject = String.format("%s Report for %s for %s",
                        "Visit Feedback - " + template.getAudit().getAuditor().getName(),
                        template.getAudit().getAuditUnit().getName(),
                        AuditUtil.getFormattedTime(template.getAudit().getAuditSubmissionDate(), "EEE dd MMM yyyy"));
                fileName = "Visit Feedback - " + template.getAudit().getAuditor().getName() + template.getAudit().getAuditUnit().getName() + "_" +
                        AuditUtil.getFormattedTime(template.getAudit().getAuditDate(), "EEE dd MMM yyyy") + "_" + auditReport.getName();
            } else {
            	/*if(template.getAudit().getAuditType() == AuditType.AREA_MANAGER) {
                    fileName = AuditType.AREA_MANAGER.name() + "_";
                    mailSubject = AuditType.AREA_MANAGER.name() + "_";
                }*/
                mailSubject = template.getAudit().getAuditType().name() + "_" + String.format("%s Report for %s for %s",
                        template.getAudit().getAuditForm().getName(),
                        template.getAudit().getAuditUnit().getName(),
                        AuditUtil.getFormattedTime(template.getAudit().getAuditSubmissionDate(), "EEE dd MMM yyyy"));
                fileName = template.getAudit().getAuditType().name() + "_" + template.getAudit().getAuditForm().getName() +
                        "_" + template.getAudit().getAuditUnit().getName() + "_" +
                        AuditUtil.getFormattedTime(template.getAudit().getAuditDate(), "EEE dd MMM yyyy") + "_" + auditReport.getName();
            }

            AuditReportNotification emailNotification = new AuditReportNotification(template,
                    envProperties.getEnvType(), mailSubject);
            List<AttachmentData> attachments = new ArrayList<>();
            AttachmentData invoiceDetail = new AttachmentData();
            invoiceDetail.setAttachment(IOUtils.toByteArray(new FileInputStream(auditReport)));
            invoiceDetail.setContentType("application/pdf");
            invoiceDetail.setFileName(fileName);
            attachments.add(invoiceDetail);
            emailNotification.sendRawMail(attachments);
            auditReport.delete();// delete the temp file created on Server
        } catch (Exception e) {
            LOG.error("Error while sending email to for audit report id:", e);
            throw new AuditException("Error while sending email to for audit report id:");
        }
    }

    private File uploadAuditReportToS3(AuditDetailData auditDetailData) throws AuditException, TemplateRenderingException, IOException {
        String auditReportHTMLTemplate = generateAuditReportTemplate(auditDetailData);
        String path = envProperties.getBasePath() + "/audit/" + auditDetailData.getId() + ".pdf";
        try (OutputStream outputStream = new FileOutputStream(path)) {
            HtmlConverter.convertToPdf(auditReportHTMLTemplate, outputStream);
            outputStream.flush();
            File file = new File(envProperties.getBasePath() + "/audit/" + auditDetailData.getId() + ".pdf");
            String fileName = auditDetailData.getId() + ".pdf";
            String baseDir = "audit/INDIVIDUAL_AUDIT_REPORTS";
            try {
                FileDetail s3File = fileArchiveService.saveFileToS3(envProperties.getS3AuditBucket(), baseDir, file, true);
                if (s3File != null) {
                    DocumentDetailData documentDetailData = new DocumentDetailData();
                    documentDetailData.setUpdatedBy(auditDetailData.getAuditorId());
                    documentDetailData.setDocumentLink(fileName);
                    documentDetailData.setFileUrl(s3File.getUrl());
                    documentDetailData.setDocumentUploadType(DocUploadType.AUDIT_REPORT.name());
                    documentDetailData.setDocumentUploadTypeId(0);
                    documentDetailData.setFileType(FileType.AUDIT_REPORT.name());
                    documentDetailData.setMimeType(MimeType.PDF.name());
                    documentDetailData.setUpdateTime(AuditUtil.getCurrentTimestamp());
                    documentDetailData.setS3Key(s3File.getKey());
                    documentDetailData.setS3Bucket(s3File.getBucket());
                    documentDetailData = auditDao.add(documentDetailData, true);
                    if (documentDetailData.getDocumentId() != null) {
                        auditDetailData.setAuditReportId(documentDetailData.getDocumentId());
                        auditDetailData = auditDao.update(auditDetailData, true);
                        if (auditDetailData != null) {
                            return file;
                        } else {
                            throw new AuditException("Error updating report id: " + documentDetailData.getDocumentId().toString() + " in audit detail.");
                        }
                    } else {
                        throw new AuditException("Error saving document detail: key:" + s3File.getKey() + " url: " + s3File.getUrl());
                    }
                } else {
                    throw new AuditException("Error uploading report to S3.");
                }
            } catch (Exception e) {
                LOG.error("Encountered error while uploading report to S3", e);
                throw new AuditException("Encountered error while uploading report to S3");
            }
        } catch (IOException e) {
            LOG.error("Error when trying to create audit report :::::: {}", auditDetailData.getId(), e);
            throw new AuditException("Error creating audit report");
        }
    }

    private String generateAuditReportTemplate(AuditDetailData auditDetailData) throws AuditException, TemplateRenderingException {
        if (auditDetailData != null) {
            Audit audit = AuditDataConverter.convert(auditDetailData, true);
            Map<Integer, AuditFormValuesData> integerAuditFormValuesDataMap = new HashMap<>();
            Map<Integer, String> auditImageFormValuesDataMap = new HashMap<>();
            auditDetailData.getAuditFormData().getValues().forEach(auditFormValuesData -> {
                integerAuditFormValuesDataMap.put(auditFormValuesData.getId(), auditFormValuesData);
            });
            AuditForm auditForm = AuditDataConverter.convert(auditDetailData.getAuditFormData(), "", "", false);
            Map<Integer, List<AuditValues>> auditValuesDataMap = new TreeMap<>();
            List<AuditValues> topLevelValues = new ArrayList<>();
            audit.getValues().sort(Comparator.comparingInt(o -> o.getAuditFormValue().getAppearanceOrder()));
            for (AuditValues auditValues : audit.getValues()) {
                Integer linkedEntityId = integerAuditFormValuesDataMap.get(auditValues.getAuditFormValue().getId()).getLinkedEntityId();
                if(auditValues.getAttachedDoc() != null && auditValues.getAttachedDoc().getDocumentId() != null){
                	DocumentDetailData detailData = auditDao.find(DocumentDetailData.class, auditValues.getAttachedDoc().getDocumentId());
                	String fileUrl = detailData.getFileUrl();
                	auditImageFormValuesDataMap.put(auditValues.getAttachedDoc().getDocumentId(), fileUrl);
                }
                if (linkedEntityId != null) {
                    List<AuditValues> values = auditValuesDataMap.get(integerAuditFormValuesDataMap.get(auditValues.getAuditFormValue().getId()).getLinkedEntityId());
                    if (values == null) {
                        values = new ArrayList<>();
                    }
                    values.add(auditValues);
                    auditValuesDataMap.put(integerAuditFormValuesDataMap.get(auditValues.getAuditFormValue().getId()).getLinkedEntityId(), values);
                } else {
                    topLevelValues.add(auditValues);
                }
            }
            Map<Integer, Map<Integer, ScoreVO>> marksComp = new HashMap<>();
            Map<Integer, List<String>> observationsComp = new HashMap<>();
            Map<Integer, List<String>> strengthsComp = new HashMap<>();
            Map<Integer, BigDecimal> auditMarksMap = new HashMap<>();
            Map<Integer, ScoreVO> qGroupMarksMap;
            List<String> observations;
            List<String> strengths;
            List<AuditDetailData> auditDetailDataList = auditDao.getPreviousAudits(auditDetailData.getId(), auditDetailData.getAuditFormData().getId(),
                    auditDetailData.getAuditUnitId(), 3);
            Integer index = 3;
            for (AuditDetailData auditDetailData1 : auditDetailDataList) {
                qGroupMarksMap = new TreeMap<>();
                observations = new ArrayList<>();
                strengths = new ArrayList<>();
                calculateAuditComparisonData(auditDetailData1, integerAuditFormValuesDataMap, qGroupMarksMap, observations, strengths);
                marksComp.put(index, qGroupMarksMap);
                observationsComp.put(index, observations);
                strengthsComp.put(index, strengths);
                auditMarksMap.put(index, AuditUtil.multiply(AuditUtil.divide(auditDetailData1.getAcquiredScore(), auditDetailData1.getTotalScore()), new BigDecimal(100)));
                index -= 1;
            }
            PQSCAuditPDFTemplate pqscAuditPDFTemplate = new PQSCAuditPDFTemplate(audit, integerAuditFormValuesDataMap,
                    envProperties.getBasePath(), marksComp, observationsComp, auditMarksMap, strengthsComp, auditValuesDataMap,
                topLevelValues, auditImageFormValuesDataMap, auditForm);
            return pqscAuditPDFTemplate.getContent();
        } else {
            throw new AuditException(FormsErrorConstants.PROVIDE_AUDIT_ID);
        }
    }

	private void calculateAuditComparisonData(AuditDetailData auditDetailData, Map<Integer, AuditFormValuesData> integerAuditFormValuesDataMap,
                                              Map<Integer, ScoreVO> marksMap, List<String> observations, List<String> strengths) {
        auditDetailData.getAuditDetailValuesDataList().forEach(auditDetailValuesData -> {
            AuditFormValuesData auditFormValuesData = auditDetailValuesData.getAuditFormValuesData();
            if (auditFormValuesData.getScoreCounted() != null && auditFormValuesData.getScoreCounted().equals(FormsServiceConstants.AUDIT_CONSTANT_YES) &&
                    auditFormValuesData.getMaxScore() != null && auditFormValuesData.getLinkedEntityId() != null) {

                if (auditDetailValuesData.getQuestionOpted().equals(FormsServiceConstants.AUDIT_CONSTANT_YES)) {
                    ScoreVO scoreData = marksMap.get(auditFormValuesData.getLinkedEntityId());
                    if (scoreData == null) {
                        scoreData = new ScoreVO();
                        scoreData.setAuditDate(AuditUtil.getFormattedTime(auditDetailData.getAuditSubmitDate(), "d-MMM"));
                    }
                    auditDetailValuesData.setMaxScore(auditFormValuesData.getMaxScore());
                    scoreData.setMaxScore(AuditUtil.add(scoreData.getMaxScore(), auditDetailValuesData.getMaxScore()));
                    if (auditDetailValuesData.getAcquiredScore() != null) {
                        scoreData.setAcquiredScore(AuditUtil.add(scoreData.getAcquiredScore(), auditDetailValuesData.getAcquiredScore()));
                    }
                    if (scoreData.getAcquiredScore() != null && scoreData.getMaxScore() != null) {
                        scoreData.setPercentage(AuditUtil.multiply(AuditUtil.divide(scoreData.getAcquiredScore(), scoreData.getMaxScore()), new BigDecimal(100)));
                    }
                    if (auditDetailValuesData.getAcquiredScore() != null &&
                            auditDetailValuesData.getAcquiredScore().compareTo(auditDetailValuesData.getMaxScore()) < 0) {
                        if (auditDetailValuesData.getAnswerComment() != null) {
                            StringBuilder observation = new StringBuilder();
                            if (auditDetailValuesData.getAuditFormValuesData().getLinkedEntityId() != null) {
                                observation.append(integerAuditFormValuesDataMap.get(auditDetailValuesData.getAuditFormValuesData().getLinkedEntityId()).getEntityLabel()).append(" - ");
                            }
                            observation.append(auditDetailValuesData.getAnswerComment());
                            observation.append(auditDetailValuesData.getProductName() != null ? " - " + auditDetailValuesData.getProductName() : "");
                            observation.append(auditDetailValuesData.getEmployeeName() != null ? " - " + auditDetailValuesData.getEmployeeName() : "");
                            observations.add(observation.toString());
                        }
                    }
                    marksMap.put(auditFormValuesData.getLinkedEntityId(), scoreData);
                }
            }
        });
        marksMap.keySet().forEach(key -> {
            ScoreVO scoreVO = marksMap.get(key);
            if (scoreVO != null && scoreVO.getPercentage() != null && scoreVO.getPercentage().compareTo(new BigDecimal(100)) == 0) {
                AuditFormValuesData auditFormValuesData = integerAuditFormValuesDataMap.get(key);
                if (auditFormValuesData != null) {
                    strengths.add(auditFormValuesData.getEntityLabel());
                }
            }
        });
    }

    private List<AuditDetailValuesData> saveAuditValuesData(AuditDetailData auditDetailData, Audit audit) throws AuditException {
        if (auditDetailData != null && audit != null && audit.getValues().size() > 0) {
            BigDecimal acquiredScore = BigDecimal.ZERO;
            BigDecimal totalScore = BigDecimal.ZERO;
            BigDecimal projectedTotalScore = BigDecimal.ZERO;
            List<AuditDetailValuesData> auditDetailValuesDataList = new ArrayList<>();
            List<AuditFormValuesData> auditFormValuesList = auditDetailData.getAuditFormData().getValues();
            Map<Integer, AuditValues> integerAuditValuesMap = new HashMap<>();
            audit.getValues().forEach(auditValues -> integerAuditValuesMap.put(auditValues.getAuditFormValue().getId(), auditValues));
            List<String> entityTypes = Arrays.asList(EntityType.HEADING.value(), EntityType.SUBHEADING.value(), EntityType.SNIPPET.value(), EntityType.QUESTION_GROUP.value());
            for (AuditFormValuesData auditFormValuesData : auditFormValuesList) {
                if (auditFormValuesData.getStatus().equals(FormsServiceConstants.ACTIVE) && auditFormValuesData.getEntityType() != null && !entityTypes.contains(auditFormValuesData.getEntityType())) {
                    AuditValues auditValues = integerAuditValuesMap.get(auditFormValuesData.getId());
                    if (auditValues != null) {
                        if (validAuditValue(auditValues, auditFormValuesData)) {
                            AuditDetailValuesData auditDetailValuesData = new AuditDetailValuesData();
                            auditDetailValuesData.setAuditDetailData(auditDetailData);
                            auditDetailValuesData.setAuditFormValuesData(auditFormValuesData);
                            if (auditFormValuesData.getScoreCounted() != null && auditFormValuesData.getScoreCounted().equals(FormsServiceConstants.AUDIT_CONSTANT_YES) &&
                                    auditValues.getMaxScore() != null) {
                                auditDetailValuesData.setMaxScore(auditValues.getMaxScore());
                                projectedTotalScore = AuditUtil.add(projectedTotalScore, auditValues.getMaxScore());
                            }
                            if (!auditValues.isQuestionOpted()) {
                                auditDetailValuesData.setQuestionOpted(AuditUtil.setStatus(auditValues.isQuestionOpted()));
                            } else {
                                if (auditFormValuesData.getScoreCounted() != null && auditFormValuesData.getScoreCounted().equals(FormsServiceConstants.AUDIT_CONSTANT_YES) &&
                                        auditValues.getAcquiredScore() != null) {
                                    auditDetailValuesData.setAcquiredScore(auditValues.getAcquiredScore());
                                    acquiredScore = AuditUtil.add(acquiredScore, auditValues.getAcquiredScore());
                                }
                                if (auditValues.getAnswerComment() != null) {
                                    auditDetailValuesData.setAnswerComment(auditValues.getAnswerComment());
                                }
                                if (auditValues.getAttachedDoc() != null) {
                                    auditDetailValuesData.setAttachedDocId(auditValues.getAttachedDoc().getDocumentId());
                                }
                                if (auditValues.getDateValue() != null) {
                                    auditDetailValuesData.setDateValue(auditValues.getDateValue());
                                }
                                if (auditValues.getEmployeeDesignation() != null) {
                                    auditDetailValuesData.setEmployeeDesignation(auditValues.getEmployeeDesignation());
                                }
                                if (auditValues.getEmployeeId() != null) {
                                    auditDetailValuesData.setEmployeeId(auditValues.getEmployeeId());
                                }
                                if (auditValues.getEmployeeName() != null) {
                                    auditDetailValuesData.setEmployeeName(auditValues.getEmployeeName());
                                }
                                if (auditFormValuesData.getScoreCounted() != null && auditFormValuesData.getScoreCounted().equals(FormsServiceConstants.AUDIT_CONSTANT_YES) &&
                                        auditValues.getMaxScore() != null) {
                                    //auditDetailValuesData.setMaxScore(auditValues.getMaxScore());
                                    totalScore = AuditUtil.add(totalScore, auditValues.getMaxScore());
                                }
                                if (auditValues.getNumberValue() != null) {
                                    auditDetailValuesData.setNumberValue(auditValues.getNumberValue());
                                }
                                if (auditValues.getOption1() != null) {
                                    auditDetailValuesData.setOption1(auditValues.getOption1());
                                }
                                if (auditValues.getOption1Marks() != null) {
                                    auditDetailValuesData.setOption1Score(auditValues.getOption1Marks());
                                }
                                if (auditValues.getOption2() != null) {
                                    auditDetailValuesData.setOption2(auditValues.getOption2());
                                }
                                if (auditValues.getOption2Marks() != null) {
                                    auditDetailValuesData.setOption2Score(auditValues.getOption2Marks());
                                }
                                if (auditValues.getOption3() != null) {
                                    auditDetailValuesData.setOption3(auditValues.getOption3());
                                }
                                if (auditValues.getOption3Marks() != null) {
                                    auditDetailValuesData.setOption3Score(auditValues.getOption3Marks());
                                }
                                if (auditValues.getOption4() != null) {
                                    auditDetailValuesData.setOption4(auditValues.getOption4());
                                }
                                if (auditValues.getOption4Marks() != null) {
                                    auditDetailValuesData.setOption4Score(auditValues.getOption4Marks());
                                }
                                if (auditValues.getOption5() != null) {
                                    auditDetailValuesData.setOption5(auditValues.getOption5());
                                }
                                if (auditValues.getOption5Marks() != null) {
                                    auditDetailValuesData.setOption5Score(auditValues.getOption5Marks());
                                }
                                if (auditValues.getOption6() != null) {
                                    auditDetailValuesData.setOption6(auditValues.getOption6());
                                }
                                if (auditValues.getOption6Marks() != null) {
                                    auditDetailValuesData.setOption6Score(auditValues.getOption6Marks());
                                }
                                if (auditValues.getOption7() != null) {
                                    auditDetailValuesData.setOption7(auditValues.getOption7());
                                }
                                if (auditValues.getOption7Marks() != null) {
                                    auditDetailValuesData.setOption7Score(auditValues.getOption7Marks());
                                }
                                if (auditValues.getProductId() != null) {
                                    auditDetailValuesData.setProductId(auditValues.getProductId());
                                }
                                if (auditValues.getProductName() != null) {
                                    auditDetailValuesData.setProductName(auditValues.getProductName());
                                }
                                if (auditValues.getTextArea() != null) {
                                    auditDetailValuesData.setTextArea(auditValues.getTextArea());
                                }
                                if (auditValues.getTextValue() != null) {
                                    auditDetailValuesData.setTextValue(auditValues.getTextValue());
                                }
                                if (auditValues.getTimeValue() != null) {
                                    auditDetailValuesData.setTimeValue(auditValues.getTimeValue());
                                }
                                if (auditValues.getUnitId() != null) {
                                    auditDetailValuesData.setUnitId(auditValues.getUnitId());
                                }
                                if (auditValues.getUnitName() != null) {
                                    auditDetailValuesData.setUnitName(auditValues.getUnitName());
                                }
                                if (auditValues.getYesNo() != null) {
                                    auditDetailValuesData.setYesNo(auditValues.getYesNo());
                                }
                            }
                            auditDetailValuesData = auditDao.add(auditDetailValuesData, true);
                            if (auditDetailValuesData == null) {
                                throw new AuditException("Error saving : " + auditValues.getAuditFormValue().getEntityLabel());
                            } else {
                                auditDetailValuesDataList.add(auditDetailValuesData);
                            }
                        } else {
                            throw new AuditException("Audit form value : " + auditValues.getAuditFormValue().getEntityLabel() + " is not correct!");
                        }
                    } else {
                        if (auditFormValuesData.getIsMandatory().equals(FormsServiceConstants.AUDIT_CONSTANT_YES)) {
                            throw new AuditException("Audit form value : " + auditFormValuesData.getEntityLabel() + " is mandatory!");
                        }/* else {
                            if (auditFormValuesData.getScoreCounted() != null && auditFormValuesData.getScoreCounted().equals(FormsServiceConstants.AUDIT_CONSTANT_YES)
                                    && auditFormValuesData.getMaxScore() != null) {
                                throw new AuditException("Audit form value : " + auditFormValuesData.getEntityLabel() + " has score so it should be mandatory!");
                            }
                        }*/
                    }
                }
            }
            auditDetailData.setAcquiredScore(acquiredScore);
            auditDetailData.setTotalScore(totalScore);
            auditDetailData.setProjectedTotalScore(projectedTotalScore);
            BigDecimal percentage = AuditUtil.multiply(AuditUtil.divide(acquiredScore, totalScore), new BigDecimal(100));
            auditDetailData.setProjectedAcquiredScore(AuditUtil.divide(AuditUtil.multiply(percentage, projectedTotalScore), new BigDecimal(100)));
            auditDetailData = auditDao.update(auditDetailData, true);
            if (auditDetailData != null) {
                return auditDetailValuesDataList;
            } else {
                throw new AuditException("Error updating audit scores.");
            }
        } else {
            throw new AuditException("Error saving audit: following value is not present : " + (auditDetailData == null ? "audit detail " : "")
                    + (audit == null ? "audit " : "") + ((audit != null && audit.getValues().size() == 0) ? "audit values " : ""));
        }
    }

    private boolean validAuditValue(AuditValues auditValues, AuditFormValuesData auditFormValuesData) throws AuditException {
        AuditFormValuesData linkedAuditFormValuesData = null;
        if (auditFormValuesData.getLinkedEntityId() != null) {
            linkedAuditFormValuesData = auditDao.find(AuditFormValuesData.class, auditFormValuesData.getLinkedEntityId());
        }
        if (!auditValues.isQuestionOpted() && (auditFormValuesData.getQuestionOptional().equals(FormsServiceConstants.AUDIT_CONSTANT_NO) &&
                (linkedAuditFormValuesData != null && linkedAuditFormValuesData.getQuestionOptional().equals(FormsServiceConstants.AUDIT_CONSTANT_NO)))) {
            throw new AuditException(auditFormValuesData.getEntityLabel() + " is not optional.");
        }

        if (auditValues.isQuestionOpted()) {
            if ((auditValues.getAcquiredScore() == null || auditValues.getMaxScore() == null) &&
                    auditFormValuesData.getScoreCounted().equals(FormsServiceConstants.AUDIT_CONSTANT_YES)) {
                throw new AuditException("Score for " + auditFormValuesData.getEntityLabel() + " is not present.");
            }
            if (auditValues.getAnswerComment() == null && auditFormValuesData.getAdditionalComment().equals(FormsServiceConstants.AUDIT_CONSTANT_YES)) {
                if (auditFormValuesData.getScoreCounted().equals(FormsServiceConstants.AUDIT_CONSTANT_YES) && auditValues.getAcquiredScore().compareTo(auditValues.getMaxScore()) != 0) {
                    throw new AuditException("Comment for " + auditFormValuesData.getEntityLabel() + " is not present.");
                }
            }
           /* if (auditValues.getAttachedDoc() == null && auditFormValuesData.getAttachDoc().equals(FormsServiceConstants.AUDIT_CONSTANT_YES)) {
                throw new AuditException("Document for " + auditFormValuesData.getEntityLabel() + " is not attached.");
            }*/
            if (AuditUtil.getStatus(auditFormValuesData.getIsMandatory())) {
                if (auditFormValuesData.getEntityType().equals(EntityType.DATE.value()) && auditValues.getDateValue() == null) {
                    throw new AuditException("Answer for " + auditFormValuesData.getEntityLabel() + " is mandatory.");
                }
                if (auditFormValuesData.getEntityType().equals(EntityType.UNIT_EMPLOYEE.value()) &&
                        (auditValues.getEmployeeDesignation() == null ||
                                auditValues.getEmployeeId() == null ||
                                auditValues.getEmployeeName() == null)) {
                    throw new AuditException("Answer for " + auditFormValuesData.getEntityLabel() + " is mandatory.");
                }
                if (auditFormValuesData.getEntityType().equals(EntityType.NUMBER.value()) && auditValues.getNumberValue() == null) {
                    throw new AuditException("Answer for " + auditFormValuesData.getEntityLabel() + " is mandatory.");
                }
                if (auditFormValuesData.getEntityType().equals(EntityType.SELECT_BOX.value()) &&
                        auditValues.getOption1() == null &&
                        auditValues.getOption2() == null &&
                        auditValues.getOption3() == null &&
                        auditValues.getOption4() == null &&
                        auditValues.getOption5() == null &&
                        auditValues.getOption6() == null &&
                        auditValues.getOption7() == null &&
                        auditValues.getOption1Marks() == null &&
                        auditValues.getOption2Marks() == null &&
                        auditValues.getOption3Marks() == null &&
                        auditValues.getOption4Marks() == null &&
                        auditValues.getOption5Marks() == null &&
                        auditValues.getOption6Marks() == null &&
                        auditValues.getOption7Marks() == null) {
                    throw new AuditException("Answer for " + auditFormValuesData.getEntityLabel() + " is mandatory.");
                }
                if (auditFormValuesData.getEntityType().equals(EntityType.PRODUCT.value()) && (auditValues.getProductId() == null ||
                        auditValues.getProductName() == null)) {
                    throw new AuditException("Answer for " + auditFormValuesData.getEntityLabel() + " is mandatory.");
                }
                if (auditFormValuesData.getEntityType().equals(EntityType.TEXTAREA.value()) && auditValues.getTextArea() == null) {
                    throw new AuditException("Answer for " + auditFormValuesData.getEntityLabel() + " is mandatory.");
                }
                if (auditFormValuesData.getEntityType().equals(EntityType.TEXT.value()) && auditValues.getTextValue() == null) {
                    throw new AuditException("Answer for " + auditFormValuesData.getEntityLabel() + " is mandatory.");
                }
                if (auditFormValuesData.getEntityType().equals(EntityType.TIME.value()) && auditValues.getTimeValue() == null) {
                    throw new AuditException("Answer for " + auditFormValuesData.getEntityLabel() + " is mandatory.");
                }
                if (auditFormValuesData.getEntityType().equals(EntityType.UNIT.value()) && (auditValues.getUnitId() == null ||
                        auditValues.getUnitName() == null)) {
                    throw new AuditException("Answer for " + auditFormValuesData.getEntityLabel() + " is mandatory.");
                }
                if (auditFormValuesData.getEntityType().equals(EntityType.BOOLEAN.value()) && auditValues.getYesNo() == null) {
                    throw new AuditException("Answer for " + auditFormValuesData.getEntityLabel() + " is mandatory.");
                }
            }
        }
        return true;
    }


}
