package com.stpl.tech.forms.data.dao;

import java.util.List;

public interface AbstractDao {

    public <T> T update(T data, boolean flush);

    public <T> List<T> update(List<T> dataList, boolean flush);

    public <T> T add(T data, boolean flush);

    public <T> List<T> findAll(Class<T> data);

    public <T, R> T find(Class<T> data, R key);

    public <T> void delete(T data);

    public void flush();
}
