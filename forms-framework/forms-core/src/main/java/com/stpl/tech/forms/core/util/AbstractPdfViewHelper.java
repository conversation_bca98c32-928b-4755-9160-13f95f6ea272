package com.stpl.tech.forms.core.util;

import com.itextpdf.html2pdf.HtmlConverter;
import org.springframework.web.servlet.view.AbstractView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;

public class AbstractPdfViewHelper extends AbstractView {

    private String contentType = "application/pdf";

    private String fileName;

    private ByteArrayOutputStream baos = createTemporaryOutputStream();

    public String getContentType() {
        return contentType;
    }

    @Override
    protected void renderMergedOutputModel(Map<String, Object> map, HttpServletRequest request, HttpServletResponse response) throws Exception {
        this.writeToResponse(response, this.baos);
    }

    public void createPdfFromHTML(String content, String fileName) throws IOException {
        HtmlConverter.convertToPdf(content, this.baos);
        this.fileName = fileName;
    }

    protected ByteArrayOutputStream createTemporaryOutputStream() {
        return new ByteArrayOutputStream(4096);
    }

    protected void writeToResponse(HttpServletResponse response, ByteArrayOutputStream baos) throws IOException {
        response.addHeader("Content-Disposition", "attachment; filename=" + this.fileName + ".pdf");
        response.setContentType(this.getContentType());
        response.setContentLength(baos.size());
        ServletOutputStream out = response.getOutputStream();
        baos.writeTo(out);
        out.flush();
    }
}
