package com.stpl.tech.forms.core.service;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.forms.core.exception.WarningLetterException;
import com.stpl.tech.forms.domain.model.DocumentDetail;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.warning.domain.model.EmployeeWarning;
import com.stpl.tech.warning.domain.model.EmployeeWarningStatus;

public interface WarningService {

	public Integer addWarningLetter(EmployeeWarning employeeWarning) throws WarningLetterException, TemplateRenderingException;

	public List<EmployeeWarning> getWarningLetters(Integer unitId, Date startDate, Date endDate, String status,
			Integer amId);

	public EmployeeWarning getWarningLetter(Integer warningId);

	public boolean processWarningAction(EmployeeWarningStatus warningStatus)
			throws TemplateRenderingException, IOException, WarningLetterException;

	public void downloadWarningReport(HttpServletResponse response, Integer warningId)
			throws WarningLetterException, TemplateRenderingException, IOException, DocumentException;

	public boolean saveImages(MultipartFile[] files, Integer warningId);

	public void downloadWarningImage(HttpServletResponse response, Integer imageId) throws IOException, WarningLetterException;

	public boolean processWarningActionJobs(String actionBy);

	public DocumentDetail saveImage(MultipartFile file, int userId);


}
