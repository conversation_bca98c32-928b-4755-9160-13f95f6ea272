package com.stpl.tech.forms.data.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = "WALLET_BUSINESS_COST_CENTER_MAPPING")
public class WalletBusinessCostCenterMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "WALLET_BCC_MAPPING_ID", nullable = false)
    private Integer walletBccMappingId;

    @Column(name = "WALLET_ID",nullable = false)
    private Integer walletId;
    @Column(name = "BCC_ID",nullable = false)
    private Integer bccId;
    @Column(name="BCC_NAME",nullable = false)
    private String bccName;
    @Column(name = "BCC_TYPE")
    private String bccType;
    @Column(name = "BCC_CODE")
    private String bccCode;
    @Column(name="MAPPING_STATUS")
    private String mappingStatus;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ADD_TIME", nullable = false, length = 19)
    private Date addTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATE_TIME", length = 19)
    private Date lastUpdateTime;

    @Column(name = "LAST_UPDATED_BY",nullable = false,length = 100)
    private String lastUpdatedBy;

}
