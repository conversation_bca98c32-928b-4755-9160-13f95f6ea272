package com.stpl.tech.forms.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "WALLET_APPROVER_MAPPING")
public class WalletApproverMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="WALLET_MAPPING_ID",nullable = false)
    private Integer walletApproverMappingId;

    @Column(name="WALLET_ID",nullable = false)
    private Integer walletId;

    @Column(name="APPROVER_ID",nullable = false)
    private Integer approverId;

    @Column(name="APPROVER_NAME",nullable = false)
    private String approverName;

    @Column(name="MAPPING_STATUS",nullable = false)
    private String mappingStatus;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name="ADD_TIME",nullable = false)
    private Date addTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name="UPDATED_TIME",nullable = false)
    private Date updatedTime;

    @Column(name="UPDATED_BY",nullable = false)
    private String updatedBy;
}
