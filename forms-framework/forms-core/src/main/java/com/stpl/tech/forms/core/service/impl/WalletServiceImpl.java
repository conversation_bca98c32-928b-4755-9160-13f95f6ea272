package com.stpl.tech.forms.core.service.impl;

import com.google.common.net.MediaType;
import com.stpl.tech.expense.domain.model.RejectionEntity;
import com.stpl.tech.expense.domain.model.VoucherRejection;
import com.stpl.tech.expense.domain.model.VoucherRejectionRequest;
import com.stpl.tech.expense.domain.model.WalletRequest;
import com.stpl.tech.expense.domain.model.WalletTransactionEntityType;
import com.stpl.tech.forms.core.exception.WalletException;
import com.stpl.tech.forms.core.mapper.VoucherCostCenterAllocationMapper;
import com.stpl.tech.forms.core.mapper.WalletMapper;
import com.stpl.tech.forms.core.service.EnvProperties;
import com.stpl.tech.forms.core.service.WalletService;
import com.stpl.tech.forms.data.WalletDataConverter;
import com.stpl.tech.forms.data.dao.WalletDao;
import com.stpl.tech.forms.data.model.ClaimDetailData;
import com.stpl.tech.forms.data.model.VoucherRejectionData;
import com.stpl.tech.forms.data.model.WalletApproverMapping;
import com.stpl.tech.forms.data.model.WalletBusinessCostCenterMapping;
import com.stpl.tech.forms.data.model.WalletData;
import com.stpl.tech.forms.data.model.WalletTransactionData;
import com.stpl.tech.forms.domain.model.BusinessCostCenters;
import com.stpl.tech.forms.domain.model.FileStorageType;
import com.stpl.tech.forms.domain.model.IdCodeName;
import com.stpl.tech.forms.domain.model.IssuingEntity;
import com.stpl.tech.forms.domain.model.TransitionType;
import com.stpl.tech.forms.domain.model.Voucher;
import com.stpl.tech.forms.domain.model.VoucherCostCenterAllocation;
import com.stpl.tech.forms.domain.model.VoucherStatus;
import com.stpl.tech.forms.domain.model.Wallet;
import com.stpl.tech.forms.domain.model.WalletAccountType;
import com.stpl.tech.forms.domain.model.WalletApproverMappingVO;
import com.stpl.tech.forms.domain.model.WalletBusinessCostCenterMappingVO;
import com.stpl.tech.forms.domain.model.WalletStatus;
import com.stpl.tech.forms.domain.model.WalletTransactionCode;
import com.stpl.tech.forms.domain.model.WalletTransactionCodeType;
import com.stpl.tech.forms.domain.model.WalletTransactionType;
import com.stpl.tech.kettle.data.expense.model.VoucherCostCenterAllocationEntity;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.expense.model.VoucherFileData;
import com.stpl.tech.kettle.data.expense.model.VoucherStatusData;
import com.stpl.tech.master.budget.metadata.model.ExpenseValidation;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class WalletServiceImpl implements WalletService {

    @Autowired
    private MasterDataCache masterDataCache;
    @Autowired
    private EnvProperties envProperties;
    @Autowired
    private WalletDao walletDao;
    @Autowired
    private FileArchiveService fileArchiveService;

    private static final Logger LOG = LoggerFactory.getLogger(WalletServiceImpl.class);

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Wallet getWalletDetails(String accountType, String walletType, String accountNo) throws WalletException {
        if (!StringUtils.isEmpty(accountNo)) {
            WalletData walletData = walletDao.getWalletDetails(accountType, walletType, accountNo);
            Wallet wallet = null;
            if (walletData != null) {
                wallet = WalletDataConverter.convert(walletData);
                BigDecimal topupAmount = getTopupAmount(walletData);
                if (topupAmount.compareTo(BigDecimal.ZERO) < 0) {
                    topupAmount = BigDecimal.ZERO;
                }
                wallet.setTopupAmount(topupAmount);
            }
            return wallet;
        } else {
            throw new WalletException("Please provide valid account number");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Wallet> getWallets(WalletRequest walletRequest) throws WalletException {
        if (walletRequest.getAccountNos() != null && walletRequest.getAccountNos().size() > 0) {
            List<WalletData> walletDatas = walletDao.getWallets(walletRequest);
            List<Wallet> wallets = new ArrayList<>();
            for (WalletData walletData : walletDatas) {
                Wallet wallet = null;
                if (walletData != null) {
                    wallet = WalletDataConverter.convert(walletData);
                    BigDecimal topupAmount = getTopupAmount(walletData);
                    if (topupAmount.compareTo(BigDecimal.ZERO) < 0) {
                        topupAmount = BigDecimal.ZERO;
                    }
                    wallet.setTopupAmount(topupAmount);
                }
                wallets.add(wallet);
            }
            return wallets;
        }
        throw new WalletException("No account numbers given.");
    }

    @SuppressWarnings("deprecation")

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String issueVoucher(Voucher voucher) throws WalletException {
        if (voucher != null && voucher.getWalletId() != null) {
            WalletData walletData = walletDao.find(WalletData.class, voucher.getWalletId());
            if (walletData != null) {
                if (walletData.getCurrentBalance().compareTo(voucher.getIssuedAmount()) >= 0) {
                    Date currentTime = AppUtils.getCurrentTimestamp();
                    VoucherData voucherData = new VoucherData();
                    voucherData.setGeneratedVoucherId(getUniqueGeneratedVoucherId());
                    voucherData.setAccountType(voucher.getAccountType().name());
                    voucherData.setAccountNo(voucher.getAccountNo());
                    voucherData.setWalletId(voucher.getWalletId());
                    voucherData.setBusinessDate(AppUtils.getCurrentBusinessDate());
                    if (voucher.getExpenseType() != null) {
                        voucherData.setExpenseType(voucher.getExpenseType());
                    }
                    // expenseDetail holds expense comment data
                    if (voucher.getExpenseDetail() != null) {
                        voucherData.setExpenseDetail(voucher.getExpenseDetail());
                    }
                    voucherData.setCurrentStatus(VoucherStatus.PENDING_SETTLEMENT.name());
                    voucherData.setIssuedAmount(voucher.getIssuedAmount());
                    voucherData.setIssuedTo(voucher.getIssuedTo().getId());
                    voucherData.setIssuedTime(currentTime);
                    voucherData.setIssuedBy(voucher.getIssuedBy().getId());
                    voucherData.setLastUpdatedTime(currentTime);
                    voucherData.setAccountableInPNL(voucher.getAccountableInPNL() ? AppConstants.YES : AppConstants.NO);
                    voucherData.setBudgetCategory(voucher.getBudgetCategory());
                    voucherData.setExpenseCategory(voucher.getExpenseCategory());
                    voucherData.setExpenseMetadataId(voucher.getExpenseMetadataId());

                    voucherData = walletDao.add(voucherData, true);
                    if (voucherData == null) {
                        throw new WalletException("Error creating voucher.");
                    }
                    saveVoucherStatus(VoucherStatus.INITIATED, VoucherStatus.PENDING_SETTLEMENT,
                            (voucher.getExpenseDetail() != null ? voucher.getExpenseDetail() : "ISSUE_VOUCHER"), voucherData,
                            voucher, currentTime);

                    saveWalletTransaction(walletData, WalletTransactionType.DEBIT, WalletTransactionCode.REDEMPTION,
                            WalletTransactionCodeType.ISSUE_VOUCHER, voucher.getIssuedAmount(), currentTime,
                            voucherData.getIssuedBy(), WalletTransactionEntityType.VOUCHER, voucherData.getId(), voucher.getEntity());

                    walletData.setCurrentBalance(
                            AppUtils.subtract(walletData.getCurrentBalance(), voucherData.getIssuedAmount()));
                    walletData.setIssuedAmount(AppUtils.add(walletData.getIssuedAmount(), voucherData.getIssuedAmount()));
                    walletData.setLastUpdateTime(currentTime);
                    walletData = walletDao.update(walletData, true);
                    saveVoucherCostCenterAllocation(voucher.getVoucherCostCenterAllocations(),walletData.getCanAllocateCostToCafes(),voucherData);
                    if (walletData != null) {
                        return voucherData.getGeneratedVoucherId();
                    } else {
                        throw new WalletException("Error updating wallet linked to voucher.");
                    }
                } else {
                    throw new WalletException("Available balance is " + walletData.getCurrentBalance()
                            + ". Insufficient balance to create this voucher.", 500);
                }
            } else {
                throw new WalletException("Please provide valid wallet id.");
            }
        } else {
            throw new WalletException("Please provide valid request data.");
        }
    }

    private void saveVoucherCostCenterAllocation(List<VoucherCostCenterAllocation> voucherCostCenterAllocations, String validate, VoucherData voucherData) throws WalletException{
        if (voucherCostCenterAllocations.isEmpty()) {
            throw new WalletException("Voucher Cost Center Allocation Cannot be present");
        }
        List<VoucherCostCenterAllocationEntity> voucherCostCenterAllocationEntity = VoucherCostCenterAllocationMapper.INSTANCE.toDtoList(voucherCostCenterAllocations);
        List<WalletBusinessCostCenterMapping> walletBusinessCostCenterMappings = walletDao.findBusinessCostCenterMappingsByWalletId(voucherData.getWalletId());
        Set<Integer> businessCostCenterId = walletBusinessCostCenterMappings.stream().map(WalletBusinessCostCenterMapping::getBccId)
                .collect(Collectors.toSet());
        for (VoucherCostCenterAllocationEntity costCenterAllocationEntity : voucherCostCenterAllocationEntity) {
            if (AppConstants.YES.equalsIgnoreCase(validate)) {
                costCenterAllocationEntity.setForceAllocation(
                        businessCostCenterId.contains(costCenterAllocationEntity.getBusinessCostCenterId())
                                ? AppConstants.NO : AppConstants.YES);
            } else {
                if (!businessCostCenterId.contains(costCenterAllocationEntity.getBusinessCostCenterId())) {
                    throw new WalletException("Business Cost Center No Allocated");
                }
                costCenterAllocationEntity.setForceAllocation(AppConstants.NO);
            }
            costCenterAllocationEntity.setVoucherId(voucherData.getId());
            costCenterAllocationEntity.setIssuedTime(AppUtils.getCurrentTimestamp());
            walletDao.add(costCenterAllocationEntity, true);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean checkPendingVouchers(Integer walletId) throws WalletException {
        if (walletId != null) {
            long tat = 3;
            Date date = AppUtils.getDayBeforeOrAfterCurrentDay(-Math.toIntExact(tat));

            long pendingIssuedVoucher = walletDao.getVoucherCount(Collections.singletonList(walletId), getPendingVoucherStatusList(), date);
            if (pendingIssuedVoucher > 0) {
                throw new WalletException("You have " + pendingIssuedVoucher + " Pending Settlement Voucher older than " + tat + " days.Please settle them first to create voucher.", 500);
            }
            long pendingRejectedVoucher = walletDao.getVoucherCount(Collections.singletonList(walletId), getPendingRejectedStatusList(), date);
            if (pendingRejectedVoucher > 0) {
                throw new WalletException("You have " + pendingRejectedVoucher + " Pending Rejected Voucher older than " + tat + " days.Please acknowledge them first to create voucher.", 500);
            }
            return true;
        } else {
            throw new WalletException("Please provide valid wallet id.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean checkPendingVouchersWithAssociatedId(Integer associatedId) throws WalletException {
        if (associatedId != null) {
            long tat = 3;
            Date date = AppUtils.getDayBeforeOrAfterCurrentDay(-Math.toIntExact(tat));
            List<WalletData> walletData = walletDao.getWalletDataWithAssociatedId(associatedId);
            List<Integer> walletId = walletData.stream().map(WalletData::getId).collect(Collectors.toList());
            Map<CacheReferenceType, String> cacheReferenceMetadata = masterDataCache.getCacheReferenceMetadata();
            final AtomicReference<Float> voucherCreationLimit = new AtomicReference<>(Float.valueOf(0));
            cacheReferenceMetadata.entrySet().forEach(cacheReferenceTypeStringEntry -> {
                if(cacheReferenceTypeStringEntry.getKey().name().equalsIgnoreCase(CacheReferenceType.VOUCHER_CREATION_LIMIT.name())){
                    voucherCreationLimit.set(Float.valueOf(cacheReferenceTypeStringEntry.getValue()));
                }
            });
            if(Objects.equals(voucherCreationLimit.get(), (float) 0)){
                voucherCreationLimit.set((float)25000) ;
            }

//            long pendingIssuedVoucher = walletDao.getVoucherCount(walletId, getPendingVoucherStatusList(), date);
//            if (pendingIssuedVoucher > 0) {
//                throw new WalletException("You have " + pendingIssuedVoucher + " Pending Settlement Voucher older than " + tat + " days.Please settle them first to create voucher.", 500);
//            }
            List<String> errors = new ArrayList<>();
            List<VoucherData> voucherData =  walletDao.getPendingVouchers(walletId,getPendingVoucherStatusList());
            Map<Integer, Integer> dayCount = new HashMap<>();
            Map<Integer, BigDecimal> pendingIssuedAmount = new HashMap<>();

            for (VoucherData voucher : voucherData) {
                if (WalletAccountType.UNIT.name().equals(voucher.getAccountType())){
                    int daysSinceBusinessDate = (int)
                            AppUtils.getDayDifference(voucher.getBusinessDate(), AppUtils.getCurrentTimestamp());
                    if (daysSinceBusinessDate > 3) {
                        dayCount.put(voucher.getWalletId(), dayCount.getOrDefault(voucher.getWalletId(), 0) + 1);
                    }
                }
                BigDecimal currentPendingAmount = pendingIssuedAmount.getOrDefault(voucher.getWalletId(), BigDecimal.ZERO);
                pendingIssuedAmount.put(voucher.getWalletId(), currentPendingAmount.add(voucher.getIssuedAmount()));
            }

            for (Map.Entry<Integer, BigDecimal> entry : pendingIssuedAmount.entrySet()) {
                if (WalletAccountType.UNIT.name().equals(walletData.get(0).getAccountType())
                && entry.getValue().compareTo(BigDecimal.valueOf(10000)) > 0){
                    errors.add("Your current pending voucher has value of " + entry.getValue() + " for wallet Id " + entry.getKey() +". It should not be greater than 2500"+ "\n");
                }
                else if(WalletAccountType.EMPLOYEE.name().equals(walletData.get(0).getAccountType())
                 && entry.getValue().compareTo(BigDecimal.valueOf(voucherCreationLimit.get())) > 0){
                    errors.add("You have pending voucher value more than " + entry.getValue() + " for wallet Id " + entry.getKey() + "\n");
                }
            }

            for (Map.Entry<Integer, Integer> entry : dayCount.entrySet()) {
                errors.add("You have " + entry.getValue() + " pending vouchers for more than 3 days for wallet Id " + entry.getKey() + "\n");
            }
            if (!errors.isEmpty()) {
                throw new WalletException(errors.toString());
            }

            long pendingRejectedVoucher = walletDao.getVoucherCount(walletId, getPendingRejectedStatusList(), date);
            if (pendingRejectedVoucher > 0) {
                throw new WalletException("You have " + pendingRejectedVoucher + " Pending Rejected Voucher older than " + tat + " days.Please acknowledge them first to create voucher.", 500);
            }
            return true;
        } else {
            throw new WalletException("Please provide valid wallet id.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean uploadVoucherDoc(MultipartFile file, Integer voucherId, String fileType) throws WalletException {
        if (voucherId == null) {
            throw new WalletException("Please provide valid voucher id.");
        }
        if (fileType == null) {
            throw new WalletException("Please provide valid file type.");
        }
        boolean result = false;
        Date currentTimeStamp = AppUtils.getCurrentTimestamp();
        VoucherData voucherData = walletDao.find(VoucherData.class, voucherId);
        if (voucherData != null) {
            int count = 0;
            if (file.isEmpty()) {
                throw new WalletException("Empty file is not allowed.");
            }
            if (file.getContentType().equalsIgnoreCase(MediaType.JPEG.toString()) || file.getContentType().equalsIgnoreCase(MediaType.PNG.toString()) ||
                    file.getContentType().equalsIgnoreCase(MediaType.PDF.toString())) {
                String fileExtension = file.getContentType().substring(file.getContentType().indexOf("/") + 1);
                String fileName = voucherData.getGeneratedVoucherId() + "_" + count++ + "." + fileExtension;
                String baseDir = "VoucherInvoice/" + voucherData.getGeneratedVoucherId();
                FileDetail s3File = fileArchiveService.saveFileToS3(envProperties.getS3AuditBucket(), baseDir, fileName,
                        file);
                if (s3File != null) {
                    VoucherFileData data = new VoucherFileData();
                    data.setFileName(fileName);
                    data.setFileType(fileType);
                    data.setS3Url(s3File.getUrl());
                    data.setVoucherData(voucherData);
                    data.setCreatedOn(currentTimeStamp);
                    data.setStatus(WalletStatus.ACTIVE.name());
                    data.setContentType(file.getContentType());
                    data.setS3Key(s3File.getKey());
                    data.setS3Bucket(s3File.getBucket());
                    data.setStorageType(FileStorageType.S3.name());
                    walletDao.add(data, true);
                    result = true;
                }
            } else {
                throw new WalletException("Only JPEG, PNG and PDF files are allowed.");
            }
            return result;
        } else {
            throw new WalletException("Please send valid voucher id.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean settleVoucher(Voucher voucher) throws WalletException {
        if (voucher != null && voucher.getWalletId() != null && voucher.getId() != null) {
            Date currentTime = AppUtils.getCurrentTimestamp();
            WalletData walletData = walletDao.find(WalletData.class, voucher.getWalletId());
            if (walletData != null) {
                VoucherData voucherData = walletDao.find(VoucherData.class, voucher.getId());
                if (voucherData != null) {
                    if (VoucherStatus.PENDING_SETTLEMENT.name().equals(voucherData.getCurrentStatus())) {
                        saveVoucherStatus(VoucherStatus.valueOf(voucherData.getCurrentStatus()), VoucherStatus.AM_PENDING,
                                voucher.getActionComment(), voucherData, voucher, currentTime);

                        if (voucher.getExpenseAmount().compareTo(BigDecimal.ZERO) == 0) {
                            voucherData.setCurrentStatus(VoucherStatus.APPROVED.name());
                        } else {
                            voucherData.setCurrentStatus(VoucherStatus.AM_PENDING.name());
                        }

                        voucherData.setLastUpdatedTime(currentTime);
                        voucherData.setExpenseAmount(voucher.getExpenseAmount());
                        List<VoucherData> existingVoucher = walletDao.findVoucherByGrNumber(voucher.getGrNumber());
                        if (!existingVoucher.isEmpty()) {
                            throw new WalletException("GR Number: " + voucher.getGrNumber() + " already exists for Voucher id: "+ existingVoucher.get(0).getGeneratedVoucherId() +". Please contact finance team for support.");
                        }
                        voucherData.setGrNumber(voucher.getGrNumber());
                        if (Objects.isNull(voucher.getVoucherDate())) {
                            throw new WalletException("Error updating voucher details.");
                        }else {
                            voucherData.setVoucherDate(voucher.getVoucherDate());
                        }
                        voucherData = walletDao.update(voucherData, true);
                        if (voucherData == null) {
                            throw new WalletException("Error updating voucher details.");
                        }
                        if (voucher.getIssuedAmount().compareTo(voucher.getExpenseAmount()) > 0) {
                            BigDecimal amount = AppUtils.subtract(voucher.getIssuedAmount(), voucher.getExpenseAmount());

                            saveWalletTransaction(walletData, WalletTransactionType.CREDIT, WalletTransactionCode.ADDITION,
                                    WalletTransactionCodeType.RETURNED_AMOUNT, amount, currentTime, voucher.getIssuedBy().getId(),
                                    WalletTransactionEntityType.VOUCHER, voucherData.getId(), voucher.getEntity());

                            walletData.setIssuedAmount(AppUtils.subtract(walletData.getIssuedAmount(), amount));
                            walletData.setCurrentBalance(AppUtils.add(walletData.getCurrentBalance(), amount));
                        } else if (voucher.getIssuedAmount().compareTo(voucher.getExpenseAmount()) < 0) {
                            BigDecimal amount = AppUtils.subtract(voucher.getExpenseAmount(), voucher.getIssuedAmount());
                            if (walletData.getCurrentBalance().compareTo(amount) < 0) {
                                throw new WalletException("Available balance is " + walletData.getCurrentBalance()
                                        + ". Insufficeint balance to create this voucher with expense amount  "
                                        + voucher.getExpenseAmount() + ".", 500);
                            }
                            saveWalletTransaction(walletData, WalletTransactionType.DEBIT, WalletTransactionCode.REDEMPTION,
                                    WalletTransactionCodeType.EXCEEDED_AMOUNT, amount, currentTime, voucher.getIssuedBy().getId(),
                                    WalletTransactionEntityType.VOUCHER, voucherData.getId(), voucher.getEntity());

                            walletData.setIssuedAmount(AppUtils.add(walletData.getIssuedAmount(), amount));
                            walletData.setCurrentBalance(AppUtils.subtract(walletData.getCurrentBalance(), amount));
                        }

                        walletData.setLastUpdateTime(currentTime);
                        walletData = walletDao.update(walletData, true);
                        if (walletData != null) {
                            return true;
                        } else {
                            throw new WalletException("Error updating wallet linked to voucher.");
                        }
                    } else {
                        throw new WalletException("Voucher status is not valid for this transaction.");
                    }
                } else {
                    throw new WalletException("Voucher id is not valid.");
                }
            } else {
                throw new WalletException("Wallet id is not valid.");
            }
        } else {
            throw new WalletException("Please provide valid request parameters.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean cancelVoucher(Voucher voucher) throws WalletException {
        if (voucher != null && voucher.getWalletId() != null && voucher.getId() != null) {
            Date currentTime = AppUtils.getCurrentTimestamp();
            WalletData walletData = walletDao.find(WalletData.class, voucher.getWalletId());
            if (walletData != null) {
                VoucherData voucherData = walletDao.find(VoucherData.class, voucher.getId());
                if (voucherData != null) {
                    if (VoucherStatus.PENDING_SETTLEMENT.name().equals(voucherData.getCurrentStatus())) {
                        saveVoucherStatus(VoucherStatus.valueOf(voucherData.getCurrentStatus()), VoucherStatus.CANCELLED,
                                voucher.getActionComment(), voucherData, voucher, currentTime);
                        voucherData.setCurrentStatus(VoucherStatus.CANCELLED.name());
                        voucherData.setLastUpdatedTime(currentTime);
                        voucherData.setGrNumber(null);
                        voucherData = walletDao.update(voucherData, true);
                        if (voucherData == null) {
                            throw new WalletException("Error updating voucher details.");
                        }
                        saveWalletTransaction(walletData, WalletTransactionType.CREDIT, WalletTransactionCode.ADDITION,
                                WalletTransactionCodeType.CANCELLED, voucher.getIssuedAmount(), currentTime,
                                voucher.getIssuedBy().getId(), WalletTransactionEntityType.VOUCHER, voucherData.getId(), voucher.getEntity());

                        walletData.setIssuedAmount(AppUtils.subtract(walletData.getIssuedAmount(), voucher.getIssuedAmount()));
                        walletData.setCurrentBalance(AppUtils.add(walletData.getCurrentBalance(), voucherData.getIssuedAmount()));
                        walletData.setLastUpdateTime(currentTime);
                        walletData = walletDao.update(walletData, true);
                        if (walletData != null) {
                            return true;
                        } else {
                            throw new WalletException("Error updating wallet linked to voucher.");
                        }
                    } else {
                        throw new WalletException("Voucher status is not valid for this transaction.");
                    }
                } else {
                    throw new WalletException("Please provide valid voucher id.");
                }
            } else {
                throw new WalletException("Please provide valid wallet id.");
            }
        } else {
            throw new WalletException("Please provide valid request parameters.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Voucher> getVoucherList(List<String> status, Date startDate, Date endDate, List<String> accountNo,
                                        Boolean isReimbursed, boolean getStatus, boolean getFileData) {
        List<VoucherData> voucherDatas = walletDao.getVoucherList(status, startDate, endDate, accountNo, isReimbursed);
        List<Voucher> vouchers = new ArrayList<>();
        voucherDatas.forEach(data -> {
            WalletData walletData = walletDao.find(WalletData.class, data.getWalletId());
            Voucher voucher = WalletDataConverter.convert(data, walletData, getStatus, getFileData, null, masterDataCache, null);
            List<VoucherCostCenterAllocation> voucherCostCenterAllocations = getVoucherCostCenterAllocation(voucher.getId());
            voucher.setVoucherCostCenterAllocations(voucherCostCenterAllocations);
            vouchers.add(WalletDataConverter.convert(data, walletData, getStatus, getFileData, null, masterDataCache, null));
        });
        return vouchers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Voucher> getVouchersByFinancePendingDate(List<String> status, Date startDate, Date endDate, List<String> accountNo,
                                        Boolean isReimbursed, boolean getStatus, boolean getFileData) {
        List<VoucherData> voucherDatas = walletDao.getVouchersByFinancePendingDate(status, startDate, endDate, accountNo, isReimbursed);
        List<Voucher> vouchers = new ArrayList<Voucher>();
        voucherDatas.forEach(data -> {
            WalletData walletData = walletDao.find(WalletData.class, data.getWalletId());
            vouchers.add(WalletDataConverter.convert(data, walletData, getStatus, getFileData, null, masterDataCache, null));
        });
        return vouchers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Voucher getVoucherDetail(Integer voucherId) throws WalletException {
        if (voucherId != null) {
            VoucherData voucherData = walletDao.find(VoucherData.class, voucherId);
            WalletData walletData = walletDao.find(WalletData.class, voucherData.getWalletId());
            List<VoucherRejectionData> voucherRejectionList = walletDao.getVoucherRejections(voucherId);
            List<VoucherRejection> voucherRejections = new ArrayList<>();
            if (voucherRejectionList != null && voucherRejectionList.size() > 0) {
                IdCodeName rejectedBy = new IdCodeName(voucherRejectionList.get(0).getRejectedBy(), masterDataCache.getEmployee(voucherRejectionList.get(0).getRejectedBy()));
                for (VoucherRejectionData voucherRejectionData : voucherRejectionList) {
                    voucherRejections.add(WalletDataConverter.convert(voucherRejectionData, rejectedBy));
                }
            }
            return WalletDataConverter.convert(voucherData, walletData, true, true, null, masterDataCache, voucherRejections);
        } else {
            throw new WalletException("Please provide valid voucher id.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Long getPendingRejectedVoucherCount(Integer walletId) {
        return walletDao.getPendingRejectedVoucherCount(walletId, getPendingRejectedStatusList());

    }

    private List<String> getPendingRejectedStatusList() {
        return Arrays
                .asList(VoucherStatus.PENDING_REJECTION_AM.name(), VoucherStatus.PENDING_REJECTION_FINANCE.name());
    }

    private List<String> getPendingVoucherStatusList() {
        return Arrays
                .asList(VoucherStatus.PENDING_SETTLEMENT.name(), VoucherStatus.AM_PENDING.name());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void downloadInvoice(HttpServletResponse response, Integer imageId) throws IOException, WalletException {
        if (imageId != null) {

            VoucherFileData data = walletDao.find(VoucherFileData.class, imageId);
            FileDetail fileDetail = new FileDetail(data.getS3Bucket(), data.getS3Key(), data.getS3Url());
            File file = fileArchiveService.getFileFromS3(envProperties.getBasePath() + File.separator + "s3",
                    fileDetail);
            if (file != null) {
                response.setContentType(data.getContentType());
                response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
                byte[] bytesArray = new byte[(int) file.length()];
                response.setContentLength(bytesArray.length);
                try {
                    OutputStream outputStream = response.getOutputStream();
                    InputStream inputStream = new FileInputStream(file);
                    int counter = 0;
                    while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
                        outputStream.write(bytesArray, 0, counter);
                        outputStream.flush();
                    }
                    outputStream.close();
                    inputStream.close();
                } catch (IOException e) {
                    LOG.error("Encountered error while writing warning image file to response stream", e);
                    throw e;
                } finally {
                    response.getOutputStream().flush();
                    file.delete(); // delete the temporary file created
                    // after completing request
                }
            }
        } else {
            throw new WalletException("Invoice not available");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean amApproveVoucher(Voucher voucher) throws WalletException {
        if (voucher != null && voucher.getId() != null && voucher.getWalletId() != null) {
            Date currentTime = AppUtils.getCurrentTimestamp();
            WalletData walletData = walletDao.find(WalletData.class, voucher.getWalletId());
            if (walletData != null) {
                VoucherData voucherData = walletDao.find(VoucherData.class, voucher.getId());
                if (voucherData != null) {
                    if (VoucherStatus.AM_PENDING.name().equals(voucherData.getCurrentStatus())) {
                        VoucherStatus toStatus = VoucherStatus.FINANCE_PENDING;
//                        if(voucherData.getExpenseAmount().compareTo(envProperties.getFinanceAutoApproveLimit()) < 1) {
//                            toStatus = VoucherStatus.APPROVED;
//                        }
                        saveVoucherStatus(VoucherStatus.valueOf(voucherData.getCurrentStatus()), toStatus,
                                voucher.getActionComment(), voucherData, voucher, currentTime);
                        voucherData.setCurrentStatus(toStatus.name());
                        voucherData.setLastUpdatedTime(currentTime);
                        voucherData = walletDao.update(voucherData, true);
                        if (voucherData == null) {
                            throw new WalletException("Error updating voucher details.");
                        }
                        walletData.setIssuedAmount(AppUtils.subtract(walletData.getIssuedAmount(), voucher.getExpenseAmount()));
                        if(toStatus.equals(VoucherStatus.FINANCE_PENDING)) {
                            walletData.setPendingApproval(AppUtils.add(walletData.getPendingApproval(), voucher.getExpenseAmount()));
                        } else {
                            walletData.setApprovedAmount(AppUtils.add(walletData.getApprovedAmount(), voucher.getExpenseAmount()));
                            walletData.setSpentAmount(AppUtils.add(walletData.getSpentAmount(), voucher.getExpenseAmount()));
                        }
                        walletData.setLastUpdateTime(currentTime);
                        walletData = walletDao.update(walletData, true);
                        if (walletData != null) {
                            return true;
                        } else {
                            throw new WalletException("Error updating wallet linked to voucher.");
                        }
                    } else {
                        throw new WalletException("Voucher status is not valid for this transaction.");
                    }
                } else {
                    throw new WalletException("Voucher id is not valid.");
                }
            } else {
                throw new WalletException("Wallet id is not valid.");
            }
        } else {
            throw new WalletException("Please provide required request parameters.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean amRejectVoucher(VoucherRejectionRequest request) throws WalletException {
        if (request != null && request.getVoucherId() != null && request.getActionBy() != null) {
            Date currentTime = AppUtils.getCurrentTimestamp();
            VoucherData voucherData = walletDao.find(VoucherData.class, request.getVoucherId());
            if (voucherData != null) {
                if (VoucherStatus.AM_PENDING.name().equals(voucherData.getCurrentStatus())) {
                    WalletData walletData = walletDao.find(WalletData.class, voucherData.getWalletId());
                    if (walletData != null) {
                        saveVoucherStatus(VoucherStatus.valueOf(voucherData.getCurrentStatus()), VoucherStatus.PENDING_REJECTION_AM,
                                request.getComment(), voucherData, request.getActionBy().getId(), currentTime);
                        for (ExpenseValidation expenseValidation : request.getRejections()) {
                            VoucherRejectionData data = new VoucherRejectionData();
                            data.setRejectedBy(request.getActionBy().getId());
                            data.setRejectionName(expenseValidation.getValidationName());
                            data.setVoucherId(voucherData.getId());
                            data.setRejectionEntity(RejectionEntity.AREA_MANAGER.name());
                            data = walletDao.update(data, true);
                            if (data == null) {
                                throw new WalletException("Error saving rejection reasons.");
                            }
                        }
                        voucherData.setCurrentStatus(VoucherStatus.PENDING_REJECTION_AM.name());
                        voucherData.setLastUpdatedTime(currentTime);
                        voucherData.setGrNumber(null);
                        voucherData = walletDao.update(voucherData, true);
                        if (voucherData == null) {
                            throw new WalletException("Error updating voucher details.");
                        }
                        walletData.setIssuedAmount(AppUtils.subtract(walletData.getIssuedAmount(), voucherData.getExpenseAmount()));
                        walletData.setRejectedAmount(AppUtils.add(walletData.getRejectedAmount(), voucherData.getExpenseAmount()));
                        walletData.setLastUpdateTime(currentTime);
                        walletData = walletDao.update(walletData, true);
                        if (walletData != null) {
                            return true;
                        } else {
                            throw new WalletException("Error updating wallet linked to voucher.");
                        }
                    } else {
                        throw new WalletException("Wallet not found for this voucher.");
                    }
                } else {
                    throw new WalletException("Voucher status is not valid for this transaction.");
                }
            } else {
                throw new WalletException("Voucher id not valid.");
            }
        } else {
            throw new WalletException("Please provide required request parameters.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean financeApproveVoucher(Voucher voucher) throws WalletException {
        if (voucher != null && voucher.getWalletId() != null && voucher.getId() != null) {
            Date currentTime = AppUtils.getCurrentTimestamp();
            WalletData walletData = walletDao.find(WalletData.class, voucher.getWalletId());
            if (walletData != null) {
                VoucherData voucherData = walletDao.find(VoucherData.class, voucher.getId());
                if (voucherData != null) {
                    if (VoucherStatus.FINANCE_PENDING.name().equals(voucherData.getCurrentStatus())) {
                        saveVoucherStatus(VoucherStatus.valueOf(voucherData.getCurrentStatus()), VoucherStatus.APPROVED,
                                voucher.getActionComment(), voucherData, voucher, currentTime);
                        voucherData.setCurrentStatus(VoucherStatus.APPROVED.name());
                        voucherData.setLastUpdatedTime(currentTime);
                        voucherData = walletDao.update(voucherData, true);
                        if (voucherData == null) {
                            throw new WalletException("Error updating voucher details.");
                        }
                        walletData.setPendingApproval(AppUtils.subtract(walletData.getPendingApproval(), voucherData.getExpenseAmount()));
                        walletData.setApprovedAmount(AppUtils.add(walletData.getApprovedAmount(), voucherData.getExpenseAmount()));
                        walletData.setSpentAmount(AppUtils.add(walletData.getSpentAmount(), voucherData.getExpenseAmount()));
                        walletData.setLastUpdateTime(currentTime);
                        walletData = walletDao.update(walletData, true);
                        if (walletData != null) {
                            return true;
                        } else {
                            throw new WalletException("Error updating wallet linked to voucher.");
                        }
                    } else {
                        throw new WalletException("Voucher status is not valid for this transaction.");
                    }
                } else {
                    throw new WalletException("Voucher id not valid.");
                }
            } else {
                throw new WalletException("Wallet id not valid.");
            }
        } else {
            throw new WalletException("Please provide required request parameters.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean financeRejectVoucher(VoucherRejectionRequest request) throws WalletException {
        if (request != null && request.getVoucherId() != null) {
            Date currentTime = AppUtils.getCurrentTimestamp();
            VoucherData voucherData = walletDao.find(VoucherData.class, request.getVoucherId());
            if (voucherData != null) {
                WalletData walletData = walletDao.find(WalletData.class, voucherData.getWalletId());
                if (walletData != null) {
                    if (VoucherStatus.FINANCE_PENDING.name().equals(voucherData.getCurrentStatus())) {
                        saveVoucherStatus(VoucherStatus.valueOf(voucherData.getCurrentStatus()),
                                VoucherStatus.PENDING_REJECTION_FINANCE, request.getComment(), voucherData, request.getActionBy().getId(), currentTime);
                        for (ExpenseValidation expenseValidation : request.getRejections()) {
                            VoucherRejectionData data = new VoucherRejectionData();
                            data.setRejectedBy(request.getActionBy().getId());
                            data.setRejectionName(expenseValidation.getValidationName());
                            data.setVoucherId(voucherData.getId());
                            data.setRejectionEntity(RejectionEntity.FINANCE.name());
                            data = walletDao.update(data, true);
                            if (data == null) {
                                throw new WalletException("Error saving rejection reasons.");
                            }
                        }
                        voucherData.setCurrentStatus(VoucherStatus.PENDING_REJECTION_FINANCE.name());
                        voucherData.setLastUpdatedTime(currentTime);
                        voucherData.setGrNumber(null);
                        voucherData = walletDao.update(voucherData, true);
                        if (voucherData == null) {
                            throw new WalletException("Error updating voucher details.");
                        }
                        walletData.setPendingApproval(AppUtils.subtract(walletData.getPendingApproval(), voucherData.getExpenseAmount()));
                        walletData.setRejectedAmount(AppUtils.add(walletData.getRejectedAmount(), voucherData.getExpenseAmount()));
                        walletData.setLastUpdateTime(currentTime);
                        walletData = walletDao.update(walletData, true);
                        if (walletData != null) {
                            return true;
                        } else {
                            throw new WalletException("Error updating wallet linked to voucher.");
                        }
                    } else {
                        throw new WalletException("Voucher status is not valid for this transaction.");
                    }
                } else {
                    throw new WalletException("Wallet id not valid.");
                }
            } else {
                throw new WalletException("Voucher id not valid.");
            }
        } else {
            throw new WalletException("Please provide required request parameters.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean acknowledgePendingRejectedVoucher(Voucher voucher) throws WalletException {
        if (voucher != null && voucher.getId() != null && voucher.getWalletId() != null) {
            Date currentTime = AppUtils.getCurrentTimestamp();
            WalletData walletData = walletDao.find(WalletData.class, voucher.getWalletId());
            if (walletData != null) {
                VoucherData voucherData = walletDao.find(VoucherData.class, voucher.getId());
                if (voucherData != null) {
                    if (VoucherStatus.PENDING_REJECTION_AM.name().equalsIgnoreCase(voucherData.getCurrentStatus()) ||
                            VoucherStatus.PENDING_REJECTION_FINANCE.name().equalsIgnoreCase(voucherData.getCurrentStatus())) {
                        saveVoucherStatus(VoucherStatus.valueOf(voucherData.getCurrentStatus()), VoucherStatus.REJECTED,
                                voucher.getActionComment(), voucherData, voucher, currentTime);

                        voucherData.setCurrentStatus(VoucherStatus.REJECTED.name());
                        voucherData.setLastUpdatedTime(currentTime);
                        voucherData = walletDao.update(voucherData, true);

                        WalletTransactionData transactionData = saveWalletTransaction(walletData, WalletTransactionType.CREDIT,
                                WalletTransactionCode.ADDITION, WalletTransactionCodeType.REJECTED, voucher.getExpenseAmount(),
                                currentTime, voucherData.getIssuedBy(), WalletTransactionEntityType.VOUCHER, voucherData.getId(), voucher.getEntity());

                        if (transactionData == null) {
                            throw new WalletException("Error saving wallet transaction.");
                        }

                        walletData.setRejectedAmount(AppUtils.subtract(walletData.getRejectedAmount(), voucher.getExpenseAmount()));
                        walletData.setCurrentBalance(AppUtils.add(walletData.getCurrentBalance(), voucher.getExpenseAmount()));

                        walletData.setLastUpdateTime(currentTime);
                        walletData = walletDao.update(walletData, true);
                        if (walletData != null) {
                            return true;
                        }
                        return null;
                    } else {
                        throw new WalletException("Voucher not valid for acknowledgement.");
                    }
                } else {
                    throw new WalletException("Voucher id not valid.");
                }
            } else {
                throw new WalletException("Wallet id not valid.");
            }
        } else {
            throw new WalletException("Please provide required request parameters.");
        }

    }

    /*@Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String topupApprovedVouchers(WalletTransaction transaction) throws WalletException {
        String result = null;
        Date currentTime = AppUtils.getCurrentTimestamp();
        if (transaction.getForced() != Boolean.TRUE && AppUtils.getDayOfWeek(currentTime) != Calendar.THURSDAY) {
            throw new WalletException("You can top up your wallet on Thursday Only");
        }
        WalletData walletData = walletDao.find(WalletData.class, transaction.getWalletId());
        WalletTransactionCodeType codeType = WalletTransactionCodeType.APPROVED;
        if (walletData.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
            codeType = WalletTransactionCodeType.INITIAL_LOAD;
        }
        WalletTransactionData transactionData = saveWalletTransaction(walletData, WalletTransactionType.CREDIT,
                WalletTransactionCode.ADDITION, codeType, walletData.getPendingApproval(),
                currentTime, transaction.getCreatedBy().getId(), WalletTransactionEntityType.VOUCHER, -1, transaction.getEntity());

        if (transactionData != null) {
            saveTopupDenomination(transaction.getDenomList(), transactionData);
        }

        BigDecimal topupAmout = getTopupAmount(walletData);
        if (topupAmout.compareTo(walletData.getApprovedAmount()) > 0) {
            result = String.format(
                    "Wallet has been top up with additional amount %f as your wallet is upgraded by finance. ",
                    topupAmout);
        } else if (topupAmout.compareTo(walletData.getApprovedAmount()) < 0) {
            if (topupAmout.compareTo(BigDecimal.ZERO) > 0) {
                result = String.format(
                        "Wallet has been top up with partial amount %f as your wallet is degraded by finance. ",
                        topupAmout);
            }
            result = String.format("Wallet has been top up with amount %f as your wallet is degraded by finance. ",
                    topupAmout);
        } else if (walletData.getApprovedAmount().compareTo(topupAmout) == 0) {
            result = String.format("Wallet has been top up with amount %f. ", topupAmout);
        }

        walletData.setCurrentBalance(AppUtils.add(walletData.getCurrentBalance(), topupAmout));
        walletData.setApprovedAmount(BigDecimal.ZERO);

        walletDao.settleVoucher(transaction.getWalletId(), currentTime);

        walletData.setTotalAmount(getTotalAmount(walletData));
        walletData.setLastUpdateTime(currentTime);
        walletData = walletDao.update(walletData, true);
        if (walletData != null) {
            return result;
        }
        return result;
    }*/

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Wallet createWallet(Wallet wallet) throws WalletException {
        if (wallet != null) {
            WalletData walletData = new WalletData();
            Date currentTime = AppUtils.getCurrentTimestamp();
            walletData.setAccountType(wallet.getAccountType().name());
            walletData.setAccountNo(wallet.getAccountNo());
            String [] s = wallet.getAccountNo().split("_");
            int id = Integer.parseInt(s[s.length-1]);
            if(WalletAccountType.UNIT.name().equalsIgnoreCase(wallet.getAccountType().name())){
                walletData.setAccountHolderName(masterDataCache.getUnit(id).getName());
                walletData.setAccountHolderContact(masterDataCache.getUnit(id).getAddress().getContact1());
                walletData.setAccountHolderEmail(masterDataCache.getUnit(id).getUnitEmail());
            } else {
                walletData.setAccountHolderName(masterDataCache.getEmployeeBasicDetail(id).getName());
                walletData.setAccountHolderContact(masterDataCache.getEmployeeBasicDetail(id).getContactNumber());
                walletData.setAccountHolderEmail(masterDataCache.getEmployeeBasicDetail(id).getEmailId());
            }
            walletData.setWalletType(wallet.getWalletType().name());
            walletData.setOpeningAmount(wallet.getOpeningAmount());
            walletData.setIfscCode(wallet.getIfscCode());
            walletData.setCardNumber(wallet.getCardNumber());
            walletData.setBankName(wallet.getBankName());
            walletData.setCardHolderName(wallet.getCardHolderName());

            if (WalletAccountType.UNIT.name().equalsIgnoreCase(wallet.getAccountType().name())) {
                if (wallet.getOpeningAmount().compareTo(BigDecimal.valueOf(Long.parseLong(
                        masterDataCache.getCacheReferenceMetadata(CacheReferenceType.MAX_OPENING_AMOUNT_FOR_UNIT)))) < 0) {
                    walletData.setOpeningAmount(wallet.getOpeningAmount());
                } else {
                    throw new WalletException("Opening Amount Cannot Be Greater Than" +
                            masterDataCache.getCacheReferenceMetadata(CacheReferenceType.MAX_OPENING_AMOUNT_FOR_UNIT));
                }
            }
            else {
                if (WalletAccountType.EMPLOYEE.name().equalsIgnoreCase(wallet.getAccountType().name())
                        && wallet.getOpeningAmount().compareTo(BigDecimal.valueOf(
                        Long.parseLong(masterDataCache.getCacheReferenceMetadata(CacheReferenceType.MAX_OPENING_AMOUNT_FOR_EMPLOYEE)))) < 0) {
                    walletData.setOpeningAmount(wallet.getOpeningAmount());
                } else {
                    throw new WalletException("Opening Amount Cannot Be Greater Than" +
                            masterDataCache.getCacheReferenceMetadata(CacheReferenceType.MAX_OPENING_AMOUNT_FOR_EMPLOYEE));
                }
            }

            walletData.setTotalAmount(BigDecimal.ZERO);
            walletData.setCurrentBalance(BigDecimal.ZERO);
            walletData.setIssuedAmount(BigDecimal.ZERO);
            walletData.setPendingApproval(BigDecimal.ZERO);
            walletData.setApprovedAmount(BigDecimal.ZERO);
            walletData.setRejectedAmount(BigDecimal.ZERO);
            walletData.setSpentAmount(BigDecimal.ZERO);

            walletData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
            walletData.setStatus(WalletStatus.ACTIVE.name());
            walletData.setCanAllocateCostToCafes(AppUtils.setStatus(wallet.isCanAllocateCostToCafes()));
            walletData.setAssociatedId(id);
            walletData = walletDao.add(walletData, true);
            List<WalletBusinessCostCenterMapping> walletBusinessCostCenterMappings = new ArrayList<>();
            if(Objects.nonNull(wallet.getBusinessCostCentersList()) && !wallet.getBusinessCostCentersList().isEmpty()){
                WalletData finalWalletData = walletData;
                wallet.getBusinessCostCentersList().stream().forEach(businessCostCenters -> {
                    walletBusinessCostCenterMappings.add(createWalletToBusinessCostCenterMapping(businessCostCenters, finalWalletData,wallet));
                });
            }
            List<WalletApproverMapping> walletApproverMappings = new ArrayList<>();
            if(Objects.nonNull(wallet.getApproverList()) && !wallet.getApproverList().isEmpty()){
                WalletData walletData1 = walletData;
                wallet.getApproverList().stream().forEach(approvers->{
                    walletApproverMappings.add(createWalletApproverMapping(approvers,walletData1,wallet));
                        });
            }
            WalletTransactionData transactionData = saveWalletTransaction(walletData, WalletTransactionType.CREDIT, WalletTransactionCode.ADDITION,
                    WalletTransactionCodeType.WALLET_CREATE, walletData.getOpeningAmount(), currentTime,
                    wallet.getCreatedBy().getId(), WalletTransactionEntityType.VOUCHER, -1, IssuingEntity.FINANCE);

            if (transactionData != null && !walletBusinessCostCenterMappings.isEmpty() ) {
                return WalletDataConverter.convert(walletData);
            }
            return null;
        } else {
            throw new WalletException("Please send valid wallet details");
        }
    }


    private WalletBusinessCostCenterMapping createWalletToBusinessCostCenterMapping(BusinessCostCenters businessCostCenter, WalletData walletData, Wallet wallet) {
        WalletBusinessCostCenterMapping mapping = WalletBusinessCostCenterMapping.builder().bccCode(businessCostCenter.getCode()).bccId(businessCostCenter.getId())
                .bccName(businessCostCenter.getName()).bccType(businessCostCenter.getType()).walletId(walletData.getId())
                .mappingStatus(AppConstants.ACTIVE).addTime(AppUtils.getCurrentTimestamp()).lastUpdateTime(AppUtils.getCurrentTimestamp()).lastUpdatedBy(wallet.getLastUpdatedBy()).build();
        return walletDao.add(mapping,true);
    }

    private WalletApproverMapping createWalletApproverMapping(Integer approver,WalletData walletData,Wallet wallet){
        WalletApproverMapping walletApproverMapping = WalletApproverMapping.builder().walletId(walletData.getId()).approverId(approver).
                approverName(masterDataCache.getEmployeeBasicDetail(approver).getName()).mappingStatus(AppConstants.ACTIVE).addTime(AppUtils.getCurrentTimestamp())
                .updatedTime(AppUtils.getCurrentTimestamp()).updatedBy(wallet.getLastUpdatedBy()).build();
        return walletDao.add(walletApproverMapping,true);
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<WalletBusinessCostCenterMappingVO> getWalletBusinessCostCenterMappings(int walletId) {
        List<WalletBusinessCostCenterMapping> walletBusinessCostCenterMappings =walletDao.findBusinessCostCenterMappingsByWalletId(walletId);
        List<WalletBusinessCostCenterMappingVO> mappingVOList =new ArrayList<>();
        if(Objects.nonNull(walletBusinessCostCenterMappings) && !walletBusinessCostCenterMappings.isEmpty()){
            for(WalletBusinessCostCenterMapping walletBusinessCostCenterMapping :walletBusinessCostCenterMappings){
                mappingVOList.add(WalletDataConverter.convert(walletBusinessCostCenterMapping));
            }
        }
        return mappingVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM",readOnly = true,propagation = Propagation.REQUIRED)
    public List<WalletApproverMappingVO> getWalletApproverMappings(int walletId) {
     List<WalletApproverMapping> walletApproverMappings = walletDao.getWalletApproverMappings(walletId);
     List<WalletApproverMappingVO> mappingVOS = new ArrayList<>();
     if(Objects.nonNull(walletApproverMappings) && !walletApproverMappings.isEmpty()){
         for(WalletApproverMapping walletApproverMapping:walletApproverMappings){
             mappingVOS.add(WalletDataConverter.convert(walletApproverMapping));
         }
     }
     return mappingVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM",propagation = Propagation.REQUIRED)
    public boolean updateWalletApproverMappings(Integer walletMappingId, String status, String lastUpdatedBy) {
        try{
            WalletApproverMapping mapping =walletDao.find(WalletApproverMapping.class,walletMappingId);
            if(Objects.nonNull(mapping)){
                mapping.setMappingStatus(status);
                mapping.setUpdatedTime(AppUtils.getCurrentTimestamp());
                mapping.setUpdatedBy(lastUpdatedBy);
            }
            mapping =walletDao.update(mapping,true);
            if (mapping == null) {
                throw new WalletException("Error updating wallet data.");
            }
            return true;
        }catch(Exception e){
            LOG.error("Exception while updating status of wallet to bcc mapping for mappingId ::{}",walletMappingId,e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Wallet findWalletByAccountNo(String accountNo) throws WalletException {
        List<WalletData> wallets = walletDao.findWalletByAccountNo(accountNo);
        if (wallets.size()==1) {
            return WalletMapper.INSTANCE.toDomain(wallets.get(0));
        }

        return null;
//        List<Wallet> walletList = new ArrayList<>();
//        if(Objects.nonNull(wallets) && !wallets.isEmpty()){
//            wallets.forEach(wallet -> walletList.add(WalletDataConverter.convert(wallet)));
//        }
//        return walletList;
    }
    @Override
    public List<String> getReportingManagersForAccountNo(String accountNo) throws WalletException{
        String employeeCode = accountNo.split("_")[3];
        Integer employeeId = Integer.valueOf(employeeCode);
        List<Integer> reportingManagers = new ArrayList<>();
        List<String> reportingManager = new ArrayList<>();
        getReportingManagers(employeeId,reportingManagers);
        if(reportingManagers.size()==0){
            throw new WalletException("No reporting Manager Mapped");
        }
        for(Integer managers:reportingManagers){
            String stringManager = masterDataCache.getEmployeeBasicDetail(managers).getName();
            String manager = managers + "_" + stringManager;
            reportingManager.add(manager);
        }
        return reportingManager;
    }




    private void getReportingManagers(Integer employeeId, List<Integer> reportingManagers) {
        Integer currentEmployeeId = masterDataCache.getEmployeeBasicDetail(employeeId).getReportingManagerId();
        if (currentEmployeeId != null && !currentEmployeeId.equals(employeeId)) {
            reportingManagers.add(currentEmployeeId);
            getReportingManagers(currentEmployeeId, reportingManagers);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class,value = "AuditDataSourceTM",readOnly = false,propagation = Propagation.REQUIRED)
    public boolean updateAllWalletBusinessCostCenterMappings(List<WalletBusinessCostCenterMappingVO> walletBusinessCostCenterMappingVOList, String lastUpdatedBy, Integer walletId) {
        try {
            if (Objects.nonNull(walletBusinessCostCenterMappingVOList) && !walletBusinessCostCenterMappingVOList.isEmpty()) {
                walletBusinessCostCenterMappingVOList.forEach(walletBusinessCostCenterMapping -> walletDao.updateWalletBusinessCostCenterMapping(walletBusinessCostCenterMapping, lastUpdatedBy,walletId));
            }
        } catch (Exception e) {
            LOG.error("Error while updating wallet business cost center mapping ::::::::", e);
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class,value = "AuditDataSourceTM",readOnly = false,propagation = Propagation.REQUIRED)
    public boolean updateAllWalletApproverMappings(List<WalletApproverMappingVO> walletApproverMappingVOList, String lastUpdatedBy, Integer walletId) {
        try {
            if(Objects.nonNull(walletApproverMappingVOList) && !walletApproverMappingVOList.isEmpty()) {
                walletApproverMappingVOList.forEach(walletApproverMapping -> walletDao.updateAllWalletApproverMappings(walletApproverMapping,lastUpdatedBy,walletId));
            }
        }catch (Exception e){
            LOG.error("Error while updating wallet approver mapping",e);
            return false;
        }
        return true;
    }


    /*@Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Wallet updateWallet(Wallet wallet) {
        WalletData walletData = walletDao.find(WalletData.class, wallet.getId());
        Date currentTime = AppUtils.getCurrentTimestamp();
        walletData.setAccountHolderName(wallet.getAccountHolderName());
        walletData.setAccountHolderContact(wallet.getAccountHolderContact());
        walletData.setAccountHolderEmail(wallet.getAccountHolderEmail());

        if (walletData.getOpeningAmount().compareTo(wallet.getOpeningAmount()) > 0) {
            saveWalletTransaction(walletData, WalletTransactionType.DEBIT, WalletTransactionCode.REDEMPTION,
                    WalletTransactionCodeType.WALLET_DOWNGRADE,
                    AppUtils.subtract(walletData.getOpeningAmount(), wallet.getOpeningAmount()), currentTime,
                    wallet.getCreatedBy().getId(), WalletTransactionEntityType.VOUCHER, -1, IssuingEntity.FINANCE);
        } else if (walletData.getOpeningAmount().compareTo(wallet.getOpeningAmount()) < 0) {
            saveWalletTransaction(walletData, WalletTransactionType.CREDIT, WalletTransactionCode.ADDITION,
                    WalletTransactionCodeType.WALLET_UPGRADE,
                    AppUtils.subtract(wallet.getOpeningAmount(), walletData.getOpeningAmount()), currentTime,
                    wallet.getCreatedBy().getId(), WalletTransactionEntityType.VOUCHER, -1, IssuingEntity.FINANCE);
        }

        walletData.setOpeningAmount(wallet.getOpeningAmount());
        walletData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
        walletData = walletDao.update(walletData, true);

        if (walletData != null) {
            return WalletDataConverter.convert(walletData);
        }
        return null;
    }*/

    @Override
    public List<DenominationDetail> getCashDenomination() {
        return masterDataCache.getPaymentMode(1).getDenominations();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Voucher> getClaimPendingVouchers(String accountNo) {
        List<VoucherData> voucherDataList = walletDao.getClaimPendingVouchers(accountNo);
        List<Voucher> vouchers = new ArrayList<Voucher>();
        voucherDataList.forEach(data -> {
            WalletData walletData = walletDao.find(WalletData.class, data.getWalletId());
            vouchers.add(WalletDataConverter.convert(data, walletData, false, false, null, masterDataCache, null));
        });
        return vouchers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Voucher> getVoucherListByIdsAndAccountNo(List<Integer> voucherIds, String accountNo) {
        List<VoucherData> voucherDataList = walletDao.getVoucherListByIdsAndAccountNo(voucherIds, accountNo);
        List<Voucher> vouchers = new ArrayList<Voucher>();
        voucherDataList.forEach(data -> {
            WalletData walletData = walletDao.find(WalletData.class, data.getWalletId());
            vouchers.add(WalletDataConverter.convert(data, walletData, false, false, null, masterDataCache, null));
        });
        return vouchers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Voucher> getVoucherListByClaimId(Integer claimId) {
        List<VoucherData> voucherDataList = walletDao.getVoucherListByClaimId(claimId);
        List<Voucher> vouchers = new ArrayList<Voucher>();
        voucherDataList.forEach(data -> {
            WalletData walletData = walletDao.find(WalletData.class, data.getWalletId());
            vouchers.add(WalletDataConverter.convert(data, walletData, true, false, null, masterDataCache, null));
        });
        return vouchers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Integer> getVoucherIdsByClaimId(Integer claimId) {
        return walletDao.getVoucherIdsByClaimId(claimId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VoucherData> getVoucherDataListByClaimId(Integer claimId) {
        return walletDao.getVoucherListByClaimId(claimId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VoucherData> getVoucherDataListByIdsAndAccountNo(List<Integer> voucherIds, String accountNo) {
        return walletDao.getVoucherListByIdsAndAccountNo(voucherIds, accountNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateWalletFromClaimAcknowledgement(ClaimDetailData claimDetailData, Integer updatedBy) throws WalletException {
        Date currentTime = AppUtils.getCurrentTimestamp();
        /*if (transaction.getForced() != Boolean.TRUE && AppUtils.getDayOfWeek(currentTime) != Calendar.THURSDAY) {
            throw new WalletException("You can top up your wallet on Thursday Only");
        }*/
        WalletData walletData = walletDao.find(WalletData.class, claimDetailData.getWalletId());
        WalletTransactionCodeType codeType = WalletTransactionCodeType.APPROVED;
        WalletTransactionData transactionData = saveWalletTransaction(walletData, WalletTransactionType.CREDIT,
                WalletTransactionCode.ADDITION, codeType, claimDetailData.getClaimApprovedAmount(),
                currentTime, updatedBy, WalletTransactionEntityType.CLAIM, claimDetailData.getClaimId(), IssuingEntity.FINANCE);
        if (transactionData != null) {
            if (claimDetailData.getClaimApprovedAmount().compareTo(walletData.getApprovedAmount()) > 0) {
                throw new WalletException("Claim amount shall not be greater than approved amount.");
            } else {
                walletData.setCurrentBalance(AppUtils.add(walletData.getCurrentBalance(), claimDetailData.getClaimApprovedAmount()));
                walletData.setApprovedAmount(AppUtils.subtract(walletData.getApprovedAmount(), claimDetailData.getClaimApprovedAmount()));
                /*for (Voucher voucher : getVoucherListByClaimId(claimDetailData.getClaimId())) {
                    walletDao.settleVoucher(voucher.getId(), currentTime);
                }*/
                List<Integer> voucherIds = getVoucherIdsByClaimId(claimDetailData.getClaimId());
                walletDao.settleVouchers(voucherIds, currentTime, masterDataCache.getExpenseDataById(envProperties.getRefundExpenseMetadataId()).getId());
                walletData.setTotalAmount(getTotalAmount(walletData));
                walletData.setLastUpdateTime(currentTime);
                walletData = walletDao.update(walletData, true);
                if (walletData == null) {
                    throw new WalletException("Error updating wallet data.");
                }
            }
        } else {
            throw new WalletException("Error saving wallet transaction data.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void upgradeWalletAmount(ClaimDetailData claimDetailData, Integer updatedBy) throws WalletException {
        Date currentTime = AppUtils.getCurrentTimestamp();
        WalletData walletData = walletDao.find(WalletData.class, claimDetailData.getWalletId());
        WalletTransactionCodeType codeType = WalletTransactionCodeType.WALLET_UPGRADE;
        WalletTransactionData transactionData = saveWalletTransaction(walletData, WalletTransactionType.CREDIT,
                WalletTransactionCode.ADDITION, codeType, claimDetailData.getClaimApprovedAmount(),
                currentTime, updatedBy, WalletTransactionEntityType.CLAIM, claimDetailData.getClaimId(), IssuingEntity.FINANCE);
        if (transactionData != null) {
            walletData.setOpeningAmount(AppUtils.add(walletData.getOpeningAmount(), claimDetailData.getClaimApprovedAmount()));
            walletData.setCurrentBalance(AppUtils.add(walletData.getCurrentBalance(), claimDetailData.getClaimApprovedAmount()));
            walletData.setTotalAmount(getTotalAmount(walletData));
            walletData.setLastUpdateTime(currentTime);
            walletData = walletDao.update(walletData, true);
            if (walletData == null) {
                throw new WalletException("Error updating wallet data.");
            }
        } else {
            throw new WalletException("Error saving wallet transaction data.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void downgradeWalletAmount(ClaimDetailData claimDetailData, Integer updatedBy) throws WalletException {
        Date currentTime = AppUtils.getCurrentTimestamp();
        WalletData walletData = walletDao.find(WalletData.class, claimDetailData.getWalletId());
        if (claimDetailData.getClaimApprovedAmount().compareTo(walletData.getCurrentBalance()) > 0) {
            throw new WalletException("Wallet cannot be downgraded due to insufficient balance. Please settle all vouchers and claims.");
        }
        WalletTransactionCodeType codeType = WalletTransactionCodeType.WALLET_DOWNGRADE;
        WalletTransactionData transactionData = saveWalletTransaction(walletData, WalletTransactionType.CREDIT,
                WalletTransactionCode.REDEMPTION, codeType, claimDetailData.getClaimApprovedAmount(),
                currentTime, updatedBy, WalletTransactionEntityType.CLAIM, claimDetailData.getClaimId(), IssuingEntity.FINANCE);
        if (transactionData != null) {
            walletData.setOpeningAmount(AppUtils.subtract(walletData.getOpeningAmount(), claimDetailData.getClaimApprovedAmount()));
            walletData.setCurrentBalance(AppUtils.subtract(walletData.getCurrentBalance(), claimDetailData.getClaimApprovedAmount()));
            walletData.setTotalAmount(getTotalAmount(walletData));
            walletData.setLastUpdateTime(currentTime);
            walletData = walletDao.update(walletData, true);
            if (walletData == null) {
                throw new WalletException("Error updating wallet data.");
            }
        } else {
            throw new WalletException("Error saving wallet transaction data.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Wallet> getUserWalletData(String accountType, Integer associatedId) {
        List<WalletData> walletData = walletDao.getUserWalletData(accountType,associatedId);
        return WalletMapper.INSTANCE.toDomainList(walletData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VoucherCostCenterAllocation> getVoucherCostCenterAllocation(Integer voucherId) {
        List<VoucherCostCenterAllocationEntity> voucherCostCenterAllocationEntities = walletDao.getVoucherCostCenterAllocation(voucherId);
        return VoucherCostCenterAllocationMapper.INSTANCE.toDomainList(voucherCostCenterAllocationEntities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateWalletBusinessCostCenterMapping(Integer walletBccMappingId, String status, String lastUpdatedBy) {
        try{
            WalletBusinessCostCenterMapping mapping =walletDao.find(WalletBusinessCostCenterMapping.class,walletBccMappingId);
            if(Objects.nonNull(mapping)){
                mapping.setMappingStatus(status);
                mapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                mapping.setLastUpdatedBy(lastUpdatedBy);
            }
            mapping =walletDao.update(mapping,true);
            if (mapping == null) {
                throw new WalletException("Error updating wallet data.");
            }
            return true;
        }catch(Exception e){
            LOG.error("Exception while updating status of wallet to bcc mapping for mappingId ::{}",walletBccMappingId,e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Long getPendingRejectedVoucherCountWithAssociatedId(Integer associatedId) {
        List<WalletData> walletData = walletDao.getWalletDataWithAssociatedId(associatedId);
        List<Integer> walletId = walletData.stream().map(WalletData::getId).collect(Collectors.toList());
        return walletDao.getPendingRejectedVoucherCountWithAssociatedId(walletId, getPendingRejectedStatusList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Wallet> getWalletAccounts(WalletAccountType walletAccountType, String employeeCode, boolean byPass) throws WalletException {
        List<WalletData> walletData = walletDao.getWalletAccounts(walletAccountType,employeeCode,byPass);
        List<Wallet> wallets = new ArrayList<>();
        if (WalletAccountType.UNIT.equals(walletAccountType) && !byPass){
            walletData.forEach(wallet -> {
                if ( (Objects.nonNull(masterDataCache.getUnit(wallet.getAssociatedId()).getUnitManager())
                && masterDataCache.getUnit(wallet.getAssociatedId()).getUnitManager().getId() == Integer.parseInt(employeeCode))
                    || (Objects.nonNull(masterDataCache.getUnit(wallet.getAssociatedId()).getCafeManager()) &&
                        masterDataCache.getUnit(wallet.getAssociatedId()).getCafeManager().getId() == Integer.parseInt(employeeCode))) {
                    wallets.add(WalletMapper.INSTANCE.toDomain(wallet));
                }
            });
            return wallets;
        }
        return WalletMapper.INSTANCE.toDomainList(walletData);
    }



    private BigDecimal getTotalAmount(WalletData walletData) {
        BigDecimal spentAmount = AppUtils.add(walletData.getCurrentBalance(), walletData.getIssuedAmount());
        spentAmount = AppUtils.add(spentAmount, walletData.getRejectedAmount());
        spentAmount = AppUtils.add(spentAmount, walletData.getPendingApproval());
        spentAmount = AppUtils.add(spentAmount, walletData.getApprovedAmount());
        return spentAmount;
    }

    private BigDecimal getTopupAmount(WalletData walletData) {
        BigDecimal totalAmout = getTotalAmount(walletData);
        if (totalAmout.compareTo(BigDecimal.ZERO) == 0) {
            return walletData.getOpeningAmount();
        }
        BigDecimal topupAmout = BigDecimal.ZERO;
        BigDecimal amount = AppUtils.subtract(walletData.getOpeningAmount(), totalAmout);
        if (walletData.getOpeningAmount().compareTo(totalAmout) > 0) {
            topupAmout = AppUtils.add(walletData.getApprovedAmount(), amount);
        } else if (walletData.getOpeningAmount().compareTo(totalAmout) < 0) {
            topupAmout = AppUtils.add(walletData.getApprovedAmount(), amount);
        } else if (walletData.getOpeningAmount().compareTo(totalAmout) == 0) {
            topupAmout = walletData.getApprovedAmount();
        }
        return topupAmout;
    }

    private String getUniqueGeneratedVoucherId() {
        String generatedVoucherId = AppUtils.generateRandomOrderId();
        List<VoucherData> list = walletDao.getVouchers(generatedVoucherId);
        if (list.size() > 0) {
            getUniqueGeneratedVoucherId();
        }
        return generatedVoucherId;
    }

    private WalletTransactionData saveWalletTransaction(WalletData data, WalletTransactionType transType,
                                                        WalletTransactionCode code, WalletTransactionCodeType codeType, BigDecimal amount, Date date,
                                                        Integer issuedBy, WalletTransactionEntityType entityType, Integer entityId, IssuingEntity entity) {
        WalletTransactionData transData = new WalletTransactionData();
        transData.setWalletData(data);
        transData.setEntityType(entityType.name());
        transData.setEntityId(entityId);
        transData.setTransactionType(transType.name());
        transData.setTransactionAmount(amount);
        transData.setTransactionCode(code.name());
        transData.setTransactionCodeType(codeType.name());
        transData.setTransactionStatus(TransitionType.SUCCESS.name());
        transData.setTransactionTime(date);
        transData.setCreatedBy(issuedBy);
        transData.setEntity(entity.name());
        return walletDao.add(transData, true);
    }

    private void saveVoucherStatus(VoucherStatus from, VoucherStatus to, String comment, VoucherData voucherData,
                                   Voucher voucher, Date currentTime) {
        VoucherStatusData statusData = new VoucherStatusData();
        statusData.setVoucherData(voucherData);
        statusData.setFromStatus(from.name());
        statusData.setToStatus(to.name());
        statusData.setGeneratedBy(voucher.getIssuedBy().getId());
        if (comment != null) {
            statusData.setActionComment(comment);
        }
        statusData.setActionTime(currentTime);
        statusData.setTransitionStatus(TransitionType.SUCCESS.name());
        walletDao.add(statusData, true);
    }

    private void saveVoucherStatus(VoucherStatus from, VoucherStatus to, String comment, VoucherData voucherData,
                                   Integer updatedBy, Date currentTime) {
        VoucherStatusData statusData = new VoucherStatusData();
        statusData.setVoucherData(voucherData);
        statusData.setFromStatus(from.name());
        statusData.setToStatus(to.name());
        statusData.setGeneratedBy(updatedBy);
        if (comment != null) {
            statusData.setActionComment(comment);
        }
        statusData.setActionTime(currentTime);
        statusData.setTransitionStatus(TransitionType.SUCCESS.name());
        walletDao.add(statusData, true);
    }
}
