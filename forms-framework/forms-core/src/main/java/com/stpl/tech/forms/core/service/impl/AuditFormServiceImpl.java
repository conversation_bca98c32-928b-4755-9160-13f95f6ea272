package com.stpl.tech.forms.core.service.impl;

import com.stpl.tech.forms.core.exception.AuditException;
import com.stpl.tech.forms.core.service.AuditFormService;
import com.stpl.tech.forms.data.AuditDataConverter;
import com.stpl.tech.forms.data.dao.AuditFormDao;
import com.stpl.tech.forms.data.model.AuditFormData;
import com.stpl.tech.forms.domain.model.AuditForm;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class AuditFormServiceImpl implements AuditFormService {

    @Autowired
    private AuditFormDao auditFormDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<AuditForm> getAllAuditForms() {
        List<AuditForm> auditForms = new ArrayList<>();
        List<AuditFormData> auditFormDataList = auditFormDao.findAll(AuditFormData.class);
        if (auditFormDataList != null && auditFormDataList.size() > 0) {
            auditFormDataList.forEach(auditFormData -> {
                String createdBy = masterDataCache.getEmployee(auditFormData.getCreatedBy());
                String updatedBy = masterDataCache.getEmployee(auditFormData.getLastUpdatedBy());
                auditForms.add(AuditDataConverter.convert(auditFormData, createdBy, updatedBy, false));
            });
        }
        return auditForms;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<AuditForm> getActiveAuditForms() {
        List<AuditForm> auditForms = new ArrayList<>();
        List<AuditFormData> auditFormDataList = auditFormDao.findAll(AuditFormData.class);
        if (auditFormDataList != null && auditFormDataList.size() > 0) {
            auditFormDataList.forEach(auditFormData -> {
                if(auditFormData.getStatus().equals("ACTIVE")){
                    String createdBy = masterDataCache.getEmployee(auditFormData.getCreatedBy());
                    String updatedBy = masterDataCache.getEmployee(auditFormData.getLastUpdatedBy());
                    auditForms.add(AuditDataConverter.convert(auditFormData, createdBy, updatedBy, false));
                }
            });
        }
        return auditForms;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public AuditForm getAuditForm(Integer formId) throws AuditException {
        if (formId != null) {
            AuditFormData auditFormData = auditFormDao.find(AuditFormData.class, formId);
            if (auditFormData != null) {
                String createdBy = masterDataCache.getEmployee(auditFormData.getCreatedBy());
                String updatedBy = masterDataCache.getEmployee(auditFormData.getLastUpdatedBy());
                return AuditDataConverter.convert(auditFormData, createdBy, updatedBy, true);
            } else {
                throw new AuditException("Form id is not valid!");
            }
        } else {
            throw new AuditException("Please provide form id!");
        }
    }
}
