package com.stpl.tech.forms.data.dao.impl;

import com.stpl.tech.forms.data.dao.AuditDao;
import com.stpl.tech.forms.data.model.AuditDetailData;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.Date;
import java.util.List;

@Repository
public class AuditDaoImpl extends AbstractDaoImpl implements AuditDao {

    @Override
    public List<AuditDetailData> searchAudit(Integer unitId, Date start, Date end, Integer auditFormId) {
        StringBuilder queryString = new StringBuilder("FROM AuditDetailData a WHERE a.auditSubmitDate >= :start AND a.auditSubmitDate <= :end");
        if(unitId != null){
            queryString.append(" AND a.auditUnitId = :unitId");
        }
        if(auditFormId != null){
            queryString.append(" AND a.auditFormData.id = :auditFormId");
        }
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("start", start)
                .setParameter("end", end);
        if(unitId != null){
            query.setParameter("unitId", unitId);
        }
        if(auditFormId != null){
            query.setParameter("auditFormId", auditFormId);
        }
        return query.getResultList();
    }

    @Override
    public List<AuditDetailData> getPreviousAudits(Integer auditId, Integer auditFormId, Integer unitId, Integer limit) {
        Query query = manager.createQuery("FROM AuditDetailData a WHERE a.auditFormData.id = :auditFormId AND a.auditUnitId = :unitId AND a.id <= :auditId ORDER BY a.id DESC");
        query.setParameter("auditId", auditId)
                .setParameter("auditFormId", auditFormId)
                .setParameter("unitId", unitId);
        query.setMaxResults(limit);
        return query.getResultList();
    }

}
