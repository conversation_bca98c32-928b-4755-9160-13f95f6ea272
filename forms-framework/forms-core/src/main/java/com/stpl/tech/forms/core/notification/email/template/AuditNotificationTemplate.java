package com.stpl.tech.forms.core.notification.email.template;

import com.stpl.tech.forms.domain.model.Audit;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AuditNotificationTemplate extends AbstractVelocityTemplate {

    private String basePath;
    private Audit audit;
    private List<String> emails;

    public AuditNotificationTemplate() {
    }

    public AuditNotificationTemplate(String basePath, Audit audit, List<String> emails) {
        this.basePath = basePath;
        this.audit = audit;
        this.emails = emails;
    }

    @Override
    public String getTemplatePath() {
        return "templates/AuditNotificationTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/auditReportEmail/" + audit.getId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("audit", audit);
        return stringObjectMap;
    }

    public Audit getAudit() {
        return audit;
    }

    public List<String> getEmails() {
        return emails;
    }
}
