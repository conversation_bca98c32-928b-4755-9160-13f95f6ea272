package com.stpl.tech.forms.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "AUDIT_DETAIL_VALUES")
public class AuditDetailValuesData {

    private Integer id;
    private AuditDetailData auditDetailData;
    private AuditFormValuesData auditFormValuesData;
    private Integer employeeId;
    private String employeeName;
    private String employeeDesignation;
    private Integer unitId;
    private String unitName;
    private Integer productId;
    private String productName;
    private String option1;
    private BigDecimal option1Score;
    private String option2;
    private BigDecimal option2Score;
    private String option3;
    private BigDecimal option3Score;
    private String option4;
    private BigDecimal option4Score;
    private String option5;
    private BigDecimal option5Score;
    private String option6;
    private BigDecimal option6Score;
    private String option7;
    private BigDecimal option7Score;
    private String textArea;
    private String yesNo;
    private Date dateValue;
    private Date timeValue;
    private Integer numberValue;
    private String textValue;
    private String answerComment;
    private Integer attachedDocId;
    private BigDecimal maxScore;
    private BigDecimal acquiredScore;
    private String questionOpted = "Y";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AUDIT_DETAIL_ID", nullable = false)
    public AuditDetailData getAuditDetailData() {
        return auditDetailData;
    }

    public void setAuditDetailData(AuditDetailData auditDetailData) {
        this.auditDetailData = auditDetailData;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "FORM_VALUE_ID", nullable = false)
    public AuditFormValuesData getAuditFormValuesData() {
        return auditFormValuesData;
    }

    public void setAuditFormValuesData(AuditFormValuesData auditFormValuesData) {
        this.auditFormValuesData = auditFormValuesData;
    }

    @Column(name = "EMPLOYEE_ID", nullable = true)
    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    @Column(name = "EMPLOYEE_NAME", nullable = true, length = 100)
    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    @Column(name = "EMPLOYEE_DESIGNATION", nullable = true, length = 100)
    public String getEmployeeDesignation() {
        return employeeDesignation;
    }

    public void setEmployeeDesignation(String employeeDesignation) {
        this.employeeDesignation = employeeDesignation;
    }

    @Column(name = "UNIT_ID", nullable = true)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_NAME", nullable = true, length = 100)
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "PRODUCT_ID", nullable = true)
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_NAME", nullable = true, length = 100)
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "OPTION_1", nullable = true, length = 250)
    public String getOption1() {
        return option1;
    }

    public void setOption1(String option1) {
        this.option1 = option1;
    }

    @Column(name = "OPTION_1_MARKS", nullable = true)
    public BigDecimal getOption1Score() {
        return option1Score;
    }

    public void setOption1Score(BigDecimal option1Score) {
        this.option1Score = option1Score;
    }

    @Column(name = "OPTION_2", nullable = true, length = 250)
    public String getOption2() {
        return option2;
    }

    public void setOption2(String option2) {
        this.option2 = option2;
    }

    @Column(name = "OPTION_2_MARKS", nullable = true)
    public BigDecimal getOption2Score() {
        return option2Score;
    }

    public void setOption2Score(BigDecimal option2Score) {
        this.option2Score = option2Score;
    }

    @Column(name = "OPTION_3", nullable = true, length = 250)
    public String getOption3() {
        return option3;
    }

    public void setOption3(String option3) {
        this.option3 = option3;
    }

    @Column(name = "OPTION_3_MARKS", nullable = true)
    public BigDecimal getOption3Score() {
        return option3Score;
    }

    public void setOption3Score(BigDecimal option3Score) {
        this.option3Score = option3Score;
    }

    @Column(name = "OPTION_4", nullable = true, length = 250)
    public String getOption4() {
        return option4;
    }

    public void setOption4(String option4) {
        this.option4 = option4;
    }

    @Column(name = "OPTION_4_MARKS", nullable = true)
    public BigDecimal getOption4Score() {
        return option4Score;
    }

    public void setOption4Score(BigDecimal option4Score) {
        this.option4Score = option4Score;
    }

    @Column(name = "OPTION_5", nullable = true, length = 250)
    public String getOption5() {
        return option5;
    }

    public void setOption5(String option5) {
        this.option5 = option5;
    }

    @Column(name = "OPTION_5_MARKS", nullable = true)
    public BigDecimal getOption5Score() {
        return option5Score;
    }

    public void setOption5Score(BigDecimal option5Score) {
        this.option5Score = option5Score;
    }

    @Column(name = "OPTION_6", nullable = true, length = 250)
    public String getOption6() {
        return option6;
    }

    public void setOption6(String option6) {
        this.option6 = option6;
    }

    @Column(name = "OPTION_6_MARKS", nullable = true)
    public BigDecimal getOption6Score() {
        return option6Score;
    }

    public void setOption6Score(BigDecimal option6Score) {
        this.option6Score = option6Score;
    }

    @Column(name = "OPTION_7", nullable = true, length = 250)
    public String getOption7() {
        return option7;
    }

    public void setOption7(String option7) {
        this.option7 = option7;
    }

    @Column(name = "OPTION_7_MARKS", nullable = true)
    public BigDecimal getOption7Score() {
        return option7Score;
    }

    public void setOption7Score(BigDecimal option7Score) {
        this.option7Score = option7Score;
    }

    @Column(name = "TEXTAREA", nullable = true, length = 1000)
    public String getTextArea() {
        return textArea;
    }

    public void setTextArea(String textArea) {
        this.textArea = textArea;
    }

    @Column(name = "YES_NO", nullable = true, length = 1)
    public String getYesNo() {
        return yesNo;
    }

    public void setYesNo(String yesNo) {
        this.yesNo = yesNo;
    }

    @Column(name = "DATE_VALUE", nullable = true)
    public Date getDateValue() {
        return dateValue;
    }

    public void setDateValue(Date dateValue) {
        this.dateValue = dateValue;
    }

    @Column(name = "TIME_VALUE", nullable = true)
    public Date getTimeValue() {
        return timeValue;
    }

    public void setTimeValue(Date timeValue) {
        this.timeValue = timeValue;
    }

    @Column(name = "NUMBER_VALUE", nullable = true)
    public Integer getNumberValue() {
        return numberValue;
    }

    public void setNumberValue(Integer numberValue) {
        this.numberValue = numberValue;
    }

    @Column(name = "TEXT_VALUE", nullable = true, length = 500)
    public String getTextValue() {
        return textValue;
    }

    public void setTextValue(String textValue) {
        this.textValue = textValue;
    }

    @Column(name = "ANSWER_COMMENT", nullable = true, length = 500)
    public String getAnswerComment() {
        return answerComment;
    }

    public void setAnswerComment(String answerComment) {
        this.answerComment = answerComment;
    }

    @Column(name = "ATTACHED_DOC_ID", nullable = true)
    public Integer getAttachedDocId() {
        return attachedDocId;
    }

    public void setAttachedDocId(Integer attachedDocId) {
        this.attachedDocId = attachedDocId;
    }

    @Column(name = "MAX_SCORE", nullable = true)
    public BigDecimal getMaxScore() {
        return maxScore;
    }

    public void setMaxScore(BigDecimal maxScore) {
        this.maxScore = maxScore;
    }

    @Column(name = "ACQUIRED_SCORE", nullable = true)
    public BigDecimal getAcquiredScore() {
        return acquiredScore;
    }

    public void setAcquiredScore(BigDecimal acquiredScore) {
        this.acquiredScore = acquiredScore;
    }

    @Column(name = "QUESTION_OPTED", nullable = false, length = 1)
    public String getQuestionOpted() {
        return questionOpted;
    }

    public void setQuestionOpted(String questionOpted) {
        this.questionOpted = questionOpted;
    }
}
