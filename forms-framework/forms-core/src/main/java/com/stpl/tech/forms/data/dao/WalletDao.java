package com.stpl.tech.forms.data.dao;

import com.stpl.tech.expense.domain.model.WalletRequest;
import com.stpl.tech.forms.core.exception.WalletException;
import com.stpl.tech.forms.data.model.VoucherRejectionData;
import com.stpl.tech.forms.data.model.WalletApproverMapping;
import com.stpl.tech.forms.data.model.WalletBusinessCostCenterMapping;
import com.stpl.tech.forms.data.model.WalletData;
import com.stpl.tech.forms.domain.model.WalletAccountType;
import com.stpl.tech.forms.domain.model.WalletApproverMappingVO;
import com.stpl.tech.forms.domain.model.WalletBusinessCostCenterMappingVO;
import com.stpl.tech.kettle.data.expense.model.VoucherCostCenterAllocationEntity;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.model.OrderRefundDetail;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface WalletDao extends AbstractDao {

	public WalletData getWalletDetails(String accountType, String walletType, String accountNo);

    List<WalletData> getWallets(WalletRequest walletRequest);

    public List<VoucherData> getVouchers(String generatedVoucherId);

    public VoucherData getVouchersByGeneratedId(String generatedVoucherId);

	public List<VoucherData> getVoucherList(List<String> status, Date startDate, Date endDate, List<String> accountNo, Boolean isReimbursed);

    List<VoucherData> getVouchersByFinancePendingDate(List<String> status, Date startDate, Date endDate, List<String> accountNo,
                                                      Boolean isReimbursed);

    public void settleVoucher(Integer walletId, Date lastUpdatedTime, int expenseId) throws WalletException;

    void settleVouchers(List<Integer> voucherIds, Date lastUpdatedTime, int expenseId) throws WalletException;

    List<VoucherData> getClaimPendingVouchers(String accountNo);

    List<VoucherData> getVoucherListByIdsAndAccountNo(List<Integer> voucherIds, String accountNo);

    List<VoucherData> getVoucherListByClaimId(Integer claimId);

    List<Integer> getVoucherIdsByClaimId(Integer claimId);

    public Long getPendingRejectedVoucherCount(Integer walletId, List<String> status);

	public Long getVoucherCount(List<Integer> walletId, List<String> status , Date date);

    List<WalletData> getWalletDataWithAssociatedId(Integer associatedId);

    List<VoucherData> getPendingVouchers(List<Integer> walletIds, List<String> status);

    List<VoucherRejectionData> getVoucherRejections(Integer voucherId);

    List<WalletBusinessCostCenterMapping> findBusinessCostCenterMappingsByWalletId(int walletId);

    List<WalletData> findWalletByAccountNo(String accountNo);

    void updateWalletBusinessCostCenterMapping(WalletBusinessCostCenterMappingVO walletBusinessCostCenterMapping, String lastUpdatedBy, Integer walletId);

    List<WalletData> getUserWalletData(String accountType, Integer associatedId);

    WalletData getWalletDataByType(String accountType, Integer associatedId);

    List<VoucherCostCenterAllocationEntity> getVoucherCostCenterAllocation(Integer voucherId);

    Long getPendingRejectedVoucherCountWithAssociatedId(List<Integer> associatedId, List<String> pendingRejectedStatusList);

    List<WalletData> getWalletAccounts(WalletAccountType walletAccountType, String employeeCode, boolean byPass) throws WalletException;

    List<WalletApproverMapping> getWalletApproverMappings(int walletId);

    void updateAllWalletApproverMappings(WalletApproverMappingVO walletApproverMapping, String lastUpdatedBy, Integer walletId);

    List<VoucherData> findVoucherByGrNumber(Integer grNumber);

    public OrderRefundDetail getOrderRefundByOrderId(int orderId);

    OrderRefundDetail getOrderRefundByVoucherId(Integer voucherId);

    List<WalletData> findAllWalletsInStatus(String status, String accountType);

    Map<Integer, List<VoucherData>> findAllPendingVouchers(List<Integer> walletIds, String status, Integer pastDaysFrom, Integer pastDaysToInteger);
}
