package com.stpl.tech.forms.data;

import com.stpl.tech.expense.domain.model.Claim;
import com.stpl.tech.expense.domain.model.ClaimLog;
import com.stpl.tech.expense.domain.model.ClaimStatus;
import com.stpl.tech.expense.domain.model.ClaimType;
import com.stpl.tech.expense.domain.model.OrderRefundVoucher;
import com.stpl.tech.expense.domain.model.RejectionEntity;
import com.stpl.tech.expense.domain.model.VoucherRejection;
import com.stpl.tech.expense.domain.model.WalletTransactionEntityType;
import com.stpl.tech.forms.data.model.ClaimDetailData;
import com.stpl.tech.forms.data.model.ClaimLogDetailData;
import com.stpl.tech.forms.data.model.VoucherRejectionData;
import com.stpl.tech.forms.data.model.WalletApproverMapping;
import com.stpl.tech.forms.data.model.WalletBusinessCostCenterMapping;
import com.stpl.tech.forms.data.model.WalletData;
import com.stpl.tech.forms.data.model.WalletTransactionData;
import com.stpl.tech.forms.domain.model.FileStorageType;
import com.stpl.tech.forms.domain.model.IdCodeName;
import com.stpl.tech.forms.domain.model.IssuingEntity;
import com.stpl.tech.forms.domain.model.Voucher;
import com.stpl.tech.forms.domain.model.VoucherFileDetail;
import com.stpl.tech.forms.domain.model.VoucherStatus;
import com.stpl.tech.forms.domain.model.VoucherStatusDetail;
import com.stpl.tech.forms.domain.model.Wallet;
import com.stpl.tech.forms.domain.model.WalletAccountType;
import com.stpl.tech.forms.domain.model.WalletApproverMappingVO;
import com.stpl.tech.forms.domain.model.WalletBusinessCostCenterMappingVO;
import com.stpl.tech.forms.domain.model.WalletStatus;
import com.stpl.tech.forms.domain.model.WalletTransaction;
import com.stpl.tech.forms.domain.model.WalletTransactionCode;
import com.stpl.tech.forms.domain.model.WalletTransactionCodeType;
import com.stpl.tech.forms.domain.model.WalletTransactionStatus;
import com.stpl.tech.forms.domain.model.WalletTransactionType;
import com.stpl.tech.forms.domain.model.WalletType;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.expense.model.VoucherFileData;
import com.stpl.tech.kettle.data.expense.model.VoucherStatusData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderRefundDetail;
import com.stpl.tech.kettle.data.model.OrderRefundStatus;
import com.stpl.tech.kettle.domain.model.RefundType;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

public class WalletDataConverter {
    private static char UNIT_IDENTIFIER= 'U';
    private static char EMPLOYEE_IDENTIFIER= 'E';


    public static Claim convert(ClaimDetailData claimDetailData, IdCodeName createdBy, IdCodeName lastUpdatedBy, IdCodeName employee, IdCodeName unit) {
        Claim claim = new Claim();
        claim.setClaimApprovedAmount(claimDetailData.getClaimApprovedAmount());
        claim.setClaimCurrentStatus(ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus()));
        claim.setClaimId(claimDetailData.getClaimId());
        claim.setClaimRequestedAmount(claimDetailData.getClaimRequestedAmount());
        claim.setClaimType(ClaimType.valueOf(claimDetailData.getClaimType()));
        claim.setCreatedBy(createdBy);
        claim.setCreationTime(claimDetailData.getCreationTime());
        claim.setWalletId(claimDetailData.getWalletId());
        if(employee != null){
            claim.setEmployee(employee);
        }
        if(claimDetailData.getHappayId() != null) {
            claim.setHappayId(claimDetailData.getHappayId());
        }
        claim.setLastUpdatedBy(lastUpdatedBy);
        claim.setLastUpdateTime(claimDetailData.getLastUpdateTime());
        if(unit != null) {
            claim.setUnit(unit);
        }
        return claim;
    }

    public static ClaimLog convert(ClaimLogDetailData claimLogDetailData, IdCodeName updatedBy) {
        ClaimLog claimLog = new ClaimLog();
        claimLog.setClaimId(claimLogDetailData.getClaimId());
        if(claimLogDetailData.getComments() != null) {
            claimLog.setComments(claimLogDetailData.getComments());
        }
        claimLog.setFromStatus(ClaimStatus.valueOf(claimLogDetailData.getFromStatus()));
        claimLog.setId(claimLogDetailData.getId());
        claimLog.setToStatus(ClaimStatus.valueOf(claimLogDetailData.getToStatus()));
        claimLog.setUpdatedBy(updatedBy);
        claimLog.setUpdateTime(claimLogDetailData.getUpdateTime());
        return claimLog;
    }

    public static Wallet convert(WalletData walletData) {
        Wallet wallet = new Wallet();
        StringBuilder sb= new StringBuilder();
        if(AppConstants.BATCH_CODE_KEY_TYPE_UNIT.equals(walletData.getAccountType())){
            sb.append(UNIT_IDENTIFIER);
        }else{
            sb.append(EMPLOYEE_IDENTIFIER);
        }
        sb.append('_').append(walletData.getWalletType()).append(walletData.getAccountNo());
        wallet.setId(walletData.getId());
        wallet.setAccountType(WalletAccountType.valueOf(walletData.getAccountType()));
        wallet.setAccountNo(walletData.getAccountNo());
        wallet.setAccountHolderName(walletData.getAccountHolderName());
        wallet.setAccountHolderContact(walletData.getAccountHolderContact());
        wallet.setAccountHolderEmail(walletData.getAccountHolderEmail());
        wallet.setWalletType(WalletType.valueOf(walletData.getWalletType()));
        wallet.setOpeningAmount(walletData.getOpeningAmount());
        wallet.setTotalAmount(walletData.getTotalAmount());
        if (walletData.getCurrentBalance() != null) {
            wallet.setCurrentBalance(walletData.getCurrentBalance());
        }
        if (walletData.getIssuedAmount() != null) {
            wallet.setIssuedAmount(walletData.getIssuedAmount());
        }
        if (walletData.getCurrentBalance() != null) {
            wallet.setCurrentBalance(walletData.getCurrentBalance());
        }
        if (walletData.getPendingApproval() != null) {
            wallet.setPendingApproval(walletData.getPendingApproval());
        }
        if (walletData.getApprovedAmount() != null) {
            wallet.setApprovedAmount(walletData.getApprovedAmount());
        }
        if (walletData.getRejectedAmount() != null) {
            wallet.setRejectedAmount(walletData.getRejectedAmount());
        }
        if (walletData.getSpentAmount() != null) {
            wallet.setSpentAmount(walletData.getSpentAmount());
        }
        if (walletData.getAssociatedId() != null) {
            wallet.setAssociatedId(walletData.getAssociatedId());
        }
        wallet.setStatus(WalletStatus.valueOf(walletData.getStatus()));
        wallet.setLastUpdateTime(walletData.getLastUpdateTime());
        return wallet;

    }

    public static Voucher convert(VoucherData data, WalletData walletData, boolean status, boolean fileData,
                                  List<WalletTransactionData> transactionDatas, MasterDataCache masterDataCache, List<VoucherRejection> rejections) {
        Voucher voucher = new Voucher();
        voucher.setId(data.getId());
        voucher.setGeneratedVoucherId(data.getGeneratedVoucherId());
        voucher.setAccountType(WalletAccountType.valueOf(data.getAccountType()));
        voucher.setAccountNo(data.getAccountNo());
        voucher.setWalletId(data.getWalletId());
        voucher.setBusinessDate(data.getBusinessDate());
        if (data.getExpenseType() != null) {
            voucher.setExpenseType(data.getExpenseType());
        }
        if (data.getExpenseDetail() != null) {
            voucher.setExpenseDetail(data.getExpenseDetail());
        }
        voucher.setWallet(new IdCodeName(walletData.getId(), walletData.getAccountNo(), walletData.getAccountHolderName()));
        voucher.setCurrentStatus(VoucherStatus.valueOf(data.getCurrentStatus()));
        voucher.setIssuedAmount(data.getIssuedAmount());
        if (data.getExpenseAmount() != null) {
            voucher.setExpenseAmount(data.getExpenseAmount());
        }
        EmployeeBasicDetail employee = masterDataCache.getEmployeeBasicDetail(data.getIssuedTo());
        voucher.setIssuedTo(new IdCodeName(data.getIssuedTo(), employee.getEmployeeCode(), employee.getName()));
        voucher.setIssuedTime(data.getIssuedTime());
        voucher.setAccountableInPNL(data.getAccountableInPNL().equals(AppConstants.YES));
        voucher.setExpenseCategory(data.getExpenseCategory());
        voucher.setBudgetCategory(data.getBudgetCategory());
        voucher.setExpenseMetadataId(data.getExpenseMetadataId());
        if(data.getPnlDetailId() != null) {
            voucher.setPnlDetailId(data.getPnlDetailId());
        }
        if(data.getPnlInclusionDate() != null) {
            voucher.setPnlInclusionDate(data.getPnlInclusionDate());
        }
        employee = masterDataCache.getEmployeeBasicDetail(data.getIssuedBy());
        voucher.setIssuedBy(new IdCodeName(data.getIssuedBy(), employee.getEmployeeCode(), employee.getName()));
        if (data.getLastUpdatedTime() != null) {
            voucher.setLastUpdatedTime(data.getLastUpdatedTime());
        }
        if (data.getIsReimbursed() != null) {
            voucher.setIsReimbursed(AppUtils.getStatus(data.getIsReimbursed()));
        }
        if (status) {
            data.getStatusDatas().forEach(statusData -> {
                voucher.getStatusDetail().add(convert(statusData, masterDataCache));
            });
        }
        if (fileData) {
            data.getFileDatas().forEach(file -> {
                voucher.getFileDetails().add(convert(file, masterDataCache));
            });
        }
        if (transactionDatas != null) {
            transactionDatas.forEach(transactionData -> {
                transactionData.setEntityId(data.getId());
                transactionData.setEntityType(WalletTransactionEntityType.VOUCHER.name());
                voucher.getTransactions().add(convert(transactionData, masterDataCache));
            });
        }
        if(rejections != null) {
            voucher.setRejections(rejections);
        }
        voucher.setVoucherDate(data.getVoucherDate());
        voucher.setGrNumber(data.getGrNumber());
        return voucher;
    }

    private static WalletTransaction convert(WalletTransactionData data, MasterDataCache masterDataCache) {
        WalletTransaction trans = new WalletTransaction();
        trans.setWalletId(data.getWalletData().getId());
        trans.setEntityType(WalletTransactionEntityType.valueOf(data.getEntityType()));
        trans.setEntityId(data.getEntityId());
        trans.setTransactionType(WalletTransactionType.valueOf(data.getTransactionType()));
        trans.setTransactionAmount(data.getTransactionAmount());
        trans.setTransactionCode(WalletTransactionCode.valueOf(data.getTransactionCode()));
        trans.setTransactionCodeType(WalletTransactionCodeType.valueOf(data.getTransactionCodeType()));
        trans.setTransactionStatus(WalletTransactionStatus.valueOf(data.getTransactionStatus()));
        trans.setTransactionTime(data.getTransactionTime());
        trans.setEntity(IssuingEntity.valueOf(data.getEntity()));
        EmployeeBasicDetail employee = masterDataCache.getEmployeeBasicDetail(data.getCreatedBy());
        trans.setCreatedBy(new IdCodeName(data.getCreatedBy(), employee.getEmployeeCode(), employee.getName()));
        return trans;
    }

    private static VoucherFileDetail convert(VoucherFileData fileData, MasterDataCache masterDataCache) {
        VoucherFileDetail detail = new VoucherFileDetail();
        detail.setId(fileData.getId());
        detail.setFileName(fileData.getFileName());
        detail.setFileType(fileData.getFileType());
        detail.setContentType(fileData.getContentType());
        if (fileData.getFilePath() != null) {
            detail.setFilePath(fileData.getFilePath());
        }
        detail.setStatus(fileData.getStatus());
        detail.setCreatedOn(fileData.getCreatedOn());
        detail.setStorageType(FileStorageType.valueOf(fileData.getStorageType()));
        /*if (fileData.getS3Url() != null) {
            detail.setS3Url(fileData.getS3Url());
        }*/
        return detail;
    }

    private static VoucherStatusDetail convert(VoucherStatusData statusData, MasterDataCache masterDataCache) {
        VoucherStatusDetail statusDetail = new VoucherStatusDetail();
        statusDetail.setFromStatus(VoucherStatus.valueOf(statusData.getFromStatus()));
        statusDetail.setToStatus(VoucherStatus.valueOf(statusData.getToStatus()));
        EmployeeBasicDetail employee = masterDataCache.getEmployeeBasicDetail(statusData.getGeneratedBy());
        statusDetail.setGeneratedBy(
                new IdCodeName(statusData.getGeneratedBy(), employee.getEmployeeCode(), employee.getName()));
        if (statusData.getActionComment() != null) {
            statusDetail.setActionComment(statusData.getActionComment());
        }
        statusDetail.setActionTime(statusData.getActionTime());
        statusDetail.setTransitionStatus(statusData.getTransitionStatus());
        return statusDetail;
    }

    public static VoucherRejection convert(VoucherRejectionData voucherRejectionData, IdCodeName rejectedBy) {
        VoucherRejection voucherRejection = new VoucherRejection();
        voucherRejection.setId(voucherRejectionData.getId());
        voucherRejection.setName(voucherRejectionData.getRejectionName());
        voucherRejection.setRejectedBy(rejectedBy);
        voucherRejection.setRejectionEntity(RejectionEntity.valueOf(voucherRejectionData.getRejectionEntity()));
        voucherRejection.setVoucherId(voucherRejectionData.getVoucherId());
        return voucherRejection;
    }

    public static  WalletBusinessCostCenterMappingVO convert(WalletBusinessCostCenterMapping walletBusinessCostCenterMapping) {
        return WalletBusinessCostCenterMappingVO.builder().walletBccMappingId(walletBusinessCostCenterMapping.getWalletBccMappingId()).walletId(walletBusinessCostCenterMapping.getWalletId())
                .bccCode(walletBusinessCostCenterMapping.getBccCode()).bccName(walletBusinessCostCenterMapping.getBccName()).bccId(walletBusinessCostCenterMapping.getBccId())
                .bccType(walletBusinessCostCenterMapping.getBccType()).mappingStatus(walletBusinessCostCenterMapping.getMappingStatus()).addTime(walletBusinessCostCenterMapping.getAddTime())
                .lastUpdatedBy(walletBusinessCostCenterMapping.getLastUpdatedBy()).lastUpdateTime(walletBusinessCostCenterMapping.getLastUpdateTime()).build();
    }

    public static WalletApproverMappingVO convert (WalletApproverMapping walletApproverMapping){
        return WalletApproverMappingVO.builder().walletApproverMappingId(walletApproverMapping.getWalletApproverMappingId()).walletId(walletApproverMapping.getWalletId()).
                approverId(walletApproverMapping.getApproverId()).approverName(walletApproverMapping.getApproverName()).mappingStatus(walletApproverMapping.getMappingStatus()).
                addTime(walletApproverMapping.getAddTime()).updatedTime(walletApproverMapping.getUpdatedTime()).updatedBy(walletApproverMapping.getUpdatedBy()).build();
    }

    public static OrderRefundDetail convert(OrderRefundVoucher orderRefundVoucher, OrderDetail orderDetail) {
        return OrderRefundDetail.builder().orderId(orderRefundVoucher.getOrderId())
                .refundAmount(orderRefundVoucher.getRefundAmount()).refundReason(orderRefundVoucher.getRefundReason())
                .refundStatus(OrderRefundStatus.INITIATED.name()).refundType(String.valueOf(RefundType.SERVICE_CHARGE)).createdBy(orderRefundVoucher.getCreatedBy()).creationTime(AppUtils.getCurrentTimestamp())
                .otpVerifiedBy(orderRefundVoucher.getOtpVerifiedBy()).otpVerifiedContact(orderRefundVoucher.getOtpVerifiedContact()).updatedTime(AppUtils.getCurrentTimestamp())
                .unitId(orderDetail.getUnitId()).build();
    }
}
