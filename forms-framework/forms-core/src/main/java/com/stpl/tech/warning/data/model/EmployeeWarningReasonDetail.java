package com.stpl.tech.warning.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "EMPLOYEE_WARNING_REASON")
public class EmployeeWarningReasonDetail {

	private Integer warningReasonId;
	private Integer warningId;
	private Integer warningStatusId;
	private String reasonAddedBy;
	private Integer reasonId;
	private String reasonName;
	private String reasonCategory;
	private String status;
	private Date lastUpdatedOn;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "EMPLOYEE_WARNING_REASON_ID")
	public Integer getWarningReasonId() {
		return warningReasonId;
	}

	public void setWarningReasonId(Integer warningReasonId) {
		this.warningReasonId = warningReasonId;
	}
	
	@Column(name = "WARNING_ID", nullable = false)
	public Integer getWarningId() {
		return warningId;
	}

	public void setWarningId(Integer warningId) {
		this.warningId = warningId;
	}


	@Column(name = "EMPLOYEE_WARNING_STATUS_ID", nullable = false)
	public Integer getWarningStatusId() {
		return warningStatusId;
	}

	public void setWarningStatusId(Integer warningStatusId) {
		this.warningStatusId = warningStatusId;
	}

	@Column(name = "REASON_ADDED_BY", nullable = false)
	public String getReasonAddedBy() {
		return reasonAddedBy;
	}

	public void setReasonAddedBy(String reasonAddedBy) {
		this.reasonAddedBy = reasonAddedBy;
	}

	@Column(name = "REASON_ID", nullable = false)
	public Integer getReasonId() {
		return reasonId;
	}

	public void setReasonId(Integer reasonId) {
		this.reasonId = reasonId;
	}

	@Column(name = "REASON_NAME", nullable = false)
	public String getReasonName() {
		return reasonName;
	}

	public void setReasonName(String reasonName) {
		this.reasonName = reasonName;
	}

	@Column(name = "REASON_CATEGORY", nullable = false)
	public String getReasonCategory() {
		return reasonCategory;
	}

	public void setReasonCategory(String reasonCategory) {
		this.reasonCategory = reasonCategory;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "LAST_UPDATED_ON", nullable = false)
	public Date getLastUpdatedOn() {
		return lastUpdatedOn;
	}

	public void setLastUpdatedOn(Date lastUpdatedOn) {
		this.lastUpdatedOn = lastUpdatedOn;
	}
}
