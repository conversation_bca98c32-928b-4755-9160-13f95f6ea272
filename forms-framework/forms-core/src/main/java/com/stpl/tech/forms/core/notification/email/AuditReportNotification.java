package com.stpl.tech.forms.core.notification.email;

import com.stpl.tech.forms.core.notification.email.template.AuditNotificationTemplate;
import com.stpl.tech.forms.core.util.AuditUtil;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;
import org.apache.commons.text.FormattableUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class AuditReportNotification extends EmailNotification {
	private static final Logger LOG = LoggerFactory.getLogger(AuditReportNotification.class);

    private AuditNotificationTemplate auditNotificationTemplate;
	private EnvType envType;
	private String subjectOfEmail;

	public AuditReportNotification() {
	}

	public AuditReportNotification(AuditNotificationTemplate auditNotificationTemplate,
								   EnvType envType, String subjectOfEmail) {
		this.auditNotificationTemplate = auditNotificationTemplate;
		this.envType = envType;
		this.subjectOfEmail = subjectOfEmail;
		//this.emails = emails;
	}

	@Override
	public String[] getToEmails() {
		LOG.info("Audit Notification to emails : " + auditNotificationTemplate.getEmails());
		if (AuditUtil.isDev(getEnvironmentType())) {
			return new String[] { "<EMAIL>"};
		} else {
			Set<String> mails = new HashSet<>();
			if(auditNotificationTemplate.getEmails()!=null && auditNotificationTemplate.getEmails().size() > 0){
				mails.addAll(auditNotificationTemplate.getEmails());
			}
			String[] simpleArray = new String[mails.size()];
			return mails.toArray(simpleArray);
		}
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		/*subjectOfEmail = String.format("%s Report for %s for %s",
				auditNotificationTemplate.getAudit().getAuditForm().getName(),
				auditNotificationTemplate.getAudit().getAuditUnit().getName(),
				AuditUtil.getFormattedTime(auditNotificationTemplate.getAudit().getAuditSubmissionDate(), "EEE dd MMM yyyy"));*/
		if (AuditUtil.isDev(getEnvironmentType())) {
			subjectOfEmail = " [DEV] : " + subjectOfEmail;
		}
		return subjectOfEmail;
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return auditNotificationTemplate.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}


	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}
}
