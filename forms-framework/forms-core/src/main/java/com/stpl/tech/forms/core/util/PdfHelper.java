package com.stpl.tech.forms.core.util;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.ElementList;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 21-05-2017.
 */
public class PdfHelper {

    private static final Logger LOG = LoggerFactory.getLogger(PdfHelper.class);

    public static File createPdf(String content, String basePath, String fileName) throws IOException, DocumentException {
        Document document = new Document(PageSize.A4);
        PdfWriter.getInstance(document, new FileOutputStream(basePath + File.separator + fileName));
        document.open();
        PdfPTable table = new PdfPTable(1);
        PdfPCell cell = new PdfPCell();
        ElementList list = XMLWorkerHelper.parseToElementList(content, null);
        list.forEach(element -> {
            try {
                cell.addElement(element);
            } catch (Exception e) {
                LOG.error("Error adding this element {}", element, e);
            }
        });

        table.addCell(cell);
        table.setTotalWidth(PageSize.A4.getWidth());
        table.setLockedWidth(true);
        table.getDefaultCell().setBorder(0);
        table.setPaddingTop(0.0f);
        document.add(table);
        document.close();
        return new File(basePath + File.separator + fileName);
    }

}
