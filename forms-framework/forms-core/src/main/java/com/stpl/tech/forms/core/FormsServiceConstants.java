/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.forms.core;

/**
 * Created by <PERSON><PERSON> on 04-05-2016.
 */
public class FormsServiceConstants {

    private FormsServiceConstants() {
    }

    public static final String API_VERSION = "v1";

    public static final String SEPARATOR = "/";

    public static final String AUDIT_FORM_MANAGEMENT_ROOT_CONTEXT = "audit-form-management";

    public static final String AUDIT_MANAGEMENT_ROOT_CONTEXT = "audit-management";

    public static final String BATCH_MANAGEMENT_ROOT_CONTEXT = "batch-management";

    public static final String AUDIT_CONSTANT_YES = "Y";

    public static final String AUDIT_CONSTANT_NO = "N";

    public static final String ACTIVE = "ACTIVE";

    public static final String IN_ACTIVE = "IN_ACTIVE";

    public static final String CHARSET = "utf-8";

    public static final int SYSTEM_USER = 120056;

    public static final String WARNING_LETTER_MANAGEMENT_ROOT_CONTEXT = "warning-letter-management";

    public static final String WALLET_MANAGEMENT_ROOT_CONTEXT = "wallet-management";

    public static final String CLAIM_MANAGEMENT_ROOT_CONTEXT = "claim-management";

}
