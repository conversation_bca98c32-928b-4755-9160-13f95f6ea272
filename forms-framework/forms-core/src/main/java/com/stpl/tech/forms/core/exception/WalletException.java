package com.stpl.tech.forms.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.NOT_ACCEPTABLE, reason = "Wallet Exception")
public class WalletException extends Exception {

	private static final long serialVersionUID = 5750539343830351457L;

	private Integer errorCode;
	private String errorMsg;
	private String errorTitle;

	public WalletException(String errorTitle, String errorMsg, Integer errorCode) {
		this.errorMsg = errorMsg;
		this.errorTitle = errorTitle;
		this.errorCode = errorCode;
	}

	public WalletException(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	public WalletException(String errorMsg, Integer errorCode) {
		this.errorMsg = errorMsg;
		this.errorCode = errorCode;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public String getErrorTitle() {
		return errorTitle;
	}

	public Integer getErrorCode() {
		return errorCode;
	}
}
