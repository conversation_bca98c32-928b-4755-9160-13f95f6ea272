
package com.stpl.tech.warning.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "EMPLOYEE_WARNING_STATUS")
public class EmployeeWarningStatusDetail {

	private Integer statusId;
	private Integer warningId;
	private String fromStatus;
	private String toStatus;
	private Integer authorityId;
	private String authorityName;
	private String authorityDesg;
	private String comment;
	private String actionTakenBy;
	private Date actionTakenOn;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "EMPLOYEE_WARNING_STATUS_ID")
	public Integer getStatusId() {
		return statusId;
	}

	public void setStatusId(Integer statusId) {
		this.statusId = statusId;
	}

	@Column(name = "WARNING_ID", nullable = false)
	public Integer getWarningId() {
		return warningId;
	}

	public void setWarningId(Integer warningId) {
		this.warningId = warningId;
	}

	@Column(name = "FROM_STATUS", nullable = false)
	public String getFromStatus() {
		return fromStatus;
	}

	public void setFromStatus(String fromStatus) {
		this.fromStatus = fromStatus;
	}

	@Column(name = "TO_STATUS", nullable = false)
	public String getToStatus() {
		return toStatus;
	}

	public void setToStatus(String toStatus) {
		this.toStatus = toStatus;
	}

	@Column(name = "AUTHORITY_ID", nullable = false)
	public Integer getAuthorityId() {
		return authorityId;
	}

	public void setAuthorityId(Integer authorityId) {
		this.authorityId = authorityId;
	}

	@Column(name = "AUTHORITY_NAME", nullable = false)
	public String getAuthorityName() {
		return authorityName;
	}

	public void setAuthorityName(String authorityName) {
		this.authorityName = authorityName;
	}

	@Column(name = "AUTHORITY_DESIGNATION", nullable = true)
	public String getAuthorityDesg() {
		return authorityDesg;
	}

	public void setAuthorityDesg(String authorityDesg) {
		this.authorityDesg = authorityDesg;
	}

	@Column(name = "COMMENT", nullable = false)
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	@Column(name = "ACTION_TAKEN_BY", nullable = false)
	public String getActionTakenBy() {
		return actionTakenBy;
	}

	public void setActionTakenBy(String actionTakenBy) {
		this.actionTakenBy = actionTakenBy;
	}

	@Column(name = "ACTION_TAKEN_ON", nullable = false)
	public Date getActionTakenOn() {
		return actionTakenOn;
	}

	public void setActionTakenOn(Date actionTakenOn) {
		this.actionTakenOn = actionTakenOn;
	}

}
