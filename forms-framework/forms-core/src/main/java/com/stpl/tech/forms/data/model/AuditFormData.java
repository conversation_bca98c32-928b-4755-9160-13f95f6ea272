package com.stpl.tech.forms.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "AUDIT_FORM")
public class AuditFormData {

    private Integer id;
    private String name;
    private String description;
    private Integer createdBy;
    private Integer lastUpdatedBy;
    private Date creationTime;
    private Date lastUpdateTime;
    private String notificationEmails;
    private String notificationSlackChannels;
    private String status = "ACTIVE";
    private String askWarningLetter = "N";
    private String supportTrial = "Y";
    private String showPastStats = "N";
    private List<AuditFormValuesData> values = new ArrayList<>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "NAME", nullable = false)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "DESCRIPTION")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "LAST_UPDATED_BY", nullable = false)
    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Column(name = "CREATION_TIME", nullable = false)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "LAST_UPDATED", nullable = false)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @Column(name = "NOTIFICATION_EMAILS")
    public String getNotificationEmails() {
        return notificationEmails;
    }

    public void setNotificationEmails(String notificationEmails) {
        this.notificationEmails = notificationEmails;
    }

    @Column(name = "NOTIFICATION_SLACK_CHANNELS")
    public String getNotificationSlackChannels() {
        return notificationSlackChannels;
    }

    public void setNotificationSlackChannels(String notificationSlackChannels) {
        this.notificationSlackChannels = notificationSlackChannels;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "auditFormData")
    public List<AuditFormValuesData> getValues() {
        return values;
    }

    public void setValues(List<AuditFormValuesData> values) {
        this.values = values;
    }

    @Column(name = "ASK_WARNING_LETTER", nullable = false)
    public String getAskWarningLetter() {
        return askWarningLetter;
    }

    public void setAskWarningLetter(String askWarningLetter) {
        this.askWarningLetter = askWarningLetter;
    }

    @Column(name = "SUPPORT_TRIAL", nullable = false)
    public String getSupportTrial() {
        return supportTrial;
    }

    public void setSupportTrial(String supportTrial) {
        this.supportTrial = supportTrial;
    }

    @Column(name = "SHOW_PAST_STATS", nullable = false)
    public String getShowPastStats() {
        return showPastStats;
    }

    public void setShowPastStats(String showPastStats) {
        this.showPastStats = showPastStats;
    }
}
