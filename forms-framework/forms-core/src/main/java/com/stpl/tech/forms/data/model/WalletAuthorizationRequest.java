/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.forms.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 20 Jul, 2015 3:19:50 PM by Hibernate Tools 4.0.0


@SuppressWarnings("serial")
@Entity
@Table(name = "WALLET_AUTHORIZATION_REQUEST")
public class WalletAuthorizationRequest implements java.io.Serializable {

	private Integer authorizationRequestId;
	private String authorizationId;
	private String authorizationMode;
	private String authorizationCode;
	private String authorizationText;
	private Date addTime;
	private Date validationTime;

	public WalletAuthorizationRequest() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "AUTHORIZATION_REQUEST_ID", unique = true, nullable = false)
	public Integer getAuthorizationRequestId() {
		return this.authorizationRequestId;
	}

	public void setAuthorizationRequestId(Integer authorizationRequestId) {
		this.authorizationRequestId = authorizationRequestId;
	}

	@Column(name = "AUTHORIZATON_ID", nullable = false)
	public String getAuthorizationId() {
		return this.authorizationId;
	}

	public void setAuthorizationId(String authorizationId) {
		this.authorizationId = authorizationId;
	}

	@Column(name = "AUTHORIZATION_MODE", nullable = false)
	public String getAuthorizationMode() {
		return this.authorizationMode;
	}

	public void setAuthorizationMode(String authorizationMode) {
		this.authorizationMode = authorizationMode;
	}

	@Column(name = "AUTHORZATION_CODE", nullable = false)
	public String getAuthorizationCode() {
		return this.authorizationCode;
	}

	public void setAuthorizationCode(String authorizationCode) {
		this.authorizationCode = authorizationCode;
	}

	@Column(name = "AUTHORZATION_TEXT", nullable = false)
	public String getAuthorizationText() {
		return this.authorizationText;
	}

	public void setAuthorizationText(String authorizationText) {
		this.authorizationText = authorizationText;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ADD_TIME", nullable = false, length = 19)
	public Date getAddTime() {
		return this.addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "VALIDATION_TIME", nullable = true, length = 19)
	public Date getValidationTime() {
		return this.validationTime;
	}

	public void setValidationTime(Date validationTime) {
		this.validationTime = validationTime;
	}
}
