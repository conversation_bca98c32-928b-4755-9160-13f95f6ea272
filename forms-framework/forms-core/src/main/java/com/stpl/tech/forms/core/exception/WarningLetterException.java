package com.stpl.tech.forms.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "Warning Letter Exception")
public class WarningLetterException extends Exception {

	private static final long serialVersionUID = 2743891720682406919L;
	private Integer errorCode;
	private String errorMsg;
	private String errorTitle;

	public WarningLetterException(String errorTitle, String errorMsg, Integer errorCode) {
		this.errorMsg = errorMsg;
		this.errorTitle = errorTitle;
		this.errorCode = errorCode;
	}

	public WarningLetterException(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public String getErrorTitle() {
		return errorTitle;
	}

	public Integer getErrorCode() {
		return errorCode;
	}
}
