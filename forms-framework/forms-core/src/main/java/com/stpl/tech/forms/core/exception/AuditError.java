package com.stpl.tech.forms.core.exception;

import java.io.Serializable;

/**
 * Created by Chaayos on 27-07-2017.
 */
public class AuditError implements Serializable {

    private static final long serialVersionUID = -2616742475173127189L;
    
    private Integer errorCode;
    private String errorMsg;
    private String errorTitle;

    AuditError(String errorTitle, String errorMsg, Integer errorCode) {
        this.errorMsg = errorMsg;
        this.errorTitle = errorTitle;
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public String getErrorTitle() {
        return errorTitle;
    }

    public Integer getErrorCode() {
        return errorCode;
    }
}
