package com.stpl.tech.forms.data.model;


import javax.persistence.*;

@Entity
@Table(name = "BATCH_CODE_COUNTER" , uniqueConstraints = @UniqueConstraint(columnNames = {"ID", "CODE_PREFIX"}))
public class BatchCodeCounter {

    private Integer id;

    private String codePrefix;

    private Integer currentCounter;

    private Integer nextCounter;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "CODE_PREFIX", unique = true, nullable = false)
    public String getCodePrefix() {
        return codePrefix;
    }

    public void setCodePrefix(String codePrefix) {
        this.codePrefix = codePrefix;
    }

    @Column(name = "CURRENT_COUNTER", nullable = false)
    public Integer getCurrentCounter() {
        return currentCounter;
    }

    public void setCurrentCounter(Integer currentCounter) {
        this.currentCounter = currentCounter;
    }

    @Column(name = "NEXT_COUNTER", nullable = false)
    public Integer getNextCounter() {
        return nextCounter;
    }

    public void setNextCounter(Integer nextCounter) {
        this.nextCounter = nextCounter;
    }

}
