package com.stpl.tech.forms.core.service;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.forms.core.exception.AuditException;
import com.stpl.tech.forms.domain.model.Audit;
import com.stpl.tech.util.TemplateRenderingException;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

public interface AuditService {

    public Integer submitAudit(Audit audit) throws AuditException, TemplateRenderingException, IOException;

    public List<Audit> findAudit(Integer unitId, Date start, Date end, Integer auditFormId) throws AuditException;

    public void downloadAuditReport(HttpServletResponse response, Integer auditId) throws AuditException, TemplateRenderingException, IOException, DocumentException;

    public View downloadGenerateAudit(Integer auditId) throws AuditException, TemplateRenderingException, IOException;

    public void emailAuditReport(Integer auditId) throws AuditException, TemplateRenderingException, IOException, DocumentException;

    public Audit getAudit(Integer auditId);
}
