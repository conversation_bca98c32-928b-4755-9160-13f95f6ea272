package com.stpl.tech.forms.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "BATCH_CODE")
public class BatchCode implements java.io.Serializable {

    private Integer id;

	private BatchData batchData;

    private String code;


    private String status;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "BATCH_CODE", nullable = false)
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "BATCH_DATA_ID", nullable = false)
	public BatchData getBatchData() {
		return batchData;
	}

	public void setBatchData(BatchData batchData) {
		this.batchData = batchData;
	}


}
