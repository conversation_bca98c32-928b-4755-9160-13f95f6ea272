
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.forms.data.dao.impl;

import com.stpl.tech.forms.data.dao.FormAuthorizationDao;
import com.stpl.tech.forms.data.model.WalletAuthorizationRequest;
import com.stpl.tech.master.core.AuthorizationType;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;

@Repository
public class FormAuthorizationDaoImpl extends AbstractDaoImpl implements FormAuthorizationDao {

	private static final Logger LOG = LoggerFactory.getLogger(FormAuthorizationDaoImpl.class);

	@Override
	public String createSMSAuthorizationRequest(String contactNumber, String code, String text) {
		return createAuthorizationRequest(AuthorizationType.SMS, contactNumber, code, text);
	}

	private String createAuthorizationRequest(AuthorizationType type, String id, String code, String text) {
		LOG.info("Contact : " + id + "  text : " + text);
		WalletAuthorizationRequest request = new WalletAuthorizationRequest();
		request.setAddTime(AppUtils.getCurrentTimestamp());
		request.setAuthorizationId(id);
		request.setAuthorizationText(text);
		request.setAuthorizationMode(type.name());
		request.setAuthorizationCode(code);
		manager.persist(request);
		return request.getAuthorizationCode();
	}

	@Override
	public Integer updateAuthorizatioRequest(String contactNo, String token) {
		StringBuilder queryBuilder = new StringBuilder(
				"UPDATE WalletAuthorizationRequest a SET a.validationTime = :validationTime WHERE  a.authorizationId = :authorizationId and ");
		queryBuilder.append(
				" a.authorizationMode = :authorizationMode and a.authorizationCode = :authorizationCode and a.validationTime is null ");
		Query query = manager.createQuery(queryBuilder.toString());
		query.setParameter("authorizationId", contactNo);
		query.setParameter("authorizationMode", AuthorizationType.SMS.name());
		query.setParameter("authorizationCode", token);
		query.setParameter("validationTime", AppUtils.getCurrentTimestamp());
		return query.executeUpdate();
	}
	
}
