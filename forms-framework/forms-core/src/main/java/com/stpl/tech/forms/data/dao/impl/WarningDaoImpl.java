package com.stpl.tech.forms.data.dao.impl;

import com.stpl.tech.forms.data.dao.WarningDao;
import com.stpl.tech.warning.data.model.EmployeeWarningDetail;
import com.stpl.tech.warning.data.model.EmployeeWarningReasonDetail;
import com.stpl.tech.warning.data.model.EmployeeWarningStatusDetail;
import com.stpl.tech.warning.data.model.WarningImageDetailData;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.Date;
import java.util.List;

@Repository
public class WarningDaoImpl extends AbstractDaoImpl implements WarningDao {

	@Override
	public List<EmployeeWarningDetail> getWarningLetters(Integer unitId, Date startDate, Date endDate, String status, Integer amId) {
		StringBuilder queryString = new StringBuilder("FROM EmployeeWarningDetail e WHERE ");
		if (unitId != null && unitId != 0) {
			queryString.append("e.unitId = :unitId ");
		} else {
			queryString.append(" 1 = 1 ");
		}

		if (status != null) {
			queryString.append("and e.warningStatus = :status ");
		}
		
		if (amId != null) {
			queryString.append("and e.areaMangerId = :amId ");
		}

		if (startDate != null) {
			queryString.append("and e.creationTime >= :startDate ");
		}

		if (endDate != null) {
			queryString.append("and e.creationTime <= :endDate ");
		}

		queryString.append("order by e.creationTime desc");

		Query query = manager.createQuery(queryString.toString());

		if (unitId != null && unitId != 0) {
			query.setParameter("unitId", unitId);
		}

		if (status != null) {
			query.setParameter("status", status);
		}
		
		if (amId != null) {
			query.setParameter("amId", amId);
		}

		if (startDate != null) {
			query.setParameter("startDate", startDate);
		}

		if (endDate != null) {
			query.setParameter("endDate", endDate);
		}

		return query.getResultList();
	}

	@Override
	public List<EmployeeWarningStatusDetail> getWarningStatusDetail(Integer warningId) {
		Query query = manager.createQuery("FROM EmployeeWarningStatusDetail e where e.warningId = :warningId");
		if (warningId != null) {
			query.setParameter("warningId", warningId);
		}
		return query.getResultList();
	}

	@Override
	public List<EmployeeWarningReasonDetail> getWarningReasons(Integer statusId) {
		Query query = manager.createQuery("FROM EmployeeWarningReasonDetail e where e.warningStatusId = :statusId");
		if (statusId != null) {
			query.setParameter("statusId", statusId);
		}
		return query.getResultList();
	}

	@Override
	public List<WarningImageDetailData> getWarningImages(Integer warningId, String status) {
		Query query = manager.createQuery("FROM WarningImageDetailData e where e.warningId = :warningId and e.status = :status");
		if (warningId != null) {
			query.setParameter("warningId", warningId);
		}
		query.setParameter("status", status);
		return query.getResultList();
	}

	@Override
	public List<EmployeeWarningDetail> getWarningLetters(String warningStage) {
		StringBuilder queryString = new StringBuilder(
				"FROM EmployeeWarningDetail e WHERE e.warningStage  = :warningStage ");
		queryString.append("order by e.creationTime desc");

		Query query = manager.createQuery(queryString.toString());
		query.setParameter("warningStage", warningStage);

		return query.getResultList();
	}

}
