package com.stpl.tech.forms.core.mapper;

import com.stpl.tech.forms.data.model.WalletData;
import com.stpl.tech.forms.domain.model.VoucherCostCenterAllocation;
import com.stpl.tech.forms.domain.model.Wallet;
import com.stpl.tech.kettle.data.expense.model.VoucherCostCenterAllocationEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VoucherCostCenterAllocationMapper {
    VoucherCostCenterAllocationMapper INSTANCE = Mappers.getMapper(VoucherCostCenterAllocationMapper.class);

    VoucherCostCenterAllocationEntity toDto(VoucherCostCenterAllocation monthlyTargetsDataVO);

    List<VoucherCostCenterAllocationEntity> toDtoList(List<VoucherCostCenterAllocation> monthlyTargetsDataVO);

    VoucherCostCenterAllocation toDomain(VoucherCostCenterAllocationEntity monthlyTargetsData);

    List<VoucherCostCenterAllocation> toDomainList(List<VoucherCostCenterAllocationEntity> monthlyTargetsData);
}
