package com.stpl.tech.forms.data.dao;

import com.stpl.tech.forms.data.model.BatchCode;
import com.stpl.tech.forms.data.model.BatchData;

import java.util.List;

public interface BatchManagementDao {

    BatchData getBatchData(String batchId);

    BatchData saveBatchData(BatchData batchData);

    boolean saveBatchCodes(List<BatchCode> batchCodes);

    List<BatchCode> getBatchCodes(int batchDataId);

    List<String> checkIfUniqueBatchCodes(List<String> generatedCodes, List<String> existingBatchCodes);

    String getCodePrefix(String keyId, String keyType);

    int updateBatchCounter(String codePrefix, int counterResetLimit);
}
