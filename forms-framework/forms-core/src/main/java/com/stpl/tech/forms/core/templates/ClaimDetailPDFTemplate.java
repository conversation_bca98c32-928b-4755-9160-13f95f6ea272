package com.stpl.tech.forms.core.templates;

import com.stpl.tech.expense.domain.model.Claim;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class ClaimDetailPDFTemplate extends AbstractVelocityTemplate {

    private Claim claim;
    private String basePath;

    public ClaimDetailPDFTemplate() {

    }

    public ClaimDetailPDFTemplate(Claim claim, String basePath) {
        this.claim = claim;
        this.basePath = basePath;
    }

    @Override
    public String getTemplatePath() {
        return "templates/ClaimDetailPDFTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + claim.getClaimId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("claim", claim);
        return stringObjectMap;
    }

}
