package com.stpl.tech.forms.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serializable;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by chaayos on 17-05-2017.
 */
@ResponseStatus(value = HttpStatus.OK, reason = "Generic Audit Exception")
public class AuditException extends Exception implements Serializable {

    private static final long serialVersionUID = 6163156256372353453L;

    private final AuditError code;

    public AuditException(String message) {
        super(message);
        this.code = new AuditError("Error in transaction", message, 702);
    }

    public AuditException(String title, String message) {
        super(message);
        this.code = new AuditError(title, message, 702);
    }


    public AuditException(AuditError code) {
        super(code.getErrorMsg());
        this.code = code;
    }

    public AuditError getCode() {
        return code;
    }

}
