package com.stpl.tech.forms.data.dao.impl;

import com.stpl.tech.forms.data.dao.AbstractDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;

public class AbstractDaoImpl implements AbstractDao {

    private static final Logger LOG = LoggerFactory.getLogger(AbstractDaoImpl.class);

    @PersistenceContext(unitName = "AuditDataSourcePUName")
    @Qualifier(value = "AuditDataSourceEMFactory")
    protected EntityManager manager;

    @Override
    public <T> T update(T data, boolean flush) {

        try {
            data = manager.merge(data);
            if (flush) {
                manager.flush();
            }

            return data;
        } catch (Exception e) {
            LOG.error("Error updating {}", data.getClass().getName(), e);
        }
        return null;
    }

    @Override
    public <T> List<T> update(List<T> dataList, boolean flush) {

        try {
            for (T data : dataList) {
                update(data, false);
            }
            manager.flush();
            return dataList;
        } catch (Exception e) {
            LOG.error("Error updating List of objects ", e);
        }
        return new ArrayList<>();
    }


    @Override
    public <T> T add(T data, boolean flush) {
        try {
            manager.persist(data);
            if (flush) {
                manager.flush();
            }
            return data;
        } catch (Exception e) {
            LOG.error("Error adding {}", data.getClass().getName(), e);
        }
        return null;
    }

    @Override
    public <T> List<T> findAll(Class<T> data) {
        Query query = manager.createQuery("FROM " + data.getName() + " T");
        return query.getResultList();
    }

    @Override
    public <T, R> T find(Class<T> data, R key) {
        return manager.find(data, key);
    }

    @Override
    public <T> void delete(T data) {
        try {
            manager.remove(data);
            manager.flush();
        } catch (Exception e) {
            LOG.error("Error deleting {}", data.getClass().getName(), e);
        }
    }

    @Override
    public void flush() {
        manager.flush();
    }
}
