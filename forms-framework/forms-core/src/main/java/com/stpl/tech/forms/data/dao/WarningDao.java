package com.stpl.tech.forms.data.dao;

import com.stpl.tech.warning.data.model.EmployeeWarningDetail;
import com.stpl.tech.warning.data.model.EmployeeWarningReasonDetail;
import com.stpl.tech.warning.data.model.EmployeeWarningStatusDetail;
import com.stpl.tech.warning.data.model.WarningImageDetailData;

import java.util.Date;
import java.util.List;

public interface WarningDao extends AbstractDao {

	public List<EmployeeWarningDetail> getWarningLetters(Integer unitId,
			Date startDate, Date endDate, String status, Integer amId);

	public List<EmployeeWarningStatusDetail> getWarningStatusDetail(Integer warningId);

	public List<EmployeeWarningReasonDetail> getWarningReasons(Integer statusId);

	public List<WarningImageDetailData> getWarningImages(Integer warningId, String status);

	public List<EmployeeWarningDetail> getWarningLetters(String warningStage);
}
