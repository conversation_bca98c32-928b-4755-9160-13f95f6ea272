/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * 
 */
package com.stpl.tech.forms.core.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.forms.core.service.FormAuthorizationService;
import com.stpl.tech.forms.data.dao.FormAuthorizationDao;

@Service
public class FormAuthorizationServiceImpl implements FormAuthorizationService {

	@Autowired
	private FormAuthorizationDao dao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String createSMSAuthorizationRequest(String contactNumber, String code, String text) {
		return dao.createSMSAuthorizationRequest(contactNumber, code, text);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Integer updateSMSAuthorizationRequest(String contactNo, String token) {
		return dao.updateAuthorizatioRequest(contactNo, token);
	}

}
