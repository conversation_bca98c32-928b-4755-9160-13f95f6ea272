package com.stpl.tech.forms.core.templates;

import com.stpl.tech.forms.data.model.AuditFormValuesData;
import com.stpl.tech.forms.domain.model.Audit;
import com.stpl.tech.forms.domain.model.AuditForm;
import com.stpl.tech.forms.domain.model.AuditValues;
import com.stpl.tech.forms.domain.model.ScoreVO;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class PQSCAuditPDFTemplate extends AbstractVelocityTemplate {

    private Audit audit;
    private String basePath;
    private Map<Integer, List<String>> observationsComp;
    private Map<Integer, AuditFormValuesData> auditFormValuesDataMap;
    Map<Integer, Map<Integer, ScoreVO>> marksComp;
    Map<Integer, BigDecimal> auditMarksMap;
    Map<Integer, List<String>> strengthsComp;
    Map<Integer, List<AuditValues>> auditValuesDataMap;
    List<AuditValues> topLevelValues;
    private Map<Integer, String> auditImageFormValuesDataMap;
    private AuditForm auditForm;

    public PQSCAuditPDFTemplate() {

    }

    public PQSCAuditPDFTemplate(Audit audit, Map<Integer, AuditFormValuesData> auditFormValuesDataMap, String basePath,
                                Map<Integer, Map<Integer, ScoreVO>> marksComp, Map<Integer, List<String>> observationsComp,
                                Map<Integer, BigDecimal> auditMarksMap, Map<Integer, List<String>> strengthsComp,
                                Map<Integer, List<AuditValues>> auditValuesDataMap, List<AuditValues> topLevelValues,
                                Map<Integer, String> auditImageFormValuesDataMap, AuditForm auditForm) {
        this.audit = audit;
        this.auditFormValuesDataMap = auditFormValuesDataMap;
        this.basePath = basePath;
        this.marksComp = marksComp;
        this.observationsComp = observationsComp;
        this.auditMarksMap = auditMarksMap;
        this.strengthsComp = strengthsComp;
        this.auditValuesDataMap = auditValuesDataMap;
        this.topLevelValues = topLevelValues;
        this.auditImageFormValuesDataMap = auditImageFormValuesDataMap;
        this.auditForm = auditForm;
    }

    @Override
    public String getTemplatePath() {
        return "templates/PQSCAuditPDFTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + audit.getAuditForm().getName() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("audit", audit);
        stringObjectMap.put("auditFormValuesDataMap", auditFormValuesDataMap);
        stringObjectMap.put("marksComp", marksComp);
        stringObjectMap.put("observationsComp", observationsComp);
        stringObjectMap.put("auditMarksMap", auditMarksMap);
        stringObjectMap.put("strengthsComp", strengthsComp);
        stringObjectMap.put("auditValuesDataMap", auditValuesDataMap);
        stringObjectMap.put("topLevelValues", topLevelValues);
        stringObjectMap.put("auditImageFormValuesDataMap", auditImageFormValuesDataMap);
        stringObjectMap.put("auditForm", auditForm);
        return stringObjectMap;
    }

}
