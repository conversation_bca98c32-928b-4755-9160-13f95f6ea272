package com.stpl.tech.forms.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "CLAIM_DETAIL")
public class ClaimDetailData {

    private Integer claimId;
    private String claimType;
    private Date creationTime;
    private Date lastUpdateTime;
    private Integer createdBy;
    private Integer lastUpdatedBy;
    private String claimCurrentStatus;
    private Integer unitId;
    private Integer employeeId;
    private BigDecimal claimRequestedAmount;
    private BigDecimal claimApprovedAmount;
    private String happayId;
    private Integer walletId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CLAIM_ID", nullable = false, unique = true)
    public Integer getClaimId() {
        return claimId;
    }

    public void setClaimId(Integer claimId) {
        this.claimId = claimId;
    }

    @Column(name = "CLAIM_TYPE", nullable = false, length = 50)
    public String getClaimType() {
        return claimType;
    }

    public void setClaimType(String claimType) {
        this.claimType = claimType;
    }

    @Column(name = "CREATION_TIME", nullable = false)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "LAST_UPDATE_TIME", nullable = false)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "LAST_UPDATED_BY", nullable = false)
    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Column(name = "CURRENT_STATUS", length = 50, nullable = false)
    public String getClaimCurrentStatus() {
        return claimCurrentStatus;
    }

    public void setClaimCurrentStatus(String claimCurrentStatus) {
        this.claimCurrentStatus = claimCurrentStatus;
    }

    @Column(name = "UNIT_ID")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "REQUESTED_AMOUNT", nullable = false)
    public BigDecimal getClaimRequestedAmount() {
        return claimRequestedAmount;
    }

    public void setClaimRequestedAmount(BigDecimal claimRequestedAmount) {
        this.claimRequestedAmount = claimRequestedAmount;
    }

    @Column(name = "APPROVED_AMOUNT")
    public BigDecimal getClaimApprovedAmount() {
        return claimApprovedAmount;
    }

    public void setClaimApprovedAmount(BigDecimal claimApprovedAmount) {
        this.claimApprovedAmount = claimApprovedAmount;
    }

    @Column(name = "EMPLOYEE_ID")
    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    @Column(name = "HAPPAY_ID", length = 50)
    public String getHappayId() {
        return happayId;
    }

    public void setHappayId(String happayId) {
        this.happayId = happayId;
    }

    @Column(name = "WALLET_ID", nullable = false)
    public Integer getWalletId() {
        return walletId;
    }

    public void setWalletId(Integer walletId) {
        this.walletId = walletId;
    }
}
