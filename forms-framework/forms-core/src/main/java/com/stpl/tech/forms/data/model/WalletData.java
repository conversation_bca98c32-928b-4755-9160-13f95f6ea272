package com.stpl.tech.forms.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "WALLET_DATA")
public class WalletData {

	private Integer id;
	private String accountType;
	private String accountNo;
	private String accountHolderName;
	private String accountHolderContact;
	private String accountHolderEmail;
	private String walletType;
	private BigDecimal openingAmount;
	private BigDecimal totalAmount;
	private BigDecimal currentBalance;
	private BigDecimal issuedAmount;
	private BigDecimal pendingApproval;
	private BigDecimal approvedAmount;
	private BigDecimal rejectedAmount;
	private BigDecimal spentAmount;
	private Date lastUpdateTime;
	private String status;
	private String canAllocateCostToCafes;
	private Integer associatedId;
	private String ifscCode;

	private String cardNumber;

	private String bankName;
	private String cardHolderName;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "ACCOUNT_TYPE", nullable = false)
	public String getAccountType() {
		return accountType;
	}

	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	@Column(name = "ACCOUNT_NO", nullable = false)
	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	@Column(name = "ACCOUNT_HOLDER_NAME", nullable = false)
	public String getAccountHolderName() {
		return accountHolderName;
	}

	public void setAccountHolderName(String accountHolderName) {
		this.accountHolderName = accountHolderName;
	}

	@Column(name = "ACCOUNT_HOLDER_CONTACT", nullable = false)
	public String getAccountHolderContact() {
		return accountHolderContact;
	}

	public void setAccountHolderContact(String accountHolderContact) {
		this.accountHolderContact = accountHolderContact;
	}

	@Column(name = "ACCOUNT_HOLDER_EMAIL", nullable = false)
	public String getAccountHolderEmail() {
		return accountHolderEmail;
	}

	public void setAccountHolderEmail(String accountHolderEmail) {
		this.accountHolderEmail = accountHolderEmail;
	}

	@Column(name = "WALLET_TYPE", nullable = false)
	public String getWalletType() {
		return walletType;
	}

	public void setWalletType(String walletType) {
		this.walletType = walletType;
	}

	@Column(name = "OPENINIG_AMOUNT", nullable = false)
	public BigDecimal getOpeningAmount() {
		return openingAmount;
	}

	public void setOpeningAmount(BigDecimal openingAmount) {
		this.openingAmount = openingAmount;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = false)
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@Column(name = "CURRENT_BALANCE", nullable = true)
	public BigDecimal getCurrentBalance() {
		return currentBalance;
	}

	public void setCurrentBalance(BigDecimal currentBalance) {
		this.currentBalance = currentBalance;
	}

	@Column(name = "ISSUED_AMOUNT", nullable = true)
	public BigDecimal getIssuedAmount() {
		return issuedAmount;
	}

	public void setIssuedAmount(BigDecimal issuedAmount) {
		this.issuedAmount = issuedAmount;
	}

	@Column(name = "PENDING_APPROVAL", nullable = true)
	public BigDecimal getPendingApproval() {
		return pendingApproval;
	}

	public void setPendingApproval(BigDecimal pendingApproval) {
		this.pendingApproval = pendingApproval;
	}

	@Column(name = "APPROVED_AMOUNT", nullable = true)
	public BigDecimal getApprovedAmount() {
		return approvedAmount;
	}

	public void setApprovedAmount(BigDecimal approvedAmount) {
		this.approvedAmount = approvedAmount;
	}

	@Column(name = "REJECTED_AMOUNT", nullable = true)
	public BigDecimal getRejectedAmount() {
		return rejectedAmount;
	}

	public void setRejectedAmount(BigDecimal rejectedAmount) {
		this.rejectedAmount = rejectedAmount;
	}

	@Column(name = "SPENT_AMOUNT", nullable = true)
	public BigDecimal getSpentAmount() {
		return spentAmount;
	}

	public void setSpentAmount(BigDecimal spentAmount) {
		this.spentAmount = spentAmount;
	}

	@Column(name = "LAST_UPDATE_TIME", nullable = true)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}


	@Column(name = "CAN_ALLOCATE_COST_TO_CAFES", nullable = false)
	public String getCanAllocateCostToCafes() {
		return canAllocateCostToCafes;
	}

	public void setCanAllocateCostToCafes(String canAllocateCostToCafes) {
		this.canAllocateCostToCafes = canAllocateCostToCafes;
	}
	@Column(name = "ASSOCIATED_ID", nullable = false)
	public Integer getAssociatedId() {
		return associatedId;
	}

	public void setAssociatedId(Integer associatedId) {
		this.associatedId = associatedId;
	}


	@Column(name = "IFSC_CODE",nullable = false)
	public String getIfscCode() {
		return ifscCode;
	}

	public void setIfscCode(String ifscCode) {
		this.ifscCode = ifscCode;
	}

	@Column(name = "CARD_NUMBER",nullable = false)
	public String getCardNumber() {
		return cardNumber;
	}

	public void setCardNumber(String cardNumber) {
		this.cardNumber = cardNumber;
	}

	@Column(name = "BANK_NAME",nullable = false)
	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	@Column(name="CARD_HOLDER_NAME",nullable = false)
	public String getCardHolderName() {
		return cardHolderName;
	}

	public void setCardHolderName(String cardHolderName) {
		this.cardHolderName = cardHolderName;
	}
}