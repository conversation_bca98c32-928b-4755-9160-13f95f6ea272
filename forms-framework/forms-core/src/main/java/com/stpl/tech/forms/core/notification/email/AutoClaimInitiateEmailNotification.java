package com.stpl.tech.forms.core.notification.email;

import com.stpl.tech.forms.core.notification.email.template.AutoInitiateClaimInfoTemplate;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;
import org.apache.commons.lang3.StringUtils;

import java.util.stream.Stream;

public class AutoClaimInitiateEmailNotification extends EmailNotification {

    private final String toMailId;
    private final AutoInitiateClaimInfoTemplate autoInitiateClaimInfoTemplate;
    private final EnvType envType;

    public AutoClaimInitiateEmailNotification(String toMailId, AutoInitiateClaimInfoTemplate autoInitiateClaimInfoTemplate, EnvType envType) {
        this.autoInitiateClaimInfoTemplate = autoInitiateClaimInfoTemplate;
        this.envType = envType;
        if(StringUtils.isBlank(toMailId)) {
            toMailId = AppConstants.TECHNOLOGY_EMAIL;
        }
        this.toMailId = toMailId;
    }

    @Override
    public String[] getToEmails() {
        return (TransactionUtils.isDev(getEnvironmentType()) ?
                new String[] {AppConstants.TECHNOLOGY_EMAIL} :
                Stream.of(toMailId).toArray(String[]::new)
        );
    }

    @Override
    public String getFromEmail() {
        return AppConstants.REPORTING_EMAIL;
    }

    @Override
    public String subject() {
        return (TransactionUtils.isDev(getEnvironmentType()) ? "[ DEV ]" : "") +
                "[ Reminder ] Initiate claim for Vouchers";
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return autoInitiateClaimInfoTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
