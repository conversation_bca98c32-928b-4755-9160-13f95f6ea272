package com.stpl.tech.forms.core.util;

import com.stpl.tech.forms.domain.model.Voucher;
import com.stpl.tech.forms.domain.model.VoucherStatus;
import com.stpl.tech.forms.domain.model.VoucherStatusDetail;
import com.stpl.tech.master.Counter;
import com.stpl.tech.util.AppUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public class SpreadsheetUtil{

	@SuppressWarnings("deprecation")
	public static View getVoucherDetailsSheet(List<Voucher> voucherList, String fileName) {
		return new AbstractXlsxView() {
			@Override
			protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
					HttpServletRequest request, HttpServletResponse response) throws Exception {

				response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
				createVouchersSheet(voucherList, workbook);
			}

		};
	}

	private static void createVouchersSheet(List<Voucher> voucherList, Workbook workbook) {
		Sheet sheet = workbook.createSheet("Voucher Details");
		generateRecipeSheet(workbook, sheet);
		Counter rowCount = new Counter(3);
		if (voucherList.size() > 0) {
			for (Voucher voucher : voucherList) {
				if (voucher != null) {
					writeVoucher(sheet, rowCount.increment(), voucher);
				}
			}
		}
	}

	private static Sheet generateRecipeSheet(Workbook workbook, Sheet sheet) {
		sheet.setDefaultColumnWidth(20);
		int count = 0;
		CellStyle style = generateHeaderStyle(workbook);
		Row header = sheet.createRow(2);
		createHeader(header, count++, "Voucher Id", style);
		createHeader(header, count++, "A/c No", style);
		createHeader(header, count++, "A/c Holder Name", style);
		createHeader(header, count++, "Expense Header", style);
		createHeader(header, count++, "Expense Comment", style);
		createHeader(header, count++, "Status", style);
		createHeader(header, count++, "Issued Amount", style);
		createHeader(header, count++, "Expense Amount", style);
		createHeader(header, count++, "Issued To", style);
		createHeader(header, count++, "Issued Time", style);
		createHeader(header, count++, "Issued By", style);
		createHeader(header, count++, "Settled By", style);
		createHeader(header, count++, "Settlement Comment", style);
		createHeader(header, count++, "Settled On", style);
		createHeader(header, count++, "Area Manager Name", style);
		createHeader(header, count++, "Area Manager comment", style);
		createHeader(header, count++, "Area Manager Action Time", style);
		createHeader(header, count++, "Area Manager Action", style);
		createHeader(header, count++, "Finance Name", style);
		createHeader(header, count++, "Finance Comment", style);
		createHeader(header, count++, "Fianance Action Time", style);
		createHeader(header, count++, "Fianance Action", style);
		createHeader(header, count++, "Reimbursed", style);
		return sheet;
	}

	private static void createHeader(Row header, int number, String name, CellStyle style) {
		header.createCell(number).setCellValue(name);
		header.getCell(number).setCellStyle(style);
	}

	private static CellStyle generateHeaderStyle(Workbook workbook) {
		CellStyle style = workbook.createCellStyle();
		Font font = workbook.createFont();
		font.setFontName("Arial");
		style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		font.setBold(true);
		font.setColor(HSSFColor.HSSFColorPredefined.WHITE.getIndex());
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setFont(font);
		return style;
	}

	private static void writeVoucher(Sheet sheet, Counter rowCount, Voucher voucher) {
		Row aRow = sheet.createRow(rowCount.getC());
		int count = 0;
		aRow.createCell(count++).setCellValue(voucher.getGeneratedVoucherId());
		aRow.createCell(count++).setCellValue(voucher.getAccountNo());
		aRow.createCell(count++).setCellValue(voucher.getWallet().getName());
		aRow.createCell(count++).setCellValue(voucher.getExpenseType());
		aRow.createCell(count++).setCellValue(voucher.getExpenseDetail() != null ? voucher.getExpenseDetail() : "-");
		aRow.createCell(count++).setCellValue(voucher.getCurrentStatus().name());
		aRow.createCell(count++).setCellValue(voucher.getIssuedAmount().floatValue());
		aRow.createCell(count++)
				.setCellValue(voucher.getExpenseAmount() != null ? voucher.getExpenseAmount().floatValue() : 0f);
		aRow.createCell(count++)
				.setCellValue(voucher.getIssuedTo().getName() + " - " + voucher.getIssuedTo().getCode());
		aRow.createCell(count++).setCellValue(AppUtils.getBillPrintFormat(voucher.getIssuedTime()));
		aRow.createCell(count++)
				.setCellValue(voucher.getIssuedBy().getName() + " - " + voucher.getIssuedBy().getCode());
		for (VoucherStatusDetail statusDetail : voucher.getStatusDetail()) {
			if (statusDetail.getToStatus().equals(VoucherStatus.AM_PENDING)
					|| statusDetail.getToStatus().equals(VoucherStatus.CANCELLED)) {
				aRow.createCell(count++).setCellValue(
						statusDetail.getGeneratedBy().getName() + " - " + statusDetail.getGeneratedBy().getCode());
				aRow.createCell(count++)
						.setCellValue(statusDetail.getActionComment() != null ? statusDetail.getActionComment() : "-");
				aRow.createCell(count++).setCellValue(AppUtils.getBillPrintFormat(statusDetail.getActionTime()));
			} else if (statusDetail.getToStatus().equals(VoucherStatus.FINANCE_PENDING)
					|| statusDetail.getToStatus().equals(VoucherStatus.PENDING_REJECTION_AM)) {

				aRow.createCell(count++).setCellValue(
						statusDetail.getGeneratedBy().getName() + " - " + statusDetail.getGeneratedBy().getCode());
				aRow.createCell(count++)
						.setCellValue(statusDetail.getActionComment() != null ? statusDetail.getActionComment() : "-");
				aRow.createCell(count++).setCellValue(AppUtils.getBillPrintFormat(statusDetail.getActionTime()));
				if (statusDetail.getToStatus().equals(VoucherStatus.FINANCE_PENDING)) {
					aRow.createCell(count++).setCellValue(VoucherStatus.APPROVED.name());
				} else {
					aRow.createCell(count++).setCellValue(VoucherStatus.REJECTED.name());
				}
			} else if (statusDetail.getToStatus().equals(VoucherStatus.APPROVED)
					|| statusDetail.getToStatus().equals(VoucherStatus.PENDING_REJECTION_FINANCE)) {
				aRow.createCell(count++).setCellValue(
						statusDetail.getGeneratedBy().getName() + " - " + statusDetail.getGeneratedBy().getCode());
				aRow.createCell(count++)
						.setCellValue(statusDetail.getActionComment() != null ? statusDetail.getActionComment() : "-");
				aRow.createCell(count++).setCellValue(AppUtils.getBillPrintFormat(statusDetail.getActionTime()));
				if (statusDetail.getToStatus().equals(VoucherStatus.APPROVED)) {
					aRow.createCell(count++).setCellValue(VoucherStatus.APPROVED.name());
				} else {
					aRow.createCell(count++).setCellValue(VoucherStatus.REJECTED.name());
				}
				if (statusDetail.getToStatus().equals(VoucherStatus.APPROVED)) {
					aRow.createCell(count++).setCellValue(voucher.getIsReimbursed() == Boolean.TRUE ? "YES" : "NO");
				} else {
					aRow.createCell(count++).setCellValue("-");
				}
			}
		}

	}

}
