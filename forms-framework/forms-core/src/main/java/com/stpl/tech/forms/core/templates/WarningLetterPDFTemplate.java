package com.stpl.tech.forms.core.templates;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.forms.domain.model.Audit;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import com.stpl.tech.warning.domain.model.EmployeeWarning;

public class WarningLetterPDFTemplate extends AbstractVelocityTemplate {

	private String basePath;

	private EmployeeWarning warning;
	
	private EmployeeBasicDetail guiltyDetail;
	
	private Audit audit;

	@Override
	public String getTemplatePath() {
		return "templates/WarningLetterPDFTemplate.html";
	}

	public WarningLetterPDFTemplate(String basePath, EmployeeWarning warning, EmployeeBasicDetail employeeBasicDetail, Audit audit) {
		super();
		this.basePath = basePath;
		this.warning = warning;
		this.guiltyDetail = employeeBasicDetail;
		this.audit = audit;
	}

	@Override
	public String getFilepath() {
		return basePath + "/warningLetter/" + warning.getGuiltyPerson().getName() + "_" + warning.getId() + ".html";
	}

	@Override
	public Map<String, Object> getData() {
		Map<String, Object> stringObjectMap = new HashMap<>();
		stringObjectMap.put("warning", warning);
		stringObjectMap.put("guiltyDetail", guiltyDetail);
		stringObjectMap.put("auditDetail", audit);
		return stringObjectMap;
	}

}
