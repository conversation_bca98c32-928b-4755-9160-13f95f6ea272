package com.stpl.tech.forms.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "WALLET_TRANSACTION_DATA")
public class WalletTransactionData {
	private Integer id;
	private WalletData walletData;
	private String entityType;
	private Integer entityId;
	private String transactionType;
	private BigDecimal transactionAmount;
	private String transactionCode;
	private String transactionCodeType;
	private String transactionStatus;
	private Date transactionTime;
	private Integer createdBy;
	private String entity;
	private String isForced;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID", nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@ManyToOne
	@JoinColumn(name = "WALLET_ID")
	public WalletData getWalletData() {
		return walletData;
	}

	public void setWalletData(WalletData walletData) {
		this.walletData = walletData;
	}

	@Column(name = "ENTITY_TYPE", nullable = false)
	public String getEntityType() {
		return entityType;
	}

	public void setEntityType(String entityType) {
		this.entityType = entityType;
	}
	@Column(name = "ENTITY_ID", nullable = false)
	public Integer getEntityId() {
		return entityId;
	}

	public void setEntityId(Integer entityId) {
		this.entityId = entityId;
	}

	@Column(name = "TRANSACTION_TYPE", nullable = false)
	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	@Column(name = "TRANSACTION_AMOUNT", nullable = false)
	public BigDecimal getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(BigDecimal transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	@Column(name = "TRANSACTION_CODE", nullable = false)
	public String getTransactionCode() {
		return transactionCode;
	}

	public void setTransactionCode(String transactionCode) {
		this.transactionCode = transactionCode;
	}

	@Column(name = "TRANSACTION_CODE_TYPE", nullable = false)
	public String getTransactionCodeType() {
		return transactionCodeType;
	}

	public void setTransactionCodeType(String transactionCodeType) {
		this.transactionCodeType = transactionCodeType;
	}

	@Column(name = "TRANSACTION_STATUS", nullable = false)
	public String getTransactionStatus() {
		return transactionStatus;
	}

	public void setTransactionStatus(String transactionStatus) {
		this.transactionStatus = transactionStatus;
	}

	@Column(name = "TRANSACTION_TIME", nullable = true)
	public Date getTransactionTime() {
		return transactionTime;
	}

	public void setTransactionTime(Date transactionTime) {
		this.transactionTime = transactionTime;
	}

	@Column(name = "CREATED_BY", nullable = false)
	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	@Column(name = "ENTITY", nullable = false)
	public String getEntity() {
		return entity;
	}

	public void setEntity(String entity) {
		this.entity = entity;
	}

	@Column(name = "IS_FORCED", nullable = true)
	public String getIsForced() {
		return isForced;
	}

	public void setIsForced(String isForced) {
		this.isForced = isForced;
	}
	
	

}
