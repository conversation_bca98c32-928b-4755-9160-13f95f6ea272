package com.stpl.tech.forms.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "AUDIT_DETAIL")
public class AuditDetailData {

    private int id;
    private AuditFormData auditFormData;
    private Integer auditorId;
    private String auditorName;
    private Integer auditUnitId;
    private String auditUnitName;
    private Date auditDate;
    private Date auditTime;
    private Date auditSubmitDate;
    private Integer cafeManagerId;
    private String cafeManager;
    private Integer managerOnDutyId;
    private String managerOnDuty;
    private Integer areaManagerId;
    private String areaManager;
    private String auditType;
    private BigDecimal totalScore;
    private BigDecimal acquiredScore;
    private BigDecimal projectedTotalScore;
    private BigDecimal projectedAcquiredScore;
    private String status;
    private Integer auditReportId;
    private List<AuditDetailValuesData> auditDetailValuesDataList = new ArrayList<>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AUDIT_FORM_ID", nullable = false)
    public AuditFormData getAuditFormData() {
        return auditFormData;
    }

    public void setAuditFormData(AuditFormData auditFormData) {
        this.auditFormData = auditFormData;
    }

    @Column(name = "AUDITOR_ID", nullable = false)
    public Integer getAuditorId() {
        return auditorId;
    }

    public void setAuditorId(Integer auditorId) {
        this.auditorId = auditorId;
    }

    @Column(name = "AUDITOR_NAME", nullable = false)
    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    @Column(name = "AUDIT_UNIT_ID", nullable = false)
    public Integer getAuditUnitId() {
        return auditUnitId;
    }

    public void setAuditUnitId(Integer auditUnitId) {
        this.auditUnitId = auditUnitId;
    }

    @Column(name = "AUDIT_UNIT_NAME", nullable = false)
    public String getAuditUnitName() {
        return auditUnitName;
    }

    public void setAuditUnitName(String auditUnitName) {
        this.auditUnitName = auditUnitName;
    }

    @Column(name = "AUDIT_DATE", nullable = false)
    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    @Column(name = "AUDIT_TIME", nullable = false)
    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    @Column(name = "AUDIT_SUBMIT_DATE", nullable = false)
    public Date getAuditSubmitDate() {
        return auditSubmitDate;
    }

    public void setAuditSubmitDate(Date auditSubmitDate) {
        this.auditSubmitDate = auditSubmitDate;
    }

    @Column(name = "CAFE_MANAGER_ID", nullable = false)
    public Integer getCafeManagerId() {
        return cafeManagerId;
    }

    public void setCafeManagerId(Integer cafeManagerId) {
        this.cafeManagerId = cafeManagerId;
    }

    @Column(name = "CAFE_MANAGER", nullable = false)
    public String getCafeManager() {
        return cafeManager;
    }

    public void setCafeManager(String cafeManager) {
        this.cafeManager = cafeManager;
    }

    @Column(name = "MANAGER_ON_DUTY_ID", nullable = false)
    public Integer getManagerOnDutyId() {
        return managerOnDutyId;
    }

    public void setManagerOnDutyId(Integer managerOnDutyId) {
        this.managerOnDutyId = managerOnDutyId;
    }

    @Column(name = "MANAGER_ON_DUTY", nullable = false)
    public String getManagerOnDuty() {
        return managerOnDuty;
    }

    public void setManagerOnDuty(String managerOnDuty) {
        this.managerOnDuty = managerOnDuty;
    }

    @Column(name = "AREA_MANAGER_ID", nullable = false)
    public Integer getAreaManagerId() {
        return areaManagerId;
    }

    public void setAreaManagerId(Integer areaManagerId) {
        this.areaManagerId = areaManagerId;
    }

    @Column(name = "AREA_MANAGER", nullable = false)
    public String getAreaManager() {
        return areaManager;
    }

    public void setAreaManager(String areaManager) {
        this.areaManager = areaManager;
    }

    @Column(name = "AUDIT_TYPE", nullable = false)
    public String getAuditType() {
        return auditType;
    }

    public void setAuditType(String auditType) {
        this.auditType = auditType;
    }


    @Column(name = "TOTAL_SCORE", nullable = false)
    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    @Column(name = "ACQUIRED_SCORE", nullable = false)
    public BigDecimal getAcquiredScore() {
        return acquiredScore;
    }

    public void setAcquiredScore(BigDecimal acquiredScore) {
        this.acquiredScore = acquiredScore;
    }

    @Column(name = "PROJECTED_TOTAL_SCORE", nullable = false)
    public BigDecimal getProjectedTotalScore() {
        return projectedTotalScore;
    }

    public void setProjectedTotalScore(BigDecimal projectedTotalScore) {
        this.projectedTotalScore = projectedTotalScore;
    }

    @Column(name = "PROJECTED_ACQUIRED_SCORE", nullable = false)
    public BigDecimal getProjectedAcquiredScore() {
        return projectedAcquiredScore;
    }

    public void setProjectedAcquiredScore(BigDecimal projectedAcquiredScore) {
        this.projectedAcquiredScore = projectedAcquiredScore;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "AUDIT_REPORT_ID", nullable = true)
    public Integer getAuditReportId() {
        return auditReportId;
    }

    public void setAuditReportId(Integer auditReportId) {
        this.auditReportId = auditReportId;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "auditDetailData")
    public List<AuditDetailValuesData> getAuditDetailValuesDataList() {
        return auditDetailValuesDataList;
    }

    public void setAuditDetailValuesDataList(List<AuditDetailValuesData> auditDetailValuesDataList) {
        this.auditDetailValuesDataList = auditDetailValuesDataList;
    }
}
