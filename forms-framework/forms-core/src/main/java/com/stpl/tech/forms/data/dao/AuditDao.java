package com.stpl.tech.forms.data.dao;

import com.stpl.tech.forms.data.model.AuditDetailData;

import java.util.Date;
import java.util.List;

public interface AuditDao extends AbstractDao {

    public List<AuditDetailData> searchAudit(Integer unitId, Date start, Date end, Integer auditFormId);

    public List<AuditDetailData> getPreviousAudits(Integer auditId, Integer auditFormId, Integer unitId, Integer limit);
}
