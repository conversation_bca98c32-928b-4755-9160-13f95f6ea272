/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.forms.core.service;

import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EnvProperties {

    @Autowired
    Environment env;

    public String getBasePath() {
        return env.getProperty("server.base.dir");
    }

    public EnvType getEnvType() {
        return EnvType.valueOf(env.getProperty("environment.type"));
    }

    public String getKettleServiceBasePath() {
        return env.getProperty("base.path.kettle.service");
    }

    public String getMasterServiceBasePath() {
        return env.getProperty("base.path.master.service");
    }

    public String getS3Bucket() {
        return env.getProperty("amazon.s3.bucket", "chaayosdevtest");
    }

    public String getS3AuditBucket() {
        return env.getProperty("amazon.s3.audit.bucket", "chaayosdevtest");
    }
    
    public boolean getSendWalletOTPSMS() {
		return Boolean.valueOf(env.getProperty("send.wallet.otp.sms", "false"));
	}

    public int getBatchCounterResetLimit() {
        return Integer.valueOf(env.getProperty("batch.counter.reset.limit"));
    }

    public BigDecimal getFinanceAutoApproveLimit() {
        return BigDecimal.valueOf(Integer.valueOf(env.getProperty("voucher.auto.approve.amount", "250")));
    }

    public boolean getSendOTPLastFourDigits() {
        return Boolean.valueOf(env.getProperty("send.otp.last.four.digit", "false"));
    }

    public Integer getAutoSettleDays() {
        return Integer.valueOf(env.getProperty("auto-settle-payment-days", "0"));
    }

    public Integer getAutoClaimInitiateInDays() {
        return Integer.valueOf(env.getProperty("auto.claim.initiate.days", "0"));
    }

    public Integer getMinimumAutoInitiateAmount() {
        return Integer.valueOf(env.getProperty("auto.claim.initiate.minimum.amount", "1000"));
    }

    public Integer getRefundExpenseMetadataId() {
        return Integer.valueOf(env.getProperty("refund.expense.metadata.id", "86"));
    }
}
