package com.stpl.tech.forms.core.notification.email;

import java.util.HashSet;
import java.util.Set;

import com.stpl.tech.forms.core.notification.email.template.WarningLetterNotificationTemplate;
import com.stpl.tech.forms.core.util.AuditUtil;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class WarningLetterReportNotifiaction extends EmailNotification {

	private WarningLetterNotificationTemplate letterNotificationTemplate;
	private EnvType envType;
	private String subjectOfEmail;

	public WarningLetterReportNotifiaction() {
	}

	public WarningLetterReportNotifiaction(WarningLetterNotificationTemplate letterNotificationTemplate,
			EnvType envType) {
		this.letterNotificationTemplate = letterNotificationTemplate;
		this.envType = envType;
		// this.emails = emails;
	}

	@Override
	public String[] getToEmails() {
		if (AuditUtil.isDev(getEnvironmentType())) {
			return new String[] { "<EMAIL>", "<EMAIL>" , "<EMAIL>" };
		} else {
			Set<String> mails = new HashSet<>();
			if (letterNotificationTemplate.getEmails() != null && letterNotificationTemplate.getEmails().size() > 0) {
				mails.addAll(letterNotificationTemplate.getEmails());
			}
			String[] simpleArray = new String[mails.size()];
			return mails.toArray(simpleArray);
		}
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		subjectOfEmail = String.format("Warning Letter Details for %s - %s of cafe  %s on %s having status %s",
				letterNotificationTemplate.getWarning().getGuiltyPerson().getName(),
				letterNotificationTemplate.getGuiltyDetail().getEmployeeCode(),
				letterNotificationTemplate.getWarning().getUnit().getName(),
				AuditUtil.getFormattedTime(letterNotificationTemplate.getWarning().getUpdateTime(), "EEE dd MMM yyyy"),
				letterNotificationTemplate.getWarning().getWarningStage());
		if (AuditUtil.isDev(getEnvironmentType())) {
			subjectOfEmail = " [DEV] : " + subjectOfEmail;
		}
		return subjectOfEmail;
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return letterNotificationTemplate.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}
}
