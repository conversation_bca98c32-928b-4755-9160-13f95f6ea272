package com.stpl.tech.forms.core.service;

import com.stpl.tech.expense.domain.model.Claim;
import com.stpl.tech.expense.domain.model.ClaimFindVO;
import com.stpl.tech.expense.domain.model.ClaimLog;
import com.stpl.tech.expense.domain.model.ClaimRequestVO;
import com.stpl.tech.expense.domain.model.ClaimStatus;
import com.stpl.tech.expense.domain.model.ClaimType;
import com.stpl.tech.expense.domain.model.OrderRefundClaimVo;
import com.stpl.tech.expense.domain.model.OrderRefundVoucher;
import com.stpl.tech.forms.core.exception.WalletException;
import com.stpl.tech.forms.domain.model.Voucher;
import com.stpl.tech.kettle.data.model.OrderRefundDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.util.Date;
import java.util.List;

public interface ClaimManagementService {

    List<Claim> findClaims(Date startDate, Date endDate, ClaimStatus status, ClaimType type, List<String> unitIds, Integer walletId, Integer employeeId);

    Integer addClaim(ClaimRequestVO request) throws WalletException;

    List<ClaimLog> getClaimLogs(Integer claimId);

    List<Voucher> getClaimVouchers(Integer claimId);

    boolean setHappayIdToClaim(String happayId, Integer claimId, Integer userId) throws WalletException;

    public boolean approveRejectClaim(ClaimRequestVO request, boolean isApprove) throws WalletException, EmailGenerationException;

    boolean acknowledgeClaim(ClaimRequestVO request) throws WalletException;
    void autoAcknowledgeClaims() throws WalletException, EmailGenerationException;

    void downloadClaimDetail(HttpServletResponse response, Integer claimId) throws WalletException, TemplateRenderingException, FileNotFoundException;

    View bulkDownload(ClaimFindVO response);

    View downloadById(Integer claimId);

    View downloadTemplate();

    boolean uploadApprovals(MultipartFile file, Integer requestBy)throws Exception;

    boolean serviceChargeRefundVoucher(OrderRefundClaimVo orderRefundIds) throws WalletException, EmailGenerationException, DataNotFoundException;

    OrderRefundDetail createServiceCharge(OrderRefundVoucher orderRefundVoucher) throws DataNotFoundException;

    void sendMailForInitiateClaim();

    void autoInitiateClaims();
}
