package com.stpl.tech.forms.core.notification.email.template;

import com.stpl.tech.forms.domain.model.Voucher;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AutoInitiateClaimInfoTemplate extends AbstractVelocityTemplate {
    private final List<Voucher> vouchers;
    private final Integer autoInitiateInDays;

    public AutoInitiateClaimInfoTemplate(List<Voucher> vouchers, Integer autoInitiateInDays) {
        this.vouchers = vouchers;
        this.autoInitiateInDays = autoInitiateInDays;
    }

    @Override
    public String getTemplatePath() {
        return "templates/AutoInitiateClaimTemplate.html";
    }

    @Override
    public String getFilepath() {
        return "/data/app/kettle/stage" + "/autoInitialClaimTemplate/" + 1 + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("vouchers", vouchers);
        stringObjectMap.put("autoInitiateInDays", autoInitiateInDays);
        return stringObjectMap;
    }
}
