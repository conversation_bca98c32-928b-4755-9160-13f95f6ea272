package com.stpl.tech.forms.data;

import com.stpl.tech.forms.core.FormsServiceConstants;
import com.stpl.tech.forms.core.util.AuditUtil;
import com.stpl.tech.forms.data.model.AuditDetailData;
import com.stpl.tech.forms.data.model.AuditDetailValuesData;
import com.stpl.tech.forms.data.model.AuditFormData;
import com.stpl.tech.forms.data.model.AuditFormValuesData;
import com.stpl.tech.forms.data.model.DocumentDetailData;
import com.stpl.tech.forms.domain.model.Audit;
import com.stpl.tech.forms.domain.model.AuditForm;
import com.stpl.tech.forms.domain.model.AuditFormValues;
import com.stpl.tech.forms.domain.model.AuditType;
import com.stpl.tech.forms.domain.model.AuditValues;
import com.stpl.tech.forms.domain.model.DocUploadType;
import com.stpl.tech.forms.domain.model.DocumentDetail;
import com.stpl.tech.forms.domain.model.EntityType;
import com.stpl.tech.forms.domain.model.FileType;
import com.stpl.tech.forms.domain.model.IdCodeName;
import com.stpl.tech.forms.domain.model.MimeType;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.warning.data.model.EmployeeWarningDetail;
import com.stpl.tech.warning.data.model.EmployeeWarningReasonDetail;
import com.stpl.tech.warning.data.model.EmployeeWarningStatusDetail;
import com.stpl.tech.warning.data.model.WarningImageDetailData;
import com.stpl.tech.warning.domain.model.EmployeeWarning;
import com.stpl.tech.warning.domain.model.EmployeeWarningReason;
import com.stpl.tech.warning.domain.model.EmployeeWarningStatus;
import com.stpl.tech.warning.domain.model.ReasonOwner;
import com.stpl.tech.warning.domain.model.WarningImageDetail;
import com.stpl.tech.warning.domain.model.WarningImpactType;
import com.stpl.tech.warning.domain.model.WarningStatus;

import java.sql.Time;
import java.util.HashMap;
import java.util.Map;

public class AuditDataConverter {

    public static AuditForm convert(AuditFormData auditFormData, String createdBy, String lastUpdatedBy, boolean full) {
        AuditForm auditForm = new AuditForm();
        auditForm.setId(auditFormData.getId());
        auditForm.setCreatedBy(AuditUtil.getIdCodeName(auditFormData.getCreatedBy(), "", createdBy));
        auditForm.setCreationTime(auditFormData.getCreationTime());
        auditForm.setDescription(auditFormData.getDescription());
        auditForm.setLastUpdatedBy(AuditUtil.getIdCodeName(auditFormData.getCreatedBy(), "", lastUpdatedBy));
        auditForm.setLastUpdateTime(auditFormData.getLastUpdateTime());
        auditForm.setName(auditFormData.getName());
        auditForm.setNotificationEmail(auditFormData.getNotificationEmails());
        auditForm.setNotificationSlackChannels(auditFormData.getNotificationSlackChannels());
        auditForm.setStatus(auditFormData.getStatus());
        auditForm.setAskWarningLetter(AuditUtil.getStatus(auditFormData.getAskWarningLetter()));
        auditForm.setSupportTrial(AuditUtil.getStatus(auditFormData.getSupportTrial()));
        auditForm.setShowPastStats(AuditUtil.getStatus(auditFormData.getShowPastStats()));
        if (full) {
            Map<Integer, AuditFormValuesData> auditFormValuesDataMap = new HashMap<>();
            auditFormData.getValues().stream().forEach(auditFormValuesData -> {
                auditFormValuesDataMap.put(auditFormValuesData.getId(), auditFormValuesData);
            });
            auditFormData.getValues().stream().forEach(auditFormValuesData -> {
                if (auditFormValuesData.getStatus().equals(FormsServiceConstants.ACTIVE)) {
                    auditForm.getValues().add(AuditDataConverter.convert(auditFormValuesData, auditFormValuesDataMap));
                }
            });
        }
        return auditForm;
    }

    public static AuditFormValues convert(AuditFormValuesData auditFormValuesData, Map<Integer, AuditFormValuesData> auditFormValuesDataMap) {
        AuditFormValues auditFormValues = new AuditFormValues();
        if (auditFormValuesData != null) {
            auditFormValues.setAdditionalComment(AuditUtil.getStatus(auditFormValuesData.getAdditionalComment()));
            auditFormValues.setAnsweredBy(auditFormValuesData.getAnsweredBy());
            if (auditFormValuesData.getAppearanceOrder() != null) {
                auditFormValues.setAppearanceOrder(auditFormValuesData.getAppearanceOrder());
            }
            if (auditFormValuesData.getEntityDescription() != null) {
                auditFormValues.setEntityDescription(auditFormValuesData.getEntityDescription());
            }
            if (auditFormValuesData.getEntityLabel() != null) {
                auditFormValues.setEntityLabel(auditFormValuesData.getEntityLabel());
            }
            if (auditFormValuesData.getEntityMetadata() != null) {
                auditFormValues.setEntityMetadata(auditFormValuesData.getEntityMetadata());
            }
            auditFormValues.setEntityType(EntityType.fromValue(auditFormValuesData.getEntityType()));
            if (auditFormValuesData.getEntityValues() != null) {
                auditFormValues.setEntityValues(auditFormValuesData.getEntityValues());
            }
            auditFormValues.setId(auditFormValuesData.getId());
            if (auditFormValuesData.getLinkedApi() != null) {
                auditFormValues.setLinkedApi(auditFormValuesData.getLinkedApi());
            }
            if (auditFormValuesData.getLinkedDataType() != null) {
                auditFormValues.setLinkedDataType(auditFormValuesData.getLinkedDataType());
            }
            if (auditFormValuesData.getLinkedEntityId() != null) {
                auditFormValues.setLinkedEntity(convert(auditFormValuesDataMap.get(auditFormValuesData.getLinkedEntityId()), auditFormValuesDataMap));
            }
            if (auditFormValuesData.getMaxScore() != null) {
                auditFormValues.setMaxScore(auditFormValuesData.getMaxScore());
            }
            auditFormValues.setId(auditFormValuesData.getId());
            auditFormValues.setScoreCounted(AuditUtil.getStatus(auditFormValuesData.getScoreCounted()));
            auditFormValues.setStatus(auditFormValuesData.getStatus());
            if (auditFormValuesData.getAttachDoc() != null) {
                auditFormValues.setAttachDoc(AuditUtil.getStatus(auditFormValuesData.getAttachDoc()));
            }
            auditFormValues.setQuestionOptional(AuditUtil.getStatus(auditFormValuesData.getQuestionOptional()));
            auditFormValues.setIsMandatory(AuditUtil.getStatus(auditFormValuesData.getIsMandatory()));
            auditFormValues.setShowAnswerScore(AuditUtil.getStatus(auditFormValuesData.getShowAnswerScore()));
            auditFormValues.setShowMaxScore(AuditUtil.getStatus(auditFormValuesData.getShowMaxScore()));
        }
        return auditFormValues;
    }

    public static Audit convert(AuditDetailData auditDetailData, boolean full) {
        Audit audit = new Audit();
        audit.setAreaManager(new IdCodeName(auditDetailData.getAreaManagerId(), auditDetailData.getAreaManager()));
        audit.setAuditDate(auditDetailData.getAuditDate());
        audit.setAuditForm(new IdCodeName(auditDetailData.getAuditFormData().getId(), auditDetailData.getAuditFormData().getName()));
        audit.setAuditor(new IdCodeName(auditDetailData.getAuditorId(), auditDetailData.getAuditorName()));
        audit.setAuditSubmissionDate(auditDetailData.getAuditSubmitDate());
        audit.setAuditTime(auditDetailData.getAuditTime());
        audit.setAuditType(AuditType.fromValue(auditDetailData.getAuditType()));
        audit.setAuditUnit(new IdCodeName(auditDetailData.getAuditUnitId(), auditDetailData.getAuditUnitName()));
        audit.setCafeManager(new IdCodeName(auditDetailData.getCafeManagerId(), auditDetailData.getCafeManager()));
        audit.setId(auditDetailData.getId());
        audit.setManagerOnDuty(new IdCodeName(auditDetailData.getManagerOnDutyId(), auditDetailData.getManagerOnDuty()));
        audit.setTotalScore(auditDetailData.getTotalScore());
        if (AppUtils.getDaysDiff(audit.getAuditSubmissionDate(), AppUtils.getCurrentTimestamp()) < 2) {
            audit.setCancel(true);
        } else {
            audit.setCancel(false);
        }
        audit.setAcquiredScore(auditDetailData.getAcquiredScore());
        if (auditDetailData.getProjectedAcquiredScore() != null) {
            audit.setProjectedAcquiredScore(auditDetailData.getProjectedAcquiredScore());
        }
        if (auditDetailData.getProjectedTotalScore() != null) {
            audit.setProjectedTotalScore(auditDetailData.getProjectedTotalScore());
        }
        if (full) {
            for (AuditDetailValuesData auditDetailValuesData : auditDetailData.getAuditDetailValuesDataList()) {
                audit.getValues().add(convert(auditDetailValuesData));
            }
        }
        return audit;
    }

    public static AuditValues convert(AuditDetailValuesData auditDetailValuesData) {
        AuditValues auditValues = new AuditValues();
        auditValues.setAcquiredScore(auditDetailValuesData.getAcquiredScore());
        if (auditDetailValuesData.getAnswerComment() != null) {
            auditValues.setAnswerComment(auditDetailValuesData.getAnswerComment());
        }
        if (auditDetailValuesData.getAttachedDocId() != null) {
            DocumentDetail dd = new DocumentDetail();
            dd.setDocumentId(auditDetailValuesData.getAttachedDocId());
            auditValues.setAttachedDoc(dd);
        }
        Map<Integer, AuditFormValuesData> auditFormValuesDataMap = new HashMap<>();
        auditValues.setAuditFormValue(convert(auditDetailValuesData.getAuditFormValuesData(), auditFormValuesDataMap));
        if (auditDetailValuesData.getDateValue() != null) {
            auditValues.setDateValue(auditDetailValuesData.getDateValue());
        }
        if (auditDetailValuesData.getEmployeeDesignation() != null) {
            auditValues.setEmployeeDesignation(auditDetailValuesData.getEmployeeDesignation());
        }
        if (auditDetailValuesData.getEmployeeId() != null) {
            auditValues.setEmployeeId(auditDetailValuesData.getEmployeeId());
        }
        if (auditDetailValuesData.getEmployeeName() != null) {
            auditValues.setEmployeeName(auditDetailValuesData.getEmployeeName());
        }
        if (auditDetailValuesData.getId() != null) {
            auditValues.setId(auditDetailValuesData.getId());
        }
        if (auditDetailValuesData.getMaxScore() != null) {
            auditValues.setMaxScore(auditDetailValuesData.getMaxScore());
        }
        if (auditDetailValuesData.getNumberValue() != null) {
            auditValues.setNumberValue(auditDetailValuesData.getNumberValue());
        }
        if (auditDetailValuesData.getOption1() != null) {
            auditValues.setOption1(auditDetailValuesData.getOption1());
        }
        if (auditDetailValuesData.getOption1Score() != null) {
            auditValues.setOption1Marks(auditDetailValuesData.getOption1Score());
        }
        if (auditDetailValuesData.getOption2() != null) {
            auditValues.setOption2(auditDetailValuesData.getOption2());
        }
        if (auditDetailValuesData.getOption2Score() != null) {
            auditValues.setOption2Marks(auditDetailValuesData.getOption2Score());
        }
        if (auditDetailValuesData.getOption3() != null) {
            auditValues.setOption3(auditDetailValuesData.getOption3());
        }
        if (auditDetailValuesData.getOption3Score() != null) {
            auditValues.setOption3Marks(auditDetailValuesData.getOption3Score());
        }
        if (auditDetailValuesData.getOption4() != null) {
            auditValues.setOption4(auditDetailValuesData.getOption4());
        }
        if (auditDetailValuesData.getOption4Score() != null) {
            auditValues.setOption4Marks(auditDetailValuesData.getOption4Score());
        }
        if (auditDetailValuesData.getOption5() != null) {
            auditValues.setOption5(auditDetailValuesData.getOption5());
        }
        if (auditDetailValuesData.getOption5Score() != null) {
            auditValues.setOption5Marks(auditDetailValuesData.getOption5Score());
        }
        if (auditDetailValuesData.getOption6() != null) {
            auditValues.setOption6(auditDetailValuesData.getOption6());
        }
        if (auditDetailValuesData.getOption6Score() != null) {
            auditValues.setOption6Marks(auditDetailValuesData.getOption6Score());
        }
        if (auditDetailValuesData.getOption7() != null) {
            auditValues.setOption7(auditDetailValuesData.getOption7());
        }
        if (auditDetailValuesData.getOption7Score() != null) {
            auditValues.setOption7Marks(auditDetailValuesData.getOption7Score());
        }
        if (auditDetailValuesData.getProductId() != null) {
            auditValues.setProductId(auditDetailValuesData.getProductId());
        }
        if (auditDetailValuesData.getProductName() != null) {
            auditValues.setProductName(auditDetailValuesData.getProductName());
        }
        if (auditDetailValuesData.getTextArea() != null) {
            auditValues.setTextArea(auditDetailValuesData.getTextArea());
        }
        if (auditDetailValuesData.getTextValue() != null) {
            auditValues.setTextValue(auditDetailValuesData.getTextValue());
        }
        if (auditDetailValuesData.getTimeValue() != null) {
            auditValues.setTimeValue(new Time(auditDetailValuesData.getTimeValue().getTime()));
        }
        if (auditDetailValuesData.getUnitId() != null) {
            auditValues.setUnitId(auditDetailValuesData.getUnitId());
        }
        if (auditDetailValuesData.getUnitName() != null) {
            auditValues.setUnitName(auditDetailValuesData.getUnitName());
        }
        if (auditDetailValuesData.getYesNo() != null) {
            auditValues.setYesNo(auditDetailValuesData.getYesNo());
        }
        if (auditDetailValuesData.getQuestionOpted() != null) {
            auditValues.setQuestionOpted(AuditUtil.getStatus(auditDetailValuesData.getQuestionOpted()));
        }
        return auditValues;
    }

    public static EmployeeWarning convert(EmployeeWarningDetail detail, MasterDataCache masterDataCache) {
        EmployeeWarning warning = new EmployeeWarning();
        if (detail.getPqscAuditId() != null) {
            warning.setAudit(new IdCodeName(detail.getPqscAuditId(), "", ""));
        }
        warning.setId(detail.getWarningId());
        warning.setGuiltyPerson(
                new IdCodeName(detail.getGulityPersonId(), detail.getGulityPersonDesg(), detail.getGulityPersonName()));
        warning.setImpactType(WarningImpactType.valueOf(detail.getImpactType()));
        warning.setManagerOnDuty(
                new IdCodeName(detail.getManagerOnDuty(), "", masterDataCache.getEmployee(detail.getManagerOnDuty())));
        warning.setUnit(
                new IdCodeName(detail.getUnitId(), "", masterDataCache.getUnit(detail.getUnitId()).getName()));
        warning.setWarningStage(detail.getWarningStage());
        warning.setWarningStatus(WarningStatus.valueOf(detail.getWarningStatus()));
        warning.setUpdateTime(detail.getCreationTime());
        warning.setAreaManager(
                new IdCodeName(detail.getAreaMangerId(), "", masterDataCache.getEmployee(detail.getAreaMangerId())));
        if (detail.getDateOfIncidence() != null) {
            warning.setDoi(detail.getDateOfIncidence());
        }
        return warning;
    }

    public static EmployeeWarningStatus convert(EmployeeWarningStatusDetail statusDetail,
                                                MasterDataCache masterDataCache) {
        EmployeeWarningStatus warningStatus = new EmployeeWarningStatus();
        warningStatus.setFromStatus(statusDetail.getFromStatus());
        warningStatus.setToStatus(statusDetail.getToStatus());
        warningStatus.setId(statusDetail.getStatusId());
        warningStatus.setWarningId(statusDetail.getWarningId());
        warningStatus.setComment(statusDetail.getComment());
        warningStatus.setActionTakenBy(statusDetail.getActionTakenBy());
        warningStatus.setAuthorisedPerson(new IdCodeName(statusDetail.getAuthorityId(), statusDetail.getAuthorityDesg(),
                statusDetail.getAuthorityName()));
        warningStatus.setActionTakenOn(statusDetail.getActionTakenOn());

        return warningStatus;
    }

    public static EmployeeWarningReason convert(EmployeeWarningReasonDetail reasonDetail,
                                                MasterDataCache masterDataCache) {
        EmployeeWarningReason reason = new EmployeeWarningReason();
        reason.setId(reasonDetail.getWarningReasonId());
        reason.setWarningId(reasonDetail.getWarningId());
        reason.setWarningStatusId(reasonDetail.getWarningStatusId());
        reason.setReasonId(reasonDetail.getReasonId());
        reason.setReasonName(reasonDetail.getReasonName());
        reason.setCategory(reasonDetail.getReasonCategory());
        reason.setReasonAddedBy(ReasonOwner.valueOf(reasonDetail.getReasonAddedBy()));
        reason.setStatus(reasonDetail.getStatus());
        reason.setUpdatedOn(reasonDetail.getLastUpdatedOn());
        return reason;
    }

    public static WarningImageDetail convert(WarningImageDetailData data) {
        WarningImageDetail detail = new WarningImageDetail();
        detail.setId(data.getImageId());
        detail.setImageName(data.getImageName());
        detail.setImageUrl(data.getImageUrl());
        detail.setWarningId(data.getWarningId());
        detail.setStatus(data.getStatus());
        detail.setCreatedOn(data.getCreatedOn());
        return detail;
    }

    public static DocumentDetail convert(DocumentDetailData detailData) {
        DocumentDetail detail = new DocumentDetail();
        detail.setDocumentId(detailData.getDocumentId());
        detail.setUpdatedBy(new IdCodeName(detailData.getUpdatedBy(), "", ""));
        detail.setDocumentLink(detailData.getDocumentLink());
        detail.setFileUrl(detailData.getFileUrl());
        detail.setUploadType(DocUploadType.valueOf(detailData.getDocumentUploadType()));
        detail.setUploadTypeId(0);
        detail.setFileType(FileType.valueOf(detailData.getFileType()));
        detail.setMimeType(MimeType.valueOf(detailData.getMimeType()));
        detail.setUpdateTime(detailData.getUpdateTime());
        detail.setS3Key(detailData.getS3Key());
        detail.setS3Bucket(detailData.getS3Bucket());
        return detail;
    }
}
