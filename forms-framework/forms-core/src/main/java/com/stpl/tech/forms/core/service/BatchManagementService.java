package com.stpl.tech.forms.core.service;

import com.stpl.tech.forms.core.domain.model.BatchCodeResponse;
import com.stpl.tech.master.core.exception.DataUpdationException;

public interface BatchManagementService {

    BatchCodeResponse generatePackCodes(int unitId, int product,String productName, int count, int length) throws DataUpdationException;

    String getCodePrefix(String keyId, String keyType);
}
