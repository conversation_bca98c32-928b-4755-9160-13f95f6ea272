package com.stpl.tech.forms.data.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@SuppressWarnings("serial")
@Entity
@Table(name = "BATCH_DATA", uniqueConstraints = @UniqueConstraint(columnNames = {"ID", "CODE_PREFIX"}))
public class BatchData implements java.io.Serializable {

    private Integer id;

    private String codePrefix;

    private Date manufactureDate;

    private Date expiryDate;

    private Integer createdBy;

    private Integer count;

    private Integer length;
    
    private Integer unitId;

    private String unitBatchCode;

    private String unitName;

    private Integer productId;

    private String productBatchCode;

    private String productName;

    private Date codeGenerationDate;

    private String dateBatchCode;

	private List<BatchCode> batchCodes = new ArrayList<BatchCode>(0);


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "CODE_PREFIX", unique = true, nullable = false)
    public String getCodePrefix() {
        return codePrefix;
    }

    public void setCodePrefix(String codePrefix) {
        this.codePrefix = codePrefix;
    }

    @Column(name = "MANUFACTURE_DATE", nullable = false)
    public Date getManufactureDate() {
        return manufactureDate;
    }

    public void setManufactureDate(Date manufactureDate) {
        this.manufactureDate = manufactureDate;
    }

    @Column(name = "EXPIRY_DATE", nullable = false)
    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "COUNT", nullable = false)
    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    @Column(name = "LENGTH", nullable = false)
    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_BATCH_CODE", nullable = false)
    public String getUnitBatchCode() {
        return unitBatchCode;
    }

    public void setUnitBatchCode(String unitBatchCode) {
        this.unitBatchCode = unitBatchCode;
    }

    @Column(name = "UNIT_NAME")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_BATCH_CODE", nullable = false)
    public String getProductBatchCode() {
        return productBatchCode;
    }

    public void setProductBatchCode(String productBatchCode) {
        this.productBatchCode = productBatchCode;
    }

    @Column(name = "PRODUCT_NAME")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "CODE_GENERATION_DATE")
	public Date getCodeGenerationDate() {
		return codeGenerationDate;
	}

	public void setCodeGenerationDate(Date generationDate) {
		this.codeGenerationDate = generationDate;
	}

    @Column(name = "DATE_BATCH_CODE")
	public String getDateBatchCode() {
		return dateBatchCode;
	}

	public void setDateBatchCode(String datePrefix) {
		this.dateBatchCode = datePrefix;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "batchData")
	public List<BatchCode> getBatchCodes() {
		return batchCodes;
	}

	public void setBatchCodes(List<BatchCode> batchCodes) {
		this.batchCodes = batchCodes;
	}
    
}
