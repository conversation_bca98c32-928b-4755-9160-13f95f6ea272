package com.stpl.tech.kettle.core.notification.sms;

import com.stpl.tech.master.core.notification.sms.Messenger;
import com.stpl.tech.master.core.notification.sms.SMSType;
import com.stpl.tech.master.domain.model.UnitBasicDetail;

public enum WalletSMSNotificationType {
	WALLET_AUTHENTICATION_MESSAGE(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String customerName = "Chai Lover";
			String message = String.format("Hi %s,\n" +
					"OTP for Chaayos account login is %s. Download Chaayos app for hassle free ordering.", customerName,token);
//			String message = String.format("Your wallet authentication OTP is %s\nFrom Team Chaayos!", token);
			return message;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.OTP;
		}
	},false);

	private final Messenger<Object, String> template;
	private final boolean whatsapp;
	private WalletSMSNotificationType(Messenger<Object, String> template, boolean whatsapp) {
		this.whatsapp = whatsapp;
		this.template = template;
	}
	public boolean isWhatsapp() {
		return whatsapp;
	}

	public Messenger<Object, String> getTemplate() {
		return template;
	}

	public String getMessage(Object object) {
		return template.getMessage(object);
	}

	public String getMessage(Object object, UnitBasicDetail detail) {
		return template.getMessage(object);
	}
}
