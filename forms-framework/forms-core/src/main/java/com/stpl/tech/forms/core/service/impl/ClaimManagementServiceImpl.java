package com.stpl.tech.forms.core.service.impl;

import com.itextpdf.html2pdf.HtmlConverter;
import com.stpl.tech.expense.domain.model.Claim;
import com.stpl.tech.expense.domain.model.ClaimFindVO;
import com.stpl.tech.expense.domain.model.ClaimLog;
import com.stpl.tech.expense.domain.model.ClaimRequestVO;
import com.stpl.tech.expense.domain.model.ClaimStatus;
import com.stpl.tech.expense.domain.model.ClaimType;
import com.stpl.tech.expense.domain.model.OrderRefundClaimVo;
import com.stpl.tech.expense.domain.model.OrderRefundVoucher;
import com.stpl.tech.forms.core.FormsErrorConstants;
import com.stpl.tech.forms.core.exception.WalletException;
import com.stpl.tech.forms.core.notification.email.AutoClaimInitiateEmailNotification;
import com.stpl.tech.master.notification.EmailNotificationTemplate;
import com.stpl.tech.forms.core.notification.email.template.AutoInitiateClaimInfoTemplate;
import com.stpl.tech.forms.core.service.ClaimManagementService;
import com.stpl.tech.forms.core.service.EnvProperties;
import com.stpl.tech.forms.core.service.WalletService;
import com.stpl.tech.forms.core.templates.ClaimDetailPDFTemplate;
import com.stpl.tech.forms.core.util.AuditUtil;
import com.stpl.tech.forms.data.WalletDataConverter;
import com.stpl.tech.forms.data.dao.ClaimManagementDao;
import com.stpl.tech.forms.data.dao.WalletDao;
import com.stpl.tech.forms.data.model.ClaimDetailData;
import com.stpl.tech.forms.data.model.ClaimLogDetailData;
import com.stpl.tech.forms.data.model.WalletData;
import com.stpl.tech.forms.domain.model.ApprovalDataVO;
import com.stpl.tech.forms.domain.model.ClaimsDetailDataVO;
import com.stpl.tech.forms.domain.model.DownloadClaimsVO;
import com.stpl.tech.forms.domain.model.IdCodeName;
import com.stpl.tech.forms.domain.model.IssuingEntity;
import com.stpl.tech.forms.domain.model.Voucher;
import com.stpl.tech.forms.domain.model.VoucherCostCenterAllocation;
import com.stpl.tech.forms.domain.model.VoucherStatus;
import com.stpl.tech.forms.domain.model.Wallet;
import com.stpl.tech.forms.domain.model.WalletAccountType;
import com.stpl.tech.forms.domain.model.WalletBusinessCostCenterMappingVO;
import com.stpl.tech.forms.domain.model.WalletStatus;
import com.stpl.tech.forms.domain.model.WalletType;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderRefundDetail;
import com.stpl.tech.kettle.data.model.OrderRefundStatus;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.budget.metadata.model.ExpenseMetadata;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ClaimManagementServiceImpl implements ClaimManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(ClaimManagementServiceImpl.class);

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private EnvProperties envProperties;

    @Autowired
    private ClaimManagementDao claimManagementDao;

    @Autowired
    private WalletService walletService;

    @Autowired
    private WalletDao walletDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Claim> findClaims(Date startDate, Date endDate, ClaimStatus status, ClaimType type, List<String> unitIds, Integer walletId, Integer employeeId) {
        List<Claim> claims = new ArrayList<>();
        List<ClaimDetailData> claimDetailDataList = claimManagementDao.findClaims(startDate, endDate, status, type, unitIds, walletId, employeeId);
        for (ClaimDetailData claimDetailData : claimDetailDataList) {
            IdCodeName createdBy = new IdCodeName(claimDetailData.getCreatedBy(), "", masterDataCache.getEmployee(claimDetailData.getCreatedBy()));
            IdCodeName lastUpdatedBy = new IdCodeName(claimDetailData.getLastUpdatedBy(), "", masterDataCache.getEmployee(claimDetailData.getLastUpdatedBy()));
            IdCodeName employee = null;
            if (claimDetailData.getEmployeeId() != null) {
                employee = new IdCodeName(claimDetailData.getEmployeeId(), "", masterDataCache.getEmployee(claimDetailData.getEmployeeId()));
            }
            IdCodeName unit = null;
            if (claimDetailData.getUnitId() != null) {
                try {
                    unit = new IdCodeName(claimDetailData.getUnitId(), masterDataCache.getUnitBasicDetail(claimDetailData.getUnitId()).getEmail(),
                            masterDataCache.getUnitBasicDetail(claimDetailData.getUnitId()).getName());
                }catch (Exception e) {
                    LOG.info("Unable to fetch unit data");
                }
            }
            Claim claim = WalletDataConverter.convert(claimDetailData, createdBy, lastUpdatedBy, employee, unit);
            claims.add(claim);
        }
        return claims;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean serviceChargeRefundVoucher(OrderRefundClaimVo orderClaimRequestVo) throws WalletException, EmailGenerationException, DataNotFoundException {
        List<Voucher> voucherList = new ArrayList<>();
        for (OrderRefundVoucher orderRefundVoucher : orderClaimRequestVo.getOrderRefundVouchers()) {
            if(Objects.isNull(orderRefundVoucher.getCreatedBy())) {
                orderRefundVoucher.setCreatedBy(orderClaimRequestVo.getRequestedBy().getId());
            }
            OrderRefundDetail orderRefundDetail = createServiceCharge(orderRefundVoucher);
            if (Objects.nonNull(orderRefundDetail) && Objects.isNull(orderRefundDetail.getReferenceRefundId())) {
                WalletData walletData = walletDao.getWalletDataByType(WalletAccountType.UNIT.name(), orderRefundDetail.getUnitId());
                if (Objects.nonNull(walletData)) {
                    try {
                        String generatedId = walletService.issueVoucher(setServiceChargeRefundVoucher(orderRefundDetail, masterDataCache, walletData, orderClaimRequestVo));
                        VoucherData voucherData = walletDao.getVouchersByGeneratedId(generatedId);
                        if (Objects.nonNull(voucherData)) {
                            Voucher voucher = WalletDataConverter.convert(voucherData, walletData, true, false, null, masterDataCache, null);
                            voucher.setExpenseAmount(orderRefundDetail.getRefundAmount());
                            voucher.setVoucherDate(AppUtils.getCurrentTimestamp());
                            walletService.settleVoucher(voucher);
                            walletService.amApproveVoucher(voucher);
                            walletService.financeApproveVoucher(voucher);
                            orderRefundDetail.setReferenceRefundId(voucherData.getId());
                            orderRefundDetail.setRefundStatus(OrderRefundStatus.CREATED.name());
                            orderRefundDetail.setUpdatedTime(AppUtils.getCurrentTimestamp());
                            if(Objects.nonNull(orderRefundDetail.getOrderRefundDetailId())){
                                walletDao.update(orderRefundDetail, true);
                            } else {
                                walletDao.add(orderRefundDetail, true);
                            }
                            voucherList.add(voucher);
                        }
                    } catch (Exception e) {
                        LOG.error("Error while creating voucher for order id: {} ", orderRefundVoucher.getOrderId(), e);
                        throw new WalletException("Error creating refund voucher ::::::::::::::::::::::");
                    }
                } else {
                    LOG.info("Wallet not found for unit id: {}", orderRefundDetail.getUnitId());
                }
            }
        }
        if (Objects.nonNull(voucherList) && !voucherList.isEmpty()) {
            try {
                ClaimRequestVO claimRequestVO = setServiceChargeClaimRequest(voucherList);
                addClaim(claimRequestVO);
                return true;
            } catch (Exception e) {
                throw new WalletException("Error while creating refund claim ::::::::::::::::::::::");
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderRefundDetail createServiceCharge(OrderRefundVoucher orderRefundVoucher) throws DataNotFoundException {
        try {
            OrderDetail orderDetail = walletDao.find(OrderDetail.class, orderRefundVoucher.getOrderId());
            BigDecimal refundAmount = BigDecimal.ZERO;
            if (Objects.nonNull(orderDetail) && Objects.nonNull(orderDetail.getServiceCharge()) && Objects.nonNull(orderDetail.getServiceTaxAmount())) {
                refundAmount = AppUtils.add(orderDetail.getServiceTaxAmount(), orderDetail.getServiceCharge());
            }
            if (refundAmount.compareTo(BigDecimal.ZERO) > 0 && !orderDetail.getOrderStatus().equals(OrderStatus.CANCELLED)) {
                OrderRefundDetail orderRefundDetail = walletDao.getOrderRefundByOrderId(orderRefundVoucher.getOrderId());
                if (Objects.isNull(orderRefundDetail)) {
                    orderRefundVoucher.setRefundAmount(refundAmount.setScale(0, RoundingMode.UP));
                    orderRefundDetail = WalletDataConverter.convert(orderRefundVoucher, orderDetail);
                    orderRefundDetail.setRoundOffAmount(AppUtils.subtract(orderRefundDetail.getRefundAmount(), refundAmount));
                }
                return orderRefundDetail;
            } else {
                LOG.info("Order Detail not found for order id ::::::::: {}", orderRefundVoucher.getOrderId());
            }
        } catch (Exception e) {
            LOG.error("Error while creating service charge refund ::::::::: {}", e.getMessage());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer addClaim(ClaimRequestVO request) throws WalletException {
        if(ClaimType.HAPPAY.equals(request.getClaimType())) {
            return addHappayClaim(request);
        }
        if(ClaimType.WALLET_UPGRADE.equals(request.getClaimType())) {
            return addWalletUpgradeClaim(request);
        }
        if(ClaimType.WALLET_DOWNGRADE.equals(request.getClaimType())) {
            return addWalletDowngradeClaim(request);
        }
        if(ClaimType.ICICI.equals(request.getClaimType())){
            return addIciciClaim(request);
        }
        throw new WalletException("Invalid transaction. Please provide valid claim type.");
    }

    private Integer addHappayClaim(ClaimRequestVO request) throws WalletException {
        List<VoucherData> voucherDataList = walletService.getVoucherDataListByIdsAndAccountNo(request.getVoucherIds(), request.getAccountNo());
        Set<VoucherData> validVouchers = new HashSet<>();
        BigDecimal claimAmount = BigDecimal.ZERO;
        for (VoucherData voucherData : voucherDataList) {
            if (VoucherStatus.APPROVED.name().equals(voucherData.getCurrentStatus()) && voucherData.getClaimId() == null) {
                validVouchers.add(voucherData);
                claimAmount = AuditUtil.add(claimAmount, voucherData.getExpenseAmount());
            }
        }
        String[] wallet = request.getAccountNo().split("_");
        Integer unitId = Integer.parseInt(wallet[wallet.length-1]);
        if (validVouchers.size() > 0) {
            ClaimDetailData claimDetailData = new ClaimDetailData();
            claimDetailData.setWalletId(voucherDataList.get(0).getWalletId());
            claimDetailData.setClaimApprovedAmount(claimAmount);
            claimDetailData.setClaimCurrentStatus(ClaimStatus.CREATED.name());
            claimDetailData.setClaimRequestedAmount(claimAmount);
            claimDetailData.setClaimType(ClaimType.HAPPAY.name());
            claimDetailData.setCreatedBy(request.getRequestBy().getId());
            claimDetailData.setCreationTime(AuditUtil.getCurrentTimestamp());
            claimDetailData.setLastUpdatedBy(claimDetailData.getCreatedBy());
            claimDetailData.setLastUpdateTime(claimDetailData.getCreationTime());
            claimDetailData.setUnitId(unitId);
            claimDetailData = claimManagementDao.add(claimDetailData, true);
            if (claimDetailData != null) {
                addClaimLog(claimDetailData.getClaimId(), request.getComment(), ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus()),
                        ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus()), request.getRequestBy().getId());
                for (VoucherData voucherData : validVouchers) {
                    voucherData.setClaimId(claimDetailData.getClaimId());
                    voucherData = claimManagementDao.update(voucherData, true);
                    if (voucherData == null) {
                        throw new WalletException("Error updating claim id in vouchers!");
                    }
                }
            } else {
                throw new WalletException("Error creating claim detail!");
            }
            return claimDetailData.getClaimId();
        }
        return null;
    }

    private Integer addIciciClaim(ClaimRequestVO request) throws WalletException {
        List<VoucherData> voucherDataList = walletService.getVoucherDataListByIdsAndAccountNo(request.getVoucherIds(), request.getAccountNo());
        Set<VoucherData> validVouchers = new HashSet<>();
        BigDecimal claimAmount = BigDecimal.ZERO;
        for (VoucherData voucherData : voucherDataList) {
            if (VoucherStatus.APPROVED.name().equals(voucherData.getCurrentStatus()) && voucherData.getClaimId() == null) {
                validVouchers.add(voucherData);
                claimAmount = AuditUtil.add(claimAmount, voucherData.getExpenseAmount());
            }
        }
        String[] wallet = request.getAccountNo().split("_");
        Integer unitId = Integer.parseInt(wallet[wallet.length-1]);
        if (validVouchers.size() > 0) {
            ClaimDetailData claimDetailData = new ClaimDetailData();
            claimDetailData.setWalletId(voucherDataList.get(0).getWalletId());
            claimDetailData.setClaimApprovedAmount(claimAmount);
            claimDetailData.setClaimCurrentStatus(ClaimStatus.CREATED.name());
            claimDetailData.setClaimRequestedAmount(claimAmount);
            claimDetailData.setClaimType(ClaimType.ICICI.name());
            claimDetailData.setCreatedBy(request.getRequestBy().getId());
            claimDetailData.setCreationTime(AuditUtil.getCurrentTimestamp());
            claimDetailData.setLastUpdatedBy(claimDetailData.getCreatedBy());
            claimDetailData.setLastUpdateTime(claimDetailData.getCreationTime());
            claimDetailData.setUnitId(unitId);
            claimDetailData = claimManagementDao.add(claimDetailData, true);
            if (claimDetailData != null) {
                addClaimLog(claimDetailData.getClaimId(), request.getComment(), ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus()),
                        ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus()), request.getRequestBy().getId());
                for (VoucherData voucherData : validVouchers) {
                    voucherData.setClaimId(claimDetailData.getClaimId());
                    voucherData = claimManagementDao.update(voucherData, true);
                    if (voucherData == null) {
                        throw new WalletException("Error updating claim id in vouchers!");
                    }
                }
            } else {
                throw new WalletException("Error creating claim detail!");
            }
            return claimDetailData.getClaimId();
        }
        return null;
    }

    private Integer addWalletUpgradeClaim(ClaimRequestVO request) throws WalletException {
        if(request.getHappayId() == null) {
            throw new WalletException("Please fill transaction id.");
        }
        if(new BigDecimal(25000).compareTo(request.getClaimAmount()) < 0) {
            throw new WalletException("Maximum wallet upgrade limit in single transaction is 25000 only.");
        }
        if(new BigDecimal(0).compareTo(request.getClaimAmount()) == 0) {
            throw new WalletException("Please add wallet upgrade amount.");
        }
        Wallet wallet = walletService.getWalletDetails(WalletAccountType.UNIT.name(), WalletType.PETTY_CASH.name(),
                request.getAccountNo());
        if(wallet != null) {
            BigDecimal claimAmount = request.getClaimAmount();
            ClaimDetailData claimDetailData = new ClaimDetailData();
            claimDetailData.setWalletId(wallet.getId());
            claimDetailData.setClaimApprovedAmount(claimAmount);
            claimDetailData.setClaimCurrentStatus(ClaimStatus.SETTLED.name());
            claimDetailData.setClaimRequestedAmount(claimAmount);
            claimDetailData.setClaimType(ClaimType.WALLET_UPGRADE.name());
            claimDetailData.setCreatedBy(request.getRequestBy().getId());
            claimDetailData.setCreationTime(AuditUtil.getCurrentTimestamp());
            claimDetailData.setLastUpdatedBy(claimDetailData.getCreatedBy());
            claimDetailData.setLastUpdateTime(claimDetailData.getCreationTime());
            claimDetailData.setUnitId(wallet.getAssociatedId());
            claimDetailData.setHappayId(request.getHappayId());
            claimDetailData = claimManagementDao.add(claimDetailData, true);
            if (claimDetailData != null) {
                //update wallet limit
                walletService.upgradeWalletAmount(claimDetailData, request.getRequestBy().getId());
                addClaimLog(claimDetailData.getClaimId(), request.getComment(), ClaimStatus.CREATED,
                        ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus()), request.getRequestBy().getId());
            } else {
                throw new WalletException("Error creating claim detail!");
            }
            return claimDetailData.getClaimId();
        } else {
            throw new WalletException("Wallet not found.");
        }
    }

    private Integer addWalletDowngradeClaim(ClaimRequestVO request) throws WalletException {
        if(request.getHappayId() == null) {
            throw new WalletException("Please fill transaction id.");
        }
        if(new BigDecimal(0).compareTo(request.getClaimAmount()) == 0) {
            throw new WalletException("Please add wallet downgrade amount.");
        }
        Wallet wallet = walletService.getWalletDetails(WalletAccountType.UNIT.name(), WalletType.PETTY_CASH.name(),
                request.getAccountNo());
        if(wallet != null) {
            BigDecimal claimAmount = request.getClaimAmount();
            ClaimDetailData claimDetailData = new ClaimDetailData();
            claimDetailData.setWalletId(wallet.getId());
            claimDetailData.setClaimApprovedAmount(claimAmount);
            claimDetailData.setClaimCurrentStatus(ClaimStatus.SETTLED.name());
            claimDetailData.setClaimRequestedAmount(claimAmount);
            claimDetailData.setClaimType(ClaimType.WALLET_DOWNGRADE.name());
            claimDetailData.setCreatedBy(request.getRequestBy().getId());
            claimDetailData.setCreationTime(AuditUtil.getCurrentTimestamp());
            claimDetailData.setLastUpdatedBy(claimDetailData.getCreatedBy());
            claimDetailData.setLastUpdateTime(claimDetailData.getCreationTime());
            claimDetailData.setUnitId(wallet.getAssociatedId());
            claimDetailData.setHappayId(request.getHappayId());
            claimDetailData = claimManagementDao.add(claimDetailData, true);
            if (claimDetailData != null) {
                //update wallet limit
                walletService.downgradeWalletAmount(claimDetailData, request.getRequestBy().getId());
                addClaimLog(claimDetailData.getClaimId(), request.getComment(), ClaimStatus.CREATED,
                        ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus()), request.getRequestBy().getId());
            } else {
                throw new WalletException("Error creating claim detail!");
            }
            return claimDetailData.getClaimId();
        } else {
            throw new WalletException("Wallet not found.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ClaimLog> getClaimLogs(Integer claimId) {
        List<ClaimLogDetailData> claimLogDetailDataList = claimManagementDao.getClaimLogsByClaimId(claimId);
        List<ClaimLog> claimLogs = new ArrayList<>();
        for (ClaimLogDetailData claimLogDetailData : claimLogDetailDataList) {
            IdCodeName updatedBy = new IdCodeName(claimLogDetailData.getUpdatedBy(), masterDataCache.getEmployee(claimLogDetailData.getUpdatedBy()));
            claimLogs.add(WalletDataConverter.convert(claimLogDetailData, updatedBy));
        }
        return claimLogs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Voucher> getClaimVouchers(Integer claimId) {
        return walletService.getVoucherListByClaimId(claimId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setHappayIdToClaim(String happayId, Integer claimId, Integer userId) throws WalletException {
        ClaimDetailData claimDetailData = claimManagementDao.find(ClaimDetailData.class, claimId);
        if (claimDetailData != null && ClaimType.HAPPAY.name().equalsIgnoreCase(claimDetailData.getClaimType()) &&
                ClaimStatus.INITIATED.name().equalsIgnoreCase(claimDetailData.getClaimCurrentStatus())) {
            ClaimStatus currentStatus = ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus());
            claimDetailData.setHappayId(happayId);
            claimDetailData.setClaimCurrentStatus(ClaimStatus.CREATED.name());
            claimDetailData.setLastUpdatedBy(userId);
            claimDetailData.setLastUpdateTime(AuditUtil.getCurrentTimestamp());
            claimDetailData = claimManagementDao.update(claimDetailData, true);
            if (claimDetailData != null) {
                addClaimLog(claimId, null, currentStatus,
                        ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus()), userId);
                return true;
            } else {
                throw new WalletException("Error updating Happay id to claim id: " + claimId);
            }
        } else {
            throw new WalletException("Invalid claim id: " + claimId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean approveRejectClaim(ClaimRequestVO request, boolean isApprove) throws WalletException, EmailGenerationException {
        ClaimDetailData claimDetailData = claimManagementDao.find(ClaimDetailData.class, request.getClaimId());
        if (claimDetailData != null && ClaimStatus.CREATED.name().equalsIgnoreCase(claimDetailData.getClaimCurrentStatus())) {
            ClaimStatus currentStatus = ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus());
            if (isApprove) {
                claimDetailData.setClaimCurrentStatus(ClaimStatus.APPROVED.name());
            } else {
                claimDetailData.setClaimCurrentStatus(ClaimStatus.REJECTED.name());
                if (request.getComment() == null) {
                    throw new WalletException("Please provide reason to reject.");
                }
            }
            if (Objects.nonNull(request.getHappayId())){
                claimDetailData.setHappayId(request.getHappayId());
            }
            claimDetailData.setLastUpdatedBy(request.getRequestBy().getId());
            claimDetailData.setLastUpdateTime(AuditUtil.getCurrentTimestamp());
            claimDetailData = claimManagementDao.update(claimDetailData, true);
            if (claimDetailData != null) {
                addClaimLog(claimDetailData.getClaimId(), request.getComment(), currentStatus,
                        ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus()), request.getRequestBy().getId());
                if(claimDetailData.getClaimCurrentStatus().equals(ClaimStatus.APPROVED.name())) {
                    try {
                        sendPaymentEmail(claimDetailData, false);
                    } catch (Exception exp) {
                        LOG.error("Error while sending Mail of Payment notification for claimId: " + claimDetailData.getClaimId(), exp);
                    }
                }
                return true;
            } else {
                throw new WalletException("Error updating Happay id to claim id: " + request.getClaimId());
            }
        } else {
            throw new WalletException("Invalid claim id: " + request.getClaimId());
        }
    }

    public void sendPaymentEmail(ClaimDetailData claimDetailData, boolean isAutoAcknowledgeMail) throws EmailGenerationException {
        String body = "";
        String subject = "";
        String name = "";
        String toMail = "";
        Unit unit = masterDataCache.getUnit(claimDetailData.getUnitId());
        if(Objects.nonNull(unit)) {
            name = unit.getName();
            toMail = unit.getUnitEmail();
        } else {
            EmployeeBasicDetail employee = masterDataCache.getEmployeeBasicDetail(claimDetailData.getUnitId());
            if(Objects.isNull(employee)) {
                throw new EmailGenerationException("Unit or Employee Not found to send EMAIL for ID: " + claimDetailData.getUnitId());
            }
            name = employee.getName();
            toMail = employee.getEmailId();
        }
        toMail = (StringUtils.hasLength(toMail) && AppUtils.isValidEmail(toMail)) ? toMail : AppConstants.TECHNOLOGY_EMAIL;
        List<String> toMailsList = new ArrayList<>();
        toMailsList.add(toMail);
        if(isAutoAcknowledgeMail) {
            subject = "Payment Auto Settlement Notification [COMPLETED]";
            toMailsList.add(AppConstants.FINANCE_EMAIL);
            body = "Dear " + name + "<br><br>" +
                    "The payment of INR <b>" + claimDetailData.getClaimApprovedAmount() + "</b> received on " +  TransactionUtils.getDateString(claimDetailData.getLastUpdateTime()) +
                    " has been Auto Acknowledged in kettle.<br>" +
                    "If you have not received the payment or have any concerns, please raise them immediately.<br><br>" +
                    "Thank you for your cooperation.";
        } else {
            subject = "Payment Notification";
            body = "Dear " + name + "<br><br>" +
                    "We are pleased to inform you that a payment of INR <b>" + claimDetailData.getClaimApprovedAmount() + "</b> " +
                    "has been disbursed on " + TransactionUtils.getDateString(claimDetailData.getLastUpdateTime()) + ".<br> " +
                    "Kindly check and acknowledge this amount in Kettle at your earliest convenience. If the \"Payment\" is not acknowledged by the cafes within <b>" + envProperties.getAutoSettleDays() + " days,</b> it will be automatically settled to avoid delays.<br><br>" +
                    "If you have not received the payment or have any concerns, please raise them immediately.<br><br>" +
                    "Thank you for your cooperation.";
        }
        new EmailNotificationTemplate(subject, body, toMailsList.toArray(new String[toMailsList.size()]), envProperties.getEnvType(), AppConstants.REPORTING_EMAIL).sendEmail();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void downloadClaimDetail(HttpServletResponse response, Integer claimId) throws WalletException, TemplateRenderingException, FileNotFoundException {
        ClaimDetailData claimDetailData = claimManagementDao.find(ClaimDetailData.class, claimId);
        String path = envProperties.getBasePath() + "/claim/" + claimDetailData.getClaimId() + ".pdf";
        String claimTemplate = generateClaimTemplate(claimDetailData);
        if (claimDetailData != null) {
            try (OutputStream outputStream = new FileOutputStream(path)) {
                HtmlConverter.convertToPdf(claimTemplate, outputStream);
                outputStream.flush();
                File file = new File(envProperties.getBasePath() + "/claim/" + claimDetailData.getClaimId() + ".pdf");
                if (file != null) {
                    response.setContentType("application/pdf");
                    response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
                    byte[] bytesArray = new byte[(int) file.length()];
                    response.setContentLength(bytesArray.length);
                    try {
                        OutputStream outputStream1 = response.getOutputStream();
                        InputStream inputStream = new FileInputStream(file);
                        int counter = 0;
                        while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
                            outputStream1.write(bytesArray, 0, counter);
                            outputStream1.flush();
                        }
                        outputStream1.close();
                        inputStream.close();
                    } catch (IOException e) {
                        LOG.error("Encountered error while writing warning letter file to response stream", e);
                        throw e;
                    } finally {
                        response.getOutputStream().flush();
                        file.delete();
                    }
                }
            } catch (IOException e) {
                LOG.error("Error while trying to download claim PDF :::::: {}", claimDetailData.getClaimId(), e);
                throw new WalletException("Error downloading claim report");
            }
        } else {
            throw new WalletException("Invalid claim id: " + claimId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View bulkDownload(ClaimFindVO claimFindVO) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "BulkClaims.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<DownloadClaimsVO> downloadClaimsVOS = claimManagementDao.bulkDownload(claimFindVO);
                List<ClaimsDetailDataVO> claimsDetailDataVOS =
                        downloadClaimsVOS.stream().map(val -> ClaimsDetailDataVO.builder().cardNumber(val.getCardNumber()).cr("cr").amount(val.getClaimApprovedAmount()).
                                claimExpenseId(val.getClaimExpenseId()).corporateId(val.getIfscCode())
                                .build()).collect(Collectors.toList());
                writer.writeSheet(claimsDetailDataVOS, ClaimsDetailDataVO.class);
            }
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public View downloadById(Integer claimId) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "ClaimsById.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<DownloadClaimsVO> downloadClaimsVOS = claimManagementDao.downloadById(response,claimId);
                List<ClaimsDetailDataVO> claimsDetailDataVOS =
                        downloadClaimsVOS.stream().map(val -> ClaimsDetailDataVO.builder().cardNumber(val.getCardNumber()).cr("cr").amount(val.getClaimApprovedAmount()).
                                claimExpenseId(val.getClaimExpenseId()).corporateId(val.getIfscCode())
                                .build()).collect(Collectors.toList());
                writer.writeSheet(claimsDetailDataVOS, ClaimsDetailDataVO.class);
            }
        };
    }

    @Override
    public View downloadTemplate() {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "Template.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<ApprovalDataVO> approvalDataVOS = new ArrayList<>();
                writer.writeSheet(approvalDataVOS, ApprovalDataVO.class);
            }
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean uploadApprovals(MultipartFile file, Integer requestBy) throws Exception{
        List<ExcelParsingException> errors = new ArrayList<>();
        Workbook workbook;
        if (file.getOriginalFilename().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        SheetParser parser = new SheetParser();
        List<ApprovalDataVO> approvalDataVOS = parser.createEntity(workbook.getSheetAt(0),
                ApprovalDataVO.class, errors::add);
        for(ApprovalDataVO approvalDataVO : approvalDataVOS){
//            claimManagementDao.insertExpenseId(approvalDataVO);
            ClaimRequestVO claimRequestVO = new ClaimRequestVO();
            claimRequestVO.setClaimId(approvalDataVO.getClaimExpenseId());
            claimRequestVO.setHappayId(approvalDataVO.getExpenseSettlementId());
            claimRequestVO.setRequestBy(new IdCodeName(requestBy,""));
            approveRejectClaim(claimRequestVO,true);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean acknowledgeClaim(ClaimRequestVO request) throws WalletException {
        ClaimDetailData claimDetailData = claimManagementDao.find(ClaimDetailData.class, request.getClaimId());
        if (claimDetailData != null) {
            if (ClaimType.HAPPAY.name().equals(claimDetailData.getClaimType())
                || ClaimType.ICICI.name().equals(claimDetailData.getClaimType())) {
                return acknowledgeHappayClaim(claimDetailData, request);
            }
        }
        throw new WalletException("Invalid claim id: " + request.getClaimId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void autoAcknowledgeClaims() throws WalletException, EmailGenerationException {
        List<ClaimDetailData> claimList = claimManagementDao.getAllClaimsToAutoSettle(envProperties.getAutoSettleDays());
        if(CollectionUtils.isEmpty(claimList)) {
            LOG.info("NO CLAIMS FOUND TO SETTLE");
            return;
        }
        for(ClaimDetailData claim : claimList) {
            try {
                autoAcknowledgeClaim(claim);
            } catch (Exception exp) {
                LOG.error("Error while auto acknowledging for claimId: " + claim.getClaimId(), exp);
            }
        }
        claimManagementDao.update(claimList, false);
    }

    private boolean acknowledgeHappayClaim(ClaimDetailData claimDetailData, ClaimRequestVO request) throws WalletException {
        String claimCurrentStatus = claimDetailData.getClaimCurrentStatus();
        if (ClaimStatus.APPROVED.name().equalsIgnoreCase(claimCurrentStatus) ||
                ClaimStatus.REJECTED.name().equalsIgnoreCase(claimCurrentStatus)) {
            ClaimStatus currentStatus = ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus());
            claimDetailData.setClaimCurrentStatus(ClaimStatus.SETTLED.name());
            claimDetailData.setLastUpdatedBy(request.getRequestBy().getId());
            claimDetailData.setLastUpdateTime(AuditUtil.getCurrentTimestamp());
            claimDetailData = claimManagementDao.update(claimDetailData, true);
            if (claimDetailData != null) {
                addClaimLog(claimDetailData.getClaimId(), request.getComment(), currentStatus,
                        ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus()), request.getRequestBy().getId());
                if (ClaimStatus.APPROVED.name().equalsIgnoreCase(claimCurrentStatus)) {
                    //updating wallet amount
                    walletService.updateWalletFromClaimAcknowledgement(claimDetailData, request.getRequestBy().getId());
                    return true;
                } else {
                    for (VoucherData data : walletService.getVoucherDataListByClaimId(claimDetailData.getClaimId())) {
                        data.setClaimId(null);
                        data = claimManagementDao.update(data, true);
                        if (data == null) {
                            throw new WalletException("Error updating voucher for claim.");
                        }
                    }
                    return true;
                }
            } else {
                throw new WalletException("Error acknowledging claim id: " + request.getClaimId());
            }
        } else {
            throw new WalletException("Invalid claim id: " + request.getClaimId());
        }
    }

    private void autoAcknowledgeClaim(ClaimDetailData claimDetailData) throws WalletException, EmailGenerationException {
        String claimCurrentStatus = claimDetailData.getClaimCurrentStatus();
        ClaimStatus currentStatus = ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus());
        claimDetailData.setClaimCurrentStatus(ClaimStatus.SETTLED.name());
        claimDetailData.setLastUpdatedBy(AppConstants.SYSTEM_EMPLOYEE_ID);
        claimDetailData.setLastUpdateTime(AuditUtil.getCurrentTimestamp());
        addClaimLog(claimDetailData.getClaimId(), "Auto Settling from System Side, because delay in settling", currentStatus,
                ClaimStatus.valueOf(claimDetailData.getClaimCurrentStatus()), AppConstants.SYSTEM_EMPLOYEE_ID);
        if (ClaimStatus.APPROVED.name().equalsIgnoreCase(claimCurrentStatus)) {
            walletService.updateWalletFromClaimAcknowledgement(claimDetailData, AppConstants.SYSTEM_EMPLOYEE_ID);
            sendPaymentEmail(claimDetailData, true);
        } else {
            List<VoucherData> voucherDataList = walletService.getVoucherDataListByClaimId(claimDetailData.getClaimId());
            for (VoucherData data : voucherDataList) {
                data.setClaimId(null);
            }
            claimManagementDao.update(voucherDataList, false);
        }
    }


    private String generateClaimTemplate(ClaimDetailData claimDetailData) throws WalletException, TemplateRenderingException {
        if (claimDetailData != null) {
            IdCodeName createdBy = new IdCodeName(claimDetailData.getCreatedBy(), "", masterDataCache.getEmployee(claimDetailData.getCreatedBy()));
            IdCodeName lastUpdatedBy = new IdCodeName(claimDetailData.getLastUpdatedBy(), "", masterDataCache.getEmployee(claimDetailData.getLastUpdatedBy()));
            IdCodeName employee = null;
            if (claimDetailData.getEmployeeId() != null) {
                employee = new IdCodeName(claimDetailData.getEmployeeId(), "", masterDataCache.getEmployee(claimDetailData.getEmployeeId()));
            }
            IdCodeName unit = null;
            if (claimDetailData.getUnitId() != null) {
                try {
                    unit = new IdCodeName(claimDetailData.getUnitId(), "", masterDataCache.getUnitBasicDetail(claimDetailData.getUnitId()).getName());
                }catch (Exception e) {
                    LOG.info("Unable to fetch unit data");
                }
            }
            Claim claim = WalletDataConverter.convert(claimDetailData, createdBy, lastUpdatedBy, employee, unit);
            claim.setClaimLogs(getClaimLogs(claim.getClaimId()));
            claim.setVouchers(getClaimVouchers(claim.getClaimId()));
            ClaimDetailPDFTemplate claimDetailPDFTemplate = new ClaimDetailPDFTemplate(claim, envProperties.getBasePath());
            return claimDetailPDFTemplate.getContent();
        } else {
            throw new WalletException(FormsErrorConstants.PROVIDE_AUDIT_ID);
        }
    }

    private void addClaimLog(Integer claimId, String comment, ClaimStatus fromStatus, ClaimStatus toStatus, Integer updatedBy) throws WalletException {
        ClaimLogDetailData claimLogDetailData = new ClaimLogDetailData();
        claimLogDetailData.setClaimId(claimId);
        if (comment != null) {
            claimLogDetailData.setComments(comment);
        }
        if (fromStatus != null) {
            claimLogDetailData.setFromStatus(fromStatus.name());
        }
        claimLogDetailData.setToStatus(toStatus.name());
        claimLogDetailData.setUpdatedBy(updatedBy);
        claimLogDetailData.setUpdateTime(AuditUtil.getCurrentTimestamp());
        claimLogDetailData = claimManagementDao.add(claimLogDetailData, true);
        if (claimLogDetailData == null) {
            throw new WalletException("Error creating claim logs.");
        }
    }

    private ClaimRequestVO setServiceChargeClaimRequest(List<Voucher> voucherList) {
        ClaimRequestVO claimRequest = new ClaimRequestVO();
        List<Integer> voucherIds = new ArrayList<>();
        claimRequest.setClaimType(ClaimType.ICICI);
        claimRequest.setRequestBy(AuditUtil.getSystemUser());
        claimRequest.setAccountNo(voucherList.get(0).getAccountNo());
        for (Voucher voucher : voucherList) {
            claimRequest.setClaimAmount(AppUtils.add(claimRequest.getClaimAmount(), voucher.getExpenseAmount()));
            voucherIds.add(voucher.getId());
        }
        claimRequest.setVoucherIds(voucherIds);
        return claimRequest;
    }

    private Voucher setServiceChargeRefundVoucher(OrderRefundDetail orderRefundDetail, MasterDataCache masterDataCache, WalletData walletData, OrderRefundClaimVo orderClaimRequestVo) {
        Voucher voucher = new Voucher();
        ExpenseMetadata expenseMetadata = masterDataCache.getExpenseDataById(envProperties.getRefundExpenseMetadataId());
        voucher.setAccountType(Enum.valueOf(WalletAccountType.class, walletData.getAccountType()));
        voucher.setWalletId(walletData.getId());
        voucher.setAccountNo(walletData.getAccountNo());
        voucher.setExpenseType(expenseMetadata.getDesc());
        voucher.setExpenseCategory(expenseMetadata.getType());
        voucher.setBudgetCategory(expenseMetadata.getBudgetCat());
        voucher.setExpenseMetadataId(expenseMetadata.getId());
        voucher.setAccountableInPNL(expenseMetadata.isAccountable());
        voucher.setIssuedAmount(orderRefundDetail.getRefundAmount());
        voucher.setExpenseAmount(orderRefundDetail.getRefundAmount());
        voucher.setExpenseDetail(AppConstants.REFUND_VOUCHER);
        voucher.setIssuedBy(AuditUtil.getSystemUser());
        voucher.setIssuedTo(AuditUtil.getSystemUser());
        voucher.setEntity(IssuingEntity.SYSTEM);
        List<WalletBusinessCostCenterMappingVO> wallerBccMapping = walletService.getWalletBusinessCostCenterMappings(walletData.getId());
        List<VoucherCostCenterAllocation> allocations = new ArrayList<>();
        if (Objects.nonNull(wallerBccMapping) && !wallerBccMapping.isEmpty()) {
            allocations.add(VoucherCostCenterAllocation.builder()
                    .businessCostCenterId(wallerBccMapping.get(0).getBccId())
                    .businessCostCenter(wallerBccMapping.get(0).getBccName())
                    .allocatedIssuedAmount(orderRefundDetail.getRefundAmount())
                    .build());
        }
        voucher.setVoucherCostCenterAllocations(allocations);
        return voucher;
    }

    @Override
    public void sendMailForInitiateClaim() {
        List<WalletData> walletDataList = walletDao.findAllWalletsInStatus(WalletStatus.ACTIVE.name(), WalletAccountType.UNIT.name());
        if(CollectionUtils.isEmpty(walletDataList)) {
            return;
        }
        Map<Integer, List<VoucherData>> voucherListByWalletId = walletDao.findAllPendingVouchers(
                walletDataList.stream().map(WalletData::getId).collect(Collectors.toList()),
                VoucherStatus.APPROVED.name(), 1, 0);

        if(CollectionUtils.isEmpty(voucherListByWalletId)) {
            return;
        }

        for(WalletData wallet : walletDataList) {
            List<VoucherData> voucherDataList = voucherListByWalletId.get(wallet.getId());
            if(CollectionUtils.isEmpty(voucherDataList)) {
                continue;
            }
            List<Voucher> vouchers = new ArrayList<Voucher>();
            voucherDataList.forEach(data -> {
                vouchers.add(WalletDataConverter.convert(data, wallet, false, false, null, masterDataCache, null));
            });
            try {
                AutoInitiateClaimInfoTemplate template = new AutoInitiateClaimInfoTemplate(vouchers, envProperties.getAutoClaimInitiateInDays());
                AutoClaimInitiateEmailNotification autoClaimInitiateEmailNotification = new AutoClaimInitiateEmailNotification(
                        wallet.getAccountHolderEmail(), template, envProperties.getEnvType()
                );
                autoClaimInitiateEmailNotification.sendEmail();
                LOG.info("Mail sent to wallet Id : {} and wallet holder MailId : {}", wallet.getId(), wallet.getAccountHolderEmail());
            } catch (Exception exp) {
                LOG.error("Un expected error while sending for initiate claim - WalletId : {}", wallet.getId(), exp);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "AuditDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void autoInitiateClaims() {
        Integer autoClaimInitiateInDays = envProperties.getAutoClaimInitiateInDays();
        List<WalletData> walletDataList = walletDao.findAllWalletsInStatus(WalletStatus.ACTIVE.name(), WalletAccountType.UNIT.name());
        if(CollectionUtils.isEmpty(walletDataList)) {
            return;
        }
        Map<Integer, List<VoucherData>> voucherListByWalletId = walletDao.findAllPendingVouchers(
                walletDataList.stream().map(WalletData::getId).collect(Collectors.toList()),
                VoucherStatus.APPROVED.name(), autoClaimInitiateInDays, (autoClaimInitiateInDays-1));

        if(CollectionUtils.isEmpty(voucherListByWalletId)) {
            return;
        }
        for(WalletData wallet : walletDataList) {
            try {
                autoInitiateClaims(wallet, voucherListByWalletId.get(wallet.getId()));
            } catch (Exception exp) {
                LOG.error("Error while auto initiating a claim for wallet ID : {}", wallet.getId());
            }
        }
    }

    private void autoInitiateClaims(WalletData wallet, List<VoucherData> voucherDataList) throws WalletException {
        if(CollectionUtils.isEmpty(voucherDataList)) {
            return;
        }
        LOG.info("Auto Initiating claim for vouchers with walletId : {}", wallet.getId());
        Set<VoucherData> validVouchers = new HashSet<>();
        BigDecimal claimAmount = BigDecimal.ZERO;
        for (VoucherData voucherData : voucherDataList) {
            if (VoucherStatus.APPROVED.name().equals(voucherData.getCurrentStatus()) && voucherData.getClaimId() == null) {
                validVouchers.add(voucherData);
                claimAmount = AuditUtil.add(claimAmount, voucherData.getExpenseAmount());
            }
        }
        if(claimAmount.compareTo(BigDecimal.valueOf(envProperties.getMinimumAutoInitiateAmount())) < 0) {
            LOG.info("claim Amount is less than {} , so this claim was not auto initiating.", envProperties.getMinimumAutoInitiateAmount());
            return;
        }
        if (validVouchers.size() > 0) {
            ClaimDetailData claimDetailData = new ClaimDetailData();
            claimDetailData.setWalletId(wallet.getId());
            claimDetailData.setClaimApprovedAmount(claimAmount);
            claimDetailData.setClaimCurrentStatus(ClaimStatus.CREATED.name());
            claimDetailData.setClaimRequestedAmount(claimAmount);
            claimDetailData.setClaimType(ClaimType.ICICI.name());
            claimDetailData.setCreatedBy(AppConstants.SYSTEM_EMPLOYEE_ID);
            claimDetailData.setCreationTime(AuditUtil.getCurrentTimestamp());
            claimDetailData.setLastUpdatedBy(AppConstants.SYSTEM_EMPLOYEE_ID);
            claimDetailData.setLastUpdateTime(AuditUtil.getCurrentTimestamp());
            claimDetailData.setUnitId(wallet.getAssociatedId());
            claimDetailData = claimManagementDao.add(claimDetailData, true);
            if (claimDetailData != null) {
                addClaimLog(claimDetailData.getClaimId(), "Auto initiating claim by system", ClaimStatus.CREATED,
                ClaimStatus.CREATED, AppConstants.SYSTEM_EMPLOYEE_ID);
                for (VoucherData voucherData : validVouchers) {
                    voucherData.setClaimId(claimDetailData.getClaimId());
                    voucherData.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
                    voucherData = claimManagementDao.update(voucherData, true);
                    if (voucherData == null) {
                        throw new WalletException("Error updating claim id in vouchers!");
                    }
                }
            } else {
                throw new WalletException("Error creating claim detail!");
            }
        }
    }

}
