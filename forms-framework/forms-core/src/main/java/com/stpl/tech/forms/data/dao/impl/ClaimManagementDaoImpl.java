package com.stpl.tech.forms.data.dao.impl;

import com.stpl.tech.expense.domain.model.ClaimFindVO;
import com.stpl.tech.expense.domain.model.ClaimStatus;
import com.stpl.tech.expense.domain.model.ClaimType;
import com.stpl.tech.forms.data.dao.ClaimManagementDao;
import com.stpl.tech.forms.data.model.ClaimDetailData;
import com.stpl.tech.forms.data.model.ClaimLogDetailData;
import com.stpl.tech.forms.data.model.WalletData;
import com.stpl.tech.forms.domain.model.ApprovalDataVO;
import com.stpl.tech.forms.domain.model.DownloadClaimsVO;
import com.stpl.tech.util.AppUtils;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("unchecked")
@Repository
public class ClaimManagementDaoImpl extends AbstractDaoImpl implements ClaimManagementDao {

    @Override
    public List<ClaimDetailData> findClaims(Date startDate, Date endDate, ClaimStatus status, ClaimType type,
                                            List<String> unitIds, Integer walletId, Integer employeeId) {
        Query walletQuery = manager.createQuery("FROM WalletData where accountNo in (:accountNo)");
        walletQuery.setParameter("accountNo",unitIds);
        List<WalletData> walletData = walletQuery.getResultList();
        List<Integer> walletDetailId = walletData.stream().map(WalletData::getId).collect(Collectors.toList());

        StringBuilder stringBuilder = new StringBuilder("FROM ClaimDetailData c ");
        boolean oneParamSet = false;
        if (startDate != null || endDate != null || status != null || type != null || unitIds != null || walletId != null || employeeId != null || !walletDetailId.isEmpty()) {
            stringBuilder.append("WHERE ");
        }
        if (startDate != null) {
            stringBuilder.append("c.creationTime >= :startDate ");
            oneParamSet = true;
        }
        if (endDate != null) {
            stringBuilder.append((oneParamSet ? "AND " : "") + "c.creationTime <= :endDate ");
            oneParamSet = true;
        }
        if (status != null) {
            stringBuilder.append((oneParamSet ? "AND " : "") + "c.claimCurrentStatus = :status ");
            oneParamSet = true;
        }
        if (type != null) {
            stringBuilder.append((oneParamSet ? "AND " : "") + "c.claimType = :type ");
            oneParamSet = true;
        }
//        if (unitIds != null) {
//            stringBuilder.append((oneParamSet ? "AND " : "") + "c.unitId IN (:unitIds) ");
//            oneParamSet = true;
//        }
        if (!walletDetailId.isEmpty()) {
            stringBuilder.append((oneParamSet ? "AND " : "") + "c.walletId in (:walletId) ");
            oneParamSet = true;
        }
        Query query = manager.createQuery(stringBuilder.toString());
        if (startDate != null) {
            query.setParameter("startDate", startDate);
        }
        if (endDate != null) {
            query.setParameter("endDate", endDate);
        }
        if (status != null) {
            query.setParameter("status", status.name());
        }
        if (type != null) {
            query.setParameter("type", type.name());
        }
//        if (unitIds != null) {
//            query.setParameter("unitIds", unitIds);
//        }
//        if (walletId != null) {
//            query.setParameter("walletId", walletId);
//        }
        if (!walletDetailId.isEmpty()) {
            query.setParameter("walletId", walletDetailId);
        }
//        if (employeeId != null) {
//            query.setParameter("employeeId", employeeId);
//        }
        return query.getResultList();
    }

    @Override
    public List<ClaimLogDetailData> getClaimLogsByClaimId(Integer claimId) {
        Query query = manager.createQuery("FROM ClaimLogDetailData c WHERE c.claimId = :claimId");
        query.setParameter("claimId", claimId);
        return query.getResultList();
    }

    @Override
    public List<DownloadClaimsVO> bulkDownload(ClaimFindVO response) {
        //TODO Pass start date and end date and status
        Query query = manager.createQuery("Select w.cardNumber,w.ifscCode,c.claimApprovedAmount,c.claimId from WalletData w,ClaimDetailData c " +
                "where w.id=c.walletId and c.creationTime >= :startDate and c.creationTime <= :endDate and c.claimCurrentStatus = :status");
        query.setParameter("startDate",response.getStartDate());
        query.setParameter("endDate",response.getEndDate());
        query.setParameter("status",response.getStatus().name());
        List<Object[]> results = query.getResultList();
        List<DownloadClaimsVO> downloadClaimsVOS= new ArrayList<>();
        results.forEach((record) -> downloadClaimsVOS.add(DownloadClaimsVO.builder().cardNumber((String) record[0]).ifscCode((String) record[1])
                .claimApprovedAmount((BigDecimal) record[2]).claimExpenseId((Integer) record[3]).build()));
        return downloadClaimsVOS;
    }

    @Override
    public List<DownloadClaimsVO> downloadById(HttpServletResponse response, Integer claimId) {
        Query query = manager.createQuery("Select w.cardNumber,w.ifscCode,c.claimApprovedAmount,c.claimId from WalletData w,ClaimDetailData c where w.id=c.walletId and c.claimId= :claimId and c.claimCurrentStatus =:status");
        query.setParameter("claimId",claimId);
        query.setParameter("status",ClaimStatus.CREATED.name());
        List<Object[]> results = query.getResultList();
        List<DownloadClaimsVO> downloadClaimsVOS= new ArrayList<>();
        results.forEach((record) -> downloadClaimsVOS.add(DownloadClaimsVO.builder().cardNumber((String) record[0]).ifscCode((String) record[1])
                .claimApprovedAmount((BigDecimal) record[2]).claimExpenseId((Integer) record[3]).build()));
        return downloadClaimsVOS;
    }

    @Override
    public void insertExpenseId(ApprovalDataVO finalObject) {
            ClaimDetailData claimDetailData = manager.find(ClaimDetailData.class,finalObject.getClaimExpenseId());
            claimDetailData.setHappayId(finalObject.getExpenseSettlementId());
            claimDetailData.setClaimCurrentStatus(ClaimStatus.APPROVED.name());
            manager.persist(claimDetailData);
    }

    @Override
    public List<ClaimDetailData> getAllClaimsToAutoSettle(Integer autoSettleDays) {
        Query query = manager.createQuery("FROM ClaimDetailData cdd WHERE " +
                "cdd.lastUpdateTime < :date AND cdd.claimCurrentStatus IN (:claimStatus) AND cdd.claimType IN (:claimTypes)");

        query.setParameter("date", AppUtils.getDayBeforeOrAfterCurrentDay(-1 * autoSettleDays));
        query.setParameter("claimTypes", Arrays.asList(ClaimType.HAPPAY.name(), ClaimType.ICICI.name()));
        query.setParameter("claimStatus", Arrays.asList(ClaimStatus.APPROVED.name(), ClaimStatus.REJECTED.name()));

        return query.getResultList();
    }


}
