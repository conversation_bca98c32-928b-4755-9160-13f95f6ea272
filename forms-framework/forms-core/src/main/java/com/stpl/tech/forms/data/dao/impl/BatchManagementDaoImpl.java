package com.stpl.tech.forms.data.dao.impl;

import com.stpl.tech.forms.data.dao.BatchManagementDao;
import com.stpl.tech.forms.data.model.BatchCode;
import com.stpl.tech.forms.data.model.BatchCodeCounter;
import com.stpl.tech.forms.data.model.BatchCodePrefix;
import com.stpl.tech.forms.data.model.BatchData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.List;

@SuppressWarnings("unchecked")
@Repository
public class BatchManagementDaoImpl extends AbstractDaoImpl implements BatchManagementDao {

    @Override
    public BatchData getBatchData(String batchCodePrefix){
        try{
            Query query = manager.createQuery("FROM BatchData b WHERE b.codePrefix = :code ");
            query.setParameter("code",	batchCodePrefix);
            List result = query.getResultList();
            if(CollectionUtils.isNotEmpty(result)){
                return (BatchData) result.get(0);
            }
        }catch (NoResultException ex){
            return null;
        }
        return null;
    }

    @Override
    public BatchData saveBatchData(BatchData batchData){
        if(batchData != null){
            manager.persist(batchData);
            if(batchData.getId()!=0){
                return batchData;
            }
        }
        return null;
    }

    @Override
    public boolean saveBatchCodes(List<BatchCode> batchCodes){
        try{
            if(CollectionUtils.isNotEmpty(batchCodes)){
                for(BatchCode batchCode: batchCodes){
                    manager.persist(batchCode);
                }
                return true;
            }
        }catch (Exception ex){
            return false;
        }
        return false;
    }

    @Override
    public List<BatchCode> getBatchCodes(int batchDataId){
        if(batchDataId>0){
            Query query = manager.createQuery("FROM BatchCode b WHERE b.batchDataId = :batchDataId ");
            query.setParameter("batchDataId",	batchDataId);
            return query.getResultList();
        }
        return null;
    }

    @Override
    public List<String> checkIfUniqueBatchCodes(List<String> generatedCodes, List<String> existingBatchCodes){
        if(CollectionUtils.isNotEmpty(generatedCodes)){
            Query query = manager.createQuery("FROM BatchCode b WHERE b.code IN (:codesList) ");
            query.setParameter("codesList",	generatedCodes);
            existingBatchCodes = query.getResultList();
        }
        return existingBatchCodes;
    }


    @Override
    public String getCodePrefix(String keyId, String keyType) {
        try {
            if(StringUtils.isNotBlank(keyId)){
                Query query = manager.createQuery("FROM BatchCodePrefix b WHERE b.keyId = :keyId and b.keyType = :keyType");
                query.setParameter("keyId", keyId);
                query.setParameter("keyType", keyType);
                Object result = query.getSingleResult();
                if(result != null){
                    BatchCodePrefix batchCodePrefix = (BatchCodePrefix) result;
                    return batchCodePrefix.getCodePrefix();
                }
            }
            return StringUtils.EMPTY;
        }catch (NoResultException ex){
            return StringUtils.EMPTY;
        }
    }

    @Override
    public int updateBatchCounter(String codePrefix, int counterResetLimit) {
        if(StringUtils.isNotBlank(codePrefix) ){
            Query query = manager.createQuery("FROM BatchCodeCounter b WHERE b.codePrefix = :code ");
            query.setParameter("code", codePrefix);
            BatchCodeCounter batchCodeCounter = null;
            List result = query.getResultList();
            if(CollectionUtils.isNotEmpty(result)){
                batchCodeCounter = (BatchCodeCounter) result.get(0);
            }
            if(batchCodeCounter != null && batchCodeCounter.getNextCounter() != counterResetLimit){
                batchCodeCounter.setCurrentCounter(batchCodeCounter.getCurrentCounter() + 1);
                batchCodeCounter.setNextCounter(batchCodeCounter.getCurrentCounter() + 1);
                return batchCodeCounter.getCurrentCounter();
            }else{
                batchCodeCounter = new BatchCodeCounter();
                batchCodeCounter.setCodePrefix(codePrefix);
                batchCodeCounter.setCurrentCounter(1);
                batchCodeCounter.setNextCounter(batchCodeCounter.getCurrentCounter() + 1);
                manager.persist(batchCodeCounter);
                if(batchCodeCounter.getId() !=0 ){
                    return batchCodeCounter.getCurrentCounter();
                }
            }
        }
        return 0;
    }

}
