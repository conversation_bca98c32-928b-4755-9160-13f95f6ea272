package com.stpl.tech.forms.core.notification.email.template;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import com.stpl.tech.warning.domain.model.EmployeeWarning;

public class WarningLetterNotificationTemplate extends AbstractVelocityTemplate {
	private String basePath;
	private EmployeeWarning warning;
	private EmployeeBasicDetail guiltyDetail;
	private List<String> emails;
	private String link = null;

	public WarningLetterNotificationTemplate() {
	}

	public WarningLetterNotificationTemplate(String basePath, EmployeeWarning warning, EmployeeBasicDetail basicDetail, List<String> emails) {
		this.basePath = basePath;
		this.warning = warning;
		this.guiltyDetail = basicDetail;
		this.emails = emails;
	}

	@Override
	public String getTemplatePath() {
		return "templates/WarningLetterNotificationTemplate.html";
	}

	@Override
	public String getFilepath() {
		return basePath + "/warningReportEmail/" + warning.getId() + ".html";
	}

	@Override
	public Map<String, Object> getData() {
		Map<String, Object> stringObjectMap = new HashMap<>();
		stringObjectMap.put("warning", warning);
		stringObjectMap.put("link", link);
		stringObjectMap.put("guiltyDetail", guiltyDetail);
		return stringObjectMap;
	}

	public EmployeeWarning getWarning() {
		return warning;
	}

	public List<String> getEmails() {
		return emails;
	}
	
	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}

	public EmployeeBasicDetail getGuiltyDetail() {
		return guiltyDetail;
	}

}
