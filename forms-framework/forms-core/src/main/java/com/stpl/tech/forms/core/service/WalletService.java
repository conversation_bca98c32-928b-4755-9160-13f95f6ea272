package com.stpl.tech.forms.core.service;

import com.stpl.tech.expense.domain.model.VoucherRejectionRequest;
import com.stpl.tech.expense.domain.model.WalletRequest;
import com.stpl.tech.forms.core.exception.WalletException;
import com.stpl.tech.forms.data.model.ClaimDetailData;
import com.stpl.tech.forms.domain.model.Voucher;
import com.stpl.tech.forms.domain.model.VoucherCostCenterAllocation;
import com.stpl.tech.forms.domain.model.Wallet;
import com.stpl.tech.forms.domain.model.WalletAccountType;
import com.stpl.tech.forms.domain.model.WalletApproverMappingVO;
import com.stpl.tech.forms.domain.model.WalletBusinessCostCenterMappingVO;
import com.stpl.tech.kettle.data.expense.model.VoucherData;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

public interface WalletService {

    public Wallet getWalletDetails(String accountType, String walletType, String accountNo) throws WalletException;

    List<Wallet> getWallets(WalletRequest walletRequest) throws WalletException;

    public String issueVoucher(Voucher voucher) throws WalletException;

    public List<Voucher> getVoucherList(List<String> status, Date startDate, Date endDate, List<String> accountNo, Boolean isReimbursed, boolean getStatus, boolean getFileData);

    public boolean uploadVoucherDoc(MultipartFile file, Integer voucherId, String fileType) throws WalletException;

    public Boolean settleVoucher(Voucher voucher) throws WalletException;

    public Boolean cancelVoucher(Voucher voucher) throws WalletException;

    List<Voucher> getVouchersByFinancePendingDate(List<String> status, Date startDate, Date endDate, List<String> accountNo,
                                                  Boolean isReimbursed, boolean getStatus, boolean getFileData);

    public Voucher getVoucherDetail(Integer voucherId) throws WalletException;

    public void downloadInvoice(HttpServletResponse response, Integer imageId) throws IOException, WalletException;

    public Boolean amApproveVoucher(Voucher voucher) throws WalletException;

    public Boolean amRejectVoucher(VoucherRejectionRequest request) throws WalletException;

    public Boolean financeApproveVoucher(Voucher voucher) throws WalletException;

    public Boolean financeRejectVoucher(VoucherRejectionRequest request) throws WalletException;

    public Boolean acknowledgePendingRejectedVoucher(Voucher voucher) throws WalletException;

    /*public String topupApprovedVouchers(WalletTransaction transaction) throws WalletException;*/

    public Long getPendingRejectedVoucherCount(Integer walletId);

    public List<com.stpl.tech.master.domain.model.DenominationDetail> getCashDenomination();

    public Wallet createWallet(Wallet wallet) throws WalletException;

    /*public Wallet updateWallet(Wallet wallet);*/

    boolean checkPendingVouchers(Integer walletId) throws WalletException;

    List<Voucher> getClaimPendingVouchers(String accountNo);

    public List<Voucher> getVoucherListByIdsAndAccountNo(List<Integer> voucherIds, String accountNo);

    List<Voucher> getVoucherListByClaimId(Integer claimId);

    List<Integer> getVoucherIdsByClaimId(Integer claimId);

    List<VoucherData> getVoucherDataListByClaimId(Integer claimId);

    public List<VoucherData> getVoucherDataListByIdsAndAccountNo(List<Integer> voucherIds, String accountNo);

    void updateWalletFromClaimAcknowledgement(ClaimDetailData claimDetailData, Integer updatedBy) throws WalletException;

    void upgradeWalletAmount(ClaimDetailData claimDetailData, Integer updatedBy) throws WalletException;

    void downgradeWalletAmount(ClaimDetailData claimDetailData, Integer updatedBy) throws WalletException;

    List<WalletBusinessCostCenterMappingVO> getWalletBusinessCostCenterMappings(int walletId);

    Wallet findWalletByAccountNo(String accountNo) throws WalletException;

    boolean updateAllWalletBusinessCostCenterMappings(List<WalletBusinessCostCenterMappingVO> walletBusinessCostCenterMappingVOList, String lastUpdatedBy, Integer walletId);

    List<Wallet> getUserWalletData(String accountType, Integer associatedId);

    boolean checkPendingVouchersWithAssociatedId(Integer associatedId) throws WalletException;

    List<VoucherCostCenterAllocation> getVoucherCostCenterAllocation(Integer voucherId);

    boolean updateWalletBusinessCostCenterMapping(Integer walletBccMappingId, String status, String lastUpdatedBy);

    Long getPendingRejectedVoucherCountWithAssociatedId(Integer associatedId);

    List<Wallet> getWalletAccounts(WalletAccountType walletAccountType, String employeeCode, boolean byPass) throws WalletException;

    List<String> getReportingManagersForAccountNo(String accountNo)throws WalletException;

    boolean updateWalletApproverMappings(Integer walletMappingId, String status,String lastUpdatedBy);

    List<WalletApproverMappingVO> getWalletApproverMappings(int walletId);

    boolean updateAllWalletApproverMappings(List<WalletApproverMappingVO> walletApproverMappingVOList, String lastUpdatedBy, Integer walletId);

}
