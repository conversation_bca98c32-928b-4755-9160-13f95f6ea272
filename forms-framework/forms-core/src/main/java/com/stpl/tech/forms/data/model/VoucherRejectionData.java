package com.stpl.tech.forms.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "VOUCHER_REJECTION_DATA")
public class VoucherRejectionData {

    private Integer id;
    private String rejectionName;
    private Integer rejectedBy;
    private Integer voucherId;
    private String rejectionEntity;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "REJECTION_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "NAME", nullable = false)
    public String getRejectionName() {
        return rejectionName;
    }

    public void setRejectionName(String rejectionName) {
        this.rejectionName = rejectionName;
    }

    @Column(name = "REJECTED_BY", nullable = false)
    public Integer getRejectedBy() {
        return rejectedBy;
    }

    public void setRejectedBy(Integer rejectedBy) {
        this.rejectedBy = rejectedBy;
    }

    @Column(name = "VOUCHER_ID", nullable = false)
    public Integer getVoucherId() {
        return voucherId;
    }

    public void setVoucherId(Integer voucherId) {
        this.voucherId = voucherId;
    }

    @Column(name = "REJECTION_ENTITY", nullable = false)
    public String getRejectionEntity() {
        return rejectionEntity;
    }

    public void setRejectionEntity(String rejectionEntity) {
        this.rejectionEntity = rejectionEntity;
    }
}
