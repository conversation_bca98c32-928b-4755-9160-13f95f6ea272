package com.stpl.tech.forms.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name = "AUDIT_FORM_VALUES")
public class AuditFormValuesData {

    private Integer id;
    private AuditFormData auditFormData;
    private String entityLabel;
    private String entityDescription;
    private String entityType;
    private String entityValues;
    private String entityMetadata;
    private String linkedDataType;
    private Integer linkedEntityId;
    private String answeredBy;
    private Integer appearanceOrder;
    private String linkedApi;
    private String scoreCounted;
    private String status;
    private BigDecimal maxScore;
    private String attachDoc;
    private String additionalComment;
    private String isMandatory = "N";
    private String questionOptional = "N";
    private String showAnswerScore = "Y";
    private String showMaxScore = "Y";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AUDIT_FORM_ID", nullable = false)
    public AuditFormData getAuditFormData() {
        return auditFormData;
    }

    public void setAuditFormData(AuditFormData auditFormData) {
        this.auditFormData = auditFormData;
    }

    @Column(name = "ENTITY_LABEL")
    public String getEntityLabel() {
        return entityLabel;
    }

    public void setEntityLabel(String entityLabel) {
        this.entityLabel = entityLabel;
    }

    @Column(name = "ENTITY_DESCRIPTION")
    public String getEntityDescription() {
        return entityDescription;
    }

    public void setEntityDescription(String entityDescription) {
        this.entityDescription = entityDescription;
    }

    @Column(name = "ENTITY_TYPE", nullable = false)
    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    @Column(name = "ENTITY_VALUES")
    public String getEntityValues() {
        return entityValues;
    }

    public void setEntityValues(String entityValues) {
        this.entityValues = entityValues;
    }

    @Column(name = "ENTITY_METADATA")
    public String getEntityMetadata() {
        return entityMetadata;
    }

    public void setEntityMetadata(String entityMetadata) {
        this.entityMetadata = entityMetadata;
    }

    @Column(name = "LINKED_DATA_TYPE")
    public String getLinkedDataType() {
        return linkedDataType;
    }

    public void setLinkedDataType(String linkedDataType) {
        this.linkedDataType = linkedDataType;
    }

    @Column(name = "LINKED_ENTITY_ID")
    public Integer getLinkedEntityId() {
        return linkedEntityId;
    }

    public void setLinkedEntityId(Integer linkedEntityId) {
        this.linkedEntityId = linkedEntityId;
    }

    @Column(name = "ANSWER_BY")
    public String getAnsweredBy() {
        return answeredBy;
    }

    public void setAnsweredBy(String answeredBy) {
        this.answeredBy = answeredBy;
    }

    @Column(name = "APPEARANCE_ORDER")
    public Integer getAppearanceOrder() {
        return appearanceOrder;
    }

    public void setAppearanceOrder(Integer appearanceOrder) {
        this.appearanceOrder = appearanceOrder;
    }

    @Column(name = "LINKED_API")
    public String getLinkedApi() {
        return linkedApi;
    }

    public void setLinkedApi(String linkedApi) {
        this.linkedApi = linkedApi;
    }

    @Column(name = "SCORE_COUNTED")
    public String getScoreCounted() {
        return scoreCounted;
    }

    public void setScoreCounted(String scoreCounted) {
        this.scoreCounted = scoreCounted;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "MAX_SCORE")
    public BigDecimal getMaxScore() {
        return maxScore;
    }

    public void setMaxScore(BigDecimal maxScore) {
        this.maxScore = maxScore;
    }

    @Column(name = "ATTACH_DOC")
    public String getAttachDoc() {
        return attachDoc;
    }

    public void setAttachDoc(String attachDoc) {
        this.attachDoc = attachDoc;
    }

    @Column(name = "ADDITIONAL_COMMENT")
    public String getAdditionalComment() {
        return additionalComment;
    }

    public void setAdditionalComment(String additionalComment) {
        this.additionalComment = additionalComment;
    }

    @Column(name = "IS_MANDATORY", nullable = false, length = 1)
    public String getIsMandatory() {
        return isMandatory;
    }

    public void setIsMandatory(String isMandatory) {
        this.isMandatory = isMandatory;
    }

    @Column(name = "QUESTION_OPTIONAL", nullable = false, length = 1)
    public String getQuestionOptional() {
        return questionOptional;
    }

    public void setQuestionOptional(String questionOptional) {
        this.questionOptional = questionOptional;
    }

    @Column(name = "SHOW_ANSWER_SCORE", nullable = false, length = 1)
    public String getShowAnswerScore() {
        return showAnswerScore;
    }

    public void setShowAnswerScore(String showAnswerScore) {
        this.showAnswerScore = showAnswerScore;
    }

    @Column(name = "SHOW_MAX_SCORE", nullable = false, length = 1)
    public String getShowMaxScore() {
        return showMaxScore;
    }

    public void setShowMaxScore(String showMaxScore) {
        this.showMaxScore = showMaxScore;
    }
}
