/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.forms.core.config;

import java.util.Properties;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import com.stpl.tech.spring.config.SpringUtilityServiceConfig;

/**
 * Created by Rahul Singh on 03-05-2016.
 */

@Configuration
@Import(value = SpringUtilityServiceConfig.class)
@EnableTransactionManagement
@ComponentScan(basePackages = {"com.stpl.tech.forms"})
@EnableJpaRepositories(basePackages = {"com.stpl.tech.forms.data.dao.impl"}, entityManagerFactoryRef = "AuditDataSourceEMFactory", transactionManagerRef = "AuditDataSourceTM")
public class FormsConfig {

    @Autowired
    private Environment env;

    public FormsConfig() {
        super();
    }
    // beans

    @Bean(name = "AuditDataSourceEMFactory")
    public LocalContainerEntityManagerFactoryBean auditEntityManagerFactory() {
        final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(auditDataSource());
        em.setPackagesToScan("com.stpl.tech.forms.data.model", "com.stpl.tech.warning.data.model", "com.stpl.tech.kettle.data.expense.model", "com.stpl.tech.kettle.data.model");
        final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        em.setJpaProperties(additionalProperties());
        em.setPersistenceUnitName("AuditDataSourcePUName");
        return em;
    }

    @Bean(name = "AuditDataSource")
    public DataSource auditDataSource() {
        final DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName(env.getProperty("jdbc.driverClassName"));
        dataSource.setUrl(env.getProperty("jdbc.url"));
        dataSource.setUsername(env.getProperty("jdbc.user"));
        dataSource.setPassword(env.getProperty("jdbc.pass"));
        return dataSource;
    }

    @Bean(name = "AuditDataSourceTM")
    public PlatformTransactionManager auditTransactionManager() {
        final JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(auditEntityManagerFactory().getObject());
        return transactionManager;
    }

    @Bean(name = "AuditDataSourceET")
    public PersistenceExceptionTranslationPostProcessor scmExceptionTranslation() {
        return new PersistenceExceptionTranslationPostProcessor();
    }

    private Properties additionalProperties() {
        final Properties hibernateProperties = new Properties();
        hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getProperty("hibernate.hbm2ddl.auto"));
        hibernateProperties.setProperty("hibernate.dialect", env.getProperty("hibernate.dialect"));
        hibernateProperties.setProperty("hibernate.show_sql", env.getProperty("hibernate.show_sql"));
        return hibernateProperties;
    }

    @Bean(name = "multipartResolver")
    public CommonsMultipartResolver multipartResolver() {
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();
        final long limit = 2 * 1024 * 1024L;
        multipartResolver.setMaxUploadSize(8388608);
        return multipartResolver;
    }
}
