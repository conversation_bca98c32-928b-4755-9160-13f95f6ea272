<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Initiate Claim Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #fff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            margin-bottom: 10px;
            margin-top: 5pt;
            text-decoration: underline;
            font-weight: 700;
            color: #2c3e50;
        }

        h4 {
            margin-top: 20px;
            font-weight: 500;
        }

        .info-text {
            margin-bottom: 20px;
            font-size: 14px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            padding: 12px;
            text-align: center;
        }

        th {
            background-color: #3498db;
            color: white;
        }

        tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        tr:hover {
            background-color: #dfe6e9;
        }

        .center-align {
            text-align: center;
        }

        @media (max-width: 768px) {
            table, thead, tbody, th, td, tr {
                display: block;
            }

            thead tr {
                display: none;
            }

            tr {
                margin-bottom: 15px;
                border: 1px solid #ccc;
                border-radius: 8px;
                padding: 10px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            td {
                display: flex;
                justify-content: space-between;
                padding: 8px;
            }

            td::before {
                content: attr(data-label);
                font-weight: bold;
            }
        }

        .voucher-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #ddd;
            vertical-align: middle;
        }

        .voucher-table td:first-child {
            border-left: 1px solid #ddd;
            border-radius: 8px 0 0 8px;
        }

        .voucher-table td:last-child {
            border-right: 1px solid #ddd;
            border-radius: 0 8px 8px 0;
        }

        .voucher-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .voucher-table tr:hover {
            background-color: #f1f1f1;
        }

    </style>
</head>
<body>
<div class="container">
    <h1>Initiate Claim Notification</h1>

    <h4>Please initiate a claim for approved vouchers within ${data.autoInitiateInDays} days, or else the claim will be auto-initiated by the system.</h4>

    <p class="info-text">Refer to the voucher details below to initiate a claim:</p>

    <table>
        <thead>
        <tr>
            <th>Voucher Id</th>
            <th>Account Type</th>
            <th>Account Number</th>
            <th>Expense Type</th>
            <th>Expense Detail</th>
            <th>Expense Category</th>
            <th>Budget Category</th>
            <th>Current Status</th>
            <th>Issued Amount</th>
            <th>Expense Amount</th>
            <th>Issued To</th>
            <th>Issued Time</th>
        </tr>
        </thead>
        <tbody>
        #foreach($voucher in $data.vouchers)
        <tr>
            <td class="center-align voucher-table" data-label="Voucher Id">#if($voucher.id)${voucher.id}#else&nbsp;#end</td>
            <td class="center-align voucher-table" data-label="Account Type">#if($voucher.accountType)${voucher.accountType}#else&nbsp;#end</td>
            <td class="center-align voucher-table" data-label="Account Number">#if($voucher.accountNo)${voucher.accountNo}#else&nbsp;#end</td>
            <td class="center-align voucher-table" data-label="Expense Type">#if($voucher.expenseType)${voucher.expenseType}#else&nbsp;#end</td>
            <td class="center-align voucher-table" data-label="Expense Detail">#if($voucher.expenseDetail)${voucher.expenseDetail}#else&nbsp;#end</td>
            <td class="center-align voucher-table" data-label="Expense Category">#if($voucher.expenseCategory)${voucher.expenseCategory}#else&nbsp;#end</td>
            <td class="center-align voucher-table" data-label="Budget Category">#if($voucher.budgetCategory)${voucher.budgetCategory}#else&nbsp;#end</td>
            <td class="center-align voucher-table" data-label="Current Status">#if($voucher.currentStatus)${voucher.currentStatus}#else&nbsp;#end</td>
            <td class="center-align voucher-table" data-label="Issued Amount">#if($voucher.issuedAmount)${voucher.issuedAmount}#else&nbsp;#end</td>
            <td class="center-align voucher-table" data-label="Expense Amount">#if($voucher.expenseAmount)${voucher.expenseAmount}#else&nbsp;#end</td>
            <td class="center-align voucher-table" data-label="Issued To">#if($voucher.issuedTo && $voucher.issuedTo.name)${voucher.issuedTo.name} [ ${voucher.issuedTo.id} ]#else&nbsp;#end</td>
            <td class="center-align voucher-table" data-label="Issued Time">#if($voucher.issuedTime)${voucher.issuedTime}#else&nbsp;#end</td>
        </tr>
        #end
        </tbody>
    </table>
</div>
</body>
</html>
