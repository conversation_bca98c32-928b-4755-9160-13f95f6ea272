<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Claim Detail</title>
    <style type="text/css">
        * {
            font-size: 14px;
        }

        .content {
            padding: 0 20px 20px 20px;
            color: #000;
        }

        .block {
            margin-bottom: 50px;
        }

        .heading {
            background: #689045;
            color: #fff;
            padding: 5px 10px;
            margin-bottom: 10px;
        }

        .table {
            width: 100%;
        }

        .table td, .table th {
            border-top: #ccc 1px solid;
            border-right: #ccc 1px solid;
            padding: 6px 2px;
        }

        .table td:first-child, .table th:first-child {
            border-left: #ccc 1px solid;
            padding: 6px 2px;
        }

        .table thead td {
            font-weight: bold;
        }

        .table tr:nth-child(2n) td {
            background: #f8f8f8;
        }

        .table tr:last-child td {
            border-bottom: #ccc 1px solid;
        }
    </style>
</head>
<body>

<div class="content">
    <div class="block">
        <div style="text-align: center; padding: 10px; margin-bottom: 30px;">
            <p style="font-size: 24px;">Claim Detail</p>
        </div>
        <p class="heading">
            Summary
        </p>
        <div style="padding: 20px 0;">
            <table style="width: 100%;">
                <tr>
                    <td>Claim Id:</td>
                    <td>$data.claim.claimId</td>
                </tr>
                <tr>
                    <td>Claim Type:</td>
                    <td>$data.claim.claimType</td>
                </tr>
                <tr>
                    <td>Generated By:</td>
                    <td>$data.claim.createdBy.name</td>
                </tr>
                <tr>
                    <td>Requested Amount:</td>
                    <td>$data.claim.claimRequestedAmount</td>
                </tr>
                <tr>
                    <td>Request Time:</td>
                    <td>$data.claim.creationTime</td>
                </tr>
                <tr>
                    <td>Current Status:</td>
                    <td>$data.claim.claimCurrentStatus</td>
                </tr>
                #if($data.claim.happayId)
                <tr>
                    <td>Happay Id:</td>
                    <td>$data.claim.happayId</td>
                </tr>
                #end
                #if($data.claim.employee)
                <tr>
                    <td>Employee:</td>
                    <td>$data.claim.employee.name</td>
                </tr>
                #end
                #if($data.claim.unit)
                <tr>
                    <td>Unit:</td>
                    <td>$data.claim.unit.name</td>
                </tr>
                #end
                <tr>
                    <td>Updated By:</td>
                    <td>$data.claim.lastUpdatedBy.name</td>
                </tr>
                <tr>
                    <td>Update Time:</td>
                    <td>$data.claim.lastUpdateTime</td>
                </tr>
            </table>
        </div>
    </div>
    #if($data.claim.claimLogs.size() > 0)
    <div class="block">
        <p class="heading">Claim Timeline</p>
        <div style="padding: 20px 0;">
            <table class="table table-bordered table-striped">
                <tr>
                    <th scope="col">From status</th>
                    <th scope="col">To status</th>
                    <th scope="col">Updated By</th>
                    <th scope="col">Update time</th>
                    <th scope="col">Comments</th>
                </tr>
                #foreach( $log in $data.claim.claimLogs )
                <tr>
                    <td>$log.fromStatus</td>
                    <td>$log.toStatus</td>
                    <td>$log.updatedBy.name</td>
                    <td>$log.updateTime</td>
                    <td>#if($log.comments)$log.comments#end</td>
                </tr>
                #end
            </table>
        </div>
    </div>
    #end
    #if($data.claim.vouchers.size() > 0)
    <div class="block">
        <p class="heading">Voucher Details</p>
        <div style="padding: 20px 0;">
            #foreach( $voucher in $data.claim.vouchers )
            <div style="background: #ddd; padding: 10px; margin-bottom: 10px; ">
                <table style="width: 100%;" cellpadding="5">
                    <tr>
                        <td>Voucher Id: <br/>$voucher.generatedVoucherId</td>
                        <td>Account Type: <br/>$voucher.accountType</td>
                        <td>Issued Amount: <br/>$voucher.issuedAmount</td>
                        <td>Expense Amount: <br/>$voucher.expenseAmount</td>
                    </tr>
                    <tr>
                        <td>Expense Type: <br/>$voucher.expenseType</td>
                        <td>Business Date: <br/>$voucher.businessDate</td>
                        <td>Issued By: <br/>$voucher.issuedBy.name</td>
                        <td>Issued To: <br/>$voucher.issuedTo.name</td>
                        <td>#if($voucher.expenseDetail)$voucher.expenseDetail #end</td>
                    </tr>
                </table>
            </div>
            #if($voucher.statusDetail.size() > 0)
            <div class="block">
                <p>Voucher Status</p>
                <div style="padding: 20px 0;">
                    <table class="table table-bordered table-striped">
                        <tr>
                            <th scope="col">From status</th>
                            <th scope="col">To status</th>
                            <th scope="col">Updated By</th>
                            <th scope="col">Update time</th>
                            <th scope="col">Transition Status</th>
                            <th scope="col">Comments</th>
                        </tr>
                        #foreach( $log in $voucher.statusDetail )
                        <tr>
                            <td>$log.fromStatus</td>
                            <td>$log.toStatus</td>
                            <td>$log.generatedBy.name</td>
                            <td>$log.actionTime</td>
                            <td>$log.transitionStatus</td>
                            <td>#if($log.comments)$log.actionComment#end</td>
                        </tr>
                        #end
                    </table>
                </div>
            </div>
            #end
            #end
        </div>
    </div>
    #end
</div>
</body>
</html>