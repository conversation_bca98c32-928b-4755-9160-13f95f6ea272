<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>forms-framework</artifactId>
        <groupId>com.stpl.tech.forms</groupId>
        <version>6.2.41</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>forms-core</artifactId>
    <packaging>jar</packaging>

    <name>forms-core</name>
    <url>http://maven.apache.org</url>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
    </properties>
    <dependencies>
        
        <dependency>
            <groupId>com.stpl.tech.kettle.transaction</groupId>
            <artifactId>data-model</artifactId>
            <version>6.2.41</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.forms</groupId>
            <artifactId>forms-domain</artifactId>
            <version>6.2.41</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>4.1</version>
        </dependency>
        <dependency>
		    <groupId>javax.servlet</groupId>
		    <artifactId>javax.servlet-api</artifactId>
		    <scope>provided</scope>
		</dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
    </dependencies>
</project>
